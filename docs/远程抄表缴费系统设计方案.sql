-- 远程抄表缴费系统 - 数据库设计方案
-- <AUTHOR>
-- @date 2025-01-15

-- =====================================================
-- 1. 房间信息表（如果不存在）
-- =====================================================
CREATE TABLE IF NOT EXISTS `tb_room_info` (
  `uid` varchar(36) NOT NULL COMMENT '主键ID',
  `room_code` varchar(50) NOT NULL COMMENT '房间编号',
  `room_name` varchar(100) NOT NULL COMMENT '房间名称',
  `room_type` varchar(20) DEFAULT NULL COMMENT '房间类型(单人间/双人间/套房等)',
  `building_code` varchar(50) DEFAULT NULL COMMENT '楼栋编号',
  `building_name` varchar(100) DEFAULT NULL COMMENT '楼栋名称',
  `floor_number` int(11) DEFAULT NULL COMMENT '楼层',
  `area_code` varchar(20) DEFAULT NULL COMMENT '区域编码',
  `area_name` varchar(100) DEFAULT NULL COMMENT '区域名称',
  `room_status` int(11) DEFAULT 1 COMMENT '房间状态(1正常 2停用 3维修)',
  `occupant_count` int(11) DEFAULT 0 COMMENT '入住人数',
  `max_occupant` int(11) DEFAULT 1 COMMENT '最大入住人数',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` int(11) DEFAULT 1 COMMENT '记录状态(1正常 0删除)',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator_id` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `modify_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modifier_id` varchar(36) DEFAULT NULL COMMENT '修改人ID',
  PRIMARY KEY (`uid`),
  UNIQUE KEY `uk_room_code` (`room_code`),
  KEY `idx_building_code` (`building_code`),
  KEY `idx_area_code` (`area_code`),
  KEY `idx_room_status` (`room_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间信息表';

-- =====================================================
-- 2. 房间设备关联表
-- =====================================================
CREATE TABLE IF NOT EXISTS `tb_room_equipment` (
  `uid` varchar(36) NOT NULL COMMENT '主键ID',
  `room_code` varchar(50) NOT NULL COMMENT '房间编号',
  `equipment_code` varchar(50) NOT NULL COMMENT '设备编号',
  `equipment_type` int(11) NOT NULL COMMENT '设备类型(1电表 2水表 4热水表 221气表)',
  `equipment_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `install_date` date DEFAULT NULL COMMENT '安装日期',
  `install_location` varchar(200) DEFAULT NULL COMMENT '安装位置',
  `is_main_meter` tinyint(1) DEFAULT 0 COMMENT '是否主表(0否 1是)',
  `status` int(11) DEFAULT 1 COMMENT '状态(1正常 0删除)',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator_id` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `modify_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modifier_id` varchar(36) DEFAULT NULL COMMENT '修改人ID',
  PRIMARY KEY (`uid`),
  UNIQUE KEY `uk_room_equipment` (`room_code`, `equipment_code`),
  KEY `idx_room_code` (`room_code`),
  KEY `idx_equipment_code` (`equipment_code`),
  KEY `idx_equipment_type` (`equipment_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间设备关联表';

-- =====================================================
-- 3. 月结费用汇总表
-- =====================================================
CREATE TABLE IF NOT EXISTS `tb_monthly_room_bill` (
  `uid` varchar(36) NOT NULL COMMENT '主键ID',
  `bill_month` varchar(7) NOT NULL COMMENT '账单月份(YYYY-MM)',
  `room_code` varchar(50) NOT NULL COMMENT '房间编号',
  `room_name` varchar(100) NOT NULL COMMENT '房间名称',
  `building_name` varchar(100) DEFAULT NULL COMMENT '楼栋名称',
  `area_name` varchar(100) DEFAULT NULL COMMENT '区域名称',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  
  -- 电费信息
  `electric_usage` decimal(15,4) DEFAULT 0 COMMENT '电量用量(kWh)',
  `electric_amount` decimal(15,2) DEFAULT 0 COMMENT '电费金额',
  `electric_tip_usage` decimal(15,4) DEFAULT 0 COMMENT '尖时用量',
  `electric_tip_amount` decimal(15,2) DEFAULT 0 COMMENT '尖时费用',
  `electric_peak_usage` decimal(15,4) DEFAULT 0 COMMENT '峰时用量',
  `electric_peak_amount` decimal(15,2) DEFAULT 0 COMMENT '峰时费用',
  `electric_flat_usage` decimal(15,4) DEFAULT 0 COMMENT '平时用量',
  `electric_flat_amount` decimal(15,2) DEFAULT 0 COMMENT '平时费用',
  `electric_valley_usage` decimal(15,4) DEFAULT 0 COMMENT '谷时用量',
  `electric_valley_amount` decimal(15,2) DEFAULT 0 COMMENT '谷时费用',
  
  -- 水费信息
  `water_usage` decimal(15,4) DEFAULT 0 COMMENT '水量用量(m³)',
  `water_amount` decimal(15,2) DEFAULT 0 COMMENT '水费金额',
  
  -- 热水费信息
  `hot_water_usage` decimal(15,4) DEFAULT 0 COMMENT '热水用量(m³)',
  `hot_water_amount` decimal(15,2) DEFAULT 0 COMMENT '热水费金额',
  
  -- 燃气费信息
  `gas_usage` decimal(15,4) DEFAULT 0 COMMENT '燃气用量(m³)',
  `gas_amount` decimal(15,2) DEFAULT 0 COMMENT '燃气费金额',
  
  -- 费用汇总
  `total_amount` decimal(15,2) DEFAULT 0 COMMENT '总费用',
  `paid_amount` decimal(15,2) DEFAULT 0 COMMENT '已缴费用',
  `unpaid_amount` decimal(15,2) DEFAULT 0 COMMENT '未缴费用',
  
  -- 账单状态
  `bill_status` int(11) DEFAULT 1 COMMENT '账单状态(1待缴费 2部分缴费 3已缴费 4已逾期)',
  `payment_status` int(11) DEFAULT 0 COMMENT '缴费状态(0未缴费 1已缴费)',
  `due_date` date DEFAULT NULL COMMENT '缴费截止日期',
  `bill_date` date DEFAULT NULL COMMENT '账单生成日期',
  `payment_date` datetime DEFAULT NULL COMMENT '缴费时间',
  
  -- 其他信息
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` int(11) DEFAULT 1 COMMENT '记录状态(1正常 0删除)',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator_id` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `modify_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modifier_id` varchar(36) DEFAULT NULL COMMENT '修改人ID',
  
  PRIMARY KEY (`uid`),
  UNIQUE KEY `uk_room_month` (`room_code`, `bill_month`),
  KEY `idx_bill_month` (`bill_month`),
  KEY `idx_room_code` (`room_code`),
  KEY `idx_bill_status` (`bill_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_due_date` (`due_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='月结房间账单表';

-- =====================================================
-- 4. 缴费记录表
-- =====================================================
CREATE TABLE IF NOT EXISTS `tb_room_payment_record` (
  `uid` varchar(36) NOT NULL COMMENT '主键ID',
  `payment_no` varchar(50) NOT NULL COMMENT '缴费单号',
  `bill_id` varchar(36) NOT NULL COMMENT '账单ID',
  `room_code` varchar(50) NOT NULL COMMENT '房间编号',
  `bill_month` varchar(7) NOT NULL COMMENT '账单月份',
  
  -- 缴费信息
  `payment_amount` decimal(15,2) NOT NULL COMMENT '缴费金额',
  `payment_method` varchar(20) NOT NULL COMMENT '缴费方式(现金/银行卡/微信/支付宝/转账)',
  `payment_date` datetime NOT NULL COMMENT '缴费时间',
  `payment_person` varchar(50) DEFAULT NULL COMMENT '缴费人',
  `payment_phone` varchar(20) DEFAULT NULL COMMENT '缴费人电话',
  
  -- 收费信息
  `cashier_id` varchar(36) DEFAULT NULL COMMENT '收费员ID',
  `cashier_name` varchar(50) DEFAULT NULL COMMENT '收费员姓名',
  `receipt_no` varchar(50) DEFAULT NULL COMMENT '收据号',
  
  -- 第三方支付信息
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方交易号',
  `payment_channel` varchar(50) DEFAULT NULL COMMENT '支付渠道',
  `payment_status` int(11) DEFAULT 1 COMMENT '支付状态(1成功 2失败 3退款)',
  
  -- 其他信息
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` int(11) DEFAULT 1 COMMENT '记录状态(1正常 0删除)',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator_id` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `modify_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modifier_id` varchar(36) DEFAULT NULL COMMENT '修改人ID',
  
  PRIMARY KEY (`uid`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_room_code` (`room_code`),
  KEY `idx_bill_month` (`bill_month`),
  KEY `idx_payment_date` (`payment_date`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_cashier_id` (`cashier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间缴费记录表';

-- =====================================================
-- 5. 缴费明细表
-- =====================================================
CREATE TABLE IF NOT EXISTS `tb_room_payment_detail` (
  `uid` varchar(36) NOT NULL COMMENT '主键ID',
  `payment_id` varchar(36) NOT NULL COMMENT '缴费记录ID',
  `bill_id` varchar(36) NOT NULL COMMENT '账单ID',
  `room_code` varchar(50) NOT NULL COMMENT '房间编号',
  `bill_month` varchar(7) NOT NULL COMMENT '账单月份',
  
  -- 费用类型明细
  `fee_type` varchar(20) NOT NULL COMMENT '费用类型(electric/water/hot_water/gas)',
  `fee_name` varchar(50) NOT NULL COMMENT '费用名称(电费/水费/热水费/燃气费)',
  `usage_amount` decimal(15,4) DEFAULT 0 COMMENT '用量',
  `unit_price` decimal(10,4) DEFAULT 0 COMMENT '单价',
  `fee_amount` decimal(15,2) NOT NULL COMMENT '费用金额',
  `payment_amount` decimal(15,2) NOT NULL COMMENT '本次缴费金额',
  
  -- 分时电费明细（仅电费）
  `time_period` varchar(20) DEFAULT NULL COMMENT '时段(tip/peak/flat/valley)',
  `period_usage` decimal(15,4) DEFAULT 0 COMMENT '时段用量',
  `period_price` decimal(10,4) DEFAULT 0 COMMENT '时段单价',
  `period_amount` decimal(15,2) DEFAULT 0 COMMENT '时段费用',
  
  -- 其他信息
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` int(11) DEFAULT 1 COMMENT '记录状态(1正常 0删除)',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator_id` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  
  PRIMARY KEY (`uid`),
  KEY `idx_payment_id` (`payment_id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_room_code` (`room_code`),
  KEY `idx_bill_month` (`bill_month`),
  KEY `idx_fee_type` (`fee_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间缴费明细表';

-- =====================================================
-- 6. 欠费提醒表
-- =====================================================
CREATE TABLE IF NOT EXISTS `tb_room_arrears_notice` (
  `uid` varchar(36) NOT NULL COMMENT '主键ID',
  `room_code` varchar(50) NOT NULL COMMENT '房间编号',
  `bill_month` varchar(7) NOT NULL COMMENT '欠费月份',
  `arrears_amount` decimal(15,2) NOT NULL COMMENT '欠费金额',
  `overdue_days` int(11) DEFAULT 0 COMMENT '逾期天数',
  
  -- 提醒信息
  `notice_type` varchar(20) NOT NULL COMMENT '提醒类型(sms/email/phone/visit)',
  `notice_content` text DEFAULT NULL COMMENT '提醒内容',
  `notice_date` datetime NOT NULL COMMENT '提醒时间',
  `notice_person` varchar(50) DEFAULT NULL COMMENT '提醒人',
  `notice_status` int(11) DEFAULT 1 COMMENT '提醒状态(1已发送 2已读 3已处理)',
  
  -- 联系信息
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  
  -- 其他信息
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` int(11) DEFAULT 1 COMMENT '记录状态(1正常 0删除)',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator_id` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  
  PRIMARY KEY (`uid`),
  KEY `idx_room_code` (`room_code`),
  KEY `idx_bill_month` (`bill_month`),
  KEY `idx_notice_date` (`notice_date`),
  KEY `idx_notice_status` (`notice_status`),
  KEY `idx_overdue_days` (`overdue_days`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间欠费提醒表';

-- =====================================================
-- 7. 视图：房间账单汇总视图
-- =====================================================
CREATE OR REPLACE VIEW `v_room_bill_summary` AS
SELECT 
    rb.uid,
    rb.bill_month,
    rb.room_code,
    rb.room_name,
    rb.building_name,
    rb.area_name,
    rb.contact_person,
    rb.contact_phone,
    
    -- 费用汇总
    rb.total_amount,
    rb.paid_amount,
    rb.unpaid_amount,
    
    -- 用量汇总
    rb.electric_usage,
    rb.water_usage,
    rb.hot_water_usage,
    rb.gas_usage,
    
    -- 状态信息
    rb.bill_status,
    rb.payment_status,
    rb.due_date,
    rb.bill_date,
    rb.payment_date,
    
    -- 逾期信息
    CASE 
        WHEN rb.payment_status = 0 AND rb.due_date < CURDATE() 
        THEN DATEDIFF(CURDATE(), rb.due_date)
        ELSE 0 
    END AS overdue_days,
    
    -- 缴费统计
    COALESCE(pr.payment_count, 0) AS payment_count,
    COALESCE(pr.last_payment_date, NULL) AS last_payment_date
    
FROM tb_monthly_room_bill rb
LEFT JOIN (
    SELECT 
        bill_id,
        COUNT(*) AS payment_count,
        MAX(payment_date) AS last_payment_date
    FROM tb_room_payment_record 
    WHERE status = 1 AND payment_status = 1
    GROUP BY bill_id
) pr ON rb.uid = pr.bill_id
WHERE rb.status = 1;

-- =====================================================
-- 8. 索引优化
-- =====================================================

-- 房间信息表索引
ALTER TABLE tb_room_info ADD INDEX idx_room_type (room_type);
ALTER TABLE tb_room_info ADD INDEX idx_building_floor (building_code, floor_number);

-- 月结账单表索引
ALTER TABLE tb_monthly_room_bill ADD INDEX idx_bill_amount (total_amount);
ALTER TABLE tb_monthly_room_bill ADD INDEX idx_unpaid_amount (unpaid_amount);
ALTER TABLE tb_monthly_room_bill ADD INDEX idx_overdue (due_date, payment_status);

-- 缴费记录表索引
ALTER TABLE tb_room_payment_record ADD INDEX idx_payment_method (payment_method);
ALTER TABLE tb_room_payment_record ADD INDEX idx_payment_amount (payment_amount);

-- 缴费明细表索引
ALTER TABLE tb_room_payment_detail ADD INDEX idx_fee_type_month (fee_type, bill_month);

-- =====================================================
-- 9. 示例数据插入
-- =====================================================

-- 插入示例房间信息
INSERT INTO tb_room_info (uid, room_code, room_name, room_type, building_code, building_name, floor_number, area_code, area_name, contact_person, contact_phone) VALUES
(UUID(), 'A101', 'A栋101室', '单人间', 'A', 'A栋宿舍楼', 1, 'AREA001', '学生宿舍区', '张三', '13800138001'),
(UUID(), 'A102', 'A栋102室', '双人间', 'A', 'A栋宿舍楼', 1, 'AREA001', '学生宿舍区', '李四', '13800138002'),
(UUID(), 'B201', 'B栋201室', '套房', 'B', 'B栋公寓楼', 2, 'AREA002', '教师公寓区', '王五', '13800138003');

-- 插入示例设备关联
INSERT INTO tb_room_equipment (uid, room_code, equipment_code, equipment_type, equipment_name, is_main_meter) VALUES
(UUID(), 'A101', 'E001', 1, 'A101电表', 1),
(UUID(), 'A101', 'W001', 2, 'A101水表', 1),
(UUID(), 'A102', 'E002', 1, 'A102电表', 1),
(UUID(), 'A102', 'W002', 2, 'A102水表', 1);

-- =====================================================
-- 10. 常用查询SQL
-- =====================================================

-- 查询房间月结账单
SELECT
    bill_month,
    room_code,
    room_name,
    total_amount,
    paid_amount,
    unpaid_amount,
    bill_status,
    payment_status,
    due_date,
    CASE
        WHEN payment_status = 0 AND due_date < CURDATE()
        THEN DATEDIFF(CURDATE(), due_date)
        ELSE 0
    END AS overdue_days
FROM tb_monthly_room_bill
WHERE status = 1
ORDER BY bill_month DESC, room_code;
