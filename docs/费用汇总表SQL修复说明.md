# 费用汇总表SQL修复说明

## 问题描述

费用汇总表 `tb_equipment_cost_summary` 的INSERT语句字段数量和顺序与实际表结构不匹配，导致 "Column count doesn't match value count" 错误。

## ✅ 实际表结构

根据您提供的实际INSERT语句，`tb_equipment_cost_summary` 表的完整字段结构：

```sql
INSERT INTO tb_equipment_cost_summary (
    uid,                    -- 主键UUID
    area_code,              -- 区域编码
    summary_month,          -- 汇总月份 (YYYY-MM)
    water_total_usage,      -- 水表总用量
    water_total_cost,       -- 水表总费用
    electric_total_usage,   -- 电表总用量
    electric_total_cost,    -- 电表总费用
    hot_water_total_usage,  -- 热水表总用量
    hot_water_total_cost,   -- 热水表总费用
    gas_total_usage,        -- 气表总用量
    gas_total_cost,         -- 气表总费用
    total_cost,             -- 总费用
    status,                 -- 状态
    create_date,            -- 创建时间
    creator_id,             -- 创建者
    modify_date,            -- 修改时间
    modifier_id,            -- 修改者
    remark,                 -- 备注
    paid_amount,            -- 已付金额
    unpaid_amount,          -- 未付金额
    payment_status,         -- 支付状态 (0=未支付, 1=已支付)
    due_date                -- 到期日期
) VALUES (...)
```

## 🔧 修复内容

### 1. **修正字段顺序和数量**

#### 修复前：
```java
// 错误：字段顺序混乱，缺少必需字段
"(uid, area_code, summary_month, " + usageField + ", " + costField + ", total_cost, " +
"water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, " +
"hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, " +
"status, create_date, creator_id)"
```

#### 修复后：
```java
// 正确：按实际表结构排列，包含所有必需字段
"(uid, area_code, summary_month, " +
"water_total_usage, water_total_cost, " +
"electric_total_usage, electric_total_cost, " +
"hot_water_total_usage, hot_water_total_cost, " +
"gas_total_usage, gas_total_cost, " +
"total_cost, status, create_date, creator_id, " +
"modify_date, modifier_id, remark, " +
"paid_amount, unpaid_amount, payment_status, due_date)"
```

### 2. **修正VALUES部分**

#### 修复前：
```java
// 错误：数据格式不匹配，字段数量不对
if ("1".equals(equipment_type)) { // 电表
    insertSql += "0, 0, " + usage_amount + ", " + total_cost + ", 0, 0, 0, 0, ";
}
insertSql += total_cost + ", 1, NOW(), 'manual_input')";
```

#### 修复后：
```java
// 正确：数据格式匹配，字段数量正确
if ("1".equals(equipment_type)) { // 电表
    insertSql += "0.0000, 0.00, " + usage_amount + ", " + total_cost + ", 0.0000, 0.00, 0.0000, 0.00, ";
}
// 添加所有必需字段
insertSql += total_cost + ", 1, NOW(), 'manual_input', " +
           "NOW(), 'manual_input', '手动录入', " +
           "0.00, " + total_cost + ", 0, NULL)";
```

### 3. **各设备类型的数据设置**

#### 电表 (equipment_type = "1"):
```java
"0.0000, 0.00, " + usage_amount + ", " + total_cost + ", 0.0000, 0.00, 0.0000, 0.00, "
// 水表=0, 电表=实际值, 热水表=0, 气表=0
```

#### 水表 (equipment_type = "2"):
```java
usage_amount + ", " + total_cost + ", 0.0000, 0.00, 0.0000, 0.00, 0.0000, 0.00, "
// 水表=实际值, 电表=0, 热水表=0, 气表=0
```

#### 热水表 (equipment_type = "4"):
```java
"0.0000, 0.00, 0.0000, 0.00, " + usage_amount + ", " + total_cost + ", 0.0000, 0.00, "
// 水表=0, 电表=0, 热水表=实际值, 气表=0
```

#### 气表 (equipment_type = "221"):
```java
"0.0000, 0.00, 0.0000, 0.00, 0.0000, 0.00, " + usage_amount + ", " + total_cost + ", "
// 水表=0, 电表=0, 热水表=0, 气表=实际值
```

### 4. **支付相关字段设置**

```java
// 支付相关字段的默认值
"paid_amount": 0.00,           // 已付金额：0
"unpaid_amount": total_cost,   // 未付金额：等于总费用
"payment_status": 0,           // 支付状态：0=未支付
"due_date": NULL               // 到期日期：空
```

## 📊 完整的INSERT示例

### 电表数据插入：
```sql
INSERT INTO tb_equipment_cost_summary (
    uid, area_code, summary_month,
    water_total_usage, water_total_cost,
    electric_total_usage, electric_total_cost,
    hot_water_total_usage, hot_water_total_cost,
    gas_total_usage, gas_total_cost,
    total_cost, status, create_date, creator_id,
    modify_date, modifier_id, remark,
    paid_amount, unpaid_amount, payment_status, due_date
) VALUES (
    'uuid-string', '001001002001', '2025-07',
    0.0000, 0.00,           -- 水表
    50.5, 176.75,           -- 电表（实际值）
    0.0000, 0.00,           -- 热水表
    0.0000, 0.00,           -- 气表
    176.75, 1, NOW(), 'manual_input',
    NOW(), 'manual_input', '手动录入',
    0.00, 176.75, 0, NULL
);
```

### 水表数据插入：
```sql
INSERT INTO tb_equipment_cost_summary (
    uid, area_code, summary_month,
    water_total_usage, water_total_cost,
    electric_total_usage, electric_total_cost,
    hot_water_total_usage, hot_water_total_cost,
    gas_total_usage, gas_total_cost,
    total_cost, status, create_date, creator_id,
    modify_date, modifier_id, remark,
    paid_amount, unpaid_amount, payment_status, due_date
) VALUES (
    'uuid-string', '001001002001', '2025-07',
    30.2, 105.70,           -- 水表（实际值）
    0.0000, 0.00,           -- 电表
    0.0000, 0.00,           -- 热水表
    0.0000, 0.00,           -- 气表
    105.70, 1, NOW(), 'manual_input',
    NOW(), 'manual_input', '手动录入',
    0.00, 105.70, 0, NULL
);
```

## 🎯 修复效果

修复后的代码：

1. **字段匹配**：INSERT语句的字段数量和顺序与实际表结构完全匹配
2. **数据格式**：数值字段使用正确的格式（如 0.0000, 0.00）
3. **完整性**：包含所有必需字段，包括支付相关字段
4. **逻辑正确**：根据设备类型正确设置对应的用量和费用字段

## 💡 关键改进

### 1. **支付功能支持**
- 新增 `paid_amount`（已付金额）
- 新增 `unpaid_amount`（未付金额）
- 新增 `payment_status`（支付状态）
- 新增 `due_date`（到期日期）

### 2. **数据完整性**
- 所有字段都有明确的默认值
- 支持后续的支付功能扩展
- 便于账单管理和统计

### 3. **格式规范**
- 用量字段：4位小数（0.0000）
- 金额字段：2位小数（0.00）
- 状态字段：整数（0, 1）

现在费用汇总表的INSERT语句应该能够正确执行了！
