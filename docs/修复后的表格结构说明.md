# 修复后的表格结构说明

## 问题修复

### ❌ 修复前的问题
1. **电价信息缺少尖时电价** - 标题写的是"尖峰平谷四时段"但实际只有峰平谷三个
2. **分时费用部分不完整** - 没有独立的分时费用计算列
3. **谷时电价单独显示** - 布局不合理

### ✅ 修复后的完整结构

现在表格包含以下完整的列组：

## 📊 完整表格布局

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│ [行号] │ 设备基本信息 │ 读数信息 │ 基础读数数据 │ 分时读数 │ 费用计算 │ 电价信息 │ 分时费用 │ 操作信息 │ 备注 │
│   |    │      |       │    |     │      |       │    |     │    |     │    |     │    |     │    |     │  |   │
│ 固定   │    固定      │  可滚动  │    可滚动    │  可滚动  │  可滚动  │  可滚动  │  可滚动  │  可滚动  │可滚动│
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

## 📋 详细列组说明

### 1. 设备基本信息（固定列组）
```javascript
{
    text: '设备基本信息',
    locked: true,
    columns: [
        '设备编号',      // equipment_code
        '设备类型',      // equipment_type (1=电表,2=水表,4=热水表,221=气表)
        '是否分时表',    // is_time_sharing (0=否,1=是)
        '区域编码'       // area_code
    ]
}
```

### 2. 读数信息
```javascript
{
    text: '读数信息',
    columns: [
        '读数日期',      // reading_date
        '读数时间'       // reading_time
    ]
}
```

### 3. 基础读数数据
```javascript
{
    text: '基础读数数据',
    columns: [
        '当前读数',      // current_reading (带单位)
        '上次读数',      // previous_reading (带单位)
        '用量'          // usage_amount (计算值，带单位)
    ]
}
```

### 4. 分时读数（尖峰平谷四时段）
```javascript
{
    text: '分时读数（尖峰平谷四时段）',
    columns: [
        '尖时读数',      // tip_reading (仅分时电表显示)
        '峰时读数',      // peak_reading (仅分时电表显示)
        '平时读数',      // flat_reading (仅分时电表显示)
        '谷时读数'       // valley_reading (仅分时电表显示)
    ]
}
```

### 5. 费用计算
```javascript
{
    text: '费用计算',
    columns: [
        '单价',          // unit_price (非分时表) 或 "分时计费"
        '当前余额',      // current_balance (带颜色显示)
        '计算费用'       // 智能计算总费用
    ]
}
```

### 6. 电价信息（完整四时段）⭐ 修复重点
```javascript
{
    text: '电价信息',
    columns: [
        '尖时电价',      // tip_price ⭐ 新增
        '峰时电价',      // peak_price
        '平时电价',      // flat_price  
        '谷时电价'       // valley_price
    ]
}
```

### 7. 分时费用（完整四时段）⭐ 修复重点
```javascript
{
    text: '分时费用',
    columns: [
        '尖时费用',      // tip_cost ⭐ 新增
        '峰时费用',      // peak_cost ⭐ 新增
        '平时费用',      // flat_cost ⭐ 新增
        '谷时费用'       // valley_cost ⭐ 新增
    ]
}
```

### 8. 操作信息
```javascript
{
    text: '操作信息',
    columns: [
        '创建时间',      // create_date
        '修改时间'       // modify_date
    ]
}
```

### 9. 备注信息
```javascript
{
    text: '备注信息',
    dataIndex: 'remark'
}
```

## 🔍 显示逻辑

### 分时表（电表 + is_time_sharing=1）
```
设备编号: E001
设备类型: 电表  
是否分时表: 是

分时读数:
尖时读数: 5.1234 kWh
峰时读数: 12.3456 kWh
平时读数: 10.2345 kWh  
谷时读数: 6.8643 kWh

电价信息:
尖时电价: ￥1.5860
峰时电价: ￥1.5000
平时电价: ￥1.2000
谷时电价: ￥0.9850

分时费用:
尖时费用: ￥8.12
峰时费用: ￥18.52
平时费用: ￥12.28
谷时费用: ￥6.75

计算费用: ￥45.67
         尖:￥8.12 峰:￥18.52 平:￥12.28 谷:￥6.75
```

### 非分时表（水表/气表 或 电表is_time_sharing=0）
```
设备编号: W001
设备类型: 水表
是否分时表: 否

分时读数: - - - -（灰色显示）

电价信息: - - - -（灰色显示）

分时费用: - - - -（灰色显示）

费用计算:
单价: ￥3.0000
计算费用: ￥20.37
```

## 💡 计算逻辑

### 分时费用计算
```javascript
// 每个时段独立计算
尖时费用 = 尖时读数 × 尖时电价
峰时费用 = 峰时读数 × 峰时电价  
平时费用 = 平时读数 × 平时电价
谷时费用 = 谷时读数 × 谷时电价

// 总费用
总费用 = 尖时费用 + 峰时费用 + 平时费用 + 谷时费用
```

### 非分时费用计算
```javascript
总费用 = 用量 × 单价
```

## 🎯 修复效果

### ✅ 修复前后对比

#### 修复前：
```
电价信息: [峰时电价] [平时电价] [谷时电价]  ❌ 缺少尖时电价
分时费用: 没有独立的分时费用列组        ❌ 不完整
```

#### 修复后：
```
电价信息: [尖时电价] [峰时电价] [平时电价] [谷时电价]  ✅ 完整四时段
分时费用: [尖时费用] [峰时费用] [平时费用] [谷时费用]  ✅ 完整四时段
```

### ✅ 用户体验提升

1. **信息完整性**：四时段信息完整显示
2. **逻辑清晰**：电价和费用分开显示，逻辑更清晰
3. **数据对应**：每个时段的读数、电价、费用一一对应
4. **计算透明**：用户可以清楚看到每个时段的费用构成

### ✅ 数据验证

用户可以通过界面验证：
```
尖时读数 × 尖时电价 = 尖时费用 ✓
峰时读数 × 峰时电价 = 峰时费用 ✓  
平时读数 × 平时电价 = 平时费用 ✓
谷时读数 × 谷时电价 = 谷时费用 ✓

尖时费用 + 峰时费用 + 平时费用 + 谷时费用 = 总费用 ✓
```

现在表格结构完全正确，包含了完整的尖峰平谷四时段的电价信息和分时费用计算！
