/**
 * 水电费账单导出前端调用示例
 * 使用您的ExcelUtil格式
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */

// 在 WEDayCostView.js 中添加以下方法

/**
 * 导出Excel账单
 */
onExportExcel: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    if (!startDate || !endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return;
    }

    // 构建导出参数
    var params = {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode
    };

    // 显示确认对话框
    Ext.Msg.confirm('确认导出', 
        '确定要导出 ' + params.startDate + ' 至 ' + params.endDate + ' 的水电费账单吗？', 
        function(btn) {
            if (btn === 'yes') {
                me.doExportExcel(params);
            }
        }
    );
},

/**
 * 执行Excel导出
 */
doExportExcel: function(params) {
    var me = this;
    
    // 显示加载提示
    var loadingMask = new Ext.LoadMask({
        msg: '正在生成Excel账单，请稍候...',
        target: me
    });
    loadingMask.show();

    // 发送导出请求
    Ext.Ajax.request({
        url: '/Hotel/WECost/exportExcel',
        method: 'POST',
        params: params,
        timeout: 300000, // 5分钟超时
        success: function(response) {
            loadingMask.hide();
            
            try {
                var result = Ext.decode(response.responseText);
                
                if (result.success) {
                    // 成功处理
                    var data = result.data;
                    
                    // 显示成功信息
                    Ext.Msg.alert('成功', 
                        'Excel账单生成成功！<br/>' +
                        '文件名: ' + data.fileName + '<br/>' +
                        '记录数: ' + data.recordCount + '<br/>' +
                        '生成时间: ' + data.exportTime + '<br/>' +
                        '<a href="' + data.filePath + '" target="_blank">点击下载</a>'
                    );
                    
                    // 自动下载文件
                    if (data.filePath) {
                        window.open(data.filePath, '_blank');
                    }
                } else {
                    // 错误处理
                    Ext.Msg.alert('错误', result.msg || '导出失败');
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
            }
        },
        failure: function(response) {
            loadingMask.hide();
            Ext.Msg.alert('错误', '网络请求失败，请重试');
        }
    });
},

/**
 * 打印账单
 */
onPrintBill: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    if (!startDate || !endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return;
    }

    // 构建打印参数
    var params = {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode
    };

    me.showPrintPreview(params);
},

/**
 * 显示打印预览
 */
showPrintPreview: function(params) {
    var me = this;
    
    // 创建打印预览窗口
    var printWindow = Ext.create('Ext.window.Window', {
        title: '水电费账单打印预览 - ' + params.startDate + ' 至 ' + params.endDate,
        width: 800,
        height: 600,
        modal: true,
        maximizable: true,
        layout: 'fit',
        items: [{
            xtype: 'component',
            itemId: 'printContent',
            autoEl: {
                tag: 'iframe',
                style: 'width: 100%; height: 100%; border: none;'
            }
        }],
        buttons: [{
            text: '导出Excel',
            iconCls: 'x-fa fa-file-excel-o',
            handler: function() {
                printWindow.close();
                me.doExportExcel(params);
            }
        }, {
            text: '打印',
            iconCls: 'x-fa fa-print',
            handler: function() {
                var iframe = printWindow.down('component[itemId=printContent]').getEl().dom;
                iframe.contentWindow.print();
            }
        }, {
            text: '关闭',
            handler: function() {
                printWindow.close();
            }
        }]
    });

    printWindow.show();
    
    // 加载打印内容
    me.loadPrintContent(printWindow, params);
},

/**
 * 加载打印内容
 */
loadPrintContent: function(printWindow, params) {
    var me = this;
    
    Ext.Ajax.request({
        url: '/Hotel/WECost/generatePrintContent',
        method: 'POST',
        params: params,
        success: function(response) {
            try {
                var result = Ext.decode(response.responseText);
                
                if (result.success) {
                    var iframe = printWindow.down('component[itemId=printContent]').getEl().dom;
                    var doc = iframe.contentDocument || iframe.contentWindow.document;
                    doc.open();
                    doc.write(result.data.htmlContent);
                    doc.close();
                } else {
                    Ext.Msg.alert('错误', result.msg || '生成打印内容失败');
                    printWindow.close();
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
                printWindow.close();
            }
        },
        failure: function() {
            Ext.Msg.alert('错误', '网络请求失败');
            printWindow.close();
        }
    });
},

/**
 * 预览账单
 */
onPreviewBill: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    if (!startDate || !endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return;
    }

    // 构建预览参数
    var params = {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode
    };

    // 发送预览请求
    Ext.Ajax.request({
        url: '/Hotel/WECost/previewBill',
        method: 'POST',
        params: params,
        success: function(response) {
            try {
                var result = Ext.decode(response.responseText);
                
                if (result.success) {
                    // 显示预览窗口
                    me.showPreviewWindow(result.data, params);
                } else {
                    Ext.Msg.alert('错误', result.msg || '预览失败');
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
            }
        },
        failure: function() {
            Ext.Msg.alert('错误', '网络请求失败');
        }
    });
},

/**
 * 显示预览窗口
 */
showPreviewWindow: function(data, params) {
    var me = this;
    
    var previewWindow = Ext.create('Ext.window.Window', {
        title: '账单预览 - ' + data.startDate + ' 至 ' + data.endDate,
        width: 800,
        height: 600,
        modal: true,
        maximizable: true,
        layout: 'fit',
        items: [{
            xtype: 'component',
            autoEl: {
                tag: 'iframe',
                style: 'width: 100%; height: 100%; border: none;'
            },
            listeners: {
                afterrender: function(component) {
                    var iframe = component.getEl().dom;
                    var doc = iframe.contentDocument || iframe.contentWindow.document;
                    doc.open();
                    doc.write(data.htmlContent);
                    doc.close();
                }
            }
        }],
        buttons: [{
            text: '导出Excel',
            iconCls: 'x-fa fa-file-excel-o',
            handler: function() {
                previewWindow.close();
                me.doExportExcel(params);
            }
        }, {
            text: '打印',
            iconCls: 'x-fa fa-print',
            handler: function() {
                var iframe = previewWindow.down('component').getEl().dom;
                iframe.contentWindow.print();
            }
        }, {
            text: '关闭',
            handler: function() {
                previewWindow.close();
            }
        }]
    });

    previewWindow.show();
},

/**
 * 检查导出状态
 */
checkExportStatus: function(params, callback) {
    Ext.Ajax.request({
        url: '/Hotel/WECost/checkExportStatus',
        method: 'POST',
        params: params,
        success: function(response) {
            try {
                var result = Ext.decode(response.responseText);
                if (callback) {
                    callback(result);
                }
            } catch (e) {
                if (callback) {
                    callback({success: false, msg: '响应数据解析失败'});
                }
            }
        },
        failure: function() {
            if (callback) {
                callback({success: false, msg: '网络请求失败'});
            }
        }
    });
}

// 在工具栏中添加导出按钮
/*
{
    xtype: 'button',
    itemId: 'exportBill',
    iconCls: 'x-fa fa-file-excel-o',
    text: '导出账单',
    menu: [
        {
            text: '导出Excel',
            iconCls: 'x-fa fa-file-excel-o',
            handler: function() {
                me.onExportExcel();
            }
        },
        {
            text: '打印账单',
            iconCls: 'x-fa fa-print',
            handler: function() {
                me.onPrintBill();
            }
        },
        {
            text: '预览账单',
            iconCls: 'x-fa fa-eye',
            handler: function() {
                me.onPreviewBill();
            }
        }
    ]
}
*/
