# 表结构和保存逻辑完整修改方案

## 问题分析

### ❌ 原有问题
1. **表结构不完整**：只有当前读数的尖峰平谷，没有上次读数
2. **无法计算用量**：缺少上次读数，无法计算各时段的实际用量
3. **费用计算错误**：直接用读数×电价，而不是用量×电价
4. **数据保存逻辑缺失**：没有处理上次读数的获取和保存

### ✅ 解决方案
1. **扩展表结构**：添加上次读数的尖峰平谷字段
2. **完善保存逻辑**：自动获取和设置上次读数
3. **修正计算逻辑**：使用用量进行费用计算
4. **优化界面显示**：显示当前读数、上次读数、用量三列

## 📊 表结构修改

### 新增字段列表

```sql
-- 上次读数的分时字段
previous_tip_reading     decimal(15,4)  -- 上次尖时读数
previous_peak_reading    decimal(15,4)  -- 上次峰时读数  
previous_flat_reading    decimal(15,4)  -- 上次平时读数
previous_valley_reading  decimal(15,4)  -- 上次谷时读数

-- 各时段用量字段
tip_usage               decimal(15,4)  -- 尖时用量
peak_usage              decimal(15,4)  -- 峰时用量
flat_usage              decimal(15,4)  -- 平时用量
valley_usage            decimal(15,4)  -- 谷时用量

-- 上次读表时间
previous_reading_time   datetime       -- 上次读表时间

-- 分时电价字段
tip_price               decimal(10,4)  -- 尖时电价
peak_price              decimal(10,4)  -- 峰时电价
flat_price              decimal(10,4)  -- 平时电价
valley_price            decimal(10,4)  -- 谷时电价

-- 分时费用字段
tip_cost                decimal(15,2)  -- 尖时费用
peak_cost               decimal(15,2)  -- 峰时费用
flat_cost               decimal(15,2)  -- 平时费用
valley_cost             decimal(15,2)  -- 谷时费用
total_time_sharing_cost decimal(15,2)  -- 分时总费用
```

### 完整表结构

```sql
CREATE TABLE `tb_equipment_reading_records` (
  `uid` varchar(36) NOT NULL COMMENT '主键ID',
  `equipment_code` varchar(50) NOT NULL COMMENT '设备编号',
  `equipment_type` int(11) NOT NULL COMMENT '设备类型(1电表 2水表 4热水表 221气表)',
  `reading_date` date NOT NULL COMMENT '读数日期',
  `reading_time` datetime NOT NULL COMMENT '读数时间',
  
  -- 基础读数
  `current_reading` decimal(15,4) DEFAULT NULL COMMENT '当前读数',
  `previous_reading` decimal(15,4) DEFAULT NULL COMMENT '上次读数',
  `usage_amount` decimal(15,4) DEFAULT NULL COMMENT '用量(当前读数-上次读数)',
  `current_balance` decimal(15,2) DEFAULT NULL COMMENT '当前余额',
  `unit_price` decimal(10,4) DEFAULT NULL COMMENT '单价',
  
  -- 当前分时读数
  `tip_reading` decimal(15,4) DEFAULT NULL COMMENT '尖时读数(分时表)',
  `peak_reading` decimal(15,4) DEFAULT NULL COMMENT '峰时读数(分时表)',
  `flat_reading` decimal(15,4) DEFAULT NULL COMMENT '平时读数(分时表)',
  `valley_reading` decimal(15,4) DEFAULT NULL COMMENT '谷时读数(分时表)',
  
  -- ⭐ 新增：上次分时读数
  `previous_tip_reading` decimal(15,4) DEFAULT NULL COMMENT '上次尖时读数(分时表)',
  `previous_peak_reading` decimal(15,4) DEFAULT NULL COMMENT '上次峰时读数(分时表)',
  `previous_flat_reading` decimal(15,4) DEFAULT NULL COMMENT '上次平时读数(分时表)',
  `previous_valley_reading` decimal(15,4) DEFAULT NULL COMMENT '上次谷时读数(分时表)',
  
  -- ⭐ 新增：各时段用量
  `tip_usage` decimal(15,4) DEFAULT NULL COMMENT '尖时用量',
  `peak_usage` decimal(15,4) DEFAULT NULL COMMENT '峰时用量',
  `flat_usage` decimal(15,4) DEFAULT NULL COMMENT '平时用量',
  `valley_usage` decimal(15,4) DEFAULT NULL COMMENT '谷时用量',
  
  -- ⭐ 新增：上次读表时间
  `previous_reading_time` datetime DEFAULT NULL COMMENT '上次读表时间',
  
  -- ⭐ 新增：分时电价
  `tip_price` decimal(10,4) DEFAULT NULL COMMENT '尖时电价',
  `peak_price` decimal(10,4) DEFAULT NULL COMMENT '峰时电价',
  `flat_price` decimal(10,4) DEFAULT NULL COMMENT '平时电价',
  `valley_price` decimal(10,4) DEFAULT NULL COMMENT '谷时电价',
  
  -- ⭐ 新增：分时费用
  `tip_cost` decimal(15,2) DEFAULT NULL COMMENT '尖时费用',
  `peak_cost` decimal(15,2) DEFAULT NULL COMMENT '峰时费用',
  `flat_cost` decimal(15,2) DEFAULT NULL COMMENT '平时费用',
  `valley_cost` decimal(15,2) DEFAULT NULL COMMENT '谷时费用',
  `total_time_sharing_cost` decimal(15,2) DEFAULT NULL COMMENT '分时总费用',
  
  -- 其他字段
  `is_time_sharing` tinyint(1) DEFAULT 0 COMMENT '是否为分时表(0否 1是)',
  `area_code` varchar(20) DEFAULT NULL COMMENT '区域编码',
  `status` int(11) DEFAULT 1 COMMENT '状态(1正常 0删除)',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator_id` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `modify_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modifier_id` varchar(36) DEFAULT NULL COMMENT '修改人ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`uid`),
  UNIQUE KEY `uk_equipment_date` (`equipment_code`,`reading_date`),
  KEY `idx_equipment_code` (`equipment_code`),
  KEY `idx_reading_date` (`reading_date`),
  KEY `idx_equipment_type` (`equipment_type`),
  KEY `idx_area_code` (`area_code`),
  KEY `idx_equipment_reading_time` (`equipment_code`,`reading_time`),
  KEY `idx_previous_reading_time` (`equipment_code`,`previous_reading_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备读数记录表';
```

## 💾 数据保存逻辑

### 保存流程

```mermaid
graph TD
    A[接收新读数] --> B[查询上次读数记录]
    B --> C[设置上次读数字段]
    C --> D[计算各时段用量]
    D --> E[获取电价信息]
    E --> F[计算各时段费用]
    F --> G[保存到数据库]
```

### 核心逻辑

#### 1. 获取上次读数
```java
// 查询该设备的最新一条记录作为上次读数
Map<String, Object> previousRecord = getPreviousReading(equipmentCode);

if (previousRecord != null) {
    // 设置基础上次读数
    readingData.put("previous_reading", previousRecord.get("current_reading"));
    readingData.put("previous_reading_time", previousRecord.get("reading_time"));
    
    // 如果是分时电表，设置上次分时读数
    if (equipmentType == 1 && isTimeSharing) {
        readingData.put("previous_tip_reading", previousRecord.get("tip_reading"));
        readingData.put("previous_peak_reading", previousRecord.get("peak_reading"));
        readingData.put("previous_flat_reading", previousRecord.get("flat_reading"));
        readingData.put("previous_valley_reading", previousRecord.get("valley_reading"));
    }
}
```

#### 2. 计算用量
```java
// 总用量
BigDecimal totalUsage = currentReading.subtract(previousReading);
readingData.put("usage_amount", totalUsage);

// 分时用量
if (equipmentType == 1 && isTimeSharing) {
    readingData.put("tip_usage", tipCurrent.subtract(tipPrevious));
    readingData.put("peak_usage", peakCurrent.subtract(peakPrevious));
    readingData.put("flat_usage", flatCurrent.subtract(flatPrevious));
    readingData.put("valley_usage", valleyCurrent.subtract(valleyPrevious));
}
```

#### 3. 计算费用
```java
// 分时费用计算
if (equipmentType == 1 && isTimeSharing) {
    BigDecimal tipCost = tipUsage.multiply(tipPrice);
    BigDecimal peakCost = peakUsage.multiply(peakPrice);
    BigDecimal flatCost = flatUsage.multiply(flatPrice);
    BigDecimal valleyCost = valleyUsage.multiply(valleyPrice);
    
    BigDecimal totalCost = tipCost.add(peakCost).add(flatCost).add(valleyCost);
    
    readingData.put("tip_cost", tipCost);
    readingData.put("peak_cost", peakCost);
    readingData.put("flat_cost", flatCost);
    readingData.put("valley_cost", valleyCost);
    readingData.put("total_time_sharing_cost", totalCost);
} else {
    // 非分时费用计算
    BigDecimal totalCost = usage.multiply(unitPrice);
    readingData.put("total_cost", totalCost);
}
```

## 🖥️ 前端界面修改

### 新的表格结构

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│ 设备信息 │ 读数信息 │ 基础读数 │        分时读数（尖峰平谷）        │ 费用计算 │ 电价信息 │ 分时费用 │ 操作信息 │
│  (固定)  │         │         │ 尖时 │ 峰时 │ 平时 │ 谷时 │         │         │         │         │
│         │         │         │当前|上次|用量│当前|上次|用量│当前|上次|用量│当前|上次|用量│         │         │         │         │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

### 分时读数显示

每个时段包含三列：
```javascript
{
    text: '尖时读数',
    columns: [
        {
            text: '当前读数',
            dataIndex: 'tip_reading',
            renderer: function(value) {
                return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
            }
        },
        {
            text: '上次读数',
            dataIndex: 'previous_tip_reading',
            renderer: function(value) {
                return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
            }
        },
        {
            text: '尖时用量',
            dataIndex: 'tip_usage',
            renderer: function(value, metaData, record) {
                if (value !== null && value !== undefined) {
                    return '<strong>' + value.toFixed(4) + ' kWh</strong>';
                }
                // 计算用量
                var current = record.get('tip_reading') || 0;
                var previous = record.get('previous_tip_reading') || 0;
                var calculated = current - previous;
                return '<strong>' + calculated.toFixed(4) + ' kWh</strong>';
            }
        }
    ]
}
```

## 📈 数据示例

### 分时电表数据示例

```json
{
    "equipment_code": "E001",
    "equipment_type": 1,
    "is_time_sharing": 1,
    "reading_date": "2025-01-15",
    "reading_time": "2025-01-15 10:00:00",
    
    // 基础读数
    "current_reading": 1234.5678,
    "previous_reading": 1200.0000,
    "usage_amount": 34.5678,
    
    // 当前分时读数
    "tip_reading": 105.1234,
    "peak_reading": 112.3456,
    "flat_reading": 110.2345,
    "valley_reading": 106.8643,
    
    // 上次分时读数
    "previous_tip_reading": 100.0000,
    "previous_peak_reading": 100.0000,
    "previous_flat_reading": 100.0000,
    "previous_valley_reading": 100.0000,
    
    // 各时段用量
    "tip_usage": 5.1234,
    "peak_usage": 12.3456,
    "flat_usage": 10.2345,
    "valley_usage": 6.8643,
    
    // 分时电价
    "tip_price": 1.5860,
    "peak_price": 1.5000,
    "flat_price": 1.2000,
    "valley_price": 0.9850,
    
    // 分时费用
    "tip_cost": 8.12,
    "peak_cost": 18.52,
    "flat_cost": 12.28,
    "valley_cost": 6.75,
    "total_time_sharing_cost": 45.67,
    
    "previous_reading_time": "2025-01-14 10:00:00"
}
```

### 界面显示效果

```
设备编号: E001 | 设备类型: 电表 | 分时表: 是

尖时读数: 当前 105.1234 kWh | 上次 100.0000 kWh | 用量 5.1234 kWh
峰时读数: 当前 112.3456 kWh | 上次 100.0000 kWh | 用量 12.3456 kWh  
平时读数: 当前 110.2345 kWh | 上次 100.0000 kWh | 用量 10.2345 kWh
谷时读数: 当前 106.8643 kWh | 上次 100.0000 kWh | 用量 6.8643 kWh

电价信息: 尖 ￥1.5860 | 峰 ￥1.5000 | 平 ￥1.2000 | 谷 ￥0.9850

分时费用: 尖 ￥8.12 | 峰 ￥18.52 | 平 ￥12.28 | 谷 ￥6.75

计算费用: ￥45.67
详细: 尖:5.12kWh×￥1.5860=￥8.12
     峰:12.35kWh×￥1.5000=￥18.52
     平:10.23kWh×￥1.2000=￥12.28
     谷:6.86kWh×￥0.9850=￥6.75
```

## 🚀 部署步骤

### 1. 数据库修改
```sql
-- 执行表结构修改脚本
source tb_equipment_reading_records表结构修改.sql;
```

### 2. 后端代码部署
```bash
# 部署Service类
cp EquipmentReadingService.java src/main/java/com/ymiots/campusos/service/equipment/
```

### 3. 前端代码部署
```bash
# 部署修改后的View
cp WEDayCostView.js src/main/resources/static/app/view/hotel/wedaycost/
```

### 4. 测试验证
1. 测试表结构修改是否成功
2. 测试数据保存逻辑是否正确
3. 测试前端界面显示是否正常
4. 测试费用计算是否准确

## ✅ 修改优势

### 1. 数据完整性
- **历史数据保留**：保存上次读数，便于数据追溯
- **用量计算准确**：基于实际用量计算费用
- **数据关联性**：当前读数与上次读数的关联

### 2. 计算准确性
- **费用计算正确**：用量×电价，而不是读数×电价
- **分时计费精确**：各时段独立计算
- **数据验证**：可以验证总用量=各时段用量之和

### 3. 业务逻辑清晰
- **保存流程标准化**：自动获取上次读数
- **计算逻辑统一**：统一的费用计算方法
- **数据展示完整**：当前、上次、用量三维度展示

### 4. 扩展性好
- **支持多种设备**：电表、水表、气表等
- **支持分时和非分时**：灵活的计费方式
- **便于维护**：清晰的代码结构

现在您的系统已经具备了完整的设备读数记录功能，包括正确的表结构、保存逻辑和界面显示！
