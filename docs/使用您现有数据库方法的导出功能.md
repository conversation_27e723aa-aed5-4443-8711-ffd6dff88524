# 使用您现有数据库方法的导出功能

## 问题解决

您说没有 `queryList` 方法，我已经修正了代码，使用您现有的 `dbService.QueryList()` 方法。

## ✅ 修正后的代码

### 1. 简化版导出服务

我创建了 `WECostExportServiceSimple.java`，使用您现有的数据库方法：

```java
@Service
public class WECostExportServiceSimple extends BaseService {
    
    /**
     * 查询费用记录（使用您现有的数据库方法）
     */
    private JSONArray getCostRecords(String startDate, String endDate, String areaCode) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT r.equipment_code, r.equipment_type, r.area_code, r.reading_date, ");
            sql.append("r.current_reading, r.previous_reading, r.usage_amount, r.unit_price, ");
            sql.append("r.total_cost, r.current_balance ");
            sql.append("FROM tb_equipment_reading_records r ");
            sql.append("WHERE r.status = 1 ");
            sql.append("AND DATE(r.reading_date) >= '").append(startDate).append("' ");
            sql.append("AND DATE(r.reading_date) <= '").append(endDate).append("' ");
            
            if (areaCode != null && !areaCode.trim().isEmpty()) {
                sql.append("AND r.area_code = '").append(areaCode).append("' ");
            }
            
            sql.append("ORDER BY r.area_code, r.equipment_code, r.reading_date");
            
            // 使用您现有的 QueryList 方法
            return dbService.QueryList(sql.toString());
            
        } catch (Exception e) {
            Log.error(WECostExportServiceSimple.class, 
                String.format("查询费用记录失败: %s", e.getMessage()), e);
            return new JSONArray();
        }
    }
}
```

### 2. 修正后的控制器

```java
@Controller
@RequestMapping("/Hotel/WECost")
public class WECostExportController {

    @Autowired
    private WECostExportServiceSimple exportService;  // 使用简化版服务

    @RequestMapping("/exportBill")
    @ResponseBody
    public JsonResult exportBill(@RequestParam String startDate,
                                @RequestParam String endDate,
                                @RequestParam(required = false) String areaCode,
                                @RequestParam String exportType,
                                HttpServletResponse response) {
        try {
            JsonResult result;
            
            switch (exportType.toLowerCase()) {
                case "excel":
                    result = exportService.exportToExcel(startDate, endDate, areaCode, response);
                    break;
                default:
                    JsonResult errorResult = new JsonResult();
                    errorResult.setSuccess(false);
                    errorResult.setMsg("不支持的导出类型: " + exportType);
                    return errorResult;
            }

            return result;

        } catch (Exception e) {
            JsonResult errorResult = new JsonResult();
            errorResult.setSuccess(false);
            errorResult.setMsg("导出失败: " + e.getMessage());
            return errorResult;
        }
    }
}
```

## 📊 数据处理方式

### 1. 使用您的 QueryList 方法

```java
// 执行SQL查询
JSONArray jsonArray = dbService.QueryList(sql.toString());

// 处理返回的JSONArray
for (int i = 0; i < jsonArray.size(); i++) {
    net.sf.json.JSONObject record = jsonArray.getJSONObject(i);
    
    // 安全获取字段值
    String equipmentCode = getString(record, "equipment_code");
    double totalCost = getDouble(record, "total_cost");
    BigDecimal amount = getBigDecimal(record, "total_cost");
}
```

### 2. 安全的数据获取方法

```java
/**
 * 安全获取字符串值
 */
private String getString(net.sf.json.JSONObject obj, String key) {
    try {
        Object value = obj.get(key);
        return value != null ? value.toString() : "";
    } catch (Exception e) {
        return "";
    }
}

/**
 * 安全获取double值
 */
private double getDouble(net.sf.json.JSONObject obj, String key) {
    try {
        Object value = obj.get(key);
        if (value == null) return 0.0;
        return Double.parseDouble(value.toString());
    } catch (Exception e) {
        return 0.0;
    }
}

/**
 * 安全获取BigDecimal值
 */
private BigDecimal getBigDecimal(net.sf.json.JSONObject obj, String key) {
    try {
        Object value = obj.get(key);
        if (value == null) return BigDecimal.ZERO;
        return new BigDecimal(value.toString());
    } catch (Exception e) {
        return BigDecimal.ZERO;
    }
}
```

## 🔧 前端调用方式

### 1. 导出Excel

```javascript
// 导出Excel
onExportExcel: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    if (!startDate || !endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return;
    }

    // 构建导出参数
    var params = {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode,
        exportType: 'excel'
    };

    // 显示确认对话框
    Ext.Msg.confirm('确认导出', 
        '确定要导出 ' + params.startDate + ' 至 ' + params.endDate + ' 的水电费账单吗？', 
        function(btn) {
            if (btn === 'yes') {
                me.doExport(params);
            }
        }
    );
},

// 执行导出
doExport: function(params) {
    var me = this;
    
    // 显示加载提示
    var loadingMask = new Ext.LoadMask({
        msg: '正在生成账单，请稍候...',
        target: me
    });
    loadingMask.show();

    // 发送导出请求
    Ext.Ajax.request({
        url: '/Hotel/WECost/exportBill',
        method: 'POST',
        params: params,
        timeout: 300000, // 5分钟超时
        success: function(response) {
            loadingMask.hide();
            
            try {
                var result = Ext.decode(response.responseText);
                
                if (result.success) {
                    // 成功处理
                    var data = result.data;
                    Ext.Msg.alert('成功', 
                        '账单导出成功！<br/>' +
                        '文件名: ' + data.fileName + '<br/>' +
                        '记录数: ' + data.recordCount + '<br/>' +
                        '导出时间: ' + data.exportTime
                    );
                } else {
                    // 错误处理
                    Ext.Msg.alert('错误', result.msg || '导出失败');
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
            }
        },
        failure: function(response) {
            loadingMask.hide();
            Ext.Msg.alert('错误', '网络请求失败，请重试');
        }
    });
}
```

### 2. 打印预览

```javascript
// 打印账单
onPrintBill: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    if (!startDate || !endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return;
    }

    // 构建打印参数
    var params = {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode
    };

    // 发送生成打印内容请求
    Ext.Ajax.request({
        url: '/Hotel/WECost/generatePrintContent',
        method: 'POST',
        params: params,
        success: function(response) {
            try {
                var result = Ext.decode(response.responseText);
                
                if (result.success) {
                    // 显示打印预览
                    me.showPrintPreview(result.data);
                } else {
                    Ext.Msg.alert('错误', result.msg || '生成打印内容失败');
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
            }
        },
        failure: function() {
            Ext.Msg.alert('错误', '网络请求失败');
        }
    });
}
```

## 📋 生成的账单内容

### Excel账单包含：
- **基本信息**：账单期间、生成时间、区域编码
- **明细数据**：设备编号、类型、读数、用量、单价、费用、余额
- **汇总统计**：总计费用

### HTML打印账单包含：
- **专业样式**：标题、表格、汇总区域
- **完整数据**：所有费用明细
- **打印优化**：适合A4纸张打印

## 🚀 使用步骤

1. **部署代码**：将新的服务类和控制器部署到您的项目
2. **前端集成**：在现有界面添加导出按钮
3. **测试功能**：选择日期范围和区域，测试导出
4. **调整样式**：根据需要调整Excel和HTML的样式

现在导出功能使用您现有的数据库方法，应该可以正常工作了！
