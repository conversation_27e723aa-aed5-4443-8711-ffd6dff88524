-- tb_equipment_reading_records 表结构修改
-- 添加上次读数的尖峰平谷字段，用于计算各时段用量

-- 1. 添加上次读数的分时字段
ALTER TABLE `tb_equipment_reading_records` 
ADD COLUMN `previous_tip_reading` decimal(15, 4) NULL DEFAULT NULL COMMENT '上次尖时读数(分时表)' AFTER `valley_reading`,
ADD COLUMN `previous_peak_reading` decimal(15, 4) NULL DEFAULT NULL COMMENT '上次峰时读数(分时表)' AFTER `previous_tip_reading`,
ADD COLUMN `previous_flat_reading` decimal(15, 4) NULL DEFAULT NULL COMMENT '上次平时读数(分时表)' AFTER `previous_peak_reading`,
ADD COLUMN `previous_valley_reading` decimal(15, 4) NULL DEFAULT NULL COMMENT '上次谷时读数(分时表)' AFTER `previous_flat_reading`;

-- 2. 添加各时段用量字段（可选，也可以通过计算得出）
ALTER TABLE `tb_equipment_reading_records`
ADD COLUMN `tip_usage` decimal(15, 4) NULL DEFAULT NULL COMMENT '尖时用量(当前尖时读数-上次尖时读数)' AFTER `previous_valley_reading`,
ADD COLUMN `peak_usage` decimal(15, 4) NULL DEFAULT NULL COMMENT '峰时用量(当前峰时读数-上次峰时读数)' AFTER `tip_usage`,
ADD COLUMN `flat_usage` decimal(15, 4) NULL DEFAULT NULL COMMENT '平时用量(当前平时读数-上次平时读数)' AFTER `peak_usage`,
ADD COLUMN `valley_usage` decimal(15, 4) NULL DEFAULT NULL COMMENT '谷时用量(当前谷时读数-上次谷时读数)' AFTER `flat_usage`;

-- 3. 添加上次读表时间字段
ALTER TABLE `tb_equipment_reading_records`
ADD COLUMN `previous_reading_time` datetime NULL DEFAULT NULL COMMENT '上次读表时间' AFTER `valley_usage`;

-- 4. 添加分时电价字段（如果不在其他表中维护的话）
ALTER TABLE `tb_equipment_reading_records`
ADD COLUMN `tip_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '尖时电价' AFTER `previous_reading_time`,
ADD COLUMN `peak_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '峰时电价' AFTER `tip_price`,
ADD COLUMN `flat_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '平时电价' AFTER `peak_price`,
ADD COLUMN `valley_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '谷时电价' AFTER `flat_price`;

-- 5. 添加分时费用字段（可选，也可以通过计算得出）
ALTER TABLE `tb_equipment_reading_records`
ADD COLUMN `tip_cost` decimal(15, 2) NULL DEFAULT NULL COMMENT '尖时费用' AFTER `valley_price`,
ADD COLUMN `peak_cost` decimal(15, 2) NULL DEFAULT NULL COMMENT '峰时费用' AFTER `tip_cost`,
ADD COLUMN `flat_cost` decimal(15, 2) NULL DEFAULT NULL COMMENT '平时费用' AFTER `peak_cost`,
ADD COLUMN `valley_cost` decimal(15, 2) NULL DEFAULT NULL COMMENT '谷时费用' AFTER `flat_cost`,
ADD COLUMN `total_time_sharing_cost` decimal(15, 2) NULL DEFAULT NULL COMMENT '分时总费用' AFTER `valley_cost`;

-- 6. 添加索引优化查询性能
CREATE INDEX `idx_equipment_reading_time` ON `tb_equipment_reading_records`(`equipment_code`, `reading_time`);
CREATE INDEX `idx_previous_reading_time` ON `tb_equipment_reading_records`(`equipment_code`, `previous_reading_time`);

-- 7. 查看修改后的表结构
DESCRIBE `tb_equipment_reading_records`;

-- 8. 示例数据插入（用于测试）
/*
INSERT INTO `tb_equipment_reading_records` (
    `uid`, `equipment_code`, `equipment_type`, `reading_date`, `reading_time`,
    `current_reading`, `previous_reading`, `usage_amount`,
    `tip_reading`, `peak_reading`, `flat_reading`, `valley_reading`,
    `previous_tip_reading`, `previous_peak_reading`, `previous_flat_reading`, `previous_valley_reading`,
    `tip_usage`, `peak_usage`, `flat_usage`, `valley_usage`,
    `previous_reading_time`, `is_time_sharing`, `area_code`, `status`,
    `tip_price`, `peak_price`, `flat_price`, `valley_price`,
    `tip_cost`, `peak_cost`, `flat_cost`, `valley_cost`, `total_time_sharing_cost`,
    `create_date`, `remark`
) VALUES (
    UUID(), 'E001', 1, '2025-01-15', '2025-01-15 10:00:00',
    1234.5678, 1200.0000, 34.5678,
    105.1234, 112.3456, 110.2345, 106.8643,
    100.0000, 100.0000, 100.0000, 100.0000,
    5.1234, 12.3456, 10.2345, 6.8643,
    '2025-01-14 10:00:00', 1, 'A001', 1,
    1.5860, 1.5000, 1.2000, 0.9850,
    8.12, 18.52, 12.28, 6.75, 45.67,
    NOW(), '分时电表测试数据'
);
*/
