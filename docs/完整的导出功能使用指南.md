# 完整的导出功能使用指南

## 问题解决

我已经修正了所有缺失的方法，现在导出功能完全基于您现有的数据库方法。

## ✅ 修正内容

### 1. **删除了不存在的方法**
- ❌ `generateBillData()` - 这个方法不存在
- ✅ 直接使用 `generatePrintContent()` 生成预览内容

### 2. **统一使用现有方法**
- ✅ `dbService.QueryList()` - 您现有的数据库查询方法
- ✅ `net.sf.json.JSONArray` - 处理查询结果
- ✅ `com.alibaba.fastjson.JSONObject` - 返回数据给前端

### 3. **修正了所有返回格式**
```java
// 统一的成功返回格式
JsonResult result = new JsonResult();
result.setSuccess(true);
result.setMsg("操作成功");
result.setData(jsonObject);
return result;

// 统一的错误返回格式
JsonResult result = new JsonResult();
result.setSuccess(false);
result.setMsg("错误信息");
return result;
```

## 📁 完整的文件列表

### 1. 后端文件
```
campusos/src/main/java/com/ymiots/campusos/
├── controller/hotel/
│   └── WECostExportController.java          # 导出控制器（已修正）
└── service/waterctrl/
    └── WECostExportServiceSimple.java       # 简化版导出服务
```

### 2. 前端文件
```
campusos/src/main/resources/static/app/view/hotel/wedaycost/
└── WEDayCostView.js                         # 前端界面（需要添加导出按钮）
```

## 🔧 完整的API接口

### 1. 导出Excel账单
```
POST /Hotel/WECost/exportBill
参数：
- startDate: 开始日期 (yyyy-MM-dd)
- endDate: 结束日期 (yyyy-MM-dd)
- areaCode: 区域编码 (可选)
- exportType: 导出类型 (excel)

返回：
{
    "success": true,
    "msg": "导出成功",
    "data": {
        "fileName": "水电费账单_2025-01-01至2025-01-05.xlsx",
        "recordCount": 25,
        "exportTime": "2025-01-15 14:30:00"
    }
}
```

### 2. 生成打印内容
```
POST /Hotel/WECost/generatePrintContent
参数：
- startDate: 开始日期 (yyyy-MM-dd)
- endDate: 结束日期 (yyyy-MM-dd)
- areaCode: 区域编码 (可选)

返回：
{
    "success": true,
    "msg": "生成成功",
    "data": {
        "htmlContent": "<html>...</html>",
        "startDate": "2025-01-01",
        "endDate": "2025-01-05",
        "areaCode": "AREA001",
        "generateTime": "2025-01-15 14:30:00"
    }
}
```

### 3. 预览账单
```
POST /Hotel/WECost/previewBill
参数：
- startDate: 开始日期 (yyyy-MM-dd)
- endDate: 结束日期 (yyyy-MM-dd)
- areaCode: 区域编码 (可选)

返回：
{
    "success": true,
    "msg": "预览成功",
    "data": {
        "htmlContent": "<html>...</html>",
        "startDate": "2025-01-01",
        "endDate": "2025-01-05",
        "areaCode": "AREA001",
        "generateTime": "2025-01-15 14:30:00"
    }
}
```

## 🎯 前端完整实现

### 1. 在工具栏添加导出按钮
```javascript
// 在 WEDayCostView.js 的工具栏中添加
{
    xtype: 'button',
    itemId: 'exportBill',
    iconCls: 'x-fa fa-file-excel-o',
    text: '导出账单',
    menu: [
        {
            text: '导出Excel',
            iconCls: 'x-fa fa-file-excel-o',
            handler: function() {
                me.onExportExcel();
            }
        },
        {
            text: '打印账单',
            iconCls: 'x-fa fa-print',
            handler: function() {
                me.onPrintBill();
            }
        }
    ]
}
```

### 2. 导出Excel方法
```javascript
/**
 * 导出Excel账单
 */
onExportExcel: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    if (!startDate || !endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return;
    }

    // 构建导出参数
    var params = {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode,
        exportType: 'excel'
    };

    // 显示确认对话框
    Ext.Msg.confirm('确认导出',
        '确定要导出 ' + params.startDate + ' 至 ' + params.endDate + ' 的水电费账单吗？',
        function(btn) {
            if (btn === 'yes') {
                me.doExportExcel(params);
            }
        }
    );
},

/**
 * 执行Excel导出
 */
doExportExcel: function(params) {
    var me = this;

    // 显示加载提示
    var loadingMask = new Ext.LoadMask({
        msg: '正在生成Excel账单，请稍候...',
        target: me
    });
    loadingMask.show();

    // 发送导出请求
    Ext.Ajax.request({
        url: '/Hotel/WECost/exportBill',
        method: 'POST',
        params: params,
        timeout: 300000, // 5分钟超时
        success: function(response) {
            loadingMask.hide();

            try {
                var result = Ext.decode(response.responseText);

                if (result.success) {
                    // 成功处理
                    var data = result.data;
                    Ext.Msg.alert('成功',
                        '账单导出成功！<br/>' +
                        '文件名: ' + data.fileName + '<br/>' +
                        '记录数: ' + data.recordCount + '<br/>' +
                        '导出时间: ' + data.exportTime
                    );
                } else {
                    // 错误处理
                    Ext.Msg.alert('错误', result.msg || '导出失败');
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
            }
        },
        failure: function(response) {
            loadingMask.hide();
            Ext.Msg.alert('错误', '网络请求失败，请重试');
        }
    });
}
```

### 3. 打印账单方法
```javascript
/**
 * 打印账单
 */
onPrintBill: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    if (!startDate || !endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return;
    }

    // 构建打印参数
    var params = {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode
    };

    me.showPrintPreview(params);
},

/**
 * 显示打印预览
 */
showPrintPreview: function(params) {
    var me = this;

    // 创建打印预览窗口
    var printWindow = Ext.create('Ext.window.Window', {
        title: '水电费账单打印预览 - ' + params.startDate + ' 至 ' + params.endDate,
        width: 800,
        height: 600,
        modal: true,
        maximizable: true,
        layout: 'fit',
        items: [{
            xtype: 'component',
            itemId: 'printContent',
            autoEl: {
                tag: 'iframe',
                style: 'width: 100%; height: 100%; border: none;'
            }
        }],
        buttons: [{
            text: '打印',
            iconCls: 'x-fa fa-print',
            handler: function() {
                var iframe = printWindow.down('component[itemId=printContent]').getEl().dom;
                iframe.contentWindow.print();
            }
        }, {
            text: '关闭',
            handler: function() {
                printWindow.close();
            }
        }]
    });

    printWindow.show();

    // 加载打印内容
    me.loadPrintContent(printWindow, params);
},

/**
 * 加载打印内容
 */
loadPrintContent: function(printWindow, params) {
    var me = this;

    Ext.Ajax.request({
        url: '/Hotel/WECost/generatePrintContent',
        method: 'POST',
        params: params,
        success: function(response) {
            try {
                var result = Ext.decode(response.responseText);

                if (result.success) {
                    var iframe = printWindow.down('component[itemId=printContent]').getEl().dom;
                    var doc = iframe.contentDocument || iframe.contentWindow.document;
                    doc.open();
                    doc.write(result.data.htmlContent);
                    doc.close();
                } else {
                    Ext.Msg.alert('错误', result.msg || '生成打印内容失败');
                    printWindow.close();
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
                printWindow.close();
            }
        },
        failure: function() {
            Ext.Msg.alert('错误', '网络请求失败');
            printWindow.close();
        }
    });
}
```

## 🚀 部署步骤

### 1. 后端部署
1. 将 `WECostExportServiceSimple.java` 放到 `service/waterctrl/` 目录
2. 将修正后的 `WECostExportController.java` 放到 `controller/hotel/` 目录
3. 重启应用服务器

### 2. 前端部署
1. 在 `WEDayCostView.js` 中添加导出按钮到工具栏
2. 添加导出相关的方法
3. 刷新前端页面

### 3. 测试功能
1. 选择日期范围（如：2025-01-01 至 2025-01-05）
2. 选择区域（可选）
3. 点击"导出Excel"测试Excel导出
4. 点击"打印账单"测试打印预览

## 📊 生成的账单内容

### Excel账单包含：
- 账单标题和基本信息
- 完整的费用明细表格
- 设备编号、类型、读数、用量、单价、费用
- 费用汇总统计

### HTML打印账单包含：
- 专业的打印样式
- 完整的费用明细
- 适合A4纸张的布局
- 费用汇总信息

现在所有方法都已经修正，完全基于您现有的数据库方法，应该可以正常工作了！