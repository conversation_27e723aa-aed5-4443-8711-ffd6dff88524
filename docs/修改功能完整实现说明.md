# 修改功能完整实现说明

## 功能概述

完善了设备读数的修改功能，现在新增和修改都使用相同的 `EquipmentReadingWindow` 窗口，并且后端支持真正的UPDATE操作。

## ✅ 完整实现

### 1. **前端修改**

#### 控制器修改 (`WEDayCostController.js`)
```javascript
onEditClick:function(){
    var me = this;
    if (me.wecostgrid.getSelection().length == 0) {
        toast('请选择需修改的设备读数记录...');
        return;
    }
    var node = me.wecostgrid.getSelection()[0];
    
    // 使用新的设备读数窗口
    var editWin = Ext.create('CamPus.view.hotel.wedaycost.EquipmentReadingWindow',{
        title: '修改设备读数',
        wecostgrid: me.wecostgrid,
        areacode: node.get('area_code') || node.get('areaCode')
    });
    
    // 加载现有数据到表单
    var form = editWin.down('form[itemId=readingForm]');
    if (form) {
        // 设置ID（关键：用于判断是修改操作）
        form.down('hiddenfield[itemId=id]').setValue(node.get('id') || node.get('uid'));
        
        // 设置基本字段
        form.down('textfield[name=equipment_code]').setValue(node.get('equipment_code'));
        form.down('combobox[name=equipment_type]').setValue(node.get('equipment_type'));
        form.down('textfield[name=area_code]').setValue(node.get('area_code'));
        
        // 设置读数时间
        var readingDate = node.get('reading_date') || node.get('reading_time');
        if (readingDate) {
            form.down('datetimefield[name=reading_date]').setValue(new Date(readingDate));
        }
        
        // 设置读数信息
        form.down('numberfield[name=current_reading]').setValue(node.get('current_reading'));
        form.down('numberfield[name=current_balance]').setValue(node.get('current_balance'));
        
        // 处理分时电表
        var isTimeSharing = node.get('is_time_sharing');
        var equipmentType = node.get('equipment_type');
        
        if (isTimeSharing == 1 && equipmentType == '1') {
            form.down('checkboxfield[name=is_time_sharing]').setValue(true);
            editWin.onEquipmentTypeChange('1');
            editWin.onTimeSharingChange(true);
            
            // 设置分时读数
            form.down('numberfield[name=tip_reading]').setValue(node.get('tip_reading'));
            form.down('numberfield[name=peak_reading]').setValue(node.get('peak_reading'));
            form.down('numberfield[name=flat_reading]').setValue(node.get('flat_reading'));
            form.down('numberfield[name=valley_reading]').setValue(node.get('valley_reading'));
        }
        
        // 设置其他字段
        form.down('textareafield[name=remark]').setValue(node.get('remark'));
        form.down('combobox[name=status]').setValue(node.get('status') || 1);
        
        // 触发费用计算
        editWin.calculateCost();
    }
    
    editWin.show();
}
```

#### 窗口修改 (`WEDayCostView.js`)
```javascript
// 添加隐藏的ID字段
{
    xtype: 'hiddenfield',
    itemId: 'id',
    name: 'id',
    value: 0  // 0表示新增，>0表示修改
}
```

### 2. **后端修改**

#### 控制器方法修改 (`WECostController.java`)
```java
@RequestMapping("/saveEquipmentReading")
@Permission(menu = menuid)
public JsonResult saveEquipmentReading(HttpServletRequest request,
                                     @RequestParam(required = false, defaultValue = "0") Integer id,  // 新增ID参数
                                     @RequestParam String equipment_code,
                                     // ... 其他参数
) {
    try {
        // 判断是新增还是修改
        boolean isUpdate = (id != null && id > 0);
        String operation = isUpdate ? "修改" : "新增";
        
        Log.info(WECostController.class, 
            String.format("%s设备读数 - ID: %s, 设备: %s", operation, id, equipment_code));

        // 获取上次读数和单价
        Double previous_reading = getPreviousReading(equipment_code, equipment_type);
        Double unit_price = getEquipmentUnitPrice(equipment_type);
        // ... 获取分时电表数据

        // 计算费用
        double usage_amount = current_reading - previous_reading;
        // ... 费用计算逻辑

        // 根据操作类型调用不同方法
        int readingResult;
        if (isUpdate) {
            readingResult = updateEquipmentReading(id, equipment_code, equipment_type, ...);
        } else {
            readingResult = insertEquipmentReading(equipment_code, equipment_type, ...);
        }

        if (readingResult > 0) {
            // 保存到费用计算表
            boolean costSaved = saveToCostCalculationTable(...);
            
            JsonResult jsonResult = new JsonResult();
            jsonResult.setSuccess(true);
            jsonResult.setMsg("设备读数" + operation + "成功");
            return jsonResult;
        }
    } catch (Exception e) {
        // 错误处理
    }
}
```

#### 新增INSERT方法
```java
private int insertEquipmentReading(String equipment_code, String equipment_type, ...) {
    try {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO tb_equipment_reading_records (");
        sql.append("uid, equipment_code, equipment_type, area_code, reading_date, reading_time, ");
        sql.append("current_reading, previous_reading, usage_amount, current_balance, unit_price, ");
        sql.append("tip_reading, peak_reading, flat_reading, valley_reading, ");
        sql.append("is_time_sharing, status, create_date, creator_id, remark");
        sql.append(") VALUES (");
        sql.append("'").append(java.util.UUID.randomUUID().toString()).append("', ");
        // ... 设置所有字段值
        sql.append(")");

        return weCostService.dbService.ExecuteUpdate(sql.toString());
    } catch (Exception e) {
        Log.error(WECostController.class, "插入设备读数失败", e);
        return 0;
    }
}
```

#### 新增UPDATE方法
```java
private int updateEquipmentReading(Integer id, String equipment_code, String equipment_type, ...) {
    try {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE tb_equipment_reading_records SET ");
        sql.append("equipment_code = '").append(equipment_code).append("', ");
        sql.append("equipment_type = '").append(equipment_type).append("', ");
        sql.append("area_code = '").append(area_code).append("', ");
        sql.append("reading_date = '").append(reading_date.substring(0, 10)).append("', ");
        sql.append("reading_time = '").append(reading_date).append("', ");
        sql.append("current_reading = ").append(current_reading).append(", ");
        sql.append("previous_reading = ").append(previous_reading).append(", ");
        sql.append("usage_amount = ").append(usage_amount).append(", ");
        sql.append("current_balance = ").append(current_balance).append(", ");
        sql.append("unit_price = ").append(unit_price).append(", ");

        // 分时电表字段
        if (is_time_sharing == 1 && "1".equals(equipment_type)) {
            sql.append("tip_reading = ").append(tip_reading != null ? tip_reading : 0).append(", ");
            sql.append("peak_reading = ").append(peak_reading != null ? peak_reading : 0).append(", ");
            sql.append("flat_reading = ").append(flat_reading != null ? flat_reading : 0).append(", ");
            sql.append("valley_reading = ").append(valley_reading != null ? valley_reading : 0).append(", ");
        } else {
            sql.append("tip_reading = 0, peak_reading = 0, flat_reading = 0, valley_reading = 0, ");
        }

        sql.append("is_time_sharing = ").append(is_time_sharing).append(", ");
        sql.append("status = ").append(status).append(", ");
        sql.append("modify_date = NOW(), ");
        sql.append("modifier_id = 'manual_input', ");
        sql.append("remark = '").append(remark != null ? remark : "手动修改").append("' ");
        sql.append("WHERE id = ").append(id);

        return weCostService.dbService.ExecuteUpdate(sql.toString());
    } catch (Exception e) {
        Log.error(WECostController.class, "更新设备读数失败", e);
        return 0;
    }
}
```

## 🎯 关键特性

### 1. **操作判断**
```java
// 通过ID参数判断操作类型
boolean isUpdate = (id != null && id > 0);

// 前端设置ID
form.down('hiddenfield[itemId=id]').setValue(node.get('id'));  // 修改时设置实际ID
// 新增时ID默认为0
```

### 2. **界面统一**
- 新增和修改使用相同的窗口
- 相同的字段布局和验证规则
- 相同的费用计算逻辑

### 3. **数据完整性**
- 修改时自动加载所有现有数据
- 支持分时电表的完整修改
- 保持数据的一致性

### 4. **SQL分离**
- INSERT操作：`insertEquipmentReading()` 方法
- UPDATE操作：`updateEquipmentReading()` 方法
- 避免复杂的条件SQL构建

## 📊 操作流程

### 新增流程：
```
点击"新增" → 打开窗口(ID=0) → 录入数据 → 保存 → 调用insertEquipmentReading() → INSERT到数据库
```

### 修改流程：
```
选择记录 → 点击"修改" → 打开窗口并加载数据(ID>0) → 修改数据 → 保存 → 调用updateEquipmentReading() → UPDATE数据库
```

## 💡 使用效果

现在用户在操作设备读数时：

1. **界面统一**：新增和修改使用相同的现代化界面
2. **操作简化**：只需输入当前读数，系统自动处理计算
3. **数据准确**：修改时保留所有原有数据，不会丢失
4. **功能完整**：支持普通设备和分时电表的完整操作流程
5. **真正修改**：后端执行真正的UPDATE操作，而不是重复INSERT

## 🚀 技术亮点

1. **共用保存方法**：一个方法处理新增和修改
2. **智能判断**：通过ID参数自动判断操作类型
3. **方法分离**：INSERT和UPDATE逻辑分别处理
4. **数据加载**：修改时自动加载所有字段数据
5. **费用计算**：修改时重新计算费用，确保准确性

现在修改功能已经完全实现，与新增功能保持一致的用户体验！
