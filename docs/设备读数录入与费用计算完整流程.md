# 设备读数录入与费用计算完整流程

## 功能概述

完善了设备读数手动录入功能，**同时保存到读数表和费用计算表**，实现完整的费用计算流程，包括：
1. 保存到 `tb_equipment_reading_records` 读数记录表
2. 保存到 `tb_equipment_cost_calculation` 费用计算表  
3. 更新 `tb_equipment_cost_summary` 费用汇总表

## ✅ 完整的数据流程

### 1. **数据保存流程**
```
用户录入读数 
    ↓
前端实时计算费用
    ↓
提交到后端
    ↓
保存到读数记录表 (tb_equipment_reading_records)
    ↓
保存到费用计算表 (tb_equipment_cost_calculation)
    ↓
更新费用汇总表 (tb_equipment_cost_summary)
    ↓
返回成功结果
```

### 2. **后端保存逻辑**
```java
// 1. 保存读数记录
int readingResult = weCostService.dbService.ExecuteUpdate(readingSql);

if (readingResult > 0) {
    // 2. 保存费用计算数据
    boolean costSaved = saveToCostCalculationTable(...);
    
    if (costSaved) {
        // 3. 更新费用汇总
        updateCostSummaryTable(...);
        return "设备读数和费用计算数据保存成功";
    }
}
```

## 📊 涉及的数据库表

### 1. **tb_equipment_reading_records** (读数记录表)
```sql
INSERT INTO tb_equipment_reading_records (
    equipment_code,        -- 设备编号
    equipment_type,        -- 设备类型
    area_code,            -- 区域编码
    reading_date,         -- 读数时间
    current_reading,      -- 当前读数
    previous_reading,     -- 上次读数
    usage_amount,         -- 用量 (自动计算)
    unit_price,           -- 单价
    total_cost,           -- 总费用 (自动计算)
    
    -- 分时电表字段
    tip_reading, tip_usage, tip_price, tip_cost,
    peak_reading, peak_usage, peak_price, peak_cost,
    flat_reading, flat_usage, flat_price, flat_cost,
    valley_reading, valley_usage, valley_price, valley_cost,
    
    is_time_sharing,      -- 是否分时电表
    current_balance,      -- 当前余额
    remark,              -- 备注
    status,              -- 状态
    create_time          -- 创建时间
) VALUES (...)
```

### 2. **tb_equipment_cost_calculation** (费用计算表)
```sql
INSERT INTO tb_equipment_cost_calculation (
    uid,                  -- 主键UUID
    equipment_code,       -- 设备编号
    equipment_type,       -- 设备类型
    calculation_date,     -- 计算日期 (YYYY-MM-DD)
    reading_record_id,    -- 关联读数记录ID
    
    -- 基础费用信息
    current_reading, previous_reading, usage_amount, unit_price, total_cost,
    
    -- 分时电表费用信息
    tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost,
    peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost,
    flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost,
    valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost,
    
    is_time_sharing,      -- 是否分时电表
    area_code,           -- 区域编码
    status,              -- 状态
    create_date,         -- 创建时间
    creator_id,          -- 创建者 ('manual_input')
    remark               -- 备注
) VALUES (...)
```

### 3. **tb_equipment_cost_summary** (费用汇总表)
```sql
-- 如果当月记录已存在，则更新
UPDATE tb_equipment_cost_summary SET 
    electric_total_usage = electric_total_usage + 用量,
    electric_total_cost = electric_total_cost + 费用,
    total_cost = water_total_cost + electric_total_cost + hot_water_total_cost + gas_total_cost,
    modify_date = NOW()
WHERE area_code = '区域编码' AND summary_month = 'YYYY-MM'

-- 如果当月记录不存在，则插入
INSERT INTO tb_equipment_cost_summary (
    uid, area_code, summary_month,
    water_total_usage, water_total_cost,      -- 水表汇总
    electric_total_usage, electric_total_cost, -- 电表汇总
    hot_water_total_usage, hot_water_total_cost, -- 热水表汇总
    gas_total_usage, gas_total_cost,          -- 气表汇总
    total_cost,           -- 总费用
    status, create_date, creator_id
) VALUES (...)
```

## 🔧 费用计算逻辑

### 1. **普通设备费用计算**
```java
// 计算用量
double usage_amount = current_reading - previous_reading;

// 计算总费用
double totalCost = usage_amount * unit_price;

Log.info("普通设备费用计算: 用量%.2f * 单价%.4f = 总费用%.2f", 
    usage_amount, unit_price, totalCost);
```

### 2. **分时电表费用计算**
```java
if (is_time_sharing == 1 && "1".equals(equipment_type)) {
    // 尖时计算
    double tipUsage = tipReading - previousTipReading;
    double tipCost = tipUsage * tipPrice;
    
    // 峰时计算
    double peakUsage = peakReading - previousPeakReading;
    double peakCost = peakUsage * peakPrice;
    
    // 平时计算
    double flatUsage = flatReading - previousFlatReading;
    double flatCost = flatUsage * flatPrice;
    
    // 谷时计算
    double valleyUsage = valleyReading - previousValleyReading;
    double valleyCost = valleyUsage * valleyPrice;
    
    // 总费用
    double totalCost = tipCost + peakCost + flatCost + valleyCost;
    
    Log.info("分时电表总费用: %.2f (尖%.2f + 峰%.2f + 平%.2f + 谷%.2f)", 
        totalCost, tipCost, peakCost, flatCost, valleyCost);
}
```

### 3. **费用汇总更新**
```java
// 根据设备类型确定更新字段
switch (equipment_type) {
    case "1":   // 电表
        usageField = "electric_total_usage";
        costField = "electric_total_cost";
        break;
    case "2":   // 水表
        usageField = "water_total_usage";
        costField = "water_total_cost";
        break;
    case "4":   // 热水表
        usageField = "hot_water_total_usage";
        costField = "hot_water_total_cost";
        break;
    case "221": // 气表
        usageField = "gas_total_usage";
        costField = "gas_total_cost";
        break;
}

// 更新对应字段
UPDATE tb_equipment_cost_summary SET 
    {usageField} = {usageField} + {usage_amount},
    {costField} = {costField} + {total_cost},
    total_cost = water_total_cost + electric_total_cost + hot_water_total_cost + gas_total_cost
WHERE area_code = '{area_code}' AND summary_month = '{YYYY-MM}'
```

## 📋 数据示例

### 1. **普通水表录入示例**
```json
输入数据:
{
    "equipment_code": "WM001",
    "equipment_type": "2",
    "area_code": "AREA001", 
    "reading_date": "2025-01-15 14:30:00",
    "current_reading": 1250.5,
    "previous_reading": 1200.0,
    "unit_price": 3.5
}

计算结果:
- 用量: 50.5
- 费用: 176.75

保存到的表:
1. tb_equipment_reading_records: 完整读数记录
2. tb_equipment_cost_calculation: 费用计算记录
3. tb_equipment_cost_summary: 更新2025-01月水表汇总
```

### 2. **分时电表录入示例**
```json
输入数据:
{
    "equipment_code": "EM001",
    "equipment_type": "1",
    "area_code": "AREA001",
    "reading_date": "2025-01-15 14:30:00",
    "is_time_sharing": 1,
    "tip_reading": 1200.0, "previous_tip_reading": 1150.0, "tip_price": 1.2,
    "peak_reading": 1300.0, "previous_peak_reading": 1250.0, "peak_price": 0.8,
    "flat_reading": 1250.0, "previous_flat_reading": 1200.0, "flat_price": 0.6,
    "valley_reading": 1250.0, "previous_valley_reading": 1200.0, "valley_price": 0.3
}

计算结果:
- 尖时: 用量50.0, 费用60.0
- 峰时: 用量50.0, 费用40.0  
- 平时: 用量50.0, 费用30.0
- 谷时: 用量50.0, 费用15.0
- 总计: 用量200.0, 费用145.0

保存到的表:
1. tb_equipment_reading_records: 完整分时读数记录
2. tb_equipment_cost_calculation: 分时费用计算记录
3. tb_equipment_cost_summary: 更新2025-01月电表汇总
```

## 🎯 关键特性

### 1. **事务性保存**
- 先保存读数记录
- 再保存费用计算
- 最后更新费用汇总
- 任一步骤失败都有相应提示

### 2. **完整的日志记录**
```java
Log.info("设备读数保存成功，开始保存到费用计算表");
Log.info("费用计算表保存成功");
Log.info("费用汇总表更新成功");
Log.info("设备读数和费用计算数据保存成功");
```

### 3. **智能汇总更新**
- 按区域和月份汇总
- 按设备类型分类统计
- 自动计算总费用
- 支持增量更新

### 4. **数据关联**
- 费用计算记录关联读数记录
- 汇总数据可追溯到明细
- 支持数据审计和核查

## 🚀 使用效果

现在当用户手动录入设备读数时：

1. **前端实时显示**费用计算结果
2. **后端保存**到三个相关表中
3. **自动更新**月度费用汇总
4. **完整记录**计算过程和结果
5. **支持查询**和报表统计

这样就实现了从读数录入到费用计算的完整闭环，确保数据的一致性和完整性！
