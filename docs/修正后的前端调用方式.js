/**
 * 修正后的前端调用方式
 * 解决了Excel导出的序列化问题
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */

// 在 WEDayCostView.js 中添加以下方法

/**
 * 导出Excel账单（修正版）
 */
onExportExcel: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    if (!startDate || !endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return;
    }

    // 构建导出参数
    var params = {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode
    };

    // 显示确认对话框
    Ext.Msg.confirm('确认导出', 
        '确定要导出 ' + params.startDate + ' 至 ' + params.endDate + ' 的水电费账单吗？', 
        function(btn) {
            if (btn === 'yes') {
                me.doExportExcel(params);
            }
        }
    );
},

/**
 * 执行Excel导出（修正版 - 直接下载文件）
 */
doExportExcel: function(params) {
    var me = this;
    
    // 先检查是否有数据
    Ext.Ajax.request({
        url: '/Hotel/WECost/checkExportStatus',
        method: 'POST',
        params: params,
        success: function(response) {
            try {
                var result = Ext.decode(response.responseText);
                
                if (result.success) {
                    if (result.data.hasData) {
                        // 有数据，开始下载
                        me.downloadExcel(params, result.data.recordCount);
                    } else {
                        Ext.Msg.alert('提示', '没有找到符合条件的费用记录');
                    }
                } else {
                    Ext.Msg.alert('错误', result.msg || '检查数据失败');
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
            }
        },
        failure: function() {
            Ext.Msg.alert('错误', '网络请求失败');
        }
    });
},

/**
 * 下载Excel文件
 */
downloadExcel: function(params, recordCount) {
    var me = this;
    
    // 显示下载提示
    Ext.Msg.show({
        title: '正在下载',
        msg: '正在生成Excel账单，共 ' + recordCount + ' 条记录，请稍候...',
        width: 300,
        wait: true,
        waitConfig: {interval: 200}
    });

    // 构建下载URL
    var url = '/Hotel/WECost/exportExcel';
    var paramStr = Ext.Object.toQueryString(params);
    var downloadUrl = url + '?' + paramStr;
    
    // 创建隐藏的iframe进行下载
    var iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = downloadUrl;
    
    // 监听下载完成
    iframe.onload = function() {
        setTimeout(function() {
            Ext.Msg.hide();
            
            // 检查是否下载成功
            try {
                var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                var content = iframeDoc.body.textContent || iframeDoc.body.innerText;
                
                if (content && content.indexOf('导出失败') !== -1) {
                    Ext.Msg.alert('错误', content);
                } else {
                    Ext.Msg.alert('成功', 
                        'Excel账单下载成功！<br/>' +
                        '记录数: ' + recordCount + '<br/>' +
                        '请查看浏览器下载目录'
                    );
                }
            } catch (e) {
                // 跨域或其他错误，假设下载成功
                Ext.Msg.alert('成功', 
                    'Excel账单下载成功！<br/>' +
                    '记录数: ' + recordCount + '<br/>' +
                    '请查看浏览器下载目录'
                );
            }
            
            // 清理iframe
            document.body.removeChild(iframe);
        }, 1000);
    };
    
    iframe.onerror = function() {
        Ext.Msg.hide();
        Ext.Msg.alert('错误', '下载失败，请重试');
        document.body.removeChild(iframe);
    };
    
    document.body.appendChild(iframe);
},

/**
 * 打印账单（保持不变）
 */
onPrintBill: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    if (!startDate || !endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return;
    }

    // 构建打印参数
    var params = {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode
    };

    me.showPrintPreview(params);
},

/**
 * 显示打印预览（保持不变）
 */
showPrintPreview: function(params) {
    var me = this;
    
    // 创建打印预览窗口
    var printWindow = Ext.create('Ext.window.Window', {
        title: '水电费账单打印预览 - ' + params.startDate + ' 至 ' + params.endDate,
        width: 800,
        height: 600,
        modal: true,
        maximizable: true,
        layout: 'fit',
        items: [{
            xtype: 'component',
            itemId: 'printContent',
            autoEl: {
                tag: 'iframe',
                style: 'width: 100%; height: 100%; border: none;'
            }
        }],
        buttons: [{
            text: '打印',
            iconCls: 'x-fa fa-print',
            handler: function() {
                var iframe = printWindow.down('component[itemId=printContent]').getEl().dom;
                iframe.contentWindow.print();
            }
        }, {
            text: '关闭',
            handler: function() {
                printWindow.close();
            }
        }]
    });

    printWindow.show();
    
    // 加载打印内容
    me.loadPrintContent(printWindow, params);
},

/**
 * 加载打印内容（保持不变）
 */
loadPrintContent: function(printWindow, params) {
    var me = this;
    
    Ext.Ajax.request({
        url: '/Hotel/WECost/generatePrintContent',
        method: 'POST',
        params: params,
        success: function(response) {
            try {
                var result = Ext.decode(response.responseText);
                
                if (result.success) {
                    var iframe = printWindow.down('component[itemId=printContent]').getEl().dom;
                    var doc = iframe.contentDocument || iframe.contentWindow.document;
                    doc.open();
                    doc.write(result.data.htmlContent);
                    doc.close();
                } else {
                    Ext.Msg.alert('错误', result.msg || '生成打印内容失败');
                    printWindow.close();
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
                printWindow.close();
            }
        },
        failure: function() {
            Ext.Msg.alert('错误', '网络请求失败');
            printWindow.close();
        }
    });
},

/**
 * 预览账单
 */
onPreviewBill: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    if (!startDate || !endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return;
    }

    // 构建预览参数
    var params = {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode
    };

    // 发送预览请求
    Ext.Ajax.request({
        url: '/Hotel/WECost/previewBill',
        method: 'POST',
        params: params,
        success: function(response) {
            try {
                var result = Ext.decode(response.responseText);
                
                if (result.success) {
                    // 显示预览窗口
                    me.showPreviewWindow(result.data);
                } else {
                    Ext.Msg.alert('错误', result.msg || '预览失败');
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
            }
        },
        failure: function() {
            Ext.Msg.alert('错误', '网络请求失败');
        }
    });
},

/**
 * 显示预览窗口
 */
showPreviewWindow: function(data) {
    var previewWindow = Ext.create('Ext.window.Window', {
        title: '账单预览 - ' + data.startDate + ' 至 ' + data.endDate,
        width: 800,
        height: 600,
        modal: true,
        maximizable: true,
        layout: 'fit',
        items: [{
            xtype: 'component',
            autoEl: {
                tag: 'iframe',
                style: 'width: 100%; height: 100%; border: none;'
            },
            listeners: {
                afterrender: function(component) {
                    var iframe = component.getEl().dom;
                    var doc = iframe.contentDocument || iframe.contentWindow.document;
                    doc.open();
                    doc.write(data.htmlContent);
                    doc.close();
                }
            }
        }],
        buttons: [{
            text: '导出Excel',
            iconCls: 'x-fa fa-file-excel-o',
            handler: function() {
                previewWindow.close();
                // 直接调用导出方法
                var params = {
                    startDate: data.startDate,
                    endDate: data.endDate,
                    areaCode: data.areaCode
                };
                me.downloadExcel(params, '未知');
            }
        }, {
            text: '打印',
            iconCls: 'x-fa fa-print',
            handler: function() {
                var iframe = previewWindow.down('component').getEl().dom;
                iframe.contentWindow.print();
            }
        }, {
            text: '关闭',
            handler: function() {
                previewWindow.close();
            }
        }]
    });

    previewWindow.show();
}
