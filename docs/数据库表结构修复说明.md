# 数据库表结构修复说明

## 问题描述

根据您提供的实际数据库INSERT语句，发现我们的SQL与实际表结构不匹配，导致插入失败。

## ✅ 实际表结构

### 1. **tb_equipment_reading_records** (读数记录表)
```sql
-- 实际字段结构
INSERT INTO tb_equipment_reading_records (
    uid,                    -- 主键UUID
    equipment_code,         -- 设备编号
    equipment_type,         -- 设备类型
    reading_date,           -- 读数日期 (YYYY-MM-DD)
    reading_time,           -- 读数时间 (YYYY-MM-DD HH:mm:ss)
    current_reading,        -- 当前读数
    previous_reading,       -- 上次读数
    usage_amount,           -- 用量
    current_balance,        -- 当前余额
    unit_price,             -- 单价
    tip_reading,            -- 尖时读数
    peak_reading,           -- 峰时读数
    flat_reading,           -- 平时读数
    valley_reading,         -- 谷时读数
    is_time_sharing,        -- 是否分时电表
    area_code,              -- 区域编码
    status,                 -- 状态
    create_date,            -- 创建时间
    creator_id,             -- 创建者
    modify_date,            -- 修改时间
    modifier_id,            -- 修改者
    remark                  -- 备注
) VALUES (...)
```

**特点**：
- ❌ **没有** 分时电表的上次读数字段 (`previous_tip_reading` 等)
- ❌ **没有** 分时电表的用量字段 (`tip_usage` 等)
- ❌ **没有** 分时电表的单价字段 (`tip_price` 等)
- ❌ **没有** 分时电表的费用字段 (`tip_cost` 等)
- ✅ **只有** 分时电表的当前读数字段 (`tip_reading` 等)

### 2. **tb_equipment_cost_calculation** (费用计算表)
```sql
-- 实际字段结构
INSERT INTO tb_equipment_cost_calculation (
    uid,                    -- 主键UUID
    equipment_code,         -- 设备编号
    equipment_type,         -- 设备类型
    calculation_date,       -- 计算日期
    reading_record_id,      -- 关联读数记录ID
    current_reading,        -- 当前读数
    previous_reading,       -- 上次读数
    usage_amount,           -- 用量
    unit_price,             -- 单价
    total_cost,             -- 总费用
    
    -- 分时电表完整字段
    tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost,
    peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost,
    flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost,
    valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost,
    
    previous_reading_time,  -- 上次读数时间
    is_time_sharing,        -- 是否分时电表
    area_code,              -- 区域编码
    status,                 -- 状态
    create_date,            -- 创建时间
    creator_id,             -- 创建者
    modify_date,            -- 修改时间
    modifier_id,            -- 修改者
    remark                  -- 备注
) VALUES (...)
```

**特点**：
- ✅ **有** 完整的分时电表字段，包括上次读数、用量、单价、费用
- ✅ **有** `previous_reading_time` 字段

## 🔧 修复内容

### 1. **修正读数记录表SQL**
```java
// 修复前：包含不存在的字段
sql.append("tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, ");

// 修复后：只包含存在的字段
sql.append("tip_reading, peak_reading, flat_reading, valley_reading, ");
```

### 2. **修正字段顺序**
```java
// 按实际表结构调整字段顺序
sql.append("uid, equipment_code, equipment_type, area_code, reading_date, reading_time, ");
sql.append("current_reading, previous_reading, usage_amount, current_balance, unit_price, ");
sql.append("tip_reading, peak_reading, flat_reading, valley_reading, ");
sql.append("is_time_sharing, status, create_date, creator_id, remark");
```

### 3. **修正VALUES部分**
```java
// 添加UUID主键
sql.append("'").append(java.util.UUID.randomUUID().toString()).append("', ");

// 分离日期和时间
sql.append("'").append(reading_date.substring(0, 10)).append("', ");  // reading_date
sql.append("'").append(reading_date).append("', ");  // reading_time

// 调整字段顺序
sql.append(current_reading).append(", ");
sql.append(previous_reading).append(", ");
sql.append(usage_amount).append(", ");
sql.append(current_balance).append(", ");
sql.append(unit_price).append(", ");

// 分时电表只保存读数
if (is_time_sharing == 1) {
    sql.append(tipReading).append(", ");
    sql.append(peakReading).append(", ");
    sql.append(flatReading).append(", ");
    sql.append(valleyReading).append(", ");
} else {
    sql.append("0, 0, 0, 0, ");
}

// 结尾字段
sql.append(is_time_sharing).append(", ");
sql.append(status).append(", ");
sql.append("NOW(), ");
sql.append("'manual_input', ");
sql.append("'").append(remark != null ? remark : "手动录入").append("'");
```

### 4. **费用计算表保持完整**
```java
// 费用计算表包含完整的分时电表字段
costSql.append("tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, ");
costSql.append("peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, ");
costSql.append("flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, ");
costSql.append("valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, ");
```

## 📊 数据流程

### 1. **读数记录表** - 只保存原始读数
```
用户输入: 当前读数
系统获取: 上次读数、单价
计算: 用量 = 当前读数 - 上次读数
保存: 原始读数数据（不包含费用计算）
```

### 2. **费用计算表** - 保存完整计算结果
```
获取: 读数记录表的数据
计算: 各时段用量、单价、费用
保存: 完整的费用计算数据
```

### 3. **费用汇总表** - 按月统计
```
汇总: 费用计算表的数据
统计: 按设备类型、区域、月份
保存: 月度汇总数据
```

## 🎯 修复效果

修复后的代码：

1. **符合实际表结构**：SQL字段与数据库表完全匹配
2. **数据完整性**：读数记录表保存原始数据，费用计算表保存计算结果
3. **功能完整**：支持普通设备和分时电表的完整流程
4. **错误处理**：完善的异常捕获和日志记录

## 💡 关键区别

### 读数记录表 vs 费用计算表

| 字段类型 | 读数记录表 | 费用计算表 |
|---------|-----------|-----------|
| 基本读数 | ✅ 有 | ✅ 有 |
| 分时读数 | ✅ 有 | ✅ 有 |
| 上次分时读数 | ❌ 无 | ✅ 有 |
| 分时用量 | ❌ 无 | ✅ 有 |
| 分时单价 | ❌ 无 | ✅ 有 |
| 分时费用 | ❌ 无 | ✅ 有 |

### 设计理念

- **读数记录表**：记录原始的抄表数据，保持数据的原始性
- **费用计算表**：基于读数记录进行费用计算，包含完整的计算过程和结果
- **费用汇总表**：基于费用计算表进行统计汇总，用于报表和分析

现在SQL语句应该能够正确插入到实际的数据库表中了！
