# 水电费账单导出功能总结

## 功能概述

为水电费管理系统添加了完整的账单导出功能，支持Excel导出、PDF生成和打印预览，用于向租客提供详细的水电费账单。

## 🔧 主要功能

### 1. 前端界面增强

#### 工具栏按钮
```javascript
{
    xtype: 'button',
    itemId: 'exportBill',
    iconCls: 'x-fa fa-file-excel-o',
    text: '导出账单',
    menu: [
        {
            text: '导出Excel',
            iconCls: 'x-fa fa-file-excel-o',
            handler: function() { me.onExportExcel(); }
        },
        {
            text: '打印账单',
            iconCls: 'x-fa fa-print',
            handler: function() { me.onPrintBill(); }
        },
        {
            text: '生成PDF账单',
            iconCls: 'x-fa fa-file-pdf-o',
            handler: function() { me.onExportPDF(); }
        }
    ]
}
```

#### 导出方法
- **onExportExcel()** - 导出Excel格式账单
- **onPrintBill()** - 打印账单预览
- **onExportPDF()** - 生成PDF账单
- **doExport()** - 执行导出请求
- **showPrintPreview()** - 显示打印预览窗口

### 2. 后端控制器

#### WECostExportController
```java
@Controller
@RequestMapping("/Hotel/WECost")
public class WECostExportController {
    
    @RequestMapping("/exportBill")
    @ResponseBody
    public JsonResult exportBill(
        @RequestParam String startDate,
        @RequestParam String endDate,
        @RequestParam(required = false) String areaCode,
        @RequestParam String exportType,
        HttpServletResponse response
    );
    
    @RequestMapping("/generatePrintContent")
    @ResponseBody
    public JsonResult generatePrintContent(
        @RequestParam String startDate,
        @RequestParam String endDate,
        @RequestParam(required = false) String areaCode
    );
    
    @RequestMapping("/batchExportBill")
    @ResponseBody
    public JsonResult batchExportBill(
        @RequestParam String startDate,
        @RequestParam String endDate,
        @RequestParam String areaCodes,
        @RequestParam String exportType,
        HttpServletResponse response
    );
}
```

### 3. 导出服务类

#### WECostExportService
```java
@Service
public class WECostExportService extends BaseService {
    
    // Excel导出
    public JsonResult exportToExcel(String startDate, String endDate, String areaCode, HttpServletResponse response);
    
    // PDF导出
    public JsonResult exportToPDF(String startDate, String endDate, String areaCode, HttpServletResponse response);
    
    // 生成打印内容
    public String generatePrintContent(String startDate, String endDate, String areaCode);
    
    // 生成账单数据
    public Map<String, Object> generateBillData(String startDate, String endDate, String areaCode);
    
    // 批量导出
    public JsonResult batchExport(String startDate, String endDate, String[] areaCodes, String exportType, HttpServletResponse response);
}
```

## 📊 账单内容

### 1. 账单基本信息
```
- 账单标题：水电费账单
- 账单期间：2025-01-01 至 2025-01-05
- 生成时间：2025-01-15 14:30:00
- 区域信息：A栋宿舍楼
```

### 2. 费用明细表
| 设备编号 | 设备类型 | 区域编码 | 读数日期 | 当前读数 | 上次读数 | 用量 | 单价 | 费用 | 余额 |
|---------|---------|---------|---------|---------|---------|------|------|------|------|
| E001 | 电表 | AREA001 | 2025-01-05 | 1234.56 | 1200.00 | 34.56 | ￥1.20 | ￥41.47 | ￥158.53 |
| W001 | 水表 | AREA001 | 2025-01-05 | 456.78 | 450.00 | 6.78 | ￥3.00 | ￥20.34 | ￥79.66 |

### 3. 分时电表详细信息
对于分时电表，账单会显示：
```
尖时用量：5.12kWh × ￥1.5860 = ￥8.12
峰时用量：12.35kWh × ￥1.5000 = ￥18.52
平时用量：10.23kWh × ￥1.2000 = ￥12.28
谷时用量：6.86kWh × ￥0.9850 = ￥6.75
总计：￥45.67
```

### 4. 费用汇总
```
电费总计：￥150.00
水费总计：￥80.00
热水费总计：￥30.00
燃气费总计：￥40.00
总计：￥300.00
```

## 🎨 导出格式

### 1. Excel格式特点
- **专业样式**：标题、表头、数据区域使用不同样式
- **自动列宽**：根据内容自动调整列宽
- **金额格式**：货币格式显示，右对齐
- **边框线条**：完整的表格边框
- **合并单元格**：标题行合并显示

### 2. HTML/打印格式特点
- **响应式设计**：适配不同屏幕尺寸
- **打印优化**：@media print样式优化
- **清晰布局**：标题、信息、表格、汇总分区显示
- **专业外观**：类似正式发票的样式

### 3. PDF格式特点
- **便携性**：跨平台兼容
- **不可编辑**：保证账单完整性
- **高质量**：矢量图形，缩放不失真

## 🔍 数据查询逻辑

### 1. 费用记录查询
```sql
SELECT r.equipment_code, r.equipment_type, r.area_code, r.reading_date,
       r.current_reading, r.previous_reading, r.usage_amount, r.unit_price,
       r.tip_reading, r.previous_tip_reading, r.tip_usage, r.tip_price, r.tip_cost,
       r.peak_reading, r.previous_peak_reading, r.peak_usage, r.peak_price, r.peak_cost,
       r.flat_reading, r.previous_flat_reading, r.flat_usage, r.flat_price, r.flat_cost,
       r.valley_reading, r.previous_valley_reading, r.valley_usage, r.valley_price, r.valley_cost,
       r.total_cost, r.is_time_sharing, r.current_balance
FROM tb_equipment_reading_records r
WHERE r.status = 1
  AND DATE(r.reading_date) >= ? AND DATE(r.reading_date) <= ?
  AND r.area_code = ?
ORDER BY r.area_code, r.equipment_code, r.reading_date
```

### 2. 费用汇总计算
```java
// 按设备类型分类汇总
switch (equipmentType) {
    case "1": // 电表
        totalElectricUsage = totalElectricUsage.add(usage);
        totalElectricCost = totalElectricCost.add(cost);
        break;
    case "2": // 水表
        totalWaterUsage = totalWaterUsage.add(usage);
        totalWaterCost = totalWaterCost.add(cost);
        break;
    // ... 其他设备类型
}

// 计算总计
BigDecimal grandTotal = totalElectricCost.add(totalWaterCost)
                                        .add(totalHotWaterCost)
                                        .add(totalGasCost);
```

## 🚀 使用场景

### 1. 日常收费
```javascript
// 选择日期范围：2025-01-01 至 2025-01-05
// 选择区域：A栋宿舍楼
// 点击"导出Excel" -> 生成详细账单
```

### 2. 月度结算
```javascript
// 选择整月：2025-01-01 至 2025-01-31
// 选择全部区域
// 点击"生成PDF账单" -> 生成月度账单
```

### 3. 打印收据
```javascript
// 选择具体日期
// 选择特定区域
// 点击"打印账单" -> 打印预览 -> 打印
```

### 4. 批量导出
```javascript
// 选择多个区域
// 调用批量导出接口
// 生成压缩包下载
```

## ⚙️ 技术实现

### 1. Excel生成
```java
// 使用Apache POI
Workbook workbook = new XSSFWorkbook();
Sheet sheet = workbook.createSheet("水电费账单");

// 创建样式
Map<String, CellStyle> styles = createExcelStyles(workbook);

// 写入数据
writeExcelData(sheet, billData, styles);

// 输出文件
workbook.write(response.getOutputStream());
```

### 2. HTML生成
```java
// 使用StringBuilder构建HTML
StringBuilder html = new StringBuilder();
html.append("<!DOCTYPE html>");
html.append("<html><head>");
html.append("<style>/* CSS样式 */</style>");
html.append("</head><body>");
// 添加内容
html.append("</body></html>");
```

### 3. 前端打印
```javascript
// 创建打印预览窗口
var printWindow = Ext.create('Ext.window.Window', {
    title: '水电费账单打印预览',
    width: 800,
    height: 600,
    modal: true,
    items: [{
        xtype: 'component',
        autoEl: { tag: 'iframe' }
    }]
});

// 加载HTML内容到iframe
var iframe = printWindow.down('component').getEl().dom;
iframe.contentDocument.write(htmlContent);

// 调用打印
iframe.contentWindow.print();
```

## 📋 文件结构

```
campusos/
├── src/main/java/com/ymiots/campusos/
│   ├── controller/hotel/
│   │   └── WECostExportController.java     # 导出控制器
│   └── service/waterctrl/
│       └── WECostExportService.java        # 导出服务类
└── src/main/resources/static/app/view/hotel/wedaycost/
    └── WEDayCostView.js                    # 前端界面（已修改）
```

## 🎯 功能特色

### 1. 完整性
- ✅ **数据完整**：包含所有读数、用量、电价、费用信息
- ✅ **格式丰富**：支持Excel、PDF、HTML打印三种格式
- ✅ **信息详细**：分时电表显示四时段明细

### 2. 专业性
- ✅ **样式美观**：专业的账单样式设计
- ✅ **格式规范**：符合财务账单标准
- ✅ **数据准确**：直接从数据库获取，确保准确性

### 3. 易用性
- ✅ **操作简单**：一键导出，自动下载
- ✅ **预览功能**：打印前可预览效果
- ✅ **批量处理**：支持多区域批量导出

### 4. 扩展性
- ✅ **模板化**：易于修改账单模板
- ✅ **多格式**：易于扩展新的导出格式
- ✅ **可配置**：账单内容可根据需求调整

现在您的水电费管理系统已经具备了完整的账单导出功能，可以为租客提供专业、详细的水电费账单！
