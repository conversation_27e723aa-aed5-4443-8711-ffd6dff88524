# 设备读数记录表格完整方案

## 方案概述

基于 `tb_equipment_reading_records` 数据库表结构，设计了一个完整的设备读数记录显示界面，支持多种设备类型（电表、水表、热水表、气表），并实现了智能分时计费功能。

## 核心特性

### ✅ 1. 完整的四时段支持（尖峰平谷）

#### 数据库字段映射
```sql
-- 基础读数
current_reading     -- 当前读数
previous_reading    -- 上次读数
usage_amount        -- 用量

-- 分时读数（仅电表分时表）
tip_reading         -- 尖时读数
peak_reading        -- 峰时读数  
flat_reading        -- 平时读数
valley_reading      -- 谷时读数

-- 分时标识
is_time_sharing     -- 是否分时表(0否 1是)
```

#### 界面显示逻辑
```javascript
// 只有电表且启用分时计费时才显示分时读数
if (isTimeSharing == 1 && equipmentType == '1') {
    // 显示尖峰平谷四个时段的读数
    return value.toFixed(4) + ' kWh';
} else {
    // 非分时表显示灰色横线
    return '<span style="color: gray;">-</span>';
}
```

### ✅ 2. 智能费用计算

#### 分时计费逻辑
```javascript
calculateCost: function(record) {
    var isTimeSharing = record.get('is_time_sharing');
    var equipmentType = record.get('equipment_type');
    
    // 电表且启用分时计费
    if (equipmentType == '1' && isTimeSharing == 1) {
        return this.calculateTimeSharingCost(record, usage);
    } else {
        // 非分时计费，使用统一单价
        var unitPrice = record.get('unit_price') || 0;
        var cost = usage * unitPrice;
        return '<strong>￥' + cost.toFixed(2) + '</strong>';
    }
}
```

#### 分时费用计算
```javascript
calculateTimeSharingCost: function(record, totalUsage) {
    // 获取四个时段的读数和电价
    var tipReading = record.get('tip_reading') || 0;
    var peakReading = record.get('peak_reading') || 0;
    var flatReading = record.get('flat_reading') || 0;
    var valleyReading = record.get('valley_reading') || 0;
    
    var tipPrice = record.get('tip_price') || 0;
    var peakPrice = record.get('peak_price') || 0;
    var flatPrice = record.get('flat_price') || 0;
    var valleyPrice = record.get('valley_price') || 0;

    // 计算各时段费用
    var tipCost = tipReading * tipPrice;
    var peakCost = peakReading * peakPrice;
    var flatCost = flatReading * flatPrice;
    var valleyCost = valleyReading * valleyPrice;
    
    var totalCost = tipCost + peakCost + flatCost + valleyCost;
    
    // 显示总费用和明细
    var detail = '尖:￥' + tipCost.toFixed(2) + 
                ' 峰:￥' + peakCost.toFixed(2) + 
                ' 平:￥' + flatCost.toFixed(2) + 
                ' 谷:￥' + valleyCost.toFixed(2);
    
    return '<div><strong>￥' + totalCost.toFixed(2) + '</strong></div>' +
           '<div style="font-size: 10px; color: #666;">' + detail + '</div>';
}
```

### ✅ 3. 多设备类型支持

#### 设备类型映射
```javascript
var typeMap = {
    '1': '电表',      // 支持分时计费
    '2': '水表',      // 统一单价
    '4': '热水表',    // 统一单价
    '221': '气表'     // 统一单价
};

var unitMap = {
    '1': 'kWh',       // 电表单位
    '2': 'm³',        // 水表单位
    '4': 'm³',        // 热水表单位
    '221': 'm³'       // 气表单位
};
```

### ✅ 4. 列固定功能

#### 固定列配置
```javascript
enableLocking: true,  // 启用列锁定功能

// 固定的列
{
    text: '设备基本信息',
    locked: true,  // 固定设备基本信息列组
    columns: [
        {
            text: '设备编号',
            locked: true
        },
        {
            text: '设备类型', 
            locked: true
        }
        // ... 其他固定列
    ]
}
```

## 表格列结构

### 📋 完整列布局

```
[行号] [设备基本信息] [读数信息] [基础读数数据] [分时读数] [费用计算] [分时电价] [操作信息] [备注]
  |         |           |          |           |        |        |         |         |
固定      固定        可滚动     可滚动      可滚动   可滚动   可滚动    可滚动    可滚动
```

### 📊 列组详细说明

#### 1. 设备基本信息（固定列组）
- **设备编号**：equipment_code
- **设备类型**：equipment_type（1=电表，2=水表，4=热水表，221=气表）
- **是否分时表**：is_time_sharing（0=否，1=是）
- **区域编码**：area_code

#### 2. 读数信息
- **读数日期**：reading_date
- **读数时间**：reading_time

#### 3. 基础读数数据
- **当前读数**：current_reading（带单位显示）
- **上次读数**：previous_reading（带单位显示）
- **用量**：usage_amount（计算值或数据库值）

#### 4. 分时读数（仅分时电表显示）
- **尖时读数**：tip_reading
- **峰时读数**：peak_reading
- **平时读数**：flat_reading
- **谷时读数**：valley_reading

#### 5. 费用计算
- **单价**：unit_price（非分时表）或显示"分时计费"
- **当前余额**：current_balance（带颜色显示正负）
- **计算费用**：智能计算的费用（分时或统一单价）

#### 6. 分时电价（仅分时电表显示）
- **尖时电价**：tip_price
- **峰时电价**：peak_price
- **平时电价**：flat_price
- **谷时电价**：valley_price

#### 7. 操作信息
- **创建时间**：create_date
- **修改时间**：modify_date

#### 8. 备注信息
- **备注**：remark

## 界面效果示例

### 🔌 电表（分时表）显示效果
```
设备编号: E001
设备类型: 电表
是否分时表: 是
当前读数: 1234.5678 kWh
上次读数: 1200.0000 kWh
用量: 34.5678 kWh

分时读数:
尖时读数: 5.1234 kWh
峰时读数: 12.3456 kWh  
平时读数: 10.2345 kWh
谷时读数: 6.8643 kWh

费用计算: ￥45.67
         尖:￥8.12 峰:￥18.52 平:￥12.28 谷:￥6.75

分时电价:
尖时电价: ￥1.5860
峰时电价: ￥1.5000
平时电价: ￥1.2000
谷时电价: ￥0.9850
```

### 🚰 水表显示效果
```
设备编号: W001
设备类型: 水表
是否分时表: 否
当前读数: 456.7890 m³
上次读数: 450.0000 m³
用量: 6.7890 m³

分时读数: - - - -（灰色显示）

费用计算: ￥20.37
单价: ￥3.0000

分时电价: - - - -（灰色显示）
```

## 数据处理逻辑

### 💡 智能显示规则

#### 1. 分时数据显示
```javascript
// 只有电表且启用分时计费才显示分时相关数据
if (isTimeSharing == 1 && equipmentType == '1') {
    // 显示实际数据
    return value.toFixed(4) + ' kWh';
} else {
    // 显示灰色横线
    return '<span style="color: gray;">-</span>';
}
```

#### 2. 费用计算优先级
```javascript
// 1. 电表 + 分时表 → 使用分时电价计算
// 2. 其他情况 → 使用统一单价计算
// 3. 如果有分时读数 → 按各时段分别计算
// 4. 如果没有分时读数 → 按总用量 × 平时电价计算
```

#### 3. 单位自动适配
```javascript
var unit = me.getUnitByType(equipmentType);
// 电表: kWh
// 水表/热水表/气表: m³
```

## 技术特性

### 🔧 ExtJS功能特性

#### 1. 列锁定
- 前面的设备基本信息列固定不动
- 后面的数据列可以横向滚动
- 用户体验更好，关键信息始终可见

#### 2. 分组列头
- 使用嵌套columns实现分组显示
- 逻辑清晰，信息分类明确

#### 3. 智能渲染
- 根据数据动态显示内容
- 分时表和非分时表显示不同
- 费用计算实时进行

#### 4. 响应式设计
- 支持不同屏幕尺寸
- 列宽自适应
- 分页显示

### 📊 数据绑定

#### Store配置
```javascript
me.wecoststore = Ext.create('CamPus.view.hotel.wedaycost.WEDayCostStore');

// Store应该配置为从tb_equipment_reading_records表获取数据
proxy: {
    type: 'ajax',
    url: '/EquipmentReading/getReadingRecords',
    reader: {
        type: 'json',
        rootProperty: 'data',
        totalProperty: 'total'
    }
}
```

## 使用说明

### 🚀 部署步骤

1. **确保数据库表存在**
   ```sql
   -- 检查tb_equipment_reading_records表是否存在
   SHOW TABLES LIKE 'tb_equipment_reading_records';
   ```

2. **配置后端接口**
   - 创建对应的Controller处理数据查询
   - 确保返回的数据包含所有必要字段

3. **前端部署**
   - 将WEDayCostView.js部署到指定目录
   - 确保Store配置正确

4. **测试功能**
   - 测试不同设备类型的显示
   - 测试分时计费功能
   - 测试列固定功能

### 📝 数据要求

#### 必需字段
```javascript
// 基础字段
equipment_code      // 设备编号
equipment_type      // 设备类型
reading_date        // 读数日期
reading_time        // 读数时间
current_reading     // 当前读数
previous_reading    // 上次读数
is_time_sharing     // 是否分时表

// 分时字段（电表分时表需要）
tip_reading         // 尖时读数
peak_reading        // 峰时读数
flat_reading        // 平时读数
valley_reading      // 谷时读数
tip_price           // 尖时电价
peak_price          // 峰时电价
flat_price          // 平时电价
valley_price        // 谷时电价

// 费用字段
unit_price          // 统一单价
current_balance     // 当前余额
```

## 总结

这个完整的设备读数记录表格方案提供了：

### ✅ 完整功能
1. **四时段支持**：尖、峰、平、谷完整的分时计费
2. **多设备类型**：电表、水表、热水表、气表
3. **智能计费**：根据是否分时表自动选择计费方式
4. **列固定**：重要信息固定显示，数据可滚动查看

### ✅ 用户体验
1. **直观显示**：分组列头，信息分类清晰
2. **智能隐藏**：非分时表自动隐藏分时相关列
3. **实时计算**：费用自动计算并显示明细
4. **响应式设计**：适配不同屏幕尺寸

### ✅ 技术优势
1. **数据准确**：基于实际数据库表结构设计
2. **性能优化**：分页显示，列固定技术
3. **扩展性好**：易于添加新的设备类型和计费方式
4. **维护性强**：代码结构清晰，注释完整

现在您的界面已经完全支持tb_equipment_reading_records表的所有字段，并实现了完整的尖峰平谷四时段分时计费功能！
