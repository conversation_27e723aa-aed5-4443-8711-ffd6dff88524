# 设备读数录入与费用计算功能

## 功能概述

完善了设备读数手动录入功能，**支持实时费用计算**，用户在录入读数时可以立即看到计算出的用量和费用，确保数据准确性。

## ✅ 新增的费用计算功能

### 1. **实时费用计算**
- **自动触发**：当用户输入读数、单价时自动计算
- **即时显示**：用量和费用实时更新显示
- **分类计算**：普通设备和分时电表分别计算

### 2. **前端实时计算**
```javascript
/**
 * 计算费用（主方法）
 */
calculateCost: function() {
    var isTimeSharing = this.down('checkboxfield[name=is_time_sharing]').getValue();
    var equipmentType = this.down('combobox[name=equipment_type]').getValue();
    
    if (isTimeSharing && equipmentType === '1') {
        // 分时电表费用计算
        this.calculateTimeSharingCost();
    } else {
        // 普通设备费用计算
        this.calculateNormalCost();
    }
}
```

### 3. **普通设备计算**
```javascript
calculateNormalCost: function() {
    var currentReading = this.down('numberfield[name=current_reading]').getValue() || 0;
    var previousReading = this.down('numberfield[name=previous_reading]').getValue() || 0;
    var unitPrice = this.down('numberfield[name=unit_price]').getValue() || 0;
    
    // 计算用量
    var usage = currentReading - previousReading;
    if (usage < 0) usage = 0;
    
    // 计算费用
    var cost = usage * unitPrice;
    
    // 更新显示
    this.down('displayfield[itemId=usageDisplay]').setValue(usage.toFixed(2));
    this.down('displayfield[itemId=costDisplay]').setValue(cost.toFixed(2));
}
```

### 4. **分时电表计算**
```javascript
calculateTimeSharingCost: function() {
    // 获取各时段数据
    var tipUsage = Math.max(0, tipReading - previousTipReading);
    var tipCost = tipUsage * tipPrice;
    
    var peakUsage = Math.max(0, peakReading - previousPeakReading);
    var peakCost = peakUsage * peakPrice;
    
    var flatUsage = Math.max(0, flatReading - previousFlatReading);
    var flatCost = flatUsage * flatPrice;
    
    var valleyUsage = Math.max(0, valleyReading - previousValleyReading);
    var valleyCost = valleyUsage * valleyPrice;
    
    // 总计
    var totalUsage = tipUsage + peakUsage + flatUsage + valleyUsage;
    var totalCost = tipCost + peakCost + flatCost + valleyCost;
    
    // 显示详细信息
    this.down('displayfield[itemId=usageDisplay]').setValue(
        '尖:' + tipUsage.toFixed(2) + ' 峰:' + peakUsage.toFixed(2) + 
        ' 平:' + flatUsage.toFixed(2) + ' 谷:' + valleyUsage.toFixed(2) + 
        ' 总:' + totalUsage.toFixed(2)
    );
    
    this.down('displayfield[itemId=costDisplay]').setValue(
        '尖:' + tipCost.toFixed(2) + ' 峰:' + peakCost.toFixed(2) + 
        ' 平:' + flatCost.toFixed(2) + ' 谷:' + valleyCost.toFixed(2) + 
        ' 总:' + totalCost.toFixed(2)
    );
}
```

## 🔧 后端费用计算

### 1. **详细的计算日志**
```java
// 分时电表计算
if (is_time_sharing == 1 && "1".equals(equipment_type)) {
    Log.info(WECostController.class, "处理分时电表数据");
    
    // 尖时计算
    double tipUsage = tipReading - previousTipReading;
    double tipCost = tipUsage * tipPrice;
    
    Log.info(WECostController.class, String.format(
        "尖时: 读数%.2f, 上次%.2f, 用量%.2f, 单价%.4f, 费用%.2f", 
        tipReading, previousTipReading, tipUsage, tipPrice, tipCost));
    
    // ... 其他时段计算
    
    // 分时电表总费用 = 各时段费用之和
    double totalCost = tipCost + peakCost + flatCost + valleyCost;
    
    Log.info(WECostController.class, String.format(
        "分时电表总费用: %.2f (尖%.2f + 峰%.2f + 平%.2f + 谷%.2f)", 
        totalCost, tipCost, peakCost, flatCost, valleyCost));
}
```

### 2. **普通设备计算**
```java
else {
    Log.info(WECostController.class, "处理普通设备数据");
    
    // 普通设备总费用 = 用量 * 单价
    double totalCost = usage_amount * unit_price;
    
    Log.info(WECostController.class, String.format(
        "普通设备费用计算: 用量%.2f * 单价%.4f = 总费用%.2f", 
        usage_amount, unit_price, totalCost));
}
```

## 📊 界面显示效果

### 1. **普通设备显示**
```
用量: 50.50
费用(元): 176.75
```

### 2. **分时电表显示**
```
用量: 尖:12.50 峰:15.30 平:18.20 谷:10.80 总:56.80
费用(元): 尖:15.00 峰:12.24 平:10.92 谷:3.24 总:41.40
```

## 🎯 字段监听器

### 自动计算触发字段：
```javascript
// 普通读数字段
'current_reading', 'previous_reading', 'unit_price'

// 分时电表字段
'tip_reading', 'previous_tip_reading', 'tip_price',
'peak_reading', 'previous_peak_reading', 'peak_price',
'flat_reading', 'previous_flat_reading', 'flat_price',
'valley_reading', 'previous_valley_reading', 'valley_price'

// 每个字段都添加了 change 监听器
listeners: {
    change: function() {
        me.calculateCost();
    }
}
```

## 💡 计算规则

### 1. **用量计算**
```
用量 = 当前读数 - 上次读数
如果用量 < 0，则用量 = 0（防止负数）
```

### 2. **费用计算**

#### 普通设备：
```
费用 = 用量 × 单价
```

#### 分时电表：
```
尖时费用 = (尖时当前读数 - 尖时上次读数) × 尖时单价
峰时费用 = (峰时当前读数 - 峰时上次读数) × 峰时单价
平时费用 = (平时当前读数 - 平时上次读数) × 平时单价
谷时费用 = (谷时当前读数 - 谷时上次读数) × 谷时单价

总费用 = 尖时费用 + 峰时费用 + 平时费用 + 谷时费用
```

## 🚀 使用流程

### 1. **打开录入窗口**
```javascript
var window = Ext.create('CamPus.view.hotel.wedaycost.EquipmentReadingWindow', {
    areacode: areaCode,
    wecostgrid: me.wecostgrid
});
window.show();
```

### 2. **选择设备类型**
- 选择"电表"时，显示分时电表选项
- 选择其他类型时，隐藏分时选项

### 3. **输入读数数据**
- 输入当前读数 → 自动计算用量和费用
- 输入上次读数 → 重新计算用量和费用
- 输入单价 → 重新计算费用

### 4. **分时电表操作**
- 勾选"是否分时电表" → 显示四时段输入框
- 输入各时段读数和单价 → 实时计算各时段费用
- 自动汇总总用量和总费用

### 5. **保存数据**
- 前端验证通过 → 提交到后端
- 后端重新计算费用 → 保存到数据库
- 返回成功信息 → 刷新列表

## 📋 数据验证

### 前端验证：
- 当前读数不能小于上次读数
- 分时电表各时段读数验证
- 必填字段验证

### 后端验证：
- 重新计算所有费用
- 数据类型和范围验证
- SQL注入防护

## 🔍 调试信息

### 前端控制台输出：
```javascript
console.log('普通设备费用计算:', {
    currentReading: 1250.5,
    previousReading: 1200.0,
    usage: 50.5,
    unitPrice: 3.5,
    cost: 176.75
});

console.log('分时电表费用计算:', {
    tip: { usage: 12.5, cost: 15.0 },
    peak: { usage: 15.3, cost: 12.24 },
    flat: { usage: 18.2, cost: 10.92 },
    valley: { usage: 10.8, cost: 3.24 },
    total: { usage: 56.8, cost: 41.4 }
});
```

### 后端日志输出：
```
INFO - 尖时: 读数1200.00, 上次1150.00, 用量50.00, 单价1.2000, 费用60.00
INFO - 峰时: 读数1300.00, 上次1250.00, 用量50.00, 单价0.8000, 费用40.00
INFO - 平时: 读数1250.00, 上次1200.00, 用量50.00, 单价0.6000, 费用30.00
INFO - 谷时: 读数1250.00, 上次1200.00, 用量50.00, 单价0.3000, 费用15.00
INFO - 分时电表总费用: 145.00 (尖60.00 + 峰40.00 + 平30.00 + 谷15.00)
```

## 🎉 功能特点

### 1. **实时计算**
- 用户输入时立即看到计算结果
- 避免录入错误数据
- 提高录入效率

### 2. **详细显示**
- 普通设备显示总用量和总费用
- 分时电表显示各时段详细信息
- 便于核对和确认

### 3. **智能验证**
- 前端实时验证
- 后端二次验证
- 确保数据准确性

### 4. **完整日志**
- 前端控制台调试信息
- 后端详细计算日志
- 便于问题排查

现在您的设备读数录入功能不仅能保存数据，还能实时计算和显示费用，让用户在录入时就能看到准确的费用信息！
