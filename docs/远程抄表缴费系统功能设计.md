# 远程抄表缴费系统功能设计

## 系统概述

这是一个完整的远程抄表缴费管理系统，支持多种设备类型（电表、水表、热水表、燃气表），实现自动抄表、费用计算、月结账单生成、缴费管理等全流程功能。

## 🏗️ 系统架构

### 核心模块
```
远程抄表缴费系统
├── 设备管理模块
│   ├── 设备信息管理
│   ├── 设备数据同步
│   └── 设备状态监控
├── 抄表计费模块
│   ├── 自动抄表
│   ├── 费用计算
│   └── 分时计费
├── 房间管理模块
│   ├── 房间信息管理
│   ├── 房间设备关联
│   └── 入住管理
├── 月结账单模块
│   ├── 账单生成
│   ├── 账单查询
│   └── 账单统计
├── 缴费管理模块
│   ├── 缴费记录
│   ├── 缴费明细
│   └── 多种支付方式
└── 欠费管理模块
    ├── 欠费提醒
    ├── 逾期处理
    └── 催费管理
```

## 📊 数据库设计

### 核心表结构

#### 1. 房间信息表 (tb_room_info)
```sql
-- 房间基础信息
room_code        -- 房间编号 (主键)
room_name        -- 房间名称
room_type        -- 房间类型
building_code    -- 楼栋编号
building_name    -- 楼栋名称
floor_number     -- 楼层
area_code        -- 区域编码
contact_person   -- 联系人
contact_phone    -- 联系电话
room_status      -- 房间状态
occupant_count   -- 入住人数
```

#### 2. 房间设备关联表 (tb_room_equipment)
```sql
-- 房间与设备的关联关系
room_code        -- 房间编号
equipment_code   -- 设备编号
equipment_type   -- 设备类型 (1电表 2水表 4热水表 221气表)
equipment_name   -- 设备名称
is_main_meter    -- 是否主表
install_date     -- 安装日期
```

#### 3. 月结房间账单表 (tb_monthly_room_bill)
```sql
-- 房间月度账单汇总
bill_month       -- 账单月份 (YYYY-MM)
room_code        -- 房间编号
room_name        -- 房间名称

-- 电费信息
electric_usage   -- 电量用量
electric_amount  -- 电费金额
electric_tip_usage/amount    -- 尖时用量/费用
electric_peak_usage/amount   -- 峰时用量/费用
electric_flat_usage/amount   -- 平时用量/费用
electric_valley_usage/amount -- 谷时用量/费用

-- 其他费用
water_usage/amount      -- 水费用量/金额
hot_water_usage/amount  -- 热水用量/金额
gas_usage/amount        -- 燃气用量/金额

-- 费用汇总
total_amount     -- 总费用
paid_amount      -- 已缴费用
unpaid_amount    -- 未缴费用

-- 状态信息
bill_status      -- 账单状态 (1待缴费 2部分缴费 3已缴费 4已逾期)
payment_status   -- 缴费状态 (0未缴费 1已缴费)
due_date         -- 缴费截止日期
```

#### 4. 房间缴费记录表 (tb_room_payment_record)
```sql
-- 缴费记录
payment_no       -- 缴费单号
bill_id          -- 账单ID
room_code        -- 房间编号
bill_month       -- 账单月份
payment_amount   -- 缴费金额
payment_method   -- 缴费方式 (现金/银行卡/微信/支付宝/转账)
payment_date     -- 缴费时间
payment_person   -- 缴费人
cashier_name     -- 收费员
receipt_no       -- 收据号
transaction_id   -- 第三方交易号
```

#### 5. 房间缴费明细表 (tb_room_payment_detail)
```sql
-- 缴费明细
payment_id       -- 缴费记录ID
fee_type         -- 费用类型 (electric/water/hot_water/gas)
fee_name         -- 费用名称
usage_amount     -- 用量
unit_price       -- 单价
fee_amount       -- 费用金额
payment_amount   -- 本次缴费金额

-- 分时电费明细
time_period      -- 时段 (tip/peak/flat/valley)
period_usage     -- 时段用量
period_price     -- 时段单价
period_amount    -- 时段费用
```

## 🔄 业务流程

### 1. 自动抄表流程
```mermaid
graph TD
    A[定时任务启动] --> B[获取设备列表]
    B --> C[按采集器分组]
    C --> D[同步设备数据]
    D --> E[保存读数记录]
    E --> F[计算费用]
    F --> G[保存费用记录]
    G --> H[更新设备状态]
```

### 2. 月结账单生成流程
```mermaid
graph TD
    A[月结任务启动] --> B[获取房间列表]
    B --> C[汇总房间费用]
    C --> D[计算各类费用]
    D --> E[生成账单记录]
    E --> F[设置缴费截止日期]
    F --> G[发送账单通知]
```

### 3. 缴费处理流程
```mermaid
graph TD
    A[用户缴费] --> B[选择缴费方式]
    B --> C[确认缴费金额]
    C --> D[处理支付]
    D --> E[生成缴费记录]
    E --> F[更新账单状态]
    F --> G[生成收据]
    G --> H[发送缴费通知]
```

## 💰 费用计算逻辑

### 分时电费计算
```java
// 获取各时段用量和电价
tipUsage = tipCurrent - tipPrevious;
peakUsage = peakCurrent - peakPrevious;
flatUsage = flatCurrent - flatPrevious;
valleyUsage = valleyCurrent - valleyPrevious;

// 计算各时段费用
tipCost = tipUsage × tipPrice;
peakCost = peakUsage × peakPrice;
flatCost = flatUsage × flatPrice;
valleyCost = valleyUsage × valleyPrice;

// 总电费
totalElectricCost = tipCost + peakCost + flatCost + valleyCost;
```

### 非分时费用计算
```java
// 计算用量
usage = currentReading - previousReading;

// 计算费用
cost = usage × unitPrice;
```

### 房间费用汇总
```java
// 汇总房间所有设备费用
totalAmount = electricAmount + waterAmount + hotWaterAmount + gasAmount;
```

## 🏠 房间管理功能

### 房间信息管理
- **房间档案**：房间编号、名称、类型、楼栋、楼层等
- **入住管理**：入住人数、联系人、联系方式
- **状态管理**：正常、停用、维修等状态

### 房间设备关联
- **设备绑定**：房间与设备的关联关系
- **主表标识**：标识房间的主要计量设备
- **设备信息**：设备名称、安装位置、安装日期

### 房间费用查询
- **历史账单**：查询房间历史缴费记录
- **费用明细**：查看各类费用的详细构成
- **用量统计**：分析房间用量趋势

## 📋 月结账单功能

### 账单生成
- **自动生成**：定时任务自动生成月结账单
- **手动生成**：支持手动触发账单生成
- **批量生成**：支持批量生成多个房间账单

### 账单内容
- **费用汇总**：电费、水费、热水费、燃气费
- **用量明细**：各类设备的用量统计
- **分时明细**：电表分时用量和费用明细
- **缴费信息**：缴费状态、截止日期、逾期天数

### 账单查询
- **按房间查询**：查询指定房间的账单
- **按月份查询**：查询指定月份的所有账单
- **按状态查询**：查询待缴费、已缴费、逾期账单
- **统计报表**：收费统计、欠费统计等

## 💳 缴费管理功能

### 支付方式
- **现金支付**：现场现金缴费
- **银行卡支付**：POS机刷卡支付
- **微信支付**：微信扫码支付
- **支付宝支付**：支付宝扫码支付
- **银行转账**：银行转账缴费
- **在线支付**：网上银行等在线支付

### 缴费记录
- **缴费单号**：唯一的缴费流水号
- **缴费信息**：金额、方式、时间、缴费人
- **收费信息**：收费员、收据号
- **第三方信息**：交易号、支付渠道

### 缴费明细
- **费用分类**：按电费、水费等分类记录
- **分时明细**：电费分时段明细
- **用量明细**：各类用量的缴费明细

### 收据管理
- **电子收据**：生成电子收据
- **打印收据**：支持收据打印
- **收据查询**：查询历史收据

## 📢 欠费管理功能

### 欠费识别
- **自动识别**：系统自动识别逾期未缴费房间
- **欠费统计**：统计欠费金额、逾期天数
- **欠费排序**：按欠费金额、逾期时间排序

### 催费提醒
- **短信提醒**：发送催费短信
- **电话提醒**：电话催费记录
- **邮件提醒**：发送催费邮件
- **上门催费**：上门催费记录

### 欠费处理
- **分期缴费**：支持分期缴费
- **欠费减免**：特殊情况欠费减免
- **停用处理**：长期欠费停用处理

## 📊 统计报表功能

### 收费统计
- **月度统计**：月度收费金额、缴费率
- **年度统计**：年度收费趋势分析
- **房间统计**：各房间缴费情况
- **设备统计**：各类设备费用统计

### 用量统计
- **用量趋势**：各类设备用量趋势
- **分时统计**：电表分时用量统计
- **对比分析**：同比、环比分析

### 欠费统计
- **欠费分析**：欠费房间、金额统计
- **逾期分析**：逾期天数分布
- **催费效果**：催费方式效果分析

## 🔧 系统管理功能

### 用户权限
- **角色管理**：管理员、收费员、查询员等
- **权限控制**：功能权限、数据权限
- **操作日志**：记录用户操作日志

### 系统配置
- **电价配置**：分时电价、统一电价
- **费用配置**：水费、燃气费等单价
- **系统参数**：缴费截止日期、提醒设置

### 数据管理
- **数据备份**：定期数据备份
- **数据清理**：历史数据清理
- **数据导入导出**：支持数据导入导出

## 🚀 系统特色

### 1. 智能化
- **自动抄表**：定时自动抄取设备数据
- **自动计费**：自动计算各类费用
- **自动账单**：自动生成月结账单
- **智能提醒**：自动发送各类提醒

### 2. 精确化
- **分时计费**：支持电表分时计费
- **精确计算**：精确到小数点后4位
- **费用透明**：完整的费用计算链路
- **数据追溯**：完整的数据历史记录

### 3. 便民化
- **多种支付**：支持多种支付方式
- **在线缴费**：支持在线缴费
- **电子收据**：提供电子收据
- **短信通知**：及时发送各类通知

### 4. 管理化
- **统一管理**：统一的房间设备管理
- **权限控制**：完善的权限管理
- **报表分析**：丰富的统计报表
- **异常监控**：设备异常监控

现在您拥有了一个完整的远程抄表缴费系统，支持从设备抄表到费用计算、从账单生成到缴费管理的全流程功能！
