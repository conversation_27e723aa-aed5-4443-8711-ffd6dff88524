# EquipmentCostService 电价缓存功能完善

## 修改概述

在 `EquipmentCostService.java` 中添加了完整的电价缓存功能，支持从缓存获取电价信息并进行费用计算。

## 🔧 主要修改内容

### 1. 新增电价信息类

#### `PriceInfo` 类
```java
private static class PriceInfo {
    private BigDecimal unitPrice;      // 统一单价
    private BigDecimal tipPrice;       // 尖时电价
    private BigDecimal peakPrice;      // 峰时电价
    private BigDecimal flatPrice;      // 平时电价
    private BigDecimal valleyPrice;    // 谷时电价
    
    // getter/setter 方法
}
```

**功能**：封装电价信息，支持分时和非分时电价。

### 2. 电价缓存获取方法

#### `getPriceInfoFromCache()` 方法
```java
private PriceInfo getPriceInfoFromCache(EquipmentReadingRecord record) {
    PriceInfo priceInfo = new PriceInfo();
    String equipmentType = String.valueOf(record.getEquipmentType());
    String areaCode = record.getAreaCode();
    
    if (record.getIsTimeSharing() == 1 && "1".equals(equipmentType)) {
        // 分时电表，从缓存获取四时段电价
        priceInfo.setTipPrice(getPriceFromCache("tip_price", areaCode, equipmentType));
        priceInfo.setPeakPrice(getPriceFromCache("peak_price", areaCode, equipmentType));
        priceInfo.setFlatPrice(getPriceFromCache("flat_price", areaCode, equipmentType));
        priceInfo.setValleyPrice(getPriceFromCache("valley_price", areaCode, equipmentType));
    } else {
        // 非分时表，获取统一单价
        BigDecimal unitPrice = getPriceFromCache("unit_price", areaCode, equipmentType);
        priceInfo.setUnitPrice(unitPrice);
    }
    
    return priceInfo;
}
```

**功能**：根据设备类型和分时标识，从缓存获取相应的电价信息。

#### `getPriceFromCache()` 方法
```java
private BigDecimal getPriceFromCache(String priceType, String areaCode, String equipmentType) {
    // 构建缓存键：price_type_area_equipment
    String cacheKey = String.format("%s_%s_%s", priceType, areaCode, equipmentType);
    
    // 从Redis缓存获取
    String priceStr = redisService.get(cacheKey);
    if (priceStr != null && !priceStr.isEmpty()) {
        return new BigDecimal(priceStr);
    }
    
    // 缓存未命中，从数据库获取并缓存
    BigDecimal price = getPriceFromDatabase(priceType, areaCode, equipmentType);
    if (price != null) {
        // 缓存1小时
        redisService.setex(cacheKey, 3600, price.toString());
        return price;
    }
    
    // 返回默认价格
    return getDefaultPrice(priceType, equipmentType);
}
```

**功能**：
- 优先从Redis缓存获取电价
- 缓存未命中时从数据库获取并缓存
- 提供默认电价兜底

### 3. 数据库电价获取

#### `getPriceFromDatabase()` 方法
```java
private BigDecimal getPriceFromDatabase(String priceType, String areaCode, String equipmentType) {
    // 优先获取区域特定配置
    String sql = "SELECT config_value FROM tb_sys_config " +
                "WHERE config_key = ? AND area_code = ? AND equipment_type = ? AND status = 1";
    
    String configKey = String.format("%s_%s", priceType, equipmentType);
    List<Map<String, Object>> results = dbService.queryList(sql, configKey, areaCode, equipmentType);
    
    if (!results.isEmpty()) {
        Object value = results.get(0).get("config_value");
        return value != null ? new BigDecimal(value.toString()) : null;
    }
    
    // 如果区域特定配置不存在，尝试获取全局配置
    sql = "SELECT config_value FROM tb_sys_config " +
          "WHERE config_key = ? AND (area_code IS NULL OR area_code = '') AND equipment_type = ? AND status = 1";
    
    results = dbService.queryList(sql, configKey, equipmentType);
    if (!results.isEmpty()) {
        Object value = results.get(0).get("config_value");
        return value != null ? new BigDecimal(value.toString()) : null;
    }
    
    return null;
}
```

**功能**：
- 优先获取区域特定的电价配置
- 区域配置不存在时获取全局配置
- 支持灵活的配置层级

### 4. 默认电价配置

#### `getDefaultPrice()` 方法
```java
private BigDecimal getDefaultPrice(String priceType, String equipmentType) {
    switch (equipmentType) {
        case "1": // 电表
            switch (priceType) {
                case "tip_price": return new BigDecimal("1.5860");
                case "peak_price": return new BigDecimal("1.5000");
                case "flat_price": return new BigDecimal("1.2000");
                case "valley_price": return new BigDecimal("0.9850");
                case "unit_price": return new BigDecimal("1.2000");
            }
            break;
        case "2": // 水表
            return new BigDecimal("3.0000");
        case "4": // 热水表
            return new BigDecimal("4.0000");
        case "221": // 气表
            return new BigDecimal("2.5000");
    }
    return new BigDecimal("1.0000");
}
```

**功能**：提供各种设备类型的默认电价，确保系统稳定运行。

### 5. 费用计算方法

#### `calculateCostWithPrice()` 方法
```java
private CostCalculationResult calculateCostWithPrice(EquipmentReadingRecord record, PriceInfo priceInfo) {
    if (record.getIsTimeSharing() == 1 && record.getEquipmentType() == 1) {
        // 分时电表费用计算
        return calculateTimeSharingCost(record, priceInfo);
    } else {
        // 非分时表费用计算
        return calculateNormalCost(record, priceInfo);
    }
}
```

#### `calculateTimeSharingCost()` 方法
```java
private CostCalculationResult calculateTimeSharingCost(EquipmentReadingRecord record, PriceInfo priceInfo) {
    CostCalculationResult result = new CostCalculationResult();
    
    // 设置用量
    result.setTipUsage(record.getTipUsage() != null ? record.getTipUsage() : BigDecimal.ZERO);
    result.setPeakUsage(record.getPeakUsage() != null ? record.getPeakUsage() : BigDecimal.ZERO);
    result.setFlatUsage(record.getFlatUsage() != null ? record.getFlatUsage() : BigDecimal.ZERO);
    result.setValleyUsage(record.getValleyUsage() != null ? record.getValleyUsage() : BigDecimal.ZERO);
    
    // 计算各时段费用
    BigDecimal tipCost = result.getTipUsage().multiply(priceInfo.getTipPrice());
    BigDecimal peakCost = result.getPeakUsage().multiply(priceInfo.getPeakPrice());
    BigDecimal flatCost = result.getFlatUsage().multiply(priceInfo.getFlatPrice());
    BigDecimal valleyCost = result.getValleyUsage().multiply(priceInfo.getValleyPrice());
    
    result.setTipCost(tipCost);
    result.setPeakCost(peakCost);
    result.setFlatCost(flatCost);
    result.setValleyCost(valleyCost);
    
    // 计算总费用
    BigDecimal totalCost = tipCost.add(peakCost).add(flatCost).add(valleyCost);
    result.setTotalCost(totalCost);
    
    return result;
}
```

### 6. 修改更新方法

#### `updateCostCalculation()` 方法
```java
private JsonResult updateCostCalculation(EquipmentReadingRecord record) {
    // 从缓存获取电价信息
    PriceInfo priceInfo = getPriceInfoFromCache(record);

    // 计算费用（包含电价信息）
    CostCalculationResult costResult = calculateCostWithPrice(record, priceInfo);

    String updateSql = "UPDATE tb_equipment_cost_calculation SET " +
            "usage_amount = ?, unit_price = ?, total_cost = ?, " +
            "tip_usage = ?, tip_price = ?, tip_cost = ?, " +
            "peak_usage = ?, peak_price = ?, peak_cost = ?, " +
            "flat_usage = ?, flat_price = ?, flat_cost = ?, " +
            "valley_usage = ?, valley_price = ?, valley_cost = ?, " +
            "modify_date = ?, modifier_id = 'system' " +
            "WHERE reading_record_id = ? AND status = 1";

    int result = dbService.excuteSql(updateSql,
            record.getUsageAmount(),
            priceInfo.getUnitPrice(),
            costResult.getTotalCost(),
            // 尖时
            costResult.getTipUsage(),
            priceInfo.getTipPrice(),
            costResult.getTipCost(),
            // 峰时
            costResult.getPeakUsage(),
            priceInfo.getPeakPrice(),
            costResult.getPeakCost(),
            // 平时
            costResult.getFlatUsage(),
            priceInfo.getFlatPrice(),
            costResult.getFlatCost(),
            // 谷时
            costResult.getValleyUsage(),
            priceInfo.getValleyPrice(),
            costResult.getValleyCost(),
            currentDate,
            record.getUid()
    );
}
```

## 📊 缓存策略

### 缓存键设计
```
格式：{price_type}_{area_code}_{equipment_type}
示例：
- tip_price_A001_1     (A001区域电表尖时电价)
- peak_price_A001_1    (A001区域电表峰时电价)
- unit_price_B002_2    (B002区域水表单价)
```

### 缓存层级
```
1. Redis缓存 (优先级最高)
   ↓ 缓存未命中
2. 数据库区域配置
   ↓ 区域配置不存在
3. 数据库全局配置
   ↓ 全局配置不存在
4. 默认电价 (兜底)
```

### 缓存时间
- **缓存时长**：1小时 (3600秒)
- **更新策略**：被动更新（缓存过期后重新获取）
- **失效处理**：自动降级到数据库查询

## 🎯 功能特性

### 1. 智能电价获取
- **分时电表**：自动获取尖峰平谷四时段电价
- **非分时表**：获取统一单价
- **区域支持**：支持不同区域的差异化电价

### 2. 多层级配置
- **区域配置**：优先使用区域特定电价
- **全局配置**：区域配置不存在时使用全局电价
- **默认配置**：提供兜底电价确保系统稳定

### 3. 缓存优化
- **Redis缓存**：提高电价获取性能
- **自动缓存**：数据库查询结果自动缓存
- **缓存穿透保护**：默认电价防止缓存穿透

### 4. 完整费用计算
- **用量×电价**：准确的费用计算公式
- **分时计费**：支持四时段独立计费
- **电价保存**：将使用的电价保存到费用记录

## 📝 配置示例

### 数据库配置表 (tb_sys_config)
```sql
-- 分时电价配置
INSERT INTO tb_sys_config (config_key, config_value, area_code, equipment_type, status) VALUES
('tip_price_1', '1.5860', 'A001', '1', 1),
('peak_price_1', '1.5000', 'A001', '1', 1),
('flat_price_1', '1.2000', 'A001', '1', 1),
('valley_price_1', '0.9850', 'A001', '1', 1),

-- 非分时电价配置
('unit_price_1', '1.2000', 'A001', '1', 1),
('unit_price_2', '3.0000', 'A001', '2', 1),
('unit_price_4', '4.0000', 'A001', '4', 1),
('unit_price_221', '2.5000', 'A001', '221', 1);
```

### Redis缓存示例
```
tip_price_A001_1 = "1.5860"
peak_price_A001_1 = "1.5000"
flat_price_A001_1 = "1.2000"
valley_price_A001_1 = "0.9850"
unit_price_A001_2 = "3.0000"
```

## ✅ 修改优势

### 1. 性能优化
- **缓存加速**：Redis缓存提高电价获取速度
- **减少数据库压力**：避免频繁查询数据库
- **智能降级**：缓存失效时自动降级

### 2. 配置灵活
- **区域差异化**：支持不同区域的电价配置
- **设备类型支持**：支持多种设备类型的电价
- **动态更新**：电价配置可动态调整

### 3. 系统稳定
- **默认兜底**：提供默认电价确保系统稳定
- **异常处理**：完善的异常处理机制
- **日志记录**：详细的操作日志

### 4. 数据完整
- **电价保存**：将使用的电价保存到费用记录
- **计算透明**：费用计算过程完全透明
- **审计支持**：支持费用计算的审计追踪

现在您的费用计算服务已经具备了完整的电价缓存功能，能够高效、准确地进行费用计算！
