# 费用计算表字段完善总结

## 修改概述

在 `EquipmentCostService.java` 中修改了费用计算表的保存逻辑，添加了完整的读数信息（包括上次读数），使费用计算过程完全透明和可追溯。

## 🔧 主要修改内容

### 1. 修改 INSERT SQL

#### 新增字段
```sql
INSERT INTO tb_equipment_cost_calculation (
    uid, equipment_code, equipment_type, calculation_date, reading_record_id,
    -- ⭐ 基础读数信息
    current_reading, previous_reading, usage_amount, unit_price, total_cost,
    -- ⭐ 尖时完整信息
    tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost,
    -- ⭐ 峰时完整信息
    peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost,
    -- ⭐ 平时完整信息
    flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost,
    -- ⭐ 谷时完整信息
    valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost,
    -- ⭐ 其他信息
    previous_reading_time, is_time_sharing, area_code, status, create_date, creator_id
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, 'system')
```

### 2. 修改 UPDATE SQL

#### 完整更新字段
```sql
UPDATE tb_equipment_cost_calculation SET
    -- ⭐ 基础读数信息
    current_reading = ?, previous_reading = ?, usage_amount = ?, unit_price = ?, total_cost = ?,
    -- ⭐ 尖时完整信息
    tip_reading = ?, previous_tip_reading = ?, tip_usage = ?, tip_price = ?, tip_cost = ?,
    -- ⭐ 峰时完整信息
    peak_reading = ?, previous_peak_reading = ?, peak_usage = ?, peak_price = ?, peak_cost = ?,
    -- ⭐ 平时完整信息
    flat_reading = ?, previous_flat_reading = ?, flat_usage = ?, flat_price = ?, flat_cost = ?,
    -- ⭐ 谷时完整信息
    valley_reading = ?, previous_valley_reading = ?, valley_usage = ?, valley_price = ?, valley_cost = ?,
    -- ⭐ 其他信息
    previous_reading_time = ?, is_time_sharing = ?, modify_date = ?, modifier_id = 'system'
WHERE reading_record_id = ? AND status = 1
```

### 3. 参数传递完善

#### INSERT 参数列表
```java
int result = dbService.excuteSql(insertSql,
    uid, record.getEquipmentCode(), record.getEquipmentType(), 
    record.getReadingDate(), record.getUid(),
    // 基础读数信息
    record.getCurrentReading(),
    record.getPreviousReading(),
    record.getUsageAmount(),
    record.getUnitPrice(),
    costResult.getTotalCost(),
    // 尖时信息
    record.getTipReading(),
    record.getPreviousTipReading(),
    costResult.getTipUsage(),
    record.getTipPrice(),
    costResult.getTipCost(),
    // 峰时信息
    record.getPeakReading(),
    record.getPreviousPeakReading(),
    costResult.getPeakUsage(),
    record.getPeakPrice(),
    costResult.getPeakCost(),
    // 平时信息
    record.getFlatReading(),
    record.getPreviousFlatReading(),
    costResult.getFlatUsage(),
    record.getFlatPrice(),
    costResult.getFlatCost(),
    // 谷时信息
    record.getValleyReading(),
    record.getPreviousValleyReading(),
    costResult.getValleyUsage(),
    record.getValleyPrice(),
    costResult.getValleyCost(),
    // 其他信息
    record.getPreviousReadingTime(),
    record.getIsTimeSharing(),
    record.getAreaCode(),
    currentDate
);
```

## 📊 费用计算表数据结构

### 完整字段列表

#### 基础信息
```java
uid                    // 主键ID
equipment_code         // 设备编号
equipment_type         // 设备类型
calculation_date       // 计算日期
reading_record_id      // 读数记录ID
```

#### 基础读数信息
```java
current_reading        // 当前读数
previous_reading       // 上次读数
usage_amount          // 用量 (current_reading - previous_reading)
unit_price            // 单价（非分时表）
total_cost            // 总费用
```

#### 尖时信息
```java
tip_reading           // 当前尖时读数
previous_tip_reading  // 上次尖时读数
tip_usage            // 尖时用量 (tip_reading - previous_tip_reading)
tip_price            // 尖时电价
tip_cost             // 尖时费用 (tip_usage × tip_price)
```

#### 峰时信息
```java
peak_reading          // 当前峰时读数
previous_peak_reading // 上次峰时读数
peak_usage           // 峰时用量 (peak_reading - previous_peak_reading)
peak_price           // 峰时电价
peak_cost            // 峰时费用 (peak_usage × peak_price)
```

#### 平时信息
```java
flat_reading          // 当前平时读数
previous_flat_reading // 上次平时读数
flat_usage           // 平时用量 (flat_reading - previous_flat_reading)
flat_price           // 平时电价
flat_cost            // 平时费用 (flat_usage × flat_price)
```

#### 谷时信息
```java
valley_reading          // 当前谷时读数
previous_valley_reading // 上次谷时读数
valley_usage           // 谷时用量 (valley_reading - previous_valley_reading)
valley_price           // 谷时电价
valley_cost            // 谷时费用 (valley_usage × valley_price)
```

#### 其他信息
```java
previous_reading_time  // 上次读表时间
is_time_sharing       // 是否分时表
area_code             // 区域编码
status                // 状态
create_date           // 创建时间
creator_id            // 创建人ID
modify_date           // 修改时间
modifier_id           // 修改人ID
```

## 🎯 数据示例

### 分时电表费用计算记录
```json
{
    "uid": "uuid-1234",
    "equipment_code": "E001",
    "equipment_type": 1,
    "calculation_date": "2025-01-15",
    "reading_record_id": "record-uuid",
    
    // 基础读数信息
    "current_reading": 1234.5678,
    "previous_reading": 1200.0000,
    "usage_amount": 34.5678,
    "unit_price": null,
    "total_cost": 45.67,
    
    // 尖时信息
    "tip_reading": 105.1234,
    "previous_tip_reading": 100.0000,
    "tip_usage": 5.1234,
    "tip_price": 1.5860,
    "tip_cost": 8.12,
    
    // 峰时信息
    "peak_reading": 112.3456,
    "previous_peak_reading": 100.0000,
    "peak_usage": 12.3456,
    "peak_price": 1.5000,
    "peak_cost": 18.52,
    
    // 平时信息
    "flat_reading": 110.2345,
    "previous_flat_reading": 100.0000,
    "flat_usage": 10.2345,
    "flat_price": 1.2000,
    "flat_cost": 12.28,
    
    // 谷时信息
    "valley_reading": 106.8643,
    "previous_valley_reading": 100.0000,
    "valley_usage": 6.8643,
    "valley_price": 0.9850,
    "valley_cost": 6.75,
    
    // 其他信息
    "previous_reading_time": "2025-01-14 10:00:00",
    "is_time_sharing": 1,
    "area_code": "A001"
}
```

### 非分时表费用计算记录
```json
{
    "uid": "uuid-5678",
    "equipment_code": "W001",
    "equipment_type": 2,
    "calculation_date": "2025-01-15",
    "reading_record_id": "record-uuid",
    
    // 基础读数信息
    "current_reading": 456.7890,
    "previous_reading": 450.0000,
    "usage_amount": 6.7890,
    "unit_price": 3.0000,
    "total_cost": 20.37,
    
    // 分时信息（全部为null或0）
    "tip_reading": null,
    "previous_tip_reading": null,
    "tip_usage": null,
    "tip_price": null,
    "tip_cost": null,
    // ... 其他分时字段类似
    
    // 其他信息
    "previous_reading_time": "2025-01-14 10:00:00",
    "is_time_sharing": 0,
    "area_code": "A001"
}
```

## 🔍 费用计算验证

### 数据完整性验证
```sql
-- 验证用量计算
SELECT 
    equipment_code,
    current_reading - previous_reading AS calculated_usage,
    usage_amount AS stored_usage,
    CASE 
        WHEN ABS((current_reading - previous_reading) - usage_amount) < 0.0001 
        THEN '✓' ELSE '✗' 
    END AS usage_check
FROM tb_equipment_cost_calculation;

-- 验证分时用量总和
SELECT 
    equipment_code,
    tip_usage + peak_usage + flat_usage + valley_usage AS total_time_usage,
    usage_amount AS total_usage,
    CASE 
        WHEN ABS((tip_usage + peak_usage + flat_usage + valley_usage) - usage_amount) < 0.0001 
        THEN '✓' ELSE '✗' 
    END AS time_usage_check
FROM tb_equipment_cost_calculation 
WHERE is_time_sharing = 1;

-- 验证费用计算
SELECT 
    equipment_code,
    tip_usage * tip_price AS calculated_tip_cost,
    tip_cost AS stored_tip_cost,
    CASE 
        WHEN ABS((tip_usage * tip_price) - tip_cost) < 0.01 
        THEN '✓' ELSE '✗' 
    END AS tip_cost_check
FROM tb_equipment_cost_calculation 
WHERE is_time_sharing = 1;
```

## ✅ 修改优势

### 1. 费用计算透明
- **完整记录**：保存当前读数、上次读数、用量、电价、费用全链路
- **可追溯性**：任何时候都能验证费用计算的正确性
- **审计支持**：满足财务审计的要求

### 2. 数据完整性
- **读数保留**：保存完整的读数历史
- **电价记录**：记录计算时使用的电价
- **时间关联**：记录上次读表时间

### 3. 业务支持
- **分时计费**：完整支持四时段分时计费
- **多设备类型**：支持电表、水表、气表等
- **灵活查询**：支持各种维度的费用分析

### 4. 系统稳定性
- **数据验证**：可以验证计算结果的正确性
- **错误排查**：出现问题时可以快速定位
- **历史分析**：支持费用趋势分析

## 🚀 使用场景

### 1. 费用核对
```sql
-- 查看某设备的费用计算明细
SELECT 
    equipment_code,
    calculation_date,
    current_reading,
    previous_reading,
    usage_amount,
    tip_usage, tip_price, tip_cost,
    peak_usage, peak_price, peak_cost,
    flat_usage, flat_price, flat_cost,
    valley_usage, valley_price, valley_cost,
    total_cost
FROM tb_equipment_cost_calculation 
WHERE equipment_code = 'E001' 
ORDER BY calculation_date DESC;
```

### 2. 电价变更影响分析
```sql
-- 分析电价变更对费用的影响
SELECT 
    calculation_date,
    AVG(tip_price) AS avg_tip_price,
    AVG(peak_price) AS avg_peak_price,
    AVG(flat_price) AS avg_flat_price,
    AVG(valley_price) AS avg_valley_price,
    SUM(total_cost) AS total_cost
FROM tb_equipment_cost_calculation 
WHERE is_time_sharing = 1
GROUP BY calculation_date
ORDER BY calculation_date;
```

### 3. 用量趋势分析
```sql
-- 分析各时段用量趋势
SELECT 
    DATE_FORMAT(calculation_date, '%Y-%m') AS month,
    SUM(tip_usage) AS total_tip_usage,
    SUM(peak_usage) AS total_peak_usage,
    SUM(flat_usage) AS total_flat_usage,
    SUM(valley_usage) AS total_valley_usage
FROM tb_equipment_cost_calculation 
WHERE is_time_sharing = 1
GROUP BY DATE_FORMAT(calculation_date, '%Y-%m')
ORDER BY month;
```

现在您的费用计算表已经包含了完整的读数信息，费用计算过程完全透明和可追溯！
