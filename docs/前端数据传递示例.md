# 前端数据传递示例

## 问题说明

您的 `JsonResult` 类使用 `JSONObject` 作为 `data` 字段类型，需要正确构造返回数据。

## ✅ 正确的后端返回方式

### 1. 控制器返回示例

```java
@RequestMapping("/exportBill")
@ResponseBody
public JsonResult exportBill(@RequestParam String startDate,
                            @RequestParam String endDate,
                            @RequestParam(required = false) String areaCode,
                            @RequestParam String exportType,
                            HttpServletResponse response) {
    try {
        JsonResult result;
        
        switch (exportType.toLowerCase()) {
            case "excel":
                result = exportService.exportToExcel(startDate, endDate, areaCode, response);
                break;
            case "pdf":
                result = exportService.exportToPDF(startDate, endDate, areaCode, response);
                break;
            default:
                // 创建错误返回
                JsonResult errorResult = new JsonResult();
                errorResult.setSuccess(false);
                errorResult.setMsg("不支持的导出类型: " + exportType);
                return errorResult;
        }

        return result;

    } catch (Exception e) {
        Log.error(WECostExportController.class, 
            String.format("导出水电费账单失败: %s", e.getMessage()), e);
        
        // 创建异常返回
        JsonResult errorResult = new JsonResult();
        errorResult.setSuccess(false);
        errorResult.setMsg("导出失败: " + e.getMessage());
        return errorResult;
    }
}
```

### 2. 服务层返回示例

```java
public JsonResult exportToExcel(String startDate, String endDate, String areaCode, HttpServletResponse response) {
    try {
        // 1. 生成Excel文件
        // ... Excel生成逻辑 ...
        
        String fileName = String.format("水电费账单_%s至%s.xlsx", startDate, endDate);
        
        // 2. 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", 
            "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        
        // 3. 输出文件到响应流
        workbook.write(response.getOutputStream());
        workbook.close();
        response.getOutputStream().flush();
        response.getOutputStream().close();
        
        // 4. 创建成功返回（注意：文件已经通过response输出，这里主要是日志记录）
        JSONObject data = new JSONObject();
        data.put("fileName", fileName);
        data.put("exportTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        data.put("recordCount", recordCount);
        
        JsonResult result = new JsonResult();
        result.setSuccess(true);
        result.setMsg("导出成功");
        result.setData(data);
        return result;
        
    } catch (Exception e) {
        Log.error(WECostExportService.class, 
            String.format("Excel导出失败: %s", e.getMessage()), e);
        
        JsonResult result = new JsonResult();
        result.setSuccess(false);
        result.setMsg("Excel导出失败: " + e.getMessage());
        return result;
    }
}
```

### 3. 打印内容生成示例

```java
@RequestMapping("/generatePrintContent")
@ResponseBody
public JsonResult generatePrintContent(@RequestParam String startDate,
                                     @RequestParam String endDate,
                                     @RequestParam(required = false) String areaCode) {
    try {
        // 1. 生成HTML内容
        String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);
        
        // 2. 创建返回数据
        JSONObject data = new JSONObject();
        data.put("htmlContent", htmlContent);
        data.put("generateTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        data.put("startDate", startDate);
        data.put("endDate", endDate);
        data.put("areaCode", areaCode);

        // 3. 构造JsonResult
        JsonResult result = new JsonResult();
        result.setSuccess(true);
        result.setMsg("生成成功");
        result.setData(data);
        return result;

    } catch (Exception e) {
        Log.error(WECostExportController.class, 
            String.format("生成打印内容失败: %s", e.getMessage()), e);
        
        JsonResult result = new JsonResult();
        result.setSuccess(false);
        result.setMsg("生成失败: " + e.getMessage());
        return result;
    }
}
```

## 📱 前端接收数据示例

### 1. 前端Ajax请求

```javascript
// 导出Excel
onExportExcel: function() {
    var me = this;
    var params = me.getExportParams();
    
    if (!me.validateParams(params)) {
        return;
    }
    
    // 显示加载提示
    var loadingMask = new Ext.LoadMask({
        msg: '正在生成Excel账单，请稍候...',
        target: me
    });
    loadingMask.show();

    // 发送导出请求
    Ext.Ajax.request({
        url: '/Hotel/WECost/exportBill',
        method: 'POST',
        params: params,
        timeout: 300000, // 5分钟超时
        success: function(response) {
            loadingMask.hide();
            
            try {
                var result = Ext.decode(response.responseText);
                
                if (result.success) {
                    // 成功处理
                    var data = result.data;
                    Ext.Msg.alert('成功', 
                        '账单导出成功！<br/>' +
                        '文件名: ' + data.fileName + '<br/>' +
                        '导出时间: ' + data.exportTime + '<br/>' +
                        '记录数: ' + data.recordCount
                    );
                    
                    // 如果有下载链接，自动下载
                    if (data.downloadUrl) {
                        window.open(data.downloadUrl, '_blank');
                    }
                } else {
                    // 错误处理
                    Ext.Msg.alert('错误', result.msg || '导出失败');
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
            }
        },
        failure: function(response) {
            loadingMask.hide();
            Ext.Msg.alert('错误', '网络请求失败，请重试');
        }
    });
},

// 获取导出参数
getExportParams: function() {
    var me = this;
    var startDate = me.wecostgrid.down('datefield[itemId=starttime]').getValue();
    var endDate = me.wecostgrid.down('datefield[itemId=endtime]').getValue();
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';

    return {
        startDate: Ext.Date.format(startDate, 'Y-m-d'),
        endDate: Ext.Date.format(endDate, 'Y-m-d'),
        areaCode: areaCode,
        exportType: 'excel'
    };
},

// 验证参数
validateParams: function(params) {
    if (!params.startDate || !params.endDate) {
        Ext.Msg.alert('提示', '请选择日期范围');
        return false;
    }
    
    var startDate = new Date(params.startDate);
    var endDate = new Date(params.endDate);
    
    if (startDate > endDate) {
        Ext.Msg.alert('提示', '开始日期不能大于结束日期');
        return false;
    }
    
    return true;
}
```

### 2. 打印预览处理

```javascript
// 打印账单
onPrintBill: function() {
    var me = this;
    var params = me.getExportParams();
    params.exportType = 'print';
    
    if (!me.validateParams(params)) {
        return;
    }

    // 发送生成打印内容请求
    Ext.Ajax.request({
        url: '/Hotel/WECost/generatePrintContent',
        method: 'POST',
        params: params,
        success: function(response) {
            try {
                var result = Ext.decode(response.responseText);
                
                if (result.success) {
                    // 显示打印预览
                    me.showPrintPreview(result.data);
                } else {
                    Ext.Msg.alert('错误', result.msg || '生成打印内容失败');
                }
            } catch (e) {
                Ext.Msg.alert('错误', '响应数据解析失败');
            }
        },
        failure: function() {
            Ext.Msg.alert('错误', '网络请求失败');
        }
    });
},

// 显示打印预览
showPrintPreview: function(data) {
    var me = this;
    
    // 创建打印预览窗口
    var printWindow = Ext.create('Ext.window.Window', {
        title: '水电费账单打印预览 - ' + data.startDate + ' 至 ' + data.endDate,
        width: 800,
        height: 600,
        modal: true,
        maximizable: true,
        layout: 'fit',
        items: [{
            xtype: 'component',
            itemId: 'printContent',
            autoEl: {
                tag: 'iframe',
                style: 'width: 100%; height: 100%; border: none;'
            }
        }],
        buttons: [{
            text: '打印',
            iconCls: 'x-fa fa-print',
            handler: function() {
                var iframe = printWindow.down('component[itemId=printContent]').getEl().dom;
                iframe.contentWindow.print();
            }
        }, {
            text: '关闭',
            handler: function() {
                printWindow.close();
            }
        }]
    });

    printWindow.show();
    
    // 加载HTML内容到iframe
    var iframe = printWindow.down('component[itemId=printContent]').getEl().dom;
    var doc = iframe.contentDocument || iframe.contentWindow.document;
    doc.open();
    doc.write(data.htmlContent);
    doc.close();
}
```

## 🔑 关键要点

### 1. 后端返回格式
```java
// 成功返回
JsonResult result = new JsonResult();
result.setSuccess(true);
result.setMsg("操作成功");

JSONObject data = new JSONObject();
data.put("key1", "value1");
data.put("key2", "value2");
result.setData(data);

return result;

// 失败返回
JsonResult result = new JsonResult();
result.setSuccess(false);
result.setMsg("错误信息");
return result;
```

### 2. 前端接收格式
```javascript
// Ajax成功回调
success: function(response) {
    var result = Ext.decode(response.responseText);
    
    if (result.success) {
        // 成功处理
        var data = result.data;  // JSONObject数据
        console.log(data.key1);  // 访问具体字段
        console.log(data.key2);
    } else {
        // 错误处理
        Ext.Msg.alert('错误', result.msg);
    }
}
```

### 3. 文件下载处理
对于Excel/PDF导出，有两种方式：

**方式1：直接响应文件流**
```java
// 后端直接输出文件到response
response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
workbook.write(response.getOutputStream());
```

**方式2：返回下载链接**
```java
// 后端保存文件到临时目录，返回下载链接
JSONObject data = new JSONObject();
data.put("downloadUrl", "/download/" + fileName);
result.setData(data);

// 前端处理
if (data.downloadUrl) {
    window.open(data.downloadUrl, '_blank');
}
```

现在您的数据传递应该可以正常工作了！
