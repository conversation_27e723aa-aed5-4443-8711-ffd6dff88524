# 修改功能完善说明

## 功能概述

将设备读数的修改功能也改为使用新的 `EquipmentReadingWindow` 窗口，保持界面的一致性。

## ✅ 前端修改

### 1. **修改控制器中的编辑方法**

**文件**: `campusos/src/main/resources/static/app/view/hotel/wedaycost/WEDayCostController.js`

```javascript
onEditClick:function(){
    var me = this;
    if (me.wecostgrid.getSelection().length == 0) {
        toast('请选择需修改的设备读数记录...');
        return;
    }
    var node = me.wecostgrid.getSelection()[0];
    
    // 使用新的设备读数窗口
    var editWin = Ext.create('CamPus.view.hotel.wedaycost.EquipmentReadingWindow',{
        title: '修改设备读数',
        wecostgrid: me.wecostgrid,
        areacode: node.get('area_code') || node.get('areaCode')
    });
    
    // 加载现有数据到表单
    var form = editWin.down('form[itemId=readingForm]');
    if (form) {
        // 设置ID（用于判断是修改操作）
        form.down('numberfield[itemId=id]').setValue(node.get('id') || node.get('uid'));
        
        // 设置基本字段
        form.down('textfield[name=equipment_code]').setValue(node.get('equipment_code'));
        form.down('combobox[name=equipment_type]').setValue(node.get('equipment_type'));
        form.down('textfield[name=area_code]').setValue(node.get('area_code') || node.get('areaCode'));
        
        // 设置读数时间
        var readingDate = node.get('reading_date') || node.get('reading_time');
        if (readingDate) {
            var dateField = form.down('datetimefield[name=reading_date]');
            if (dateField) {
                dateField.setValue(new Date(readingDate));
            }
        }
        
        // 设置读数信息
        form.down('numberfield[name=current_reading]').setValue(node.get('current_reading'));
        form.down('numberfield[name=current_balance]').setValue(node.get('current_balance'));
        
        // 检查是否是分时电表
        var isTimeSharing = node.get('is_time_sharing');
        var equipmentType = node.get('equipment_type');
        
        if (isTimeSharing == 1 && equipmentType == '1') {
            // 设置分时电表选项
            form.down('checkboxfield[name=is_time_sharing]').setValue(true);
            
            // 触发显示分时电表字段
            editWin.onEquipmentTypeChange('1');
            editWin.onTimeSharingChange(true);
            
            // 设置分时读数
            var tipField = form.down('numberfield[name=tip_reading]');
            var peakField = form.down('numberfield[name=peak_reading]');
            var flatField = form.down('numberfield[name=flat_reading]');
            var valleyField = form.down('numberfield[name=valley_reading]');
            
            if (tipField) tipField.setValue(node.get('tip_reading'));
            if (peakField) peakField.setValue(node.get('peak_reading'));
            if (flatField) flatField.setValue(node.get('flat_reading'));
            if (valleyField) valleyField.setValue(node.get('valley_reading'));
        } else {
            form.down('checkboxfield[name=is_time_sharing]').setValue(false);
            editWin.onTimeSharingChange(false);
        }
        
        // 设置其他字段
        form.down('textareafield[name=remark]').setValue(node.get('remark'));
        form.down('combobox[name=status]').setValue(node.get('status') || 1);
        
        // 触发费用计算
        editWin.calculateCost();
    }
    
    editWin.show();
}
```

## 🔧 后端修改

### 1. **修改保存方法支持UPDATE操作**

**文件**: `campusos/src/main/java/com/ymiots/campusos/controller/hotel/WECostController.java`

#### 添加ID参数：
```java
@RequestMapping("/saveEquipmentReading")
@Permission(menu = menuid)
public JsonResult saveEquipmentReading(HttpServletRequest request,
                                     @RequestParam(required = false, defaultValue = "0") Integer id,  // 新增ID参数
                                     @RequestParam String equipment_code,
                                     // ... 其他参数
```

#### 判断操作类型：
```java
// 判断是新增还是修改
boolean isUpdate = (id != null && id > 0);
String operation = isUpdate ? "修改" : "新增";

Log.info(WECostController.class, 
    String.format("%s设备读数 - ID: %s, 设备: %s", operation, id, equipment_code));
```

#### 构建不同的SQL：
```java
if (isUpdate) {
    // 修改操作 - UPDATE语句
    StringBuilder updateSql = new StringBuilder();
    updateSql.append("UPDATE tb_equipment_reading_records SET ");
    updateSql.append("equipment_code = '").append(equipment_code).append("', ");
    updateSql.append("equipment_type = '").append(equipment_type).append("', ");
    updateSql.append("area_code = '").append(area_code).append("', ");
    updateSql.append("reading_date = '").append(reading_date.substring(0, 10)).append("', ");
    updateSql.append("reading_time = '").append(reading_date).append("', ");
    updateSql.append("current_reading = ").append(current_reading).append(", ");
    updateSql.append("previous_reading = ").append(previous_reading).append(", ");
    updateSql.append("usage_amount = ").append(usage_amount).append(", ");
    updateSql.append("current_balance = ").append(current_balance).append(", ");
    updateSql.append("unit_price = ").append(unit_price).append(", ");
    
    // 分时电表字段
    if (is_time_sharing == 1 && "1".equals(equipment_type)) {
        updateSql.append("tip_reading = ").append(tipReading).append(", ");
        updateSql.append("peak_reading = ").append(peakReading).append(", ");
        updateSql.append("flat_reading = ").append(flatReading).append(", ");
        updateSql.append("valley_reading = ").append(valleyReading).append(", ");
    } else {
        updateSql.append("tip_reading = 0, peak_reading = 0, flat_reading = 0, valley_reading = 0, ");
    }
    
    updateSql.append("is_time_sharing = ").append(is_time_sharing).append(", ");
    updateSql.append("status = ").append(status).append(", ");
    updateSql.append("modify_date = NOW(), ");
    updateSql.append("modifier_id = 'manual_input', ");
    updateSql.append("remark = '").append(remark != null ? remark : "手动修改").append("' ");
    updateSql.append("WHERE id = ").append(id);
    
    result = weCostService.dbService.ExecuteUpdate(updateSql.toString());
    
} else {
    // 新增操作 - 使用原有的INSERT语句
    // ... 原有的INSERT逻辑
}
```

## 📊 数据流程

### 1. **新增流程**
```
用户点击"新增" → 打开空白窗口 → 录入数据 → 保存 → INSERT到数据库
```

### 2. **修改流程**
```
用户选择记录 → 点击"修改" → 打开窗口并加载数据 → 修改数据 → 保存 → UPDATE数据库
```

### 3. **判断逻辑**
```java
// 前端：通过ID字段判断
if (form.down('numberfield[itemId=id]').getValue() > 0) {
    // 修改操作
} else {
    // 新增操作
}

// 后端：通过ID参数判断
boolean isUpdate = (id != null && id > 0);
```

## 🎯 关键特性

### 1. **界面一致性**
- 新增和修改使用相同的窗口
- 相同的字段布局和验证规则
- 相同的费用计算逻辑

### 2. **数据完整性**
- 修改时保留原有数据结构
- 支持分时电表的完整修改
- 自动重新计算费用

### 3. **操作便利性**
- 自动加载现有数据到表单
- 支持分时电表状态的切换
- 实时费用计算和显示

## 💡 使用效果

修改完成后：

1. **统一界面**：新增和修改使用相同的现代化界面
2. **简化操作**：用户只需输入当前读数，系统自动处理其他计算
3. **数据准确**：修改时保持数据的完整性和一致性
4. **功能完整**：支持普通设备和分时电表的完整修改流程

## 🚀 实施步骤

1. **修改前端控制器**：更新 `onEditClick` 方法
2. **修改后端接口**：添加ID参数和UPDATE逻辑
3. **测试功能**：验证新增和修改功能
4. **部署上线**：确保功能正常运行

现在修改功能也使用了新的设备读数录入窗口，保持了界面的一致性和功能的完整性！
