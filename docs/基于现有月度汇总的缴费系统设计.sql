-- 基于现有 tb_equipment_cost_summary 的缴费系统设计
-- <AUTHOR>
-- @date 2025-01-15

-- =====================================================
-- 1. 扩展现有的 tb_equipment_cost_summary 表，添加缴费相关字段
-- =====================================================
ALTER TABLE `tb_equipment_cost_summary`
ADD COLUMN `paid_amount` decimal(15,2) DEFAULT 0 COMMENT '已缴费金额' AFTER `total_cost`,
ADD COLUMN `unpaid_amount` decimal(15,2) DEFAULT 0 COMMENT '未缴费金额' AFTER `paid_amount`,
ADD COLUMN `payment_status` int(11) DEFAULT 0 COMMENT '缴费状态(0未缴费 1部分缴费 2已缴费)' AFTER `unpaid_amount`,
ADD COLUMN `due_date` date DEFAULT NULL COMMENT '缴费截止日期' AFTER `payment_status`,
ADD COLUMN `last_payment_date` datetime DEFAULT NULL COMMENT '最后缴费时间' AFTER `due_date`,
ADD COLUMN `payment_count` int(11) DEFAULT 0 COMMENT '缴费次数' AFTER `last_payment_date`;

-- 更新未缴费金额（初始化时等于总费用）
UPDATE `tb_equipment_cost_summary` 
SET `unpaid_amount` = `total_cost` 
WHERE `unpaid_amount` = 0 AND `total_cost` > 0;

-- =====================================================
-- 2. 创建缴费记录表
-- =====================================================
CREATE TABLE `tb_equipment_payment_record` (
  `uid` varchar(36) NOT NULL COMMENT '主键ID',
  `payment_no` varchar(50) NOT NULL COMMENT '缴费单号',
  `summary_id` varchar(36) NOT NULL COMMENT '费用汇总ID',
  `area_code` varchar(20) NOT NULL COMMENT '区域编码',
  `summary_month` varchar(7) NOT NULL COMMENT '汇总月份',
  
  -- 缴费信息
  `payment_amount` decimal(15,2) NOT NULL COMMENT '缴费金额',
  `payment_method` varchar(20) NOT NULL COMMENT '缴费方式(cash/card/wechat/alipay/transfer/online)',
  `payment_date` datetime NOT NULL COMMENT '缴费时间',
  `payment_person` varchar(50) DEFAULT NULL COMMENT '缴费人',
  `payment_phone` varchar(20) DEFAULT NULL COMMENT '缴费人电话',
  
  -- 收费信息
  `cashier_id` varchar(36) DEFAULT NULL COMMENT '收费员ID',
  `cashier_name` varchar(50) DEFAULT NULL COMMENT '收费员姓名',
  `receipt_no` varchar(50) DEFAULT NULL COMMENT '收据号',
  
  -- 第三方支付信息
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方交易号',
  `payment_channel` varchar(50) DEFAULT NULL COMMENT '支付渠道',
  `payment_status` int(11) DEFAULT 1 COMMENT '支付状态(1成功 2失败 3退款)',
  
  -- 费用分配明细（JSON格式存储各类费用的分配）
  `cost_allocation` text DEFAULT NULL COMMENT '费用分配明细(JSON格式)',
  
  -- 其他信息
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` int(11) DEFAULT 1 COMMENT '记录状态(1正常 0删除)',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator_id` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `modify_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `modifier_id` varchar(36) DEFAULT NULL COMMENT '修改人ID',
  
  PRIMARY KEY (`uid`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_summary_id` (`summary_id`),
  KEY `idx_area_code` (`area_code`),
  KEY `idx_summary_month` (`summary_month`),
  KEY `idx_payment_date` (`payment_date`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_cashier_id` (`cashier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备费用缴费记录表';

-- =====================================================
-- 3. 创建欠费提醒表
-- =====================================================
CREATE TABLE `tb_equipment_arrears_notice` (
  `uid` varchar(36) NOT NULL COMMENT '主键ID',
  `summary_id` varchar(36) NOT NULL COMMENT '费用汇总ID',
  `area_code` varchar(20) NOT NULL COMMENT '区域编码',
  `summary_month` varchar(7) NOT NULL COMMENT '欠费月份',
  `arrears_amount` decimal(15,2) NOT NULL COMMENT '欠费金额',
  `overdue_days` int(11) DEFAULT 0 COMMENT '逾期天数',
  
  -- 提醒信息
  `notice_type` varchar(20) NOT NULL COMMENT '提醒类型(sms/email/phone/system)',
  `notice_content` text DEFAULT NULL COMMENT '提醒内容',
  `notice_date` datetime NOT NULL COMMENT '提醒时间',
  `notice_person` varchar(50) DEFAULT NULL COMMENT '提醒人',
  `notice_status` int(11) DEFAULT 1 COMMENT '提醒状态(1已发送 2已读 3已处理)',
  
  -- 联系信息
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  
  -- 其他信息
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` int(11) DEFAULT 1 COMMENT '记录状态(1正常 0删除)',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator_id` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  
  PRIMARY KEY (`uid`),
  KEY `idx_summary_id` (`summary_id`),
  KEY `idx_area_code` (`area_code`),
  KEY `idx_summary_month` (`summary_month`),
  KEY `idx_notice_date` (`notice_date`),
  KEY `idx_notice_status` (`notice_status`),
  KEY `idx_overdue_days` (`overdue_days`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备费用欠费提醒表';

-- =====================================================
-- 4. 创建视图：费用汇总缴费视图
-- =====================================================
CREATE OR REPLACE VIEW `v_equipment_cost_payment_summary` AS
SELECT 
    s.uid,
    s.area_code,
    s.summary_month,
    s.water_total_usage,
    s.water_total_cost,
    s.electric_total_usage,
    s.electric_total_cost,
    s.hot_water_total_usage,
    s.hot_water_total_cost,
    s.gas_total_usage,
    s.gas_total_cost,
    s.total_cost,
    s.paid_amount,
    s.unpaid_amount,
    s.payment_status,
    s.due_date,
    s.last_payment_date,
    s.payment_count,
    
    -- 缴费状态描述
    CASE s.payment_status
        WHEN 0 THEN '未缴费'
        WHEN 1 THEN '部分缴费'
        WHEN 2 THEN '已缴费'
        ELSE '未知'
    END AS payment_status_desc,
    
    -- 逾期信息
    CASE 
        WHEN s.payment_status < 2 AND s.due_date < CURDATE() 
        THEN DATEDIFF(CURDATE(), s.due_date)
        ELSE 0 
    END AS overdue_days,
    
    -- 缴费进度
    CASE 
        WHEN s.total_cost > 0 
        THEN ROUND(s.paid_amount / s.total_cost * 100, 2)
        ELSE 0 
    END AS payment_progress,
    
    -- 最近缴费记录
    pr.recent_payment_amount,
    pr.recent_payment_date,
    pr.recent_payment_method
    
FROM tb_equipment_cost_summary s
LEFT JOIN (
    SELECT 
        summary_id,
        payment_amount AS recent_payment_amount,
        payment_date AS recent_payment_date,
        payment_method AS recent_payment_method,
        ROW_NUMBER() OVER (PARTITION BY summary_id ORDER BY payment_date DESC) AS rn
    FROM tb_equipment_payment_record 
    WHERE status = 1 AND payment_status = 1
) pr ON s.uid = pr.summary_id AND pr.rn = 1
WHERE s.status = 1;

-- =====================================================
-- 5. 创建索引优化
-- =====================================================

-- 费用汇总表索引
ALTER TABLE tb_equipment_cost_summary ADD INDEX idx_payment_status (payment_status);
ALTER TABLE tb_equipment_cost_summary ADD INDEX idx_due_date (due_date);
ALTER TABLE tb_equipment_cost_summary ADD INDEX idx_unpaid_amount (unpaid_amount);
ALTER TABLE tb_equipment_cost_summary ADD INDEX idx_area_month_payment (area_code, summary_month, payment_status);

-- 缴费记录表索引
ALTER TABLE tb_equipment_payment_record ADD INDEX idx_payment_method (payment_method);
ALTER TABLE tb_equipment_payment_record ADD INDEX idx_payment_amount (payment_amount);
ALTER TABLE tb_equipment_payment_record ADD INDEX idx_area_month (area_code, summary_month);

-- =====================================================
-- 6. 常用查询SQL
-- =====================================================

-- 查询区域月度费用汇总（含缴费状态）
SELECT 
    area_code,
    summary_month,
    total_cost,
    paid_amount,
    unpaid_amount,
    payment_status_desc,
    overdue_days,
    payment_progress
FROM v_equipment_cost_payment_summary 
ORDER BY summary_month DESC, area_code;

-- 查询欠费区域
SELECT 
    area_code,
    summary_month,
    unpaid_amount,
    overdue_days,
    due_date
FROM v_equipment_cost_payment_summary 
WHERE payment_status < 2 
  AND due_date < CURDATE()
ORDER BY overdue_days DESC, unpaid_amount DESC;

-- 查询缴费记录
SELECT 
    pr.payment_no,
    pr.area_code,
    pr.summary_month,
    pr.payment_amount,
    pr.payment_method,
    pr.payment_date,
    pr.payment_person,
    pr.cashier_name,
    pr.cost_allocation
FROM tb_equipment_payment_record pr
WHERE pr.status = 1 AND pr.payment_status = 1
ORDER BY pr.payment_date DESC;

-- 查询月度收费统计
SELECT 
    summary_month,
    COUNT(*) AS total_areas,
    SUM(total_cost) AS total_amount,
    SUM(paid_amount) AS paid_amount,
    SUM(unpaid_amount) AS unpaid_amount,
    SUM(CASE WHEN payment_status = 2 THEN 1 ELSE 0 END) AS paid_count,
    SUM(CASE WHEN payment_status < 2 THEN 1 ELSE 0 END) AS unpaid_count,
    ROUND(SUM(paid_amount) / SUM(total_cost) * 100, 2) AS payment_rate
FROM tb_equipment_cost_summary 
WHERE status = 1
GROUP BY summary_month
ORDER BY summary_month DESC;

-- 查询区域费用明细
SELECT 
    s.area_code,
    s.summary_month,
    s.water_total_usage,
    s.water_total_cost,
    s.electric_total_usage,
    s.electric_total_cost,
    s.hot_water_total_usage,
    s.hot_water_total_cost,
    s.gas_total_usage,
    s.gas_total_cost,
    s.total_cost,
    s.paid_amount,
    s.unpaid_amount
FROM tb_equipment_cost_summary s
WHERE s.status = 1
ORDER BY s.summary_month DESC, s.area_code;

-- =====================================================
-- 7. 示例数据插入
-- =====================================================

-- 更新现有汇总记录的缴费截止日期（下月15日）
UPDATE tb_equipment_cost_summary 
SET due_date = DATE_ADD(STR_TO_DATE(CONCAT(summary_month, '-01'), '%Y-%m-%d'), INTERVAL 45 DAY)
WHERE due_date IS NULL;

-- 插入示例缴费记录
INSERT INTO tb_equipment_payment_record (
    uid, payment_no, summary_id, area_code, summary_month,
    payment_amount, payment_method, payment_date, payment_person,
    cashier_name, receipt_no, cost_allocation
) VALUES (
    UUID(), 
    CONCAT('PAY', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s')),
    (SELECT uid FROM tb_equipment_cost_summary WHERE area_code = 'AREA001' AND summary_month = '2025-01' LIMIT 1),
    'AREA001',
    '2025-01',
    1500.00,
    'wechat',
    NOW(),
    '张三',
    '收费员001',
    CONCAT('REC', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s')),
    '{"electric_cost": 800.00, "water_cost": 400.00, "gas_cost": 300.00}'
);

-- =====================================================
-- 8. 触发器：自动更新缴费状态
-- =====================================================
DELIMITER $$

CREATE TRIGGER `tr_update_payment_status_after_payment` 
AFTER INSERT ON `tb_equipment_payment_record`
FOR EACH ROW
BEGIN
    DECLARE total_paid DECIMAL(15,2) DEFAULT 0;
    DECLARE total_cost DECIMAL(15,2) DEFAULT 0;
    DECLARE new_status INT DEFAULT 0;
    
    -- 计算该汇总记录的总缴费金额
    SELECT COALESCE(SUM(payment_amount), 0) INTO total_paid
    FROM tb_equipment_payment_record 
    WHERE summary_id = NEW.summary_id AND status = 1 AND payment_status = 1;
    
    -- 获取总费用
    SELECT COALESCE(total_cost, 0) INTO total_cost
    FROM tb_equipment_cost_summary 
    WHERE uid = NEW.summary_id;
    
    -- 确定缴费状态
    IF total_paid >= total_cost THEN
        SET new_status = 2; -- 已缴费
    ELSEIF total_paid > 0 THEN
        SET new_status = 1; -- 部分缴费
    ELSE
        SET new_status = 0; -- 未缴费
    END IF;
    
    -- 更新汇总表
    UPDATE tb_equipment_cost_summary 
    SET 
        paid_amount = total_paid,
        unpaid_amount = total_cost - total_paid,
        payment_status = new_status,
        last_payment_date = NEW.payment_date,
        payment_count = (
            SELECT COUNT(*) 
            FROM tb_equipment_payment_record 
            WHERE summary_id = NEW.summary_id AND status = 1 AND payment_status = 1
        )
    WHERE uid = NEW.summary_id;
    
END$$

DELIMITER ;
