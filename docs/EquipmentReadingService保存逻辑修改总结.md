# EquipmentReadingService 保存逻辑修改总结

## 修改概述

在现有的 `EquipmentReadingService.java` 中添加了对新增字段的支持，包括上次读数的尖峰平谷、各时段用量计算等功能。

## 🔧 主要修改内容

### 1. 新增方法

#### `getPreviousReadingRecord()` 方法
```java
/**
 * 获取设备的上次读数记录（包含分时读数）
 */
private Map<String, Object> getPreviousReadingRecord(String equipmentCode) {
    try {
        String sql = "SELECT * FROM tb_equipment_reading_records " +
                "WHERE equipment_code = ? AND status = 1 AND DATE(reading_date) < DATE(NOW()) " +
                "ORDER BY reading_date DESC, reading_time DESC LIMIT 1";
        
        List<Map<String, Object>> records = dbService.queryList(sql, equipmentCode);
        if (!records.isEmpty()) {
            return records.get(0);
        }
    } catch (Exception e) {
        Log.error(EquipmentReadingService.class, "获取设备上次读数记录失败: " + e.getMessage(), e);
    }
    return null;
}
```

**功能**：获取设备的完整上次读数记录，包含所有分时读数字段。

### 2. 修改 `insertEquipmentReading()` 方法

#### 新增逻辑
```java
// 获取上次读数记录（包含分时读数）
Map<String, Object> previousRecord = getPreviousReadingRecord(equipmentCode);

// 获取上次分时读数
BigDecimal previousTipReading = null;
BigDecimal previousPeakReading = null;
BigDecimal previousFlatReading = null;
BigDecimal previousValleyReading = null;
Date previousReadingTime = null;

if (previousRecord != null) {
    previousTipReading = (BigDecimal) previousRecord.get("tip_reading");
    previousPeakReading = (BigDecimal) previousRecord.get("peak_reading");
    previousFlatReading = (BigDecimal) previousRecord.get("flat_reading");
    previousValleyReading = (BigDecimal) previousRecord.get("valley_reading");
    previousReadingTime = (Date) previousRecord.get("reading_time");
}

// 计算各时段用量
BigDecimal tipUsage = null;
BigDecimal peakUsage = null;
BigDecimal flatUsage = null;
BigDecimal valleyUsage = null;

if (equipmentDataResponse.isTimeSharingMeter()) {
    if (equipmentDataResponse.getTipNumber() != null && previousTipReading != null) {
        tipUsage = BigDecimal.valueOf(equipmentDataResponse.getTipNumber()).subtract(previousTipReading);
        if (tipUsage.compareTo(BigDecimal.ZERO) < 0) tipUsage = BigDecimal.ZERO;
    }
    // ... 其他时段类似
}
```

#### 修改SQL语句
```java
String insertSql = "INSERT INTO tb_equipment_reading_records " +
        "(uid, equipment_code, equipment_type, reading_date, reading_time, current_reading, " +
        "previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, " +
        "flat_reading, valley_reading, " +
        // ⭐ 新增字段
        "previous_tip_reading, previous_peak_reading, previous_flat_reading, previous_valley_reading, " +
        "tip_usage, peak_usage, flat_usage, valley_usage, previous_reading_time, " +
        "is_time_sharing, area_code, status, create_date, creator_id) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, 'system')";
```

### 3. 修改 `updateEquipmentReading()` 方法

#### 新增逻辑
```java
// 获取上次读数记录（包含分时读数）
Map<String, Object> previousRecord = getPreviousReadingRecord(equipmentCode);

// 获取上次分时读数
BigDecimal previousTipReading = null;
BigDecimal previousPeakReading = null;
BigDecimal previousFlatReading = null;
BigDecimal previousValleyReading = null;
Date previousReadingTime = null;

if (previousRecord != null) {
    previousTipReading = (BigDecimal) previousRecord.get("tip_reading");
    previousPeakReading = (BigDecimal) previousRecord.get("peak_reading");
    previousFlatReading = (BigDecimal) previousRecord.get("flat_reading");
    previousValleyReading = (BigDecimal) previousRecord.get("valley_reading");
    previousReadingTime = (Date) previousRecord.get("reading_time");
}

// 计算各时段用量
BigDecimal tipUsage = null;
BigDecimal peakUsage = null;
BigDecimal flatUsage = null;
BigDecimal valleyUsage = null;

if (equipmentDataResponse.isTimeSharingMeter()) {
    if (equipmentDataResponse.getTipNumber() != null && previousTipReading != null) {
        tipUsage = BigDecimal.valueOf(equipmentDataResponse.getTipNumber()).subtract(previousTipReading);
        if (tipUsage.compareTo(BigDecimal.ZERO) < 0) tipUsage = BigDecimal.ZERO;
    }
    // ... 其他时段类似
}
```

#### 修改SQL语句
```java
String updateSql = "UPDATE tb_equipment_reading_records SET " +
        "reading_time = ?, current_reading = ?, previous_reading = ?, usage_amount = ?, " +
        "current_balance = ?, unit_price = ?, tip_reading = ?, peak_reading = ?, " +
        "flat_reading = ?, valley_reading = ?, " +
        // ⭐ 新增字段
        "previous_tip_reading = ?, previous_peak_reading = ?, previous_flat_reading = ?, previous_valley_reading = ?, " +
        "tip_usage = ?, peak_usage = ?, flat_usage = ?, valley_usage = ?, " +
        "previous_reading_time = ?, is_time_sharing = ?, modify_date = ?, modifier_id = 'system' " +
        "WHERE equipment_code = ? AND DATE(reading_date) = ?";
```

### 4. 新增Import语句
```java
import java.util.List;
import java.util.Map;
```

## 📊 数据流程

### 保存流程图
```
接收设备数据
    ↓
获取上次读数记录 (getPreviousReadingRecord)
    ↓
提取上次分时读数
    ↓
计算各时段用量 (当前读数 - 上次读数)
    ↓
保存到数据库 (包含所有新字段)
    ↓
记录日志
```

### 计算逻辑
```java
// 基础用量计算
usageAmount = currentReading - previousReading;

// 分时用量计算（仅分时表）
if (isTimeSharingMeter) {
    tipUsage = tipCurrent - tipPrevious;
    peakUsage = peakCurrent - peakPrevious;
    flatUsage = flatCurrent - flatPrevious;
    valleyUsage = valleyCurrent - valleyPrevious;
}
```

## 🔍 关键特性

### 1. 自动获取上次读数
- 自动查询设备的上次读数记录
- 提取基础读数和分时读数
- 提取上次读表时间

### 2. 智能用量计算
- 基础用量 = 当前读数 - 上次读数
- 分时用量 = 各时段当前读数 - 各时段上次读数
- 防止负数用量（设为0）

### 3. 完整数据保存
- 保存当前读数和上次读数
- 保存计算出的用量
- 保存上次读表时间
- 支持分时和非分时表

### 4. 错误处理
- 异常捕获和日志记录
- 数据验证（防止负数）
- 空值处理

## 📝 使用示例

### 分时电表数据保存
```java
// 输入数据
EquipmentDataResponse response = new EquipmentDataResponse();
response.setNumber(1234.5678);  // 当前总读数
response.setTipNumber(105.1234);   // 当前尖时读数
response.setPeakNumber(112.3456);  // 当前峰时读数
response.setFlatNumber(110.2345);  // 当前平时读数
response.setValleyNumber(106.8643); // 当前谷时读数
response.setTimeSharingMeter(true);

// 自动处理
// 1. 获取上次读数：tip=100.0, peak=100.0, flat=100.0, valley=100.0
// 2. 计算用量：tipUsage=5.1234, peakUsage=12.3456, flatUsage=10.2345, valleyUsage=6.8643
// 3. 保存到数据库
```

### 保存后的数据库记录
```sql
INSERT INTO tb_equipment_reading_records (
    equipment_code, current_reading, previous_reading, usage_amount,
    tip_reading, peak_reading, flat_reading, valley_reading,
    previous_tip_reading, previous_peak_reading, previous_flat_reading, previous_valley_reading,
    tip_usage, peak_usage, flat_usage, valley_usage,
    previous_reading_time, is_time_sharing
) VALUES (
    'E001', 1234.5678, 1200.0000, 34.5678,
    105.1234, 112.3456, 110.2345, 106.8643,
    100.0000, 100.0000, 100.0000, 100.0000,
    5.1234, 12.3456, 10.2345, 6.8643,
    '2025-01-14 10:00:00', 1
);
```

## ✅ 修改优势

### 1. 数据完整性
- **历史追溯**：保留上次读数，便于数据验证
- **用量准确**：基于实际差值计算用量
- **时间关联**：记录上次读表时间

### 2. 计算准确性
- **防止错误**：负数用量自动设为0
- **分时支持**：完整的四时段用量计算
- **数据验证**：可验证总用量=各时段用量之和

### 3. 代码可维护性
- **逻辑清晰**：获取→计算→保存的清晰流程
- **错误处理**：完善的异常处理机制
- **日志记录**：详细的操作日志

### 4. 扩展性
- **向后兼容**：不影响现有功能
- **易于扩展**：可轻松添加新的计算逻辑
- **配置灵活**：支持分时和非分时表

## 🚀 部署说明

### 1. 数据库准备
确保已执行表结构修改SQL，添加了所有新字段。

### 2. 代码部署
将修改后的 `EquipmentReadingService.java` 部署到服务器。

### 3. 测试验证
1. 测试分时电表数据保存
2. 测试非分时表数据保存
3. 验证用量计算是否正确
4. 检查日志输出

### 4. 监控观察
- 观察数据保存是否正常
- 检查用量计算是否准确
- 监控异常日志

现在您的保存逻辑已经完全支持新的表结构，能够正确处理尖峰平谷四时段的读数和用量计算！
