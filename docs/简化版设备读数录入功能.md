# 简化版设备读数录入功能

## 功能概述

根据您的要求，简化了设备读数录入界面，**去掉了上次读数和单价输入字段**，这些数据由系统自动获取和计算：

- **上次读数**：从数据库中查询该设备最近一次的读数记录
- **单价**：从系统配置表 `tb_sys_config` 中获取
- **费用计算**：在后端自动完成，保存到三个相关表中

## ✅ 简化后的界面

### 1. **基本信息区域**
```javascript
- 设备编号 (必填)
- 设备类型 (必填：电表/水表/热水表/气表)
- 区域编码 (必填)
- 读数时间 (必填)
```

### 2. **读数信息区域**
```javascript
- 当前读数 (必填)
- 当前余额 (可选)
- 用量显示 (只读，显示当前读数)
- 费用显示 (只读，提示"费用将在保存时计算")
```

### 3. **分时电表区域**（仅电表显示）
```javascript
- 是否分时电表 (复选框)
- 尖时读数 (分时电表时显示)
- 峰时读数 (分时电表时显示)
- 平时读数 (分时电表时显示)
- 谷时读数 (分时电表时显示)
```

### 4. **其他信息区域**
```javascript
- 备注 (可选)
- 状态 (默认：正常)
```

## 🔧 后端自动获取逻辑

### 1. **获取上次读数**
```java
private Double getPreviousReading(String equipment_code, String equipment_type) {
    String sql = "SELECT current_reading FROM tb_equipment_reading_records " +
                "WHERE equipment_code = '" + equipment_code + "' " +
                "AND equipment_type = '" + equipment_type + "' " +
                "AND status = 1 " +
                "ORDER BY reading_date DESC LIMIT 1";
    
    Object result = weCostService.dbService.queryObject(sql);
    return result != null ? Double.parseDouble(result.toString()) : 0.0;
}
```

### 2. **获取设备单价**
```java
private Double getEquipmentUnitPrice(String equipment_type) {
    String configKey = "";
    switch (equipment_type) {
        case "1": configKey = "electric_unit_price"; break;  // 电表
        case "2": configKey = "water_unit_price"; break;     // 水表
        case "4": configKey = "hot_water_unit_price"; break; // 热水表
        case "221": configKey = "gas_unit_price"; break;     // 气表
    }
    
    String sql = "SELECT config_value FROM tb_sys_config " +
                "WHERE config_key = '" + configKey + "' AND status = 1";
    
    Object result = weCostService.dbService.queryObject(sql);
    return result != null ? Double.parseDouble(result.toString()) : getDefaultPrice(equipment_type);
}
```

### 3. **获取分时电表单价**
```java
private Double getTimeSharingPrice(String timeType) {
    String configKey = "";
    switch (timeType) {
        case "tip": configKey = "electric_tip_price"; break;    // 尖时
        case "peak": configKey = "electric_peak_price"; break;  // 峰时
        case "flat": configKey = "electric_flat_price"; break;  // 平时
        case "valley": configKey = "electric_valley_price"; break; // 谷时
    }
    
    String sql = "SELECT config_value FROM tb_sys_config " +
                "WHERE config_key = '" + configKey + "' AND status = 1";
    
    Object result = weCostService.dbService.queryObject(sql);
    return result != null ? Double.parseDouble(result.toString()) : getDefaultTimeSharingPrice(timeType);
}
```

## 📊 系统配置表设置

### 需要在 `tb_sys_config` 表中配置以下价格：

#### 普通设备单价：
```sql
INSERT INTO tb_sys_config (config_key, config_value, config_desc, status) VALUES
('electric_unit_price', '0.6', '电表单价(元/度)', 1),
('water_unit_price', '3.5', '水表单价(元/吨)', 1),
('hot_water_unit_price', '5.0', '热水表单价(元/吨)', 1),
('gas_unit_price', '2.8', '气表单价(元/立方米)', 1);
```

#### 分时电表单价：
```sql
INSERT INTO tb_sys_config (config_key, config_value, config_desc, status) VALUES
('electric_tip_price', '1.2', '电表尖时单价(元/度)', 1),
('electric_peak_price', '0.8', '电表峰时单价(元/度)', 1),
('electric_flat_price', '0.6', '电表平时单价(元/度)', 1),
('electric_valley_price', '0.3', '电表谷时单价(元/度)', 1);
```

## 🎯 前端界面变化

### 1. **隐藏的字段**
```javascript
// 这些字段改为隐藏字段，值为0或从后端获取
{
    xtype: 'hiddenfield',
    name: 'previous_reading',
    value: 0
}, {
    xtype: 'hiddenfield',
    name: 'unit_price',
    value: 0
}, {
    xtype: 'hiddenfield',
    name: 'previous_tip_reading',
    value: 0
}, {
    xtype: 'hiddenfield',
    name: 'tip_price',
    value: 0
}
// ... 其他分时字段类似
```

### 2. **简化的分时电表字段**
```javascript
// 只显示当前读数，不显示上次读数和单价
{
    xtype: 'numberfield',
    itemId: 'tipReading',
    fieldLabel: '尖时读数',
    name: 'tip_reading',
    hidden: true,  // 默认隐藏，选择分时电表时显示
    decimalPrecision: 2,
    minValue: 0
}
```

### 3. **简化的费用显示**
```javascript
// 前端只显示提示信息，不进行实际计算
calculateNormalCost: function() {
    var currentReading = this.down('numberfield[name=current_reading]').getValue() || 0;
    
    this.down('displayfield[itemId=usageDisplay]').setValue('当前读数: ' + currentReading.toFixed(2));
    this.down('displayfield[itemId=costDisplay]').setValue('费用将在保存时计算');
}
```

## 🚀 使用流程

### 1. **用户操作**
```
1. 选择设备类型 → 自动显示/隐藏分时电表选项
2. 输入设备编号 → 系统准备查询上次读数
3. 输入当前读数 → 前端显示读数信息
4. 如果是电表，可选择分时电表 → 显示四时段读数输入
5. 点击保存 → 提交到后端处理
```

### 2. **后端处理**
```
1. 接收前端数据
2. 查询该设备上次读数
3. 从配置表获取单价
4. 计算用量和费用
5. 保存到三个表：读数记录表、费用计算表、费用汇总表
6. 返回保存结果
```

## 📋 数据示例

### 用户输入：
```json
{
    "equipment_code": "WM001",
    "equipment_type": "2",
    "area_code": "AREA001",
    "reading_date": "2025-01-15 14:30:00",
    "current_reading": 1250.5,
    "current_balance": 500.0
}
```

### 系统自动获取：
```json
{
    "previous_reading": 1200.0,  // 从数据库查询
    "unit_price": 3.5,          // 从配置表获取
    "usage_amount": 50.5,       // 自动计算
    "total_cost": 176.75        // 自动计算
}
```

### 最终保存：
```json
{
    "equipment_code": "WM001",
    "equipment_type": "2",
    "area_code": "AREA001", 
    "reading_date": "2025-01-15 14:30:00",
    "current_reading": 1250.5,
    "previous_reading": 1200.0,
    "usage_amount": 50.5,
    "unit_price": 3.5,
    "total_cost": 176.75,
    "current_balance": 500.0
}
```

## 💡 优势

### 1. **简化操作**
- 用户只需输入当前读数
- 减少录入错误
- 提高录入效率

### 2. **统一管理**
- 单价统一在配置表管理
- 便于批量调整价格
- 确保价格一致性

### 3. **自动计算**
- 系统自动获取历史数据
- 自动计算用量和费用
- 减少人工计算错误

### 4. **完整记录**
- 保存完整的计算过程
- 支持数据追溯
- 便于审计和核查

现在用户录入设备读数时，界面更加简洁，只需要输入必要的信息，系统会自动处理其他计算！
