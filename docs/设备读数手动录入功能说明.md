# 设备读数手动录入功能说明

## 功能概述

为您的水电费管理系统新增了手动录入设备读数的功能，支持普通设备和分时电表的读数录入，数据直接保存到 `tb_equipment_reading_records` 表中。

## ✅ 新增内容

### 1. 前端窗口组件
**文件**: `campusos/src/main/resources/static/app/view/hotel/wedaycost/WEDayCostView.js`

#### 新增窗口类：
```javascript
Ext.define('CamPus.view.hotel.wedaycost.EquipmentReadingWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '手动录入设备读数',
    width: 800,
    height: 600
});
```

#### 主要功能区域：
1. **基本信息区域**
   - 设备编号
   - 设备类型（电表/水表/热水表/气表）
   - 区域编码
   - 读数时间

2. **读数信息区域**
   - 当前读数
   - 上次读数
   - 单价
   - 当前余额

3. **分时电表区域**（仅电表显示）
   - 是否分时电表选择
   - 尖时/峰时/平时/谷时读数和单价

4. **其他信息区域**
   - 备注
   - 状态

### 2. 后端控制器方法
**文件**: `campusos/src/main/java/com/ymiots/campusos/controller/hotel/WECostController.java`

#### 新增方法：
```java
@RequestMapping("/saveEquipmentReading")
@Permission(menu = menuid)
public JsonResult saveEquipmentReading(HttpServletRequest request, ...)
```

## 📊 数据库表结构

### 插入到 `tb_equipment_reading_records` 表
```sql
INSERT INTO tb_equipment_reading_records (
    equipment_code,        -- 设备编号
    equipment_type,        -- 设备类型 (1=电表,2=水表,4=热水表,221=气表)
    area_code,            -- 区域编码
    reading_date,         -- 读数时间
    current_reading,      -- 当前读数
    previous_reading,     -- 上次读数
    usage_amount,         -- 用量 (自动计算: current - previous)
    unit_price,           -- 单价
    
    -- 分时电表字段
    tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost,
    peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost,
    flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost,
    valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost,
    
    total_cost,           -- 总费用 (自动计算)
    is_time_sharing,      -- 是否分时电表 (0=否,1=是)
    current_balance,      -- 当前余额
    remark,              -- 备注
    status,              -- 状态 (0=禁用,1=正常)
    create_time          -- 创建时间
) VALUES (...)
```

## 🔧 前端界面特性

### 1. 智能显示控制
```javascript
/**
 * 设备类型改变事件
 */
onEquipmentTypeChange: function(equipmentType) {
    var timeSharingFieldset = this.down('fieldset[itemId=timeSharingFieldset]');
    
    if (equipmentType === '1') { // 电表
        timeSharingFieldset.setVisible(true);
        timeSharingFieldset.expand();
    } else {
        timeSharingFieldset.setVisible(false);
        timeSharingFieldset.collapse();
        // 清空分时相关字段
        this.down('checkboxfield[name=is_time_sharing]').setValue(false);
        this.onTimeSharingChange(false);
    }
}
```

### 2. 分时电表控制
```javascript
/**
 * 分时电表选择改变事件
 */
onTimeSharingChange: function(isTimeSharing) {
    var containers = [
        'tipContainer', 'tipPriceContainer',
        'peakContainer', 'peakPriceContainer', 
        'flatContainer', 'flatPriceContainer',
        'valleyContainer', 'valleyPriceContainer'
    ];
    
    Ext.each(containers, function(containerId) {
        var container = this.down('container[itemId=' + containerId + ']');
        if (container) {
            container.setVisible(isTimeSharing);
        }
    });
}
```

### 3. 数据验证
```javascript
/**
 * 验证读数数据
 */
validateReadings: function() {
    var currentReading = this.down('numberfield[name=current_reading]').getValue();
    var previousReading = this.down('numberfield[name=previous_reading]').getValue();
    
    // 验证当前读数不能小于上次读数
    if (currentReading < previousReading) {
        Ext.Msg.alert('验证失败', '当前读数不能小于上次读数');
        return false;
    }
    
    // 分时电表验证
    if (isTimeSharing) {
        // 验证各时段读数
    }
    
    return true;
}
```

## 💡 后端计算逻辑

### 1. 普通设备计算
```java
// 计算用量
double usage_amount = current_reading - previous_reading;

// 计算总费用
double totalCost = usage_amount * unit_price;
```

### 2. 分时电表计算
```java
if (is_time_sharing == 1 && "1".equals(equipment_type)) {
    // 尖时计算
    double tipUsage = tip_reading - previous_tip_reading;
    double tipCost = tipUsage * tip_price;
    
    // 峰时计算
    double peakUsage = peak_reading - previous_peak_reading;
    double peakCost = peakUsage * peak_price;
    
    // 平时计算
    double flatUsage = flat_reading - previous_flat_reading;
    double flatCost = flatUsage * flat_price;
    
    // 谷时计算
    double valleyUsage = valley_reading - previous_valley_reading;
    double valleyCost = valleyUsage * valley_price;
    
    // 总费用
    double totalCost = tipCost + peakCost + flatCost + valleyCost;
}
```

## 🚀 使用方式

### 1. 在主界面添加录入按钮
```javascript
// 在工具栏中添加
{
    xtype: 'button',
    text: '手动录入',
    iconCls: 'x-fa fa-plus',
    handler: function() {
        me.onAddEquipmentReading();
    }
}
```

### 2. 打开录入窗口
```javascript
/**
 * 打开设备读数录入窗口
 */
onAddEquipmentReading: function() {
    var me = this;
    var selectedNode = me.areatree.getSelectionModel().getSelection()[0];
    var areaCode = selectedNode ? selectedNode.get('code') : '';
    
    var window = Ext.create('CamPus.view.hotel.wedaycost.EquipmentReadingWindow', {
        areacode: areaCode,
        wecostgrid: me.wecostgrid
    });
    
    window.show();
}
```

### 3. 保存数据
```javascript
/**
 * 保存读数数据
 */
Save: function () {
    var me = this;
    var form = me.down('form[itemId=readingForm]');
    
    if (form.getForm().isValid()) {
        // 验证读数逻辑
        if (!me.validateReadings()) {
            return;
        }
        
        form.submit({
            url: '/Hotel/WECost/saveEquipmentReading',
            timeout: 60000,
            waitTitle: '系统信息',
            waitMsg: '正在保存读数数据...',
            success: function (form, action) {
                if (action.result.success) {
                    toast(action.result.msg);
                    me.wecostgrid.getStore().reload();
                    me.close();
                }
            }
        });
    }
}
```

## 📋 字段说明

### 必填字段
- **设备编号**: 唯一标识设备
- **设备类型**: 1=电表, 2=水表, 4=热水表, 221=气表
- **区域编码**: 设备所在区域
- **读数时间**: 读数的具体时间
- **当前读数**: 本次读取的数值
- **单价**: 每单位的价格

### 可选字段
- **上次读数**: 上一次的读数（默认0）
- **当前余额**: 账户余额（默认0）
- **备注**: 额外说明信息
- **状态**: 记录状态（默认1=正常）

### 分时电表字段（仅电表且选择分时时）
- **尖时读数/单价**: 尖峰时段
- **峰时读数/单价**: 高峰时段
- **平时读数/单价**: 平常时段
- **谷时读数/单价**: 低谷时段

## 🎯 数据流程

1. **用户录入** → 前端表单验证
2. **提交数据** → 后端接收参数
3. **计算费用** → 自动计算用量和费用
4. **插入数据库** → 保存到 tb_equipment_reading_records 表
5. **返回结果** → 前端显示成功/失败信息
6. **刷新列表** → 更新主界面数据

## 🔍 验证规则

### 前端验证
- 必填字段不能为空
- 数字字段格式验证
- 当前读数不能小于上次读数
- 分时电表各时段读数验证

### 后端验证
- 参数类型验证
- 数据范围验证
- SQL注入防护

## 💾 数据示例

### 普通水表录入
```json
{
    "equipment_code": "WM001",
    "equipment_type": "2",
    "area_code": "AREA001",
    "reading_date": "2025-01-15 14:30:00",
    "current_reading": 1250.5,
    "previous_reading": 1200.0,
    "unit_price": 3.5,
    "current_balance": 500.0,
    "is_time_sharing": 0,
    "remark": "正常抄表",
    "status": 1
}
```

### 分时电表录入
```json
{
    "equipment_code": "EM001",
    "equipment_type": "1",
    "area_code": "AREA001",
    "reading_date": "2025-01-15 14:30:00",
    "current_reading": 5000.0,
    "previous_reading": 4800.0,
    "unit_price": 0.6,
    "is_time_sharing": 1,
    "tip_reading": 1200.0,
    "previous_tip_reading": 1150.0,
    "tip_price": 1.2,
    "peak_reading": 1300.0,
    "previous_peak_reading": 1250.0,
    "peak_price": 0.8,
    "flat_reading": 1250.0,
    "previous_flat_reading": 1200.0,
    "flat_price": 0.6,
    "valley_reading": 1250.0,
    "previous_valley_reading": 1200.0,
    "valley_price": 0.3
}
```

现在您的系统支持手动录入设备读数，数据会直接保存到 `tb_equipment_reading_records` 表中，并自动计算用量和费用！
