# 主键UID修正说明

## 问题描述

您的数据库表主键是 `uid` 而不是 `id`，需要修正代码以使用正确的主键字段。

## ✅ 修正内容

### 1. **前端窗口修正**

#### 隐藏字段修改 (`WEDayCostView.js`)
```javascript
// 修正前：使用id
{
    xtype: 'hiddenfield',
    itemId: 'id',
    name: 'id',
    value: 0
}

// 修正后：使用uid
{
    xtype: 'hiddenfield',
    itemId: 'uid',
    name: 'uid',
    value: ''  // 空字符串表示新增，有值表示修改
}
```

### 2. **前端控制器修正**

#### 字段设置修改 (`WEDayCostController.js`)
```javascript
// 修正前：
form.down('hiddenfield[itemId=id]').setValue(node.get('id') || node.get('uid'));

// 修正后：
form.down('hiddenfield[itemId=uid]').setValue(node.get('uid'));
```

### 3. **后端参数修正**

#### 参数接收修改 (`WECostController.java`)
```java
// 修正前：
@RequestParam(required = false, defaultValue = "0") Integer id

// 修正后：
@RequestParam(required = false, defaultValue = "") String uid
```

### 4. **操作判断修正**

#### 判断逻辑修改
```java
// 修正前：
boolean isUpdate = (id != null && id > 0);

// 修正后：
boolean isUpdate = (uid != null && !uid.trim().isEmpty());
```

### 5. **UPDATE方法修正**

#### 方法签名和WHERE条件
```java
// 修正后的UPDATE方法
private int updateEquipmentReading(String uid, String equipment_code, ...) {
    try {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE tb_equipment_reading_records SET ");
        sql.append("equipment_code = '").append(equipment_code).append("', ");
        sql.append("equipment_type = '").append(equipment_type).append("', ");
        // ... 其他字段设置
        sql.append("WHERE uid = '").append(uid).append("'");  // 使用uid作为WHERE条件
        
        return weCostService.dbService.ExecuteUpdate(sql.toString());
    } catch (Exception e) {
        Log.error(WECostController.class, "更新设备读数失败", e);
        return 0;
    }
}
```

## 🎯 关键变化

### 1. **数据类型变化**
- **修正前**：`Integer id` (数字类型)
- **修正后**：`String uid` (字符串类型，通常是UUID)

### 2. **判断逻辑变化**
- **修正前**：`id != null && id > 0`
- **修正后**：`uid != null && !uid.trim().isEmpty()`

### 3. **WHERE条件变化**
- **修正前**：`WHERE id = ?` (数字比较)
- **修正后**：`WHERE uid = '?'` (字符串比较，需要引号)

### 4. **默认值变化**
- **修正前**：`defaultValue = "0"` (数字0表示新增)
- **修正后**：`defaultValue = ""` (空字符串表示新增)

## 📊 操作流程

### 新增流程：
```
点击"新增" → 窗口(uid='') → 录入数据 → 保存 → insertEquipmentReading() → INSERT
```

### 修改流程：
```
选择记录 → 点击"修改" → 窗口(uid='实际UUID') → 加载数据 → 修改 → 保存 → updateEquipmentReading() → UPDATE WHERE uid='实际UUID'
```

## 🔍 验证要点

### 1. **前端验证**
```javascript
// 检查UID字段是否正确设置
console.log('UID值:', form.down('hiddenfield[itemId=uid]').getValue());

// 新增时应该是空字符串 ''
// 修改时应该是实际的UUID值，如 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
```

### 2. **后端验证**
```java
// 检查操作判断
Log.info("UID参数: " + uid);
Log.info("是否修改操作: " + isUpdate);

// 新增时：uid = "", isUpdate = false
// 修改时：uid = "实际UUID", isUpdate = true
```

### 3. **SQL验证**
```sql
-- 修改操作的SQL应该是：
UPDATE tb_equipment_reading_records SET 
    equipment_code = 'xxx',
    equipment_type = 'xxx',
    ...
WHERE uid = 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
```

## 💡 注意事项

### 1. **UUID格式**
- UUID通常是36位字符串格式：`xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
- 需要用单引号包围：`WHERE uid = 'uuid-string'`

### 2. **空值处理**
- 新增时uid为空字符串，不是null
- 修改时uid必须是有效的UUID字符串

### 3. **数据库字段**
- 确保数据库表的主键字段确实是 `uid`
- 确保 `uid` 字段类型支持UUID字符串存储

## 🎉 修正完成

现在代码已经正确使用 `uid` 作为主键：

1. **前端**：使用 `uid` 字段进行操作判断
2. **后端**：接收 `uid` 参数并用于UPDATE的WHERE条件
3. **SQL**：正确使用 `WHERE uid = 'uuid-string'` 进行更新

修改功能现在应该能够正确地更新指定的记录，而不是创建新记录！
