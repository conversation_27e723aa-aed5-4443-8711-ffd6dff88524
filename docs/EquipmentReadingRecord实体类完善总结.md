# EquipmentReadingRecord 实体类完善总结

## 修改概述

在 `EquipmentReadingRecord.java` 实体类中添加了完整的分时读数、用量、电价和费用字段，支持完整的设备读数记录和费用计算功能。

## 🔧 新增字段详情

### 1. 上次分时读数字段

```java
/**
 * 上次尖时读数(分时表)
 */
private BigDecimal previousTipReading;

/**
 * 上次峰时读数(分时表)
 */
private BigDecimal previousPeakReading;

/**
 * 上次平时读数(分时表)
 */
private BigDecimal previousFlatReading;

/**
 * 上次谷时读数(分时表)
 */
private BigDecimal previousValleyReading;
```

**功能**：保存上次读数的尖峰平谷四时段数据，用于计算各时段用量。

### 2. 各时段用量字段

```java
/**
 * 尖时用量
 */
private BigDecimal tipUsage;

/**
 * 峰时用量
 */
private BigDecimal peakUsage;

/**
 * 平时用量
 */
private BigDecimal flatUsage;

/**
 * 谷时用量
 */
private BigDecimal valleyUsage;
```

**功能**：保存计算出的各时段用量（当前读数 - 上次读数）。

### 3. 上次读表时间

```java
/**
 * 上次读表时间
 */
private Date previousReadingTime;
```

**功能**：记录上次读表的时间，用于数据追溯和时间间隔计算。

### 4. 分时电价字段

```java
/**
 * 尖时电价
 */
private BigDecimal tipPrice;

/**
 * 峰时电价
 */
private BigDecimal peakPrice;

/**
 * 平时电价
 */
private BigDecimal flatPrice;

/**
 * 谷时电价
 */
private BigDecimal valleyPrice;
```

**功能**：保存计算费用时使用的电价，确保费用计算的可追溯性。

### 5. 分时费用字段

```java
/**
 * 尖时费用
 */
private BigDecimal tipCost;

/**
 * 峰时费用
 */
private BigDecimal peakCost;

/**
 * 平时费用
 */
private BigDecimal flatCost;

/**
 * 谷时费用
 */
private BigDecimal valleyCost;

/**
 * 分时总费用
 */
private BigDecimal totalTimeSharingCost;
```

**功能**：保存各时段的费用明细和总费用。

## 📊 完整字段结构

### 原有字段
```java
// 基础信息
private String uid;                    // 主键ID
private String equipmentCode;          // 设备编号
private Integer equipmentType;         // 设备类型
private Date readingDate;              // 读数日期
private Date readingTime;              // 读数时间

// 基础读数
private BigDecimal currentReading;     // 当前读数
private BigDecimal previousReading;    // 上次读数
private BigDecimal usageAmount;        // 用量
private BigDecimal currentBalance;     // 当前余额
private BigDecimal unitPrice;          // 单价

// 当前分时读数
private BigDecimal tipReading;         // 尖时读数
private BigDecimal peakReading;        // 峰时读数
private BigDecimal flatReading;        // 平时读数
private BigDecimal valleyReading;      // 谷时读数

// 其他字段
private Integer isTimeSharing;         // 是否分时表
private String areaCode;               // 区域编码
private Integer status;                // 状态
private Date createDate;               // 创建时间
private String creatorId;              // 创建人ID
private Date modifyDate;               // 修改时间
private String modifierId;             // 修改人ID
private String remark;                 // 备注
```

### ⭐ 新增字段
```java
// 上次分时读数
private BigDecimal previousTipReading;     // 上次尖时读数
private BigDecimal previousPeakReading;    // 上次峰时读数
private BigDecimal previousFlatReading;    // 上次平时读数
private BigDecimal previousValleyReading;  // 上次谷时读数

// 各时段用量
private BigDecimal tipUsage;               // 尖时用量
private BigDecimal peakUsage;              // 峰时用量
private BigDecimal flatUsage;              // 平时用量
private BigDecimal valleyUsage;            // 谷时用量

// 时间信息
private Date previousReadingTime;          // 上次读表时间

// 分时电价
private BigDecimal tipPrice;               // 尖时电价
private BigDecimal peakPrice;              // 峰时电价
private BigDecimal flatPrice;              // 平时电价
private BigDecimal valleyPrice;            // 谷时电价

// 分时费用
private BigDecimal tipCost;                // 尖时费用
private BigDecimal peakCost;               // 峰时费用
private BigDecimal flatCost;               // 平时费用
private BigDecimal valleyCost;             // 谷时费用
private BigDecimal totalTimeSharingCost;   // 分时总费用
```

## 🎯 数据关系

### 用量计算关系
```java
// 基础用量
usageAmount = currentReading - previousReading;

// 分时用量
tipUsage = tipReading - previousTipReading;
peakUsage = peakReading - previousPeakReading;
flatUsage = flatReading - previousFlatReading;
valleyUsage = valleyReading - previousValleyReading;

// 验证关系
usageAmount ≈ tipUsage + peakUsage + flatUsage + valleyUsage;
```

### 费用计算关系
```java
// 分时费用
tipCost = tipUsage × tipPrice;
peakCost = peakUsage × peakPrice;
flatCost = flatUsage × flatPrice;
valleyCost = valleyUsage × valleyPrice;

// 总费用
totalTimeSharingCost = tipCost + peakCost + flatCost + valleyCost;
```

## 📝 使用示例

### 分时电表数据示例
```java
EquipmentReadingRecord record = new EquipmentReadingRecord();

// 基础信息
record.setEquipmentCode("E001");
record.setEquipmentType(1);
record.setIsTimeSharing(1);
record.setReadingDate(new Date());
record.setReadingTime(new Date());

// 当前读数
record.setCurrentReading(new BigDecimal("1234.5678"));
record.setPreviousReading(new BigDecimal("1200.0000"));
record.setUsageAmount(new BigDecimal("34.5678"));

// 当前分时读数
record.setTipReading(new BigDecimal("105.1234"));
record.setPeakReading(new BigDecimal("112.3456"));
record.setFlatReading(new BigDecimal("110.2345"));
record.setValleyReading(new BigDecimal("106.8643"));

// 上次分时读数
record.setPreviousTipReading(new BigDecimal("100.0000"));
record.setPreviousPeakReading(new BigDecimal("100.0000"));
record.setPreviousFlatReading(new BigDecimal("100.0000"));
record.setPreviousValleyReading(new BigDecimal("100.0000"));

// 各时段用量
record.setTipUsage(new BigDecimal("5.1234"));
record.setPeakUsage(new BigDecimal("12.3456"));
record.setFlatUsage(new BigDecimal("10.2345"));
record.setValleyUsage(new BigDecimal("6.8643"));

// 分时电价
record.setTipPrice(new BigDecimal("1.5860"));
record.setPeakPrice(new BigDecimal("1.5000"));
record.setFlatPrice(new BigDecimal("1.2000"));
record.setValleyPrice(new BigDecimal("0.9850"));

// 分时费用
record.setTipCost(new BigDecimal("8.12"));
record.setPeakCost(new BigDecimal("18.52"));
record.setFlatCost(new BigDecimal("12.28"));
record.setValleyCost(new BigDecimal("6.75"));
record.setTotalTimeSharingCost(new BigDecimal("45.67"));

// 其他信息
record.setPreviousReadingTime(DateUtils.parseDate("2025-01-14 10:00:00"));
record.setAreaCode("A001");
record.setStatus(1);
```

### 非分时表数据示例
```java
EquipmentReadingRecord record = new EquipmentReadingRecord();

// 基础信息
record.setEquipmentCode("W001");
record.setEquipmentType(2); // 水表
record.setIsTimeSharing(0);

// 基础读数
record.setCurrentReading(new BigDecimal("456.7890"));
record.setPreviousReading(new BigDecimal("450.0000"));
record.setUsageAmount(new BigDecimal("6.7890"));
record.setUnitPrice(new BigDecimal("3.0000"));

// 分时相关字段为null或0
record.setTipReading(null);
record.setPeakReading(null);
record.setFlatReading(null);
record.setValleyReading(null);
record.setTotalTimeSharingCost(BigDecimal.ZERO);
```

## 🔍 工具方法

### 现有工具方法
```java
/**
 * 获取设备类型描述
 */
public String getEquipmentTypeDesc() {
    if (equipmentType == null) return "未知";
    switch (equipmentType) {
        case 1: return "电表";
        case 2: return "水表";
        case 4: return "热水表";
        case 221: return "气表";
        default: return "未知";
    }
}

/**
 * 判断是否为分时表
 */
public boolean isTimeSharingMeter() {
    return tipReading != null || peakReading != null || 
           flatReading != null || valleyReading != null;
}
```

## ✅ Lombok 支持

### 自动生成的方法
由于使用了 `@Data` 和 `@Accessors(chain = true)` 注解，以下方法会自动生成：

```java
// Getter 方法
public BigDecimal getTipUsage() { return tipUsage; }
public BigDecimal getPeakUsage() { return peakUsage; }
// ... 所有字段的getter

// Setter 方法（支持链式调用）
public EquipmentReadingRecord setTipUsage(BigDecimal tipUsage) { 
    this.tipUsage = tipUsage; 
    return this; 
}
public EquipmentReadingRecord setPeakUsage(BigDecimal peakUsage) { 
    this.peakUsage = peakUsage; 
    return this; 
}
// ... 所有字段的setter

// equals, hashCode, toString 方法
```

### 链式调用示例
```java
EquipmentReadingRecord record = new EquipmentReadingRecord()
    .setEquipmentCode("E001")
    .setEquipmentType(1)
    .setIsTimeSharing(1)
    .setTipUsage(new BigDecimal("5.1234"))
    .setPeakUsage(new BigDecimal("12.3456"))
    .setTipPrice(new BigDecimal("1.5860"))
    .setPeakPrice(new BigDecimal("1.5000"));
```

## 🎯 修改优势

### 1. 数据完整性
- **历史数据保留**：保存上次读数，便于数据追溯
- **用量计算准确**：基于实际差值计算各时段用量
- **费用计算透明**：保存电价和费用明细

### 2. 业务支持
- **分时计费**：完整支持尖峰平谷四时段计费
- **审计追踪**：费用计算过程完全可追溯
- **数据验证**：可验证用量和费用计算的正确性

### 3. 开发便利
- **Lombok支持**：自动生成getter/setter方法
- **链式调用**：支持流畅的对象构建
- **类型安全**：使用BigDecimal确保精度

### 4. 扩展性
- **向后兼容**：新增字段不影响现有功能
- **易于维护**：清晰的字段命名和注释
- **灵活配置**：支持分时和非分时表的不同需求

现在您的 `EquipmentReadingRecord` 实体类已经完全支持分时读数记录和费用计算的所有需求！
