$(function () {
    $("#pagebox").css("margin-top", (($(window).height() - $("#pagebox").height()) / 2) + "px");
    var keyboard = $('#keyboard').val();


    var visitorinfo = {};

    var layoutdata = function (data) {
        if (!data) {
            return;
        }
        var idcardinfo = data.split('#');

        visitorinfo = {
            name: idcardinfo[0],
            sex: idcardinfo[1],
            nation: idcardinfo[2],
            birthday: idcardinfo[3],
            address: idcardinfo[4],
            idcard: idcardinfo[5],
            photo: idcardinfo[12],
            idcardphoto: idcardinfo[11],
            idtype: 1,
            hour: $('#hour').val(),
            company: $('#company').val()
        };

        $('#photo').html('<img src="data:image/png;base64,' + idcardinfo[12] + '"  style="height:126px;"/><img  style="margin-left:30px;height: 126px;" src="data:image/png;base64,' + idcardinfo[11] + '"/>');
        $('#name').html(idcardinfo[0]);
        $('#sex').html(idcardinfo[1]);
        $('#nation').html(idcardinfo[2]);
        $('#birthday').html(idcardinfo[3]);
        $('#idcard').html(idcardinfo[5]);
        $('#address').html(idcardinfo[4]);
        $('#org').html(idcardinfo[6]);
        $('#startdate').html(idcardinfo[7] + ' 至 ' + idcardinfo[8]);
        $('#degree').html(idcardinfo[9]);

        $.postAjax({
            url: "/visitor/pcGetVisitorRecord",
            data: {
                idcard: $('#idcard').html()
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success && result.data.length != 0) {
                    var visitorname = result.data[0].name;
                    var byvisitor = result.data[0].byvisitor;
                    if (result.data[0].qrcode && $('#visitorPrintQRCode').val() && $('#visitorPrintQRCode').val() == 1) {
                        useractive.qrCode('window.QrCodePrintmsg', result.data[0].qrcode, visitorname, $('#byvisitorOrgname').val(), byvisitor, result.data[0].visittime);
                    }
                } else {
                    $.toast(result.msg, "forbidden");
                }
            }
        });
    };


    // var websocket = null;
    // var websocketclient = function () {
    //     var url = 'ws://localhost:1234';
    //     var onOpenCallBack = function (wss, evt) {
    //         $('#status').attr('style', 'text-align:center;color:#20c326;');
    //         $('#status').html('连接成功');
    //     };
    //     var onErrorCallBack = function (wss, evt) {
    //         $('#status').attr('style', 'text-align:center;color:red;');
    //         $('#status').html('通讯错误');
    //     };
    //     var onMessageCallBack = function (wss, evt) {
    //         log.writeLog("visitor", "接收到数据：" + evt.data);
    //         layoutdata(evt.data);
    //     };
    //     var onCloseCallBack = function (wss, evt) {
    //         $('#status').attr('style', 'text-align:center;color:red;');
    //         $('#status').html('通讯中断');
    //         websocket = null;
    //     };
    //     var onStateChangeCallBack = function (wss, state, oldstate) {
    //         if (state == 1) {
    //             $('#status').attr('style', 'text-align:center;color:#f7aa0d;');
    //             $('#status').html('正在连接');
    //         }
    //         if (oldstate == 1 && state == -1) {
    //             //正在连接转错误
    //             websocket = null;
    //             $('#status').attr('style', 'text-align:center;color:red;');
    //             $('#status').html('连接失败');
    //         }
    //     };
    //     websocket = new WebSocketClient(url, onOpenCallBack, onErrorCallBack, onMessageCallBack, onCloseCallBack, onStateChangeCallBack);
    //     websocket.connect();
    // };
    //
    // websocketclient();

    // $('#sendcmd').click(function () {
    //     closePro();
    //     visitorinfo = {};
    //     if (websocket && websocket.client && websocket.state == 2) {
    //         websocket.send('begin#' + $('#favalue').val());
    //     } else {
    //         if (websocket && websocket.state == 1) {
    //             $('#status').attr('style', 'text-align:center;color:#f7aa0d;');
    //             $('#status').html('正在重连，请勿重复操作...');
    //         } else {
    //             $('#status').attr('style', 'text-align:center;color:#f7aa0d;');
    //             $('#status').html('设备通讯中断，正在重连...');
    //             websocketclient();
    //         }
    //     }
    // });

    window.onbeforeunload = function () {
        if (websocket && websocket.state != 0) {
            websocket.close();
        }
    };

    $('#mobile').focus(function () {
        closePro();
        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.openWinOsk('', 'winosk');  
        }
        useractive.do();
        var numPad = new NumKeyBoard({
            text: '手机号',
            valType: 'tel',
            precision: 0,       //精确度
            minVal: 0, //最小值
            maxVal: 20000000000  //最大值
        });
        numPad.open(function (data) {
            $('#mobile').val(data);
            useractive.do();
        });
    });

    var initform = function () {
        $('#photo').html('<img src="/ui/img/icon-man.png"/><img  style="margin-left:30px;" src="/ui/img/icon-subface.png"/>');
        $('#name').html('');
        $('#sex').html('');
        $('#nation').html('');
        $('#birthday').html('');
        $('#idcard').html('');
        $('#address').html('');
        $('#org').html('');
        $('#startdate').html('');
        $('#degree').html('');
        $('#reason').val('');
        $('#byvisitor').val('');
        $('#company').val('');
        $('#mobile').val('');
        $('#plateno').val('');
        $('#things').val('');
        $('#plateui li span').html('');
        $('#plateinput').attr('data-pai', '');
        $('#personnum').val('1');
        $('#hour').val('6')
    };

    $('#submit').click(function () {
        closePro();
        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.openWinOsk('', 'winosk');  
        }
        useractive.do();
        if (!visitorinfo || !visitorinfo.idcard) {
            $.toast("请先认证身份");
            return;
        }

        if (!$('#byvisitorid').val()) {
            $.toast("请选择被访人");
            return;
        }
        if (!$('#byarea').val()) {
            $.toast("请选择通行区域");
            return;
        }
        if (!$('#mobile').val()) {
            $.toast("请填写手机号");
            return;
        }else if($('#mobile').val().length != 11){
            $.toast("请输入11位长度手机号");
            return;
        }

        var p = $('#plateinput').attr('data-pai');
        if (p) {
            var ps = p.split('	');
            var plateno = [];
            for (var i = 0; i < ps.length; i++) {
                if (ps[i]) {
                    plateno.push(ps[i]);
                }
            }
            $('#plateno').val(plateno.join(''));
        }

        visitorinfo.reason = $('#reason').val();
        visitorinfo.byvisitor = $('#byvisitor').val();
        visitorinfo.byvisitorid = $('#byvisitorid').val();
        visitorinfo.mobile = $('#mobile').val();
        visitorinfo.plateno = $('#plateno').val();
        visitorinfo.things = $('#things').val();
        visitorinfo.personnum = $('#personnum').val();
        visitorinfo.company = $('#company').val();
        visitorinfo.hour = $('#hour').val();
        visitorinfo.areaposition = $('#byareaposition').val();
        visitorinfo.areapositionName = $('#byarea').val();
        $.postAjax({
            url: "/visitor/SubmitVisiterInfo",
            data: {
                visitorinfo: JSON.stringify(visitorinfo)
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    var visitorname = visitorinfo.name;
                    var byvisitor = visitorinfo.byvisitor;
                    visitorinfo = {};
                    $.toast("登记成功", function () {
                        initform();
                        if ($('#visitorPrintQRCode').val() && $('#visitorPrintQRCode').val() == 1) {
                            useractive.qrCode('window.QrCodePrintmsg', result.data.qrcode, visitorname, $('#byvisitorOrgname').val(), byvisitor, result.data.visittime);
                        }
                    });
                } else {
                    $.toast(result.msg, "forbidden");
                }
            }
        });
    });

    //人证比对
    window.cardandfacecompare = function (identid, sfzmsg) {
        // $('#photo').html('< img src="data:image/png;base64,' + sfzmsg + '" style="width:200px;"/>');
        // visitorinfo.photo = sfzmsg;
        layoutdata(sfzmsg);
    };
    //人证比对打开窗体
    $('#cardandfacecompare').click(function () {
        useractive.idCardAndFaceComPare('window.cardandfacecompare', 'cardface');
    });

    window.changefaceimg = function (identid, imgbase64) {
        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.closeWinOsk('', 'winosk');
        }
        if(visitorinfo.idcardphoto){
            $('#photo').html('<img src="data:image/png;base64,' + imgbase64 + '"  style="height:126px;"/><img  style="margin-left:30px;height: 126px;" src="data:image/png;base64,' + visitorinfo.idcardphoto + '"/>');
        }else {
            $('#photo').html('<img src="data:image/png;base64,' + imgbase64 + '" style="width:200px;"/>');
        }
        visitorinfo.photo = imgbase64;
    };

    $('#takephoto').click(function () {

        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.closeWinOsk('', 'winosk');
        }
        useractive.camera('window.changefaceimg', 'photo');
    });

    //证件扫描结果回调
    window.changetakecardmsg = function (identid, result) {
        var data = JSON.parse(result);
        if (data.cardtype == 1) { //护照
            var sex = data.sex.split('/')[0];
            visitorinfo = {
                name: data.hznamecn,
                sex: sex,
                birthday: data.borndate,
                address: data.bornaddress,
                idcard: data.hzcode,
                idtype: 4,
                photo: '',
                nation: '',
                idcardphoto: data.images,
                hour: $('#hour').val()
            };

            $('#name').html(data.hznamecn);
            $('#sex').html(sex);
            $('#birthday').html(data.borndate);
            $('#idcard').html(data.hzcode);
            $('#address').html(data.bornaddress);
            $('#org').html(data.IssuingAuthority);
            $('#startdate').html(data.ValidyEnd);
        } else if (data.cardtype == 2) { //港澳通行证
            visitorinfo = {
                name: data.namechn,
                sex: data.sex,
                birthday: data.birthday,
                address: data.address,
                idtype: 4,
                idcard: data.cardnum,
                hour: $('#hour').val(),
                idcardphoto: data.images,
                nation: '',
                photo: ''
            };

            $('#name').html(data.namechn);
            $('#sex').html(data.sex);
            $('#birthday').html(data.birthday);
            $('#idcard').html(data.cardnum);
            $('#address').html(data.address);
            $('#startdate').html(data.validdate);
        } else if (data.cardtype == 3) { //驾驶证
            visitorinfo = {
                name: data.personname,
                sex: data.sex,
                birthday: data.personborn,
                address: data.personaddress,
                idtype: 2,
                idcard: data.personsfz,
                idcardphoto: data.images,
                photo: '',
                nation: '',
                hour: $('#hour').val()
            };

            $('#name').html(data.personname);
            $('#sex').html(data.sex);
            $('#birthday').html(data.personborn);
            $('#idcard').html(data.personsfz);
            $('#address').html(data.personaddress);
            $('#startdate').html(data.expdatestart + ' 至 ' + data.expdateend);
        }
        $('#photo').html('<img src="data:image/png;base64,' + data.images + '" style="width:200px;height:126px;"/>');
        
    };
    //证件扫描打开窗体
    $('#takecardcmd').click(function () {

        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.closeWinOsk('', 'winosk');
        }
        initform();
        useractive.takeCardCamera('window.changetakecardmsg', 'takecard');
    });

    //二维码打印结果回调
    window.QrCodePrintmsg = function (qrcode) {
        $.alert(qrcode, "证件扫描提示");
    };
    // //二维码打印
    // $('#qrcodeprint').click(function () {
    //     //二维码,拜访人，被拜访人部门,被拜访人姓名,拜访时间
    //     useractive.qrcode('window.QrCodePrintmsg', '123456789','张三','研发部','胡东林','2021-07-19');
    // });

    $('#personnum').click(function () {

        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.closeWinOsk('', 'winosk');
        }
        useractive.do();
        var numPad = new NumKeyBoard({
            text: '人数',
            valType: 'tel',
            precision: 0,//精确度
            minVal: 0,	//最小值
            maxVal: 100  //最大值
        });
        numPad.open(function (data) {
            $('#personnum').val(data);
            useractive.do();
        });
    });


    $('#selectinfo').click(function () {
        closePro();
        useractive.do();

        var str = '';
        str += '<table style="margin-left:auto;margin-right:auto;margin-top: 30px;" id="serachbox">';
        str += '	<tr>';
        str += '		<td style="text-align:center; height:64px;font-size: 20px;width:120px;">被访人姓名：</td>';
        str += '		<td><input type="text" id="empname" value="' + $('#byvisitor').val() + '" style="width:200px;" placeholder="请输入被访人姓名"/></td>';
        str += '		<td style="text-align:center; height:64px;font-size: 20px;width:120px;">手机：</td>';
        str += '		<td><input type="text" id="empmobile" value="" style="width:200px;" placeholder="至少尾号后4位"/></td>';
        str += '		<td style="text-align:center;width: 140px;">';
        str += '			<a class="weui-btn" href="javascript:void(0)" id="searchbutton" style="width: 120px;">查询</a>';
        str += '		</td>';
        str += '		<td style="text-align:center;width: 140px;">';
        str += '			<a class="weui-btn" href="javascript:void(0)" id="clearbutton" style="width: 120px;">清除条件</a>';
        str += '		</td>';
        str += '	</tr>';
        str += '</table>';
        str += '<div style="height:440px;" class="weui-grids" id="empinfolist"></div>';

        var windowbox = $.windowbox({
            title: '检索被访人',
            style: 'width:1200px;margin-left:-600px;',
            buttons: [
                {
                    id: 'selectok',
                    text: '确定',
                    style: 'width:150px;',
                    click: function (b) {
                        var sl = $('#empinfolist .empitemselected', windowbox);
                        if (sl.length == 0) {
                            $.alert('请选择被访人', "系统提示");
                            return;
                        }
                        var infoid = sl.attr('data-uid');
                        var infoname = sl.attr('data-name');
                        var orgname = sl.attr('data-orgname');
                        $('#byvisitorid').val(infoid);
                        $('#byvisitor').val(infoname);
                        $('#byvisitorOrgname').val(orgname);
                        windowbox.remove();
                    }
                }
            ],
            html: str
        });

        $('#empname').on('focus',function(){
            if (keyboard!=0||keyboard==""){
                closePro();
                useractive.openWinOsk('', 'winosk');
            }
        })

        $('#empmobile').on('focus',function(){
            if (keyboard!=0||keyboard==""){
            closePro();
            useractive.openWinOsk('', 'winosk');  
        }
        })


        $('#searchbutton', windowbox).click(function () {
            if (keyboard!=0||keyboard==""){
            closePro();
            useractive.closeWinOsk('', 'winosk');
        }

            if (!$('#empname', windowbox).val() && !$('#empmobile', windowbox).val()) {
                $.alert('请输入被访人姓名或手机号', "系统提示");
                return;
            }

            if ($('#empmobile', windowbox).val()) {
                if ($('#empmobile', windowbox).val().length < 4) {
                    $.alert('被访人手机号至少后4位', "系统提示");
                    return;
                }
            }
            
            $.postAjax({
                url: "/visitor/getByVisitorWithMobileList",
                data: {
                    key: $('#empname', windowbox).val(),
                    mobile: $('#empmobile', windowbox).val()
                },
                hideLoading: false,
                success: function (result, textStatus, jqXHR) {
                    if (result.success) {
                        var str = '';
                        $.each(result.data, function (i, item) {
                            str += '<a href="javascript:void(0)" class="weui-grid js_grid empitem" data-uid="' + item.uid + '" data-name="' + item.name + '" data-orgname="' + item.orgname + '" style="width:20%;">';
                            str += '	<div class="weui-grid__icon" style="width: 72px;height: 72px;border-radius: 50%;margin-right: 10px;display: block;overflow: hidden;float: left;">';
                            if (!item.imgpath) {
                                item.imgpath = '/ui/img/icon-default-s.png';
                            }
                            str += '	  <img src="' + item.imgpath + '" onerror="javascript:this.src=\'/ui/img/icon-default-s.png\';">';
                            str += '	</div>';
                            str += '	<p class="weui-grid__label" style="text-align:left">' + item.name + '</p>';
                            if (item.orgname) {
                                str += '	<p class="weui-grid__label" style="text-align:left;color:#999">' + item.orgname + '</p>';
                            }
                            if (item.mobile) {
                                var mobile = item.mobile;
                                if (item.mobile.length == 11) {
                                    mobile = item.mobile.substr(0, 3) + '****' + item.mobile.substr(7, 4);
                                }
                                str += '<p class="weui-grid__label" style="text-align:left;color:#999">' + mobile + '</p>';
                            }
                            str += '</a>';
                        });
                        $('#empinfolist', windowbox).html(str);
                        $('#empinfolist .empitem', windowbox).click(function () {
                            $('#empinfolist .empitem', windowbox).removeClass('empitemselected');
                            $(this).addClass('empitemselected');
                        });
                    } else {
                        $.toast(result.msg, "forbidden");
                    }
                }
            });
        });

        $('#empmobile', windowbox).focus(function () {
            useractive.do();
            var numPad = new NumKeyBoard({
                text: '手机号',
                valType: 'tel',
                precision: 0,       //精确度
                minVal: 0,        //最小值
                maxVal: 20000000000  //最大值
            });
            numPad.open(function (data) {
                $('#empmobile', windowbox).val(data);
                useractive.do();
                $('#searchbutton', windowbox).click();
            });
        });

        if ($('#empname', windowbox).val()) {
            $('#searchbutton', windowbox).click();
        }

        $('#clearbutton', windowbox).focus(function () {
            $('#empname', windowbox).val('');
            $('#empmobile', windowbox).val('');
            $('#empinfolist', windowbox).html('');
        });

    });

    $('#selectarea').click(function () {
        closePro();
        useractive.do();

        $.postAjax({
            url: "/visitor/getUserAlleyAreaList",
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    // 检查 positionName 是否为数组且至少包含一个区域对象
                    var positionNameList = result.data;

                    if (Array.isArray(positionNameList) && positionNameList.length > 0) {
                        // 提取 positionName 数组
                        var areas = positionNameList.map(function (area) {
                            return {
                                position: area.position,
                                positionName: area.positionName
                            };
                        });

                        // 创建复选框列表
                        var checkboxList = areas.map(function (area, index) {
                            return '<label class="weui-cell weui-check__label" for="checkbox' + index + '">\n' +
                                '    <div class="weui-cell__bd">\n' +
                                '        <p>' + area.positionName + '</p>\n' +
                                '    </div>\n' +
                                '    <div class="weui-cell__ft">\n' +
                                '        <input type="checkbox" class="weui-check" name="areaCheckbox" id="checkbox' + index + '" value="' + area.position + '" data-positionName="' + area.positionName + '">\n' +
                                '        <span class="weui-icon-checked"></span>\n' +
                                '    </div>\n' +
                                '</label>';
                        }).join('');



                        var checkboxListStartHtml = '<div class="weui-cells weui-cells_checkbox">';  // 复选框列表
                        var checkboxListEndHtml = '</div>';

                        $.windowbox({
                            title: '选择通行区域',
                            html: checkboxListStartHtml + checkboxList + checkboxListEndHtml,
                            buttons: [
                                {
                                    id: 'takearea',
                                    text: '确认',
                                    style: 'width:100px;',
                                    click: function () {
                                        // 获取所有选中的复选框的值和对应的 positionName
                                            var areaindex = [];
                                            var areaname = [];
                                        $('input[name="areaCheckbox"]:checked').each(function() {
                                            var position = $(this).val();  // 获取选中的值（即 area.position）
                                            var positionName = $(this).attr('data-positionName');  // 使用 .attr() 获取 data-positionName 值（即 area.positionName）
                                            areaindex.push(position);  // 保存 position
                                            areaname.push(positionName);  // 保存 positionName
                                        });
                                        // 设置表单字段的值
                                        $('#byareaposition').val(areaindex.join(','));
                                        $('#byareaname').val(areaname.join(','));
                                        $('#byarea').val(areaname.join(','));
                                        $('#windowbox').remove();  // 关闭弹窗

                                    }
                                }
                            ]
                        });
                    } else {
                        // 处理没有有效区域数据的情况
                        console.error('No valid area data found.');
                        $.alert("未找到有效的通行区域", "系统提示");
                    }
                } else {
                    console.error('Failed to fetch position names:', result.msg);
                    $.alert(result.msg, "系统提示");
                }
            }
        });
    });




    $('#reason').focus(function(){
        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.openWinOsk('', 'winosk');
        }
    });
    $('#things').focus(function(){
        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.openWinOsk('', 'winosk');
        }
    });

    $('#company').focus(function(){
        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.openWinOsk('', 'winosk');
        }
    });

    $('.input_pro').click(function(){
        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.openWinOsk('', 'winosk');  
        }
    });
    
    $('.input_pp').click(function(){
        if (keyboard!=0||keyboard==""){
            closePro();
            useractive.openWinOsk('', 'winosk');  
        }
    });
});
