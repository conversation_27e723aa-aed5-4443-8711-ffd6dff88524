$(function () {
    $(document).ready(function() {
        var defaultPosition = $('#areaposition').val();
        var devices = $('#devices').val();
        var userDeviceSettings = [];
        if (devices) {
            try {
                userDeviceSettings = JSON.parse(devices);
                console.log(userDeviceSettings)
            } catch (e) {
                userDeviceSettings = [];
            }
        }

        var status = $('#status').val();
        var auditremark = '';
        var infoid = $('#infoid').val();
        var uid = $('#uid').val();
        var deviceSettings = {}; // 存储所有设备的设置
        // 全局变量，用于存储每个梯控设备选择的楼层信息
        var selectedFloors = {};
        // 只生成一次，所有门禁设备共用
        const defaultPassword = Math.floor(100000 + Math.random() * 900000).toString();
        // 发起 AJAX 请求获取后端数据
        $.postAjax({
            url: '/visitor/getVisitorPosition', // 后端数据接口的URL
            success: function(response) { // 请求成功时的回调函数
                if (response.success) { // 检查请求是否成功
                    var data = response.data; // 获取返回数据中的选项数组

                    // 创建一个 <select> 元素
                    var selectElement = $('<select id="areaposition"  class="weui-select" name="areaposition"></select>');
                    // 添加一个提示选项
                    var placeholderOption = $('<option disabled selected value="">请点击选择区域</option>');
                    selectElement.append(placeholderOption);
                    // 循环遍历选项数组，并为每个选项创建一个 <option> 元素
                    $.each(data, function(index, option) {
                        var optionElement = $('<option></option>')
                            .val(option.position) // 设置选项的值为 position
                            .text(option.name); // 设置选项的显示文本为 name

                        // 将 <option> 元素添加到 <select> 元素中
                        selectElement.append(optionElement);
                    });
                    // 根据 status 状态来禁用或启用下拉框
                    if (status === '1') {
                        selectElement.prop('disabled', true);
                    }
                    // 将 <select> 元素添加到页面中
                    $('#areapositionname').empty().append(selectElement);
                    // 设置默认值为选中项
                    selectElement.val(defaultPosition);

                    // 添加change事件监听器获取选中的值
                    selectElement.on('change', function() {
                        defaultPosition = $(this).val();
                        loadChannelDevices(defaultPosition);
                    });

                    // 初始化显示
                    if (defaultPosition) {
                        loadChannelDevices(defaultPosition);
                    }
                } else {
                    console.error('Error fetching data:', response.msg); // 打印错误信息到控制台
                }
            },
            error: function(xhr, status, error) { // 请求失败时的回调函数
                console.error('Error fetching data:', error); // 打印错误信息到控制台
            }
        });
        $('#bdisagree').click(function () {
            auditremark = $('#auditremark').val();
            auditLeave(-1);
        });
        $('#bagree').click(function () {
            auditremark = $('#auditremark').val();
            auditLeave(1);
        });
        $('#saveDeviceSettings').click(function() {
            // 验证设备设置
            let isValid = true;
            for (const deviceId in deviceSettings) {
                const device = deviceSettings[deviceId];
                // if (device.type === '12' && // 梯控设备
                //     (!device.settings.floors || device.settings.floors.length === 0)) {
                //     $.alert(`请为设备 ${deviceId} 选择至少一个楼层`);
                //     isValid = false;
                //     break;
                // }
                if (device.type === '2' && // 人脸控制器
                    (!device.settings.password || device.settings.password.length !== 6)) {
                    $.alert(`请为设备 ${deviceId} 输入6位数字密码`);
                    isValid = false;
                    break;
                }
            }

            if (!isValid) return;

            // 显示提交中状态
            $.showLoading('提交中...');

            const doorPermissionBits = getDoorPermissionBitsFromDeviceSettings(deviceSettings);

            const devices = Object.entries(deviceSettings)
                .filter(([deviceId, device]) => {
                    // 梯控设备且未选楼层的，过滤掉
                    if (device.type === '12' && (!device.settings.floors || device.settings.floors.length === 0)) {
                        return false;
                    }
                    return true;
                })
                .map(([deviceId, device]) => {
                    if (device.type === '12') {
                        let selectedFloors = device.settings.floors || [];
                        let floorBits = getFloorBits(selectedFloors, 64);
                        return {
                            deviceId: deviceId,
                            type: device.type,
                            floors:floorBits
                        };
                    } else if (device.type === '2') {
                        return {
                            deviceId: deviceId,
                            type: device.type,
                            doorRight: doorPermissionBits[deviceId]
                        };
                    } else {
                        return {
                            deviceId: deviceId,
                            type: device.type
                        };
                    }
                });

            // 构建提交数据
            const submitData = {
                uid: uid,
                status: status,
                remark: auditremark,
                areaPosition: defaultPosition,
                globalPassword: window.globalPassword,
                globalUsageCount: window.globalUsageCount,
                devices: devices
            };

            // 发送请求
            $.ajax({
                url: "/visitor/saveDeviceSettings",
                method: "POST",
                contentType: "application/json",
                data: JSON.stringify(submitData),
                hideLoading: false,
                success: function (result) {
                    $.hideLoading();
                    if (result.success) {
                        $.alert("保存成功", function () {
                            window.location.refresh();
                        });
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                },
                error: function(xhr, status, error) {
                    $.hideLoading();
                    $.alert('保存失败，请重试');
                    console.error('Error:', error);
                }
            });
    });

        var auditLeave = function (status) {
            // 验证设备设置
            let isValid = true;
            for (const deviceId in deviceSettings) {
                const device = deviceSettings[deviceId];
                // if (device.type === '12' && // 梯控设备
                //     (!device.settings.floors || device.settings.floors.length === 0)) {
                //     $.alert(`请为设备 ${deviceId} 选择至少一个楼层`);
                //     isValid = false;
                //     break;
                // }
                if (device.type === '2' && // 人脸控制器
                    (!device.settings.password || device.settings.password.length !== 6)) {
                    $.alert(`请为设备 ${deviceId} 输入6位数字密码`);
                    isValid = false;
                    break;
                }
            }

            if (!isValid) return;

            // 显示提交中状态
            $.showLoading('提交中...');

            const doorPermissionBits = getDoorPermissionBitsFromDeviceSettings(deviceSettings);

            const devices = Object.entries(deviceSettings)
                .filter(([deviceId, device]) => {
                    // 梯控设备且未选楼层的，过滤掉
                    if (device.type === '12' && (!device.settings.floors || device.settings.floors.length === 0)) {
                        return false;
                    }
                    return true;
                })
                .map(([deviceId, device]) => {
                    if (device.type === '12') {
                        let selectedFloors = device.settings.floors || [];
                        let floorBits = getFloorBits(selectedFloors, 64);
                        return {
                            deviceId: deviceId,
                            type: device.type,
                            floors:floorBits
                        };
                    } else if (device.type === '2') {
                        return {
                            deviceId: deviceId,
                            type: device.type,
                            doorRight: doorPermissionBits[deviceId]
                        };
                    } else {
                        return {
                            deviceId: deviceId,
                            type: device.type
                        };
                    }
                });

            // 构建提交数据
            const submitData = {
                uid: uid,
                status: status,
                remark: auditremark,
                areaPosition: defaultPosition,
                globalPassword: window.globalPassword,
                globalUsageCount: window.globalUsageCount,
                devices: devices
            };

            // 发送请求
            $.ajax({
                url: "/visitor/auditApply",
                method: "POST",
                contentType: "application/json",
                data: JSON.stringify(submitData),
                hideLoading: false,
                success: function (result) {
                    $.hideLoading();
                    if (result.success) {
                        $.alert("审核成功", function () {
                            window.location.refresh();
                        });
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                },
                error: function(xhr, status, error) {
                    $.hideLoading();
                    $.alert('提交失败，请重试');
                    console.error('Error:', error);
                }
            });
        }

    if($('#isleave').val() != 1){
        if($('#status').val() == 2){
            $('#cancelstatus').show();
            $('#cancel').hide();
        }else if($('#status').val() == 0){
            $('#cancel').show();
            // if($('#auditstatus').val() == 1 && $('#byagree').val() == 1){
            //     $('#agree').show();
            // }else if($('#auditstatus').val() == 1 && $('#byagree').val() == 0){
            //     $('#disagree').show();
            // }else {
            //     $('#disaduit').show()
            // }
        } else if($('#status').val() == -1){
            $('#disagree').show();
            $('#cancel').hide();
        } else if($('#status').val() == 1){
            $('#agree').show();
        }
    }else if($('#isleave').val() == 1){
        $('#hadleave').show();
        $('#cancel').hide();
    }



    $('#cancel').click(function(){
        $.confirm("您确定取消预约?", "取消预约", function () {
            $.postAjax({
                url: "/visitor/cancelVisitSub",
                data: {
                    uid: $('#uid').val()
                },
                hideLoading: false,
                success: function (result, textStatus, jqXHR) {
                    if (result.success) {
                        $.toast("取消成功", function () {
                            window.location.refresh();
                        });
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }
            });
        });
    })

    var max = mp.pageSize;
    var loadRecordList = function () {
        $.postAjax({
            url: "/visitor/getApprovalRecord",
            data: {
                'formid': $('#uid').val(),
                'start': $("#list a").length,
                'limit': max
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if (result.data.length > 0) {
                        $.each(result.data, function (i, item) {
                            var status = ''
                            if (item.auditcondition == 1) {
                                status = "<lable style='color:#0bb20c;'>已审核</lable>";
                            } else if (item.auditcondition == 0) {
                                status = "<lable style='color:red;'>待审核</lable>";
                            }

                            var str='';
                            str+='<div class="weui-panel">';
                            str+='	<div class="weui-panel__bd">';
                            str+='	   <a class="weui-media-box weui-media-box_text" style="display:block;">';
                            str+='		<h4 class="weui-media-box__title">'+item.name+'</h4>';
                            str+='		<ul class="weui-media-box__info">';
                            if(item.remark != undefined){
                                str+='		<p class="weui-media-box__desc">'+"备注:" + item.remark + '</p>';
                            }
                            str+='		  <li class="weui-media-box__info__meta">'+"审核时间：" + item.createdate + '</li>';
                            str+='		  <li class="weui-media-box__info__meta">'+"审核状态:" + status + '</li>';
                            str+='		</ul>';
                            str+='	  </a>';
                            str+='	</div>';
                            $("#list").append(str)
                        });
                    }
                }
            },
        });
    }
    loadRecordList();

    // 加载通道关联的设备
    function loadChannelDevices(position) {
        // 显示加载中
        $('#deviceControls').show();
        $('#deviceList').html('<div class="weui-loading"></div>');

        $.postAjax({
            url: '/visitor/getChannelDevices',
            data: { position: position },
            success: function(response) {
                if (response.success) {
                    const devices = response.data;
                    if (devices && devices.length > 0) {
                        $('#deviceList').empty();
                        deviceSettings = {}; // 清空设备设置

                        // 1. 判断是否有类型为2的设备
                        const hasType2 = devices.some(device => device.devclass === '2');
                        // console.log('设备列表:', devices, '是否有类型2:', hasType2);

                        // 先清空全局密码和使用次数输入框容器，避免重复插入
                        $('#globalPasswordUsageContainer').empty();

                        if (hasType2) {
                            // 生成6位随机默认密码
                            const defaultPassword = Math.floor(100000 + Math.random() * 900000).toString();
                            const globalInputs = $(`
                                <div class="weui-cells__title" style="margin-top:10px;">门禁控制器密码与使用次数</div>
                                <div class="weui-cell">
                                    <div class="weui-cell__hd"><label class="weui-label" style="width:90px;">密码</label></div>
                                    <div class="weui-cell__bd">
                                        <input type="text" id="global-password" class="weui-input" maxlength="6" placeholder="请输入6位数字密码" value="${defaultPassword}" />
                                    </div>
                                </div>
                                <div class="weui-cell">
                                    <div class="weui-cell__hd"><label class="weui-label" style="width:90px;">使用次数</label></div>
                                    <div class="weui-cell__bd">
                                        <input type="number" id="global-usage-count" class="weui-input" min="1" placeholder="请输入使用次数" />
                                    </div>
                                </div>
                            `);
                            $('#globalPasswordUsageContainer').append(globalInputs);

                            // 设置默认密码到全局变量
                            window.globalPassword = defaultPassword;

                            $('#global-password').on('input', function() {
                                this.value = this.value.replace(/[^\d]/g, '').slice(0, 6);
                                window.globalPassword = this.value;
                            });
                            $('#global-usage-count').on('input', function() {
                                window.globalUsageCount = this.value;
                            });

                            if (status === '1') {
                                $('#global-password').prop('disabled', true);
                                $('#global-usage-count').prop('disabled', true);
                            }

                            // 回显全局密码和使用次数
                            if (userDeviceSettings.length > 0) {
                                // 找到第一个有 password 和 usageCount 的对象
                                var first = userDeviceSettings.find(d => d.password && d.usageCount);
                                if (first) {
                                    $('#global-password').val(first.password);
                                    $('#global-usage-count').val(first.usageCount);
                                    window.globalPassword = first.password;
                                    window.globalUsageCount = first.usageCount;
                                }
                            }
                        }

                        let mergedDevices = {};
                        devices.forEach(device => {
                            if (device.devclass === '2') {
                                if (!mergedDevices[device.devid]) {
                                    mergedDevices[device.devid] = {
                                        devname: device.devname,
                                        devclass: device.devclass,
                                        doors: []
                                    };
                                }
                                mergedDevices[device.devid].doors.push({
                                    // doorid: device.doorNo, // 注意这里用 doorNo 作为 doorid
                                    doorName: device.doorName,
                                    relay: device.relays
                                });
                            }
                            const deviceElement = createDeviceControl(device);
                            $('#deviceList').append(deviceElement);
                        });

                        // 赋值到 deviceSettings
                        Object.keys(mergedDevices).forEach(devid => {
                            deviceSettings[devid] = {
                                type: '2',
                                doors: mergedDevices[devid].doors,
                                settings: { password: window.globalPassword || '' }
                            };
                        });
                    } else {
                        $('#deviceControls').hide();
                    }
                } else {
                    weui.alert('加载设备信息失败：' + response.msg);
                    $('#deviceControls').hide();
                }
            },
            error: function() {
                weui.alert('加载设备信息失败，请重试');
                $('#deviceControls').hide();
            }
        });
    }

    // 创建设备控制界面
    function createDeviceControl(device) {
        let selectedFloorsArr = [];
        var userSetting = userDeviceSettings.find(function(d) {
            return String(d.devId) === String(device.devid);
        });
        if (userSetting && userSetting.floors) {
            selectedFloorsArr = userSetting.floors.split(',').map((v, idx) => v === "1" ? (idx + 1) : null).filter(v => v);
        }

        const deviceType = device.devclass === '12' ? '梯控设备' : device.devclass === '9' ? '人脸机' : '门禁控制器';

        // 梯控设备：设备名+楼层选择同行
        if (device.devclass === '12') {
            // 统一初始化
            if (!deviceSettings[device.devid]) {
                deviceSettings[device.devid] = {
                    type: device.devclass,
                    settings: {}
                };
            }
            deviceSettings[device.devid].settings.floors = selectedFloorsArr.slice();

            const deviceElement = $('<div class="weui-cell device-item"></div>');
            deviceElement.append(`<div class="weui-cell__bd"><p>${device.devname} (${deviceType})</p></div>`);
            const right = $('<div class="weui-cell__ft"></div>');
            const floorSelection = $('<div class="floor-selection-inline"></div>');

            $.postAjax({
                url: '/visitor/getElevatorFloors',
                data: { deviceId: device.devid },
                success: function(response) {
                    if (response.success) {
                        const floors = response.data;
                        floors.forEach((floor, idx) => {
                            const floorItem = $('<label class="floor-item-inline"></label>');
                            const checkbox = $('<input type="checkbox" />').val(floor.id);
                            // 回显：如果已选则勾选
                            if (selectedFloorsArr.includes(floor.id)) {
                                checkbox.prop('checked', true);
                            }
                            if (status === '1') {
                                checkbox.prop('disabled', true);
                            }
                            floorItem.append(checkbox).append($('<span></span>').text(floor.name));
                            checkbox.on('change', function() {
                                if (this.checked) {
                                    deviceSettings[device.devid].settings.floors.push(floor.id);
                                } else {
                                    deviceSettings[device.devid].settings.floors =
                                        deviceSettings[device.devid].settings.floors.filter(id => id !== floor.id);
                                }
                            });
                            floorSelection.append(floorItem);
                        });
                    }
                }
            });
            right.append(floorSelection);
            deviceElement.append(right);
            return deviceElement;
        }

        // 门禁设备：设备名+密码输入框同行
        if (device.devclass === '2') {
            const deviceElement = $('<div class="weui-cell device-item"></div>');
            deviceElement.append(`<div class="weui-cell__bd"><p>${device.devname} ${device.doorName}(${deviceType})</p></div>`);
            return deviceElement;
        }

        // 人脸机：只显示设备名，右侧留空
        if (device.devclass === '9') {
            if (!deviceSettings[device.devid]) {
                deviceSettings[device.devid] = {
                    type: device.devclass
                };
            }
            const deviceElement = $('<div class="weui-cell device-item"></div>');
            deviceElement.append(`<div class="weui-cell__bd"><p>${device.devname} (${deviceType})</p></div>`);
            deviceElement.append('<div class="weui-cell__ft"></div>');
            return deviceElement;
        }
    }

    function getDoorPermissionBitsFromDeviceSettings(deviceSettings) {
        const result = {};
        for (const deviceId in deviceSettings) {
            const device = deviceSettings[deviceId];
            if (device.type === '2' && device.doors && device.doors.length > 0) {
                const relays = device.doors.map(d => d.relay); // 用 relay 字段
                const maxBits = 8; // 假设最多8个门
                let bits = Array(maxBits).fill('0');
                relays.forEach(relay => {
                    if (relay >= 0 && relay < maxBits) {
                        bits[maxBits - 1 - relay] = '1';
                    }
                });
                result[deviceId] = bits.join('');
            }
        }
        return result;
    }

    function getFloorBits(selectedFloors, maxFloor = 64) {
        // 1. 初始化全0数组
        let bits = Array(maxFloor).fill(0);
        // 2. 置1
        selectedFloors.forEach(id => {
            if (id >= 1 && id <= maxFloor) {
                bits[id - 1] = 1; // id=1对应下标0
            }
        });
        // 3. 转字符串
        return bits.join(',');
    }

})
});
