$(function () {
    mp.setTitle('访客预约');

    $.postAjax({
        url: "/visitor/getByVisitorList",
        data: { key: $('#key').val() },
        hideLoading: false,
        success: function (result) {
            if (result.success) {
                if (!result.data || result.data.length === 0) {
                    $.alert("无可用审核人", "系统提示");
                    return;
                }
                // 假设 status 字段在 result.data[0] 里
                var status = result.data[0].status;
                if (status == 1) {
                    renderByVisitorList(result.data);
                } else if (status == 0) {
                    $.alert("当前被访人不是审核人，请选择一个系统默认审核人进行审核", "系统提示", function () {
                        renderAuditorList(result.data);
                    });
                }
            } else {
                $.alert(result.msg, "系统提示");
            }
        }
    });

    // 渲染被访人列表
    function renderByVisitorList(data) {
        $("#list").empty();
        $.each(data, function (i, item) {
            if (!item.name) item.name = '';
            if (!item.mobile) item.mobile = '';
            var str = "<a class='weui-cell weui-cell_access' href='javascript:;' data-uid='" + item.uid + "'>";
            str += "<div class='weui-cell__bd' id='byvisitor' data-name='" + item.name + "'>" + item.name + "<br><div style='font-size: 12px;color: #888;'>" + item.orgname + "</div></div>";
            str += "<div class='weui-cell__ft' >" + (item.mobile.sliceHide ? item.mobile.sliceHide(3, 7) : item.mobile) + "</div>";
            str += "</a>";
            $("#list").append(str);
        });
        $("#list a").unbind();
        $('#list a').click(function () {
            window.location.href = '/visitor/subvisitor?byvisitorid=' + $(this).attr('data-uid') + '&time=' + ((new Date()).getTime());
        });
    }

    // 渲染审核人列表
    function renderAuditorList(data) {
        $("#list").empty();
        $.each(data, function (i, item) {
            if (!item.name) item.name = '';
            if (!item.mobile) item.mobile = '';
            var str = "<a class='weui-cell weui-cell_access' href='javascript:;' data-uid='" + item.uid + "'>";
            str += "<div class='weui-cell__bd' id='byvisitor' data-name='" + item.name + "'>" + item.name + "<br><div style='font-size: 12px;color: #888;'>" + item.orgname + "</div></div>";
            str += "<div class='weui-cell__ft' >" + (item.mobile.sliceHide ? item.mobile.sliceHide(3, 7) : item.mobile) + "</div>";
            str += "</a>";
            $("#list").append(str);
        });
        $("#list a").unbind();
        $('#list a').click(function () {
            window.location.href = '/visitor/subvisitor?byvisitorid=' + $(this).attr('data-uid') + '&time=' + ((new Date()).getTime());
        });
    }
});