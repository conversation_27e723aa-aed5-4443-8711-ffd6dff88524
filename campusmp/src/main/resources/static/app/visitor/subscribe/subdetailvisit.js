$(function () {

    if($('#isleave').val() != 1){
        if($('#status').val() == 2){
            $('#cancelstatus').show();
            $('#cancel').hide();
        }else if($('#status').val() == 0){
            $('#cancel').show();
            // if($('#auditstatus').val() == 1 && $('#byagree').val() == 1){
            //     $('#agree').show();
            // }else if($('#auditstatus').val() == 1 && $('#byagree').val() == 0){
            //     $('#disagree').show();
            // }else {
            //     $('#disaduit').show()
            // }
        } else if($('#status').val() == -1){
            $('#disagree').show();
            $('#cancel').hide();
        } else if($('#status').val() == 1){
            $('#agree').show();
        }
    }else if($('#isleave').val() == 1){
        $('#hadleave').show();
        $('#cancel').hide();
    }
    
    

    $('#cancel').click(function(){
        $.confirm("您确定取消预约?", "取消预约", function () {
            $.postAjax({
                url: "/visitor/cancelVisitSub",
                data: {
                    uid: $('#uid').val()
                },
                hideLoading: false,
                success: function (result, textStatus, jqXHR) {
                    if (result.success) {
                        $.toast("取消成功", function () {
                            window.history.back();
                        });
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }
            });
        });
    })

    var max = mp.pageSize;
    var loadRecordList = function () {
        $.postAjax({
            url: "/visitor/getApprovalRecord",
            data: {
                'formid': $('#uid').val(),
                'start': $("#list a").length,
                'limit': max
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if (result.data.length > 0) {
                        $.each(result.data, function (i, item) {
                            var status = ''
                            if (item.auditcondition == 1) {
                                status = "<lable style='color:#0bb20c;'>已审核</lable>";
                            } else if (item.auditcondition == 0) {
                                status = "<lable style='color:red;'>待审核</lable>";
                            }

                            var str='';
                            str+='<div class="weui-panel">';
                            str+='	<div class="weui-panel__bd">';
                            str+='	   <a class="weui-media-box weui-media-box_text" style="display:block;">';
                            str+='		<h4 class="weui-media-box__title">'+item.name+'</h4>';
                            str+='		<ul class="weui-media-box__info">';
                            if(item.remark != undefined){
                                str+='		<p class="weui-media-box__desc">'+"备注:" + item.remark + '</p>';
                            }
                            str+='		  <li class="weui-media-box__info__meta">'+"审核时间：" + item.createdate + '</li>';
                            str+='		  <li class="weui-media-box__info__meta">'+"审核状态:" + status + '</li>';
                            str+='		</ul>';
                            str+='	  </a>';
                            str+='	</div>';
                            $("#list").append(str)
                        });
                    }
                }
            },
        });
    }
    loadRecordList();

    // 解析设备权限信息
    var devices = $('#devices').val();
    var userDeviceSettings = [];
    if (devices) {
        try {
            userDeviceSettings = JSON.parse(devices);
        } catch (e) {
            userDeviceSettings = [];
        }
    }

    // 1. 全局密码和次数（只展示一次）
    if (userDeviceSettings.length > 0) {
        var first = userDeviceSettings.find(d => d.password && d.usageCount);
        if (first) {
            $('#password').html('<span style="color:#1890ff;font-weight:bold;">' + first.password + '</span>');
            $('#usageCount').html('<span style="color:#fa8c16;font-weight:bold;">' + first.usageCount + '</span>');
        }
    }

    // 2. 展示所有设备详细信息
    var html = '';
    var statusMap = {
        0: '待下载',
        1: '已下载',
        2: '下载失败',
        3: '待删除',
        4: '删除失败',
        5: '待重下',
        6: '重下失败'
    };
    var statusColorMap = {
        0: '#faad14', // 橙色
        1: '#52c41a', // 绿色
        2: '#f5222d', // 红色
        3: '#faad14', // 橙色
        4: '#f5222d', // 红色
        5: '#faad14', // 橙色
        6: '#f5222d'  // 红色
    };
    userDeviceSettings.forEach(function(d, idx) {
        var devName = (d.devName + '设备');
        var typeName = '';
        if (d.doors) {
            typeName = '门禁控制器';
        } else if (d.floors) {
            typeName = '梯控设备';
        } else {
            typeName = '人脸机';
        }

        html += '<div class="weui-panel" style="margin-bottom:12px;">';
        html += '<div class="weui-panel__hd" style="font-weight:bold;">' + devName + '（' + typeName + '）</div>';
        html += '<div class="weui-panel__bd">';

        // 下载状态
        var statusText = statusMap[d.status] !== undefined ? statusMap[d.status] : '未知';
        var statusColor = statusColorMap[d.status] || '#999';
        html += '<div class="weui-cell"><div class="weui-cell__bd"><p>下载状态</p></div><div class="weui-cell__ft" style="color:' + statusColor + ';font-weight:bold;">' + statusText + '</div></div>';

        // 门禁控制器展示门信息
        if (d.doors) {
            html += '<div class="weui-cell"><div class="weui-cell__bd"><p>门信息</p></div><div class="weui-cell__ft">';
            var doorNames = d.doorName;
            html += doorNames && doorNames.length > 0 ? doorNames : '';
            html += '</div></div>';
        }

        // 梯控设备展示楼层权限（异步加载真实楼层名）
        if (d.floors && d.devId) {
            var panelIdx = 'floorPanel_' + idx;
            html += '<div class="weui-cell"><div class="weui-cell__bd"><p>可通行楼层</p></div><div class="weui-cell__ft" id="' + panelIdx + '">加载中...</div></div>';

            // 异步加载楼层名
            (function(panelIdx, d) {
                $.post('/visitor/getElevatorFloors', { deviceId: d.devId, infoType: 2 }, function(response) {
                    // 调试输出
                    // console.log('设备ID:', d.devId, '楼层权限:', d.floors, '楼层列表:', response.data);

                    if (response.success && Array.isArray(response.data) && d.floors) {
                        var floorArr = d.floors.split(',');
                        var floorNames = [];
                        // 只遍历楼层列表，防止越界
                        response.data.forEach(function(floor, i) {
                            if (floorArr[i] === "1") {
                                floorNames.push(floor.name);
                            }
                        });
                        $('#' + panelIdx).text(floorNames.length > 0 ? floorNames.join('、') : '无');
                    } else {
                        $('#' + panelIdx).text('无');
                    }
                });
            })(panelIdx, d);
        }

        // 人脸机只展示类型
        if (!d.doors && !d.floors) {
            html += '<div class="weui-cell"><div class="weui-cell__bd"><p>人脸识别设备</p></div><div class="weui-cell__ft">已授权</div></div>';
        }

        html += '</div></div>'; // 结束 weui-panel
    });
    $('#deviceList').html('<div class="weui-cell"><div class="weui-cell__bd"></div></div>');

    // if (html) {
    //     $('#deviceList').html(html);
    // } else {
    //     $('#deviceList').html('<div class="weui-cell"><div class="weui-cell__bd">无设备信息</div></div>');
    // }

});
