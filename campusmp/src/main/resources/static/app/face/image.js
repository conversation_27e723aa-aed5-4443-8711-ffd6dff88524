$(function () {
    $('#resultImageContainer').height($('#resultImageContainer').width() * 4 / 3);


    $('#Rotate').click(function () {
        $.showLoading('旋转中');

    });


    $('#uploaderInput').change(function (e) {
        if (e.target.files.length > 0) {
            var file = e.target.files[0];
            $.showLoading('上传中');
            compressImage(file)
                .then(compressedFile => {
                    // 创建一个新的 FormData 对象
                    var formData = new FormData();

                    // 创建一个新的 File 对象并追加到 FormData
                    var compressedBlob = new Blob([compressedFile], {type: compressedFile.type});
                    var compressedFileObj = new File([compressedBlob], file.name);
                    formData.set('faceimg', compressedFileObj);
                    var stuId = $('#stuId').val();
                    // 添加其他表单数据，如果需要的话
                    formData.set('orientation', $('#orientation').val());
                    formData.set('visitorid', $('#visitorid').val());
                    formData.set('stuId', stuId);
                    formData.set('name', $('#name').val());
                    formData.set('uid', $('#uid').val());
                    // 使用 AJAX 异步上传
                    // 将 FormData 转换为表单元素的值
                    var form = $('#faceform');
                    form.find('input[name="faceimg"]').remove();  // 移除原始文件输入
                    form.append('<input type="file" name="faceimg" style="display: none;" />');
                    var fileInput = form.find('input[name="faceimg"]');
                    var dataTransfer = new DataTransfer();
                    dataTransfer.items.add(compressedFileObj);
                    fileInput[0].files = dataTransfer.files;

                    // 提交表单
                    form.submit();
                })
                .catch(error => {
                    // 处理错误
                    console.error('压缩图像时发生错误:', error);
                });
        }
    });


    $('#changeUploaderInput').change(function (e) {
        if (e.target.files.length > 0) {
            $.showLoading('上传中');
            var file = e.target.files[0];
            EXIF.getData(file, function () {
                var Orientation = EXIF.getTag(this, 'Orientation');
                $('#orientation').val(Orientation);
                $('#changefaceform').submit();
            });
            compressImage(file)
                .then(compressedFile => {
                    // 创建一个新的 FormData 对象
                    var formData = new FormData();

                    // 创建一个新的 File 对象并追加到 FormData
                    var compressedBlob = new Blob([compressedFile], {type: compressedFile.type});
                    var compressedFileObj = new File([compressedBlob], file.name);
                    formData.set('faceimg', compressedFileObj);
                    var stuId = $('#stuId').val();
                    // 添加其他表单数据，如果需要的话
                    formData.set('orientation', $('#orientationC').val());
                    formData.set('visitorid', $('#visitoridC').val());
                    formData.set('stuId', stuId);
                    formData.set('name', $('#name').val());
                    formData.set('uid', $('#uid').val());
                    // 使用 AJAX 异步上传
                    // 将 FormData 转换为表单元素的值
                    var form = $('#changefaceform');
                    form.find('input[name="faceimg"]').remove();  // 移除原始文件输入
                    form.append('<input type="file" name="faceimg" style="display: none;" />');
                    var fileInput = form.find('input[name="faceimg"]');
                    var dataTransfer = new DataTransfer();
                    dataTransfer.items.add(compressedFileObj);
                    fileInput[0].files = dataTransfer.files;

                    // 提交表单
                    form.submit();
                })
                .catch(error => {
                    // 处理错误
                    console.error('压缩图像时发生错误:', error);
                });
        }
    });

    function compressImage(file) {
        return new Promise((resolve, reject) => {
            const maxSize = 500; // 最大宽或高的尺寸
            const reader = new FileReader();

            reader.onload = function (e) {
                const img = new Image();
                img.onload = function () {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    let width = img.width;
                    let height = img.height;

                    // 限制图片大小
                    if (width > height && width > maxSize) {
                        height = Math.round((height * maxSize) / width);
                        width = maxSize;
                    } else if (height > width && height > maxSize) {
                        width = Math.round((width * maxSize) / height);
                        height = maxSize;
                    }

                    // 设置 canvas 大小
                    canvas.width = width;
                    canvas.height = height;

                    // 获取图片方向信息（EXIF）
                    EXIF.getData(file, function () {
                        const orientation = EXIF.getTag(this, 'Orientation') || 1;

                        // 根据方向调整画布
                        switch (orientation) {
                            case 3: // 180° 旋转
                                ctx.rotate(Math.PI);
                                ctx.drawImage(img, -width, -height, width, height);
                                break;
                            case 6: // 顺时针 90°
                                canvas.width = height;
                                canvas.height = width;
                                ctx.rotate(Math.PI / 2);
                                ctx.drawImage(img, 0, -height, width, height);
                                break;
                            case 8: // 逆时针 90°
                                canvas.width = height;
                                canvas.height = width;
                                ctx.rotate(-Math.PI / 2);
                                ctx.drawImage(img, -width, 0, width, height);
                                break;
                            default: // 不旋转
                                ctx.drawImage(img, 0, 0, width, height);
                        }

                        // 将 canvas 转为 Blob
                        canvas.toBlob(blob => {
                            if (blob) {
                                resolve(new File([blob], file.name, { type: file.type }));
                            } else {
                                reject(new Error("图像压缩失败"));
                            }
                        }, file.type, 0.8); // 压缩质量 0.8
                    });
                };

                img.onerror = function () {
                    reject(new Error("图像加载失败"));
                };

                img.src = e.target.result; // 加载图片
            };

            reader.onerror = function () {
                reject(new Error("文件读取失败"));
            };

            reader.readAsDataURL(file); // 读取文件
        });
    }

    $('#modifyImage').click(
        function () {
            // 切换文件上传控件的可见性
            $('#uploadphotoC').show(); // 使用jQuery的show()方法显示控件
        });

    $('#backstu').click(function () {
        window.location.href = '/face/student?classname=' + $('#classname').val() + '&orgCode=' + $('#orgCode').val();
    })
    $('#backClass').click(function () {
        window.location.href = '/teach/classInfo_detail?uid=' + $('#stuId').val();
    })

    $('#removeimg').click(function () {
        var uid = $('#uid').val();
        var stuId = $('#stuId2').val();
        $.confirm("确定删除?删除后无法通过人脸识别认证,需要联系管理员重新授权！", "删除人脸", function () {
            $.postAjax({
                url: "/face/removeface",
                data: {
                    uid: uid,
                    stuId: stuId
                },
                success: function (result, textStatus, jqXHR) {
                    if (result.success) {
                        window.location.refresh();
                        $.toast("提交成功");
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }
            });
        });
    });

});