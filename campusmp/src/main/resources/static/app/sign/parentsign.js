$(function () {
    // 设置页面标题
    mp.setTitle('签到');
    wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: $('#appId').val(), // 必填，公众号的唯一标识
        timestamp: parseInt($('#timeStamp').val()), // 必填，生成签名的时间戳
        nonceStr: $('#nonceStr').val(), // 必填，生成签名的随机串
        signature: $('#signature').val(),// 必填，签名
        jsApiList: ['getLocation'] // 需要调用的JS接口
    });

    wx.ready(function () {
        wx.getLocation({
            type: 'wgs84', // 返回的是标准的WGS84坐标系
            success: function (res) {
                var latitude = res.latitude; // 纬度
                var longitude = res.longitude; // 经度
                var speed = res.speed; // 速度
                var accuracy = res.accuracy; // 精度
                // 在这里发送经纬度到后台进行范围判断
                checkInRange(latitude, longitude, accuracy);
            },
            fail: function (error) {
                console.log(error);
                alert("无法获取您的位置，请检查权限设置");
                // 定位失败时，退回上一页面
                history.back(); // 返回上一页
            }
        });
    });

    function checkInRange(latitude, longitude, accuracy) {
        // 请求当前签到状态
        $.postAjax({
            url: "/sign/SignInLocation",
            data: {
                latitude: latitude,
                longitude: longitude,
                accuracy: accuracy
            },
            success: (result) => {
                if (result.success) {
                    alert('定位成功！');
                    // 定位成功后，展示签到按钮并启用
                    showCheckInButton();
                } else {
                    alert('您不在签到范围内');
                }
            },
            error: function(error) {
                alert('签到请求失败，请稍后重试');
            }
        });
    }

    const signInCfg = JSON.parse($("#signInCfg").val());
    const statusText = document.getElementById('statusText');
    const checkInBtn = document.getElementById('checkInBtn');
    const recordsDiv = document.getElementById('records');

    const current = new Date();
    const currentTimeInSeconds = current.getHours() * 3600 + current.getMinutes() * 60 + current.getSeconds();

    // 格式化时间为秒数
    const getTimeInSeconds = (timeObj) => {
        const [hours, minutes] = timeObj.split(":").map(num => parseInt(num, 10));
        return hours * 3600 + minutes * 60;
    };

    // 判断当前时间是否在签到时间范围内
    const isWithinTimeRange = (startTimeInSeconds, endTimeInSeconds) => {
        return currentTimeInSeconds >= startTimeInSeconds && currentTimeInSeconds <= endTimeInSeconds;
    };

    let periodUid = '';
    let isWithinAnyTimeRange = false;

    // 生成签到时间段列表
    signInCfg.forEach(item => {
        const startTimeInSeconds = getTimeInSeconds(item.startTime);
        const endTimeInSeconds = getTimeInSeconds(item.endTime);
        const listItem = `<div class="weui-cells__title">${item.name}: ${item.startTime} - ${item.endTime}</div>`;
        $("#list").append(listItem);

        if (isWithinTimeRange(startTimeInSeconds, endTimeInSeconds)) {
            periodUid = item.uid;
            isWithinAnyTimeRange = true;
        }
    });

    // 请求当前签到状态
    $.postAjax({
        url: "/sign/checkSignIn",
        data: { periodUid, date: current },
        success: (result) => {
            if (result.success && result.data) {
                updateStatusTextAndButton(true, result.data[0].signin_time);
            } else {
                updateStatusTextAndButton(false);
            }
        }
    });

    // 更新签到按钮和状态文本
    const updateStatusTextAndButton = (hasCheckedIn, signInTime = null) => {
        if (hasCheckedIn) {
            statusText.innerText = '已签到';
            checkInBtn.classList.add('checked');
            checkInBtn.innerText = '已签到';
            appendSignInRecord(signInTime);
        } else {
            if (isWithinAnyTimeRange) {
                statusText.innerText = '未签到';
                // 定位成功后才显示并启用按钮
                checkInBtn.style.display = 'block';
                checkInBtn.disabled = false;  // 启用按钮
                checkInBtn.addEventListener('click', handleSignInClick);
            } else {
                statusText.innerText = '不在签到时间范围内';
                checkInBtn.style.display = 'none';
            }
        }
    };

    // 显示并启用签到按钮
    const showCheckInButton = () => {
        checkInBtn.style.display = 'block'; // 显示按钮
        checkInBtn.disabled = false; // 启用按钮
    };

    // 点击签到按钮时的处理
    const handleSignInClick = () => {
        $.postAjax({
            url: "/sign/saveSignIn",
            data: { periodUid, date: current },
            success: (result) => {
                if (result.success) {
                    updateStatusTextAndButton(true, new Date().toLocaleString());
                    $.toast("签到成功");
                } else {
                    $.alert(result.msg, "系统提示");
                }
            }
        });
    };

    // 添加签到记录到页面
    const appendSignInRecord = (signInTime) => {
        const recordDiv = document.createElement('div');
        recordDiv.className = 'weui-cell record-item';
        recordDiv.innerHTML = `<div class="weui-cell__bd"><p>${signInTime}</p></div>`;
        recordsDiv.appendChild(recordDiv);
    };
});
