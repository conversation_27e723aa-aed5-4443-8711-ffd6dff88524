$(function () {
    mp.setTitle('账户明细');


    var max = mp.pageSize;

    var show = false;

    var loading = false;  //状态标记
    var StartOnAutoLoad=function(){
        $(document.body).infinite().on("infinite", function () {
            if (loading) return;
            loading = true;
            setTimeout(function () {
                loadRecordList();
            }, 500);   //模拟延迟
        });
    };

    var loadRecordList = function () {
        $('#loadmore').show();
        $.postAjax({
            url: "/consume/gettranlist",
            data: {
            	start: $("#list a").length,
            	limit: max,
            	startTime: $("#startTime").val(),
            	endTime: $("#endTime").val(),
            	cardid:$('#cardlist').val()
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if(result.data.length < max && $("#list a").length===0){
                        $('#loadmore').hide();
                    }

                    $("#total").empty();
                    if (result.msg == undefined){
                        result.msg = 0
                    }
                    $("#total").append("<label for=\"\" class='weui-label' style=\"font-size: 20px\">"+result.msg+"元</label>")
                    if (result.data.length > 0) {
                        $.each(result.data, function (i, item) {
                            //totalmoney+=item.money;
                            if(!item.money){
                                item.money='';
                            }
                            $("#list").append("<a class='weui-cell weui-cell_access' href='javascript:;' data-uid='" + item.uid + "'>"
                                + "<div class='weui-cell__bd' >￥" + item.money + "元</div>"
                                + "<div class='weui-cell__ft' >" + item.tradedate + "</div>"
                                + "</a>")
                        });
                        $("#list a ").unbind();
                        $('#list a').click(function () {
                            $.showToast();
                            if (!show) {
                                detailed($(this).attr('data-uid'));
                                show = true;
                            }
                        })
                        $('#loadmore').hide();
                        StartOnAutoLoad();
                    } else {
                        //$("#total").empty();
                        $.toptip('已无更多记录');
                        $('#loadmore').hide();
                        $(document.body).destroyInfinite()
                    }
                } else {
                    $("#total").empty();
                    $.alert(result.msg, "系统提示");
                }
            },
            complete:function(jqXHR, textStatus){
           	 loading = false;
            }
        });
    };

    $.postAjax({
        url: "/consume/getcardlist",
        data: {},
        hideLoading: false,
        success: function (result, textStatus, jqXHR) {
            if (result.success) {
                if (result.data.length > 0) {
                    $.each(result.data, function (i, item) {
                       var des=item.cardno+'('+item.usedes+')';
                       if(item.status==0){
                    	   des+='无效卡';
                       }else if(item.status==2){
                    	   des+='挂失';
                       }
                       var sel=' selected="selected" ';
                       if(!item.ismain){
                    	   sel=''; 
                       }
                       $('#cardlist').append('<option value="'+item.uid+'" '+sel+' >'+des+'</option>');
                    });
                    
                    loadRecordList();
                }else{
                	$.toptip('未发卡');
                }
            }
        }
    });
    
    $('#cardlist').change(function(){
    	$("#list").html('<div class="weui-panel__hd">账户明细</div>');
    	loadRecordList();
    });
    function initializeStartDateToFirstOfMonth() {
        var currentDate = new Date();
        currentDate.setDate(1);

        var year = currentDate.getFullYear();
        var month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
        var day = currentDate.getDate().toString().padStart(2, '0');
        var hours = '00';
        var minutes = '00';

        var formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
        $("#startTime").val(formattedDate);
    }

    $(document).ready(function() {
        initializeStartDateToFirstOfMonth();
    });
    $("#select").click(function () {
        var startData = new Date($("#startTime").val());
        var endData = new Date($("#endTime").val());
        if (startData > endData ) {
            $.toptip('起始日期不能大于结束日期');
            return;
        }
        $('#list').html('<div class="weui-panel__hd">账户明细</div>');
        loadRecordList();
    });

    $.touch('#detailedrecord', function (e) {
        if (e.eventCode == 'moveleft') {
            $.closePopup();
            $("#list").show();
            show = false
        }
    });

    var detailed = function (uid) {
        $.postAjax({
            url: "/consume/getTrandetail",
            data: {
                uid: uid
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.data && result.data.length > 0) {
                    var item = result.data[0];

                    if(!item["wtuid"]){
                        $("#wtuidcell").hide();
                    }
                    if(!item["transactionid"]){
                        $("#transactionidcell").hide();
                    }

                    if (item) {
                        for (var k in item) {

                            if(k != 'money'){
                                if (!item[k]) {
                                    $("#" + k).html("暂无");
                                }
                                else{
                                    $("#" + k).html(item[k]);

                                }
                            }else{
                                $("#" + k).html('￥'+item[k]+'元');
                            }


                        }
                    }
                }
                $("#detailedrecord").popup();
                $("#list").hide();
                $.showToast();
            }
        });
    }
});
