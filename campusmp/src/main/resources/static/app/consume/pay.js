$(function(){

    mp.setTitle('充值');

    $(document).ready(function() {
        $('#money').on('input', function() {
            var value = parseFloat($(this).val());
            // if (value < 10) {
            //     $('#error-message').show();
            // } else {
            //     $('#error-message').hide();
            // }
        });

        // $('#money').on('blur', function() {
        //     var value = parseFloat($(this).val());
        //     if (value < 10) {
        //         $('#money').val('');
        //         alert('充值金额不能少于10元');
        //     }
        // });
    });


    var GetCardInfo=function(cardid){
        $.postAjax({
            url: "/consume/getcardinfo",
            data: { cardid:cardid },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if (result.data) {
                       $('#balance').html('￥'+result.data.balance.toString()+'元');
                       $('#vicewallet').html('￥'+result.data.vicewallet.toString()+'元');
                       $('#waterwallet').html('￥'+result.data.waterwallet.toString()+'元');
                    }else{
                    	$.toptip('获取卡片信息失败');
                    }
                }
            }
        });
    }



    $("#wallet").select({
        title: "选择充值钱包",
        items: [
            {
                title: '消费钱包',
                value: 1
            },
            {
                title: '水控钱包',
                value: 2
            }
        ],
        onClose:function(d){
            if(d.data.values == 1){
                $("#body").val($('#name').text()+'个人钱包充值')
            }else if(d.data.values == 2){
                $("#body").val($('#name').text()+'水控钱包充值')
            }
        }
    });

    $.postAjax({
        url: "/consume/getcardlist",
        data: {},
        hideLoading: false,
        success: function (result, textStatus, jqXHR) {
            if (result.success) {
                if (result.data.length > 0) {
                    $.each(result.data, function (i, item) {
                       var des=item.cardno+'('+item.usedes+')';
                       if(item.status==0){
                    	   des+='无效卡';
                       }else if(item.status==2){
                    	   des+='挂失';
                       }
                       var sel=' selected="selected" ';
                       if(!item.ismain){
                    	   sel=''; 
                       }else{
                    	   GetCardInfo(item.uid);
                       }
                       $('#cardlist').append('<option value="'+item.uid+'" '+sel+' >'+des+'</option>');
                    });
                }else{
                	$.toptip('未发卡');
                }
            }
        }
    });
    
    $('#cardlist').change(function(){
    	GetCardInfo($('#cardlist').val());
    });
    
    $('#submit').click(function(){
        if(!$('#money').val()){
            $.alert("请输入充值金额！", "系统提示");
            return;
        }
        if(parseInt($('#money').val()) == NaN){
            $.alert("请输入正确的数字！", "系统提示");
            $('#money').val(0);
            return;
        }
        $('#walletnum').val($('#wallet').attr('data-values'));
        $('#form').submit();
        return false;
    });

});