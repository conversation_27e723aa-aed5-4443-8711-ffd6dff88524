$(function () {

    var detailed = function () {
        var uid = $('#uid').val()
        $.postAjax({
            url: "/time/getSigncardRecordDetails",
            data: {
                uid: uid
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.data && result.data.length > 0) {
                    var item = result.data;
                    if (item) {
                        var auditrecord = item;

                        // for(var i = 0; i < booktimelist.length; i++){
                        //     var html = `<div class="weui-cell">
                        //                     <div class="weui-cell__bd">
                        //                         <p>${booktimelist[i].daily}</p>
                        //                     </div>
                        //                 </div>`;
                        //     $('#booktime').append(html);
                        // }

                        for (var j = 0; j < auditrecord.length; j++) {
                            var auditconditionHtml = ''
                            if (auditrecord[j].auditcondition == 0) {
                                auditconditionHtml = "<lable style='color:red;'>(待审核)</lable>";
                            } else if (auditrecord[j].auditcondition == 1) {
                                auditconditionHtml = "<lable style='color:#0bb20c;'>(审核完成)</lable>";
                            } else if (auditrecord[j].auditcondition == 2) {
                                auditconditionHtml = "<lable style='color:red;'>(已取消)</lable>";
                            } else if (auditrecord[j].auditcondition == 3) {
                                auditconditionHtml = "<lable style='color:#0bb20c;'>(已恢复)</lable>";
                            }
                            if (!auditrecord[j].remark) {
                                auditrecord[j].remark = '空';
                            }
                            if (!auditrecord[j].auditdate) {
                                auditrecord[j].auditdate = '未审核';
                            }
                            var html = '<div class="weui-media-box weui-media-box_text">';
                            html += '<h4 class="weui-media-box__title">' + auditrecord[j].name + auditconditionHtml + '</h4>';
                            html += '<p class="weui-media-box__desc">审核备注：' + auditrecord[j].remark + '</p>';
                            html += '<ul class="weui-media-box__info">';
                            html += '<li class="weui-media-box__info__meta">审核时间：</li>';
                            html += '<li class="weui-media-box__info__meta">' + auditrecord[j].auditdate + '</li>';
                            if (auditrecord[j].currentauditnodelevel) {
                                html += '<li class="weui-media-box__info__meta weui-media-box__info__meta_extra">审核等级：' + auditrecord[j].currentauditnodelevel + '级</li>';
                            }
                            html += '</ul>';
                            html += '</div>';
                            $('#auditRecord').append(html);

                        }
                    }
                }
            }
        });
    }
    detailed();

    var auditremark = '';
    var infoid = $('#infoid').val();

    $('#bagree').click(function () {
        auditremark = $('#auditremark').val();
        auditLeave(1);
    });

    $('#bdisagree').click(function () {
        auditremark = $('#auditremark').val();
        auditLeave(-1);
    });

    // 详情页JS
    $('#backBtn').click(function () {
        // 获取当前URL参数
        let params = window.location.search; // 例如 ?id=123&name=张三&starttime=2024-06-01&endtime=2024-06-10
        // 去掉id参数，只保留筛选条件
        let searchParams = new URLSearchParams(params);
        searchParams.delete('uid'); // 假设id是详情页用的
        searchParams.delete('endtime'); // 假设id是详情页用的
        // 跳回列表页并带上参数
        var s = '/time/auditsigncardlist' + (searchParams.toString() ? '?' + searchParams.toString() : '');
        window.location.href = s;
    });

    var uid = $('#uid').val();


    var auditLeave = function (status) {
        // 获取当前日期并设置为00:00:00
        var now = new Date();
        now.setHours(0, 0, 0, 0);

        // 计算3天前的日期
        var minDate = new Date(now);
        minDate.setDate(now.getDate() - 3);

        // 获取签名时间
        var signTimeStr = $('#createCardDt').val();
        // 将日期字符串转换为 iOS 支持的格式
        signTimeStr = signTimeStr.replace(/-/g, '/');  // 将所有连字符替换为斜杠
        var signTime = new Date(signTimeStr);

        // 比较日期
        if (signTime < minDate) {
            $.toptip("缺勤单已超过三天，禁止审核！！！");
            return;
        }

        $.postAjax({
            url: "/time/auditsigncardfrom",
            data: {
                uid: uid,
                infoid: infoid,
                status: status,
                remark: auditremark,
                dUserId: $('#resultSelect').val()
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if (result.success) {
                        $.toast("审核成功", function () {
                            window.location.refresh();
                        });
                    }
                } else {
                    $.alert(result.msg, "系统提示");
                }
            }
        });
    }

    $(document).ready(function () {
        // 向后端发送请求获取审核人类型数据
        $.ajax({
            url: '/time/getpsitoin', // 后端接口地址
            method: 'POST',
            async: false,
            dataType: 'json',
            success: function (res) {
                // 清空现有的选项（除了默认选项）
                $('#psitoin').find('option:not(:first)').remove();

                // 动态添加选项
                res.data.forEach(function (item) {
                    $('#psitoin').append(
                        `<option value="${item.name}">${item.name}</option>`
                    );
                });
            },
            error: function (error) {
                console.error('获取请假类型失败:', error);
            }
        });
    });

    // 获取元素引用
    const selectElement = document.getElementById('psitoin');
    const inputElement = document.getElementById('aduitname');
    const resultSelect = document.getElementById('resultSelect');

    // 防抖函数（300ms触发一次）
    function debounce(func, delay) {
        let timer;
        return function () {
            clearTimeout(timer);
            timer = setTimeout(() => {
                func.apply(this, arguments);
            }, delay);
        };
    }

    // 搜索处理函数
    function handleSearch() {
        const type = selectElement.value;
        const name = inputElement.value.trim();

        // 验证输入
        if (!type) {
            alert('请先选择审核人类型');
            return;
        }

        // 构建请求体
        const requestBody = {
            type: type,
            name: name
        };

        $.ajax({
            url: '/time/searchaduit', // 后端接口地址
            method: 'POST',
            async: false,
            dataType: 'json',
            data: requestBody,
            success: function (data) {
                // 清空之前的结果
                resultSelect.innerHTML = '<option value="">请选择审核人</option>';
                // 填充新的结果
                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.uid;
                    option.textContent = item.name.trim(); // 去除姓名首尾空格
                    resultSelect.appendChild(option);
                });
            },
            error: function (error) {
                console.error('请求错误:', error);
            }
        });
    }

    // 选择审核人类型时清空审核人姓名
    selectElement.addEventListener('change', function () {
        inputElement.value = '';
        resultSelect.innerHTML = '<option value="">请选择审核人</option>';
        handleSearch();
    });

    // 选择审核人后将姓名回写到输入框
    resultSelect.addEventListener('change', function () {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            inputElement.value = selectedOption.textContent.trim(); // 去除姓名首尾空格
        }
    });

    // 绑定事件
    inputElement.addEventListener('input', debounce(handleSearch, 300));
    selectElement.addEventListener('change', handleSearch);


    // if($('#usertype').val() == 2 || $('#usertype').val() == 3){
    //     var peoples = JSON.parse($('#peoples').val());
    //     var oldpeople = '';
    //     $('#people').select({
    //         title: "选择人员",
    //         items: peoples,
    //         onClose:function(d){
    //             if(d.data.values !== oldpeople){
    //                 $("#list").empty()
    //                 oldpeople = d.data.values;
    //                 // loadRecordList();
    //             }
    //         },
    //         onOpen: function(d){
    //             if(!peoples){
    //                 $.toptip('无可选人员');
    //             }
    //             oldpeople = d.data.values;
    //         }
    //     });
    // }
});
