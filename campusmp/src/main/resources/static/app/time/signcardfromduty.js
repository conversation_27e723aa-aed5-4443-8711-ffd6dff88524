$(function () {
    mp.setTitle('缺卡申请');




    // if($('#usertype').val() == 2 || $('#usertype').val() == 3){
    //     var peoples = JSON.parse($('#peoples').val());
    //     var oldpeople = '';
    //     $('#people').select({
    //         title: "选择人员",
    //         items: peoples,
    //         onClose:function(d){
    //             if(d.data.values !== oldpeople){
    //                 $("#list").empty()
    //                 oldpeople = d.data.values;
    //                 // loadRecordList();
    //             }
    //         },
    //         onOpen: function(d){
    //             if(!peoples){
    //                 $.toptip('无可选人员');
    //             }
    //             oldpeople = d.data.values;
    //         }
    //     });
    // }
    $(document).ready(function () {
        // 向后端发送请求获取请假类型数据
        $.ajax({
            url: '/time/getTypes', // 后端接口地址
            method: 'POST',
            async: false,
            dataType: 'json',
            success: function (res) {
                // 清空现有的选项（除了默认选项）
                $('#leaveschool').find('option:not(:first)').remove();

                // 动态添加选项
                res.data.forEach(function (item) {
                    $('#leaveschool').append(
                        `<option value="${item.value}">${item.name}</option>`
                    );
                });
            },
            error: function (error) {
                console.error('获取请假类型失败:', error);
            }
        });
    });

    $(document).ready(function () {
        // 向后端发送请求获取审核人类型数据
        $.ajax({
            url: '/time/getpsitoin', // 后端接口地址
            method: 'POST',
            async: false,
            dataType: 'json',
            success: function (res) {
                // 清空现有的选项（除了默认选项）
                $('#psitoin').find('option:not(:first)').remove();

                // 动态添加选项
                res.data.forEach(function (item) {
                    $('#psitoin').append(
                        `<option value="${item.name}">${item.name}</option>`
                    );
                });
            },
            error: function (error) {
                console.error('获取请假类型失败:', error);
            }
        });
    });

    // 获取元素引用
    const selectElement = document.getElementById('psitoin');
    const inputElement = document.getElementById('aduitname');
    const resultSelect = document.getElementById('resultSelect');

    // 防抖函数（300ms触发一次）
    function debounce(func, delay) {
        let timer;
        return function () {
            clearTimeout(timer);
            timer = setTimeout(() => {
                func.apply(this, arguments);
            }, delay);
        };
    }

    // 搜索处理函数
    function handleSearch() {
        const type = selectElement.value;
        const name = inputElement.value.trim();

        // 验证输入
        if (!type) {
            alert('请先选择审核人类型');
            return;
        }

        // 构建请求体
        const requestBody = {
            type: type,
            name: name
        };

        $.ajax({
            url: '/time/searchaduit', // 后端接口地址
            method: 'POST',
            async: false,
            dataType: 'json',
            data: requestBody,
            success: function (data) {
                // 清空之前的结果
                resultSelect.innerHTML = '<option value="">请选择审核人</option>';
                // 填充新的结果
                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.uid;
                    option.textContent = item.name.trim(); // 去除姓名首尾空格
                    resultSelect.appendChild(option);
                });
            },
            error: function (error) {
                console.error('请求错误:', error);
            }
        });
    }

    // 选择审核人类型时清空审核人姓名
    selectElement.addEventListener('change', function () {
        inputElement.value = '';
        resultSelect.innerHTML = '<option value="">请选择审核人</option>';
        handleSearch();
    });

    // 选择审核人后将姓名回写到输入框
    resultSelect.addEventListener('change', function () {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            inputElement.value = selectedOption.textContent.trim(); // 去除姓名首尾空格
        }
    });

    // 绑定事件
    inputElement.addEventListener('input', debounce(handleSearch, 300));
    selectElement.addEventListener('change', handleSearch);

    // 获取当前日期和时间
    var now = new Date();

    // 计算14天前的日期
    var minDate = new Date();
    minDate.setDate(now.getDate() - 3);

    // 格式化日期为datetime-local需要的格式 (YYYY-MM-DDTHH:mm)
    function formatStartDateForInput(date) {
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        var hours = String(0).padStart(2, '0');
        var minutes = String(0).padStart(2, '0');

        return year + '-' + month + '-' + day + 'T' + hours + ':' + minutes;
    }

    function formatEndDateForInput(date) {
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        var hours = String(date.getHours()).padStart(2, '0');
        var minutes = String(date.getMinutes()).padStart(2, '0');

        return year + '-' + month + '-' + day + 'T' + hours + ':' + minutes;
    }
    // // 格式化日期为datetime-local需要的格式 (YYYY-MM-DDTHH:mm)
    // function formatDateForInputMax(date) {
    //     var year = date.getFullYear();
    //     var month = String(date.getMonth() + 1).padStart(2, '0');
    //     var day = String(date.getDate()).padStart(2, '0');
    //
    //     return year + '-' + month + '-' + day + 'T' + 23 + ':' + 59;
    // }
    // 设置开始时间的最小和最大日期
    var startTimeInput = document.getElementById('signtime');
    startTimeInput.min = formatStartDateForInput(minDate);
    startTimeInput.max = formatEndDateForInput(now);


    $('#submit').click(function (e) {
        var signtime = $('#signtime').val();
        var worktype = $('#worktype').val();
        var ftype = $('#ftype').val();
        var reason = $('#reason').val();
        var docid = $('#people').attr('data-values');
        var aduituid = $('#resultSelect').val();
        if(!signtime){
            $.toptip("请填写缺卡时间！");
            return;
        }
        if(!worktype){
            $.toptip("请选择上下班类型！");
            return;
        }
        if(!ftype){
            $.toptip("请选择缺卡类型！");
            return;
        }
        if (ftype==2){
            if(!reason){
                $.toptip("请填写原因备注！");
                return;
            }
        }
        if (!aduituid) {
            $.toptip("请选择审核人员！");
            return;
        }
        if(!docid){
            $.toptip("请选择人员！");
            return;
        }
        if (now < minDate) {
            $.toptip("请选择14天内的时间进行补签！");
            return;
        }


        $.confirm("确认是否缺卡吗？", function() {
            $.postAjax({
                url: "/time/signcardfromdutyfrom",
                data: {
                    docid: docid,
                    signtime: signtime,
                    worktype: worktype,
                    ftype: ftype,
                    des: reason,
                    dUserId: aduituid
                },
                hideLoading: false,
                success: function (result, textStatus, jqXHR) {
                    if (result.success) {
                        $.toast("申请成功,等待审核！",function () {
                            window.location.href = '/time/signcardrecord?time=' + ((new Date()).getTime());
                        });
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }
            });
        });
    });

});