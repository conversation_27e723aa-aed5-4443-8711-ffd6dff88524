$(function () {
    mp.setTitle('考勤打卡记录');

    var max = mp.pageSize;

    var show = false;

    var loading = false;  //状态标记
    var StartOnAutoLoad=function(){
        $(document.body).infinite().on("infinite", function () {
            if (loading) return;
            loading = true;
            setTimeout(function () {
                loadRecordList();
            }, 500);   //模拟延迟
        });
    };
    // 获取当前日期和时间
    var now = new Date();

    // 计算14天前的日期
    var minDate = new Date();
    minDate.setDate(now.getDate() - 15);

    // 格式化日期为datetime-local需要的格式 (YYYY-MM-DDTHH:mm)
    function formatDateForInput(date) {
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        var hours = String(date.getHours()).padStart(2, '0');
        var minutes = String(date.getMinutes()).padStart(2, '0');

        return year + '-' + month + '-' + day + 'T' + hours + ':' + minutes;
    }
    // 设置开始时间的最小和最大日期
    var startTimeInput = document.getElementById('startTime');
    startTimeInput.min = formatDateForInput(minDate);
    startTimeInput.max = formatDateForInput(now);

    // 设置结束时间的最小和最大日期
    var endTimeInput = document.getElementById('endTime');
    endTimeInput.min = formatDateForInput(minDate);
    endTimeInput.max = formatDateForInput(now);

    // 添加事件监听器，确保结束时间不小于开始时间
    startTimeInput.addEventListener('change', function() {
        if (this.value) {
            endTimeInput.min = this.value;

            // 如果当前结束时间早于新的开始时间，重置结束时间
            if (endTimeInput.value && endTimeInput.value < this.value) {
                endTimeInput.value = this.value;
            }
        }
    });

    // 添加事件监听器，确保开始时间不大于结束时间
    endTimeInput.addEventListener('change', function() {
        if (this.value && startTimeInput.value > this.value) {
            startTimeInput.value = this.value;
        }
    });

    // 初始验证
    if (startTimeInput.value && endTimeInput.value) {
        if (startTimeInput.value > endTimeInput.value) {
            endTimeInput.value = startTimeInput.value;
        }
    }
    var loadRecordList = function () {
        var startTime = $("#startTime").val();
        var endTime = $("#endTime").val();
        if (startTime) {
            startTime = startTime.replace('T', ' ');
        }
        if (endTime) {
            endTime = endTime.replace('T', ' ');
        }
        $('#loadmore').show();
        $.postAjax({
            url: "/time/record/getTimeRecord",
            data: {
                'start': $("#list a").length,
                'limit': max,
                'startTime': startTime,
                'endTime': endTime
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if (result.data.length > 0) {
                        $.each(result.data, function (i, item) {
                            if(!item.timetypename){
                                item.timetypename='';
                            }
                            $("#list").append("<a class='weui-cell weui-cell_access' href='javascript:;' data-uid='" + item.uid + "'>"
                                + "<div class='weui-cell__hd'><img src='" + ((item.direction == 1) ? '/ui/img/icon-into.png' : '/ui/img/icon-exit.png') + "' ></div>"
                                + "<div class='weui-cell__bd' >" + item.timetypename + "</div>"
                                + "<div class='weui-cell__ft' >" + item.recordtime + "</div>"
                                + "</a>")
                        });
                        $("#list a ").unbind();
                        $('#list a').click(function () {
                            $.showToast();
                            if (!show) {
                                detailed($(this).attr('data-uid'));
                                show = true;
                            }
                        })
                        $('#loadmore').hide();
                        StartOnAutoLoad();
                    } else {
                        $.toptip('已无更多记录');
                        $('#loadmore').hide();
                        $(document.body).destroyInfinite()
                    }
                } else {
                    $.alert(result.msg, "系统提示");
                }
            },
            complete:function(jqXHR, textStatus){
              	 loading = false;
            }
        });
    }


    loadRecordList();

    $("#select").click(function () {
        var startData = new Date($("#startTime").val());
        var endData = new Date($("#endTime").val());
        if (startData > endData ) {
            $.toptip('起始日期不能大于结束日期');
            return;
        }
        $('#list').html('<div class="weui-panel__hd">考勤信息</div>');
        loadRecordList();
    })

    $.touch('#detailedrecord', function (e) {
        if (e.eventCode == 'moveleft') {
            $.closePopup();
            $("#list").show();
            show = false
        }
    });

    var detailed = function (uid) {
        $.postAjax({
            url: "/time/record/detailedrecord",
            data: {
                uid: uid
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.data && result.data.length > 0) {
                    var item = result.data[0];
                    if (item) {
                        for (var k in item) {
                            if (!item[k]) {
                                if (k != 'imgpath') {
                                    $("#" + k).html("暂无");
                                } else {
                                    $("#" + k).attr("src", '/ui/img/icon-default.png');
                                }
                            } else {
                                if (k != 'imgpath') {
                                    $("#" + k).html(item[k]);
                                } else {
                                    $("#" + k).attr("src", $('#resourcedomain').val()+item[k]);
                                }
                            }
                        }
                    }
                }
                $("#detailedrecord").popup();
                $("#list").hide();
                $.showToast();
            }
        });
    }
});
