$(function () {

    var max = mp.pageSize;

    var loading = false;  //状态标记
    var StartOnAutoLoad=function(){
        $(document.body).infinite().on("infinite", function () {
            if (loading) return;
            loading = true;
            setTimeout(function () {
                loadRecordList();
            }, 500);   //模拟延迟
        });
    };

    var loadRecordList = function () {
        var key = $("#selectText").val();
        var starttime = $("#starttime").val();
        var endtime = $("#endtime").val();
        if (starttime) {
            starttime = starttime.replace('T', ' ');
        }
        if (endtime) {
            endtime = endtime.replace('T', ' ');
        }
        $.postAjax({
            url: "/time/getAuditSigncardList",
            data: {
                key: key,
                start: $("#list a").length,
                limit: max,
                starttime: starttime,
                endtime: endtime
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if (result.data.length > 0) {
                        $.each(result.data, function (i, item) {
                            if(!item.name){
                                item.name='';
                            }
                            var status = '';
                            if (item.status == 0) {
                                status = "<lable style='color:red;'>审核中</lable>";
                            } else if(item.status == 1) {
                                status = "<lable style='color:#0bb20c;'>审核完成</lable>";
                            } else if(item.status == 2) {
                                status = "<lable style='color:red;'>已取消</lable>";
                            }
                            $("#list").append("<a class='weui-cell weui-cell_access' href='javascript:;' data-uid='" + item.uid + "'>"
                                + "<div class='weui-cell__bd' style='font-size: 15px;'><p>" + item.infoname + "</p></n><p>缺卡时间：" + item.signtime + "</p></div>"
                                + "<div class='weui-cell__ft' >" + status + "</div>"
                                + "</a>")
                        });
                        $("#list a ").unbind();
                        $('#list a').click(function () {
                            $(location).attr('href', '/time/auditsigncard?uid='+$(this).attr('data-uid')+'&time=' + ((new Date()).getTime())+'&name='+key+'&starttime='+starttime);
                        })
                        $('#loadmore').hide();
                        StartOnAutoLoad();
                    } else {
                        $.toptip('已无更多记录');
                        $('#loadmore').hide();
                        $(document.body).destroyInfinite();
                    }
                } else {
                    $.alert(result.msg, "系统提示");
                }
            },
            complete:function(jqXHR, textStatus){
           	 loading = false;
           }
        });
    }

    loadRecordList();

    $("#select").click(function () {
        var startData = new Date($("#starttime").val());
        var endData = new Date($("#endtime").val());
        if (startData > endData) {
            $.toptip('起始日期不能大于结束日期');
            return;
        }
        $('#list').html('<div class="weui-panel__hd">缺卡记录</div>');
        loadRecordList();
    })

});
