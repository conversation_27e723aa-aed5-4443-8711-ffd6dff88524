$(function () {
    // 0. 初始化变量
    var photoFile = null;
    var isProcessing = false;
    var latitude, longitude, accuracy;
    var activeBlobs = []; // 跟踪所有活跃的Blob URL

    // 1. 设备性能检测和微信环境优化
    function getDeviceProfile() {
        const ua = navigator.userAgent;
        const isWeChat = /MicroMessenger/i.test(ua);
        const isIOS = /iPhone|iPad|iPod/i.test(ua);
        const isAndroid = /Android/i.test(ua);

        return {
            isLowEnd: /Android [1-6]/.test(ua) ||
                (navigator.deviceMemory && navigator.deviceMemory < 2) ||
                (isWeChat && isAndroid), // 微信安卓版本更保守
            isWeChat: isWeChat,
            isIOS: isIOS,
            isAndroid: isAndroid,
            // 微信环境下的特殊配置
            maxSize: isWeChat ? (isIOS ? 800 : 600) : 1024,
            quality: isWeChat ? 0.5 : 0.7
        };
    }

    // 2. 内存管理 - 清理所有Blob URL
    function cleanupAllBlobs() {
        activeBlobs.forEach(url => {
            try {
                URL.revokeObjectURL(url);
            } catch (e) {
                console.warn('清理Blob失败:', e);
            }
        });
        activeBlobs = [];
    }

    // 3. 页面卸载时清理资源
    window.addEventListener('beforeunload', cleanupAllBlobs);
    window.addEventListener('pagehide', cleanupAllBlobs);

    // 2. 拍照按钮事件
    $('#uploadPhoto').click(function() {
        if (isProcessing) {
            $.toast("正在处理上一张照片，请稍候");
            return;
        }
        resetFileInput();
    });

    function resetFileInput() {
        const $input = $('#photoInput');
        $input.val('');
        setTimeout(() => $input.click(), 100);
    }

    // 3. 拍照后处理 - 微信优化版本
    $('#photoInput').change(function(e) {
        if (!e.target.files[0] || isProcessing) return;

        const file = e.target.files[0];
        const device = getDeviceProfile();

        // 微信环境下的文件大小检查
        if (device.isWeChat && file.size > 5 * 1024 * 1024) {
            $.alert("照片过大，请重新拍摄");
            resetFileInput();
            return;
        }

        isProcessing = true;
        showProcessing('正在处理照片...');

        // 微信环境下使用更保守的处理策略
        if (device.isWeChat) {
            processPhotoForWeChat(file, device)
                .then(processedFile => {
                    photoFile = processedFile;
                    return showPhotoPreview(processedFile);
                })
                .catch(error => {
                    console.error('微信照片处理错误:', error);
                    handleWeChatPhotoError(error, file);
                })
                .finally(() => {
                    isProcessing = false;
                });
        } else {
            safelyProcessPhoto(file, device)
                .then(processedFile => {
                    photoFile = processedFile;
                    return showPhotoPreview(processedFile);
                })
                .catch(error => {
                    console.error('照片处理错误:', error);
                    handlePhotoError(error, file, device);
                })
                .finally(() => {
                    isProcessing = false;
                });
        }
    });

    // 4. 微信专用照片处理方法
    function processPhotoForWeChat(file, device) {
        return new Promise((resolve, reject) => {
            // 微信环境下更保守的设置
            const settings = {
                maxSize: device.maxSize,
                quality: device.quality,
                timeout: 5000 // 微信环境下缩短超时时间
            };

            // 如果文件已经很小，直接使用
            if (file.size < 200 * 1024) {
                resolve(file);
                return;
            }

            const timeout = setTimeout(() => {
                reject(new Error('WECHAT_TIMEOUT'));
            }, settings.timeout);

            try {
                const blobUrl = URL.createObjectURL(file);
                activeBlobs.push(blobUrl);

                const img = new Image();

                img.onload = function() {
                    try {
                        // 计算压缩比例
                        const ratio = Math.min(
                            settings.maxSize / img.width,
                            settings.maxSize / img.height,
                            1 // 不放大
                        );

                        const canvas = document.createElement('canvas');
                        canvas.width = Math.floor(img.width * ratio);
                        canvas.height = Math.floor(img.height * ratio);

                        const ctx = canvas.getContext('2d');

                        // 微信环境下的特殊处理
                        ctx.imageSmoothingEnabled = false; // 关闭平滑以节省内存
                        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                        canvas.toBlob(blob => {
                            clearTimeout(timeout);

                            if (!blob) {
                                reject(new Error('WECHAT_BLOB_FAILED'));
                                return;
                            }

                            const processedFile = new File([blob], "wechat_photo.jpg", {
                                type: "image/jpeg",
                                lastModified: Date.now()
                            });

                            resolve(processedFile);
                        }, 'image/jpeg', settings.quality);

                    } catch (e) {
                        clearTimeout(timeout);
                        reject(new Error('WECHAT_CANVAS_ERROR'));
                    }
                };

                img.onerror = () => {
                    clearTimeout(timeout);
                    reject(new Error('WECHAT_IMAGE_ERROR'));
                };

                img.src = blobUrl;

            } catch (e) {
                clearTimeout(timeout);
                reject(new Error('WECHAT_INIT_ERROR'));
            }
        });
    }

    // 5. 通用照片处理方法（非微信环境）
    function safelyProcessPhoto(file, device) {
        return new Promise((resolve, reject) => {
            const settings = {
                maxSize: device.isLowEnd ? 1024 : 1600,
                quality: device.isLowEnd ? 0.6 : 0.8,
                timeout: device.isLowEnd ? 8000 : 12000
            };

            const timeout = setTimeout(() => {
                reject(new Error('PROCESS_TIMEOUT'));
            }, settings.timeout);

            const blobUrl = URL.createObjectURL(file);
            activeBlobs.push(blobUrl);

            const img = new Image();

            img.onload = function() {
                try {
                    const ratio = Math.min(
                        settings.maxSize / img.width,
                        settings.maxSize / img.height
                    );

                    const canvas = document.createElement('canvas');
                    canvas.width = Math.floor(img.width * ratio);
                    canvas.height = Math.floor(img.height * ratio);

                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                    canvas.toBlob(blob => {
                        clearTimeout(timeout);

                        if (!blob) {
                            reject(new Error('BLOB_CONVERSION_FAILED'));
                            return;
                        }

                        resolve(new File([blob], "optimized.jpg", {
                            type: "image/jpeg",
                            lastModified: Date.now()
                        }));
                    }, 'image/jpeg', settings.quality);
                } catch (e) {
                    clearTimeout(timeout);
                    reject(new Error('MEMORY_ERROR'));
                }
            };

            img.onerror = () => {
                clearTimeout(timeout);
                reject(new Error('IMAGE_LOAD_ERROR'));
            };

            img.src = blobUrl;
        });
    }

    // 5. 显示处理状态
    function showProcessing(message) {
        $('#photoPreview').html(`
            <div class="photo-processing">
                <span class="processing-spinner"></span>
                <span>${message}</span>
            </div>
        `);
        $('#processedImage').hide();
    }

    // 6. 显示照片预览
    function showPhotoPreview(file) {
        return new Promise(resolve => {
            const url = URL.createObjectURL(file);
            const img = document.getElementById('processedImage');

            img.onload = function() {
                $('#photoPreview').empty();
                $(img).show();
                URL.revokeObjectURL(url);
                resolve();
            };

            img.onerror = function() {
                $('#photoPreview').html(`
                    <div class="photo-error">
                        照片加载失败，请重试
                    </div>
                `);
                URL.revokeObjectURL(url);
                resolve();
            };

            img.src = url;
        });
    }

    // 7. 微信专用错误处理
    function handleWeChatPhotoError(error, file) {
        console.error('微信照片处理错误:', error);

        const errorActions = {
            'WECHAT_TIMEOUT': () => {
                $.toast('处理超时，使用原图');
                photoFile = file;
                showPhotoPreview(file);
            },
            'WECHAT_BLOB_FAILED': () => {
                $.toast('照片压缩失败，使用原图');
                photoFile = file;
                showPhotoPreview(file);
            },
            'WECHAT_CANVAS_ERROR': () => {
                $.toast('内存不足，使用原图');
                photoFile = file;
                showPhotoPreview(file);
            },
            'default': () => {
                // 微信环境下尽量不让用户重试，直接使用原图
                $.toast('处理失败，使用原图');
                photoFile = file;
                showPhotoPreview(file);
            }
        };

        (errorActions[error.message] || errorActions.default)();
    }

    // 8. 通用错误处理
    function handlePhotoError(error, file, device) {
        console.error('Photo error:', error);

        const errorActions = {
            'MEMORY_ERROR': () => {
                $.toast('内存不足，尝试简化处理...');
                uploadOriginalFile(file);
            },
            'PROCESS_TIMEOUT': () => {
                $.toast('处理超时，上传原图');
                uploadOriginalFile(file);
            },
            'default': () => {
                $('#photoPreview').html(`
                    <div class="photo-error">
                        照片处理失败，请重试
                    </div>
                `);
                resetFileInput();
            }
        };

        (errorActions[error.message] || errorActions.default)();
    }

    // 8. 上传原图
    function uploadOriginalFile(file) {
        photoFile = file;
        showPhotoPreview(file);
    }

    // 9. 微信定位和打卡处理
    wx.config({
        debug: false,
        appId: $('#appId').val(),
        timestamp: parseInt($('#timeStamp').val()),
        nonceStr: $('#nonceStr').val(),
        signature: $('#signature').val(),
        jsApiList: ['getLocation']
    });

    wx.ready(function() {
        wx.getLocation({
            type: 'gcj02',
            success: function(res) {
                latitude = res.latitude;
                longitude = res.longitude;
                accuracy = res.accuracy;
            },
            fail: function(err) {
                console.error('定位失败:', err);
            }
        });
    });

    // 10. 打卡提交 - 微信优化版本
    $('#returned').click(function() {
        if (!photoFile) {
            $.alert("请先拍摄照片");
            return;
        }

        // 防止重复提交
        if ($(this).attr('disabled')) {
            return;
        }

        $(this).attr('disabled', true);

        var accuracyDouble = safeToDouble(accuracy);
        var longitudeDouble = safeToDouble(longitude);
        var latitudeDouble = safeToDouble(latitude);
        var formData = new FormData();
        formData.append('photoFile', photoFile);
        formData.append('image', $('#imgInfo').val());
        formData.append('uid', $('#uid').val());
        formData.append('accuracy', accuracyDouble);
        formData.append('longitude', longitudeDouble);
        formData.append('latitude', latitudeDouble);

        $.showLoading();

        // 微信环境下的特殊处理
        const device = getDeviceProfile();
        const ajaxSettings = {
            url: "/time/checkSignIn",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            timeout: device.isWeChat ? 30000 : 60000, // 微信环境下缩短超时时间
            success: function(res) {
                if (res.success) {
                    // 清理资源
                    cleanupAllBlobs();

                    $.toast("打卡成功", function() {
                        // 微信环境下使用replace避免返回问题
                        if (device.isWeChat) {
                            window.location.replace('/time/timerecord?t=' + Date.now());
                        } else {
                            window.location.href = '/time/timerecord?t=' + Date.now();
                        }
                    });
                } else {
                    $('#returned').attr('disabled', false);
                    $.alert(res.msg || "打卡失败");
                }
            },
            error: function(xhr, status, error) {
                $('#returned').attr('disabled', false);

                if (status === 'timeout') {
                    $.alert("网络超时，请检查网络后重试");
                } else {
                    $.alert("网络错误，请重试");
                }
            },
            complete: function() {
                $.hideLoading();
            }
        };

        $.ajax(ajaxSettings);
    });

    // 通用转换函数
    function safeToDouble(value, defaultValue = 0) {
        if (value === undefined || value === null || value === '') {
            return defaultValue;
        }

        // 处理字符串类型的数字
        return value;
    }
});