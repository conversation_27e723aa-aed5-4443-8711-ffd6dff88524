$(function () {
    // 初始化变量
    var photoFile = null;
    var isProcessing = false;
    var latitude, longitude, accuracy;

    // 简化的设备检测
    function isWeChat() {
        return /MicroMessenger/i.test(navigator.userAgent);
    }

    // 2. 拍照按钮事件
    $('#uploadPhoto').click(function() {
        if (isProcessing) {
            $.toast("正在处理上一张照片，请稍候");
            return;
        }
        resetFileInput();
    });

    function resetFileInput() {
        const $input = $('#photoInput');
        $input.val('');
        setTimeout(() => $input.click(), 100);
    }

    // 拍照后处理 - 简化版本
    $('#photoInput').change(function(e) {
        if (!e.target.files[0] || isProcessing) return;

        const file = e.target.files[0];

        // 文件大小检查
        if (file.size > 10 * 1024 * 1024) {
            $.alert("照片过大，请重新拍摄");
            this.value = '';
            return;
        }

        isProcessing = true;
        showProcessing('正在压缩照片...');

        // 简化的压缩处理
        compressPhoto(file)
            .then(compressedFile => {
                photoFile = compressedFile;
                showPhotoPreview(compressedFile);
            })
            .catch(error => {
                console.error('照片压缩失败:', error);
                // 压缩失败就用原图
                photoFile = file;
                showPhotoPreview(file);
                $.toast('压缩失败，使用原图');
            })
            .finally(() => {
                isProcessing = false;
            });
    });

    // 简化的照片压缩方法
    function compressPhoto(file) {
        return new Promise((resolve, reject) => {
            // 如果文件已经很小，直接使用
            if (file.size < 500 * 1024) {
                resolve(file);
                return;
            }

            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            img.onload = function() {
                try {
                    // 计算压缩尺寸
                    let { width, height } = img;
                    const maxSize = isWeChat() ? 800 : 1200;

                    if (width > maxSize || height > maxSize) {
                        const ratio = Math.min(maxSize / width, maxSize / height);
                        width = Math.floor(width * ratio);
                        height = Math.floor(height * ratio);
                    }

                    canvas.width = width;
                    canvas.height = height;

                    // 绘制图片
                    ctx.drawImage(img, 0, 0, width, height);

                    // 转换为Blob
                    canvas.toBlob(blob => {
                        if (blob) {
                            const compressedFile = new File([blob], "compressed.jpg", {
                                type: "image/jpeg",
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        } else {
                            reject(new Error('压缩失败'));
                        }

                        // 清理
                        canvas.width = 0;
                        canvas.height = 0;
                        img.src = '';
                    }, 'image/jpeg', isWeChat() ? 0.6 : 0.8);

                } catch (e) {
                    reject(e);
                }
            };

            img.onerror = () => reject(new Error('图片加载失败'));

            // 使用FileReader读取文件
            const reader = new FileReader();
            reader.onload = e => img.src = e.target.result;
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsDataURL(file);
        });
    }

    // 5. 显示处理状态
    function showProcessing(message) {
        $('#photoPreview').html(`
            <div class="photo-processing">
                <span class="processing-spinner"></span>
                <span>${message}</span>
            </div>
        `);
        $('#processedImage').hide();
    }

    // 6. 显示照片预览
    function showPhotoPreview(file) {
        return new Promise(resolve => {
            const url = URL.createObjectURL(file);
            const img = document.getElementById('processedImage');

            img.onload = function() {
                $('#photoPreview').empty();
                $(img).show();
                URL.revokeObjectURL(url);
                resolve();
            };

            img.onerror = function() {
                $('#photoPreview').html(`
                    <div class="photo-error">
                        照片加载失败，请重试
                    </div>
                `);
                URL.revokeObjectURL(url);
                resolve();
            };

            img.src = url;
        });
    }



    // 8. 上传原图
    function uploadOriginalFile(file) {
        photoFile = file;
        showPhotoPreview(file);
    }

    // 9. 微信定位和打卡处理
    wx.config({
        debug: false,
        appId: $('#appId').val(),
        timestamp: parseInt($('#timeStamp').val()),
        nonceStr: $('#nonceStr').val(),
        signature: $('#signature').val(),
        jsApiList: ['getLocation']
    });

    wx.ready(function() {
        wx.getLocation({
            type: 'gcj02',
            success: function(res) {
                latitude = res.latitude;
                longitude = res.longitude;
                accuracy = res.accuracy;
            },
            fail: function(err) {
                console.error('定位失败:', err);
            }
        });
    });

    // 打卡提交
    $('#returned').click(function() {
        if (!photoFile) {
            $.alert("请先拍摄照片");
            return;
        }

        // 防止重复提交
        if ($(this).attr('disabled')) {
            return;
        }

        $(this).attr('disabled', true);

        var accuracyDouble = safeToDouble(accuracy);
        var longitudeDouble = safeToDouble(longitude);
        var latitudeDouble = safeToDouble(latitude);
        var formData = new FormData();
        formData.append('photoFile', photoFile);
        formData.append('image', $('#imgInfo').val());
        formData.append('uid', $('#uid').val());
        formData.append('accuracy', accuracyDouble);
        formData.append('longitude', longitudeDouble);
        formData.append('latitude', latitudeDouble);

        $.showLoading();

        $.ajax({
            url: "/time/checkSignIn",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            timeout: 30000,
            success: function(res) {
                if (res.success) {
                    $.toast("打卡成功", function() {
                        if (isWeChat()) {
                            window.location.replace('/time/timerecord?t=' + Date.now());
                        } else {
                            window.location.href = '/time/timerecord?t=' + Date.now();
                        }
                    });
                } else {
                    $('#returned').attr('disabled', false);
                    $.alert(res.msg || "打卡失败");
                }
            },
            error: function(xhr, status, error) {
                $('#returned').attr('disabled', false);

                if (status === 'timeout') {
                    $.alert("网络超时，请检查网络后重试");
                } else {
                    $.alert("网络错误，请重试");
                }
            },
            complete: function() {
                $.hideLoading();
            }
        });
    });

    // 通用转换函数
    function safeToDouble(value, defaultValue = 0) {
        if (value === undefined || value === null || value === '') {
            return defaultValue;
        }

        // 处理字符串类型的数字
        return value;
    }
});