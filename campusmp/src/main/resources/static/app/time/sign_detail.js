$(function () {
    // 0. 初始化变量
    var photoFile = null;
    var isProcessing = false;
    var latitude, longitude, accuracy;

    // 1. 设备性能检测
    function getDeviceProfile() {
        const ua = navigator.userAgent;
        return {
            isLowEnd: /Android [1-6]/.test(ua) ||
                (navigator.deviceMemory && navigator.deviceMemory < 2),
            isWeChat: /MicroMessenger/i.test(ua)
        };
    }

    // 2. 拍照按钮事件
    $('#uploadPhoto').click(function() {
        if (isProcessing) {
            $.toast("正在处理上一张照片，请稍候");
            return;
        }
        resetFileInput();
    });

    function resetFileInput() {
        const $input = $('#photoInput');
        $input.val('');
        setTimeout(() => $input.click(), 100);
    }

    // 3. 拍照后处理
    $('#photoInput').change(function(e) {
        if (!e.target.files[0] || isProcessing) return;

        const file = e.target.files[0];
        const device = getDeviceProfile();

        isProcessing = true;
        showProcessing('正在处理照片...');

        safelyProcessPhoto(file, device)
            .then(processedFile => {
                photoFile = processedFile;
                return showPhotoPreview(processedFile);
            })
            .catch(error => {
                console.error('照片处理错误:', error);
                handlePhotoError(error, file, device);
            })
            .finally(() => {
                isProcessing = false;
            });
    });

    // 4. 安全处理照片核心方法
    function safelyProcessPhoto(file, device) {
        return new Promise((resolve, reject) => {
            const settings = {
                maxSize: device.isLowEnd ? 1024 : 1600,
                quality: device.isLowEnd ? 0.6 : 0.8,
                timeout: device.isLowEnd ? 8000 : 12000
            };

            const timeout = setTimeout(() => {
                reject(new Error('PROCESS_TIMEOUT'));
            }, settings.timeout);

            const blobUrl = URL.createObjectURL(file);
            const img = new Image();

            img.onload = function() {
                try {
                    const ratio = Math.min(
                        settings.maxSize / img.width,
                        settings.maxSize / img.height
                    );

                    const canvas = document.createElement('canvas');
                    canvas.width = Math.floor(img.width * ratio);
                    canvas.height = Math.floor(img.height * ratio);

                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                    canvas.toBlob(blob => {
                        clearTimeout(timeout);
                        cleanupResources();

                        if (!blob) {
                            reject(new Error('BLOB_CONVERSION_FAILED'));
                            return;
                        }

                        resolve(new File([blob], "optimized.jpg", {
                            type: "image/jpeg",
                            lastModified: Date.now()
                        }));
                    }, 'image/jpeg', settings.quality);
                } catch (e) {
                    cleanupResources();
                    reject(new Error('MEMORY_ERROR'));
                }
            };

            img.onerror = () => {
                cleanupResources();
                reject(new Error('IMAGE_LOAD_ERROR'));
            };

            img.src = blobUrl;

            function cleanupResources() {
                URL.revokeObjectURL(blobUrl);
                img.src = '';
                if (img.complete) img.onload = null;
            }
        });
    }

    // 5. 显示处理状态
    function showProcessing(message) {
        $('#photoPreview').html(`
            <div class="photo-processing">
                <span class="processing-spinner"></span>
                <span>${message}</span>
            </div>
        `);
        $('#processedImage').hide();
    }

    // 6. 显示照片预览
    function showPhotoPreview(file) {
        return new Promise(resolve => {
            const url = URL.createObjectURL(file);
            const img = document.getElementById('processedImage');

            img.onload = function() {
                $('#photoPreview').empty();
                $(img).show();
                URL.revokeObjectURL(url);
                resolve();
            };

            img.onerror = function() {
                $('#photoPreview').html(`
                    <div class="photo-error">
                        照片加载失败，请重试
                    </div>
                `);
                URL.revokeObjectURL(url);
                resolve();
            };

            img.src = url;
        });
    }

    // 7. 错误处理
    function handlePhotoError(error, file, device) {
        console.error('Photo error:', error);

        const errorActions = {
            'MEMORY_ERROR': () => {
                $.toast('内存不足，尝试简化处理...');
                uploadOriginalFile(file);
            },
            'PROCESS_TIMEOUT': () => {
                $.toast('处理超时，上传原图');
                uploadOriginalFile(file);
            },
            'default': () => {
                $('#photoPreview').html(`
                    <div class="photo-error">
                        照片处理失败，请重试
                    </div>
                `);
                resetFileInput();
            }
        };

        (errorActions[error.message] || errorActions.default)();
    }

    // 8. 上传原图
    function uploadOriginalFile(file) {
        photoFile = file;
        showPhotoPreview(file);
    }

    // 9. 微信定位和打卡处理
    wx.config({
        debug: false,
        appId: $('#appId').val(),
        timestamp: parseInt($('#timeStamp').val()),
        nonceStr: $('#nonceStr').val(),
        signature: $('#signature').val(),
        jsApiList: ['getLocation']
    });

    wx.ready(function() {
        wx.getLocation({
            type: 'gcj02',
            success: function(res) {
                latitude = res.latitude;
                longitude = res.longitude;
                accuracy = res.accuracy;
            },
            fail: function(err) {
                console.error('定位失败:', err);
            }
        });
    });

    // 10. 打卡提交
    $('#returned').click(function() {
        if (!photoFile) {
            $.alert("请先拍摄照片");
            return;
        }
        var accuracyDouble = safeToDouble(accuracy);
        var longitudeDouble = safeToDouble(longitude);
        var latitudeDouble = safeToDouble(latitude);
        var formData = new FormData();
        formData.append('photoFile', photoFile);
        formData.append('image', $('#imgInfo').val());
        formData.append('uid', $('#uid').val());
        formData.append('accuracy', accuracyDouble);
        formData.append('longitude', longitudeDouble);
        formData.append('latitude', latitudeDouble);

        $.showLoading();
        $.ajax({
            url: "/time/checkSignIn",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            hideLoading: false,
            success: function(res) {
                if (res.success) {
                    setTimeout(function () {
                        $('#returned').attr("disabled","false");
                    }, 3000);
                    $.toast("打卡成功", function() {
                        window.location.href = '/time/timerecord?t=' + Date.now();
                    });
                } else {
                    $.alert(res.msg || "打卡失败");
                }
            },
            error: function() {
                $.alert("网络错误，请重试");
            },
            complete: function() {
                // $.hideLoading();
            }
        });
    });

    // 通用转换函数
    function safeToDouble(value, defaultValue = 0) {
        if (value === undefined || value === null || value === '') {
            return defaultValue;
        }

        // 处理字符串类型的数字
        return value;
    }
});