$(function () {
    $(document).ready(function () {
        let imgPath = $('#imgPath').val();
        if (!imgPath) return;

        let imgArray = imgPath.split(',');
        let $container = $('#imageContainer');

        // 清空容器，避免重复加载
        $container.empty();

        imgArray.forEach((src, index) => {
            if (src.includes("face")) {
                let imgHtml = `
                <div class="image-item">
                    <img src="${src}" class="preview-image" data-src="${src}">
                </div>`;
                $container.append(imgHtml);
            }
        });

        // 监听点击事件，打开预览
        $(document).on("click", ".preview-image", function () {
            let src = $(this).data("src");
            $("#galleryImg").css("background-image", `url(${src})`);
            $("#gallery").fadeIn(200);
        });

        // 关闭预览
        $("#gallery, #closeGallery").on("click", function () {
            $("#gallery").fadeOut(200);
        });
    });


    var detailed = function () {
        var uid = $('#uid').val()
        $.postAjax({
            url: "/time/getAbsenceRecordDetails",
            data: {
                uid: uid
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.data && result.data.length > 0) {
                    var item = result.data;
                    if (item) {
                        var auditrecord = item;

                        // for(var i = 0; i < booktimelist.length; i++){
                        //     var html = `<div class="weui-cell">
                        //                     <div class="weui-cell__bd">
                        //                         <p>${booktimelist[i].daily}</p>
                        //                     </div>
                        //                 </div>`;
                        //     $('#booktime').append(html);
                        // }

                        for(var j=0; j<auditrecord.length; j++){
                            var auditconditionHtml = ''
                            if(auditrecord[j].auditcondition == 0){
                                auditconditionHtml = "<lable style='color:red;'>(待审核)</lable>";
                            }else if(auditrecord[j].auditcondition == 1) {
                                auditconditionHtml = "<lable style='color:#0bb20c;'>(审核完成)</lable>";
                            }else if(auditrecord[j].auditcondition == 2) {
                                auditconditionHtml = "<lable style='color:red;'>(已取消)</lable>";
                            }else if(auditrecord[j].auditcondition == 3) {
                                auditconditionHtml = "<lable style='color:#0bb20c;'>(已恢复)</lable>";
                            }
                            if(!auditrecord[j].remark){
                                auditrecord[j].remark = '空';
                            }
                            if(!auditrecord[j].auditdate){
                                auditrecord[j].auditdate = '未审核';
                            }
                            var html = '<div class="weui-media-box weui-media-box_text">';
                            html += '<h4 class="weui-media-box__title">' + auditrecord[j].name + auditconditionHtml + '</h4>';
                            html += '<p class="weui-media-box__desc">审核备注：' + auditrecord[j].remark + '</p>';
                            html += '<ul class="weui-media-box__info">';
                            html += '<li class="weui-media-box__info__meta">审核时间：</li>';
                            html += '<li class="weui-media-box__info__meta">' + auditrecord[j].auditdate + '</li>';
                            if (auditrecord[j].currentauditnodelevel) {
                                html += '<li class="weui-media-box__info__meta weui-media-box__info__meta_extra">审核等级：' + auditrecord[j].currentauditnodelevel + '级</li>';
                            }
                            html += '</ul>';
                            html += '</div>';
                            $('#auditRecord').append(html);

                        }
                    }
                }
            }
        });
    }
    detailed();

    $('#cancel').click(function () {
        $.confirm("确认取消请假申请！","取消申请",function () {
        var uid = $('#uid').val();
        $.postAjax({
            url: "/time/cancelAbsence",
            data: {
                uid: uid
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if (result.success) {
                        $.toast("取消成功", function () {
                            location.reload();
                            // $(location).attr('href', '/time/absenceRecord?&time=' + ((new Date()).getTime()));
                        });
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }else {
                    $.alert(result.msg, "系统提示");
                }
            }
        });
        })
    });


    // $('#approvalrecord').click(function () {
    //     var uid = $('#uid').val();
    //     $(location).attr('href', '/askforleave/approvalRecord?formid=' + uid + '&time=' + ((new Date()).getTime()));
    // });


});