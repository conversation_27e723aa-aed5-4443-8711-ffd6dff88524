$(function () {
    mp.setTitle('请假申请');


    let photoFiles = []; // 用于存储上传的图片文件

    // 图片上传功能
    $('#uploadBox').click(function () {
        const fileInput = $('<input type="file" multiple accept="image/*" style="display: none;">');
        fileInput.on('change', async function (e) {
            const files = e.target.files;
            if (files.length > 0) {
                for (const file of files) {
                    if (file.type.startsWith('image/')) {
                        try {
                            // 压缩图片
                            const compressedBlob = await compressImage(file);
                            const compressedFile = new File([compressedBlob], file.name, {
                                type: 'image/jpeg',
                            });

                            const reader = new FileReader();
                            reader.onload = function (e) {
                                const img = document.createElement('img');
                                img.src = e.target.result;
                                img.classList.add('preview-img');

                                const deleteBtn = document.createElement('div');
                                deleteBtn.classList.add('delete-btn');
                                deleteBtn.innerHTML = '×';
                                deleteBtn.onclick = function () {
                                    const index = photoFiles.findIndex(item => item.file === compressedFile);
                                    if (index !== -1) {
                                        photoFiles.splice(index, 1);
                                    }
                                    $(this).parent().remove();
                                };

                                const previewItem = document.createElement('div');
                                previewItem.classList.add('preview-item');
                                previewItem.appendChild(img);
                                previewItem.appendChild(deleteBtn);

                                $('#previewContainer').prepend(previewItem);
                            };
                            reader.readAsDataURL(compressedBlob);

                            // 将压缩后的文件存储到 photoFiles 中
                            photoFiles.push({ file: compressedFile });
                        } catch (error) {
                            console.error('图片压缩失败:', error);
                            $.toptip('图片压缩失败，请重试');
                        }
                    }
                }
            }
        });
        fileInput.click();
    });

    /**
     * 图片压缩函数
     * @param {File} file - 图片文件
     * @param {number} maxWidth - 最大宽度
     * @param {number} maxHeight - 最大高度
     * @param {number} quality - 压缩质量 (0.1 ~ 1)
     * @returns {Promise<Blob>} - 返回压缩后的 Blob 对象
     */
    function compressImage(file, maxWidth = 800, maxHeight = 800, quality = 0.7) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.src = URL.createObjectURL(file);
            img.onload = () => {
                const canvas = document.createElement('canvas');
                let width = img.width;
                let height = img.height;

                // 按比例缩放图片
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }

                canvas.width = width;
                canvas.height = height;

                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);

                // 将 canvas 转换为 Blob
                canvas.toBlob(
                    (blob) => {
                        if (blob) {
                            resolve(blob);
                        } else {
                            reject(new Error('图片压缩失败'));
                        }
                    },
                    'image/jpeg',
                    quality
                );
            };
            img.onerror = (error) => {
                reject(error);
            };
        });
    }
    // 图片预览功能
    $(document).on('click', '.preview-img', function () {
        const imgSrc = $(this).attr('src');
        const overlay = $('<div class="image-preview-overlay">');
        const img = $('<img class="preview-large">').attr('src', imgSrc);
        const closeBtn = $('<div class="close-btn">×</div>').click(function () {
            overlay.remove();
        });

        overlay.append(img).append(closeBtn).appendTo('body');
    });

    //
    // // 提交表单时的图片验证
    // $('#submit').click(function (e) {
    //     if (photoFiles.length === 0) {
    //         $.toptip("请至少上传一张照片");
    //         return;
    //     }
    // });

    // 是否续假选择逻辑
    $('#sfxvjia').change(function () {
        const isXvjia = $(this).val() === '1';
        if (isXvjia) {
            $('#xvjiaCell').show(); // 显示续假时长输入框
        } else {
            $('#xvjiaCell').hide(); // 隐藏续假时长输入框
            $('#xvjiaDay').val(''); // 清空续假时长输入框
        }
    });

    $(document).ready(function () {
        // 向后端发送请求获取请假类型数据
        $.ajax({
            url: '/time/getTypes', // 后端接口地址
            method: 'POST',
            async: false,
            dataType: 'json',
            success: function (res) {
                // 清空现有的选项（除了默认选项）
                $('#leaveschool').find('option:not(:first)').remove();

                // 动态添加选项
                res.data.forEach(function (item) {
                    $('#leaveschool').append(
                        `<option value="${item.value}">${item.name}</option>`
                    );
                });
            },
            error: function (error) {
                console.error('获取请假类型失败:', error);
            }
        });
    });

    $(document).ready(function () {
        // 向后端发送请求获取审核人类型数据
        $.ajax({
            url: '/time/getpsitoin', // 后端接口地址
            method: 'POST',
            async: false,
            dataType: 'json',
            success: function (res) {
                // 清空现有的选项（除了默认选项）
                $('#psitoin').find('option:not(:first)').remove();

                // 动态添加选项
                res.data.forEach(function (item) {
                    $('#psitoin').append(
                        `<option value="${item.name}">${item.name}</option>`
                    );
                });
            },
            error: function (error) {
                console.error('获取请假类型失败:', error);
            }
        });
    });

    // 获取元素引用
    const selectElement = document.getElementById('psitoin');
    const inputElement = document.getElementById('aduitname');
    const resultSelect = document.getElementById('resultSelect');

    // 防抖函数（300ms触发一次）
    function debounce(func, delay) {
        let timer;
        return function () {
            clearTimeout(timer);
            timer = setTimeout(() => {
                func.apply(this, arguments);
            }, delay);
        };
    }

    // 搜索处理函数
    function handleSearch() {
        const type = selectElement.value;
        const name = inputElement.value.trim();

        // 验证输入
        if (!type) {
            alert('请先选择审核人类型');
            return;
        }

        // 构建请求体
        const requestBody = {
            type: type,
            name: name
        };

        $.ajax({
            url: '/time/searchaduit', // 后端接口地址
            method: 'POST',
            async: false,
            dataType: 'json',
            data: requestBody,
            success: function (data) {
                // 清空之前的结果
                resultSelect.innerHTML = '<option value="">请选择审核人</option>';
                // 填充新的结果
                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.uid;
                    option.textContent = item.name.trim(); // 去除姓名首尾空格
                    resultSelect.appendChild(option);
                });
            },
            error: function (error) {
                console.error('请求错误:', error);
            }
        });
    }

    // 选择审核人类型时清空审核人姓名
    selectElement.addEventListener('change', function () {
        inputElement.value = '';
        resultSelect.innerHTML = '<option value="">请选择审核人</option>';
        handleSearch();
    });

    // 选择审核人后将姓名回写到输入框
    resultSelect.addEventListener('change', function () {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            inputElement.value = selectedOption.textContent.trim(); // 去除姓名首尾空格
        }
    });

    // 绑定事件
    inputElement.addEventListener('input', debounce(handleSearch, 300));
    selectElement.addEventListener('change', handleSearch);


    // if($('#usertype').val() == 2 || $('#usertype').val() == 3){
    //     var peoples = JSON.parse($('#peoples').val());
    //     var oldpeople = '';
    //     $('#people').select({
    //         title: "选择人员",
    //         items: peoples,
    //         onClose:function(d){
    //             if(d.data.values !== oldpeople){
    //                 $("#list").empty()
    //                 oldpeople = d.data.values;
    //                 // loadRecordList();
    //             }
    //         },
    //         onOpen: function(d){
    //             if(!peoples){
    //                 $.toptip('无可选人员');
    //             }
    //             oldpeople = d.data.values;
    //         }
    //     });
    // }

    $('#submit').click(function (e) {
        var starttime = $('#starttime').val();
        var endtime = $('#endtime').val();
        var reason = $('#reason').val();
        var leaveschool = $('#leaveschool').val();
        var sfxvjia = $('#sfxvjia').val();
        var xvjiaDay = $('#xvjiaDay').val();
        var qunianshu = $('#qunianshu').val();
        var bennianshu = $('#bennianshu').val();
        var timeUnit = $('#timeUnit').val();
        var applyDay = $('#applyDay').val();
        var infolabel = $('#psitoin').val();
        var aduituid = $('#resultSelect').val();
        var docid = $('#people').attr('data-values');

        if (!starttime || !endtime) {
            $.toptip("请填写请假开始时间与结束时间！");
            return;
        }
        if (!reason) {
            $.toptip("请填写原因备注！");
            return;
        }
        if (sfxvjia == 1) {
            if (!xvjiaDay) {
                $.toptip("请填写续假时长！");
                return;
            }
        }
        if (!leaveschool) {
            $.toptip("请选择请假类型！");
            return;
        }
        if (!bennianshu) {
            $.toptip("请填写本年超/缺勤班数！");
            return;
        }
        if (!qunianshu) {
            $.toptip("请填写历年超/缺勤班数！");
            return;
        }
        if (!applyDay) {
            $.toptip("请填写请假时长！");
            return;
        }
        if (photoFiles.length === 0) {
            $.toptip("请至少上传一张照片");
            return;
        }
        if (timeUnit == 'hour') {
            applyDay = (applyDay / 24).toFixed(2);
        }
        if (!docid) {
            $.toptip("请选择人员！");
            return;
        }
        if (!aduituid) {
            $.toptip("请选择审核人员！");
            return;
        }
        if (starttime > endtime) {
            $.toptip('开始时间不能大于结束时间');
            return;
        }
        if (new Date(starttime).format("yyyy-MM-dd") < new Date().format("yyyy-MM-dd")) {
            $.toptip('开始时间不能小于当天日期');
            return;
        }

        var formData = new FormData();
        photoFiles.forEach((item, index) => {
            formData.append(`photoFiles`, item.file); // 将图片文件添加到 FormData
        });
        formData.append('docid', docid);
        formData.append('starttime', starttime);
        formData.append('endtime', endtime);
        formData.append('des', reason);
        formData.append('type', leaveschool);
        formData.append('applyDay', applyDay);
        formData.append('infolabel', infolabel);
        formData.append('dUserId', aduituid);
        formData.append('sfxvjia', sfxvjia);
        formData.append('xvjiaDay', xvjiaDay);
        formData.append('qunianshu', qunianshu);
        formData.append('bennianshu', bennianshu);

        $.confirm("确认申请请假吗？", function () {
            $.showLoading('提交中...');
            $.ajax({
                url: "/time/absencefromdutyfrom",
                data: formData,
                type: "POST",
                processData: false,
                contentType: false,
                success: function (result, textStatus, jqXHR) {
                    $.hideLoading();
                    if (result.success) {
                        $.toast("申请成功,等待审核！", function () {
                            window.location.href = '/time/absencerecord?time=' + ((new Date()).getTime());
                        });
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }
            });
        });

        // 初始化请假类型选择
        $.ajax({
            url: '/time/getTypes',
            method: 'POST',
            dataType: 'json',
            success: function (res) {
                $('#leaveschool').empty().append(
                    $('<option>').prop('disabled', true).prop('selected', true).text('请选择请假类型'),
                    ...res.data.map(item =>
                        $('<option>').val(item.value).text(item.name)
                    )
                );
            },
            error: function (err) {
                console.error('加载请假类型失败:', err);
                $.toptip('加载类型失败，请刷新重试');
            }
        });

        // // 原有人员选择逻辑
        // if ($('#usertype').val() == 2 || $('#usertype').val() == 3) {
        //     const peoples = JSON.parse($('#peoples').val());
        //     $('#people').select({
        //         title: "选择人员",
        //         items: peoples,
        //         onChange: function(d) {
        //             console.log('Selected:', d);
        //         }
        //     });
        // }
    });

    // 小数验证函数
    function validateDecimal(input) {
        const value = input.value;
        if (!/^\d*\.?\d*$/.test(value)) {
            input.value = value.substring(0, value.length - 1);
        }
    }

});