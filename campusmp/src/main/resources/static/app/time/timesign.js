$(function () {
    // 设置页面标题
    mp.setTitle('签到');

    const signInCfg = JSON.parse($("#signInCfg").val());
    const statusText = document.getElementById('statusText');
    const checkInBtn = document.getElementById('checkInBtn');
    const recordsDiv = document.getElementById('records');

    const current = new Date();
    const currentTimeInSeconds = current.getHours() * 3600 + current.getMinutes() * 60 + current.getSeconds();

    // 格式化时间为秒数
    const getTimeInSeconds = (timeObj) => {
        const [hours, minutes] = timeObj.split(":").map(num => parseInt(num, 10));
        return hours * 3600 + minutes * 60;
    };

    // 判断当前时间是否在签到时间范围内
    const isWithinTimeRange = (startTimeInSeconds, endTimeInSeconds) => {
        return currentTimeInSeconds >= startTimeInSeconds && currentTimeInSeconds <= endTimeInSeconds;
    };

    let periodUid = '';
    let isWithinAnyTimeRange = false;

    // 生成签到时间段列表
    signInCfg.forEach(item => {
        const startTimeInSeconds = getTimeInSeconds(item.startTime);
        const endTimeInSeconds = getTimeInSeconds(item.endTime);
        const listItem = `<div class="weui-cells__title">${item.name}: ${item.startTime} - ${item.endTime}</div>`;
        $("#list").append(listItem);

        if (isWithinTimeRange(startTimeInSeconds, endTimeInSeconds)) {
            periodUid = item.uid;
            isWithinAnyTimeRange = true;
        }
    });

    // 请求当前签到状态
    $.postAjax({
        url: "/sign/checkSignIn",
        data: { periodUid, date: current },
        success: (result) => {
            if (result.success && result.data) {
                updateStatusTextAndButton(true, result.data[0].signin_time);
            } else {
                updateStatusTextAndButton(false);
            }
        }
    });

    // 更新签到按钮和状态文本
    const updateStatusTextAndButton = (hasCheckedIn, signInTime = null) => {
        if (hasCheckedIn) {
            statusText.innerText = '已签到';
            checkInBtn.classList.add('checked');
            checkInBtn.innerText = '已签到';
            appendSignInRecord(signInTime);
        } else {
            if (isWithinAnyTimeRange) {
                statusText.innerText = '未签到';
                checkInBtn.style.display = 'block';
                checkInBtn.addEventListener('click', handleSignInClick);
            } else {
                statusText.innerText = '不在签到时间范围内';
                checkInBtn.style.display = 'none';
            }
        }
    };

    // 点击签到按钮时的处理
    const handleSignInClick = () => {
        $.postAjax({
            url: "/sign/saveSignIn",
            data: { periodUid, date: current },
            success: (result) => {
                if (result.success) {
                    updateStatusTextAndButton(true, new Date().toLocaleString());
                    $.toast("签到成功");
                } else {
                    $.alert(result.msg, "系统提示");
                }
            }
        });
    };

    // 添加签到记录到页面
    const appendSignInRecord = (signInTime) => {
        const recordDiv = document.createElement('div');
        recordDiv.className = 'weui-cell record-item';
        recordDiv.innerHTML = `<div class="weui-cell__bd"><p>${signInTime}</p></div>`;
        recordsDiv.appendChild(recordDiv);
    };
});
