$(function () {

    var max = mp.pageSize;

    var loading = false;  //状态标记
    var StartOnAutoLoad = function () {
        $(document.body).infinite().on("infinite", function () {
            if (loading) return;
            loading = true;
            setTimeout(function () {
                loadRecordList();
            }, 500);   //模拟延迟
        });
    };

    // if ($('#usertype').val() == 2 || $('#usertype').val() == 3) {
    //     var peoples = JSON.parse($('#peoples').val());
    //     var oldpeople = '';
    //     $('#people').select({
    //         title: "选择人员",
    //         items: peoples,
    //         onClose: function (d) {
    //             if (d.data.values != oldpeople) {
    //                 $("#list").empty()
    //                 loadRecordList();
    //             }
    //         },
    //         onOpen: function (d) {
    //             if (!peoples) {
    //                 $.toptip('无可选人员');
    //             }
    //             oldpeople = d.data.values;
    //         }
    //     });
    // }

    var loadRecordList = function () {
        var starttime = $("#starttime").val();
        var endtime = $("#endtime").val();
        var docid = $('#people').attr('data-values');
        if (starttime) {
            starttime = starttime.replace('T', ' ');
        }
        if (endtime) {
            endtime = endtime.replace('T', ' ');
        }
        $.postAjax({
            url: "/time/getSigncardRecord",
            data: {
                'docid': docid,
                'start': $("#list a").length,
                'limit': max,
                'starttime': starttime,
                'endtime': endtime
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if (result.data.length > 0) {
                        $.each(result.data, function (i, item) {
                            if (!item.name) {
                                item.name = '';
                            }
                            var status = '';
                            if (item.status == 1) {
                                status = "<lable style='color:#0bb20c;'>同意</lable>";
                            } else if (item.status == -1) {
                                status = "<lable style='color:red;'>不同意</lable>";
                            } else if (item.status == 0) {
                                status = "<lable style='color:red;'>审核中</lable>";
                            } else if (item.status == 2) {
                                status = "<lable style='color:red;'>已取消</lable>";
                            }
                            $("#list").append("<a class='weui-cell weui-cell_access' href='javascript:;' data-uid='" + item.uid + "'>"
                                + "<div class='weui-cell__bd' style='font-size: 15px;'>缺勤日期：" + item.signtime + "</div>"
                                + "<div class='weui-cell__ft' >" + status + "</div>"
                                + "</a>")
                        });
                        $("#list a ").unbind();
                        $('#list a').click(function () {
                            $(location).attr('href', '/time/detailedSigncardRecord?uid=' + $(this).attr('data-uid') + '&time=' + ((new Date()).getTime()));
                        })
                        $('#loadmore').hide();
                        StartOnAutoLoad();
                    } else {
                        $.toptip('已无更多记录');
                        $('#loadmore').hide();
                        $(document.body).destroyInfinite();
                    }
                } else {
                    $.alert(result.msg, "系统提示");
                }
            },
            complete: function (jqXHR, textStatus) {
                loading = false;
            }
        });
    }

    loadRecordList();

    $("#select").click(function () {
        var startData = new Date($("#starttime").val());
        var endData = new Date($("#endtime").val());
        if (startData > endData) {
            $.toptip('起始日期不能大于结束日期');
            return;
        }
        $('#list').html('<div class="weui-panel__hd">缺卡记录</div>');
        loadRecordList();
    })

});
