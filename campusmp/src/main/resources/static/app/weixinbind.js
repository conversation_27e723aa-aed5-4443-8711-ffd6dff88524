$(function () {
    mp.setTitle('绑定微信');
    var required = $('#required').val();

    var typelist = []
    if($('[name="usertype"]').length > 0){
        $.each($('[name="usertype"]'), function (index, value) { 
            typelist.push(parseInt($(value).val()))
       });
    }else {
        typelist = [1,2,3,4];
    }
    // $(document).ready(function() {
    //     $('#relation').change(function() {
    //         var selectedOption = $(this).find('option:selected');
    //         var gender = selectedOption.val().split('|')[1]; // 获取选项的性别信息
    //         $('#gender').val(gender); // 将性别信息填充到隐藏的输入框中
    //     });
    // });

    if($('#bind').val()){
        if($('#bind').val() == 1){
            $('#user1').show();
        }else if($('#bind').val() == 2){
            $('#user2').show();
        }else if($('#bind').val() == 3){
            $('#user3').show();
        }else if($('#bind').val() == 4){
            $('#user4').show();
        }
    }else {
    	if(typelist.length>1){
    		 $('#choiceType').show();
    	}
        $('#usertype'+typelist[0]).attr('checked','checked');
        for(var y = 0; y < 5; y++){
            if(!typelist.includes(y)){
                $('label[for="usertype'+y+'"]').hide();
            }
        }
        $('#user'+typelist[0]).show();
    }

    $('#usertype1').change(function (e) {
        if (e.target.checked) {
            $('#user1').show();
            $('#user2').hide();
            $('#user3').hide();
            $('#user4').hide();
        }
    });

    $('#usertype2').change(function (e) {
        if (e.target.checked) {
            $('#user1').hide();
            $('#user2').show();
            $('#user3').hide();
            $('#user4').hide();
        }
    });
    $('#usertype3').change(function (e) {
        if (e.target.checked) {
            $('#user1').hide();
            $('#user2').hide();
            $('#user3').show();
            $('#user4').hide();
        }
    });
    $('#usertype4').change(function (e) {
        if (e.target.checked) {
            $('#user1').hide();
            $('#user2').hide();
            $('#user3').hide();
            $('#user4').show();
        }
    });
    $('#usertype0').change(function (e) {
        if (e.target.checked) {
            $('#user1').hide();
            $('#user2').hide();
            $('#user3').hide();
            $('#user4').hide();
        }
    });

    $("#user4_idtype").select({
        title: "选择证件类型",
        items: [
            {
                title: "身份证号",
                value: "1",
            },
            {
                title: "驾驶证",
                value: "2",
            },
            {
                title: "军官证",
                value: "3",
            },
            {
                title: "其他证件",
                value: "4",
            }
        ]
    });

    $("#user4_visitorsex").select({
        title: "选择性别",
        items: ["男", "女"]
    });

    $('#submit').click(function () {
        var usertype = 4;
        if($('#bind').val()){
            usertype = $('#bind').val();
        }else {
            usertype = $('input:radio[name=usertype]:checked').val();
        }
        var isVerificationCode = $('#isVerificationCode').val()
        var info = null;
        if (usertype == 1) {
            info = {
                name: $('#user1_name').val(),
                code: $('#user1_code').val(),
                pwd: $('#user1_pwd').val()
            };
            if (!info.name) {
                $.alert("请填写本人姓名", "系统提示");
                return;
            }
            if (!info.code) {
                $.alert("请填写本人学号", "系统提示");
                return;
            }
            if (!info.pwd) {
                $.alert("请填写密码", "系统提示");
                return;
            }
        } else if (usertype == 2) {
            if (required == 1) {
                info = {
                    name: $('#user2_name').val(),
                    code: $('#user2_code').val(),
                    pwd: $('#user2_pwd').val(),
                    // newPwd: $('#user2_newPwd').val(),
                    pName: $('#user2_pName').val(),
                    pMobile: $('#user2_pMobile').val(),
                    relation: $('#relation').val(),
                    sex: $('#gender').val()
                };
                if (!info.name) {
                    $.alert("请填写学生姓名", "系统提示");
                    return;
                }
                if (!info.code) {
                    $.alert("请填写学生学号", "系统提示");
                    return;
                }
                if (!info.pName) {
                    $.alert("请填写家长姓名", "系统提示");
                    return;
                }
                if (!info.pMobile) {
                    $.alert("请填写家长手机号", "系统提示");
                    return;
                }
                if (!info.pwd) {
                    $.alert("请填写旧密码", "系统提示");
                    return;
                }
                // if (!info.newPwd) {
                //     $.alert("请填写新密码", "系统提示");
                //     return;
                // }
            } else {
                info = {
                    name: $('#user2_name').val(),
                    code: $('#user2_code').val(),
                    pwd: $('#user2_pwd').val(),
                };
                if (!info.name) {
                    $.alert("请填写学生姓名", "系统提示");
                    return;
                }
                if (!info.code) {
                    $.alert("请填写学生学号", "系统提示");
                    return;
                }
                if (!info.pwd) {
                    $.alert("请填写旧密码", "系统提示");
                    return;
                }
            }
        } else if (usertype == 3) {
            info = {
                name: $('#user3_name').val(),
                code: $('#user3_code').val(),
                mobile: $('#user3_mobile_simple').val(),
                // mobile: $('#user3_mobile').val(),
                pwd: $('#user3_pwd').val(),
            };
            if (!info.name) {
                $.alert("请填写本人姓名", "系统提示");
                return;
            }
            if (!info.mobile) {
                $.alert("请填写本人手机号", "系统提示");
                return;
            }
            if (!info.pwd) {
                $.alert("请填写密码", "系统提示");
                return;
            }
            // if (!info.mobile && isVerificationCode == "1") {
            //     $.alert("请填写本人手机号", "系统提示");
            //     return;
            // }
            // if (!info.vcode && isVerificationCode == "1") {
            //     $.alert("请填写验证码", "系统提示");
            //     return;
            // }
        } else if (usertype == 4)
        {
            info = {
                name: $('#user4_name').val(),
                mobile: $('#user4_mobile').val(),
                visitorsex: $('#user4_visitorsex').val(),
                idtype: $('#user4_idtype').attr("data-values"),
                idcard: $('#user4_idcard').val(),
                company: $('#user4_company').val(),
                position: $('#user4_position').val(),
                department: $('#user4_department').val(),
                plateno: $('#user4_plateno').val()
            };
            if (!info.name) {
                $.alert("请填写本人姓名", "系统提示");
                return;
            }
            if (!info.mobile) {
                $.alert("请填写联系方式", "系统提示");
                return;
            }
            if (!info.company) {
                $.alert("请填写本人单位", "系统提示");
                return;
            }
            // if (!info.idtype) {
            //     $.alert("请选择证件类型", "系统提示");
            //     return;
            // }
            // if (!info.idcard) {
            //     $.alert("请填写证件号码", "系统提示");
            //     return;
            // }
            // if(info.idtype == 1){
            //     if(!mp.idcard(info.idcard)){
            //         $.alert("请正确填写身份证号码", "系统提示");
            //         return;
            //     }
            // }

        }
        if (info == null) {
            $.alert("身份类型错误", "系统提示");
            return;
        }
        info.usertype = usertype;
        info.openid = $('#weixinid').val();
        info.headimgurl = $('#headimgurl').val();
        info.nickname = $('#nickname').val();
        info.sex = $('#sex').val();

        if (usertype == 4) {
             $.postAjax({
                 url: "/weixin/weixinvisitorbind",
                 data: info,
                 success: function (result, textStatus, jqXHR) {
                     if (result.success) {
                         $.toast("绑定成功");
                         if ($('#backurl').val()) {
                             window.location.href = $('#backurl').val();
                         } else {
                             window.location.href = '/';
                         }
                     } else {
                         $.alert(result.msg, "系统提示");
                     }
                 }
             });
        }else if (usertype == 2) {
            if (required == 1) {
                $.postAjax({
                    url: "/weixin/WeiXinParentsBindIsOk",
                    data: info,
                    success: function (result, textStatus, jqXHR) {
                        if (result.success) {
                            $.toast("绑定成功");
                            if ($('#backurl').val()) {
                                window.location.href = $('#backurl').val();
                            } else {
                                window.location.href = '/';
                            }
                        } else {
                            $.alert(result.msg, "系统提示");
                        }
                    }
                });
            } else {
                $.postAjax({
                    url: "/weixin/weixinbind",
                    data: info,
                    success: function (result, textStatus, jqXHR) {
                        if (result.success) {
                            $.toast("绑定成功");
                            if ($('#backurl').val()) {
                                window.location.href = $('#backurl').val();
                            } else {
                                window.location.href = '/';
                            }
                        } else {
                            $.alert(result.msg, "系统提示");
                        }
                    }
                });
            }
        } else {
            $.postAjax({
                url: "/weixin/weixinbind",
                data: info,
                success: function (result, textStatus, jqXHR) {
                    if (result.success) {
                        $.toast("绑定成功");
                        if ($('#backurl').val()) {
                            window.location.href = $('#backurl').val();
                        } else {
                            window.location.href = '/';
                        }
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }
            });
        }
    });
});