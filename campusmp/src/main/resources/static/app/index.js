$(function () {
    var title = $("#title").val();
    // 设置标题
    mp.setTitle(title);
    var isAlertPassWord = $("#isAlertPassWord").val();
    // 判断值是否为0，如果是，显示 WeUI 顶部提示框
    if (isAlertPassWord == 0) {
        // 显示顶部提示框
        $.alert("请尽快修改密码", "系统提示", function () {
            window.location.href = '/ucenter/passwordAlter?&time=' + ((new Date()).getTime());
        });
    }
    //判断是否续费
    var servicePaid = $("#servicePaid").val();
    console.log('进入前' + servicePaid);
    if (servicePaid == 0) {
        console.log(servicePaid)
        // 显示顶部提示框
        $.alert("微信公众号已过期，请联系管理员续费！！！", "系统提示", function () {
            // 关闭微信内置浏览器中的当前窗口
            if (typeof WeixinJSBridge !== "undefined") {
                WeixinJSBridge.call('closeWindow');
            } else {
                // 如果 WeixinJSBridge 不存在，使用 window.close() 作为回退方案
                window.location.href = '/';
            }
        });
    }
});