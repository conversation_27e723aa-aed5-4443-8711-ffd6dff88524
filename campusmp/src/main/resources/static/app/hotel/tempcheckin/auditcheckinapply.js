$(function () {

    $('#agree').click(function () {
        var uid = $('#uid').val();
        var status = 1;
        var auditremark = $('#auditremark').val();
        apply(uid,status,auditremark);
    })

    $('#disagree').click(function () {
        var uid = $('#uid').val();
        var status = 2;
        var auditremark = $('#auditremark').val();
        apply(uid,status,auditremark);
    })

    var apply = function(uid,status,auditremark){
        $.postAjax({
            url: "/hotel/tempcheckin/auditHotelApplyForm",
            data:{
                uid: uid,
                status: status,
                auditremark: auditremark,
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    $.alert("操作成功！",function(){
                        window.location.href='/hotel/tempcheckin/auditcheckinapplyrecords?time=' + (new Date()).getTime();
                    });
                } else {
                    $.alert(result.msg, "系统提示");
                }
            }
        });
    }
});
