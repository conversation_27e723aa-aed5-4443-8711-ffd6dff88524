$(function () {
    $(document).ready(function () {
        let imgPath = $('#imgPath').val();
        if (!imgPath) return;

        let imgArray = imgPath.split(',');
        let $container = $('#imageContainer');

        // 清空容器，避免重复加载
        $container.empty();

        imgArray.forEach((src, index) => {
            if (src.includes("face")) {
                let imgHtml = `
                <div class="image-item">
                    <img src="${src}" class="preview-image" data-src="${src}">
                </div>`;
                $container.append(imgHtml);
            }
        });

        // 监听点击事件，打开预览
        $(document).on("click", ".preview-image", function () {
            let src = $(this).data("src");
            $("#galleryImg").css("background-image", `url(${src})`);
            $("#gallery").fadeIn(200);
        });

        // 关闭预览
        $("#gallery, #closeGallery").on("click", function () {
            $("#gallery").fadeOut(200);
        });
        var id = $('#uid').val();
        $.ajax({
            url: '/alleyway/getSelectDoors', // 后端接口地址
            method: 'POST',
            data: {
                uid: id
            },
            async: false,
            dataType: 'json',
            success: function (res) {
                // 清空现有的选项（除了默认选项）
                $('#door').find('option:not(:first)').remove();

                // 动态添加选项
                res.data.forEach(function (item) {
                    const option = `<option value="${item.id}" ${item.isSelected ? 'selected' : ''}>${item.name}</option>`;
                    $('#door').append(option);
                });
            },
            error: function (error) {
                console.error('获取门禁权限失败:', error);
            }
        });
    });

    // 1. 拉取所有设备
    $.ajax({
        url: '/alleyway/getAllElevatorDevices',
        method: 'POST',
        dataType: 'json',
        success: function(res) {
            if (res.success) {
                // 2. 拉取申请单详情
                $.ajax({
                    url: '/alleyway/getDoorRecordDetails',
                    data: { uid: $('#uid').val() },
                    method: 'POST',
                    dataType: 'json',
                    success: function(detailRes) {
                        // 3. 渲染设备和楼层，并根据 detailRes.data.devices 勾选
                        // 假设 detailRes.data.devices 是 [{deviceId, type, floors}]
                        renderElevatorDevices(res.data, detailRes.data.devices || []);
                    }
                });
            }
        }
    });

    var detailed = function () {
        var uid = $('#uid').val()
        $.postAjax({
            url: "/alleyway/getDoorRecordDetails",
            data: {
                uid: uid
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.data && result.data.length > 0) {
                    var item = result.data;
                    if (item) {
                        var auditrecord = item;

                        // for(var i = 0; i < booktimelist.length; i++){
                        //     var html = `<div class="weui-cell">
                        //                     <div class="weui-cell__bd">
                        //                         <p>${booktimelist[i].daily}</p>
                        //                     </div>
                        //                 </div>`;
                        //     $('#booktime').append(html);
                        // }

                        for(var j=0; j<auditrecord.length; j++){
                            var auditconditionHtml = ''
                            if(auditrecord[j].auditcondition == 0){
                                auditconditionHtml = "<lable style='color:red;'>(待审核)</lable>";
                            }else if(auditrecord[j].auditcondition == 1) {
                                auditconditionHtml = "<lable style='color:#0bb20c;'>(审核完成)</lable>";
                            }else if(auditrecord[j].auditcondition == 2) {
                                auditconditionHtml = "<lable style='color:red;'>(已取消)</lable>";
                            }else if(auditrecord[j].auditcondition == 3) {
                                auditconditionHtml = "<lable style='color:#0bb20c;'>(已恢复)</lable>";
                            }
                            if(!auditrecord[j].remark){
                                auditrecord[j].remark = '空';
                            }
                            if(!auditrecord[j].auditdate){
                                auditrecord[j].auditdate = '未审核';
                            }
                            var html = '<div class="weui-media-box weui-media-box_text">';
                            html += '<h4 class="weui-media-box__title">' + auditrecord[j].name + auditconditionHtml + '</h4>';
                            html += '<p class="weui-media-box__desc">审核备注：' + auditrecord[j].remark + '</p>';
                            html += '<ul class="weui-media-box__info">';
                            html += '<li class="weui-media-box__info__meta">审核时间：</li>';
                            html += '<li class="weui-media-box__info__meta">' + auditrecord[j].auditdate + '</li>';
                            if (auditrecord[j].currentauditnodelevel) {
                                html += '<li class="weui-media-box__info__meta weui-media-box__info__meta_extra">审核等级：' + auditrecord[j].currentauditnodelevel + '级</li>';
                            }
                            html += '</ul>';
                            html += '</div>';
                            $('#auditRecord').append(html);

                        }
                    }
                }
            }
        });
    }
    detailed();

    var auditremark = '';
    var infoid = $('#infoid').val();


    $('#bagree').click(function () {
        auditremark = $('#auditremark').val();
        auditLeave(1);
    });

    $('#bdisagree').click(function () {
        auditremark = $('#auditremark').val();
        auditLeave(-1);
    });

    var uid = $('#uid').val();



    var auditLeave = function (status) {
        $.postAjax({
            url: "/alleyway/auditdoor",
            data: {
                uid: uid,
                infoid: infoid,
                status: status,
                remark: auditremark,
                dUserId: $('#resultSelect').val(),
                devices: collectDevicesFromUI()
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if (result.success) {
                        $.toast("审核成功", function () {
                            window.location.refresh();
                        });
                    }
                }else {
                    $.alert(result.msg, "系统提示");
                }
            }
        });
    }


    $('#save').click(function () {
        saveabsence();
    });

    var saveabsence = function () {
        var starttime = $('#starttime').val();
        var endtime = $('#endtime').val();
        var password = $('#password').val();
        var usage_count = $('#usage_count').val();
        const selectElement = document.getElementById('door');
        const selectedOptions = Array.from(selectElement.selectedOptions);
        const selectedValues = selectedOptions.map(option => option.value);
        const devids = selectedValues.join(',');
        let devices = collectDevicesFromUI();
        $.postAjax({
            url: "/alleyway/savedoorfrom",
            data: {
                uid: $('#uid').val(),
                starttime: starttime,
                endtime: endtime,
                password: password,
                usagecount: usage_count,
                devids: devids,
                devices: devices
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    $.toast("修改成功", function () {
                        window.location.refresh();
                    });
                } else {
                    $.alert(result.msg, "系统提示");
                }
            }
        });
    }

    // $(document).ready(function () {
    //     // 向后端发送请求获取审核人类型数据
    //     $.ajax({
    //         url: '/time/getpsitoin', // 后端接口地址
    //         method: 'POST',
    //         async: false,
    //         dataType: 'json',
    //         success: function (res) {
    //             // 清空现有的选项（除了默认选项）
    //             $('#psitoin').find('option:not(:first)').remove();
    //
    //             // 动态添加选项
    //             res.data.forEach(function (item) {
    //                 $('#psitoin').append(
    //                     `<option value="${item.name}">${item.name}</option>`
    //                 );
    //             });
    //         },
    //         error: function (error) {
    //             console.error('获取请假类型失败:', error);
    //         }
    //     });
    // });

    // // 获取元素引用
    // const selectElement = document.getElementById('psitoin');
    // const inputElement = document.getElementById('aduitname');
    // const resultSelect = document.getElementById('resultSelect');
    //
    // // 防抖函数（300ms触发一次）
    // function debounce(func, delay) {
    //     let timer;
    //     return function () {
    //         clearTimeout(timer);
    //         timer = setTimeout(() => {
    //             func.apply(this, arguments);
    //         }, delay);
    //     };
    // }

    // 搜索处理函数
    // function handleSearch() {
    //     const type = selectElement.value;
    //     const name = inputElement.value.trim();
    //
    //     // 验证输入
    //     if (!type) {
    //         alert('请先选择审核人类型');
    //         return;
    //     }
    //
    //     // 构建请求体
    //     const requestBody = {
    //         type: type,
    //         name: name
    //     };
    //
    //     $.ajax({
    //         url: '/time/searchaduit', // 后端接口地址
    //         method: 'POST',
    //         async: false,
    //         dataType: 'json',
    //         data: requestBody,
    //         success: function (data) {
    //             // 清空之前的结果
    //             resultSelect.innerHTML = '<option value="">请选择审核人</option>';
    //             // 填充新的结果
    //             data.data.forEach(item => {
    //                 const option = document.createElement('option');
    //                 option.value = item.uid;
    //                 option.textContent = item.name.trim(); // 去除姓名首尾空格
    //                 resultSelect.appendChild(option);
    //             });
    //         },
    //         error: function (error) {
    //             console.error('请求错误:', error);
    //         }
    //     });
    // }
    //
    // // 选择审核人类型时清空审核人姓名
    // selectElement.addEventListener('change', function () {
    //     inputElement.value = '';
    //     resultSelect.innerHTML = '<option value="">请选择审核人</option>';
    //     handleSearch();
    // });
    //
    // // 选择审核人后将姓名回写到输入框
    // resultSelect.addEventListener('change', function () {
    //     const selectedOption = this.options[this.selectedIndex];
    //     if (selectedOption.value) {
    //         inputElement.value = selectedOption.textContent.trim(); // 去除姓名首尾空格
    //     }
    // });
    //
    // // 绑定事件
    // inputElement.addEventListener('input', debounce(handleSearch, 300));
    // selectElement.addEventListener('change', handleSearch);


    // if($('#usertype').val() == 2 || $('#usertype').val() == 3){
    //     var peoples = JSON.parse($('#peoples').val());
    //     var oldpeople = '';
    //     $('#people').select({
    //         title: "选择人员",
    //         items: peoples,
    //         onClose:function(d){
    //             if(d.data.values !== oldpeople){
    //                 $("#list").empty()
    //                 oldpeople = d.data.values;
    //                 // loadRecordList();
    //             }
    //         },
    //         onOpen: function(d){
    //             if(!peoples){
    //                 $.toptip('无可选人员');
    //             }
    //             oldpeople = d.data.values;
    //         }
    //     });
    // }

    // 小数验证函数
    function validateDecimal(input) {
        const value = input.value;
        if (!/^\d*\.?\d*$/.test(value)) {
            input.value = value.substring(0, value.length - 1);
        }
    }

    function getFloorBits(selectedFloors, maxFloor = 64) {
        let bits = Array(maxFloor).fill(0);
        selectedFloors.forEach(id => {
            if (id >= 1 && id <= maxFloor) {
                bits[id - 1] = 1;
            }
        });
        return bits.join(',');
    }

    function renderElevatorDevices(devices, selectedDevices) {
        // selectedDevices: [{deviceId, type, floors}]，floors为bits字符串
        $('#elevatorDeviceContainer').empty();
        devices.forEach(device => {
            const card = $(`<div class="elevator-card" data-uid="${device.uid}"></div>`);
            card.append(`
                <div class="elevator-title">
                    <span class="elevator-icon"><i class="fa-solid fa-elevator"></i></span>
                    ${device.devname} <span class="elevator-type">(梯控设备)</span>
                </div>
            `);
            const floorList = $('<div class="floor-list-modern"></div>');
            const batchBar = $(`
                <div class="batch-bar">
                    <button type="button" class="batch-btn" data-type="all"><i class="fa fa-check-square"></i> 全选</button>
                    <button type="button" class="batch-btn" data-type="none"><i class="fa fa-square"></i> 清空</button>
                    <button type="button" class="batch-btn" data-type="reverse"><i class="fa fa-random"></i> 反选</button>
                    <button type="button" class="batch-btn" data-type="expand"><i class="fa fa-chevron-down"></i> 展开</button>
                </div>
            `);
            floorList.append(batchBar);

            // 拉取楼层
            $.ajax({
                url: '/alleyway/getElevatorFloors',
                data: { deviceId: device.uid,uid:$('#uid').val() },
                method: 'POST',
                dataType: 'json',
                success: function(res) {
                    if (res.success && res.data && res.data.length > 0) {
                        const colCount = res.data.length > 20 ? 4 : (res.data.length > 10 ? 3 : 2);
                        const perCol = Math.ceil(res.data.length / colCount);
                        let columns = Array.from({length: colCount}, () => $('<div style="display:flex;flex-direction:column;flex:1 1 0;"></div>'));
                        let isExpanded = false;
                        let allLabels = [];
                        // 查找该设备是否有已选
                        let selected = selectedDevices && selectedDevices.find(d => d.deviceId == device.uid);
                        let selectedFloors = [];
                        if (selected && selected.floors) {
                            // bits字符串转数组
                            selectedFloors = selected.floors.split(',').map((v, idx) => v === '1' ? idx + 1 : null).filter(v => v);
                        }
                        res.data.forEach((floor, idx) => {
                            const checked = floor.selected ? 'checked' : '';
                            const label = $(`
                                <label class="floor-checkbox" style="display:${idx < 8 ? 'flex' : 'none'};" data-floor-idx="${idx}">
                                    <input type="checkbox" value="${floor.id}" data-floorname="${floor.name}" ${checked} />
                                    <span class="custom-checkbox"></span>
                                    <span class="floor-label">${floor.name}</span>
                                </label>
                            `);
                            allLabels.push(label);
                            columns[Math.floor(idx / perCol)].append(label);
                        });
                        columns.forEach(col => floorList.append(col));
                        // 批量操作事件
                        floorList.on('click', '.batch-btn', function() {
                            const type = $(this).data('type');
                            const checkboxes = floorList.find('input[type="checkbox"]');
                            if (type === 'all') {
                                checkboxes.prop('checked', true);
                            } else if (type === 'none') {
                                checkboxes.prop('checked', false);
                            } else if (type === 'reverse') {
                                checkboxes.each(function() {
                                    $(this).prop('checked', !$(this).prop('checked'));
                                });
                            } else if (type === 'expand') {
                                if (!isExpanded) {
                                    allLabels.forEach(lab => lab.show());
                                    $(this).text('收起');
                                    isExpanded = true;
                                } else {
                                    allLabels.forEach((lab, idx) => lab.toggle(idx < 8));
                                    $(this).text('展开');
                                    isExpanded = false;
                                }
                            }
                        });
                    } else {
                        floorList.append('<div style="color:#999;padding:8px 0;">无楼层数据</div>');
                    }
                },
                error: function() {
                    floorList.append('<div style="color:red;padding:8px 0;">楼层加载失败</div>');
                }
            });
            card.append(floorList);
            $('#elevatorDeviceContainer').append(card);
        });
    }

    function collectDevicesFromUI() {
        let devices = [];
        $('.elevator-card').each(function() {
            const deviceId = $(this).data('uid');
            const type = '12';
            const selectedFloors = [];
            $(this).find('input[type=checkbox]:checked').each(function() {
                selectedFloors.push(Number($(this).val()));
            });
            let floorBits = null;
            if (selectedFloors.length > 0) {
                floorBits = getFloorBits(selectedFloors, 64);
            }
            devices.push({
                deviceId: deviceId,
                type: type,
                floors: floorBits
            });
        });
        return JSON.stringify(devices);
    }
});
