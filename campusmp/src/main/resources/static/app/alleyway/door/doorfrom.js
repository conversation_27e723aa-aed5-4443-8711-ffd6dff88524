$(function () {
    mp.setTitle('开门申请');


    let photoFiles = []; // 用于存储上传的图片文件

    // 图片上传功能
    $('#uploadBox').click(function () {
        const fileInput = $('<input type="file" multiple accept="image/*" style="display: none;">');
        fileInput.on('change', async function (e) {
            const files = e.target.files;
            if (files.length > 0) {
                for (const file of files) {
                    if (file.type.startsWith('image/')) {
                        try {
                            // 压缩图片
                            const compressedBlob = await compressImage(file);
                            const compressedFile = new File([compressedBlob], file.name, {
                                type: 'image/jpeg',
                            });

                            const reader = new FileReader();
                            reader.onload = function (e) {
                                const img = document.createElement('img');
                                img.src = e.target.result;
                                img.classList.add('preview-img');

                                const deleteBtn = document.createElement('div');
                                deleteBtn.classList.add('delete-btn');
                                deleteBtn.innerHTML = '×';
                                deleteBtn.onclick = function () {
                                    const index = photoFiles.findIndex(item => item.file === compressedFile);
                                    if (index !== -1) {
                                        photoFiles.splice(index, 1);
                                    }
                                    $(this).parent().remove();
                                };

                                const previewItem = document.createElement('div');
                                previewItem.classList.add('preview-item');
                                previewItem.appendChild(img);
                                previewItem.appendChild(deleteBtn);

                                $('#previewContainer').prepend(previewItem);
                            };
                            reader.readAsDataURL(compressedBlob);

                            // 将压缩后的文件存储到 photoFiles 中
                            photoFiles.push({ file: compressedFile });
                        } catch (error) {
                            console.error('图片压缩失败:', error);
                            $.toptip('图片压缩失败，请重试');
                        }
                    }
                }
            }
        });
        fileInput.click();
    });

    /**
     * 图片压缩函数
     * @param {File} file - 图片文件
     * @param {number} maxWidth - 最大宽度
     * @param {number} maxHeight - 最大高度
     * @param {number} quality - 压缩质量 (0.1 ~ 1)
     * @returns {Promise<Blob>} - 返回压缩后的 Blob 对象
     */
    function compressImage(file, maxWidth = 800, maxHeight = 800, quality = 0.7) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.src = URL.createObjectURL(file);
            img.onload = () => {
                const canvas = document.createElement('canvas');
                let width = img.width;
                let height = img.height;

                // 按比例缩放图片
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }

                canvas.width = width;
                canvas.height = height;

                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);

                // 将 canvas 转换为 Blob
                canvas.toBlob(
                    (blob) => {
                        if (blob) {
                            resolve(blob);
                        } else {
                            reject(new Error('图片压缩失败'));
                        }
                    },
                    'image/jpeg',
                    quality
                );
            };
            img.onerror = (error) => {
                reject(error);
            };
        });
    }
    // 图片预览功能
    $(document).on('click', '.preview-img', function () {
        const imgSrc = $(this).attr('src');
        const overlay = $('<div class="image-preview-overlay">');
        const img = $('<img class="preview-large">').attr('src', imgSrc);
        const closeBtn = $('<div class="close-btn">×</div>').click(function () {
            overlay.remove();
        });

        overlay.append(img).append(closeBtn).appendTo('body');
    });

    //
    // // 提交表单时的图片验证
    // $('#submit').click(function (e) {
    //     if (photoFiles.length === 0) {
    //         $.toptip("请至少上传一张照片");
    //         return;
    //     }
    // });

    // // 是否续假选择逻辑
    // $('#sfxvjia').change(function () {
    //     const isXvjia = $(this).val() === '1';
    //     if (isXvjia) {
    //         $('#xvjiaCell').show(); // 显示续假时长输入框
    //     } else {
    //         $('#xvjiaCell').hide(); // 隐藏续假时长输入框
    //         $('#xvjiaDay').val(''); // 清空续假时长输入框
    //     }
    // });

    $(document).ready(function () {
        // 向后端发送请求获取门禁数据
        $.ajax({
            url: '/alleyway/getDoors', // 后端接口地址
            method: 'POST',
            async: false,
            dataType: 'json',
            success: function (res) {
                // 清空现有的选项（除了默认选项）
                $('#door').find('option:not(:first)').remove();

                // 动态添加选项
                res.data.forEach(function (item) {
                    $('#door').append(
                        `<option value="${item.id}">${item.name}</option>`
                    );
                });
            },
            error: function (error) {
                console.error('获取门禁权限失败:', error);
            }
        });
    });

    $(document).ready(function () {
        var deptcode = $('#orgcode').val(); // 或 window.deptcode
        loadAuditorList(deptcode);
    });

    $('#submit').click(function (e) {
        var starttime = $('#starttime').val();
        var endtime = $('#endtime').val();
        var reason = $('#reason').val();
        // var deptcode = $('#usernamedept').val();
        var count = $('#usage_count').val();
        var password = $('#password').val();
        // var infolabel = $('#psitoin').val();
        var docid = $('#people').attr('data-values');
        const selectElement = document.getElementById('door');
        const selectedOptions = Array.from(selectElement.selectedOptions);
        const selectedValues = selectedOptions.map(option => option.value);
        const devids = selectedValues.join(',');
        var aduituid = $('input[name="auditorRadio"]:checked').val();

        if (!starttime) {
            $.toptip("请填写开门开始时间！");
            return;
        }

        if (!endtime) {
            $.toptip("请填写开门结束时间！");
            return;
        }
        if (!reason) {
            $.toptip("请填写申请原因！");
            return;
        }
        if (!count) {
            $.toptip("请填写开门次数！");
            return;
        }
        if (photoFiles.length === 0) {
            $.toptip("请至少上传一张照片");
            return;
        }
        if (!docid) {
            $.toptip("请选择人员！");
            return;
        }
        if (!aduituid) {
            $.toptip("请选择审核人员！");
            return;
        }
        if (new Date(starttime).format("yyyy-MM-dd") < new Date().format("yyyy-MM-dd")) {
            $.toptip('开始时间不能小于当天日期');
            return;
        }

        // 梯控设备收集
        let devices = [];
        $('.elevator-card').each(function() {
            const deviceId = $(this).data('uid');
            const type = '12';
            const selectedFloors = [];
            $(this).find('input[type=checkbox]:checked').each(function() {
                selectedFloors.push(Number($(this).val()));
            });
            if (selectedFloors.length > 0) {
                let floorBits = getFloorBits(selectedFloors, 64);
                devices.push({
                    deviceId: deviceId,
                    type: type,
                    floors: floorBits
                });
            }
        });

        // 必须门禁或梯控至少选一个
        if (!devids && devices.length === 0) {
            $.toptip("请至少选择一个门禁权限或梯控楼层！");
            return;
        }

        var formData = new FormData();
        photoFiles.forEach((item, index) => {
            formData.append(`photoFiles`, item.file); // 将图片文件添加到 FormData
        });
        formData.append('docid', docid);
        // formData.append('deptcode', deptcode);
        formData.append('starttime', starttime);
        formData.append('endtime', endtime);
        formData.append('des', reason);
        formData.append('devids', devids);
        formData.append('count', count);
        formData.append('password', password);

        formData.append('dUserId', aduituid);

        formData.append('devices', JSON.stringify(devices));

        $.confirm("确认申请开门吗？", function () {
            $.showLoading('提交中...');
            $.ajax({
                url: "/alleyway/saveDoorFrom",
                data: formData,
                type: "POST",
                processData: false,
                contentType: false,
                success: function (result, textStatus, jqXHR) {
                    $.hideLoading();
                    if (result.success) {
                        $.toast("申请成功,等待审核！", function () {
                            window.location.href = '/alleyway/doorfromrecord?time=' + ((new Date()).getTime());
                        });
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }
            });
        });

        // // 初始化请假类型选择
        // $.ajax({
        //     url: '/time/getTypes',
        //     method: 'POST',
        //     dataType: 'json',
        //     success: function (res) {
        //         $('#leaveschool').empty().append(
        //             $('<option>').prop('disabled', true).prop('selected', true).text('请选择请假类型'),
        //             ...res.data.map(item =>
        //                 $('<option>').val(item.value).text(item.name)
        //             )
        //         );
        //     },
        //     error: function (err) {
        //         console.error('加载请假类型失败:', err);
        //         $.toptip('加载类型失败，请刷新重试');
        //     }
        // });

        // // 原有人员选择逻辑
        // if ($('#usertype').val() == 2 || $('#usertype').val() == 3) {
        //     const peoples = JSON.parse($('#peoples').val());
        //     $('#people').select({
        //         title: "选择人员",
        //         items: peoples,
        //         onChange: function(d) {
        //             console.log('Selected:', d);
        //         }
        //     });
        // }
    });

    // 页面初始化时
    $.ajax({
        url: '/alleyway/getAllElevatorDevices',
        method: 'POST',
        dataType: 'json',
        success: function(res) {
            if (res.success) {
                renderElevatorDevices(res.data);
            }
        }
    });

});

function renderElevatorDevices(devices) {
    $('#elevatorDeviceContainer').empty();
    devices.forEach(device => {
        const card = $(`<div class="elevator-card" data-uid="${device.uid}"></div>`);
        card.append(`
            <div class="elevator-title">
                <span class="elevator-icon"><i class="fa-solid fa-elevator"></i></span>
                ${device.devname} <span class="elevator-type">(梯控设备)</span>
            </div>
        `);
        const floorList = $('<div class="floor-list-modern"></div>');
        // 批量操作区
        const batchBar = $(`
            <div class="batch-bar">
                <button type="button" class="batch-btn" data-type="all"><i class="fa fa-check-square"></i> 全选</button>
                <button type="button" class="batch-btn" data-type="none"><i class="fa fa-square"></i> 清空</button>
                <button type="button" class="batch-btn" data-type="reverse"><i class="fa fa-random"></i> 反选</button>
                <button type="button" class="batch-btn" data-type="expand"><i class="fa fa-chevron-down"></i> 展开</button>
            </div>
        `);
        floorList.append(batchBar);

        // 拉取楼层
        $.ajax({
            url: '/visitor/getElevatorFloors',
            data: { deviceId: device.uid, infoType: 1 },
            method: 'POST',
            dataType: 'json',
            success: function(res) {
                if (res.success && res.data && res.data.length > 0) {
                    // 多列分组
                    const colCount = res.data.length > 20 ? 4 : (res.data.length > 10 ? 3 : 2);
                    const perCol = Math.ceil(res.data.length / colCount);
                    let columns = Array.from({length: colCount}, () => $('<div style="display:flex;flex-direction:column;flex:1 1 0;"></div>'));
                    // 默认只显示前8层
                    let isExpanded = false;
                    let allLabels = [];
                    res.data.forEach((floor, idx) => {
                        const label = $(`
                            <label class="floor-checkbox" style="display:${idx < 8 ? 'flex' : 'none'};" data-floor-idx="${idx}">
                                <input type="checkbox" value="${floor.id}" data-floorname="${floor.name}" />
                                <span class="custom-checkbox"></span>
                                <span class="floor-label">${floor.name}</span>
                            </label>
                        `);
                        allLabels.push(label);
                        columns[Math.floor(idx / perCol)].append(label);
                    });
                    columns.forEach(col => floorList.append(col));

                    // 批量操作事件
                    floorList.on('click', '.batch-btn', function() {
                        const type = $(this).data('type');
                        const checkboxes = floorList.find('input[type="checkbox"]');
                        if (type === 'all') {
                            checkboxes.prop('checked', true);
                        } else if (type === 'none') {
                            checkboxes.prop('checked', false);
                        } else if (type === 'reverse') {
                            checkboxes.each(function() {
                                $(this).prop('checked', !$(this).prop('checked'));
                            });
                        } else if (type === 'expand') {
                            if (!isExpanded) {
                                allLabels.forEach(lab => lab.show());
                                $(this).text('收起');
                                isExpanded = true;
                            } else {
                                allLabels.forEach((lab, idx) => lab.toggle(idx < 8));
                                $(this).text('展开');
                                isExpanded = false;
                            }
                        }
                    });
                } else {
                    floorList.append('<div style="color:#999;padding:8px 0;">无楼层数据</div>');
                }
            },
            error: function() {
                floorList.append('<div style="color:red;padding:8px 0;">楼层加载失败</div>');
            }
        });
        card.append(floorList);
        $('#elevatorDeviceContainer').append(card);
    });
}

function getFloorBits(selectedFloors, maxFloor = 64) {
    let bits = Array(maxFloor).fill(0);
    selectedFloors.forEach(id => {
        if (id >= 1 && id <= maxFloor) {
            bits[id - 1] = 1;
        }
    });
    return bits.join(',');
}

function loadAuditorList(deptcode) {
    if (!deptcode) {
        $('#auditorList').html('<div>未获取到部门信息</div>');
        return;
    }
    $.ajax({
        url: '/alleyway/getDepartmentPerson',
        method: 'POST',
        data: { deptcode: deptcode },
        dataType: 'json',
        success: function (res) {
            if (res.success && res.data && res.data.length > 0) {
                let html = '';
                res.data.forEach(function (auditor, idx) {
                    html += `
                        <label class="auditor-radio-label">
                            <input type="radio" name="auditorRadio" value="${auditor.infoId}" ${idx === 0 ? 'checked' : ''}>
                            <span class="auditor-name">${auditor.name}</span>
                            <span class="auditor-code">(${auditor.code})</span>
                        </label>
                    `;
                });
                $('#auditorList').html(html);
            } else {
                $('#auditorList').html('<div>暂无审核人</div>');
            }
        },
        error: function () {
            $('#auditorList').html('<div>获取审核人失败</div>');
        }
    });
}