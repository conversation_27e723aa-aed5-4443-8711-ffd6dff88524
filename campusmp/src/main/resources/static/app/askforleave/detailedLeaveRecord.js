$(function () {
    $('#cancel').click(function () {
        $.confirm("确认取消请假！","取消请假",function () {
        var uid = $('#uid').val();
        $.postAjax({
            url: "/askforleave/cancelLeave",
            data: {
                uid: uid
            },
            hideLoading: false,
            success: function (result, textStatus, jqXHR) {
                if (result.success) {
                    if (result.success) {
                        $.toast("取消成功", function () {
                            $(location).attr('href', '/askforleave/leaverecord?uid=' + $(this).attr('data-uid') + '&time=' + ((new Date()).getTime()));
                        });
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }
            }
        });
        })
    });


    // $('#approvalrecord').click(function () {
    //     var uid = $('#uid').val();
    //     $(location).attr('href', '/askforleave/approvalRecord?formid=' + uid + '&time=' + ((new Date()).getTime()));
    // });


});