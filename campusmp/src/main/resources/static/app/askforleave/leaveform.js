$(function () {
    mp.setTitle('请假申请');


    if($('#usertype').val() == 2 || $('#usertype').val() == 3){
        var peoples = JSON.parse($('#peoples').val());
        var oldpeople = '';
        $('#people').select({
            title: "选择人员",
            items: peoples,
            onClose:function(d){
                if(d.data.values !== oldpeople){
                    $("#list").empty()
                    oldpeople = d.data.values;
                    // loadRecordList();
                }
            },
            onOpen: function(d){
                if(!peoples){
                    $.toptip('无可选人员');
                }
                oldpeople = d.data.values;
            }
        });
    }

    $('#submit').click(function (e) {
        var starttime = $('#starttime').val();
        var endtime = $('#endtime').val();
        var reason = $('#reason').val();
        var leaveschool = $('#leaveschool').val();
        var docid = $('#people').attr('data-values');
        if(!starttime || !endtime){
            $.toptip("请填写请假开始时间与结束时间！");
            return;
        }
        if(!reason){
            $.toptip("请填写原因！");
            return;
        }
        if(!leaveschool){
            $.toptip("请选择离校方式！");
            return;
        }
        if(!docid){
            $.toptip("请选择学生！");
            return;
        }

        if (starttime > endtime) {
            $.toptip('开始时间不能大于结束时间');
            return;
        }

        if(new Date(starttime).format("yyyy-MM-dd") < new Date().format("yyyy-MM-dd")){
            $.toptip('开始时间不能小于当天日期');
            return;
        }

        $.confirm("确认请假吗？", function() {
            $.postAjax({
                url: "/askforleave/saveleaveform",
                data: {
                    docid: docid,
                    starttime: starttime,
                    endtime: endtime,
                    reason: reason,
                    leaveschool: leaveschool,

                },
                hideLoading: false,
                success: function (result, textStatus, jqXHR) {
                    if (result.success) {
                        $.toast("请假成功,等待审核！",function () {
                            window.location.href = '/askforleave/leaverecord?time=' + ((new Date()).getTime());
                        });
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }
            });
        });
    });

});