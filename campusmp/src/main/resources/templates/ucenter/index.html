<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
  <title></title>
  <link rel="stylesheet" href="/ui/css/weui.min.css" />
  <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
  <link rel="stylesheet" href="/ui/css/main.css" />
  <script src="/ui/js/jquery-2.1.4.js"></script>
  <script src="/ui/js/jquery-weui.min.js"></script>
  <script src="/ui/js/main.js"></script>
  <script th:src="${'/app/ucenter/index.js?t='+timestamp}"></script>
</head>

<body class="page">

  <div class="weui-panel weui-panel_access">
    <div class="weui-panel__bd">
      <a href="javascript:void(0);" class="weui-media-box weui-media-box_appmsg">
        <div class="weui-media-box__hd">
          <img class="weui-media-box__thumb" th:src="${userinfo.headimgurl}" style="border-radius: 50%;" />
        </div>
        <div class="weui-media-box__bd" th:if="${userinfo.usertype!=5}">
          <h4 class="weui-media-box__title" style="height:35px;"><span th:text="${userinfo.name}"></span><span th:text="${userinfo.usertypeName}"
                                                                                                               style="color: hsla(0,0%,100%,.95); font-size: 10px; background-color: #42a5f5; border-radius: 10px; padding:2px 10px; margin-left: 10px;"></span></h4>
          <p class="weui-media-box__desc" th:text="${userinfo.orgname}"></p>
        </div>
        <div class="weui-media-box__bd" th:if="${userinfo.usertype==5}">
          <h4 class="weui-media-box__title" style="height:35px;"><span th:text="${userinfo.nickname}"></span><span th:text="${userinfo.usertypeName}"
              style="color: hsla(0,0%,100%,.95); font-size: 10px; background-color: #42a5f5; border-radius: 10px; padding:2px 10px; margin-left: 10px;"></span></h4>
          <p class="weui-media-box__desc" th:text="${orgname}"></p>
        </div>
      </a>
    </div>
    <div class="weui-panel__ft" th:if="${isaddbind==1}">
      <a href="javascript:void(0);" class="weui-cell weui-cell_access weui-cell_link open-popup" data-target="#changeemp">
        <div class="weui-cell__bd">切换绑定信息</div>
        <span class="weui-cell__ft"></span>
      </a>
    </div>
  </div>

  <div class="weui-panel">
    <div class="weui-panel__hd">功能菜单</div>
    <div class="weui-panel__bd">
      <div class="weui-media-box weui-media-box_small-appmsg">
        <div class="weui-cells">
          <a class="weui-cell weui-cell_access" href="javascript:;" id="addbind" th:if="${isaddbind==1}">
            <div class="weui-cell__hd"><img src="/ui/img/icon-bind.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>
            <div class="weui-cell__bd weui-cell_primary">
              <p>新增绑定</p>
            </div>
            <span class="weui-cell__ft"></span>
          </a>
          <a class="weui-cell weui-cell_access open-popup" data-target="#disbindemp" href="javascript:;" th:if="${userinfo.usertype!=5}">
            <div class="weui-cell__hd"><img src="/ui/img/icon-ubind.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>
            <div class="weui-cell__bd weui-cell_primary">
              <p>解除绑定</p>
            </div>
            <span class="weui-cell__ft"></span>
          </a>
          <a class="weui-cell weui-cell_access" href="javascript:;" th:if="${userinfo.usertype!=2}" id="sex">
           <input type="hidden" id="sexval" th:value="${sexval}" />
            <div class="weui-cell__hd"><img src="/ui/img/icon-sex.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>
            <div class="weui-cell__bd weui-cell_primary">
              <p>性别</p>
            </div>
            <span class="weui-cell__ft"><p th:if="${sex != ''}" th:text="${sex}"></p></span>
          </a>
          <a class="weui-cell weui-cell_access" href="/ucenter/photonumber" th:if="${userinfo.usertype!=2 and userinfo.usertype!=5}">
            <div class="weui-cell__hd"><img src="/ui/img/icon-phone.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>
            <div class="weui-cell__bd weui-cell_primary">
              <p th:if="${mobile == ''}">完善手机号</p>
              <p th:if="${mobile != ''}">手机号</p>
            </div>
            <span class="weui-cell__ft"><p th:if="${mobile != ''}" th:text="${mobile}"></p></span>
          </a>
          <a class="weui-cell weui-cell_access" th:if="${userinfo.usertype!=2 and userinfo.usertype!=5}" id="email">
            <div class="weui-cell__hd"><img src="/ui/img/icon-email.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>
            <div class="weui-cell__bd weui-cell_primary">
              <p th:if="${email == ''}">完善Email</p>
              <p th:if="${email != ''}">Email</p>
            </div>
            <span class="weui-cell__ft"><p th:if="${email != ''}" th:text="${email}"></p></span>
          </a>
          <a class="weui-cell weui-cell_access" href="/ucenter/linkman" th:if="${userinfo.usertype!=4 and userinfo.usertype!=1 and userinfo.usertype!=2 and userinfo.usertype!=5}">
            <div class="weui-cell__hd"><img src="/ui/img/icon-man.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>
            <div class="weui-cell__bd weui-cell_primary">
              <p>联系人</p>
            </div>
            <span class="weui-cell__ft"></span>
          </a>
          <a class="weui-cell weui-cell_access company" href="javascript:;" th:if="${userinfo.usertype==4}">
            <div class="weui-cell__hd"><img src="/ui/img/icon-man.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>
            <div class="weui-cell__bd weui-cell_primary">
              <p>单位信息</p>
            </div>
            <span class="weui-cell__ft" id="companyval" th:text="${company}"></span>
          </a>
<!--          <a class="weui-cell weui-cell_access company" href="javascript:;" th:if="${userinfo.usertype==4}">-->
<!--            <div class="weui-cell__hd"><img src="/ui/img/icon-man.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>-->
<!--            <div class="weui-cell__bd weui-cell_primary">-->
<!--              <p>部门</p>-->
<!--            </div>-->
<!--            <span class="weui-cell__ft" id="departmentval" th:text="${department}"></span>-->
<!--          </a>-->
<!--          <a class="weui-cell weui-cell_access company" href="javascript:;" th:if="${userinfo.usertype==4}">-->
<!--            <div class="weui-cell__hd"><img src="/ui/img/icon-man.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>-->
<!--            <div class="weui-cell__bd weui-cell_primary">-->
<!--              <p>职位</p>-->
<!--            </div>-->
<!--            <span class="weui-cell__ft" id="positionval" th:text="${position}"></span>-->
<!--          </a>-->
          <a class="weui-cell weui-cell_access" href="/ucenter/updateDetails" th:if="${userinfo.usertype==2 or userinfo.usertype==5}">
            <div class="weui-cell__hd"><img src="/ui/img/icon-man.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>
            <div class="weui-cell__bd weui-cell_primary">
              <p>完善信息</p>
            </div>
            <span class="weui-cell__ft"></span>
          </a>
          <a class="weui-cell weui-cell_access" href="/ucenter/passwordAlter" th:if="${userinfo.usertype==2}">
            <div class="weui-cell__hd"><img src="/ui/img/icon-password.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>
            <div class="weui-cell__bd weui-cell_primary">
              <p>设置密码</p>
            </div>
            <span class="weui-cell__ft"></span>
          </a>
          <a class="weui-cell weui-cell_access" href="/ucenter/password" th:if="${userinfo.usertype==1}">
            <div class="weui-cell__hd"><img src="/ui/img/icon-password.png" alt="" style="width:20px;margin-right:5px;display:block" /></div>
            <div class="weui-cell__bd weui-cell_primary">
              <p>设置密码</p>
            </div>
            <span class="weui-cell__ft"></span>
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="page_body">
    <a href="/" class="weui-btn weui-btn_default">返回首页</a>
  </div>

  <div id="changeemp" class='weui-popup__container popup-bottom'>
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
      <div class="toolbar">
        <div class="toolbar-inner">
          <a href="javascript:;" class="picker-button close-popup">关闭</a>
          <h1 class="title">选择已绑定</h1>
        </div>
      </div>
      <div class="modal-content">
        <div class="weui-grids">
          <a class="weui-grid js_grid" th:each="collect,iterStat : ${userlist}" th:href="'/ucenter/changebind?uid='+${collect.uid}">
            <div class="weui-grid__icon">
              <img th:src="${collect.headimgurl}" alt="" style="border-radius: 50%;" />
            </div>
            <p class="weui-grid__label" th:text="${collect.name}"></p>
          </a>
        </div>
      </div>
    </div>
  </div>

  <div id="disbindemp" class='weui-popup__container popup-bottom'>
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
      <div class="toolbar">
        <div class="toolbar-inner">
          <a href="javascript:;" class="picker-button close-popup">关闭</a>
          <h1 class="title">选择已绑定微信</h1>
        </div>
      </div>
      <div class="modal-content">
        <div class="weui-grids">
          <a class="weui-grid js_grid" th:each="collect,iterStat : ${userlist}" th:attr="data-uid=${collect.uid}" href="javascript:void(0)">
            <div class="weui-grid__icon">
              <img th:src="${collect.headimgurl}" alt="" style="border-radius: 50%;" />
            </div>
            <p class="weui-grid__label" th:text="${collect.name}"></p>
          </a>
        </div>
      </div>
    </div>
  </div>

</body>

</html>