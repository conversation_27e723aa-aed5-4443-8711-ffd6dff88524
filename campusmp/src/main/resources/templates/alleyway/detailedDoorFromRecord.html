<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0"/>
    <title>申请开门记录详情</title>
    <link rel="stylesheet" href="/ui/css/weui.min.css"/>
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css"/>
    <link rel="stylesheet" href="/ui/css/main.css"/>
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <!--    <script src="/app/time/detailedAbsenceRecord.js"></script>-->
    <script th:src="${'/app/alleyway/door/detailedDoorFromRecord.js?t='+timestamp}"></script>

</head>
<style>
    /* 复制 doorfrom.html 里关于梯控的全部样式 */
    .floor-list-modern {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .elevator-card {
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 2px 16px rgba(0, 0, 0, 0.06);
        margin: 18px 0;
        padding: 18px 20px 12px 20px;
        transition: box-shadow 0.2s;
    }

    .elevator-card:hover {
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.10);
    }

    .elevator-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .elevator-icon {
        font-size: 22px;
    }

    .elevator-type {
        color: #4e8cff;
        font-size: 14px;
        margin-left: 6px;
    }

    .floor-checkbox {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 16px;
        cursor: pointer;
        user-select: none;
        padding: 8px 0;
        border-radius: 8px;
        transition: background 0.18s;
    }

    .floor-checkbox:hover {
        background: #f0f4ff;
    }

    .floor-checkbox input[type="checkbox"] {
        display: none;
    }

    .custom-checkbox {
        width: 22px;
        height: 22px;
        border: 2px solid #b3c0d1;
        border-radius: 7px;
        background: #fff;
        position: relative;
        transition: border-color 0.2s, background 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .floor-checkbox input[type="checkbox"]:checked + .custom-checkbox {
        border-color: #4e8cff;
        background: #4e8cff;
    }

    .floor-checkbox input[type="checkbox"]:checked + .custom-checkbox::after {
        content: '';
        display: block;
        width: 8px;
        height: 14px;
        border: solid #fff;
        border-width: 0 3px 3px 0;
        transform: rotate(45deg);
        position: absolute;
        left: 6px;
        top: 1px;
    }

    .floor-label {
        flex: 1;
        color: #222;
        font-size: 16px;
        letter-spacing: 1px;
        text-align: right;
    }

    .batch-bar {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 12px;
    }

    .batch-btn {
        background: #4e8cff;
        color: #fff;
        border: none;
        border-radius: 18px;
        padding: 6px 18px;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.18s, box-shadow 0.18s;
        box-shadow: 0 2px 8px rgba(78, 140, 255, 0.08);
        outline: none;
    }

    .batch-btn:hover, .batch-btn:active {
        background: #2566d6;
    }

    .batch-btn:focus {
        box-shadow: 0 0 0 2px #b3d1ff;
    }
    /* 图片容器：每行 3 张 */
    .image-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: flex-start;
    }

    /* 单个图片项 */
    .image-item {
        width: calc(33.33% - 10px);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* 图片样式 */
    .preview-image {
        width: 100%;
        height: auto;
        max-width: 100px;
        max-height: 100px;
        border-radius: 5px;
        cursor: pointer;
        object-fit: cover;
    }

    /* 预览大图样式 */
    .weui-gallery__img {
        width: 100%;
        height: 100%;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
    }
    select option {
        pointer-events: none;
    }
</style>
<body class="page">
<div class="weui-cells">
    <input id="uid" type="hidden" th:value="${uid}"/>
    <input id="infoid" type="hidden" th:value="${infoid}"/>
    <input id="imgPath" type="hidden" th:value="${imgPath}"/>
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>姓名</p>
        </div>
        <div class="weui-cell__ft" id="infoname" th:text="${infoname}"></div>

    </div>
    <div class="weui-cell">
    <div class="weui-cell__bd" >
        <p>申请部门</p>
    </div>
    <div class="weui-cell__ft" id="infonamedept" th:text="${department}"></div>
    </div>


<div class="weui-cell">
    <div class="weui-cell__hd"><p>开门开始时间</p></div>

    <div class="weui-cell__ft" id="starttime" th:text="${starttime}"></div>

</div>

<div class="weui-cell">
    <div class="weui-cell__hd"><p>开门结束时间</p></div>

    <div class="weui-cell__ft" id="endtime" th:text="${endtime}"></div>

</div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">门禁权限</label></div>
        <div class="weui-cell__bd">
            <select class="weui-select" id="door" multiple>
            </select>
        </div>
    </div>

<div id="elevatorDeviceContainer"></div>

<div class="weui-cell">
    <div class="weui-cell__bd">
        <p>使用次数</p>
    </div>
    <div class="weui-cell__ft" id="usage_count" th:text="${usage_count}"></div>
</div>
<div class="weui-cell">
    <div class="weui-cell__bd">
        <p>密码</p>
    </div>
    <div class="weui-cell__ft" id="password" th:text="${password}"></div>
</div>


<div class="weui-cell">
    <div class="weui-cell__bd" style="white-space:nowrap;">
        <p>申请原因</p>
    </div>
    <div class="weui-cell__ft" id="reason" th:text="${reason}"></div>
</div>
<div class="weui-cell">
    <div class="weui-cell__bd">
        <p>审核状态</p>
    </div>
    <div class="weui-cell__ft">
        <p style="color:red;" id="disaduit" th:if="${status == 0}">审核中</p>
    </div>
    <div class="weui-cell__ft">
        <p style="color:red;" id="disaduit" th:if="${status == 2}">已取消</p>
    </div>
    <div class="weui-cell__ft">
        <p style="color:#0bb20c;" id="agree" th:if="${status == 1}">同意</p>
    </div>
    <div class="weui-cell__ft">
        <p style="color:red;" id="disagree" th:if="${status == -1}">不同意</p>
    </div>
</div>
<div class="weui-cell">
    <div class="weui-cell__bd">
        <p>附件照片</p>
    </div>

</div>
<!-- 图片容器 -->
<div id="imageContainer" class="image-grid"></div>

<!-- 预览大图 -->
<div id="gallery" class="weui-gallery" style="display: none;">
    <span class="weui-gallery__img" id="galleryImg"></span>
    <div class="weui-gallery__opr">
        <a href="javascript:" class="weui-gallery__del" id="closeGallery">关闭</a>
    </div>
</div>
</div>

<!--<div class="weui-cell" th:if="${remark}">-->
<!--    <div class="weui-cell__bd" style="white-space:nowrap;">-->
<!--        <p>审核备注</p>-->
<!--    </div>-->
<!--    <div class="weui-cell__ft" id="remarked" th:text="${remark}"></div>-->
<!--</div>-->

<div class="weui-cells">
    <div class="weui-cells__title">审核记录</div>
    <div id="auditRecord"></div>
</div>

<!--    <table th:if="${status == 1}">-->
<!--        <div class="weui-cell">-->
<!--            <div class="weui-cell__bd" style="white-space:nowrap;">-->
<!--                <p>销假状态</p>-->
<!--            </div>-->
<!--            <div class="weui-cell__ft">-->
<!--                <p style="color:red;" id="isfinish" th:if="${isfinish == 0}">未销假</p>-->
<!--            </div>-->
<!--            <div class="weui-cell__ft">-->
<!--                <p style="color:#0bb20c;" id="isfinish" th:if="${isfinish == 1}">已销假</p>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="weui-cell">-->
<!--            <div class="weui-cell__bd" style="white-space:nowrap;">-->
<!--                <p>销假时间</p>-->
<!--            </div>-->
<!--            <div class="weui-cell__ft" id="leavefinishtime" th:text="${leavefinishtime}"></div>-->
<!--        </div>-->
<!--    </table>-->
</div>
<div class="page_body">
    <a href="javascript:void(0);" class="weui-btn weui-btn_warn" id="cancel" th:if="${status == 0}">取消申请</a>
    <!--    <a href="javascript:void(0);" class="weui-btn weui-btn_primary" id="approvalrecord">查看审批记录</a>-->
    <a href="/alleyway/doorfromrecord" class="weui-btn weui-btn_default" style="background-color: #f8f8f8;color:#333"
       th:if="${usertype == 3}">返回菜单</a>
</div>

</body>

</html>