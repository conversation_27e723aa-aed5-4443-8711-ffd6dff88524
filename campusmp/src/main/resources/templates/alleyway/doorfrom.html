<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0"/>
    <title></title>
    <link rel="stylesheet" href="/ui/css/weui.min.css"/>
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css"/>
    <link rel="stylesheet" href="/ui/css/main.css"/>
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <script th:src="${'/app/alleyway/door/doorfrom.js?t='+timestamp}"></script>
    <script src="/ui/js/jweixin-1.6.0.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 10px 0;
        }

        .preview-item {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .preview-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
        }

        .delete-btn {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 18px;
            font-size: 14px;
            cursor: pointer;
            z-index: 2;
        }

        .upload-box {
            width: 80px;
            height: 80px;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            transition: all 0.3s;
        }

        .upload-box:hover {
            border-color: #07c160;
            color: #07c160;
        }

        .image-preview-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .preview-large {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 40px;
            cursor: pointer;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .weui-cell {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .weui-cell__hd {
            min-width: 100px;
        }
        .weui-cell__bd {
            flex: 1;
        }
        .weui-input {
            width: 100%;
            padding: 5px;
            box-sizing: border-box;
        }
        .generate-btn {
            margin-left: 10px;
            padding: 5px 10px;
            background-color: #007aff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .generate-btn:hover {
            background-color: #0056b3;
        }
        .floor-selection-row {
    width: 100%;
    margin-top: -10px;
    margin-bottom: 10px;
}
.floor-selection {
    margin-top: 10px;
    padding: 10px;
    background: #fff;
    border-radius: 5px;
}
.floor-item {
    margin: 5px 0;
    padding: 5px;
    border-bottom: 1px solid #eee;
}
.floor-item:last-child {
    border-bottom: none;
}
.floor-item input[type="checkbox"] {
    margin-right: 10px;
}
        .weui-input {
            text-align: center;
            letter-spacing: 5px;
            font-size: 18px;
        }

        .weui-cells__title {
            margin-top: 10px;
            margin-bottom: 5px;
            padding-left: 15px;
            color: #999;
            font-size: 14px;
        }

.floor-selection-inline {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 12px 0;
  max-height: 320px;
  overflow-y: auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  margin: 0 0 10px 0;
}

.floor-item-inline {
  display: flex;
  align-items: center;
  padding: 8px 14px;
  border-radius: 8px;
  transition: background 0.2s;
  cursor: pointer;
  font-size: 16px;
  user-select: none;
  position: relative;
}

.floor-item-inline:hover {
  background: #f0f4ff;
}

.floor-item-inline input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #b3c0d1;
  border-radius: 6px;
  margin-right: 12px;
  background: #fff;
  transition: border-color 0.2s, box-shadow 0.2s;
  outline: none;
  cursor: pointer;
  position: relative;
}

.floor-item-inline input[type="checkbox"]:checked {
  border-color: #4e8cff;
  background: #4e8cff;
}

.floor-item-inline input[type="checkbox"]:checked::after {
  content: '';
  display: block;
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 12px;
  border: solid #fff;
  border-width: 0 3px 3px 0;
  transform: rotate(45deg);
}

.floor-item-inline span {
  flex: 1;
  color: #333;
  font-size: 16px;
  letter-spacing: 1px;
}

.elevator-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.06);
  margin: 18px 0;
  padding: 18px 20px 12px 20px;
  transition: box-shadow 0.2s;
}
.elevator-card:hover {
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
}
.elevator-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}
.elevator-icon {
  font-size: 22px;
}
.elevator-type {
  color: #4e8cff;
  font-size: 14px;
  margin-left: 6px;
}
.floor-list-modern {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.floor-checkbox {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  cursor: pointer;
  user-select: none;
  padding: 8px 0;
  border-radius: 8px;
  transition: background 0.18s;
}
.floor-checkbox:hover {
  background: #f0f4ff;
}
.floor-checkbox input[type="checkbox"] {
  display: none;
}
.custom-checkbox {
  width: 22px;
  height: 22px;
  border: 2px solid #b3c0d1;
  border-radius: 7px;
  background: #fff;
  position: relative;
  transition: border-color 0.2s, background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.floor-checkbox input[type="checkbox"]:checked + .custom-checkbox {
  border-color: #4e8cff;
  background: #4e8cff;
}
.floor-checkbox input[type="checkbox"]:checked + .custom-checkbox::after {
  content: '';
  display: block;
  width: 8px;
  height: 14px;
  border: solid #fff;
  border-width: 0 3px 3px 0;
  transform: rotate(45deg);
  position: absolute;
  left: 6px;
  top: 1px;
}
.floor-label {
  flex: 1;
  color: #222;
  font-size: 16px;
  letter-spacing: 1px;
  text-align: right;
}
.batch-bar {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 12px;
}
.batch-btn {
  background: #4e8cff;
  color: #fff;
  border: none;
  border-radius: 18px;
  padding: 6px 18px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.18s, box-shadow 0.18s;
  box-shadow: 0 2px 8px rgba(78,140,255,0.08);
  outline: none;
}
.batch-btn:hover, .batch-btn:active {
  background: #2566d6;
}
.batch-btn:focus {
  box-shadow: 0 0 0 2px #b3d1ff;
}
#auditorListContainer {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 10px 16px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.04);
}
.auditor-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
}
.auditor-list li {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  font-size: 15px;
  color: #333;
}
.auditor-name {
  font-weight: 500;
  margin-right: 4px;
}
.auditor-code {
  color: #888;
  font-size: 13px;
}
.auditor-radio-label {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 6px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 15px;
  cursor: pointer;
}
.auditor-radio-label input[type="radio"] {
  margin-right: 6px;
}
    </style>
    <script>
        function generatePassword() {
            const length = 6;
            let password = '';
            for (let i = 0; i < length; i++) {
                password += Math.floor(Math.random() * 10);
            }
            document.getElementById('password').value = password;
        }
    </script>
</head>

<body>
<input id="resourcedomain" type="hidden" th:value="${resourcedomain}"/>
<input id="peoples" type="hidden" th:value="${people}"/>
<input id="usertype" type="hidden" th:value="${usertype}"/>
<input id="orgcode" type="hidden" th:value="${orgcode}"/>
<input id="infoId" type="hidden" th:value="${infoId}"/>

<div class="weui-cells__title">选择起止时间</div>
<div class="weui-cells weui-cells_form">
    <div class="weui-cell" th:if="${usertype==4 or usertype == 3}">
        <div class="weui-cell__hd"><label class="weui-label">姓名</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" id="people" placeholder="" type="text" th:value="${username}"
                   th:attr="data-values=${userDocid}" readonly="readonly"></input>
        </div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">申请部门</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" id="people" placeholder="" type="text" th:value="${usernamedept}"
                   readonly="readonly"></input>
        </div>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="" class="weui-label">开门开始时间</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="datetime-local" th:value="${starttime}" id="starttime"> </input>
        </div>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="" class="weui-label">开门结束时间</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="datetime-local" th:value="${endtime}" id="endtime"> </input>
        </div>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="" class="weui-label">开门密码</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="text" th:value="${password}" placeholder="请输入开门密码" id="password" readonly>
        </div>
        <button class="generate-btn" onclick="generatePassword()">生成密码</button>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="" class="weui-label">开门次数</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="text" th:value="${usage_count}" placeholder="请输入开门次数"
                   id="usage_count"> </input>
        </div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">门禁权限</label></div>
        <div class="weui-cell__bd">
            <select class="weui-select" id="door" multiple>
            </select>
        </div>
    </div>
    <div id="auditorListContainer" style="margin: 10px 0;">
        <label style="font-weight:bold;">审核人：</label>
        <div id="auditorList" class="auditor-list"></div>
    </div>
    <div id="elevatorDeviceContainer"></div>
  

</div>
<div class="weui-cells__title">上传图片</div>
<div class="weui-cells weui-cells_form">
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <div class="preview-container" id="previewContainer">
                <div class="upload-box" id="uploadBox">
                    <i class="weui-icon-add"></i>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="weui-cells__title">申请原因</div>
<div class="weui-cells">
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <textarea class="weui-textarea" placeholder="请输入文本" rows="3" id="reason"></textarea>
        </div>
    </div>
</div>
<div class="page_body">
    <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="submit">提交</a>
    <a href="/" class="weui-btn weui-btn_default" style="background-color: #f8f8f8;color:#333">返回</a>
</div>
</div>
</body>

</html>