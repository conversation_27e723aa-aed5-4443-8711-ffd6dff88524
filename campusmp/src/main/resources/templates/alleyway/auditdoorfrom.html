<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0"/>
    <title>开门审核</title>
    <link rel="stylesheet" href="/ui/css/weui.min.css"/>
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css"/>
    <link rel="stylesheet" href="/ui/css/main.css"/>
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <script th:src="${'/app/alleyway/door/auditdoorfrom.js?t='+timestamp}"></script>
</head>
<style>
    /* 复制 doorfrom.html 里关于梯控的全部样式 */
    .floor-list-modern {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .elevator-card {
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 2px 16px rgba(0, 0, 0, 0.06);
        margin: 18px 0;
        padding: 18px 20px 12px 20px;
        transition: box-shadow 0.2s;
    }

    .elevator-card:hover {
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.10);
    }

    .elevator-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .elevator-icon {
        font-size: 22px;
    }

    .elevator-type {
        color: #4e8cff;
        font-size: 14px;
        margin-left: 6px;
    }

    .floor-checkbox {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 16px;
        cursor: pointer;
        user-select: none;
        padding: 8px 0;
        border-radius: 8px;
        transition: background 0.18s;
    }

    .floor-checkbox:hover {
        background: #f0f4ff;
    }

    .floor-checkbox input[type="checkbox"] {
        display: none;
    }

    .custom-checkbox {
        width: 22px;
        height: 22px;
        border: 2px solid #b3c0d1;
        border-radius: 7px;
        background: #fff;
        position: relative;
        transition: border-color 0.2s, background 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .floor-checkbox input[type="checkbox"]:checked + .custom-checkbox {
        border-color: #4e8cff;
        background: #4e8cff;
    }

    .floor-checkbox input[type="checkbox"]:checked + .custom-checkbox::after {
        content: '';
        display: block;
        width: 8px;
        height: 14px;
        border: solid #fff;
        border-width: 0 3px 3px 0;
        transform: rotate(45deg);
        position: absolute;
        left: 6px;
        top: 1px;
    }

    .floor-label {
        flex: 1;
        color: #222;
        font-size: 16px;
        letter-spacing: 1px;
        text-align: right;
    }

    .batch-bar {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 12px;
    }

    .batch-btn {
        background: #4e8cff;
        color: #fff;
        border: none;
        border-radius: 18px;
        padding: 6px 18px;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.18s, box-shadow 0.18s;
        box-shadow: 0 2px 8px rgba(78, 140, 255, 0.08);
        outline: none;
    }

    .batch-btn:hover, .batch-btn:active {
        background: #2566d6;
    }

    .batch-btn:focus {
        box-shadow: 0 0 0 2px #b3d1ff;
    }

    /* 图片容器：每行 3 张 */
    .image-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: flex-start;
    }

    /* 单个图片项 */
    .image-item {
        width: calc(33.33% - 10px);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* 图片样式 */
    .preview-image {
        width: 100%;
        height: auto;
        max-width: 100px;
        max-height: 100px;
        border-radius: 5px;
        cursor: pointer;
        object-fit: cover;
    }

    /* 预览大图样式 */
    .weui-gallery__img {
        width: 100%;
        height: 100%;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
    }

    .weui-cell {
        display: flex;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #eee;
    }

    .weui-cell__hd {
        min-width: 100px;
    }

    .weui-cell__bd {
        flex: 1;
    }

    .weui-input {
        width: 100%;
        padding: 5px;
        box-sizing: border-box;
    }

    .generate-btn {
        margin-left: 10px;
        padding: 5px 10px;
        background-color: #007aff;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    }

    .generate-btn:hover {
        background-color: #0056b3;
    }
</style>
<script>
    function generatePassword() {
        const length = 6;
        let password = '';
        for (let i = 0; i < length; i++) {
            password += Math.floor(Math.random() * 10);
        }
        document.getElementById('password').value = password;
    }
</script>
<body class="page">
<div class="weui-cells">
    <input id="uid" type="hidden" th:value="${uid}"/>
    <input id="infoid" type="hidden" th:value="${infoid}"/>
    <input id="imgPath" type="hidden" th:value="${imgPath}"/>
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>姓名</p>
        </div>
        <div class="weui-cell__ft" id="infoname" th:text="${infoname}"></div>

    </div>
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>人员类型</p>
        </div>
        <div class="weui-cell__ft" id="infotype" th:text="${infotype}"></div>

    </div>
    <div class="weui-cell" th:if="${applicanttype=='1'}">
        <div class="weui-cell__bd">
            <p>申请部门</p>
        </div>
        <div class="weui-cell__ft" id="departmentname" th:text="${department}"></div>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="" class="weui-label">开门开始时间</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="datetime-local" th:value="${starttime}" id="starttime"> </input>
        </div>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="" class="weui-label">开门结束时间</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="datetime-local" th:value="${endtime}" id="endtime"> </input>
        </div>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">门禁权限</label></div>
        <div class="weui-cell__bd">
            <select class="weui-select" id="door" multiple>
            </select>
        </div>
    </div>
    <div id="elevatorDeviceContainer"></div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="" class="weui-label">开门密码</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="text" th:value="${password}" placeholder="请输入开门密码" id="password"
                   readonly>
        </div>
        <button class="generate-btn" onclick="generatePassword()">生成新密码</button>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="" class="weui-label">开门次数</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="number" th:value="${usage_count}" placeholder="请输入开门次数"
                   id="usage_count"> </input>
        </div>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__bd" style="white-space:nowrap;">
            <p>开门原因</p>
        </div>
        <div class="weui-cell__ft" id="reason" th:text="${reason}"></div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>审核状态</p>
        </div>
        <div class="weui-cell__ft">
            <p style="color:red;" id="disaduit" th:if="${status == 0 and auditcondition == 1}">审核中 您已审核完成</p>
        </div>
        <div class="weui-cell__ft">
            <p style="color:red;" id="disaduit" th:if="${status == 0 and auditcondition == 0}">审核中 您未审核</p>
        </div>
        <div class="weui-cell__ft">
            <p style="color:red;" id="disaduit" th:if="${status == 1  }">单据审批通过 您已审核完成</p>
        </div>
        <div class="weui-cell__ft">
            <p style="color:red;" id="disaduit" th:if="${status == -1  }">单据审批未通过 </p>
        </div>
        <div class="weui-cell__ft">
            <p style="color:red;" id="disaduit" th:if="${status == 2  }"> 当前审批单据已取消</p>
        </div>
    </div>
    <!--<div class="weui-cells__title" th:if="${(warremark != '' and warremark !=null ) and status != 2}">选择审核人</div>-->
    <!--<div class="weui-cell" th:if="${status == 0 and auditcondition == 0}">-->
    <!--    <div class="weui-cell__hd"><label class="weui-label">审核人类型</label></div>-->
    <!--    <div class="weui-cell__bd">-->
    <!--        <select class="weui-select" id="psitoin">-->
    <!--            <option disabled="disabled" selected="selected">请选择审核人类型</option>-->
    <!--        </select>-->
    <!--    </div>-->
    <!--</div>-->
    <!--<div class="weui-cell" th:if="${status == 0 and auditcondition == 0}">-->
    <!--    <div class="weui-cell__hd"><label class="weui-label">审核人姓名</label></div>-->
    <!--    <div class="weui-cell__bd">-->
    <!--        <input class="weui-input" id="aduitname" placeholder="请输入需要搜索的审核人姓名" type="text"></input>-->
    <!--    </div>-->
    <!--</div>-->
    <!--<div class="weui-cell" th:if="${status == 0 and auditcondition == 0}">-->
    <!--    <div class="weui-cell__hd"><label class="weui-label">选择审核人</label></div>-->
    <!--    <div class="weui-cell__bd">-->
    <!--        <select class="weui-select" id="resultSelect">-->
    <!--            <option value="">请选择审核人</option>-->
    <!--        </select>-->
    <!--    </div>-->
    <!--</div>-->
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>附件照片</p>
        </div>
    </div>
    <!-- 图片容器 -->
    <div id="imageContainer" class="image-grid"></div>
</div>

<!-- 预览大图 -->
<div id="gallery" class="weui-gallery" style="display: none;">
    <span class="weui-gallery__img" id="galleryImg"></span>
    <div class="weui-gallery__opr">
        <a href="javascript:" class="weui-gallery__del" id="closeGallery">关闭</a>
    </div>
</div>


</div>


<div class="weui-cells">
    <div class="weui-cells__title">审核记录</div>
    <div id="auditRecord"></div>
</div>

<div class="weui-cells__title" th:if="${auditcondition == 0}">审核备注</div>
<div class="weui-cells weui-cells_form" th:if="${auditcondition == 0}">
    <div class="weui-cell">
        <div class="weui-cell__bd"><textarea class="weui-textarea" placeholder="输入原因" rows="3"
                                             id="auditremark"></textarea></div>
    </div>
</div>


<div class="page_body">
    <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="bagree" th:if="${status == 0 and auditcondition == 0}">同意</a>
    <a href="javascript:void(0);" class="weui-btn weui-btn_warn" id="bdisagree"
       th:if="${status == 0 and auditcondition == 0}">不同意</a>
    <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="save"
       th:if="${status == 0 and auditcondition == 0}">保存修改</a>
    <a href="/alleyway/auditdoorfromlist" class="weui-btn weui-btn_default"
       style="background-color: #f8f8f8;color:#333">返回菜单</a>
</div>

</body>

</html>