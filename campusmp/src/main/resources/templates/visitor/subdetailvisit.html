<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    <title>访客预约</title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <script th:src="${'/app/visitor/subscribe/subdetailvisit.js?t='+timestamp}"></script>
    <style>
        .weui-panel {
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            background: #fafbfc;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        }
        .weui-panel__hd {
            padding: 10px 15px;
            background: #f5f7fa;
            border-bottom: 1px solid #e5e5e5;
            font-size: 16px;
        }
        .weui-panel__bd {
            padding: 8px 0;
        }
    </style>
</head>

<body class="page">
    <div class="weui-cells__title">访客预约详细信息</div>
    <div class="weui-cells">
        <input id="uid" type="hidden" th:value="${uid}" />
        <input id="auditstatus" type="hidden" th:value="${auditstatus}" />
        <input id="byagree" type="hidden" th:value="${byagree}" />
        <input id="visitorid" type="hidden" th:value="${visitorid}" />
        <input id="status" type="hidden" th:value="${status}" />
        <input id="isleave" type="hidden" th:value="${isleave}" />
        <input id="devices" type="hidden" th:value="${devices}" />

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>来访人</p>
            </div>
            <div class="weui-cell__ft" id="name" th:text="${name}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>被访人</p>
            </div>
            <div class="weui-cell__ft" id="byvisitor" th:text="${byvisitor}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>性别</p>
            </div>
            <div class="weui-cell__ft" id="sex" th:text="${sex}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>联系电话</p>
            </div>
            <div class="weui-cell__ft" id="mobile" th:text="${mobile}"></div>
        </div>

        <div class="weui-cell" th:if="${company!=''}">
            <div class="weui-cell__bd">
                <p>公司</p>
            </div>
            <div class="weui-cell__ft" id="company" th:text="${company}"></div>
        </div>

        <div class="weui-cell" th:if="${department!=''}">
            <div class="weui-cell__bd">
                <p>部门</p>
            </div>
            <div class="weui-cell__ft" id="department" th:text="${department}"></div>
        </div>
        <div class="weui-cell" th:if="${position!=''}">
            <div class="weui-cell__bd">
                <p>职位</p>
            </div>
            <div class="weui-cell__ft" id="position" th:text="${position}"></div>
        </div>
        <div class="weui-cell" th:if="${plateno}">
            <div class="weui-cell__bd">
                <p>来访车辆</p>
            </div>
            <div class="weui-cell__ft" id="plateno" th:text="${plateno}"></div>
        </div>  
        
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>来访原因</p>
            </div>
            <div class="weui-cell__ft" id="reason" th:text="${reason}"></div>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>来访时间</p>
            </div>
            <div class="weui-cell__ft" id="visittime" th:text="${visittime}"></div>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>离开时间</p>
            </div>
            <div class="weui-cell__ft" id="leavetime" th:text="${leavetime}"></div>
        </div>

        <div class="weui-cell" th:if="${isleave==1}">
            <div class="weui-cell__bd">
                <p>实际离开时间</p>
            </div>
            <div class="weui-cell__ft" id="leavetime" th:text="${actualleavetime}"></div>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>可通行区域</p>
            </div>
            <div class="weui-cell__ft" id="areapositionname" th:text="${areapositionname}"></div>
        </div>
        <!-- 路线图展示 -->
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>路线图</p>
                <img th:if="${image != ''}" th:src="${image}" alt="路线图" style="max-width:100%;border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.03);margin-top:8px;" />
                <img th:if="${image == ''}" src="/ui/img/faceimage.png" />
            </div>
        </div>

        <div class="weui-cells__title">设备信息</div>
    <div class="weui-cells" id="deviceList"></div>
    <div class="weui-cells__title">门禁密码与使用次数</div>
    <div class="weui-cells">
        <div class="weui-cell">
            <div class="weui-cell__bd"><p>门禁密码</p></div>
            <div class="weui-cell__ft" id="password"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd"><p>密码使用次数</p></div>
            <div class="weui-cell__ft" id="usageCount"></div>
        </div>
    </div>
      
      <div class="weui-cells__title" th:if="${othervisitor}">访客人员</div>
        <div class="weui-cells" th:if="${othervisitor}">
          <div class="weui-cell" th:each="item,othervisitors:${othervisitor}">
            <div class="weui-cell__bd"><p th:text="${item.name}"></p></div>
            <a th:href="${'/face/image?visitorid='+item.visitorid+'&amp;name='+item.name}"><div class="weui-cell__ft" style="color:red;">点击上传人脸</div></a>
          </div>
        </div>
        
      <div class="weui-cells">
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>审核状态</p>
            </div>
            <div class="weui-cell__ft" ><p style="color:red;" id="cancelstatus" hidden="hidden">已取消</p></div>
            <div class="weui-cell__ft" ><p style="color:red;" id="disaduit" hidden="hidden">未审核</p></div>
            <div class="weui-cell__ft" ><p style="color:#0bb20c;" id="agree" hidden="hidden">同意</p></div>
            <div class="weui-cell__ft" ><p style="color:red;" id="disagree" hidden="hidden">不同意</p></div>
            <div class="weui-cell__ft" ><p style="color:red;" id="hadleave" hidden="hidden">已离场</p></div>
        </div>
        <div class="weui-cell" th:if="${remark}">
            <div class="weui-cell__bd" style="white-space:nowrap;">
                <p>备注</p>
            </div>
            <div class="weui-cell__ft" id="remarked" th:text="${remark}"></div>
        </div>
          <div id="list">
          </div>
    </div>
    <div class="page_body">
        <a href="javascript:void(0);" class="weui-btn weui-btn_warn" id="cancel">取消预约</a>
        <a href="subrecord" class="weui-btn weui-btn_default" style="background-color: #f8f8f8;color:#333">返回</a>
    </div>
</body>

</html>