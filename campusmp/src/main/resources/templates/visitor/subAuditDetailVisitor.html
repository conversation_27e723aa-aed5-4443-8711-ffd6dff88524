<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    <title>访客预约审核</title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <script th:src="${'/app/visitor/subscribe/subAuditDetailVisitor.js?t='+timestamp}"></script>
    <style>
  .device-item {
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f8f8;
    border-radius: 5px;
    display: flex;
    align-items: center;
}
.device-item .weui-cell__bd {
    flex: 1;
    min-width: 120px;
    font-size: 16px;
}
.device-item .weui-cell__ft {
    min-width: 140px;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.device-password-input {
    width: 120px;
    text-align: center;
    letter-spacing: 5px;
    font-size: 18px;
    background: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 4px 8px;
}
.floor-selection-row {
    width: 100%;
    margin-top: -10px;
    margin-bottom: 10px;
}
.floor-selection {
    margin-top: 10px;
    padding: 10px;
    background: #fff;
    border-radius: 5px;
}
.floor-item {
    margin: 5px 0;
    padding: 5px;
    border-bottom: 1px solid #eee;
}
.floor-item:last-child {
    border-bottom: none;
}
.floor-item input[type="checkbox"] {
    margin-right: 10px;
}
        .weui-input {
            text-align: center;
            letter-spacing: 5px;
            font-size: 18px;
        }

        .weui-cells__title {
            margin-top: 10px;
            margin-bottom: 5px;
            padding-left: 15px;
            color: #999;
            font-size: 14px;
        }

.floor-selection-inline {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 12px 0;
  max-height: 320px;
  overflow-y: auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  margin: 0 0 10px 0;
}

.floor-item-inline {
  display: flex;
  align-items: center;
  padding: 8px 14px;
  border-radius: 8px;
  transition: background 0.2s;
  cursor: pointer;
  font-size: 16px;
  user-select: none;
  position: relative;
}

.floor-item-inline:hover {
  background: #f0f4ff;
}

.floor-item-inline input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #b3c0d1;
  border-radius: 6px;
  margin-right: 12px;
  background: #fff;
  transition: border-color 0.2s, box-shadow 0.2s;
  outline: none;
  cursor: pointer;
  position: relative;
}

.floor-item-inline input[type="checkbox"]:checked {
  border-color: #4e8cff;
  background: #4e8cff;
}

.floor-item-inline input[type="checkbox"]:checked::after {
  content: '';
  display: block;
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 12px;
  border: solid #fff;
  border-width: 0 3px 3px 0;
  transform: rotate(45deg);
}

.floor-item-inline span {
  flex: 1;
  color: #333;
  font-size: 16px;
  letter-spacing: 1px;
}
    </style>
</head>

<body class="page">
    <div class="weui-cells__title">访客预约详细信息</div>
    <div class="weui-cells">
        <input id="uid" type="hidden" th:value="${uid}" />
        <input id="auditstatus" type="hidden" th:value="${auditstatus}" />
        <input id="devices" type="hidden" th:value="${devices}" />
        <input id="byagree" type="hidden" th:value="${byagree}" />
        <input id="visitorid" type="hidden" th:value="${visitorid}" />
        <input id="status" type="hidden" th:value="${status}" />
        <input id="isleave" type="hidden" th:value="${isleave}" />
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>来访人</p>
            </div>
            <div class="weui-cell__ft" id="name" th:text="${name}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>被访人</p>
            </div>
            <div class="weui-cell__ft" id="byvisitor" th:text="${byvisitor}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>性别</p>
            </div>
            <div class="weui-cell__ft" id="sex" th:text="${sex}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>联系电话</p>
            </div>
            <div class="weui-cell__ft" id="mobile" th:text="${mobile}"></div>
        </div>

        <div class="weui-cell" th:if="${company!=''}">
            <div class="weui-cell__bd">
                <p>公司</p>
            </div>
            <div class="weui-cell__ft" id="company" th:text="${company}"></div>
        </div>

        <div class="weui-cell" th:if="${department!=''}">
            <div class="weui-cell__bd">
                <p>部门</p>
            </div>
            <div class="weui-cell__ft" id="department" th:text="${department}"></div>
        </div>
        <div class="weui-cell" th:if="${position!=''}">
            <div class="weui-cell__bd">
                <p>职位</p>
            </div>
            <div class="weui-cell__ft" id="position" th:text="${position}"></div>
        </div>
        <div class="weui-cell" th:if="${plateno}">
            <div class="weui-cell__bd">
                <p>来访车辆</p>
            </div>
            <div class="weui-cell__ft" id="plateno" th:text="${plateno}"></div>
        </div>  
        
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>来访原因</p>
            </div>
            <div class="weui-cell__ft" id="reason" th:text="${reason}"></div>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>来访时间</p>
            </div>
            <div class="weui-cell__ft" id="visittime" th:text="${visittime}"></div>
        </div>

        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>离开时间</p>
            </div>
            <div class="weui-cell__ft" id="leavetime" th:text="${leavetime}"></div>
        </div>

        <div class="weui-cell" th:if="${isleave==1}">
            <div class="weui-cell__bd">
                <p>实际离开时间</p>
            </div>
            <div class="weui-cell__ft" id="leavetime" th:text="${actualleavetime}"></div>
        </div>
<!--        <div class="weui-cell">-->
<!--            <div class="weui-cell__hd"><label class="weui-label">通行区域</label></div>-->
<!--            <div class="weui-cell__bd">-->
<!--                <input class="weui-input" id="areaposition" type="text" th:value="${areapositionindex}" th:attr="data-values=${areapositionindex}"-->
<!--                       readonly="readonly"></input>-->
<!--            </div>-->
<!--        </div>-->
        <div class="weui-cell">
            <input id="areaposition" type="hidden" th:value="${areapositionindex}" />
            <div class="weui-cell__bd">
                <p>可通行区域</p>
            </div>
            <div class="weui-cell__ft" id="areapositionname"></div>
        </div>

        <!-- 设备控制区域 -->
        <div id="deviceControls" style="display: none;">
            <div class="weui-cells__title">设备控制</div>
            <div id="globalPasswordUsageContainer"></div>
            <div class="weui-cells" id="deviceList"></div>
        </div>

<!--        <div class="weui-cells__title">访客梯控授权</div>-->
<!--        <div class="container">-->
<!--            <div class="device-list">-->
<!--                &lt;!&ndash; 这里将显示设备列表 &ndash;&gt;-->
<!--            </div>-->

<!--            <div class="floor-list">-->
<!--                &lt;!&ndash; 这里将显示楼层列表 &ndash;&gt;-->
<!--            </div>-->
<!--        </div>-->
     </div>
      
      <div class="weui-cells__title" th:if="${othervisitor}">访客人员</div>
        <div class="weui-cells" th:if="${othervisitor}">
          <div class="weui-cell" th:each="item,othervisitors:${othervisitor}">
            <div class="weui-cell__bd"><p th:text="${item.name}"></p></div>
<!--              <a th:href="${'/face/image?visitorid='+item.visitorid+'&amp;name='+item.name}"><div class="weui-cell__ft" style="color:red;">点击上传人脸</div></a>-->
          </div>
        </div>
        
      <div class="weui-cells">
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>审核状态</p>
            </div>
            <div class="weui-cell__ft" ><p style="color:red;" id="cancelstatus" hidden="hidden">已取消</p></div>
            <div class="weui-cell__ft" ><p style="color:red;" id="disaduit" hidden="hidden">未审核</p></div>
            <div class="weui-cell__ft" ><p style="color:#0bb20c;" id="agree" hidden="hidden">同意</p></div>
            <div class="weui-cell__ft" ><p style="color:red;" id="disagree" hidden="hidden">不同意</p></div>
            <div class="weui-cell__ft" ><p style="color:red;" id="hadleave" hidden="hidden">已离场</p></div>
        </div>
        <div class="weui-cell" th:if="${remark}">
            <div class="weui-cell__bd" style="white-space:nowrap;">
                <p>备注</p>
            </div>
            <div class="weui-cell__ft" id="remarked" th:text="${remark}"></div>
        </div>
          <div id="list">
          </div>
    </div>
    <div class="weui-cells__title" th:if="${auditcondition == 0}">审核备注</div>
    <div class="weui-cells weui-cells_form" th:if="${auditcondition == 0}">
        <div class="weui-cell">
            <div class="weui-cell__bd"> <textarea class="weui-textarea" placeholder="输入原因" rows="3" id="auditremark"></textarea></div>
        </div>
    </div>


    <div class="page_body">
        <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="bagree" th:if="${auditcondition == 0}">同意</a>
        <a href="javascript:void(0);" class="weui-btn weui-btn_warn" id="bdisagree" th:if="${auditcondition == 0}">不同意</a>
        <a href="javascript:void(0);" class="weui-btn weui-btn_warn" id="saveDeviceSettings" th:if="${status == '0'}">保存</a>
        <a href="javascript:void(0);" class="weui-btn weui-btn_warn" id="leaveFinish" th:if="${status == 1 and isleave == 0}">离场</a>
        <a href="/visitor/subAudit" class="weui-btn weui-btn_default" style="background-color: #f8f8f8;color:#333">返回</a>
    </div>
</body>

</html>