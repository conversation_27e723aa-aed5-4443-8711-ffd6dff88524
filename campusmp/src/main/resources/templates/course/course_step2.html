<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    　  <meta http-equiv="pragma" content="no-cache"/>
　　  <meta http-equiv="cache-control" content="no-cache"/>
　      <meta http-equiv="expires" content="0"/>
    <title></title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <script src="/app/course/course_step2.js?v=20191117"></script>
</head>
<style>
    .disabled-button {
        background-color: #ccc; /* 灰色背景色 */
        color: #999; /* 灰色文字颜色 */
        cursor: not-allowed; /* 禁用点击光标样式 */
    }
</style>
<body class="page">
<input id="courseId" type="hidden" th:value="${uid}" />
<input id="selectedNum" type="hidden" th:value="${selectedNum}" />
    <div class="weui-cells__title">基本信息</div>
    <div class="weui-cells weui-cells_form" id='firstform'>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label" id="startTime" style="width: 200px">选课开始时间:</label></div>
            <div class="weui-cell__bd"> <input class="weui-input" type="text" id="sTime" th:value="${startTime}" readonly="readonly"/> </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label" id="endTime" style="width: 200px">选课结束时间:</label></div>
            <div class="weui-cell__bd"> <input class="weui-input" type="text" id="eTime" th:value="${endTime}" readonly="readonly"/> </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label" id="zoneTeach" style="width: 200px">授课老师:</label></div>
            <div class="weui-cell__bd"> <input class="weui-input" type="text" id="teacher" th:value="${teacherName}" readonly="readonly"/> </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label" id="zoneName" style="width: 200px" >课程名称:</label></div>
            <div class="weui-cell__bd"> <input class="weui-input" type="text" id="courseName" th:value="${name}" readonly="readonly" /> </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label" id="zoneDesc" style="width: 200px">课程介绍:</label></div>
            <div class="weui-cell__bd">
                <label for="courseDesc">
                    <textarea class="weui-textarea" id="courseDesc" readonly="readonly" th:inline="text" style="height: 200px">[[${des}]]</textarea>
                </label>
            </div>
            <!--            <div class="weui-cell__bd"> <input class="weui-input" type="text" id="courseDesc" th:value="${des}" readonly="readonly" style="height: 100px;"/> </div>-->
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label" id="classTime" style="width: 200px">上课时间:</label></div>
            <div class="weui-cell__bd"> <input class="weui-input" type="text" id="cTime" th:value="${classTime}" readonly="readonly" /> </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label" id="classPlace" style="width: 200px">上课地址:</label></div>
            <div class="weui-cell__bd"> <input class="weui-input" type="text" id="cPlace" th:value="${place}" readonly="readonly" /> </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label" id="classNum" style="width: 200px">班级最多人数:</label></div>
            <div class="weui-cell__bd"> <input class="weui-input" type="text" id="maxNum" th:value="${maxNum}" readonly="readonly" /> </div>
        </div>
    </div>
    <div class="page_body">
        <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="submit">立即报名</a>
            <a href="/course/course_step1" class="weui-btn weui-btn_default" id="backhouse" style="background-color: #f8f8f8;color:#333">返回</a>
<!--        <a href="/subscribe/subscribe_wc_time" class="weui-btn weui-btn_default" id="backtime" style="background-color: #f8f8f8;color:#333">返回按时间选择</a> &ndash;&gt;-->
    </div>
</body>

</html>