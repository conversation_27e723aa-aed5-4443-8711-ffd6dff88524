<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0"/>
    <title>请假记录详情</title>
    <link rel="stylesheet" href="/ui/css/weui.min.css"/>
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css"/>
    <link rel="stylesheet" href="/ui/css/main.css"/>
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
<!--    <script src="/app/time/detailedAbsenceRecord.js"></script>-->
    <script th:src="${'/app/time/detailedAbsenceRecord.js?t='+timestamp}"></script>

</head>
<style>
    /* 图片容器：每行 3 张 */
    .image-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: flex-start;
    }

    /* 单个图片项 */
    .image-item {
        width: calc(33.33% - 10px);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* 图片样式 */
    .preview-image {
        width: 100%;
        height: auto;
        max-width: 100px;
        max-height: 100px;
        border-radius: 5px;
        cursor: pointer;
        object-fit: cover;
    }

    /* 预览大图样式 */
    .weui-gallery__img {
        width: 100%;
        height: 100%;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
    }
</style>
<body class="page">
<div class="weui-cells">
    <input id="uid" type="hidden" th:value="${uid}"/>
    <input id="infoid" type="hidden" th:value="${infoid}"/>
    <input id="imgPath" type="hidden" th:value="${imgPath}"/>
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>姓名</p>
        </div>
        <div class="weui-cell__ft" id="infoname" th:text="${infoname}"></div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>开始时间</p>
        </div>
        <div class="weui-cell__ft" id="starttime" th:text="${starttime}"></div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>结束时间</p>
        </div>
        <div class="weui-cell__ft" id="endtime" th:text="${endtime}"></div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>请假类型</p>
        </div>
        <div class="weui-cell__ft" id="leavetypename" th:text="${absencetypename}"></div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>请假天数</p>
        </div>
        <div class="weui-cell__ft" id="day" th:text="${day}"></div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">续假时长</label></div>

        <!--<div class="weui-cell__ft" id="day" th:text="${day}"></div>-->
        <input class="weui-input" type="text" id="xvjiaday" th:text="${xvjiaday}" />
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">历年超/缺勤班数</label></div>

        <!--<div class="weui-cell__ft" id="day" th:text="${day}"></div>-->
        <input class="weui-input" type="text" id="linianday" th:text="${linianday}"/>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">本年超/缺勤班数</label></div>

        <!--<div class="weui-cell__ft" id="day" th:text="${day}"></div>-->
        <input class="weui-input" type="text" id="bennianday" th:text="${bennianday}"/>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__bd" style="white-space:nowrap;">
            <p>请假原因</p>
        </div>
        <div class="weui-cell__ft" id="reason" th:text="${reason}"></div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <p>审核状态</p>
        </div>
        <div class="weui-cell__ft">
            <p style="color:red;" id="disaduit" th:if="${status == 0}">审核中</p>
        </div>
        <div class="weui-cell__ft">
            <p style="color:red;" id="disaduit" th:if="${status == 2}">已取消</p>
        </div>
        <div class="weui-cell__ft">
            <p style="color:#0bb20c;" id="agree" th:if="${status == 1}">同意</p>
        </div>
        <div class="weui-cell__ft">
            <p style="color:red;" id="disagree" th:if="${status == -1}">不同意</p>
        </div>
    </div>
    <div class="weui-cell" >
        <div class="weui-cell__bd">
            <p>附件照片</p>
        </div>

    </div>
    <!-- 图片容器 -->
    <div id="imageContainer" class="image-grid"></div>

    <!-- 预览大图 -->
    <div id="gallery" class="weui-gallery" style="display: none;">
        <span class="weui-gallery__img" id="galleryImg"></span>
        <div class="weui-gallery__opr">
            <a href="javascript:" class="weui-gallery__del" id="closeGallery">关闭</a>
        </div>
    </div>

    <!--<div class="weui-cell" th:if="${remark}">-->
    <!--    <div class="weui-cell__bd" style="white-space:nowrap;">-->
    <!--        <p>审核备注</p>-->
    <!--    </div>-->
    <!--    <div class="weui-cell__ft" id="remarked" th:text="${remark}"></div>-->
    <!--</div>-->

    <div class="weui-cells">
        <div class="weui-cells__title">审核记录</div>
        <div id="auditRecord"></div>
    </div>

<!--    <table th:if="${status == 1}">-->
<!--        <div class="weui-cell">-->
<!--            <div class="weui-cell__bd" style="white-space:nowrap;">-->
<!--                <p>销假状态</p>-->
<!--            </div>-->
<!--            <div class="weui-cell__ft">-->
<!--                <p style="color:red;" id="isfinish" th:if="${isfinish == 0}">未销假</p>-->
<!--            </div>-->
<!--            <div class="weui-cell__ft">-->
<!--                <p style="color:#0bb20c;" id="isfinish" th:if="${isfinish == 1}">已销假</p>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="weui-cell">-->
<!--            <div class="weui-cell__bd" style="white-space:nowrap;">-->
<!--                <p>销假时间</p>-->
<!--            </div>-->
<!--            <div class="weui-cell__ft" id="leavefinishtime" th:text="${leavefinishtime}"></div>-->
<!--        </div>-->
<!--    </table>-->
</div>
<div class="page_body">
    <a href="javascript:void(0);" class="weui-btn weui-btn_warn" id="cancel" th:if="${status == 0}">取消请假</a>
<!--    <a href="javascript:void(0);" class="weui-btn weui-btn_primary" id="approvalrecord">查看审批记录</a>-->
    <a href="/time/absencerecord" class="weui-btn weui-btn_default" style="background-color: #f8f8f8;color:#333" th:if="${usertype == 3}">返回菜单</a>
</div>

</body>

</html>