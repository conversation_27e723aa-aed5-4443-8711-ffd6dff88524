<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    <title>家长签到</title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <style>
        body {
            font-family: 'Arial', sans-serif;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.6;
            display: flex;
            min-height: 100vh;
            margin: 0;
        }

        .container {
            background-color: white;
            width: 100%;
            max-width: 480px;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .container:hover {
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        h2 {
            font-size: 28px;
            color: #333;
            font-weight: 600;
            text-align: center;
            margin-bottom: 15px;
            transition: color 0.3s ease;
        }

        h2:hover {
            color: #4CAF50;
        }

        .section-title {
            font-size: 18px;
            color: #444;
            margin-top: 20px;
            font-weight: 600;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .weui-btn-primary {
            border-radius: 50%;
            width: 120px;
            height: 120px;
            font-size: 18px;
            line-height: 120px;
            text-align: center;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transition: background-color 0.3s, transform 0.3s, box-shadow 0.3s ease;
        }

        .weui-btn-primary:hover {
            background-color: #45a049;
            transform: translateY(-4px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .weui-btn-primary:active {
            transform: translateY(2px);
            box-shadow: none;
        }

        #statusText {
            font-size: 20px;
            font-weight: bold;
            color: #444;
            text-align: center;
            margin-top: 20px;
            transition: color 0.3s ease;
        }

        .checked {
            background-color: #007bff;
            color: white;
        }

        .record-container {
            margin-top: 30px;
            max-height: 300px;
            overflow-y: auto;
            padding-top: 10px;
        }

        .record-item {
            padding: 12px;
            margin-bottom: 12px;
            background-color: #f1f1f1;
            border-radius: 10px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            color: #444;
            transition: background-color 0.3s ease;
        }

        .record-item:hover {
            background-color: #e0e0e0;
        }

        .record-item:last-child {
            margin-bottom: 0;
        }

        .record-item span {
            font-size: 14px;
            color: #888;
        }

        .loading {
            text-align: center;
            font-size: 18px;
            color: #777;
            display: none;
        }

        .loading.show {
            display: block;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
                border-radius: 12px;
            }

            .weui-btn-primary {
                width: 100px;
                height: 100px;
                font-size: 16px;
                line-height: 100px;
            }

            h2 {
                font-size: 24px;
            }

            .section-title {
                font-size: 16px;
            }

            #statusText {
                font-size: 18px;
            }
        }
    </style>
</head>

<body>
<div class="container">
    <input type="hidden" id="signInCfg" th:value="${signInCfg}" />

    <div class="section-title">签到时间</div>
    <div id="list"></div>

    <div class="section-title">签到状态</div>
    <div class="weui-cells">
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p id="statusText">未签到</p>
            </div>
        </div>
    </div>

    <div class="weui-btn-area">
        <button class="weui-btn weui-btn_primary" id="checkInBtn">签到</button>
    </div>

    <div class="section-title">签到记录</div>
    <div class="record-container" id="records">
        <div class="loading" id="loading">加载中...</div>
    </div>
</div>

<script src="/ui/js/jquery-2.1.4.js"></script>
<script src="/ui/js/jquery-weui.min.js"></script>
<script src="/ui/js/main.js"></script>
<!--<script src="/app/time/timesign.js"></script>-->
<script th:src="${'/app/time/timesign.js?t='+timestamp}"></script>

</body>

</html>
