<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    <title></title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <!--    <script src="/app/time/absencefromduty.js"></script>-->
    <script th:src="${'/app/time/absencefromduty.js?t='+timestamp}"></script>
    <script src="/ui/js/jweixin-1.6.0.js"></script>
    <style>
        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 10px 0;
        }

        .preview-item {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .preview-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
        }

        .delete-btn {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 18px;
            font-size: 14px;
            cursor: pointer;
            z-index: 2;
        }

        .upload-box {
            width: 80px;
            height: 80px;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            transition: all 0.3s;
        }

        .upload-box:hover {
            border-color: #07c160;
            color: #07c160;
        }

        .image-preview-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.9);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .preview-large {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 40px;
            cursor: pointer;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }
    </style>
</head>

<body>
<input id="resourcedomain" type="hidden" th:value="${resourcedomain}" />
<input id="peoples" type="hidden" th:value="${people}" />
<input id="usertype" type="hidden" th:value="${usertype}" />

<div class="weui-cells__title">选择起止时间</div>
<div class="weui-cells weui-cells_form">
    <div class="weui-cell" th:if="${usertype== 1}">
        <div class="weui-cell__bd">
            <input class="weui-input" id="people" type="text" th:value="${username}"
                   th:attr="data-values=${userDocid}" readonly="readonly"></input>
        </div>
    </div>
    <div class="weui-cell" th:if="${usertype==2 or usertype == 3}">
        <div class="weui-cell__hd"><label class="weui-label">姓名</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" id="people" placeholder="" type="text" th:value="${username}"
                   th:attr="data-values=${userDocid}" readonly="readonly"></input>
        </div>
    </div>
    <!-- 是否续假 -->
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">是否续假</label></div>
        <div class="weui-cell__bd">
            <select id="sfxvjia" class="weui-select">
                <option value="0" checked>否</option>
                <option value="1">是</option>
            </select>
        </div>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="" class="weui-label">开始时间</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="datetime-local" th:value="${starttime}" id="starttime"> </input>
        </div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="" class="weui-label">结束时间</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="datetime-local" th:value="${endtime}" id="endtime"> </input>
        </div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">请假类型</label></div>
        <div class="weui-cell__bd">
            <select class="weui-select" id="leaveschool">
                <option disabled="disabled" selected="selected">请选择请假类型</option>
            </select>
        </div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">请假时长</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="text" id="applyDay" placeholder="请输入请假时长"
                   oninput="validateDecimal(this)" />
        </div>
        <div class="weui-cell__bd">
            <select id="timeUnit" class="weui-select">
                <option value="day">天</option>
                <option value="hour">小时</option>
            </select>
        </div>
    </div>
    <!-- 续假时长（默认隐藏） -->
    <div class="weui-cell" id="xvjiaCell" style="display: none;">
        <div class="weui-cell__hd"><label class="weui-label">续假时长</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="text" id="xvjiaDay" placeholder="请输入续假时长" oninput="validateDecimal(this)" />
        </div>
        <div class="weui-cell__bd">
            <select id="xjdayUnit" class="weui-select">
                <option value="day">天</option>
                <option value="hour">小时</option>
            </select>
        </div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">历年超/缺勤班数</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="text" id="qunianshu" placeholder="请输入历年超/缺勤班数"
                   oninput="validateDecimal(this)" />
        </div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">本年超/缺勤班数</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" type="text" id="bennianshu" placeholder="请输入本年超/缺勤班数"
                   oninput="validateDecimal(this)" />
        </div>
    </div>
</div>
<!-- 审核人选择区域 -->
<div class="weui-cells__title">选择审核人</div>
<div class="weui-cells">
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">审核人类型</label></div>
        <div class="weui-cell__bd">
            <select class="weui-select" id="psitoin">
                <option disabled="disabled" selected="selected">请选择审核人类型</option>
            </select>
        </div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">审核人姓名</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" id="aduitname" placeholder="请输入需要搜索的审核人姓名" type="text" ></input>
        </div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label class="weui-label">选择审核人</label></div>
        <div class="weui-cell__bd">
            <select class="weui-select" id="resultSelect">
                <option value="">请选择审核人</option>
            </select>
        </div>
    </div>
    <div class="weui-cells__title">上传图片</div>
    <div class="weui-cells weui-cells_form">
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <div class="preview-container" id="previewContainer">
                    <div class="upload-box" id="uploadBox">
                        <i class="weui-icon-add"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="weui-cells__title">备注</div>
    <div class="weui-cells">
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <textarea class="weui-textarea" placeholder="请输入文本" rows="3" id="reason"></textarea>
            </div>
        </div>
    </div>
    <div class="page_body">
        <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="submit">提交</a>
        <a href="/" class="weui-btn weui-btn_default" style="background-color: #f8f8f8;color:#333">返回</a>
    </div>
</div>
</body>

</html>