<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    <title>请假审核</title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <script th:src="${'/app/time/auditabsence.js?t='+timestamp}"></script>

<!--    <script src="/app/time/auditabsence.js"></script>-->
</head>
<style>
    /* 图片容器：每行 3 张 */
    .image-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: flex-start;
    }

    /* 单个图片项 */
    .image-item {
        width: calc(33.33% - 10px);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* 图片样式 */
    .preview-image {
        width: 100%;
        height: auto;
        max-width: 100px;
        max-height: 100px;
        border-radius: 5px;
        cursor: pointer;
        object-fit: cover;
    }

    /* 预览大图样式 */
    .weui-gallery__img {
        width: 100%;
        height: 100%;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
    }
</style>
<body class="page">
    <div class="weui-cells">
        <input id="uid" type="hidden" th:value="${uid}" />
        <input id="infoid" type="hidden" th:value="${infoid}" />
        <input id="imgPath" type="hidden" th:value="${imgPath}"/>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>姓名</p>
            </div>
            <div class="weui-cell__ft" id="infoname" th:text="${infoname}"></div>

        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label for="" class="weui-label">开始时间</label></div>

            <!--<div class="weui-cell__ft" id="starttime" th:text="${starttime}"></div>-->
            <input class="weui-input" type="datetime-local" th:value="${starttime}" id="starttime"> </input>

        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label for="" class="weui-label">结束时间</label></div>

            <!--<div class="weui-cell__ft" id="endtime" th:text="${endtime}"></div>-->
            <input class="weui-input" type="datetime-local" th:value="${endtime}" id="endtime"> </input>

        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">是否续假</label></div>

            <div class="weui-cell__ft" id="sfxvjia" th:text="${sfxvjia}"></div>

        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>请假类型</p>
            </div>
            <div class="weui-cell__ft" id="leavetypename" th:text="${absencetypename}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">请假时长</label></div>

            <!--<div class="weui-cell__ft" id="day" th:text="${day}"></div>-->
            <input class="weui-input" type="text" id="applyDay" th:value="${day}" placeholder="请输入请假时长"
                   oninput="validateDecimal(this)" />
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">续假时长</label></div>

            <!--<div class="weui-cell__ft" id="day" th:text="${day}"></div>-->
            <input class="weui-input" type="text" id="xvjiaday" th:value="${xvjiaday}" placeholder="请输入请假时长"
                   oninput="validateDecimal(this)" />
        </div>

        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">历年超/缺勤班数</label></div>

            <!--<div class="weui-cell__ft" id="day" th:text="${day}"></div>-->
            <input class="weui-input" type="text" id="linianday" th:value="${linianday}"
                   oninput="validateDecimal(this)" />
        </div>

        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">本年超/缺勤班数</label></div>

            <!--<div class="weui-cell__ft" id="day" th:text="${day}"></div>-->
            <input class="weui-input" type="text" id="bennianday" th:value="${bennianday}"
                   oninput="validateDecimal(this)" />
        </div>

        <div class="weui-cell" >
            <div class="weui-cell__bd" style="white-space:nowrap;">
                <p>请假原因</p>
            </div>
            <div class="weui-cell__ft" id="reason" th:text="${reason}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>审核状态</p>
            </div>
            <div class="weui-cell__ft">
                <p style="color:red;" id="disaduit" th:if="${status == 0 and auditcondition == 1}">审核中 您已审核完成</p>
            </div>
            <div class="weui-cell__ft">
                <p style="color:red;" id="disaduit" th:if="${status == 0 and auditcondition == 0}">审核中 您未审核</p>
            </div>
            <div class="weui-cell__ft">
                <p style="color:red;" id="disaduit" th:if="${status == 1  }">单据审批通过 您已审核完成</p>
            </div>
            <div class="weui-cell__ft">
                <p style="color:red;" id="disaduit" th:if="${status == -1  }">单据审批未通过 </p>
            </div>
            <div class="weui-cell__ft">
                <p style="color:red;" id="disaduit" th:if="${status == 2  }"> 当前审批单据已取消</p>
            </div>
        </div>
        <div class="weui-cell" th:if="${(warremark != '' and warremark !=null ) and status != 2}">
            <div class="weui-cell__bd" style="white-space:nowrap;">
                <p>审核备注</p>
            </div>
            <div class="weui-cell__ft" id="remark" th:text="${warremark}"></div>
        </div>
        <div class="weui-cells__title" th:if="${(warremark != '' and warremark !=null ) and status != 2}">选择审核人</div>
        <div class="weui-cell" th:if="${status == 0 and auditcondition == 0}">
            <div class="weui-cell__hd"><label class="weui-label">审核人类型</label></div>
            <div class="weui-cell__bd">
                <select class="weui-select" id="psitoin">
                    <option disabled="disabled" selected="selected">请选择审核人类型</option>
                </select>
            </div>
        </div>
        <div class="weui-cell" th:if="${status == 0 and auditcondition == 0}">
            <div class="weui-cell__hd"><label class="weui-label">审核人姓名</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" id="aduitname" placeholder="请输入需要搜索的审核人姓名" type="text" ></input>
            </div>
        </div>
        <div class="weui-cell" th:if="${status == 0 and auditcondition == 0}">
            <div class="weui-cell__hd"><label class="weui-label">选择审核人</label></div>
            <div class="weui-cell__bd">
                <select class="weui-select" id="resultSelect">
                    <option value="">请选择审核人</option>
                </select>
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>附件照片</p>
            </div>
        </div>
        <!-- 图片容器 -->
        <div id="imageContainer" class="image-grid"></div>

        <!-- 预览大图 -->
        <div id="gallery" class="weui-gallery" style="display: none;">
            <span class="weui-gallery__img" id="galleryImg"></span>
            <div class="weui-gallery__opr">
                <a href="javascript:" class="weui-gallery__del" id="closeGallery">关闭</a>
            </div>
        </div>


    </div>


    <div class="weui-cells">
        <div class="weui-cells__title">审核记录</div>
        <div id="auditRecord"></div>
    </div>

    <div class="weui-cells__title" th:if="${auditcondition == 0}">审核备注</div>
    <div class="weui-cells weui-cells_form" th:if="${auditcondition == 0}">
        <div class="weui-cell">
            <div class="weui-cell__bd"> <textarea class="weui-textarea" placeholder="输入原因" rows="3" id="auditremark"></textarea></div>
        </div>
    </div>


    <div class="page_body">
        <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="save" th:if="${auditcondition == 0}">保存修改</a>
        <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="bagree" th:if="${auditcondition == 0}">同意</a>
        <a href="javascript:void(0);" class="weui-btn weui-btn_warn" id="bdisagree" th:if="${auditcondition == 0}">不同意</a>
        <a href="/time/auditabsencelist" class="weui-btn weui-btn_default" style="background-color: #f8f8f8;color:#333">返回菜单</a>
    </div>

</body>

</html>