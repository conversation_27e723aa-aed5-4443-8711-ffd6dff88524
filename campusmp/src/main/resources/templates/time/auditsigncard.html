<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    <title>缺卡审核</title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
<!--    <script src="/app/time/auditsigncard.js"></script>-->
    <script th:src="${'/app/time/auditsigncard.js?t='+timestamp}"></script>

</head>

<body class="page">
    <div class="weui-cells">
        <input id="uid" type="hidden" th:value="${uid}" />
        <input id="infoid" type="hidden" th:value="${infoid}" />
        <input id="createCardDt" type="hidden" th:value="${createDt}" />
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>姓名</p>
            </div>
            <div class="weui-cell__ft" id="infoname" th:text="${infoname}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>缺卡时间</p>
            </div>
            <div class="weui-cell__ft" id="signtime" th:text="${signtime}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>提交时间</p>
            </div>
            <div class="weui-cell__ft" id="createDt" th:text="${createDt}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>上下班类型</p>
            </div>
            <div class="weui-cell__ft" id="worktype" th:text="${worktypename}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>缺卡类型</p>
            </div>
            <div class="weui-cell__ft" id="ftype" th:text="${ftypename}"></div>
        </div>
        <!--<div class="weui-cell">-->
        <!--    <div class="weui-cell__bd">-->
        <!--        <p>结束时间</p>-->
        <!--    </div>-->
        <!--    <div class="weui-cell__ft" id="endtime" th:text="${endtime}"></div>-->
        <!--</div>-->
        <!--<div class="weui-cell">-->
        <!--    <div class="weui-cell__bd">-->
        <!--        <p>缺勤类型</p>-->
        <!--    </div>-->
        <!--    <div class="weui-cell__ft" id="leavetypename" th:text="${absencetypename}"></div>-->
        <!--</div>-->
        <div class="weui-cell" >
            <div class="weui-cell__bd" style="white-space:nowrap;">
                <p>缺卡原因</p>
            </div>
            <div class="weui-cell__ft" id="reason" th:text="${reason}"></div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <p>审核状态</p>
            </div>
            <div class="weui-cell__ft">
                <p style="color:red;" id="disaduit" th:if="${status == 0 and auditcondition == 1}">审核中 您已审核完成</p>
            </div>
            <div class="weui-cell__ft">
                <p style="color:red;" id="disaduit" th:if="${status == 0 and auditcondition == 0}">审核中 您未审核</p>
            </div>
            <div class="weui-cell__ft">
                <p style="color:red;" id="disaduit" th:if="${status == 1  }">单据审批通过 您已审核完成</p>
            </div>
            <div class="weui-cell__ft">
                <p style="color:red;" id="disaduit" th:if="${status == -1  }">单据审批未通过 </p>
            </div>
            <div class="weui-cell__ft">
                <p style="color:red;" id="disaduit" th:if="${status == 2  }"> 当前审批单据已取消</p>
            </div>
        </div>
<!--        <div class="weui-cell">-->
<!--            <div class="weui-cell__hd"><label class="weui-label">审核人类型</label></div>-->
<!--            <div class="weui-cell__bd">-->
<!--                <select class="weui-select" id="psitoin">-->
<!--                    <option disabled="disabled" selected="selected">请选择审核人类型</option>-->
<!--                </select>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="weui-cell">-->
<!--            <div class="weui-cell__hd"><label class="weui-label">审核人姓名</label></div>-->
<!--            <div class="weui-cell__bd">-->
<!--                <input class="weui-input" id="aduitname" placeholder="" type="text" ></input>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="weui-cell">-->
<!--            <div class="weui-cell__hd"><label class="weui-label">选择审核人</label></div>-->
<!--            <div class="weui-cell__bd">-->
<!--                <select class="weui-select" id="resultSelect">-->
<!--                    <option value="">请选择审核人</option>-->
<!--                </select>-->
<!--            </div>-->
<!--        </div>-->
        <div class="weui-cell" th:if="${(warremark != '' and warremark !=null ) and status != 2}">
            <div class="weui-cell__bd" style="white-space:nowrap;">
                <p>审核备注</p>
            </div>
            <div class="weui-cell__ft" id="remark" th:text="${warremark}"></div>
        </div>
    </div>


    <div class="weui-cells">
        <div class="weui-cells__title">缺勤记录</div>
        <div id="auditRecord"></div>
    </div>

    <div class="weui-cells__title" th:if="${auditcondition == 0}">审核备注</div>
    <div class="weui-cells weui-cells_form" th:if="${auditcondition == 0}">
        <div class="weui-cell">
            <div class="weui-cell__bd"> <textarea class="weui-textarea" placeholder="输入原因" rows="3" id="auditremark"></textarea></div>
        </div>
    </div>


    <div class="page_body">
        <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="bagree" th:if="${auditcondition == 0}">同意</a>
        <a href="javascript:void(0);" class="weui-btn weui-btn_warn" id="bdisagree" th:if="${auditcondition == 0}">不同意</a>
        <a href="javascript:void(0);" id="backBtn" class="weui-btn weui-btn_default">返回</a>
    </div>

</body>

</html>