<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0"/>
    <meta http-equiv="pragma" content="no-cache"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <title>打卡系统</title>
    <link rel="stylesheet" href="/ui/css/weui.min.css"/>
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css"/>
    <link rel="stylesheet" href="/ui/css/main.css"/>
    <style>
        .photo-container {
            margin: 15px 0;
            text-align: center;
        }
        .photo-title {
            font-weight: bold;
            margin-bottom: 10px;
            display: block;
        }
        #processedImage {
            max-width: 100%;
            max-height: 200px;
            display: none;
            margin: 10px auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .photo-processing {
            padding: 15px;
            background: #f5f5f5;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
        }
        .processing-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0,0,0,0.1);
            border-radius: 50%;
            border-top-color: #09BB07;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .photo-error {
            color: #F76260;
            padding: 10px;
            background: #FFF3F3;
            border-radius: 4px;
            text-align: center;
        }
    </style>
</head>
<body class="page">
<!-- 隐藏字段 -->
<input type="hidden" id="appId" th:value="${appId}"/>
<input type="hidden" id="timeStamp" th:value="${timeStamp}"/>
<input type="hidden" id="nonceStr" th:value="${nonceStr}"/>
<input type="hidden" id="signature" th:value="${signature}"/>
<input id="uid" type="hidden" th:value="${uid}"/>
<input id="imgInfo" type="hidden" th:value="${imgInfo}"/>

<div class="weui-cells__title">职工详细信息</div>
<div class="weui-cells">
    <!-- 基本信息 -->
    <div class="weui-cell">
        <div class="weui-cell__bd"><p>姓名</p></div>
        <div class="weui-cell__ft" id="name" th:text="${name}"></div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__bd"><p>工号</p></div>
        <div class="weui-cell__ft" id="code" th:text="${code}"></div>
    </div>
    <div class="weui-cell">
        <div class="weui-cell__bd"><p>部门</p></div>
        <div class="weui-cell__ft" id="className" th:text="${orgName}"></div>
    </div>

    <!-- 照片预览区域 -->
    <div class="weui-cell">
        <div class="weui-cell__bd">
            <span class="photo-title">打卡照片</span>
            <div class="photo-container">
                <img id="processedImage" alt="打卡照片预览"/>
                <div id="photoPreview"></div>
            </div>
        </div>
    </div>

    <!-- 拍照按钮 -->
    <div class="weui-cell">
        <div class="weui-cell__bd"><p>上传照片</p></div>
        <div class="weui-cell__ft">
            <input id="photoInput" type="file" accept="image/*" capture="camera" style="display:none"/>
            <button id="uploadPhoto" class="weui-btn weui-btn_primary">拍摄照片</button>
        </div>
    </div>
</div>

<div class="page_body weui-footer">
    <a href="javascript:void(0);" class="weui-btn weui-btn_primary" id="returned">打卡</a>
    <a href="javascript:history.go(-1);" class="weui-btn weui-btn_default">返回</a>
</div>

<!-- JavaScript 文件 -->
<script src="/ui/js/jquery-2.1.4.js"></script>
<script src="/ui/js/jquery-weui.min.js"></script>
<script src="/ui/js/main.js"></script>
<script th:src="${'/app/time/sign_detail.js?t='+timestamp}"></script>
<script src="/ui/js/jweixin-1.6.0.js"></script>
</body>
</html>