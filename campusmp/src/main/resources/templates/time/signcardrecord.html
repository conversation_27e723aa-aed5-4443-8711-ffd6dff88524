<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    <title>缺卡记录</title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <script th:src="${'/app/time/signcardrecord.js?t='+timestamp}"></script>

</head>

<body class="page">
    <input id="peoples" type="hidden" th:value="${people}" />
    <input id="usertype" type="hidden" th:value="${usertype}" />
    <div class="weui-cells__title">选择缺卡时间</div>
    <div class="weui-cells weui-cells_form">
        <div class="weui-cell" th:if="${usertype==2 or usertype == 3}">
            <div class="weui-cell__hd"><label for="name" class="weui-label">人员</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" id="people" type="text" th:value="${username}" th:attr="data-values=${userDocid}" readonly="readonly"></input>
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label for="" class="weui-label">起始时间</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="datetime-local" th:value="${starttime}" id="starttime"> </input>
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label for="" class="weui-label">结束时间</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="datetime-local" th:value="${endtime}" id="endtime"> </input>
            </div>
        </div>
    </div>

    <div class="page_body">
        <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="select">搜索</a>
        <a href="/" class="weui-btn weui-btn_default" style="background-color: #f8f8f8;color:#333">返回</a>
    </div>


    <div class="weui-cells" id="list">
        <div class="weui-panel__hd" >缺卡记录</div>
    </div>

    <div class="weui-loadmore" id="loadmore">
        <i class="weui-loading"></i>
        <span class="weui-loadmore__tips">正在加载</span>
    </div>

</body>

</html>