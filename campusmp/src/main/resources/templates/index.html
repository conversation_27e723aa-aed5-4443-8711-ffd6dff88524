<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <script>
        $(document).ready(function() {
            var usertype = $("#usertype").val();
            var title = $("#title").val();
            // 设置标题
            mp.setTitle(title);
            function renderFunctions(data) {
                const appContainer = $('.apps-content');
                appContainer.empty(); // 清空以前的功能模块
                appContainer.append('<h2>全部应用</h2>')
                data.forEach(category => {
                    if (category.functions.length > 0) {
                        const module = $('<div class="feature-module"></div>');
                        category.functions.forEach(func => {
                            if (func.type.includes(usertype)) {
                                const item = $(`
                                    <div class="feature-item">
                                        <img src="${func.icon}" alt="${func.name}">
                                        <p>${func.name}</p>
                                    </div>
                                `);
                                item.click(() => window.location.href = func.url + "?t=" + Date.now());
                                module.append(item);
                            }
                        });
                        if (module.children().length > 0) {
                            appContainer.append('<hr style="border: 1px solid #ddd; width: 80%; margin: 20px auto;">\n')
                            appContainer.append(`<h3>${category.name}</h3>`);
                            appContainer.append(module);
                        }
                    }
                });
            }

            $('#addbind').click(function () {
                console.log("按钮点击事件被触发");
                $.window({
                    title: '新增绑定',
                    text: '输入其他学生姓名和学号',
                    fields: [
                        {
                            id: 'name',
                            allowBlank: false,
                            placeholder: '请输入姓名...'
                        },
                        {
                            id: 'code',
                            allowBlank: false,
                            placeholder: '请输入学号...'
                        },
                        {
                            id: 'passWord',
                            allowBlank: false,
                            placeholder: '请输入密码...'
                        },
                    ],
                    onOK: function (formvalues) {
                        console.log("表单值", formvalues);  // 打印表单数据
                        $.ajax({
                            url: "/weixin/addweixinbind",
                            type: "POST",
                            data: formvalues,
                            success: function (result) {
                                console.log("请求成功", result);  // 打印请求结果
                                if (result.success) {
                                    $.toast("绑定成功", function () {
                                        window.location.reload();
                                    });
                                } else {
                                    $.alert(result.msg, "系统提示");
                                }
                            },
                            error: function (xhr, status, error) {
                                console.error("请求失败:", error);
                                $.alert("请求失败，请稍后再试", "系统提示");
                            }
                        });
                    },
                    onCancel: function () {
                        console.log("操作取消");
                    }
                });
            });



            function renderHomeFunctions(data) {
                const featureContainer = $('.feature-commommodule');
                featureContainer.empty(); // 清空以前的功能模块
                    // if (category.functions.length > 0) {
                        const module = $('<div class="feature-module"></div>');
                    data.forEach(func => {
                            if (func.type.includes(usertype)) {
                                const item = $(`
                                    <div class="feature-item">
                                        <img src="${func.icon}" alt="${func.name}">
                                        <p>${func.name}</p>
                                    </div>
                                `);
                                item.click(() => window.location.href = func.url + "?t=" + Date.now());
                                module.append(item);
                            }
                        });
                featureContainer.append(module);
                    // }
            }
            var homedata = {};
            // 发送到后端
            $.ajax({
                url: '/ucenter/getinfo', // 替换为实际的 API 端点
                type: 'POST',
                async:false,
                contentType: 'application/json',
                success: function(response) {
                    homedata =response.data;
                    console.log('Success:', response);
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                }
            });

            //点击查看详情事件处理函数
            function viewNotificationDetails(id) {
                // 打开详情页逻辑（可以是弹出模态框或跳转页面等）
                alert('查看通知详情: ' + id); // 示例
            }

            function homeFunctions(data) {
                // 设置校徽和学校名称
                // $('#schoolLogo').attr('src', data.schoolLogo);
                // $('#schoolName').text(data.schoolName);
                //
                // // 设置学生信息
                // $('#studentPhoto').attr('src', data.studentPhoto);
                // $('#studentName').html('Hi~ ' + data.studentName);
                // $('#studentClass').text(data.studentClass);
                // $('#account').text('￥'+data.account);
                // $('#studentNumber').text('学号: ' + data.studentNumber);

                // 设置通知栏
                // 设置通知栏
                var notifications = data.notifications;

// 检查通知是否存在且数量大于0
                if (notifications && notifications.length > 0) {
                    // 对通知进行排序（假设每个通知对象有一个 date 属性，表示时间）
                    notifications.sort(function(a, b) {
                        return new Date(b.date) - new Date(a.date); // 按时间从新到旧排序
                    });

                    // 只取最近的五条
                    notifications = notifications.slice(0, 3);

                    var notificationHtml = '<div style="display: flex;justify-content: space-between"><h3 style="margin: 0; font-size: 20px;margin-top: 10px;">最新通知</h3><ul style="padding: 0; list-style-type: none;"><a href="/article/notificationrecords?t='+Date.now()+'"><p style="color: #c5c0c0;font-size: 13px;margin-top: 10px">查看更多></p></a></div>';

                    // 生成通知列表
                    notifications.forEach(function(notification) {
                        notificationHtml +=
                            '<li style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #ddd;">' +
                            '<a href="/article/pushparents?articleid='+notification.uid+'&type=1" style="text-decoration: none; color: #333; flex-grow: 1;" >' +
                            notification.title +
                            '</a>' +
                            '<span style="color: #999; font-size: 12px;">' + formatDate(notification.date) + '</span>' +
                            '</li>';
                    });

                    notificationHtml += '</ul>';
                    $('#notifications').html(notificationHtml);
                }

// 格式化日期函数（自定义格式）
                function formatDate(date) {
                    var d = new Date(date);
                    return d.getFullYear() + '-' + (d.getMonth() + 1).toString().padStart(2, '0') + '-' + d.getDate().toString().padStart(2, '0');
                }


            }
            if (homedata!=null){
                homeFunctions(homedata);
            }else {
                $('#notifications').hide()
            }

            var commonData = {};
            $.ajax({
                url: '/ucenter/getcommonmenu', // 替换为实际的 API 端点
                type: 'POST',
                async:false,
                contentType: 'application/json',
                success: function(response) {
                    commonData =response.data;
                    console.log('Success:', response);
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                }
            });
            renderHomeFunctions(commonData);


            // 切换页面内容
            $(".nav-item").click(function() {
                let navId = $(this).attr("id");

                if (navId === 'home') {
                    $(".home-content").show();
                    $(".apps-content").hide();
                    $(this).addClass("active").siblings().removeClass("active");
                } else if (navId === 'apps') {
                    $(".home-content").hide();
                    $(".apps-content").show();
                    $(this).addClass("active").siblings().removeClass("active");

                    // 应用功能数据
                    var data = [];
                    // 发送到后端
                    $.ajax({
                        url: '/ucenter/gettotalmenu', // 替换为实际的 API 端点
                        type: 'POST',
                        async:false,
                        contentType: 'application/json',
                        success: function(response) {
                            data =response.data;
                            console.log('Success:', response);
                        },
                        error: function(xhr, status, error) {
                            console.error('Error:', error);
                        }
                    });
                    renderFunctions(data);
                } else if (navId === 'profile') {
                    window.location.href = '/ucenter?t=' + Date.now();
                }
            });

            // 默认显示首页
            $(".home-content").show();
            $(".apps-content").hide();
            $(".nav-item#home").addClass("active");
        });
    </script>
    <style>
        /* 通用样式 */
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #ffffff;
        }

        h2 {
            text-align: center;
            margin-top: 20px;
        }

        h3 {
            font-size: 18px;
            color: #333;
            /*margin: 20px 0 10px;*/
            text-align: left;
            margin-left: 20px;
        }

        .app-container {
            /*width: 90%;*/
            /*margin: 0 auto;*/
            background-color: #fff;
            padding: 0px 0px 60px 0px;
            border-radius: 10px;
            /*margin-bottom: 60px;*/
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .feature-module {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            /*margin: 20px 0;*/
            /*box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.1);*/
            background-color: #FFFFFF;
            border-radius: 5px;
            padding: 0px 10px 10px 10px;
        }

        .feature-item {
            text-align: center;
            width: 25%; /* 每行4个 */
        }

        .feature-item img {
            width: 50px;
            height: 50px;
            margin-bottom: 5px;
            margin-top:27px;
        }

        .feature-item p {
            font-size: 14px;
            color: #333;
            margin-bottom:15px;
        }

        /* 底部导航栏样式 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background-color: #fff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.1);
            /*z-index: 1000;*/
        }

        .nav-item {
            text-align: center;
            flex: 1;
            cursor: pointer;
        }

        .nav-item img {
            width: 30px;
            height: 30px;
        }

        .nav-item p {
            font-size: 12px;
            color: #333;
        }

        .nav-item.active p {
            color: #007aff; /* 活动状态颜色 */
        }
        .nav-item.active img {
            color: #007aff; /* 活动状态颜色 */
        }

        /* 设置页面内容底部空隙，避免被导航栏遮挡 */
        .app-container {
            margin-bottom: 80px;
        }

        /* 顶部用户信息样式 */
        .user-info {
            background-color: #00a165;
            color: #fff;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .user-info img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
        }

        .ad-banner img {
            width: 100%;
            border-radius: 10px;
        }

        .school-header {
            /*background-color: #eaf4ff;*/
            padding: 20px 20px 0px 20px;
            display: flex;
            align-items: center;
            /*justify-content: center;*/
            position: relative;
        }

        .school-header img {
            width: 60px;
            height: 60px;
            margin-left: 10px;
            border-radius: 50%
        }

        .school-header h1 {
            font-size: 30px;
            color: #ffffff;
            margin-left: 20px;
            font-weight: normal;
        }

        .student-info-card {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px auto;
            width: 80%;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: flex-end;
            flex-direction: column;
            align-content: center;
            flex-wrap: wrap;
        }

        .student-info-card img {
            width: 112.5px;
            height: 150px;
            border-radius: 10px;
        }

        .student-info-details {
            min-width:210px;
            flex-grow: 1;
            margin-left: 15px;
            line-height:1.9;
        }

        .student-info-details h3 {
            font-size: 18px;
            margin: 0;
        }

        .student-info-details p {
            color: #666;
            margin: 5px 0 0;
        }

        .switch-student-btn {
            margin-top: 10px;
            border: 1px solid #007bff;
            border-radius: 5px;
            padding: 5px 15px;
            color: #007bff;
            text-align: center;
            cursor: pointer;
            min-width: 73px;
        }


        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
        }
        .modal-content {
            background-color: #fefefe;
            border: 1px solid #888;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }
        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

    </style>
</head>
<body>
<input type="hidden" th:value="${userinfo.usertype}" id="usertype"/>
<input type="hidden" th:value="${title}" id="title"/>
<input type="hidden" th:value="${isAlertPassWord}" id="isAlertPassWord"/>
<input type="hidden" th:value="${servicePaid}" id="servicePaid"/>

<!-- 顶部应用部分 -->
<div class="app-container">
    <div class="home-content">

        <!-- 顶部校徽和学校名称 -->
        <div style="background: linear-gradient(to bottom, rgb(116 161 206) 220px, rgb(255, 255, 255) 220px)">
            <div class="school-header">
                <img id="schoolLogo" th:src="${userInfo.schoolLogo}" alt="" >
                <h1 id="schoolName" th:text = "${userInfo.schoolName}"></h1>
            </div>

            <!-- 学生信息卡片 -->
            <div class="student-info-card">
                <div style="display: flex;align-items: center;justify-content: space-between;">
                    <img id="studentPhoto" th:src="${userInfo.studentPhoto}" alt="人脸建模"
                         onclick="window.location.href = '/face/image?t='+Date.now()"
                         onerror="this.src='/ui/img/index_default.png'">
                    <div class="student-info-details">
                        <h3  th:if="${userinfo.usertype == 2}" th:text = "${'Hi~ '+userInfo.studentName+'——家属'}"></h3>
                        <h3  th:if="${userinfo.usertype != 2}" th:text = "${'Hi~ '+userInfo.studentName}"></h3>
                        <p id="studentClass" th:text = "${userInfo.studentClass}"></p>
                        <p id="studentNumber" th:if="${userinfo.usertype == 2 or userinfo.usertype == 1}" th:text = "${'学号: '+userInfo.studentNumber}"></p>
                        <p id="workNumber" th:if="${userinfo.usertype == 3}" th:text = "${'工号: '+userInfo.studentNumber}"></p>
                        <p id="visitorMobile" th:if="${userinfo.usertype == 4}" th:text = "${'电话: '+userInfo.studentNumber}"></p>
                    </div>
                </div>

                <div>
                    <a href="javascript:void(0);" class="open-popup" data-target="#changeemp">
                        <div class="switch-student-btn">
                            ⇋切换绑定
                        </div>
                    </a>
                </div>

            </div>
        </div>


        <!-- 功能按钮部分 -->
        <h3 style="margin-left: 30px ;font-size: 20px">常用功能</h3>
        <div class="feature-commommodule"></div>

        <!-- 最新通知部分 -->
        <div id="notifications" style="margin: 15px auto; background-color: #fff; padding: 5px 15px 15px 25px;line-height:2.3;width: 85%; border-radius: 10px;box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.1);">

        </div>
    </div>

    <!-- 功能展示区域 -->
    <div class="apps-content"></div>
</div>
<!-- 底部导航栏 -->
<div class="bottom-nav">
    <div class="nav-item" id="home">
        <img src="/ui/img/home.png" alt="首页">
        <p>首页</p>
    </div>
    <div class="nav-item" id="apps">
        <img src="/ui/img/yinyong.png" alt="应用">
        <p>应用</p>
    </div>
    <div class="nav-item" id="profile">
        <img src="/ui/img/person.png" alt="个人中心">
        <p>个人中心</p>
    </div>
</div>
<div id="changeemp" class='weui-popup__container popup-bottom'>
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <div class="toolbar">
            <div class="toolbar-inner">
                <a href="javascript:;" class="picker-button close-popup">关闭</a>
                <h1 class="title">选择已绑定</h1>
            </div>
        </div>
        <div class="modal-content">
            <div class="weui-grids">
                <a class="weui-grid js_grid" th:each="collect,iterStat : ${userlist}" th:href="'/ucenter/newchangebind?uid='+${collect.uid}">
                    <div class="weui-grid__icon">
                        <img th:src="${collect.headimgurl}" alt="" style="border-radius: 50%;" />
                    </div>
                    <p class="weui-grid__label" th:text="${collect.name}"></p>
                </a>
                <a class="weui-grid js_grid" id="addbind" th:if="${isaddbind==1}">
                    <div class="weui-grid__icon">
                        <img src="/ui/img/add.png" alt="" style="border-radius: 50%;" />
                    </div>
                    <p class="weui-grid__label">新增绑定</p>
                </a>
            </div>
        </div>
    </div>
</div>
</body>
</html>
