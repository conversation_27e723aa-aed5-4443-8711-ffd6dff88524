<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
  <title>家属人脸采集</title>
  <link rel="stylesheet" href="/ui/css/weui.min.css" />
  <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
  <link rel="stylesheet" href="/ui/css/main.css" />
  <script src="/ui/js/jquery-2.1.4.js"></script>
  <script src="/ui/js/jquery-weui.min.js"></script>
  <script src="/ui/js/main.js"></script>
  <script src="/ui/js/exif.js"></script>
  <script th:src="${'/app/face/imageparent.js?t='+timestamp}"></script>
</head>

<body class="page">
  <input type="hidden" id="maxphotonums" th:value="${maxphotonums}" />
  <input type="hidden" id="uid" th:value="${uid}" />
  <input type="hidden" id="stuId2" th:value="${stuId2}" />
  <input type="hidden" id="parentid2" th:value="${parentid2}" />
  <input type="hidden" name="stuId" id="classname" th:value="${classname}" th:if="${classname}" />
  <input type="hidden" name="stuId" id="orgCode" th:value="${orgCode}" th:if="${orgCode}" />
  <div style="height:20px;"></div>
  <div style="width:50%;line-break: 100%; overflow: hidden; display: block; margin-left: auto;margin-right: auto;" id="resultImageContainer">
    <img th:if="${faceimg != ''}" style="width:100%;height: 100%;" th:src="${faceimg}" />
    <img th:if="${faceimg == ''}" style="width:100%;height: 100%;" src="/ui/img/faceimage.png" />
  </div>
  <div th:if="${isfail}" style="text-align:center;padding: 5px 0 0 0; color:red;">无法检测到完整的人脸模型，请重新上传</div>

  <div class="weui-cells weui-cells_form" id="uploadphotoC" style="display: none">
    <div class="weui-cell">
      <div class="weui-cell__bd">
        <div class="weui-uploader">
          <div class="weui-uploader__hd" >
            <p class="weui-uploader__title" th:text="${'点击选择或拍摄上传'+name+'的照片'}"></p>
            <div class="weui-uploader__info" th:if="${faceimg == ''}" th:text="${'0/'+maxphotonums}"></div>
            <div class="weui-uploader__info" th:if="${faceimg != ''}" th:text="${'1/'+maxphotonums}"></div>
          </div>
          <div class="weui-uploader__bd">
<!--            <ul class="weui-uploader__files" id="uploaderFilesC">-->
<!--              <li class="weui-uploader__file" th:if="${faceimg != ''}" th:attr="data-src=${faceimg},data-type=1,data-uid=${uid}" th:style="'background-image:url(' + ${faceimg} + ')'" id="photoC">-->
<!--              </li>-->
<!--            </ul>-->
            <div class="weui-uploader__input-box"  id="changeUploader">
              <form action="/face/submitChangeParentFace" method="post" enctype="multipart/form-data" id="changefaceform">
                <input type="hidden" name="orientation" id="orientationC" value="0" />
                <input type="hidden" name="visitorid" id="visitoridC" th:value="${visitorid}" />
                <input type="hidden" name="stuId" th:value="${stuId}" />
                <input type="hidden" name="name" th:value="${name}" />
                <input type="hidden" name="parentid" th:value="${parentid}" />
                <input type="hidden" name="uid" th:value="${uid}" />
                <input id="changeUploaderInput" name="faceimg" class="weui-uploader__input" type="file" accept="image/*" />
              </form>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="weui-cells weui-cells_form" id="uploadphoto" th:if="${faceimg == ''}">
    <div class="weui-cell">
      <div class="weui-cell__bd">
        <div class="weui-uploader">
          <div class="weui-uploader__hd" >
            <p class="weui-uploader__title" th:text="${'点击选择或拍摄上传'+name+'的照片'}"></p>
            <div class="weui-uploader__info" th:if="${faceimg == ''}" th:text="${'0/'+maxphotonums}"></div>
            <div class="weui-uploader__info" th:if="${faceimg != ''}" th:text="${'1/'+maxphotonums}"></div>
          </div>
          <div class="weui-uploader__bd" id ="list">
            <ul class="weui-uploader__files" id="uploaderFiles">
              <li class="weui-uploader__file" th:if="${faceimg != ''}" th:attr="data-src=${faceimg},data-type=1,data-uid=${uid}" th:style="'background-image:url(' + ${faceimg} + ')'" id="photo">
              </li>
            </ul>
            <div class="weui-uploader__input-box" th:if="${faceimg == ''}" id="uploaderInputbutton">
              <form action="/face/submitparentface" method="post" enctype="multipart/form-data" id="faceform">
                <input type="hidden" name="orientation" id="orientation" value="0" />
                <input type="hidden" name="visitorid" id="visitorid" th:value="${visitorid}" />
                <input type="hidden" name="stuId" id="stuId" th:value="${stuId}" />
                <input type="hidden" name="parentid" id="parentid" th:value="${parentid}" />
                <input type="hidden" name="name" id="name" th:value="${name}" />
                <input id="uploaderInput" name="faceimg" class="weui-uploader__input" type="file" accept="image/*" />
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <a href="javascript:void(0);" class="weui-btn weui-btn_warn" th:if="${faceimg != ''}" style="margin-top:20px;width: 80%;" id="modifyImage">修改照片</a>
  <a href="javascript:void(0);" class="weui-btn weui-btn_warn" th:if="${faceimg != ''}" style="margin-top:20px;width: 80%;" id="removeimg">删除</a>
  <a href="/" class="weui-btn weui-btn_default" style="margin-top:20px;width: 80%;" th:if="!${stuId}">返回首页</a>
  <a class="weui-btn weui-btn_default"  style="margin-top:20px;width: 80%;" th:if="${stuId!=null}" id="backstu">返回</a>
  <div class="weui-cells__tips">1.保持人脸正面、脸部轮廓清晰，无遮挡；</div>
  <div class="weui-cells__tips" style="color:red;">2.照片分辨率使用4：3比例,拍摄时手机保持垂直方向；</div>
  <div class="weui-cells__tips">3.部分手机拍照自动旋转90°，请选择相册照片上传或旋转至正常；</div>
  <div class="page_body">
    <a href="javascript:void(0);" class="weui-btn weui-btn_default" style="display:none;" id="Rotate">旋转</a>
  </div>
</body>

</html>