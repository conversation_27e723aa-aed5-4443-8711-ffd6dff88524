<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    <title></title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <script src="/app/askforleave/leaveform.js"></script>
</head>

<body>
    <input id="resourcedomain" type="hidden" th:value="${resourcedomain}" />
    <input id="peoples" type="hidden" th:value="${people}" />
    <input id="usertype" type="hidden" th:value="${usertype}" />
    <div class="weui-cells__title">选择起止时间</div>
    <div class="weui-cells weui-cells_form">
        <div class="weui-cell" th:if="${usertype== 1}">
            <div class="weui-cell__bd">
                <input class="weui-input" id="people" type="text" th:value="${username}"
                       th:attr="data-values=${userDocid}" readonly="readonly"></input>
            </div>
        </div>
        <div class="weui-cell" th:if="${usertype==2 or usertype == 3}">
            <div class="weui-cell__hd"><label for="name" class="weui-label">学生</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" id="people" placeholder="请选择学生" type="text" th:value="${username}"
                    th:attr="data-values=${userDocid}" readonly="readonly"></input>
            </div>
        </div>


        <div class="weui-cell">
            <div class="weui-cell__hd"><label for="" class="weui-label">起始时间</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="datetime-local" th:value="${starttime}" id="starttime"> </input>
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label for="" class="weui-label">结束时间</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="datetime-local" th:value="${endtime}" id="endtime"> </input>
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label for="" class="weui-label">离校方式</label></div>
            <div class="weui-cell__bd">
                <select class="weui-select" id="leaveschool">
                    <option disabled="disabled" selected="selected">请选择离校方式</option>
                    <option value="1">自行离校</option>
                    <option value="2">家长接回</option>
                    <!-- 添加更多选项 -->
                </select>
            </div>
        </div>
    </div>
    <div class="weui-cells__title">原因</div>
    <div class="weui-cells">
        <div class="weui-cell">
            <div class="weui-cell__bd">
                <textarea class="weui-textarea" placeholder="请输入文本" rows="3" id="reason"></textarea>
            </div>
        </div>
    </div>
    <div class="page_body">
        <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="submit">提交</a>
        <a href="javascript:history.back();" class="weui-btn weui-btn_default" style="background-color: #f8f8f8;color:#333">返回</a>
    </div>


</body>

</html>