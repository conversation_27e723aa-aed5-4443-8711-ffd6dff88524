<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    <title></title>
    <link rel="stylesheet" href="/ui/css/weui.min.css" />
    <link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
    <link rel="stylesheet" href="/ui/css/main.css" />
    <script src="/ui/js/jquery-2.1.4.js"></script>
    <script src="/ui/js/jquery-weui.min.js"></script>
    <script src="/ui/js/main.js"></script>
    <script th:src="${'/app/weixinbind.js?t='+timestamp}"></script>
    <script type="text/javascript">
    function time(o,wait) {     
        var $this = $(o);
        if (wait == 0) { 
            $(o).removeAttr("disabled");
            $(o).html("获取验证码");
            wait = 60; 
            return;
        } else {
            $(o).attr("disabled", "true");
            $(o).html("重新发送(" + wait + ")"); 
            wait--; 
            setTimeout(function() {  
                time(o,wait)  
            },  
            1000) 
        }  
    }

    function sendcode(bt,vc,mo){
            var vcode=$(vc).val();
            var mobile=$(mo).val();
            if(!(/^1[3|4|5|8][0-9]\d{4,8}$/.test(mobile))){
                $.alert("请填写正确的手机号", "系统提示");
                return;
            }
            time((bt),60);
            $.postAjax({
                url: "/weixin/sendmobilecode",
                data: {
                    vcode: vcode,
                    mobile:mobile
                },
                success: function (result, textStatus, jqXHR) {
                    if (result.success) {
                        $.toast("发送成功");                
                    } else {
                        $.alert(result.msg, "系统提示");
                    }
                }
            });
    }
    </script>
</head>

<body class="page">
    <input type="hidden" id="weixinid" th:value="${weixinid}" />
    <input type="hidden" id="apptype" th:value="${apptype}" />
    <input type="hidden" id="backurl" th:value="${backurl}" />
    <input type="hidden" id="headimgurl" th:value="${headimgurl}" />
    <input type="hidden" id="nickname" th:value="${nickname}" />
    <input type="hidden" id="bind" th:value="${bind}" />
    <input type="hidden" id="sex" th:value="${sex}" />
    <input type="hidden" id="isVerificationCode" th:value="${isVerificationCode}" />
    <input type="hidden" id="required" th:value="${required}" />

    <div class="weui-msg">
        <div class="weui-msg__icon-area">
            <i class="weui-icon-info weui-icon_msg"></i>
        </div>
        <div class="weui-msg__text-area">
            <h2 class="weui-msg__title">操作说明</h2>
            <p class="weui-msg__desc">请确认身份无误</p>
        </div>
    </div>

    <div id="choiceType" style="display:none;">
        <div class="weui-cells__title">选择身份类型：</div>
        <div class="weui-cells weui-cells_radio">
            <label class="weui-cell weui-check__label" for="usertype1" th:if="${apptype=='1'}">
                <div class="weui-cell__bd">
                    <p>我是学生</p>
                </div>
                <div class="weui-cell__ft">
                    <input type="radio" class="weui-check" name="usertype" id="usertype1" value="1" />
                    <span class="weui-icon-checked"></span>
                </div>
            </label>
            <label class="weui-cell weui-check__label" for="usertype2" th:if="${apptype=='1'}">
                <div class="weui-cell__bd">
                    <p>我是学生家长</p>
                </div>
                <div class="weui-cell__ft">
                    <input type="radio" name="usertype" class="weui-check" id="usertype2" value="2" />
                    <span class="weui-icon-checked"></span>
                </div>
            </label>
            <label class="weui-cell weui-check__label" for="usertype3">
                <div class="weui-cell__bd" th:if="${apptype=='1'}">
                    <p>我是在校教职工</p>
                </div>
                <div class="weui-cell__bd" th:if="${apptype=='2'}">
                    <p>我是在职职工</p>
                </div>
                <div class="weui-cell__ft">
                    <input type="radio" name="usertype" class="weui-check" id="usertype3" value="3" />
                    <span class="weui-icon-checked"></span>
                </div>
            </label>
            <label class="weui-cell weui-check__label" for="usertype4">
                <div class="weui-cell__bd">
                    <p>我是访客</p>
                </div>
                <div class="weui-cell__ft">
                    <input type="radio" name="usertype" class="weui-check" id="usertype4" value="4" />
                    <span class="weui-icon-checked"></span>
                </div>
            </label>
        </div>
    </div>

    <div class="weui-cells__title">填写身份验证信息：</div>
    <div class="weui-cells weui-cells_form" id="user1" style="display:none;">
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">姓名</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入本人姓名" id="user1_name" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">学号</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入本人学号" id="user1_code" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">密码</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="password" placeholder="请输入账号密码" id="user1_pwd" />
            </div>
        </div>
    </div>

    <div class="weui-cells weui-cells_form" id="user2" style="display:none;">
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">学生姓名<label style="color:red">*</label></label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入学生姓名" id="user2_name" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">学生学号<label style="color:red">*</label></label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="tel" placeholder="请输入学生学号" id="user2_code" />
            </div>
        </div>
        <div class="weui-cell" th:if="${required=='1'}">
            <div class="weui-cell__hd" ><label class="weui-label">家长姓名<label style="color:red">*</label></label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入家长姓名" id="user2_pName" />
            </div>
        </div>
        <div class="weui-cell" th:if="${required=='1'}">
            <div class="weui-cell__hd" ><label class="weui-label">家长手机号<label style="color:red">*</label></label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="tel" placeholder="请输入家长手机号" id="user2_pMobile" />
            </div>
        </div>
        <div class="weui-cell weui-cell_select weui-cell_select-after" th:if="${required=='1'}">
            <div class="weui-cell__hd">
                <label class="weui-label">与学生关系</label>
            </div>
            <div class="weui-cell__bd" th:if="${required=='1'}">
                <select class="weui-select" name="relation" id="relation">
                    <option value="父亲|1">父亲</option>
                    <option value="母亲|2">母亲</option>
                    <option value="爷爷|1">爷爷</option>
                    <option value="奶奶|2">奶奶</option>
                    <option value="外公|1">外公</option>
                    <option value="外婆|2">外婆</option>
                    <option value="其他|1">其他</option>
                </select>
            </div>
        </div>
<!--        <input type="hidden" name="gender" id="gender" value=""/>-->
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">密码<label style="color:red">*</label></label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入密码" id="user2_pwd" />
            </div>
        </div>

<!--        <div class="weui-cell">-->
<!--            <div class="weui-cell__hd"><label class="weui-label">新密码<label style="color:red">*</label></label></div>-->
<!--            <div class="weui-cell__bd">-->
<!--                <input class="weui-input" type="text" placeholder="请输入新密码" id="user2_newPwd" />-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="weui-cell">-->
<!--            <div class="weui-cell__hd"><label class="weui-label">手机号</label></div>-->
<!--            <div class="weui-cell__bd">-->
<!--                <input class="weui-input" type="tel" placeholder="请输入家长手机号" id="user2_linktel" />-->
<!--            </div>-->
<!--            <div class="weui-cell__ft" th:if="${isVerificationCode == '1'}">-->
<!--                <button class="weui-vcode-btn" id="b_vcode2" onclick="sendcode('#b_vcode2','#user2_vcode','#user2_linktel')">获取验证码</button>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="weui-cell" th:if="${isVerificationCode == '1'}">-->
<!--            <div class="weui-cell__hd"><label class="weui-label">验证码</label></div>-->
<!--            <div class="weui-cell__bd">-->
<!--            <input class="weui-input" type="number" placeholder="请输入验证码" id="user2_vcode" /> -->
<!--            </div>-->
<!--        </div>-->
    </div>

    <div class="weui-cells weui-cells_form" id="user3" style="display:none;">
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">姓名</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入本人姓名" id="user3_name" />
            </div>
        </div>
    	<div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">手机号</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入本人手机号" id="user3_mobile_simple" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">密码</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="password" placeholder="请输入账号密码" id="user3_pwd" />
            </div>
        </div>
<!--        <div class="weui-cell" th:if="${isVerificationCode == '1'}">-->
<!--            <div class="weui-cell__hd"><label class="weui-label">手机号</label></div>-->
<!--            <div class="weui-cell__bd">-->
<!--                <input class="weui-input" type="tel" placeholder="请输入本人手机号" id="user3_mobile" />-->
<!--            </div>-->
<!--            <div class="weui-cell__ft">-->
<!--                <button class="weui-vcode-btn" id="b_vcode3"  onclick="sendcode('#b_vcode3','#user3_vcode','#user3_mobile')">获取验证码</button>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="weui-cell" th:if="${isVerificationCode == '1'}">-->
<!--            <div class="weui-cell__hd"><label class="weui-label">验证码</label></div>-->
<!--            <div class="weui-cell__bd">-->
<!--            <input class="weui-input" type="number" placeholder="请输入验证码" id="user3_vcode" /> -->
<!--            </div>-->
<!--        </div>-->
    </div>

    <div class="weui-cells weui-cells_form" id="user4" style="display:none;">
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">姓名<label style="color:red">*</label></label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入本人姓名" id="user4_name" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">性别</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" value="男" id="user4_visitorsex" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">手机号<label style="color:red">*</label></label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="tel" placeholder="手机号" id="user4_mobile" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label for="name" class="weui-label">证件类型</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" value="身份证号" data-values="1" id="user4_idtype" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">证件号码</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="tel" placeholder="请输入证件号码" id="user4_idcard" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">公司<label style="color:red">*</label></label></label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入公司" id="user4_company" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">部门</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入部门" id="user4_department" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">职位</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入职位" id="user4_position" />
            </div>
        </div>
	    <input type="hidden" id="user4_plateno" />
    </div>

    <div class="weui-cells weui-cells_form" id="user0" style="display:none;">
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">账号</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="text" placeholder="请输入系统账号" id="user0_code" />
            </div>
        </div>
        <div class="weui-cell">
            <div class="weui-cell__hd"><label class="weui-label">密码</label></div>
            <div class="weui-cell__bd">
                <input class="weui-input" type="password" placeholder="请输入账号密码" id="user0_pwd" />
            </div>
        </div>
    </div>

    <div class="page_body">
        <a href="javascript:void(0);" class="weui-btn weui-btn_default" id="submit">绑定微信</a>
    </div>
</body>
</html>