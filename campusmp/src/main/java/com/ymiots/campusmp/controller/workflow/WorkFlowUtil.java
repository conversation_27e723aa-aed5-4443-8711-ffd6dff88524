package com.ymiots.campusmp.controller.workflow;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.entity.ApprovalContext;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.entity.dto.WorkflowRecordDto;
import com.ymiots.campusmp.entity.dto.visitor.AuditApplyRequest;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/29 11:21:16
 */
@Service
public class WorkFlowUtil {

    @Autowired
    private List<AuditHandler> auditHandlers;

    @Autowired
    private DBService dbService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private UserRedis userRedis;


    /**
     * 根据指定行为找到相应审核人
     *
     * @param mouldId            模板id
     * @param userId             被访问人用户id
     * @param designatedPersonId 指定人id
     * @param orgCode            组织机构
     * @param areaId             区域id
     * @param nodeLevel          节点等级
     */
    public List<WorkFlowNodeDto> selSubPerson(String mouldId, String userId, String designatedPersonId, String orgCode, String areaId, int nodeLevel) {
        List<WorkFlowNodeDto> nodeDtoList = new ArrayList<>();
        //获取当前表单节点状态
        String sql = "select isfinal,infoid,ct.orgcode orgCode,behavior_status,nodelevel,mouldid" +
                " from tb_workflow_node wn" +
                " inner join tb_workflow_approvar wa on wa.nodeid = wn.uid " +
                " join tb_card_teachstudinfo ct on ct.uid = wa.infoid " +
                " where mouldid = '" + mouldId + "' and nodelevel = " + nodeLevel;
        List<WorkFlowNodeDto> workFlowNodeDtos = dbService.queryList(sql, WorkFlowNodeDto.class);
        if (CollectionUtil.isEmpty(workFlowNodeDtos)) {
            return nodeDtoList;
        }
        if (workFlowNodeDtos.get(0).getBehaviorStatus() == 0) {
            return workFlowNodeDtos;
        } else if (workFlowNodeDtos.get(0).getBehaviorStatus() == 1) {
            String sqlParents = "select distinct parentinfoid as infoId from tb_card_teachstudinfo_child where infoid = '" + userId + "' and mouldid = '" + mouldId + "'";
            List<String> stringList = jdbcTemplate.queryForList(sqlParents, String.class);
            if (stringList.size() == 0) {
                return nodeDtoList;
            }
            nodeDtoList = workFlowNodeDtos.stream()
                    .filter(node -> stringList.contains(node.getInfoId()))
                    .collect(Collectors.toList());
        } else if (workFlowNodeDtos.get(0).getBehaviorStatus() == 2) {

            String behaviorSql = "select info_id as infoId from tb_workflow_manager where area_id = '" + areaId + "'";
            List<String> forList = jdbcTemplate.queryForList(behaviorSql, String.class);
            nodeDtoList = workFlowNodeDtos.stream()
                    .filter(node -> forList.contains(node.getInfoId()))
                    .collect(Collectors.toList());
        } else if (workFlowNodeDtos.get(0).getBehaviorStatus() == 3) {
//            //根据组织架构进行判断
            String orgCodeSql = "select su.empid as infoId from tb_sys_user su " +
                    "join tb_card_orgframework_user cou on su.uid = cou.userid " +
                    "where orgcode = '" + orgCode + "'";
            List<String> orgUserList = jdbcTemplate.queryForList(orgCodeSql, String.class);
            nodeDtoList = workFlowNodeDtos.stream()
                    .filter(node -> orgUserList.contains(node.getInfoId()))
                    .collect(Collectors.toList());
            //提交人选择哪个部门就是哪个部门审核
//            nodeDtoList = workFlowNodeDtos.stream()
//                    .filter(node -> orgCode.equals(node.getOrgCode()))
//                    .collect(Collectors.toList());
        } else if (workFlowNodeDtos.get(0).getBehaviorStatus() == 4) {
            //查询访客表中的指定访客人信息
            nodeDtoList = workFlowNodeDtos.stream()
                    .filter(node -> designatedPersonId.equals(node.getInfoId()))
                    .collect(Collectors.toList());
        }else if (workFlowNodeDtos.get(0).getBehaviorStatus() == 5) {
            //根据指定的部门的领导
            nodeDtoList = workFlowNodeDtos.stream()
                    .filter(node -> orgCode.equals(node.getOrgCode()))
                    .collect(Collectors.toList());
        }
        return nodeDtoList;
    }
    /**
     * 提交审批
     */
    public JsonResult submitForApproval(HttpServletRequest request, String uid,String userId,String name, Integer status, String remark, String table, String field, String areaPosition,String applyDay) throws Exception {
       return submitForApproval(request, uid, userId, name, status, remark, table, field, areaPosition, applyDay, null);
    }

    /**
     * 提交审批
     */
    public JsonResult submitForApproval(HttpServletRequest request, String uid,String userId,String name, Integer status, String remark, String table, String field, String areaPosition,String applyDay,AuditApplyRequest requestBody) throws Exception {
        String orgCodeSQL = "select orgcode from tb_card_teachstudinfo where uid = '" + userId + "'";
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "uid为空！");
        }
        String mouldTypeSQL = "select mouldtype,node_level nodeLevel,ar.createdate createDate from tb_workflow_mould wm " +
                " inner join tb_workflow_approval_record ar on wm.uid = ar.mouldid " +
                " where formid = '" + uid + "' and auditcondition = 0";
        List<WorkflowRecordDto> workFlowNodeDtos = dbService.queryList(mouldTypeSQL, WorkflowRecordDto.class);
        if (workFlowNodeDtos.size() == 0) {
            return Json.getJsonResult(false, "请勿重复审核或者当前节点以被其他审核人审核");
        }
        Integer level = workFlowNodeDtos.stream()
                .max(Comparator.comparing(WorkflowRecordDto::getCreateDate))
                .map(WorkflowRecordDto::getNodeLevel)
                .orElse(0);
        Integer mouldType = workFlowNodeDtos.get(0).getMouldType();
        //获取到当前审核人的对应的审核节点
        List<WorkFlowNodeDto> nodeLevelList = getNodeLevel(request, userId, mouldType, level);
        if (CollectionUtil.isEmpty(nodeLevelList)) {
            return Json.getJsonResult(false, "当前节点也被审核,请勿重复审核");
        }
        WorkFlowNodeDto nodeLevel = nodeLevelList.get(0);
        int nextNodeLevel = nodeLevel.getNodeLevel() + 1;
        //查询出具体的天数
        String levelDay = nodeLevel.getDay();
        int timeStatus = nodeLevel.getTimeStatus();
        String areaId = "";
        //获取到相应的区域id
        if (nodeLevel.getBehaviorStatus() == 2) {
            String sqlLabId = "select " + field + " from " + table + " where uid = '" + uid + "'";
            areaId = dbService.queryOneField(sqlLabId, String.class);
        }

        //判断当前节点是否被审批
        int count = dbService.getCount("tb_workflow_approval_record", "node_level = " + nodeLevel.getNodeLevel() + " And formid = '" + uid + "'  and mouldid = '" + nodeLevel.getMouldId() + "' and auditdate is null");
        if (count == 0) {
            return Json.getJsonResult(false, "请勿重复审核或当前节点以被其他审核人审核");
        }
        int isDefaultAudit = dbService.getCount("tb_workflow_approval_record", "node_level = " + nextNodeLevel + " And formid = '" + uid + "' and auditcondition = 4 and mouldid = '" + nodeLevel.getMouldId() + "'");
        List<WorkFlowNodeDto> dtoList = new ArrayList<>();
        AuditHandler auditHandler = auditHandlerBasedOnCondition(table);
        return processApprovalHandler(request, new ApprovalContext(uid, status, remark, table, areaPosition, userId, name, orgCodeSQL, nodeLevel, nextNodeLevel, areaId, dtoList, auditHandler, isDefaultAudit, levelDay, applyDay, timeStatus,requestBody));
    }

    public JsonResult processApprovalHandler(HttpServletRequest request, ApprovalContext ctx) throws Exception {
        if (ctx.getStatus() == -1) {
            String rejectForm = "";
            if ("tb_access_application".equals(ctx.getTable())){
                rejectForm = "update " + ctx.getTable() + " set status =" + ctx.getStatus() + " where id = '" + ctx.getUid() + "'";
            } else {
                rejectForm = "update " + ctx.getTable() + " set status =" + ctx.getStatus() + " where uid = '" + ctx.getUid() + "'";
            }
            dbService.excuteSql(rejectForm);
            ctx.getAuditHandler().handleFailure(ctx);
        } else {
            if (ctx.getTimeStatus() == 1) {
                BigDecimal applyDayBig = new BigDecimal(ctx.getApplyDay());
                BigDecimal leveDayBig = new BigDecimal(ctx.getLevelDay());
                if (applyDayBig.compareTo(leveDayBig) < 0) {
                    String updateFormId = "update " + ctx.getTable() + " set status =? where  uid = '" + ctx.getUid() + "'";
                    dbService.excuteSql(updateFormId, ctx.getStatus());
                    if (ctx.getStatus() == 1) {
                        ctx.getAuditHandler().handleSuccess(ctx);
                    } else {
                        ctx.getAuditHandler().handleFailure(ctx);
                    }
                }
            } else {
                if (ctx.getNodeLevel().getIsFinal() == 1) {
                    String updateFormId = "";
                    if ("tb_access_application".equals(ctx.getTable())) {
                        updateFormId = "update " + ctx.getTable() + " set status =? where  id = '" + ctx.getUid() + "'";
                    } else {
                        updateFormId = "update " + ctx.getTable() + " set status =? where  uid = '" + ctx.getUid() + "'";
                    }
                    dbService.excuteSql(updateFormId, ctx.getStatus());
                    if (ctx.getStatus() == 1) {
                        ctx.getAuditHandler().handleSuccess(ctx);
                    } else {
                        ctx.getAuditHandler().handleFailure(ctx);
                    }
                } else {
                    JSONObject queryJSONObject = dbService.QueryJSONObject(ctx.getOrgCodeSQL());
                    String orgCode = queryJSONObject.getString("orgcode");
                    List<WorkFlowNodeDto> dtoList = selSubPerson(ctx.getNodeLevel().getMouldId(), ctx.getUserId(), "", orgCode, ctx.getAreaId(), ctx.getNextNodeLevel());
                    if (dtoList.size() == 0) {
                        throw new Exception("下一审批人未配置，请联系管理员！");
                    }
                    if (ctx.getIsDefaultAudit() == 0) {
                        String insertAuditRecordSQL = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate) values  " +
                                "(uuid(),'" + ctx.getUid() + "','" + ctx.getNodeLevel().getMouldId() + "',0," + ctx.getNextNodeLevel() + ",now()) ";
                        dbService.excuteSql(insertAuditRecordSQL);
                    } else {
                        String insertAuditRecordSQL = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate) values  " +
                                "(uuid(),'" + ctx.getUid() + "','" + ctx.getNodeLevel().getMouldId() + "',0," + (ctx.getNextNodeLevel() + 1) + ",now()) ";
                        dbService.excuteSql(insertAuditRecordSQL);
                    }
                    ctx.getAuditHandler().handleSuccess(ctx);
                }
            }
        }
        String updateWorkForm = "update tb_workflow_approval_record " +
                " set auditcondition = ?  , remark = ?,modifydate = ? ,auditid = ?, modifyid = ? , auditdate = ?  " +
                " where formid = '" + ctx.getUid() + "' " +
                " and node_level = " + ctx.getNodeLevel().getNodeLevel() +
                " and auditcondition = 0 ";
        dbService.excuteSql(updateWorkForm,
                1,
                ctx.getRemark() + "   " + userRedis.get(request).getName() + "   " + (ctx.getStatus() == 1 ? "  同意" : "  不同意"),
                DateHelper.format(new Date(), " yyyy-MM-dd HH:mm:ss"),
                ctx.getUserId(),
                ctx.getUserId(),
                DateHelper.format(new Date(), " yyyy-MM-dd HH:mm:ss"));

        return Json.getJsonResult(true);
    }

    public String getAuditRecordsWhereSQL(HttpServletRequest request, Integer mouldType, WorkFlowNodeDto nodeLevel,String infoField,String DesignatedPersonField) {
        //判断当前身份
        String userId = userRedis.get(request).getDocid();
        nodeLevel = getNodeLevel(request, userId, mouldType);
        int behaviorStatus = nodeLevel.getBehaviorStatus();
        int level = nodeLevel.getNodeLevel();
        String where = "";
        //判断模板行为
        if (behaviorStatus == 0) {
            where += "";
        } else if (behaviorStatus == 1) {
            String sql = "select infoid from tb_card_teachstudinfo_child where parentinfoid = '" + userId + "' and mouldid = '" + nodeLevel.getMouldId() + "'";
            List<String> stringList = dbService.queryFields(sql, String.class);
            if (stringList == null) {
                return " and 1=2 ";
            }
            if (level > 1){
                level = level - 1;
                where += " and war.auditid in (" + sql + ")";
            }else {
                where += " and "+infoField+" in (" + sql + ")";
            }
        } else if (behaviorStatus == 2) {
            String sql = "select area_id from tb_workflow_manager where info_id = '" + userId + "' ";
            List<String> stringList = dbService.queryFields(sql, String.class);
            if (stringList == null) {
                return " and 1=2 ";
            }
            where += " AND area_id in (" + sql + ")";
        } else if (behaviorStatus == 3) {
            String sql = "select orgcode from tb_sys_user su " +
                    " join tb_card_orgframework_user cou on cou.userid = su.uid " +
                    "and su.empid = '" + userId + "'";
            List<String> stringList = dbService.queryFields(sql, String.class);
            if (stringList == null) {
                return " and 1=2 ";
            }
            where += " AND ct.orgcode in (" + sql + ")";
        } else if (behaviorStatus == 4) {
            where += " and "+DesignatedPersonField+" = '" + userId + "'";
        }else if (behaviorStatus == 5) {
            String sql = "select orgcode from tb_card_teachstudinfo where uid ='" + userId + "'";
            List<String> stringList = dbService.queryFields(sql, String.class);
            if (stringList == null) {
                return " and 1=2 ";
            }
            where += " AND afl.department in (" + sql + ")";
        }
        where += " AND war.node_level = " + level;
        return where;
    }

    /**
     * 获取到当前人员节点信息
     */
    public WorkFlowNodeDto getNodeLevel(HttpServletRequest request, String userId, Integer mouldType) {
        //获取到当前人所对应的节点，根据节点类型获取到审核人员
        String sql = "SELECT nodelevel as nodeLevel,isfinal as isFinal,behavior_status as behaviorStatus,wm.uid as mouldId " +
                " FROM tb_workflow_mould wm " +
                " left join tb_workflow_node wn on wm.uid = wn.mouldid " +
                " left join tb_workflow_approvar wa on wa.nodeid = wn.uid" +
                " WHERE wa.infoid = '" + userId + "'";
        if (mouldType != null) {
            sql += " and mouldtype=" + mouldType + " ";
        }
        return dbService.queryForOne(sql, WorkFlowNodeDto.class);
    }

    /**
     * 获取到当前人员节点信息
     */
    public List<WorkFlowNodeDto> getNodeLevelList(HttpServletRequest request, String userId, Integer mouldType) {
        //获取到当前人所对应的节点，根据节点类型获取到审核人员
        String sql = "SELECT nodelevel as nodeLevel,isfinal as isFinal,behavior_status as behaviorStatus,wm.uid as mouldId,mouldtype mouldType " +
                " FROM tb_workflow_mould wm " +
                " left join tb_workflow_node wn on wm.uid = wn.mouldid " +
                " left join tb_workflow_approvar wa on wa.nodeid = wn.uid" +
                " WHERE wa.infoid = '" + userId + "'";
        if (mouldType != null) {
            sql += " and mouldtype=" + mouldType + " ";
        }
        return dbService.queryList(sql, WorkFlowNodeDto.class);
    }


    @Nullable
    public List<WorkFlowNodeDto> getNodeLevel(HttpServletRequest request, String userId, Integer mouldType,Integer level) {
        //获取到当前人所对应的节点，根据节点类型获取到审核人员
        String sql = "SELECT nodelevel as nodeLevel,isfinal as isFinal,behavior_status as behaviorStatus," +
                "time_status as timeStatus,day,wm.uid as mouldId " +
                " FROM tb_workflow_mould wm " +
                " left join tb_workflow_node wn on wm.uid = wn.mouldid " +
                " left join tb_workflow_approvar wa on wa.nodeid = wn.uid" +
                " WHERE mouldtype=" + mouldType + " and wa.infoid = '" + userId + "'";
        if (level != null) {
            sql += " and wn.nodelevel = " + level;
        }
        return dbService.queryList(sql, WorkFlowNodeDto.class);
    }


    // 根据业务条件选择合适的 AuditHandler
    private AuditHandler auditHandlerBasedOnCondition(String option) {
        // 实现根据需要的逻辑动态选择 AuditHandler
        return auditHandlers.stream()
                .filter(handler -> option.equals(handler.getHandlerName())) // 自定义 getConditionValue 方法
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No suitable AuditHandler found"));
    }
}
