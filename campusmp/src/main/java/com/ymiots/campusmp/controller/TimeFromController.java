package com.ymiots.campusmp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.model.R;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.AccessTokenService;
import com.ymiots.campusmp.service.TimeService;
import com.ymiots.campusmp.utils.weixin.WeiXinJsSDK;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/time")
public class TimeFromController extends BaseController {

    @Autowired
    private TimeService timeService;

    @Autowired
    UserRedis userredis;


    @Autowired
    WeiXinJsSDK wxjssdk;

    @Autowired
    AccessTokenService accesstoken;


    @RequestMapping(value = "/absencerecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String absenceRecord(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            model.addAttribute("usertype", userredis.get(request).getUsertype());
            SysUser sysUser = userredis.get(request);
            int usertype = sysUser.getUsertype();
            String userDocid = sysUser.getDocid();
            String username = sysUser.getName();
            if (usertype == 3 ) {
                JsonData people = timeService.getPeople(request, response);
//                model.addAttribute("people", people.getData());
                model.addAttribute("userDocid", userDocid);
                model.addAttribute("username", username);
            }
//            if ( usertype == 3) {
//                JsonData people = timeService.getPeople(request, response);
//                JSONArray data = people.getData();
//                model.addAttribute("people", data);
////                model.addAttribute("userDocid", data.getJSONObject(0).getString("value"));
////                model.addAttribute("username", data.getJSONObject(0).getString("title"));
//            }
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd 00:00");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd 23:59");
            model.addAttribute("starttime", startTime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "time/absencerecord";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/record/getTimeRecord", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData getTimeRecord(HttpServletRequest request, HttpServletResponse response, int start, int limit, String startTime, String endTime) {
        try {
            return timeService.getTimeRecord(request, response, start, limit, startTime, endTime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/signcardrecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String signcardrecord(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            model.addAttribute("usertype", userredis.get(request).getUsertype());
            SysUser sysUser = userredis.get(request);
            int usertype = sysUser.getUsertype();
            String userDocid = sysUser.getDocid();
            String username = sysUser.getName();
            if (usertype == 3 ) {
//                JsonData people = timeService.getPeople(request, response);
//                model.addAttribute("people", people.getData());
                model.addAttribute("userDocid", userDocid);
                model.addAttribute("username", username);
            }
//            if ( usertype == 3) {
//                JsonData people = timeService.getPeople(request, response);
//                JSONArray data = people.getData();
//                model.addAttribute("people", data);
////                model.addAttribute("userDocid", data.getJSONObject(0).getString("value"));
////                model.addAttribute("username", data.getJSONObject(0).getString("title"));
//            }
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd 00:00");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd 23:59");
            model.addAttribute("starttime", startTime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "time/signcardrecord";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/leaveRecordTeach", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String leaveRecordTeach(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd 00:00");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd 23:59");
            model.addAttribute("starttime", startTime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "time/leaveRecordTeach";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/getTypes", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getTypes(HttpServletRequest request, HttpServletResponse response) {
        try {
            return timeService.getTypes(request,response);
        } catch (Exception ex) {
            return Json.getJsonData(false, ex.getMessage());
        }
    }

    @RequestMapping(value = "/saveabsence", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData saveabsence(HttpServletRequest request, HttpServletResponse response,String uid,String applyDay,String starttime,String endtime,String xvjiaday,String linianday,String bennianday) {
        try {
            return timeService.saveabsence(request,response,uid,applyDay,starttime,endtime,xvjiaday,linianday,bennianday);
        } catch (Exception ex) {
            return Json.getJsonData(false, ex.getMessage());
        }
    }

    @RequestMapping(value = "/getpsitoin", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getpsitoin(HttpServletRequest request, HttpServletResponse response) {
        try {
            return timeService.getpsitoin(request,response);
        } catch (Exception ex) {
            return Json.getJsonData(false, ex.getMessage());
        }
    }

    @RequestMapping(value = "/searchaduit", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData searchaduit(HttpServletRequest request, HttpServletResponse response,String type,String name) {
        try {
            return timeService.searchaduit(request,response,type,name);
        } catch (Exception ex) {
            return Json.getJsonData(false, ex.getMessage());
        }
    }

    @RequestMapping(value = "/getAbsenceRecord", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getAbsenceRecord(HttpServletRequest request, String docid, int start, int limit, String starttime, String endtime) {
        try {
            return timeService.getAbsenceRecord(request, docid, start, limit, starttime, endtime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getSigncardRecord", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getSigncardRecord(HttpServletRequest request, String docid, int start, int limit, String starttime, String endtime) {
        try {
            return timeService.getSigncardRecord(request, docid, start, limit, starttime, endtime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }



    @RequestMapping(value = "/detailedAbsenceRecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String detailedAbsenceRecord(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            model.addAttribute("usertype", userredis.get(request).getUsertype());
            model.addAttribute("timestamp", System.currentTimeMillis());
            JSONArray detailedAbsenceRecord = timeService.getDetailedAbsenceRecord(uid, request);
            if (!detailedAbsenceRecord.isEmpty()) {
                JSONObject jo = detailedAbsenceRecord.getJSONObject(0);
                model.addAttribute("uid", jo.getString("uid"));
                model.addAttribute("infoid", jo.getString("infoid"));
                model.addAttribute("infoname", jo.getString("infoname"));
                model.addAttribute("starttime", jo.getString("starttime"));
                model.addAttribute("endtime", jo.getString("endtime"));
                model.addAttribute("absencetypename", jo.getString("absencetypename"));
                model.addAttribute("reason", jo.getString("reason"));
                model.addAttribute("status", jo.getIntValue("status"));
                model.addAttribute("remark", jo.getString("remark"));
                model.addAttribute("day", jo.getString("day"));
                model.addAttribute("sfxvjia", jo.getString("sfxvjia"));
                model.addAttribute("xvjiaday", jo.getString("xvjiaday"));
                model.addAttribute("linianday", jo.getString("linianday"));
                model.addAttribute("bennianday", jo.getString("bennianday"));
                model.addAttribute("imgPath", WebConfig.getResourceDomain()+jo.getString("imgPath"));
            }
            return "time/detailedAbsenceRecord";
        } catch (Exception ex) {
            ex.printStackTrace();
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/detailedSigncardRecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String detailedSigncardRecord(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            model.addAttribute("usertype", userredis.get(request).getUsertype());
            model.addAttribute("timestamp", System.currentTimeMillis());
            JSONArray detailedAbsenceRecord = timeService.getDetailedSigncardRecord(uid, request);
            if (!detailedAbsenceRecord.isEmpty()) {
                JSONObject jo = detailedAbsenceRecord.getJSONObject(0);
                model.addAttribute("uid", jo.getString("uid"));
                model.addAttribute("infoid", jo.getString("infoid"));
                model.addAttribute("infoname", jo.getString("infoname"));
                model.addAttribute("signtime", jo.getString("signtime"));
                model.addAttribute("workdate", jo.getString("workdate"));
                model.addAttribute("reason", jo.getString("reason"));
                model.addAttribute("status", jo.getIntValue("status"));
                model.addAttribute("remark", jo.getString("remark"));
                if (jo.getString("worktype").equals("7")){
                    model.addAttribute("worktypename", "上班");
                }else if (jo.getString("worktype").equals("8")){
                    model.addAttribute("worktypename", "下班");
                }

                if (jo.getString("ftype").equals("01")){
                    model.addAttribute("ftypename", "忘打卡");
                }else if (jo.getString("ftype").equals("02")){
                    model.addAttribute("ftypename", "其他");
                }
            }
            return "time/detailedSigncardRecord";
        } catch (Exception ex) {
            ex.printStackTrace();
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/cancelAbsence", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult cancelAbsence(String uid) {
        try {
            return timeService.cancelAbsence(uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/cancelSigncard", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult cancelSigncard(String uid) {
        try {
            return timeService.cancelSigncard(uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/auditabsencelist", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditabsencelist(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd");
            model.addAttribute("starttime", startTime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "time/auditabsencelist";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/auditsigncardlist", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditsigncardlist(HttpServletRequest request, HttpServletResponse response, Model model,String starttime,String name) {
        try {
            if (StringUtils.isBlank(starttime)) {
                starttime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd");
            }
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd");
            model.addAttribute("name", StringUtils.isBlank(name) ? null : name.trim());
            model.addAttribute("starttime", starttime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "time/auditsigncardlist";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/auditleavelistParents", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditleavelistParents(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd");
            model.addAttribute("starttime", startTime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "time/auditleavelistParents";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/getAuditAbsenceList", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getAuditAbsenceList(HttpServletRequest request, HttpServletResponse response, String key, String status, String endtime, String starttime, int start, int limit) {
        try {
            return timeService.getAuditAbsenceList(request, response, key, status, endtime, starttime, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getAuditSigncardList", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getAuditSigncardList(HttpServletRequest request, HttpServletResponse response, String key, String status, String endtime, String starttime, int start, int limit) {
        try {
            return timeService.getAuditSigncardList(request, response, key, status, endtime, starttime, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getAuditLeaveList1", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getAuditLeaveList1(HttpServletRequest request, HttpServletResponse response, String leavetype, String key, String status, String endtime, String starttime, int start, int limit) {
        try {
            return timeService.getAuditLeaveList1(request, response, leavetype, key, status, endtime, starttime, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/auditabsence", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditleave(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            int a = 0;
            String userId = userredis.getUserId(request);
            String sql = "select docid from tb_weixin_user where uid  = '" + userId + "'";
            JSONObject jsonObjects = timeService.getWeiXinDocid(sql);
            JSONArray detailedAbsenceRecord = timeService.getDetailedAbsenceRecord(uid, request);
            if (!detailedAbsenceRecord.isEmpty()) {
                JSONObject jo = detailedAbsenceRecord.getJSONObject(0);
                for (Object o : detailedAbsenceRecord) {
                    JSONObject jsonObject = (JSONObject) o;
                    if (jsonObject.getInteger("auditcondition") == 1) {
                        if (jsonObject.getString("auditid").equals(jsonObjects.getString("docid")) || jsonObject.getString("modifyid").equals(jsonObjects.getString("docid"))) {
                            a = 1;
                        }
                    } else if (jsonObject.getInteger("auditcondition") == 2) {
                        a = 2;
                    }
                }
                model.addAttribute("uid", jo.getString("uid"));
                model.addAttribute("infoid", jo.getString("infoid"));
                model.addAttribute("infoname", jo.getString("infoname"));
                model.addAttribute("starttime", jo.getString("starttime"));
                model.addAttribute("endtime", jo.getString("endtime"));
                model.addAttribute("absencetypename", jo.getString("absencetypename"));
                model.addAttribute("reason", jo.getString("reason"));
                model.addAttribute("status", jo.getIntValue("status"));
                model.addAttribute("auditcondition", a);
                model.addAttribute("warremark", jo.getString("warremark"));
                model.addAttribute("remark", jo.getString("remark"));
                model.addAttribute("day", jo.getString("day"));
                model.addAttribute("sfxvjia", jo.getString("sfxvjia"));
                model.addAttribute("xvjiaday", jo.getString("xvjiaday"));
                model.addAttribute("linianday", jo.getString("linianday"));
                model.addAttribute("bennianday", jo.getString("bennianday"));
                model.addAttribute("imgPath", jo.getString("imgPath"));
            }
            return "time/auditabsence";
        } catch (Exception ex) {
            ex.printStackTrace();
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/auditsigncard", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditsigncard(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            int a = 0;
            String userId = userredis.getUserId(request);
            String sql = "select docid from tb_weixin_user where uid  = '" + userId + "'";
            JSONObject jsonObjects = timeService.getWeiXinDocid(sql);
            JSONArray detailedSigncardRecord = timeService.getDetailedSigncardRecord(uid, request);
            if (!detailedSigncardRecord.isEmpty()) {
                JSONObject jo = detailedSigncardRecord.getJSONObject(0);
                for (Object o : detailedSigncardRecord) {
                    JSONObject jsonObject = (JSONObject) o;
                    if (jsonObject.getInteger("auditcondition") == 1) {
                        if (jsonObject.getString("auditid").equals(jsonObjects.getString("docid")) || jsonObject.getString("modifyid").equals(jsonObjects.getString("docid"))) {
                            a = 1;
                        }
                    } else if (jsonObject.getInteger("auditcondition") == 2) {
                        a = 2;
                    }
                }
                model.addAttribute("uid", jo.getString("uid"));
                model.addAttribute("infoid", jo.getString("infoid"));
                model.addAttribute("infoname", jo.getString("infoname"));
                model.addAttribute("signtime", jo.getString("signtime"));
                model.addAttribute("createDt", jo.getString("createdate"));
                model.addAttribute("workdate", jo.getString("workdate"));
                model.addAttribute("reason", jo.getString("reason"));
                model.addAttribute("status", jo.getIntValue("status"));
                model.addAttribute("auditcondition", a);
                model.addAttribute("warremark", jo.getString("warremark"));
                model.addAttribute("remark", jo.getString("remark"));
                if (jo.getString("worktype").equals("7")){
                    model.addAttribute("worktypename", "上班");
                }else if (jo.getString("worktype").equals("8")){
                    model.addAttribute("worktypename", "下班");
                }

                if (jo.getString("ftype").equals("01")){
                    model.addAttribute("ftypename", "忘打卡");
                }else if (jo.getString("ftype").equals("02")){
                    model.addAttribute("ftypename", "其他");
                }
            }
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "time/auditsigncard";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }



    @ResponseBody
    @RequestMapping(value = "/auditabsencefrom", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult auditabsencefrom(HttpServletRequest request, String uid, String infoid, Integer status, String remark,String dUserId) {
        try {
            return timeService.auditabsencefrom(request, uid, infoid, status, remark, dUserId);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/auditsigncardfrom", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult auditsigncardfrom(HttpServletRequest request, String uid, String infoid, Integer status, String remark,String dUserId) {
        try {
            return timeService.auditsigncardfrom(request, uid, infoid, status, remark,dUserId);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/leaveFinish", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult leaveFinish(HttpServletRequest request, String uid, String infoid, String status, String remark) {
        try {
            return timeService.leaveFinish(request, uid, infoid, status, remark);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/auditLeave3", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult auditLeave2(HttpServletRequest request, String uid, String infoid, String status, String remark) {
        try {
            return timeService.auditLeave2(request, uid, infoid, status, remark);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/absencefromduty", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String AbsenceForm(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("usertype", userredis.get(request).getUsertype());
        model.addAttribute("timestamp", System.currentTimeMillis());
        String startTime = DateHelper.format(new Date(), "yyyy-MM-dd 00:00");
        String endTime = DateHelper.format(new Date(), "yyyy-MM-dd 23:59");
        model.addAttribute("starttime", startTime.replace(" ", "T"));
        model.addAttribute("endtime", endTime.replace(" ", "T"));
        SysUser sysUser = userredis.get(request);
        int usertype = sysUser.getUsertype();
        String userDocid = sysUser.getDocid();
        String username = sysUser.getName();
        if (usertype == 3) {
//            JsonData people = timeService.getPeople(request, response);
//            model.addAttribute("people", people.getData());
            model.addAttribute("userDocid", userDocid);
            model.addAttribute("username", username);
        }
//        if (usertype == 3) {
//            JsonData people = timeService.getPeople(request, response);
//            JSONArray data = people.getData();
//            model.addAttribute("people", data);
////            model.addAttribute("userDocid", data.getJSONObject(0).getString("value"));
////            model.addAttribute("username", data.getJSONObject(0).getString("title"));
//        }
        return "time/absencefromduty";
    }

    @RequestMapping(value = "/signcardfromduty", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String SignCardForm(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("usertype", userredis.get(request).getUsertype());
        model.addAttribute("timestamp", System.currentTimeMillis());
        String startTime = DateHelper.format(new Date(), "yyyy-MM-dd 00:00");
        String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd 23:59");
        model.addAttribute("starttime", startTime.replace(" ", "T"));
        model.addAttribute("endtime", endTime.replace(" ", "T"));
        SysUser sysUser = userredis.get(request);
        int usertype = sysUser.getUsertype();
        String userDocid = sysUser.getDocid();
        String username = sysUser.getName();
        if (usertype == 3) {
//            JsonData people = timeService.getPeople(request, response);
//            model.addAttribute("people", people.getData());
            model.addAttribute("userDocid", userDocid);
            model.addAttribute("username", username);
        }
        return "time/signcardfromduty";
    }

    /**
     * 保存缺勤表单
     * @param request
     * @param response
     * @param docid
     * @param starttime
     * @param endtime
     * @param des
     * @param type
     * @param applyDay
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/absencefromdutyfrom", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonResult absencefromdutyfrom(HttpServletRequest request, HttpServletResponse response, String docid, String starttime, String endtime, String des, String type, String applyDay, MultipartFile[] photoFiles,
                                          String dUserId,String sfxvjia,String xvjiaDay,String qunianshu,String bennianshu) {

        try {
            return timeService.saveabsencefromdutyfrom(request, response, docid, starttime, endtime, des, type, applyDay, photoFiles, dUserId,sfxvjia,xvjiaDay,qunianshu,bennianshu);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/timerecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String Index(HttpServletRequest request, HttpServletResponse response, Model model) {
        SysUser userinfo = userredis.get(request);
        model.addAttribute("facerecord", userinfo);
        model.addAttribute("resourcedomain", WebConfig.getResourceDomain());
        String startTime = DateHelper.format(DateHelper.addDays(new Date(),-3), "yyyy-MM-dd 00:00");
        String endTime = DateHelper.format(DateHelper.addDays(new Date(),3), "yyyy-MM-dd 23:59");
        model.addAttribute("startTime", startTime.replace(" ", "T"));
        model.addAttribute("endTime", endTime.replace(" ", "T"));
        model.addAttribute("timestamp", System.currentTimeMillis());
        return "time/timerecord";
    }


    @ResponseBody
    @RequestMapping(value = "/record/detailedrecord", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData getDetailedRecord(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return timeService.getDetailedRecord(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/signcardfromdutyfrom", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonResult signcardfromdutyfrom(HttpServletRequest request, HttpServletResponse response, String docid, String signtime,String des,String worktype,String ftype,String dUserId) {


        try {
            return timeService.savesigncardfromdutyfrom(request, response, docid, signtime, des, worktype, ftype,dUserId);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 获取审核记录详情
     */
    @ResponseBody
    @RequestMapping(value = "/getAbsenceRecordDetails", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public R<?> getAbsenceRecordDetails(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return timeService.getAbsenceRecordDetails(request, response, uid);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取审核记录详情
     */
    @ResponseBody
    @RequestMapping(value = "/getSigncardRecordDetails", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public R<?> getSigncardRecordDetails(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return timeService.getSigncardRecordDetails(request, response, uid);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(e.getMessage());
        }
    }


    @RequestMapping(value = "/approvalRecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String getApprovalRecord(HttpServletRequest request, HttpServletResponse response, Model model, String formid) {
        model.addAttribute("usertype", userredis.get(request).getUsertype());
        return "time/approvalrecord";
    }


    @RequestMapping(value = "/getApprovalRecord", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getApprovalRecord(HttpServletRequest request, String formid, int start, int limit) {
        try {
            return timeService.getApprovalRecord(request, formid, start, limit);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/sign_detail", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String getHotelInfoDetail(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            JSONObject object = timeService.getClassStudentDetailList(request);
            Integer sex = object.getInteger("sex");
            String sexName = "";
            if (sex == 1) {
                sexName= "男";
            } else {
                sexName = "女";
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            //获取当前时间
            String nowDate = LocalDateTime.now().format(formatter);
            model.addAttribute("code", object.getString("code"));
            model.addAttribute("name", object.getString("name"));
            model.addAttribute("sex",sexName);
            model.addAttribute("imgInfo", object.getString("imgpath"));
//            model.addAttribute("attendanceType",attendanceType);
//            model.addAttribute("daily",daily);
            model.addAttribute("nowDate",nowDate);
            model.addAttribute("uid",object.getString("uid"));
            model.addAttribute("card", object.getString("card"));
            model.addAttribute("mobile", object.getString("mobile"));
            model.addAttribute("idcard", object.getString("idcard"));
            model.addAttribute("orgName", object.getString("orgName"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            long timestamp=wxjssdk.GetTimeStamp();
            String nonceStr=wxjssdk.GetNoncestr();
            String access_token=accesstoken.GetAccessToken();
            String jsapiticket=wxjssdk.GetJSapiTicket(access_token);
            String url= WebConfig.thisPageUrl(request, true);
            String signature= wxjssdk.MakeSignature(nonceStr, jsapiticket, timestamp, url);

            Map<String,String> paymap=new HashMap<String,String>();
            paymap.put("appId", WebConfig.getWeixinAppid());
            paymap.put("timeStamp",String.valueOf(timestamp));
            paymap.put("nonceStr",nonceStr);
            model.addAttribute("appId", paymap.get("appId"));
            model.addAttribute("timeStamp",String.valueOf(timestamp));
            model.addAttribute("nonceStr", nonceStr);
            model.addAttribute("signature", signature);
            return "time/sign_detail";
        } catch (Exception e) {
            return ViewErrorClosePage(request, response, model, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/checkSignIn", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult checkSignIn(HttpServletRequest request, HttpServletResponse response, Double accuracy, String daily, Double longitude,Double latitude, MultipartFile photoFile,String image) {
        try {
            return timeService.checkSignIn(request, response, accuracy, daily, longitude, latitude, photoFile, image);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }


}
