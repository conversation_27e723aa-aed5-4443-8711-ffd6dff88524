package com.ymiots.campusmp.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.code.kaptcha.Producer;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.entity.BindUserInfo;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.entity.UserInfo;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.CardManageService;
import com.ymiots.campusmp.service.OAuthService;
import com.ymiots.campusmp.service.SysConfigService;
import com.ymiots.campusmp.service.UcenterService;
import com.ymiots.framework.filter.Permission;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.List;

@Controller
public class IndexController extends BaseController {

    @Autowired
    UserRedis userredis;

    @Autowired
    SysConfigService syscfg;

    @Autowired
    DBService dbService;

    @Autowired
    private Producer kaptChaProducer;

    @Autowired
    private OAuthService authService;

    @Autowired
    UcenterService ucenter;

    @Autowired
    CardManageService cardManageService;


    @RequestMapping(value = "/", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String Index(HttpServletRequest request, HttpServletResponse response, Model model) throws IOException {
        SysUser userinfo = userredis.get(request);
        if(userinfo!=null){
            List<BindUserInfo> list= ucenter.getOtherUser(request, response, userinfo.getOpenid(),userinfo.getUsertype());
            model.addAttribute("userlist", list);
            String docid = userredis.get(request).getDocid();
            //List<Notifications> notifications = dbService.queryList("select imageTextUuid uid,title,pushTime date,text content from tb_photo_image_text where billType = 'NOTICE_ANNOUNCEMENT' order by pushTime limit 3", Notifications.class);
            UserInfo userInfo = null;
            if (userinfo.getUsertype()==4){
                userInfo = dbService.queryForOne("select name studentName, cardsn card,mobile studentNumber,CONCAT(company,department,position) studentClass,photo studentPhoto from tb_visitor_visitorinfo where uid = '" + docid + "'", UserInfo.class);
            }else {
                userInfo = dbService.queryForOne("select ct.name studentName,ct.mobile,ct.card,ct.code studentNumber,getorgname(ct.orgcode) studentClass,face.imgpath studentPhoto from tb_card_teachstudinfo ct left join tb_face_infoface face on ct.uid = face.infoid where ct.uid = '" + docid + "'", UserInfo.class);
            }
            if (ObjectUtil.isEmpty(userInfo)) {
                String sql = "delete from tb_weixin_user where uid=?";
                dbService.excuteSql(sql, userinfo.getUid());
                userredis.remove(request);
                return BindModelTemplate(request, response);
            } else {
                //userInfo.setNotifications(notifications);
                JSONObject object = dbService.QueryJSONObject("select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1");
                if (object != null) {
                    userInfo.setSchoolName(object.getString("schoolName"));
                    userInfo.setSchoolLogo(object.getString("schoolLogo"));
                }
                int isaddbind = 0;
                if (!WebConfig.EnableCampusCPService()) {
                    if (userinfo.getUsertype() == 2 || userinfo.getUsertype() == 3 && WebConfig.getApptype().equals("1")) {
                        isaddbind = 1;
                    }
                }
                model.addAttribute("isaddbind", isaddbind);
                model.addAttribute("userInfo", userInfo);
            }
            //String cardId = cardManageService.GetCardId(request, "");
            //if (StringUtils.isNotEmpty(cardId)){
            //    userInfo.setAccount(cardManageService.GetCardInfoByNo(userInfo.getCard(),cardId).getBalance().toString());
            //}else {
            //    userInfo.setAccount("0");
            //}
        }
        model.addAttribute("userinfo", userinfo);
        model.addAttribute("title", WebConfig.getApptypename());
        String useragent = request.getHeader("user-agent");
        String paid = authService.isServicePaid(request);
        if (!useragent.contains("CampusPCBrowser")) {
            model.addAttribute("isAlertPassWord", 1);
            model.addAttribute("servicePaid",paid);
//            String pwdSql = "select uid,mp_password as password from tb_card_teachstudinfo where uid = '" + userinfo.getDocid() + "'";
//            weChatLogin weChatLogin = dbService.queryForOne(pwdSql, weChatLogin.class);
//            if (weChatLogin != null) {
//                String password = weChatLogin.getPassword();
//                if (!StringUtils.isBlank(password)) {
//                    model.addAttribute("isAlertPassWord", 1);
//                } else {
//                    model.addAttribute("isAlertPassWord", 0);
//                }
//            } else {
//                model.addAttribute("isAlertPassWord", 1);
//            }
        }

        String menucfg = request.getHeader("menucfg");
        if (!StringUtils.isBlank(menucfg)) {
            model.addAttribute("menucfg", menucfg);
        } else {
            model.addAttribute("menucfg", "0");
        }
        model.addAttribute("timestamp", System.currentTimeMillis());
        //if (userinfo != null) {
        //    if (userinfo.getUsertype() == 5) {
        //        return View(request, "order_index");
        //    }
        //}
//         return View(request, "order_index");
        return View(request, "index");
    }

    @Data
    public static class weChatLogin {
        private String uid;
        private String password;
    }
    @RequestMapping(value = "/errorpage", method = RequestMethod.GET)
    public String ErrorPage(HttpServletRequest request, HttpServletResponse response, String msg, Model model) {
        model.addAttribute("msg", msg);
        model.addAttribute("timestamp", System.currentTimeMillis());
        return "err";
    }

    @RequestMapping(value = "/weixinbind", method = RequestMethod.GET)
    public String weixinbind(HttpServletRequest request, HttpServletResponse response, String msg, Model model, String bind) {
        try {
            String url = "/weixinauth/oauthcode?backurl=/";
            if (!StringUtils.isBlank(bind)) {
                url += "&bind=" + bind;
            }
            model.addAttribute("isVerificationCode", syscfg.get("isVerificationCode"));
            return ViewLoadingPage(request, response, model, url);
        } catch (Exception ex) {
            model.addAttribute("msg", ex.getMessage());
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "err";
        }
    }

    @RequestMapping(value = "/dev/weixinbind", method = RequestMethod.GET)
    public String weixinbind(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("apptype", WebConfig.getApptype());
        model.addAttribute("isVerificationCode", syscfg.get("isVerificationCode"));
        return BindModelTemplate(request, response);
    }

    @ResponseBody
    @RequestMapping(value = "/captcha", method = RequestMethod.GET)
    public void getCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setDateHeader("Expires", 0);
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Pragma", "no-cache");
        response.setContentType("image/jpeg");
        //List<SysUser> sysUsers = dbService.queryList("SELECT a.uid,b.code,b.name,a.docid,b.orgcode,a.openid,b.code idcode,getorgname(b.orgcode) orgname,a.nickname,b.sex,b.status,UUID() tokenid,a.usertype,a.headimgurl from tb_weixin_user a LEFT JOIN tb_card_teachstudinfo b on a.docid=b.uid", SysUser.class);
        //for (SysUser sysUser : sysUsers) {
        //    userredis.add(request, response, sysUser);
        //}
        String capText = kaptChaProducer.createText();
        request.getSession().setAttribute("captcha", capText);
        // 获取当前时间并存储到 session
        long currentTime = System.currentTimeMillis();
        request.getSession().setAttribute("captcha_time", currentTime);
        BufferedImage bi = kaptChaProducer.createImage(capText);
        ImageIO.write(bi, "jpg", response.getOutputStream());
    }
}
