package com.ymiots.campusmp.controller.videophoto;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.common.VideoCallVo;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.constant.videophoto.VideoCallResult;
import com.ymiots.campusmp.constant.videophoto.dto.DeviceGroup;
import com.ymiots.campusmp.constant.videophoto.reponse.EquipmentGroupResult;
import com.ymiots.campusmp.constant.videophoto.reponse.VideoCallResultImpl;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.AccessTokenService;
import com.ymiots.campusmp.service.videophoto.VoipService;
import com.ymiots.campusmp.utils.weixin.WeiXinJsSDK;
import com.ymiots.framework.filter.Permission;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create-date 2024/5/18 11:19
 */
@Controller
@RequestMapping("/voipApiV2")
public class VoipController {

    @Autowired
    VoipService voipService;

    @Autowired
    WeiXinJsSDK wxjssdk;

    @Autowired
    AccessTokenService accesstoken;

    @Autowired
    UserRedis userRedis;

    @Autowired
    DBService dbService;

    @Value("${xstc.depUuid}")
    private String depUuid;


    @ResponseBody
    @RequestMapping(value ="/submitData",method = RequestMethod.POST)
    public VideoCallResult submitData(HttpServletRequest request, @RequestBody VideoCallVo videoCallVo) {
        try {
            return voipService.submitData(request, videoCallVo);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new VideoCallResultImpl().fail(ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value ="/getData",method = RequestMethod.POST)
    public Object getData(HttpServletRequest request, @RequestBody VideoCallVo videoCallVo) {
        try {
            return voipService.getData(request, videoCallVo);
        } catch (Exception ex) {
            ex.printStackTrace();
            EquipmentGroupResult equipmentGroupResult = new EquipmentGroupResult();
            return equipmentGroupResult.fail(ex.getMessage());
        }
    }

//    @ResponseBody
//    @RequestMapping(value ="/bindingFlag",method = RequestMethod.POST)
//    @Permission(authorization = true, usertype = -1)
//    public JsonResult setBindingFlag(HttpServletRequest request, String setBindingFlag, String voipGroupId) {
//        try {
//            return voipService.setBindingFlag(request, setBindingFlag,voipGroupId);
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            return Json.getJsonResult(false,ex.getMessage());
//        }
//    }

    @RequestMapping(value = "/voip_step1", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String index(HttpServletRequest request, HttpServletResponse response, Model model) throws Exception {
        long timestamp=wxjssdk.GetTimeStamp();
        String sql = "select co.name schoolName,wu.uid userContactUuid,co.uid schoolUuid,wu.otheropenid otherOpenId " +
                " from tb_weixin_user wu " +
                " join tb_card_teachstudinfo ct on ct.uid = wu.docid " +
                " join tb_card_orgframework co on co.code = ct.orgcode " +
                " where wu.uid = '"+userRedis.get(request).getUid()+"'";
        VoipDto voipDto = dbService.queryForOne(sql, VoipDto.class);
        if (voipDto != null) {
            model.addAttribute("userContactUuid", voipDto.getUserContactUuid());
            String queryGroup = "select pg.voipGroupId,pg.terminalGroupName voipGroupName," +
                    " ppr.binding_flag bindingFlag,wx_id wxId" +
                    " from tb_phone_voipgroup pg " +
                    " left join tb_phone_person_relation ppr on ppr.voip_group_id = pg.uid "+
                    " where wx_id = '" + voipDto.getUserContactUuid() + "'";
            List<DeviceGroup> deviceGroups = dbService.queryList(queryGroup, DeviceGroup.class);
            ObjectMapper objectMapper = new ObjectMapper();
            if (CollectionUtil.isEmpty(deviceGroups)) {
                String groupSQL = "select voipGroupId,terminalGroupName voipGroupName from tb_phone_voipgroup ";
                List<DeviceGroup> groups = dbService.queryList(groupSQL, DeviceGroup.class);
                String deviceGroupsJson = objectMapper.writeValueAsString(groups);
                model.addAttribute("deviceGroups", deviceGroupsJson);
            } else {
                String[] list = new String[10];
                for (int i = 0; i < deviceGroups.size(); i++) {
                    list[i]=deviceGroups.get(i).getVoipGroupId();
                }
                String groupSQL = "select voipGroupId,terminalGroupName voipGroupName,'N' as bindingFlag from tb_phone_voipgroup where voipGroupId not in ('" + String.join("','", list) + "')";
                List<DeviceGroup> groups = dbService.queryList(groupSQL, DeviceGroup.class);
                if (groups!=null){
                    deviceGroups.addAll(groups);
                }
                // 将设备组列表转换为 JSON 字符串
                String deviceGroupsJson = objectMapper.writeValueAsString(deviceGroups);
                model.addAttribute("deviceGroups", deviceGroupsJson);
            }
        }
        String wxUid = userRedis.get(request).getUid();
        model.addAttribute("timeStamp", timestamp);
        model.addAttribute("wxUid", wxUid);
        return "voip/voip_step1";
    }


    @RequestMapping(value = "/voip_step2", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String voipStep2(HttpServletRequest request, HttpServletResponse response, Model model,String voipGroupId,String bindingFlag) throws Exception {
        long timestamp = wxjssdk.GetTimeStamp();
        String serverUrl = WebConfig.getDomain();
        String nonceStr = wxjssdk.GetNoncestr();
        String appId = WebConfig.getWeixinAppid();
        String access_token = accesstoken.GetAccessToken();
        String jsApiTicket = wxjssdk.GetJSapiTicket(access_token);
        String url = WebConfig.thisPageUrl(request, true);
        String signature = wxjssdk.MakeSignature(nonceStr, jsApiTicket, timestamp, url);
        String sql = "select co.name schoolName,wu.uid userContactUuid,co.uid schoolUuid,wu.otheropenid otherOpenId " +
                " from tb_weixin_user wu " +
                " join tb_card_teachstudinfo ct on ct.uid = wu.docid " +
                " join tb_card_orgframework co on co.code = ct.orgcode " +
                " where wu.uid = '" + userRedis.get(request).getUid() + "'";
        VoipDto voipDto = dbService.queryForOne(sql, VoipDto.class);
        if (voipDto != null) {
            model.addAttribute("schoolName", voipDto.getSchoolName());
            model.addAttribute("schoolUuid", voipDto.getSchoolUuid());
            model.addAttribute("userContactUuid", voipDto.getUserContactUuid());
            if (voipDto.getOtherOpenId() != null) {
                model.addAttribute("openId", voipDto.getOtherOpenId());
            } else {
                model.addAttribute("openId", "null");
            }
        }
        if ("Y".equals(bindingFlag)) {
            model.addAttribute("bindingFlag", "N");
            model.addAttribute("flag", "N");
            model.addAttribute("bindingFlagName", "取消订阅");
        } else {
            model.addAttribute("bindingFlag", "Y");
            model.addAttribute("flag", "Y");
            model.addAttribute("bindingFlagName", "立即订阅");
        }
        model.addAttribute("depUuid", depUuid);
        model.addAttribute("voipGroupId", voipGroupId);
        model.addAttribute("serverUrl", serverUrl);
        model.addAttribute("nonceStr", nonceStr);
        model.addAttribute("signature", signature);
        model.addAttribute("appId", appId);
        model.addAttribute("timeStamp", timestamp);
        return "voip/voip_step2";
    }

    @Data
    public static class VoipDto{
        private String schoolName;
        private String userContactUuid;
        private String schoolUuid;
        private String otherOpenId;
    }
}
