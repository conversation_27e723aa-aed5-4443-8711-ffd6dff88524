package com.ymiots.campusmp.controller.workflow;

import cn.hutool.core.collection.CollectionUtil;
import com.ymiots.campusmp.entity.ApprovalContext;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:10:26
 */
@Service
public class LabAuditHandler implements AuditHandler{

    @Autowired
    PushMessageHandler pushMessageHandler;

    @Override
    public String getHandlerName() {
        return "tb_laboratory_apply_record";
    }


    @Override
    public void handleSuccess(String uid, String name, List<WorkFlowNodeDto> dtoList,String areaPosition,String remark) {
        CompletableFuture.runAsync(() -> {
            if (CollectionUtil.isEmpty(dtoList)) {
                pushMessageHandler.pushLabSuccessApply(uid);
            } else {
                pushMessageHandler.pushLabApply(uid, dtoList);
            }
        });
    }

    @Override
    public void handleSuccess(ApprovalContext ctx) {
        CompletableFuture.runAsync(() -> {
            if (CollectionUtil.isEmpty(ctx.getDtoList())) {
                pushMessageHandler.pushLabSuccessApply(ctx.getUid());
            } else {
                pushMessageHandler.pushLabApply(ctx.getUid(), ctx.getDtoList());
            }
        });
    }

    @Override
    public void handleFailure(String uid,String name,String remark) {
        CompletableFuture.runAsync(() -> {
            pushMessageHandler.pushLabFailApply(uid);
        });
    }

    @Override
    public void handleFailure(ApprovalContext ctx) {
        CompletableFuture.runAsync(() -> {
            pushMessageHandler.pushLabFailApply(ctx.getUid());
        });
    }
}
