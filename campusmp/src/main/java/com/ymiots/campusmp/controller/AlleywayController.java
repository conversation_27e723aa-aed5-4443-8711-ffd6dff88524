package com.ymiots.campusmp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.entity.dto.visitor.AuditApplyRequest;
import com.ymiots.campusmp.model.R;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.AlleywayService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.filter.Permission;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/alleyway")
public class AlleywayController extends BaseController {

    @Autowired
    UserRedis userredis;

    @Autowired
    AlleywayService service;

    /**
     * 归离寝室
     */
    @RequestMapping(value = "/dormitory", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String Dormitory(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("resourcedomain", WebConfig.getResourceDomain());
        model.addAttribute("startTime", DateHelper.format(new Date(), "yyyy-MM-dd 00:00").replace(" ", "T"));
        model.addAttribute("endTime", DateHelper.format(new Date(), "yyyy-MM-dd HH:mm").replace(" ", "T"));
        model.addAttribute("timestamp", System.currentTimeMillis());
        return "alleyway/dormitory";
    }

    @RequestMapping(value = "/doorfrom", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String AbsenceForm(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("usertype", userredis.get(request).getUsertype());
        model.addAttribute("timestamp", System.currentTimeMillis());
        String startTime = DateHelper.format(new Date(), "yyyy-MM-dd 00:00");
        String endTime = DateHelper.format(new Date(), "yyyy-MM-dd 23:59");
        model.addAttribute("starttime", startTime.replace(" ", "T"));
        model.addAttribute("endtime", endTime.replace(" ", "T"));
        SysUser sysUser = userredis.get(request);
        int usertype = sysUser.getUsertype();
        String userDocid = sysUser.getDocid();
        String username = sysUser.getName();
        if (usertype == 3) {
            model.addAttribute("userDocid", userDocid);
            model.addAttribute("username", username);
            model.addAttribute("usernamedept",sysUser.getOrgname());
            model.addAttribute("orgcode",sysUser.getOrgcode());
        }
        if (usertype == 4) {
            model.addAttribute("userDocid", userDocid);
            model.addAttribute("username", username);
            model.addAttribute("usernamedept",sysUser.getOrgname());
            model.addAttribute("orgcode",sysUser.getOrgcode());
        }
//        List<VisitorService.ElevatorDevicesVo> devices = service.getElevatorDevices(uid);
//        if (!CollectionUtil.isEmpty(devices)) {
//            model.addAttribute("devices", JSON.toJSONString(devices));
//        }
        return "alleyway/doorfrom";
    }

    @RequestMapping(value = "/auditdoorfromlist", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditabsencelist(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd");
            model.addAttribute("starttime", startTime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "alleyway/auditdoorfromlist";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/auditdoorfrom", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditdoorfrom(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            int a = 0;
            String userId = userredis.getUserId(request);
            String sql = "select docid from tb_weixin_user where uid  = '" + userId + "'";
            JSONObject jsonObjects = service.getWeiXinDocid(sql);
            JSONArray detailedAbsenceRecord = service.getDetailedDoorFromRecord(uid, request);
            if (!detailedAbsenceRecord.isEmpty()) {
                JSONObject jo = detailedAbsenceRecord.getJSONObject(0);
                for (Object o : detailedAbsenceRecord) {
                    JSONObject jsonObject = (JSONObject) o;
                    if (jsonObject.getInteger("auditcondition") == 1) {
                        //if (jsonObjects.getString("docid").equals(jsonObject.getString("auditid")) || jsonObjects.getString("docid").equals(jsonObject.getString("modifyid"))||) {
                            a = 1;
                        //}
                    } else if (jsonObject.getInteger("auditcondition") == 2) {
                        a = 2;
                    }
                }
                model.addAttribute("uid", jo.getString("id"));
                //model.addAttribute("infoid", jo.getString("infoid"));
                model.addAttribute("infoname", jo.getString("applicant_name"));
                model.addAttribute("starttime", jo.getString("starttime"));
                model.addAttribute("endtime", jo.getString("endtime"));
                model.addAttribute("application_time", jo.getString("application_time"));
                model.addAttribute("applicanttype", jo.getString("applicant_type"));
                if ("1".equals(jo.getString("applicant_type"))){
                    model.addAttribute("infotype", "内部人员");
                }else {
                    model.addAttribute("infotype", "外部人员");
                }
                model.addAttribute("accesstype", jo.getString("access_type"));
                model.addAttribute("department", jo.getString("departmentname"));
                model.addAttribute("password", jo.getString("password"));
                model.addAttribute("usage_count", jo.getString("usage_count"));
                model.addAttribute("reason", jo.getString("reason"));
                model.addAttribute("status", jo.getIntValue("status"));
                model.addAttribute("down_status", jo.getIntValue("down_status"));
                model.addAttribute("auditcondition", a);
                model.addAttribute("imgPath", WebConfig.getResourceDomain()+jo.getString("attachment"));
            }
            return "alleyway/auditdoorfrom";
        } catch (Exception ex) {
            ex.printStackTrace();
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }


    @RequestMapping(value = "/detailedDoorFromRecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String detailedAbsenceRecord(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            model.addAttribute("usertype", userredis.get(request).getUsertype());
            model.addAttribute("timestamp", System.currentTimeMillis());
            JSONArray detailedAbsenceRecord = service.getDetailedDoorFromRecord(uid, request);
            if (!detailedAbsenceRecord.isEmpty()) {
                JSONObject jo = detailedAbsenceRecord.getJSONObject(0);
                model.addAttribute("uid", jo.getString("id"));
                //model.addAttribute("infoid", jo.getString("infoid"));
                model.addAttribute("infoname", jo.getString("applicant_name"));
                model.addAttribute("starttime", jo.getString("starttime"));
                model.addAttribute("endtime", jo.getString("endtime"));
                model.addAttribute("application_time", jo.getString("application_time"));
                model.addAttribute("applicant_type", jo.getString("applicant_type"));
                model.addAttribute("access_type", jo.getString("access_type"));
                model.addAttribute("department", jo.getString("departmentname"));
                model.addAttribute("password", jo.getString("password"));
                model.addAttribute("usage_count", jo.getString("usage_count"));
                model.addAttribute("reason", jo.getString("reason"));
                model.addAttribute("status", jo.getIntValue("status"));
                model.addAttribute("down_status", jo.getIntValue("down_status"));
                model.addAttribute("imgPath", WebConfig.getResourceDomain()+jo.getString("attachment"));
            }
            return "alleyway/detailedDoorFromRecord";
        } catch (Exception ex) {
            ex.printStackTrace();
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/doorfromrecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String absenceRecord(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
                model.addAttribute("usertype", userredis.get(request).getUsertype());
                SysUser sysUser = userredis.get(request);
                int usertype = sysUser.getUsertype();
                String userDocid = sysUser.getDocid();
                String username = sysUser.getName();
                if (usertype == 3 ) {
                    JsonData people = service.getPeople(request, response);
                    model.addAttribute("people", people.getData());
                    model.addAttribute("userDocid", userDocid);
                    model.addAttribute("username", username);
                }
            if (usertype == 4 ) {
                //JsonData people = service.getPeople(request, response);
                //model.addAttribute("people", people.getData());
                model.addAttribute("userDocid", userDocid);
                model.addAttribute("username", username);
            }
//            if ( usertype == 3) {
//                JsonData people = timeService.getPeople(request, response);
//                JSONArray data = people.getData();
//                model.addAttribute("people", data);
////                model.addAttribute("userDocid", data.getJSONObject(0).getString("value"));
////                model.addAttribute("username", data.getJSONObject(0).getString("title"));
//            }
                String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd 00:00");
                String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd 23:59");
                model.addAttribute("starttime", startTime.replace(" ", "T"));
                model.addAttribute("endtime", endTime.replace(" ", "T"));
                model.addAttribute("timestamp", System.currentTimeMillis());
            return "alleyway/doorfromrecord";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/getDoorFromRecord", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getDoorFromRecord(HttpServletRequest request, String docid, int start, int limit, String starttime, String endtime) {
        try {
            return service.getDoorFromRecord(request, docid, start, limit, starttime, endtime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getAuditDoorFromList", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getAuditAbsenceList(HttpServletRequest request, HttpServletResponse response, String key, String status, String endtime, String starttime, int start, int limit) {
        try {
            return service.getAuditDoorFromList(request, response, key, status, endtime, starttime, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getDoors", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getDoors(HttpServletRequest request, HttpServletResponse response) {
        try {
            return service.getDoors(request,response);
        } catch (Exception ex) {
            return Json.getJsonData(false, ex.getMessage());
        }
    }

    @RequestMapping(value = "/getDepartmentPerson", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getDepartmentPerson(HttpServletRequest request, HttpServletResponse response,String deptcode) {
        try {
            return service.getDepartmentPerson(request,response,deptcode);
        } catch (Exception ex) {
            return Json.getJsonData(false, ex.getMessage());
        }
    }

    @RequestMapping(value = "/getSelectDoors", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getSelectDoors(HttpServletRequest request, HttpServletResponse response,String uid) {
        try {
            return service.getSelectDoors(request,response,uid);
        } catch (Exception ex) {
            return Json.getJsonData(false, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/saveDoorFrom", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonResult saveDoorFrom(HttpServletRequest request, HttpServletResponse response, String docid, String deptcode, String starttime, String endtime,String dUserId,
                                   String des, String devids, String count, String password, MultipartFile[] photoFiles, @RequestParam String devices) {
        try {
            String devicesStr = devices;
            List<AuditApplyRequest.DeviceSetting> deviceList = JSONArray.parseArray(devicesStr, AuditApplyRequest.DeviceSetting.class);
            return service.saveDoorFrom(request, response, docid, deptcode, starttime, endtime, dUserId,des, devids, count, password, photoFiles, deviceList);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 获取审核记录详情
     */
    @ResponseBody
    @RequestMapping(value = "/getDoorRecordDetails", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public R<?> getDoorRecordDetails(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return service.getDoorRecordDetails(request, response, uid);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(e.getMessage());
        }
    }

    @RequestMapping(value = "/savedoorfrom", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData savedoorfrom(HttpServletRequest request, HttpServletResponse response,String uid,String starttime,String endtime,String password,String usagecount,String devids) {
        try {
            return service.savedoorfrom(request,response,uid,starttime,endtime,password,usagecount,devids);
        } catch (Exception ex) {
            return Json.getJsonData(false, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/auditdoor", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult auditdoor(HttpServletRequest request, String uid, String infoid, Integer status, String remark, @RequestParam String devices) {
        try {
            String devicesStr = devices;
            List<AuditApplyRequest.DeviceSetting> deviceList = JSONArray.parseArray(devicesStr, AuditApplyRequest.DeviceSetting.class);
            return service.auditdoor(request, uid, infoid, status, remark,deviceList);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/dormitory/getdormlist", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData DormitoryList(HttpServletRequest request, HttpServletResponse response, int start, int limit, String starttime, String endtime) {
        try {
            return service.getDormitoryList(request, response, start, limit, starttime, endtime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/dormitory/getDetailedDorm", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getDetailedDorm(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            return service.getDetailedDorm(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 出入记录
     *
     * @param request
     * @param response
     * @param model
     * @return
     */
    @RequestMapping(value = "/record", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String Index(HttpServletRequest request, HttpServletResponse response, Model model) {
        SysUser userinfo = userredis.get(request);
        model.addAttribute("record", userinfo);
        model.addAttribute("resourcedomain", WebConfig.getResourceDomain());
        model.addAttribute("usertype", userredis.get(request).getUsertype());
        SysUser sysUser = userredis.get(request);
        int usertype = sysUser.getUsertype();
        String userDocid = sysUser.getDocid();
        String username = sysUser.getName();
        if (usertype == 2 || usertype == 3) {
            JsonData people = service.getPeople(request, response);
            model.addAttribute("people", people.getData());
            model.addAttribute("userDocid", userDocid);
            model.addAttribute("username", username);
        }
        String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd 00:00");
        String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd 23:59");
        model.addAttribute("startTime", startTime.replace(" ", "T"));
        model.addAttribute("endTime", endTime.replace(" ", "T"));
        model.addAttribute("timestamp", System.currentTimeMillis());
        return "alleyway/record";
    }

    @ResponseBody
    @RequestMapping(value = "/record/detailedrecord", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData getDetailedRecord(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return service.getDetailedRecord(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/record/recordList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData getRecordList(HttpServletRequest request, HttpServletResponse response, String docid, int start, int limit, String startTime, String endTime) {
        try {
            return service.getRecordList(request, response, docid, start, limit, startTime, endTime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 人员轨迹
     *
     * @param request
     * @param model
     * @return
     */
    @RequestMapping(value = "/querytrack", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String queryTrack(HttpServletRequest request,HttpServletResponse response, Model model){
        SysUser userinfo = userredis.get(request);
        model.addAttribute("track", userinfo);
        model.addAttribute("resourcedomain", WebConfig.getResourceDomain());
        model.addAttribute("usertype", userredis.get(request).getUsertype());
        SysUser sysUser = userredis.get(request);
        int usertype = sysUser.getUsertype();
        String userDocid = sysUser.getDocid();
        String username = sysUser.getName();
        if (usertype == 2 || usertype == 3) {
            JsonData people = service.getPeople(request, response);
            model.addAttribute("people", people.getData());
            model.addAttribute("userDocid", userDocid);
            model.addAttribute("username", username);
        }
        String date = DateHelper.format(new Date(), "yyyy-MM-dd");
        model.addAttribute("date", date.replace(" ", "T"));
        model.addAttribute("timestamp", System.currentTimeMillis());
        return "alleyway/querytrack";
    }

    @ResponseBody
    @RequestMapping(value = "/record/getTrack", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JSONArray getTrack(String infoId, String date){
       return service.getTrack(infoId, date);
    }


    /**
     * 出入学校
     *
     * @param request
     * @param response
     * @param model
     * @return
     */
    @RequestMapping(value = "/outinschool", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String Outinschool(HttpServletRequest request, HttpServletResponse response, Model model) {
        SysUser userinfo = userredis.get(request);
        model.addAttribute("index", userinfo);
        model.addAttribute("resourcedomain", WebConfig.getResourceDomain());
        model.addAttribute("startTime", DateHelper.format(new Date(), "yyyy-MM-dd 00:00").replace(" ", "T"));
        model.addAttribute("endTime", DateHelper.format(new Date(), "yyyy-MM-dd HH:mm").replace(" ", "T"));
        model.addAttribute("timestamp", System.currentTimeMillis());
        return "alleyway/outinschool";
    }

    @ResponseBody
    @RequestMapping(value = "/outinschool/detailedrecord", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData getOutInSchoolDetailedRecord(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return service.getOutInSchoolDetailedRecord(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/outinschool/recordList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData getOutInSchoolRecordList(HttpServletRequest request, HttpServletResponse response, int start, int limit, String startTime, String endTime) {
        try {
            return service.getOutInSchoolRecordList(request, response, start, limit, startTime, endTime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 出入详细记录
     *
     * @return
     */
    @RequestMapping(value = "/detailedrecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String getDetailedRecord(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        model.addAttribute("timestamp", System.currentTimeMillis());
        try {
            JsonData result = service.getDetailedRecord(request, response, uid);
            if (result.isSuccess()) {
                if (result.getData().size() > 0) {
                    JSONObject item = result.getData().getJSONObject(0);
                    model.addAttribute("uid", item.getString("uid"));
                    model.addAttribute("name", item.getString("name"));
                    model.addAttribute("code", item.getString("code"));
                    model.addAttribute("orgname", item.getString("orgname"));
                    model.addAttribute("cardsn", item.getString("cardsn"));
                    model.addAttribute("areacode", item.getString("areacode"));
                    model.addAttribute("inouttype", item.getString("inouttype"));
                    model.addAttribute("recordtime", item.getString("recordtime"));
                    model.addAttribute("doorname", item.getString("doorname"));
                    model.addAttribute("recordimg", item.getString("recordimg"));
                    return "alleyway/detailedrecord";
                } else {
                    return ViewErrorClosePage(request, response, model, "出入记录不存在");
                }
            } else {
                return ViewErrorClosePage(request, response, model, result.getMsg());
            }
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    //临时授权
    @RequestMapping(value = "/tempauth/infomsg", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String infoMsg(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            SysUser sysuser = userredis.get(request);
            if (sysuser.getUsertype() != 3) {
                return ViewErrorClosePage(request, response, model, "用户类型无权限");
            }
            String posrank = service.getInfoPosRank(request, response, sysuser.getDocid());
            if (StringUtils.isBlank(posrank)) {
                return ViewErrorClosePage(request, response, model, "职级分类未设置");
            }
            if (!posrank.equals("1") && !posrank.equals("2")) {
                return ViewErrorClosePage(request, response, model, "职级分类无权限");
            }
            model.addAttribute("resourcedomain", WebConfig.getResourceDomain());
            model.addAttribute("apptype", WebConfig.getApptype());
            return "alleyway/tempauth/infomsg";
        } catch (Exception ex) {
            Log.error(this.getClass(), ex.getMessage());
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/tempauth/getInfoMsg", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData getInfoMsg(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        try {
            return service.getInfoMsg(request, response, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/tempauth/publicarea", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String publicArea(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            model.addAttribute("infoid", uid);
            return "alleyway/tempauth/publicarea";
        } catch (Exception ex) {
            Log.error(this.getClass(), ex.getMessage());
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/tempauth/getPublicArea", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData getPublicArea(HttpServletRequest request, HttpServletResponse response) {
        try {
            return service.getPublicArea(request, response);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/tempauth/tempauthmsg", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public String getSomeTempAuthMsg(HttpServletRequest request, HttpServletResponse response, Model model, String infoid, String areacode) {
        try {
            JsonData jd = service.getSomeTempAuthMsg(request, response, infoid, areacode);
            if (jd.isSuccess()) {
                JSONObject jo = jd.getData().getJSONObject(0);
                model.addAttribute("infoid", jo.getString("uid"));
                model.addAttribute("infocode", jo.getString("code"));
                model.addAttribute("infoname", jo.getString("name"));
                model.addAttribute("orgname", jo.getString("orgname"));
                model.addAttribute("areaname", jo.getString("areaname"));
                model.addAttribute("areacode", areacode);
            }
            return "alleyway/tempauth/tempauthmsg";
        } catch (Exception ex) {
            Log.error(this.getClass(), ex.getMessage());
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/tempauth/saveTempAuth", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult saveTempAuth(HttpServletRequest request, HttpServletResponse response, String infoid, String areacode, String starttime, String endtime, String allewaytimes,String remark) {
        try {
            return service.saveTempAuth(request, response, infoid, areacode, starttime, endtime, allewaytimes,remark);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    //出入查询
    @RequestMapping(value = "/accessquery", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String accessquery(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            model.addAttribute("resourcedomain", WebConfig.getResourceDomain());
            model.addAttribute("date", DateHelper.format(new Date(), "yyyy-MM-dd"));
            return "alleyway/accessquery";
        } catch (Exception ex) {
            Log.error(this.getClass(), ex.getMessage());
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/accessquery/getAccessQuery", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData getAccessQuery(HttpServletRequest request, HttpServletResponse response,String key,String date,int start,int limit){
        try {
            return service.getAccessQuery(request, response, key, date, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/cancelDoor", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult cancelDoor(String uid) {
        try {
            return service.cancelDoor(uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getElevatorFloors", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getElevatorFloors(HttpServletRequest request,String deviceId,String infoType,String uid){
        try {
            return service.getElevatorFloors(request,deviceId,infoType,uid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getAllElevatorDevices", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getAllElevatorDevices() {
        try {
            return service.getAllElevatorDevices();
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

}
