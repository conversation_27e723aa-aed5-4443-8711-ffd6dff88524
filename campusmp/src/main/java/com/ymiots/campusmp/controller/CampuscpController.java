package com.ymiots.campusmp.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.ymiots.campusmp.service.CampuscpService;
import com.ymiots.framework.common.JsonResult;

@Controller
@RequestMapping("/campuscp")
public class CampuscpController extends BaseController{

	@Autowired
	CampuscpService cpserver;
	
	@RequestMapping(value ="/oauth",method = RequestMethod.GET)
	public String OAuth(HttpServletRequest request, HttpServletResponse response, Model model, String access_token) {
		try {
			JsonResult result= cpserver.CampusCPOAuth(request, response, access_token);
			if(result.isSuccess()) {
				String backurl=result.getMsg();
				if(StringUtils.isBlank(backurl)) {
					backurl="/";
				}
				return ViewLoadingPage(request, response, model, backurl);
			}else {
				return ViewErrorClosePage(request, response, model, result.getMsg());
			}
		} catch (Exception ex) {
			return ViewErrorClosePage(request, response, model, ex.getMessage());
		}
	}
}
