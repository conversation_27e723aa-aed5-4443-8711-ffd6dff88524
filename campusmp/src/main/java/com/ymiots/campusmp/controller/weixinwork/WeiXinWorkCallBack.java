package com.ymiots.campusmp.controller.weixinwork;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.common.RestRequest;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.controller.weixinwork.aes.WXBizMsgCrypt;
import com.ymiots.campusmp.entity.card.OrgFramework;
import com.ymiots.campusmp.service.AccessTokenService;
import com.ymiots.campusmp.utils.DateUtil;
import com.ymiots.campusmp.utils.MessageUtil;
import com.ymiots.campusmp.utils.weixin.WeiXinWork;
import com.ymiots.framework.common.Log;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024-06-17 10:00:21
 */
@RestController
@RequestMapping("/work")
public class WeiXinWorkCallBack {

    @Autowired
    private DBService dbService;

    @Autowired
    AccessTokenService token;

    @Autowired
    WeiXinWork weixinwork;

    @Autowired
    RestRequest request;

    private final Logger logger = LoggerFactory.getLogger(WeiXinWorkCallBack.class);


    /**
     * get 请求  验签.
     *
     * @param msgSignature 加密
     * @param timestamp    时间戳
     * @param nonce        随机
     * @param echostr      .
     * @param response     .
     * @throws Exception .
     */
    @GetMapping(value = "/callback")
    public void reveiceMsg(@RequestParam(name = "msg_signature") final String msgSignature,
                           @RequestParam(name = "timestamp") final String timestamp,
                           @RequestParam(name = "nonce") final String nonce,
                           @RequestParam(name = "echostr") final String echostr,
                           final HttpServletResponse response) throws Exception {
        String token = WebConfig.getWeixinToken();
        String corpid = WebConfig.getWeixinWorkCorpid();
        String encodingAesKey = WebConfig.getWeixinEncodingAESKey();
        //企业回调的url-----该url不做任何的业务逻辑，仅仅微信查看是否可以调通.
        WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, encodingAesKey,corpid);
        // 随机字符串
        String sEchoStr = wxcpt.VerifyURL(msgSignature, timestamp, nonce, echostr);
        PrintWriter out = response.getWriter();
        try {
            //必须要返回解密之后的明文
            if (StringUtils.isBlank(sEchoStr)) {
                System.out.println("URL验证失败");
            } else {
                System.out.println("验证成功!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        out.write(sEchoStr);
        out.flush();
    }

    /**
     * 企业微信客户联系回调.
     *
     * @param request       request
     * @param sMsgSignature 签名
     * @param sTimestamp    时间戳
     * @param sNonce        随机值
     * @return success
     */
    @PostMapping(value = "/callback")
    public String callback(final HttpServletRequest request,
                           @RequestParam(name = "msg_signature") final String sMsgSignature,
                           @RequestParam(name = "timestamp") final String sTimestamp,
                           @RequestParam(name = "nonce") final String sNonce) {

        Map<String, String> dataMap = new HashMap<>();

        try {
            InputStream inputStream = request.getInputStream();
            String sPostData = IOUtils.toString(inputStream, "UTF-8");
            String token = WebConfig.getWeixinToken();
            String corpid = WebConfig.getWeixinWorkCorpid();
            String encodingAesKey = WebConfig.getWeixinEncodingAESKey();
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, encodingAesKey,corpid);
            //解密
            String sMsg = wxcpt.DecryptMsg(sMsgSignature, sTimestamp, sNonce, sPostData);
            //将post数据转换为map
            dataMap = MessageUtil.parseXml(sMsg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //然后去操作你的业务逻辑
        String changeType = dataMap.get("ChangeType");
        logger.warn("infoType"+changeType);
        boolean flag = false;
        if (StringUtils.isNotBlank(changeType)) {
            switch (changeType) {
                case "create_user":
                    //新增人员
                    flag = addInfo(dataMap);
                    logger.info("执行状态{},新增人员信息{},执行人{},执行时间{},执行数据{}", flag,dataMap.get("UserID"), dataMap.get("FromUserName"), DateUtil.timestampTo_yyyy_MM_dd_HH_mm_ss(Long.parseLong(dataMap.get("CreateTime"))), dataMap);
                    break;
                case "update_user":
                    //更新人员
                    flag = updateInfo(dataMap);
                    logger.info("执行状态{},修改人员信息{},执行人{},执行时间{},执行数据{}",flag, dataMap.get("UserID"), dataMap.get("FromUserName"), DateUtil.timestampTo_yyyy_MM_dd_HH_mm_ss(Long.parseLong(dataMap.get("CreateTime"))),dataMap);
                    break;
                case "delete_user":
                    //删除人员信息
                    flag = deleteInfo(dataMap);
                    logger.info("执行状态{},删除人员信息{},执行人{},执行时间{},执行数据{}",flag, dataMap.get("UserID"), dataMap.get("FromUserName"), DateUtil.timestampTo_yyyy_MM_dd_HH_mm_ss(Long.parseLong(dataMap.get("CreateTime"))),dataMap);
                    break;
                case "create_party":
                    //新增部门事件
                    flag = addDepartment(dataMap);
                    logger.info("执行状态{},新增部门事件id{},执行人{},执行时间{},执行数据{}",flag, dataMap.get("Id"), dataMap.get("FromUserName"), DateUtil.timestampTo_yyyy_MM_dd_HH_mm_ss(Long.parseLong(dataMap.get("CreateTime"))),dataMap);
                    break;
                case "update_party":
                    //更新部门事件
                    flag = updateDepartment(dataMap);
                    logger.info("执行状态{},更新部门事件id{},执行人{},执行时间{},执行数据{}",flag, dataMap.get("Id"), dataMap.get("FromUserName"), DateUtil.timestampTo_yyyy_MM_dd_HH_mm_ss(Long.parseLong(dataMap.get("CreateTime"))),dataMap);
                    break;
                case "delete_party":
                    //删除部门事件
                    flag = deleteDepartment(dataMap);
                    logger.info("执行状态{},删除部门事件id{},执行人{},执行时间{},执行数据{}",flag, dataMap.get("Id"), dataMap.get("FromUserName"), DateUtil.timestampTo_yyyy_MM_dd_HH_mm_ss(Long.parseLong(dataMap.get("CreateTime"))),dataMap);
                    break;
            }
        }
        if (flag) {
            return "success";
        } else {
            return "false";
        }
    }

    private boolean deleteDepartment(Map<String, String> dataMap) {
        String sql = "select ct.uid " +
                "from tb_card_teachstudinfo ct " +
                "join tb_card_orgframework co on ct.orgcode = co.code " +
                " where co.orgcode = '" + dataMap.get("Id") + "'";
        List<String> infoNum = dbService.queryFields(sql, String.class);
        if (CollectionUtil.isEmpty(infoNum)) {
            String delSQL = "delete from tb_card_orgframework where orgcode = '" + dataMap.get("Id") + "'";
            dbService.excuteSql(delSQL);
        }
        return true;
    }

    private boolean updateDepartment(Map<String, String> dataMap) {

        return true;
    }

    private boolean addDepartment(Map<String, String> dataMap) {
        //查询父节点id
        String selOsOrgFramework = "select uid,code from tb_card_orgframework where orgcode = '" + dataMap.get("ParentId") + "'";
        OrgFramework orgFrameworkParent = dbService.queryForOne(selOsOrgFramework, OrgFramework.class);
        String lastCode = orgFrameworkParent.getCode();
        String parentUid = orgFrameworkParent.getUid();
        String id = dataMap.get("Id");
        int count = dbService.getCount("tb_card_orgframework", "parentid='" + parentUid + "'");
        String code = lastCode + String.format("%03d", count + 1);
        //根据id获取名字
        Log.info(this.getClass(), "开始获取部门[" +id  + "]数据");
        String ACCESS_TOKEN = token.GetWorkAccessToken();
        if (StringUtils.isBlank(ACCESS_TOKEN)) {
            Log.error(this.getClass(), "企业ACCESSTOEKN为空，部门员工姓名未获取");
        }
        //获取到部门名称
        JSONObject departmentData = weixinwork.getWeiXinWorkDepartmentData(ACCESS_TOKEN, id);
        String name = "新建部门";
        if (departmentData != null) {
            name = departmentData.getJSONObject("department").getString("name");
        }
        //防止重复写入
        int num = dbService.getCount("tb_card_orgframework", "orgcode = '" + dataMap.get("Id") + "'");
        if (num > 0) {
            return true;
        }
        String updateSQL = "insert into  tb_card_orgframework(uid,code,orgcode,orgtype,name,parentid,createdate,creatorid,status) " +
                "values(uuid(),?,?,?,?,?,now(),'企业微信自动同步',1)";
        dbService.excuteSql(updateSQL,code, dataMap.get("Id"), 2, name,orgFrameworkParent.getUid());
        return true;
    }

    private boolean deleteInfo(Map<String, String> dataMap) {
        return true;
    }

    private boolean updateInfo(Map<String, String> dataMap) {
        StringBuilder sql = new StringBuilder("UPDATE tb_card_teachstudinfo SET ");
        boolean isFirst = true;

        if (StringUtils.isNotBlank(dataMap.get("NewUserID"))) {
            sql.append("code = '").append(dataMap.get("NewUserID")).append("'");
            isFirst = false;
            String weiSql = "update tb_weixin_workuser set uid = '" + dataMap.get("NewUserID") + "' where uid = '" + dataMap.get("UserID") + "'";
            dbService.excuteSql(weiSql);
        }

        if (StringUtils.isNotBlank(dataMap.get("Department"))) {
            String[] departments = dataMap.get("Department").split(",");
            String department = departments[0];

            String selSQL = "select code from tb_card_orgframework where orgcode ='" + department + "'";
            String orgCode = dbService.queryOneField(selSQL, String.class);
            if (!isFirst) {
                sql.append(", ");
            }
            sql.append("orgcode = '").append(orgCode).append("'");
        }
        sql.append(" where code  ='").append(dataMap.get("UserID")).append("'");
        dbService.excuteSql(sql.toString());
        return true;
    }

    //微信回调新增人员信息
    private boolean addInfo(Map<String, String> dataMap) {
        try {
            String sql1="select uid,orgcode orgCode,code from tb_card_orgframework where orgcode = '"+dataMap.get("Department")+"'";
            OrgFramework orgFramework = dbService.queryForOne(sql1, OrgFramework.class);
            String infoId = UUID.randomUUID().toString();
            String userID = dataMap.get("UserID");
            //根据id获取名字
            JSONObject item = new JSONObject();
            Log.info(this.getClass(), "开始员工[" +userID  + "]数据");
            String ACCESS_TOKEN = token.GetWorkAccessToken();
            if (StringUtils.isBlank(ACCESS_TOKEN)) {
                Log.error(this.getClass(), "企业ACCESSTOEKN为空，部门员工姓名未获取");
            }
            if ("1".equals(WebConfig.getIsWorkYunToken())) {
                String LOCAL_ACCESS_TOKEN = "http://pay.ymiots.com/weixinouth/getWorkDepartmentUserDetail";
                Map<String, String> params = new HashMap<String, String>();
                params.put("campusid", WebConfig.getCampusid());
                params.put("ACCESS_TOKEN", ACCESS_TOKEN);
                params.put("userId", userID);
                item = request.doPostFormRequest(LOCAL_ACCESS_TOKEN, params, JSONObject.class);
            } else {
                item = weixinwork.GetWeiXinWorkUserData(ACCESS_TOKEN, userID);
            }
            String name = "";
            String mobile = "";
            String email = "";
            String thumb_avatar = "";
            if (item != null) {
                name = item.getString("name");
                mobile = item.getString("mobile");
                email = item.getString("email");
                thumb_avatar = item.getString("thumb_avatar");
                Log.info(this.getClass(), item.toJSONString());
            }
            //判断是否系统有删除该人员
            int count = dbService.getCount("tb_card_teachstudinfo", "code = '" + dataMap.get("UserID") + "'");
            if (count > 0) {
                return true;
            }
            String sql2 = "insert into tb_card_teachstudinfo(uid,mobile,code,orgcode,name,email,createdate,creatorid,modifydate,modifierid,status,infotype) values (?,?,?,?,?,?,now(),'企业微信自动同步',now(),'企业微信自动同步',1,1)";
            dbService.excuteSql(sql2,infoId, mobile, dataMap.get("UserID"), orgFramework.getCode(), name,email);
            sql2 = "insert into tb_weixin_workuser(uid,docid,name,mobile,email,headimgurl,createdate) values(?,?,?,?,?,?,now())";
            dbService.excuteSql(sql2, dataMap.get("UserID"), infoId, name, mobile, email, thumb_avatar);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }
}
