package com.ymiots.campusmp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.entity.*;
import com.ymiots.campusmp.model.R;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.OAuthService;
import com.ymiots.campusmp.service.UcenterService;
import com.ymiots.campusmp.service.VisitorService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.filter.Permission;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

@Controller
@RequestMapping("/ucenter")
public class UcenterController extends BaseController{

	@Autowired
	UserRedis userredis;

	@Autowired
	UcenterService ucenter;

	@Autowired
	OAuthService oauth;

	@Autowired
	VisitorService visitorservice;

    @Autowired
    private DBService dbService;


	@RequestMapping(value ="",method = RequestMethod.GET)
	@Permission(authorization=true,usertype=-1)
	public String Index(HttpServletRequest request, HttpServletResponse response, Model model) {
		final SysUser userinfo= userredis.get(request);
		List<BindUserInfo> list= ucenter.getOtherUser(request, response, userinfo.getOpenid(),userinfo.getUsertype());
        String orgcode = userinfo.getOrgcode();
        if(list.size()==0) {
			//当所有绑定人员都被解除绑定时，删除缓存和cookies,跳转到绑定界面
			String backurl ="/";
			userredis.remove(request);
			Cookie cookie = userredis.getTokenCookie(request);
			if(cookie!=null) {
				// 一年有效期
				cookie.setMaxAge(-1);
				response.addCookie(cookie);
			}
			try {
				backurl = URLEncoder.encode(WebConfig.thisPageUrl(request, false),"UTF-8");
			}catch(Exception ex) {
				Log.error(UcenterController.class, ex.getMessage());
			}
            if (StringUtils.isBlank(orgcode)) {
                orgcode = "";
            }
			return ViewLoadingPage(request, response, model, "/weixinauth/oauthcode?backurl="+backurl+"&orgcode=" + orgcode);
		}
        if(list.size()>0) {
            List<BindUserInfo> clist = CollectionUtils.where(list, new CallBackFilter<BindUserInfo>() {
                @Override
                public Boolean filter(BindUserInfo model) {
                    return model.getUid().equals(userinfo.getUid());
                }
            });
            if (clist.size() == 0) {
                //当前用户被解除绑定，自动切换到第一个用户
                String uid = list.get(0).getUid();
                return ViewLoadingPage(request, response, model, "/ucenter/changebind?uid=" + uid);
            }
        }
		int isaddbind=0;
		if(!WebConfig.EnableCampusCPService()) {
			if(userinfo.getUsertype()==2 || userinfo.getUsertype()== 3 && WebConfig.getApptype().equals("1")) {
				isaddbind=1;
			}
		}

        JSONArray userPhotoNumberja = ucenter.getUserPhotoNumber(request, response);
        if (!userPhotoNumberja.isEmpty()) {
            String mobile = userPhotoNumberja.getJSONObject(0).getString("mobile");
            if (StringUtils.isBlank(mobile)) {
                mobile = "完善手机号";
            }
            model.addAttribute("mobile", mobile);
        }
        JSONArray userEmailja = ucenter.getUserEmail(request, response);
        if (!userEmailja.isEmpty()) {
            String email = userEmailja.getJSONObject(0).getString("email");
            if (StringUtils.isBlank(email)) {
                email = "完善Email";
            }
            model.addAttribute("email", email);
        }

        model.addAttribute("sexval", userinfo.getSex());
        if ("1".equals(userinfo.getSex())) {
            model.addAttribute("sex", "男");
        }else {
            model.addAttribute("sex", "女");
        }
        model.addAttribute("isaddbind", isaddbind);
		model.addAttribute("userinfo", userinfo);
		model.addAttribute("userlist", list);

		//访客
		if(userinfo.getUsertype()==4) {
			JSONObject visitor= visitorservice.GetVisitorInfo(request);
			if(visitor!=null) {
				model.addAttribute("company", visitor.getString("company"));
				model.addAttribute("department", visitor.getString("department"));
				model.addAttribute("position", visitor.getString("position"));
			}else {
				return ViewErrorPage(request, response, model, "访客信息不存在",1, "/");
			}
		}
        //直接访问程序
        if(userinfo.getUsertype()==5) {
            JSONObject visitor= getPasswordFreeLoginInfo(request);
            if(visitor!=null) {
                model.addAttribute("company", visitor.getString("name"));
                model.addAttribute("orgname", visitor.getString("name"));
            }else {
                return ViewErrorPage(request, response, model, "访客信息不存在",1, "/");
            }
        }

		model.addAttribute("timestamp", System.currentTimeMillis());
		return "ucenter/index";
	}

    private JSONObject getPasswordFreeLoginInfo(HttpServletRequest request) {
        //tb_card_orgframework
        String sql = "select name from tb_card_orgframework where code = '" + userredis.get(request).getOrgcode() + "'";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            return null;
        } else {
            return list.getJSONObject(0);
        }
    }

    @RequestMapping(value ="/changebind",method = RequestMethod.GET)
	@Permission(authorization=true,usertype=-1)
	public String ChangeBind(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
		model.addAttribute("timestamp", System.currentTimeMillis());
		SysUser user = oauth.getOAuthSysUser(request,response, uid);
		if (user != null) {
			return ViewLoadingPage(request, response, model, "/ucenter");
		}else {
			return ViewErrorPage(request, response, model, "无法切换绑定用户",1, "/ucenter");
		}
	}

    @RequestMapping(value ="/newchangebind",method = RequestMethod.GET)
    @Permission(authorization=true,usertype=-1)
    public String NewChangeBind(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        model.addAttribute("timestamp", System.currentTimeMillis());
        SysUser user = oauth.getOAuthSysUser(request,response, uid);
        if (user != null) {
            return ViewLoadingPage(request, response, model, "/");
        }else {
            return ViewErrorPage(request, response, model, "无法切换绑定用户",1, "/ucenter");
        }
    }

    @RequestMapping(value ="/photonumber",method = RequestMethod.GET)
    @Permission(authorization=true,usertype=-1)
    public String photoNumber(HttpServletRequest request, HttpServletResponse response, Model model) {
        JSONArray list= ucenter.getUserPhotoNumber(request, response);
        if(!list.isEmpty()) {
            JSONObject item=list.getJSONObject(0);
            model.addAttribute("mobile", item.getString("mobile"));
        }
        model.addAttribute("timestamp", System.currentTimeMillis());
        return "ucenter/photonumber";
    }

    /**
     * 完善手机号
     * @param request
     * @param response
     * @param number
     * @return
     */
    @ResponseBody
    @RequestMapping(value ="/updatePhotoNumber",method = RequestMethod.POST)
    @Permission(authorization=true,usertype=-1)
    public JsonResult updatePhotoNumber(HttpServletRequest request, HttpServletResponse response,String number){
        try {
            return ucenter.updatePhotoNumber(request, response, number);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }

    /**
     * 完善e-mail
     * @param request
     * @param response
     * @param email
     * @return
     */
    @ResponseBody
    @RequestMapping(value ="/updateEmail",method = RequestMethod.POST)
    @Permission(authorization=true,usertype=-1)
    public JsonResult updateEmail(HttpServletRequest request, HttpServletResponse response,String email){
        try {
            return ucenter.updateEmail(request, response, email);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }

    /**
     * 修改性别
     * @param request
     * @param response
     * @param sex
     * @return
     */
    @ResponseBody
    @RequestMapping(value ="/updateSex",method = RequestMethod.POST)
    @Permission(authorization=true,usertype=-1)
    public JsonResult updateSex(HttpServletRequest request, HttpServletResponse response,String sex){
        try {
            return ucenter.updateSex(request, response, sex);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }

	/**
	 *  联系人
	 * @param request
	 * @param response
	 * @param model
	 * @return
	 */
	@RequestMapping(value ="/linkman",method = RequestMethod.GET)
	@Permission(authorization=true,usertype=-1)
	public String LinkMan(HttpServletRequest request, HttpServletResponse response, Model model) {
		SysUser us=userredis.get(request);
		if(us.getUsertype()==4) {
			return ViewErrorPage(request, response, model, "访客无需填写联系人",1, "/ucenter");
		}
		JSONArray list= ucenter.getInfoLinkMan(request, response);
		if(list.size()==0) {
			return ViewErrorPage(request, response, model, "本人信息不存在",1, "/ucenter");
		}

        JsonData otherInfoLinkjd = ucenter.getOtherInfoLink(request, response);
        if (otherInfoLinkjd.isSuccess()) {
            model.addAttribute("otherlink", otherInfoLinkjd.getData());
        }
        JSONObject item=list.getJSONObject(0);
		model.addAttribute("linkman1", item.getString("linkman1"));
		model.addAttribute("linkmobile1", item.getString("linkmobile1"));
		model.addAttribute("linkdes1", item.getString("linkdes1"));
		model.addAttribute("linkman2", item.getString("linkman2"));
		model.addAttribute("linkmobile2", item.getString("linkmobile2"));
		model.addAttribute("linkdes2", item.getString("linkdes2"));
		model.addAttribute("timestamp", System.currentTimeMillis());
		return "ucenter/linkman";
	}

	@ResponseBody
	@RequestMapping(value ="/savelinkman",method = RequestMethod.POST)
	@Permission(authorization=true,usertype=-1)
	public JsonResult SaveLinkMan(HttpServletRequest request, HttpServletResponse response, String linkman1, String linkmobile1, String linkdes1,
			String linkman2, String linkmobile2, String linkdes2) {
		try {
			return ucenter.SaveLinkMan(request, response, linkman1, linkmobile1, linkdes1,
					linkman2, linkmobile2, linkdes2);
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}

    @ResponseBody
    @RequestMapping(value ="/saveOtherInfoLink",method = RequestMethod.POST)
    @Permission(authorization=true,usertype=-1)
    public JsonResult saveOtherInfoLink(HttpServletRequest request, HttpServletResponse response,String linklist){
        try {
            return ucenter.saveOtherInfoLink(request, response, linklist);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value ="/delOtherInfoLink",method = RequestMethod.POST)
    @Permission(authorization=true,usertype=-1)
    public JsonResult delOtherInfoLink(HttpServletRequest request, HttpServletResponse response,String uid){
        try {
            return ucenter.delOtherInfoLink(request, response, uid);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }



    @RequestMapping(value ="/password",method = RequestMethod.GET)
    @Permission(authorization=true,usertype=-1)
    public String password(HttpServletRequest request, HttpServletResponse response, Model model) {
    	model.addAttribute("timestamp", System.currentTimeMillis());
        return "ucenter/password";
    }

    @RequestMapping(value = "/passwordAlter", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String passwordAlter(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("timestamp", System.currentTimeMillis());
        return "ucenter/passwordAlter";
    }

    @ResponseBody
    @RequestMapping(value ="/setPasswordAlter",method = RequestMethod.POST)
    @Permission(authorization=true,usertype=-1)
    public JsonResult setPasswordAlter(HttpServletRequest request, HttpServletResponse response,String password,String oldpassword) {
        try {
            return ucenter.setPasswordCenter(request, response, password, oldpassword);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }

    @RequestMapping(value = "/updateDetails", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String updateDetails(HttpServletRequest request, HttpServletResponse response, Model model) {
        JSONObject item = ucenter.getInfoDetail(request, response);
        String isLocal = item.getString("is_local");
        if(StringUtils.isBlank(isLocal)){
            isLocal = "1";
        }
        String sex = item.getString("sex");
        if (StringUtils.isBlank(sex)) {
            sex="1";
        }
        String linkdes1 = item.getString("linkdes1");
        if (StringUtils.isBlank(linkdes1)) {
            linkdes1="1";
        }
        String linkdes2 = item.getString("linkdes2");
        if (StringUtils.isBlank(linkdes2)) {
            linkdes2="1";
        }
        model.addAttribute("linkman1", item.getString("linkman1"));
        model.addAttribute("linkmobile1", item.getString("linkmobile1"));
        model.addAttribute("linkdes1", linkdes1);
        model.addAttribute("linkman2", item.getString("linkman2"));
        model.addAttribute("linkmobile2", item.getString("linkmobile2"));
        model.addAttribute("linkdes2", linkdes2);
        model.addAttribute("currentAdress", item.getString("current_adress"));
        model.addAttribute("link1Employer", item.getString("link1_employer"));
        model.addAttribute("link2Employer", item.getString("link2_employer"));
        model.addAttribute("isLocal", isLocal);
        model.addAttribute("localAddress", item.getString("localAddress"));
        model.addAttribute("name", item.getString("name"));
        model.addAttribute("sex", sex);
        model.addAttribute("registrationNum", item.getString("registrationNum"));
        model.addAttribute("registrationCode", item.getString("registrationCode"));
        model.addAttribute("address", item.getString("address"));
        model.addAttribute("registered", item.getString("registered"));
        model.addAttribute("timestamp", System.currentTimeMillis());
        model.addAttribute("grade", item.getString("grade"));
        model.addAttribute("schoolnumber", item.getString("schoolnumber"));
        model.addAttribute("oldschool", item.getString("oldschool"));
        model.addAttribute("isTransfer", item.getString("isTransfer"));
        model.addAttribute("transferreason", item.getString("transferreason"));
        model.addAttribute("transfertype", item.getString("transfertype"));
        return "ucenter/updateDetails";
    }

    @ResponseBody
    @RequestMapping(value ="/saveInfoDetail",method = RequestMethod.POST)
    public JsonResult saveInfoDetail(HttpServletRequest request, HttpServletResponse response, String studentName,String sex,String registrationNum,String registrationCode,String address,String registered,String linkman1,String linkmobile1,String linkdes1,String link1Employer,String linkman2,String linkmobile2,String linkdes2,String link2Employer,String isLocal,String localAddress,String grade,String schoolnumber,String oldschool,String isTransfer,String transferreason,String transfertype) {
        try {
            return ucenter.saveInfoDetail(request, response, studentName,sex,registrationNum,registrationCode,address,registered,linkman1,linkmobile1,linkdes1,link1Employer,linkman2,linkmobile2,linkdes2,link2Employer,isLocal,localAddress,grade,schoolnumber,oldschool,isTransfer,transferreason,transfertype);
        } catch (Exception ex) {
            return Json.getJsonResult(false,ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value ="/setpassword",method = RequestMethod.POST)
    @Permission(authorization=true,usertype=-1)
    public JsonResult setPassword(HttpServletRequest request, HttpServletResponse response,String password,String oldpassword) {
        try {
            return ucenter.setPassword(request, response, password, oldpassword);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }

    @RequestMapping(value ="/qrcode",method = RequestMethod.GET)
    @Permission(authorization=true,usertype=-1)
    public String Door(HttpServletRequest request, HttpServletResponse response, Model model) {
    	model.addAttribute("timestamp", System.currentTimeMillis());
        try {
        	JsonResult result= ucenter.CreateDoorQRCode(request);
        	if(result.isSuccess()) {
                boolean issubscribe = ucenter.isSubscribe(request);
                model.addAttribute("isSubscribe",issubscribe);
                model.addAttribute("qrcode",String.format("data:image/png;base64,%s", result.getData().getString("qrcode")));
        		model.addAttribute("endtime", result.getData().getString("endtime"));
        		return "ucenter/qrcode";
        	}else {
        		return ViewErrorPage(request, response, model, result.getMsg());
        	}
        } catch (Exception e) {
            return ViewErrorPage(request, response, model, e.getMessage());
        }
    }

    @RequestMapping(value ="/login",method = RequestMethod.GET)
    public String login(HttpServletRequest request, HttpServletResponse response, Model model) {
    	String loginmodel = request.getHeader("loginmodel");
    	if(StringUtils.isBlank(loginmodel)) {
    		loginmodel="1";
    	}
    	model.addAttribute("timestamp", System.currentTimeMillis());
    	return "pc/login"+loginmodel;
    }


    @ResponseBody
    @RequestMapping(value ="/logincheck",method = RequestMethod.POST)
    public JsonResult logincheck(HttpServletRequest request, HttpServletResponse response, String usercode, String pwd) {
    	try {
    		return ucenter.logincheck(request, response, usercode, pwd);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value ="/logout",method = RequestMethod.POST)
    public JsonResult logout(HttpServletRequest request, HttpServletResponse response) {
    	try {
            return ucenter.logout(request, response);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value ="/gettotalmenu",method = RequestMethod.POST)
    public R<?> gettotalmenu(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<WeiXinMenu> weiXinMenus = ucenter.gettotalmenu(request, response);

            return R.ok(weiXinMenus);
        } catch (Exception ex) {
            return R.ok(ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value ="/getcommonmenu",method = RequestMethod.POST)
    public R<?> getcommonmenu(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<WeiXinMenuItem> weiXinMenus = ucenter.getcommonmenu(request, response);

            return R.ok(weiXinMenus);
        } catch (Exception ex) {
            return R.ok(ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value ="/getinfo",method = RequestMethod.POST)
    public R<?> getinfo(HttpServletRequest request, HttpServletResponse response) {
        try {
            UserInfo userInfo = ucenter.getinfo(request, response);

            return R.ok(userInfo);
        } catch (Exception ex) {
            return R.ok(ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value ="/updatevisitorcompany",method = RequestMethod.POST)
    public JsonResult updatevisitorcompany(HttpServletRequest request, HttpServletResponse response, String company, String department, String position) {
    	try {
            return visitorservice.UpdatevVsitorCompany(request, response, company, department, position);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }

}
