package com.ymiots.campusmp.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.entity.dto.visitor.AuditApplyRequest;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.SysConfigService;
import com.ymiots.campusmp.service.VisitorService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.filter.Permission;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/visitor")
public class VisitorController extends BaseController {

    @Autowired
    VisitorService visitor;

    @Autowired
    UserRedis userredis;

    @Autowired
    SysConfigService syscfg;

    @RequestMapping(value = "/visitorform", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String visitorform(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("visitorPrintQRCode", syscfg.get("visitorPrintQRCode"));
        model.addAttribute("keyboard", syscfg.get("keyboard"));
        return "pc/visitor/visitorform";
    }

    @RequestMapping(value = "/visitorformhand", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String visitorformhand(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("visitorPrintQRCode", syscfg.get("visitorPrintQRCode"));
        model.addAttribute("keyboard", syscfg.get("keyboard"));
        return "pc/visitor/visitorformhand";
    }

    @ResponseBody
    @RequestMapping(value = "/SubmitVisiterInfo", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult submitVisitorInfo(HttpServletRequest request, HttpServletResponse response, String visitorinfo) {
        try {
            return visitor.submitVisitorInfo(request, response, visitorinfo);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/visitorrecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String visitorrecord(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("timestamp", System.currentTimeMillis());
        model.addAttribute("resourceDomain", WebConfig.getResourceDomain());
        model.addAttribute("startTime", DateHelper.format(DateHelper.addDays(new Date(), -7), "yyyy-MM-dd"));
        model.addAttribute("endTime", DateHelper.format(new Date(), "yyyy-MM-dd"));
        model.addAttribute("visitorHelpOneself", syscfg.get("visitorHelpOneself"));
        model.addAttribute("keyboard", syscfg.get("keyboard"));
        return "pc/visitor/visitorrecord";
    }


    @ResponseBody
    @RequestMapping(value = "/getVisitorRecordList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getVisitorRecordList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit, String startTime, String endTime) {
        try {
            return visitor.getVisitorRecordList(request, response, key, start, limit, startTime, endTime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/getVisitorRecordOne", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getVisitorRecordOne(HttpServletRequest request, HttpServletResponse response, String recordId) {
        try {
            return visitor.getVisitorRecordOne(request, response, recordId);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/addVisitorToBlack", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult addVisitorToBlack(HttpServletRequest request, HttpServletResponse response, String uid, String blackremark) {
        try {
            return visitor.addVisitorToBlack(request, response, uid, blackremark);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/visitorpassrecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String visitorpassrecord(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("resourceDomain", WebConfig.getResourceDomain());
        model.addAttribute("startTime", DateHelper.format(DateHelper.addDays(new Date(), -7), "yyyy-MM-dd"));
        model.addAttribute("endTime", DateHelper.format(new Date(), "yyyy-MM-dd"));
        model.addAttribute("keyboard", syscfg.get("keyboard"));
        return "pc/visitor/visitorpassrecord";
    }

    @ResponseBody
    @RequestMapping(value = "/getVisitorPassRecordList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getVisitorPassRecordList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit, String startTime, String endTime) {
        try {
            return visitor.getVisitorPassRecordList(request, response, key, start, limit, startTime, endTime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/UpdateVisitorFace", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult UpdateVisitorFace(HttpServletRequest request, HttpServletResponse response, String uid, String visitorid, String photo) {
        try {
            return visitor.UpdateVisitorFace(request, response, uid, visitorid, photo);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/redownface", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult redownface(HttpServletRequest request, HttpServletResponse response, String recordid, String visitorId) {
        try {
            return visitor.reDownFace(request, response, recordid, visitorId);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/visitorleave", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String visitorleave(HttpServletRequest request, HttpServletResponse response, Model model, String idcard) {
        model.addAttribute("resourceDomain", WebConfig.getResourceDomain());
        if (!StringUtils.isBlank(idcard)) {
            JsonData result = visitor.GetVisitorRecord(request, response, idcard);
            if (result.isSuccess() && result.getData().size() > 0) {
                JSONObject item = result.getData().getJSONObject(0);
                model.addAttribute("visitorrecordid", item.getString("uid"));
                model.addAttribute("name", item.getString("name"));
                model.addAttribute("idcard", item.getString("idcard"));
                model.addAttribute("sex", item.getString("sex"));
                model.addAttribute("nation", item.getString("nation"));
                model.addAttribute("address", item.getString("address"));
                model.addAttribute("birthday", item.getString("birthday"));
                model.addAttribute("photo", ComHelper.getResourceUrl(WebConfig.getResourceDomain(), item.getString("photo")));
                model.addAttribute("reason", item.getString("reason"));
                model.addAttribute("byvisitor", item.getString("byvisitor"));
                model.addAttribute("plateno", item.getString("plateno"));
                model.addAttribute("things", item.getString("things"));
                model.addAttribute("mobile", item.getString("mobile"));
                model.addAttribute("createdate", item.getString("createdate"));
            }
            model.addAttribute("backurl", "/visitor/visitorrecord");
        } else {
            model.addAttribute("backurl", "/");
        }
        return "pc/visitor/visitorleave";
    }

    @ResponseBody
    @RequestMapping(value = "/pcGetVisitorRecord", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData pcGetVisitorRecord(HttpServletRequest request, HttpServletResponse response, String idcard) {
        try {
            return visitor.pcGetVisitorRecord(request, response, idcard);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/GetVisitorRecord", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonData GetVisitorRecord(HttpServletRequest request, HttpServletResponse response, String idcard) {
        try {
            return visitor.GetVisitorRecord(request, response, idcard);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/SubmitVisiterLeave", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult SubmitVisiterLeave(HttpServletRequest request, HttpServletResponse response, String recordid,String visitorid) {
        try {
            return visitor.SubmitVisiterLeave(request, response, recordid,visitorid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/getVisitorRecordNameList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getVisitorRecordNameList(HttpServletRequest request, HttpServletResponse response, String recordid) {
        try {
            return visitor.getVisitorRecordNameList(request, response, recordid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 跳转被访人员搜索页面
     *
     * @param request
     * @param response
     * @param model
     * @return
     */
    @RequestMapping(value = "/byvisitor", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String ByVisitor(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            if ("1".equals(syscfg.get("visitorusefaceorc")) && !visitor.ExistsVisitorFace(request)) {
                return ViewErrorPage(request, response, model, "请先采集人脸识别照片", 2, "/face/image?t=" + String.valueOf(System.currentTimeMillis()));
            }
            //获取未审核和未离开的访客记录
            JsonResult jr = visitor.getUnauditedAndUnLeaveRecord(request);
            if (!jr.isSuccess()) {
                model.addAttribute("issuccess", jr.isSuccess());
                model.addAttribute("type", jr.getMsg());
            }
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "visitor/byvisitor";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    /**
     * 跳转被访人员列表页面
     *
     * @param request
     * @param response
     * @param model
     * @param key
     * @return
     */
    @RequestMapping(value = "/byvisitorlist", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String byvisitorlist(HttpServletRequest request, HttpServletResponse response, Model model, String key) {
        try {
            model.addAttribute("key", key);
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "visitor/byvisitorlist";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    /**
     * 获取被访人员列表
     *
     * @param request
     * @param response
     * @param key
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getByVisitorList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getByVisitorList(HttpServletRequest request, HttpServletResponse response, String key) {
        try {
            return visitor.getByVisitorList(request, response, key);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/getByVisitorWithMobileList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getByVisitorWithMobileList(HttpServletRequest request, HttpServletResponse response, String key, String mobile) {
        try {
            return visitor.getByVisitorWithMobileList(request, response, key, mobile);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 跳转被访问人员页面
     *
     * @param request
     * @param response
     * @param model
     * @param byvisitorid
     * @return
     */
    @RequestMapping(value = "/subvisitor", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String subvisitor(HttpServletRequest request, HttpServletResponse response, Model model, String byvisitorid) {
        try {
            model.addAttribute("byvisitorid", byvisitorid);
            JSONObject byinfo = visitor.GetByVisitorInfo(request, byvisitorid);
            if (byinfo == null) {
                return ViewErrorClosePage(request, response, model, "被访人信息不存在");
            }
            model.addAttribute("byname", byinfo.getString("name"));
            model.addAttribute("byorgname", byinfo.getString("orgname"));

            model.addAttribute("timestamp", System.currentTimeMillis());
            JSONObject info = visitor.GetVisitorInfo(request);
            model.addAttribute("uid", info.getString("uid"));
            if (StringUtils.isNotBlank(info.getString("plateno"))) {
                String[] platenos = info.getString("plateno").split(",");
                for (int i = 0; i < platenos.length; i++) {
                    if (i == 0) {
                        model.addAttribute("plateno", platenos[i]);
                    } else {
                        model.addAttribute("plateno" + (i + 1), platenos[i]);
                    }
                }

            }
            model.addAttribute("name", info.getString("name"));
            model.addAttribute("idcard", info.getString("idcard"));
            model.addAttribute("mobile", info.getString("mobile"));
            model.addAttribute("idtypename", info.getString("idtypename"));

            return "visitor/subvisitor";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    /**
     * 保存访客预约
     *
     * @param request
     * @param response
     * @param byvisitorid
     * @param visittime
     * @param leavetime
     * @param reason
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/SubmitSubVisitor", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult SubmitSubVisitor(HttpServletRequest request, HttpServletResponse response,
                                       String byvisitorid, String visittime, String leavetime, String byVisitor,
                                       String reason, String plateno, String visitmobile,
                                       String personnum, String things, String othervisitor) {
        try {
            return visitor.SubmitSubVisitor(request, response, byvisitorid, visittime, leavetime, byVisitor, reason, plateno, visitmobile, personnum, things, othervisitor);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 跳转来访申请页面
     *
     * @param request
     * @param response
     * @param model
     * @return
     */
    @RequestMapping(value = "/visitorlist", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String visitorlist(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd");
            model.addAttribute("startTime", startTime);
            model.addAttribute("endTime", endTime);
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "visitor/visitorlist";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    /**
     * 生成访客二维码
     *
     * @param request
     * @param response
     * @param model
     * @return
     */
    @RequestMapping(value = "/visitorqrcode", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = 4, allowpc = false)
    public String Door(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            JsonResult result = visitor.CreateVisitorQRCode(request);
            if (result.isSuccess()) {
                model.addAttribute("qrcode", String.format("data:image/png;base64,%s", result.getData().getString("qrcode")));
                model.addAttribute("qrcodetimeout", result.getData().getString("qrcodetimeout"));
                model.addAttribute("timestamp", System.currentTimeMillis());
                return "visitor/visitorqrcode";
            } else {
                return ViewErrorPage(request, response, model, result.getMsg());
            }
        } catch (Exception e) {
            return ViewErrorPage(request, response, model, e.getMessage());
        }
    }

    /**
     * 修改访客离开时间
     *
     * @param request
     * @param response
     * @param uid
     * @param leavetime
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/editleavetime", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult editleavetime(HttpServletRequest request, HttpServletResponse response, String uid, String leavetime) {
        try {
            return visitor.editleavetime(request, response, uid, leavetime);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 访客离场页面
     */
    @RequestMapping(value = "/visitorleavepage", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = 4, allowpc = false)
    public String visitorleavepage(HttpServletRequest request, HttpServletResponse response, Model model) {
        return "visitor/visitorleavepage";
    }

    /**
     * 访客离场
     *
     * @param request
     * @param response
     * @param subscribeid
     * @param visitorid
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/visitorleave", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult visitorleave(HttpServletRequest request, HttpServletResponse response, String subscribeid, String visitorid) {
        try {
            return visitor.visitorleave(request, response, subscribeid, visitorid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 跳转访问记录页面
     *
     * @param request
     * @param response
     * @param model
     * @return
     */
    @RequestMapping(value = "/subrecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String subrecord(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd");
            model.addAttribute("startTime", startTime);
            model.addAttribute("endTime", endTime);
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "visitor/subrecord";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    /**
     * 访客访问记录
     */
    @ResponseBody
    @RequestMapping(value = "/getMyVisitList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getMyVisitList(HttpServletRequest request, HttpServletResponse response, String uid, String starttime, String endtime, int start, int limit) {
        try {
            return visitor.getMyVisitList(request, response, uid, starttime, endtime, start, limit, false);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 访客取消预约
     *
     * @param request
     * @param response
     * @param uid
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/cancelVisitSub", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult cancelVisitSub(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return visitor.cancelVisitSub(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 访问记录详细信息
     *
     * @param request
     * @param response
     * @param model
     * @param uid
     * @return
     */
    @RequestMapping(value = "/subDetailVisit", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String subDetailVisit(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            JsonData result = visitor.getMyVisitList(request, response, uid, null, null, 0, 0, true);
            if (!result.isSuccess()) {
                return ViewErrorClosePage(request, response, model, "访客预约信息不存在");
            }
            JSONArray list = result.getData();
            if (list.size() == 0) {
                return ViewErrorClosePage(request, response, model, "访客预约信息不存在");
            }

            JSONObject item = list.getJSONObject(0);
            model.addAttribute("uid", item.getString("uid"));
            model.addAttribute("name", item.getString("name"));
            model.addAttribute("visitorid", item.getString("visitorid"));
            model.addAttribute("byvisitor", item.getString("byvisitor"));
            model.addAttribute("sex", item.getString("sex"));
            model.addAttribute("mobile", item.getString("mobile"));
            model.addAttribute("reason", item.getString("reason"));
            model.addAttribute("company", item.getString("company"));
            model.addAttribute("position", item.getString("position"));
            model.addAttribute("department", item.getString("department"));
            model.addAttribute("visittime", item.getString("visittime"));
            model.addAttribute("leavetime", item.getString("leavetime"));
            model.addAttribute("auditstatus", item.getIntValue("auditstatus"));
            model.addAttribute("byagree", item.getIntValue("byagree"));
            model.addAttribute("remark", item.getString("remark"));
            model.addAttribute("status", item.getString("status"));
            model.addAttribute("plateno", item.getString("plateno"));
            model.addAttribute("isleave", item.getIntValue("isleave"));
//            model.addAttribute("actualleavetime", item.getString("actualleavetime"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            model.addAttribute("othervisitor", item.getJSONArray("othervisitor"));

            model.addAttribute("areapositionindex", item.getString("areaposition"));
            model.addAttribute("areapositionname", item.getString("areapositionname"));

            model.addAttribute("image", item.getString("imagefile"));

            List<VisitorService.ElevatorDevicesVo> devices = visitor.getElevatorDevices(uid);
            if (!CollectionUtil.isEmpty(devices)) {
                model.addAttribute("devices", JSON.toJSONString(devices));
            }
            return "visitor/subdetailvisit";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/getVisitorFormList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getVisitorFormList(HttpServletRequest request, HttpServletResponse response, String recordId) {
        try {
            return visitor.getVisitorFormList(request, response, recordId);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/addVisitorCard", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult addVisitorCard(HttpServletRequest request, HttpServletResponse response, String cardNo, String infoId, String visitorFormId) {
        try {
            return visitor.addVisitorCard(request, response, cardNo, infoId, visitorFormId);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 打印二维码
     */
    @ResponseBody
    @RequestMapping(value = "/printQrcode", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData printQrcode(HttpServletRequest request, HttpServletResponse response, String recordid, String visitorId) {
        try {
            return visitor.printQrcode(request, response, recordid, visitorId);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 获取通行区域列表
     */
    @ResponseBody
    @RequestMapping(value = "/getAlleyAreaList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getAlleyArea(HttpServletRequest request, HttpServletResponse response, String recordid, String visitorId) {
        try {
            return visitor.getAlleyArea(request, response, recordid, visitorId);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 获取通行区域列表
     */
    @ResponseBody
    @RequestMapping(value = "/getUserAlleyAreaList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getUserAlleyArea(HttpServletRequest request, HttpServletResponse response) {
        try {
            return visitor.getUserAlleyArea(request, response);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 更新访问区域
     */
    @ResponseBody
    @RequestMapping(value = "/updateAreaList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult updateAreaList(HttpServletRequest request, HttpServletResponse response, String recordid, String position) {
        try {
            return visitor.updateAreaList(request, response, recordid, position);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 更新访问区域
     */
    @ResponseBody
    @RequestMapping(value = "/updateAreaLists", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult updateAreaLists(HttpServletRequest request, HttpServletResponse response, String recordid, String positions,String positionname) {
        try {
            return visitor.updateAreaLists(request, response, recordid, positions,positionname);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     *身份认证
     */
    @RequestMapping(value = "/identityVerification", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String identityVerification(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("visitorPrintQRCode", syscfg.get("visitorPrintQRCode"));
        return "pc/visitor/identityVerification";
    }

    /**
     *身份认证列表
     */
    @ResponseBody
    @RequestMapping(value = "/getIdentityVisitorRecordList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getIdentityVisitorRecordList(HttpServletRequest request, HttpServletResponse response, String idCard) {
        try {
            return visitor.getIdentityVisitorRecordList(request, response, idCard);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 获取审核记录列表
     */
    @RequestMapping(value = "/getApprovalRecord", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getApprovalRecord(HttpServletRequest request, String formid, int start, int limit) {
        try {
            return visitor.getApprovalRecord(request, formid, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 获取访客需要审核人员列表
     */
    @RequestMapping(value = "/getVisitorApprovalList", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getVisitorApprovalList(HttpServletRequest request, HttpServletResponse response, String uid, String starttime, String endtime, int start, int limit) {
        try {
            return visitor.getVisitorApprovalList(request, response, 3,uid, starttime, endtime, start, limit, false);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 跳转访问记录页面
     */
    @RequestMapping(value = "/subAudit", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String subAudit(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd");
            model.addAttribute("startTime", startTime);
            model.addAttribute("endTime", endTime);
            model.addAttribute("timestamp", System.currentTimeMillis());
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "visitor/subAudit";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }


    /**
     * 跳转访问记录页面
     *
     * @param request
     * @param response
     * @param model
     * @return
     */
    @RequestMapping(value = "/subAuditDetail", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String subAuditDetail(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            String docId = userredis.get(request).getDocid();
            Integer reviewed = visitor.isReviewed(uid, docId);
            model.addAttribute("auditcondition", reviewed);
            model.addAttribute("infoid", docId);
            JsonData result = visitor.getVisitorApprovalList(request, response,3, uid, 0, 0, true);
            if (!result.isSuccess()) {
                return ViewErrorClosePage(request, response, model, result.getMsg());
            }
            JSONArray list = result.getData();
            if (list.size() == 0) {
                return ViewErrorClosePage(request, response, model, "访客预约信息不存在");
            }
            List<VisitorService.ElevatorDevicesVo> devices = visitor.getElevatorDevices(uid);
            if (!CollectionUtil.isEmpty(devices)) {
                model.addAttribute("devices", JSON.toJSONString(devices));
            }
            JSONObject item = list.getJSONObject(0);
            model.addAttribute("uid", item.getString("uid"));
            model.addAttribute("name", item.getString("name"));
            model.addAttribute("visitorid", item.getString("visitorid"));
            model.addAttribute("byvisitor", item.getString("byvisitor"));
            model.addAttribute("sex", item.getString("sex"));
            model.addAttribute("mobile", item.getString("mobile"));
            model.addAttribute("reason", item.getString("reason"));
            model.addAttribute("company", item.getString("company"));
            model.addAttribute("position", item.getString("position"));
            model.addAttribute("department", item.getString("department"));
            model.addAttribute("visittime", item.getString("visittime"));
            model.addAttribute("leavetime", item.getString("leavetime"));
            model.addAttribute("auditstatus", item.getIntValue("auditstatus"));
            model.addAttribute("byagree", item.getIntValue("byagree"));
            model.addAttribute("remark", item.getString("remark"));
            model.addAttribute("status", item.getString("status"));
            model.addAttribute("plateno", item.getString("plateno"));
            model.addAttribute("isleave", item.getIntValue("isleave"));
//            model.addAttribute("actualleavetime", item.getString("actualleavetime"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            model.addAttribute("othervisitor", item.getJSONArray("othervisitor"));

            model.addAttribute("areapositionindex", item.getString("areaposition"));
            model.addAttribute("areapositionname", item.getString("areapositionname"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "visitor/subAuditDetailVisitor";
        } catch (Exception ex) {
            ex.printStackTrace();
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }


    @RequestMapping(value = "/auditApply", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonResult auditApply(HttpServletRequest request,
                                 @RequestBody AuditApplyRequest requestBody) {
        try {
            String uid = requestBody.getUid();
            Integer status = requestBody.getStatus();
            String remark = requestBody.getRemark();
            String areaPosition = requestBody.getAreaPosition();
            return visitor.auditApply(request, uid, status, remark,areaPosition,requestBody, String.valueOf(requestBody.getDay()));
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getVisitorPosition", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getVisitorPosition(HttpServletRequest request){
        try {
            return visitor.getVisitorPosition(request);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getElevatorFloors", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getElevatorFloors(HttpServletRequest request,String deviceId,String infoType){
        try {
            return visitor.getElevatorFloors(request,deviceId,infoType);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getChannelDevices", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getChannelDevices(String position) {
        try {
            return visitor.getChannelDevices(position);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }


    @RequestMapping(value = "/saveDeviceSettings", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData saveDeviceSettings(@RequestBody AuditApplyRequest requestBody) {
        try {
            return visitor.saveDeviceSettings(requestBody);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 访客离访延期
     */
    @RequestMapping(value = "/delayedDeparture", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String delayedDeparture(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            model.addAttribute("startTime", DateHelper.format(DateHelper.addDays(new Date(), -7), "yyyy-MM-dd"));
            model.addAttribute("endTime", DateHelper.format(new Date(), "yyyy-MM-dd"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "visitor/delayedDeparture";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    /**
     * 访问记录详细信息
     *
     * @param request
     * @param response
     * @param model
     * @param uid
     * @return
     */
    @RequestMapping(value = "/delayedDepartureDetail", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String delayedDepartureDetail(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            JsonData result = visitor.getMyDelayedDepartureList(request, response, uid, null, null, 0, 0, true);
            if (!result.isSuccess()) {
                return ViewErrorClosePage(request, response, model, "访客预约信息不存在");
            }
            JSONArray list = result.getData();
            if (list.size() == 0) {
                return ViewErrorClosePage(request, response, model, "访客预约信息不存在");
            }

            JSONObject item = list.getJSONObject(0);
            model.addAttribute("uid", item.getString("uid"));
            model.addAttribute("name", item.getString("name"));
            model.addAttribute("visitorid", item.getString("visitorid"));
            model.addAttribute("byvisitor", item.getString("byvisitor"));
            model.addAttribute("sex", item.getString("sex"));
            model.addAttribute("mobile", item.getString("mobile"));
            model.addAttribute("reason", item.getString("reason"));
            model.addAttribute("company", item.getString("company"));
            model.addAttribute("position", item.getString("position"));
            model.addAttribute("department", item.getString("department"));
            model.addAttribute("visittime", item.getString("visittime"));
            model.addAttribute("leavetime", item.getString("leavetime"));
            model.addAttribute("auditstatus", item.getIntValue("auditstatus"));
            model.addAttribute("byagree", item.getIntValue("byagree"));
            model.addAttribute("oldLeaveTimeRemark", item.getString("remark"));
            model.addAttribute("delayCount", item.getIntValue("delayCount"));
            model.addAttribute("status", item.getIntValue("status"));
            model.addAttribute("plateno", item.getString("plateno"));
            model.addAttribute("isleave", item.getIntValue("isleave"));
//            model.addAttribute("actualleavetime", item.getString("actualleavetime"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            model.addAttribute("othervisitor", item.getJSONArray("othervisitor"));

            model.addAttribute("areapositionindex", item.getString("areaposition"));
            model.addAttribute("areapositionname", item.getString("areapositionname"));
            return "visitor/delayedDepartureDetail";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }


    /**
     * 访客延期访问记录
     */
    @ResponseBody
    @RequestMapping(value = "/getMyDelayedDepartureList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getMyDelayedDepartureList(HttpServletRequest request, HttpServletResponse response, String uid, String starttime, String endtime, int start, int limit) {
        try {
            return visitor.getMyDelayedDepartureList(request, response, uid, starttime, endtime, start, limit, false);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 获取延期审核详细信息记录
     */
    @RequestMapping(value = "/getDelayApprovalRecord", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getDelayApprovalRecord(HttpServletRequest request, String formid, int start, int limit) {
        try {
            return visitor.getDelayApprovalRecord(request, formid, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/delayedDepartureSubmit", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonResult delayedDepartureSubmit(HttpServletRequest request, String uid, String infoId,String oldLeaveTime, String delayTime,Integer delayCount){
        try {
            return visitor.delayedDepartureSubmit(request, uid, infoId,oldLeaveTime, delayTime,delayCount);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/visitorDeparture", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public String visitorDeparture(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("timestamp", System.currentTimeMillis());
        model.addAttribute("resourceDomain", WebConfig.getResourceDomain());
        model.addAttribute("startTime", DateHelper.format(DateHelper.addDays(new Date(), -7), "yyyy-MM-dd"));
        model.addAttribute("endTime", DateHelper.format(new Date(), "yyyy-MM-dd"));
        model.addAttribute("visitorHelpOneself", syscfg.get("visitorHelpOneself"));
        return "pc/visitor/visitordeparture";
    }

    @ResponseBody
    @RequestMapping(value = "/getVisitorDepartureRecordList", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getVisitorDepartureRecordList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit, String startTime, String endTime) {
        try {
            return visitor.getVisitorDepartureRecordList(request, response, key, start, limit, startTime, endTime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/getVisitorDepartureAudit", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult getVisitorDepartureAudit(HttpServletRequest request, HttpServletResponse response, Integer status,String formId,String leaveTime,String delayTime) {
        try {
            return visitor.getVisitorDepartureAudit(request, response, status, formId, leaveTime, delayTime);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }
}