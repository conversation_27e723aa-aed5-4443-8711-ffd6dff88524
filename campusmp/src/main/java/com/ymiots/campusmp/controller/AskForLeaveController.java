package com.ymiots.campusmp.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.AskForLeaveService;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/askforleave")
public class AskForLeaveController extends BaseController {

    @Autowired
    private AskForLeaveService askForLeaveService;

    @Autowired
    UserRedis userredis;


    @RequestMapping(value = "/leaverecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String leaveRecord(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            model.addAttribute("usertype", userredis.get(request).getUsertype());
            SysUser sysUser = userredis.get(request);
            int usertype = sysUser.getUsertype();
            String userDocid = sysUser.getDocid();
            String username = sysUser.getName();
            if (usertype == 2 ) {
                JsonData people = askForLeaveService.getPeople(request, response);
                model.addAttribute("people", people.getData());
                model.addAttribute("userDocid", userDocid);
                model.addAttribute("username", username);
            }
            if ( usertype == 3) {
                JsonData people = askForLeaveService.getPeople(request, response);
                JSONArray data = people.getData();
                model.addAttribute("people", data);
//                model.addAttribute("userDocid", data.getJSONObject(0).getString("value"));
//                model.addAttribute("username", data.getJSONObject(0).getString("title"));
            }
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd 00:00");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd 23:59");
            model.addAttribute("starttime", startTime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "askforleave/leaverecord";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/leaveRecordTeach", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String leaveRecordTeach(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd 00:00");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd 23:59");
            model.addAttribute("starttime", startTime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "askforleave/leaveRecordTeach";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/getLeaveRecord", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getLeaveRecord(HttpServletRequest request, String docid, int start, int limit, String starttime, String endtime) {
        try {
            return askForLeaveService.getLeaveRecord(request, docid, start, limit, starttime, endtime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getLeaveRecordTeach", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getLeaveRecordTeach(HttpServletRequest request,String key,int start, int limit, String starttime, String endtime) {
        try {
            return askForLeaveService.getLeaveRecordTeach(request, key, start, limit, starttime, endtime);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/detailedLeaveRecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String detailedLeaveRecord(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            model.addAttribute("usertype", userredis.get(request).getUsertype());
            model.addAttribute("timestamp", System.currentTimeMillis());
            JSONArray detailedLeaveRecord = askForLeaveService.getDetailedLeaveRecord(uid, request);
            if (!detailedLeaveRecord.isEmpty()) {
                JSONObject jo = detailedLeaveRecord.getJSONObject(0);
                model.addAttribute("uid", jo.getString("uid"));
                model.addAttribute("infoid", jo.getString("infoid"));
                model.addAttribute("infoname", jo.getString("infoname"));
                model.addAttribute("starttime", jo.getString("starttime"));
                model.addAttribute("endtime", jo.getString("endtime"));
                model.addAttribute("leavetypename", jo.getString("leavetypename"));
                model.addAttribute("reason", jo.getString("reason"));
                model.addAttribute("status", jo.getIntValue("status"));
                model.addAttribute("remark", jo.getString("remark"));
                if (jo.getInteger("status") == 1) {
                    model.addAttribute("isfinish", jo.getInteger("isfinish"));
                    if (!ObjectUtils.isEmpty(jo.getDate("leavefinishtime"))) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date leavefinishtime = jo.getDate("leavefinishtime");
                        model.addAttribute("leavefinishtime", sdf.format(leavefinishtime));
                    }
                }
            }
            return "askforleave/detailedLeaveRecord";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/cancelLeave", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult cancelLeave(String uid) {
        try {
            return askForLeaveService.cancelLeave(uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/auditleavelist", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditleavelist(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd");
            model.addAttribute("starttime", startTime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "askforleave/auditleavelist";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/auditleavelistParents", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditleavelistParents(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
            String startTime = DateHelper.format(DateHelper.addDays(new Date(), -3), "yyyy-MM-dd");
            String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd");
            model.addAttribute("starttime", startTime.replace(" ", "T"));
            model.addAttribute("endtime", endTime.replace(" ", "T"));
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "askforleave/auditleavelistParents";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/getAuditLeaveList", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getAuditLeaveList(HttpServletRequest request, HttpServletResponse response, String leavetype, String key, String status, String endtime, String starttime, int start, int limit) {
        try {
            return askForLeaveService.getAuditLeaveList(request, response, leavetype, key, status, endtime, starttime, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getAuditLeaveList1", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getAuditLeaveList1(HttpServletRequest request, HttpServletResponse response, String leavetype, String key, String status, String endtime, String starttime, int start, int limit) {
        try {
            return askForLeaveService.getAuditLeaveList1(request, response, leavetype, key, status, endtime, starttime, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/auditleave", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditleave(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            int a = 0;
            String userId = userredis.getUserId(request);
            String sql = "select docid from tb_weixin_user where uid  = '" + userId + "'";
            JSONObject jsonObjects = askForLeaveService.getWeiXinDocid(sql);
            JSONArray detailedLeaveRecord = askForLeaveService.getDetailedLeaveRecord(uid, request);
            if (!detailedLeaveRecord.isEmpty()) {
                JSONObject jo = detailedLeaveRecord.getJSONObject(0);
                for (Object o : detailedLeaveRecord) {
                    JSONObject jsonObject = (JSONObject) o;
                    if (jsonObject.getInteger("auditcondition") == 1) {
                        if (jsonObject.getString("auditid").equals(jsonObjects.getString("docid")) || jsonObject.getString("modifyid").equals(jsonObjects.getString("docid"))) {
                            a = 1;
                        }
                    }
                }
                model.addAttribute("uid", jo.getString("uid"));
                model.addAttribute("infoid", jo.getString("infoid"));
                model.addAttribute("infoname", jo.getString("infoname"));
                model.addAttribute("starttime", jo.getString("starttime"));
                model.addAttribute("endtime", jo.getString("endtime"));
                model.addAttribute("leavetypename", jo.getString("leavetypename"));
                model.addAttribute("reason", jo.getString("reason"));
                model.addAttribute("status", jo.getIntValue("status"));
                model.addAttribute("auditcondition", a);
                model.addAttribute("leavefinishtime", jo.getString("leavefinishtime"));
                model.addAttribute("isfinish", jo.getIntValue("isfinish"));
                model.addAttribute("warremark", jo.getString("warremark"));
                model.addAttribute("remark", jo.getString("remark"));
            }
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "askforleave/auditleave";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @RequestMapping(value = "/auditLeave2", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String auditLeave2(HttpServletRequest request, HttpServletResponse response, Model model, String uid) {
        try {
            String userId = userredis.get(request).getDocid();
            List<AskForLeaveService.WeiXinUser> leave2Record = askForLeaveService.getDetailedLeave2Record(uid, userId);
            AskForLeaveService.WeiXinUser weiXinUser = leave2Record.get(0);
            model.addAttribute("uid", weiXinUser.getUid());
            model.addAttribute("infoid", weiXinUser.getInfoId());
            model.addAttribute("infoname", weiXinUser.getName());
            model.addAttribute("starttime", weiXinUser.getStartTime());
            model.addAttribute("endtime", weiXinUser.getEndTime());
            model.addAttribute("leavetypename", "");
            model.addAttribute("reason", weiXinUser.getReason());
            model.addAttribute("status", weiXinUser.getStatus());
            model.addAttribute("auditcondition", 0);
            model.addAttribute("warremark", "");
            model.addAttribute("remark", "");
            model.addAttribute("timestamp", System.currentTimeMillis());
            return "askforleave/auditleave2";
        } catch (Exception ex) {
            return ViewErrorClosePage(request, response, model, ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/auditLeave", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult auditLeave(HttpServletRequest request, String uid, String infoid, Integer status, String remark,String day) {
        try {
            return askForLeaveService.auditLeave(request, uid, infoid, status, remark,day);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/leaveFinish", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult leaveFinish(HttpServletRequest request, String uid, String infoid, String status, String remark) {
        try {
            return askForLeaveService.leaveFinish(request, uid, infoid, status, remark);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/auditLeave3", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonResult auditLeave2(HttpServletRequest request, String uid, String infoid, String status, String remark) {
        try {
            return askForLeaveService.auditLeave2(request, uid, infoid, status, remark);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/leaveform", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String LeaveForm(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("usertype", userredis.get(request).getUsertype());
        model.addAttribute("timestamp", System.currentTimeMillis());
        String startTime = DateHelper.format(new Date(), "yyyy-MM-dd 00:00");
        String endTime = DateHelper.format(DateHelper.addDays(new Date(), 3), "yyyy-MM-dd 23:59");
        model.addAttribute("starttime", startTime.replace(" ", "T"));
        model.addAttribute("endtime", endTime.replace(" ", "T"));
        SysUser sysUser = userredis.get(request);
        int usertype = sysUser.getUsertype();
        String userDocid = sysUser.getDocid();
        String username = sysUser.getName();
        if (usertype == 2) {
            JsonData people = askForLeaveService.getPeople(request, response);
            model.addAttribute("people", people.getData());
            model.addAttribute("userDocid", userDocid);
            model.addAttribute("username", username);
        }
        if (usertype == 3) {
            JsonData people = askForLeaveService.getPeople(request, response);
            JSONArray data = people.getData();
            model.addAttribute("people", data);
//            model.addAttribute("userDocid", data.getJSONObject(0).getString("value"));
//            model.addAttribute("username", data.getJSONObject(0).getString("title"));
        }
        return "askforleave/leaveform";
    }

    @ResponseBody
    @RequestMapping(value = "/saveleaveform", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1)
    public JsonResult saveleaveform(HttpServletRequest request, HttpServletResponse response, String docid, String starttime, String endtime, String reason, String leaveschool) {

        try {
            return askForLeaveService.saveleaveform(request, response, docid, starttime, endtime, reason,leaveschool);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false, e.getMessage());
        }
    }


    @RequestMapping(value = "/approvalRecord", method = RequestMethod.GET)
    @Permission(authorization = true, usertype = -1)
    public String getApprovalRecord(HttpServletRequest request, HttpServletResponse response, Model model, String formid) {
        model.addAttribute("usertype", userredis.get(request).getUsertype());
        return "askforleave/approvalrecord";
    }


    @RequestMapping(value = "/getApprovalRecord", method = RequestMethod.POST)
    @ResponseBody
    @Permission(authorization = true, usertype = -1)
    public JsonData getApprovalRecord(HttpServletRequest request, String formid, int start, int limit) {
        try {
            return askForLeaveService.getApprovalRecord(request, formid, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

}
