package com.ymiots.campusmp.controller.workflow;

import com.ymiots.campusmp.entity.ApprovalContext;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:09:49
 */
@Component
public interface AuditHandler {

    String getHandlerName();

    /**
     * 审核通过
     * @param uid 表单id
     * @param name 提交者姓名
     * @param dtoList 审核人集合
     * @param areaPosition 区域
     */
    void handleSuccess(String uid,String name,List<WorkFlowNodeDto> dtoList,String areaPosition,String remark);


    void handleSuccess(ApprovalContext ctx);
    /**
     * 审核拒绝
     * @param uid 表单id
     * @param name 提交者姓名
     */
    void handleFailure(String uid,String name,String remark);

    void handleFailure(ApprovalContext ctx);
}
