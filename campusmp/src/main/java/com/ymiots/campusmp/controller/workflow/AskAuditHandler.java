package com.ymiots.campusmp.controller.workflow;

import cn.hutool.core.collection.CollectionUtil;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.entity.ApprovalContext;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.redis.UserRedis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:10:26
 */
@Service
public class AskAuditHandler implements AuditHandler{

    @Autowired
    PushMessageHandler pushMessageHandler;

    @Autowired
    DBService dbService;

    @Autowired
    UserRedis userRedis;

    @Override
    public String getHandlerName() {
        return "tb_card_ask_for_leave";
    }


    @Override
    public void handleSuccess(String uid, String name, List<WorkFlowNodeDto> dtoList,String areaPosition,String remark) {
        CompletableFuture.runAsync(() -> {
            if (CollectionUtil.isEmpty(dtoList)) {
                pushMessageHandler.pushAskResultApply(uid, name,"通过");
            } else {
                pushMessageHandler.pushNextAskApply(uid, dtoList);
            }
        });
    }

    @Override
    public void handleSuccess(ApprovalContext ctx) {
        CompletableFuture.runAsync(() -> {
            if (CollectionUtil.isEmpty(ctx.getDtoList())) {
                pushMessageHandler.pushAskResultApply(ctx.getUid(), ctx.getName(),"通过");
            } else {
                pushMessageHandler.pushNextAskApply(ctx.getUid(), ctx.getDtoList());
            }
        });
    }


    @Override
    public void handleFailure(String uid,String name,String remark) {
        CompletableFuture.runAsync(() -> {
            pushMessageHandler.pushAskResultApply(uid, name,"拒绝");
        });
    }

    @Override
    public void handleFailure(ApprovalContext ctx) {
        CompletableFuture.runAsync(() -> {
            pushMessageHandler.pushAskResultApply(ctx.getUid(), ctx.getName(), "拒绝");
        });
    }


}
