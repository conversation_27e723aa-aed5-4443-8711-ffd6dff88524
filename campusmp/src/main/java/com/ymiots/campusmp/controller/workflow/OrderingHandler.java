package com.ymiots.campusmp.controller.workflow;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.entity.ApprovalContext;
import com.ymiots.campusmp.entity.WeiXinUser;
import com.ymiots.campusmp.entity.consume.ConsumeApplyRecord;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.redis.RedisMessageEntity;
import com.ymiots.campusmp.redis.RedisPublish;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessageData;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Component
public class OrderingHandler implements AuditHandler {

    @Autowired
    private DBService dbService;

    @Autowired
    private RedisPublish redisPublish;


    private final Logger logger = LoggerFactory.getLogger(OrderingHandler.class);

    @Override
    public String getHandlerName() {
        return "tb_consume_apply_record";
    }

    @Override
    public void handleSuccess(String uid, String name, List<WorkFlowNodeDto> dtoList, String areaPosition, String remark) {
        CompletableFuture.runAsync(() -> {
            if (CollectionUtil.isEmpty(dtoList)) {
                pushAskResultApply(uid, name, "已通过");
            } else {
                pushNextAskApply(uid, dtoList);
            }
        });
    }

    @Override
    public void handleSuccess(ApprovalContext ctx) {
        CompletableFuture.runAsync(() -> {
            if (CollectionUtil.isEmpty(ctx.getDtoList())) {
                pushAskResultApply(ctx.getUid(), ctx.getName(), "已通过");
            } else {
                pushNextAskApply(ctx.getUid(), ctx.getDtoList());
            }
        });
    }

    @Override
    public void handleFailure(String uid, String name, String remark) {
        CompletableFuture.runAsync(() -> {
            pushAskResultApply(uid, name, "未通过");
        });
    }

    @Override
    public void handleFailure(ApprovalContext ctx) {
        CompletableFuture.runAsync(() -> {
            pushAskResultApply(ctx.getUid(), ctx.getName(), "未通过");
        });
    }

    public void pushAskResultApply(String uid, String name, String result) {
        String infoSQL = "SELECT date_format(create_dt, '%Y-%m-%d') createDt,uid,qr_code qrCode,info_id infoId, breakfast_start_time breakfastStartTime, breakfast_end_time breakfastEndTime, lunch_start_time lunchStartTime, lunch_end_time lunchEndTime, status " +
                "FROM tb_consume_apply_record " +
                " where uid = '" + uid + "'";
        ConsumeApplyRecord applyRecord = dbService.queryForOne(infoSQL, ConsumeApplyRecord.class);
        int count = dbService.getCount("tb_face_authorize_group_access", "infoid = '" + applyRecord.getInfoId() + "'");
        String querySql = " select name as groupId,attr1 as devId from tb_sys_dictionary where groupcode='SYS0000071'";
        List<DeviceGroupsDto> groupsDtos = dbService.queryList(querySql, DeviceGroupsDto.class);
        if (!CollectionUtil.isEmpty(groupsDtos)) {
            if (count == 0) {
                String saveSql = "insert into tb_face_authorize_group_access(uid,groupid,infoid,devid,downnum,downstatus,createdate,creatorid,status) values(uuid(),?,?,?,0,0,now(),'dev',0)";
                for (DeviceGroupsDto dto : groupsDtos) {
                    // 假设 dto 对象中包含了 `groupid`, `infoid`, `devid`字段
                    dbService.excuteSql(saveSql, dto.getGroupId(), applyRecord.getInfoId(), dto.getDevId());
                }
            }
        }
        String sql = "select openid,uid as uid from tb_weixin_user where docid = '" + applyRecord.getInfoId() + "'";
        List<WeiXinUser> weiXinUsers = dbService.queryList(sql, WeiXinUser.class);
        if (!CollectionUtil.isEmpty(weiXinUsers)) {
            for (WeiXinUser weiXinUser : weiXinUsers) {
                String openid = weiXinUser.getOpenid();
                String wxuserid = weiXinUser.getUid();
                String topic = "/campus/weixintemplatemsg";
                RedisMessageEntity message = new RedisMessageEntity();
                JSONObject data = new JSONObject();
                data.put("msgid", UUID.randomUUID().toString());
                data.put("openid", openid);
                data.put("wxuserid", wxuserid);
                data.put("cfgcode", "OPENTM2024080302");
                data.put("url", String.format("%s/ordering/generateQRCodeImage?time=%s", WebConfig.getDomain(), System.currentTimeMillis()));
                JSONObject wxdata = new JSONObject();
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                wxdata.put("thing6", new TemplateMessageData(name));
                wxdata.put("time2", new TemplateMessageData(formatter.format(applyRecord.getCreateDt())));
                wxdata.put("const3", new TemplateMessageData(result));
                data.put("wxtempdata", wxdata);
                message.setCmd("weixintemplatemsg");
                message.setData(data);
                message.setDevtype(0);
                redisPublish.Publish(topic, message);
            }
        } else {
            logger.info("{}", "无微信推送人员");
        }
    }

    @Data
    public static class DeviceGroupsDto {
        private String groupId;
        private String devId;
    }

    public void pushNextAskApply(String uid, List<WorkFlowNodeDto> dtoList) {
        String infoSQL = "select ct.name,getorgname(dining_dep) orgName,date_format(afl.create_dt, '%Y-%m-%d %H:%i') as createDt,afl.info_id,ct.mobile,afl.reason from tb_consume_apply_record afl " +
                " left join tb_card_teachstudinfo ct on ct.uid = afl.info_id where afl.uid = '" + uid + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(infoSQL);
        String name = jsonObject.getString("name");
        String mobile = jsonObject.getString("mobile");
        String orgName = jsonObject.getString("orgName");
        String createDt = jsonObject.getString("createDt");
        for (WorkFlowNodeDto nodeDto : dtoList) {
            String sql = "select openid,uid as uid from tb_weixin_user where docid = '" + nodeDto.getInfoId() + "'";
            List<WeiXinUser> weiXinUsers = dbService.queryList(sql, WeiXinUser.class);
            if (!CollectionUtil.isEmpty(weiXinUsers)) {
                for (WeiXinUser weiXinUser : weiXinUsers) {
                    String openid = weiXinUser.getOpenid();
                    String wxuserid = weiXinUser.getUid();
                    String topic = "/campus/weixintemplatemsg";
                    RedisMessageEntity message = new RedisMessageEntity();
                    JSONObject data = new JSONObject();
                    data.put("msgid", UUID.randomUUID().toString());
                    data.put("openid", openid);
                    data.put("wxuserid", wxuserid);
                    data.put("cfgcode", "OPENTM2024080301");
                    data.put("url", String.format("%s/ordering/orderAudit_detail?uid=%s&time=%s", WebConfig.getDomain(), uid, System.currentTimeMillis()));
                    JSONObject wxdata = new JSONObject();
                    wxdata.put("thing2", new TemplateMessageData(name).toJSONString());
                    wxdata.put("phone_number3", new TemplateMessageData(mobile).toJSONString());
                    wxdata.put("time4", new TemplateMessageData(createDt).toJSONString());
                    wxdata.put("thing1", new TemplateMessageData(orgName).toJSONString());
                    data.put("wxtempdata", wxdata);
                    message.setCmd("weixintemplatemsg");
                    message.setData(data);
                    message.setDevtype(0);
                    redisPublish.Publish(topic, message);
                }
            } else {
                logger.info("{}", "无微信推送人员");
            }
        }
    }

}
