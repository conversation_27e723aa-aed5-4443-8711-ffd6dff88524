package com.ymiots.campusmp.controller.workflow;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.annotation.CarReqField;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.controller.workflow.dto.DownLoadVisitorDTO;
import com.ymiots.campusmp.controller.workflow.dto.VisitorSubscribeDTO;
import com.ymiots.campusmp.controller.workflow.dto.pushHandlerInfoDTO;
import com.ymiots.campusmp.controller.workflow.entity.VisitorSubscribe;
import com.ymiots.campusmp.entity.ApprovalContext;
import com.ymiots.campusmp.entity.card.CarRequest;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.entity.dto.visitor.AuditApplyRequest;
import com.ymiots.campusmp.entity.dto.visitor.VisitorRecordDto;
import com.ymiots.campusmp.redis.RedisMessageEntity;
import com.ymiots.campusmp.redis.RedisPublish;
import com.ymiots.campusmp.service.SysConfigService;
import com.ymiots.campusmp.utils.OkHttpUtil;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessageData;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.entity.AliyunSmsEntity;
import com.ymiots.framework.service.AliyunSendSmsService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2024-01-08 13:48:47
 */
@Service
public class VisitorHandler implements AuditHandler {

    @Autowired
    private SysConfigService sysCfg;

    @Autowired
    AliyunSendSmsService smg;

    @Autowired
    DBService dbService;

    @Autowired
    RedisPublish redisPublish;

    @Autowired
    SysConfigService sysConfigService;

    /**
     * 新增车辆
     */
    private static final String ADD_CAR_URL = "http://%s/Cloud";


    @Autowired
    private OkHttpUtil okHttpUtil;

    @Override
    public String getHandlerName() {
        return "tb_visitor_subscribe";
    }

    @Override
    public void handleSuccess(String uid, String name, List<WorkFlowNodeDto> dtoList, String areaPosition,String remark) {
        if (CollectionUtil.isEmpty(dtoList)) {
            //下发访客名单
            downLoadVisitorCard(uid, areaPosition);
            // 如果车牌号码不为空，则推送访客信息至停车场系统
            pushParking(uid);
            CompletableFuture.runAsync(() -> {
                String sql = "select visitorid as visitorId,byvisitor byVisitor,visittime visitTime,leavetime leaveTime from tb_visitor_subscribe where uid='" + uid + "'";
                VisitorSubscribe visitorSubscribe = dbService.queryForOne(sql, VisitorSubscribe.class);
                //成功推送消息
                pushSubSuccessToVisitor(uid,visitorSubscribe.getVisitorId(), visitorSubscribe.getByVisitor(), DateHelper.format(visitorSubscribe.getVisitTime(),"yyyy-MM-dd HH:mm:ss"), DateHelper.format(visitorSubscribe.getLeaveTime(),"yyyy-MM-dd HH:mm:ss"));
            });
        } else {
            CompletableFuture.runAsync(() -> {
                pushEmailAndSMSForManager(uid,name, dtoList);
            });
        }
    }

    @Override
    public void handleSuccess(ApprovalContext ctx) {
        if (CollectionUtil.isEmpty(ctx.getDtoList())) {
            //下发访客名单
//            downLoadVisitorCard(ctx.getUid(), ctx.getAreaPosition());
            downLoadVisitorCard(ctx.getUid(), ctx.getRequestBody());
            // 如果车牌号码不为空，则推送访客信息至停车场系统
            pushParking(ctx.getUid());
            CompletableFuture.runAsync(() -> {
                String sql = "select visitorid as visitorId,byvisitor byVisitor,visittime visitTime,leavetime leaveTime from tb_visitor_subscribe where uid='" + ctx.getUid() + "'";
                VisitorSubscribe visitorSubscribe = dbService.queryForOne(sql, VisitorSubscribe.class);
                //成功推送消息
                pushSubSuccessToVisitor(ctx.getUid(),visitorSubscribe.getVisitorId(), visitorSubscribe.getByVisitor(), DateHelper.format(visitorSubscribe.getVisitTime(),"yyyy-MM-dd HH:mm:ss"), DateHelper.format(visitorSubscribe.getLeaveTime(),"yyyy-MM-dd HH:mm:ss"));
            });
        } else {
            CompletableFuture.runAsync(() -> {
                pushEmailAndSMSForManager(ctx.getUid(), ctx.getName(), ctx.getDtoList());
            });
        }
    }

    @Override
    public void handleFailure(String uid, String name,String remark) {
        CompletableFuture.runAsync(() -> {
            String sql = "select visitorid as visitorId,byvisitor byVisitor,visittime visitTime,leavetime leaveTime from tb_visitor_subscribe where visitorid='" + uid + "'";
            VisitorSubscribe visitorSubscribe = dbService.queryForOne(sql, VisitorSubscribe.class);
            //推送
            pushSubFailToVisitor(uid, "",visitorSubscribe.getVisitorId(), visitorSubscribe.getByVisitor(), DateHelper.format(visitorSubscribe.getVisitTime(),"yyyy-MM-dd HH:mm:ss"), DateHelper.format(visitorSubscribe.getLeaveTime(),"yyyy-MM-dd HH:mm:ss"));
        });
    }

    @Override
    public void handleFailure(ApprovalContext ctx) {
        CompletableFuture.runAsync(() -> {
            String sql = "select visitorid as visitorId,byvisitor byVisitor,visittime visitTime,leavetime leaveTime from tb_visitor_subscribe where visitorid='" + ctx.getUid() + "'";
            VisitorSubscribe visitorSubscribe = dbService.queryForOne(sql, VisitorSubscribe.class);
            //推送
            pushSubFailToVisitor(ctx.getUid(), "",visitorSubscribe.getVisitorId(), visitorSubscribe.getByVisitor(), DateHelper.format(visitorSubscribe.getVisitTime(),"yyyy-MM-dd HH:mm:ss"), DateHelper.format(visitorSubscribe.getLeaveTime(),"yyyy-MM-dd HH:mm:ss"));
        });
    }

    /**
     * 下发访客名单tb_visitor_subscribe
     */
    private void downLoadVisitorCard(String subscribeId, String areaPosition) {
        String visitorCardSQL = "select visitor_subscribe_id as visitorFormId,visitor_id as visitorId from tb_visitor_subscribe_person " +
                "where visitor_subscribe_id='" + subscribeId + "'";
        List<DownLoadVisitorDTO> subscribeDTOS = dbService.queryList(visitorCardSQL, DownLoadVisitorDTO.class);
        if (CollectionUtil.isEmpty(subscribeDTOS)) {
            return;
        }
        //下发主访客名单
        String insertVisitorRecordNameList = "";
        //下发人名单
        for (DownLoadVisitorDTO subscribeDTO : subscribeDTOS) {
            if ("0".equals(sysConfigService.get("visitormachineneedauth"))) {
                insertVisitorRecordNameList = "INSERT INTO tb_visitor_record_namelist(uid,devid,visitorid,downnum,createdate,status,subscribeid) SELECT uuid(),t.devid,'"
                        + subscribeDTO.getVisitorId() + "',0,now(),0,'"+subscribeId+"' FROM (select distinct devid from tb_visitor_machine_device) t ";
            } else {
                String[] split = areaPosition.split(",");
                for (int i = 0; i < split.length; i++) {
                    insertVisitorRecordNameList = "INSERT INTO tb_visitor_record_namelist(uid,devid,visitorid,downnum,createdate,status,subscribeid) SELECT uuid(),devid,'" + subscribeDTO.getVisitorId() + "',0,now(),0,'"+subscribeId+"' from tb_visitor_machine_device where position='" + split[i] + "' ";
                }
            }
            dbService.excuteSql(insertVisitorRecordNameList);
        }
    }

    /**
     * 下发访客名单tb_visitor_subscribe
     */
    private void downLoadVisitorCard(String subscribeId, AuditApplyRequest requestBody) {
        String visitorCardSQL = "select vv.name visitorName,vs.visittime visitTime,vs.leavetime leaveTime,vs.createdate createDate,vv.uid from tb_visitor_subscribe vs join tb_visitor_visitorinfo vv on vs.visitorid = vv.uid " +
                "where vs.uid='" + subscribeId + "'";
        VisitorRecordDto visitorRecordDto = dbService.queryForOne(visitorCardSQL, VisitorRecordDto.class);

        //保存到申请门禁表中
        StringBuilder sql = new StringBuilder();

        sql.append("INSERT INTO tb_access_application(id,infoid,reason,open_time_start,open_time_end,created_at,status," +
                        "applicant_name,applicant_type,application_time,department,access_type,password,usage_count,attachment) ")
                .append("values('").append(subscribeId).append("','").append(visitorRecordDto.getUid()).append("','").append("访客申请").append("','").append(visitorRecordDto.getVisitTime())
                .append("','").append(visitorRecordDto.getLeaveTime()).append("',now(),").append("1,'")
                .append(visitorRecordDto.getVisitorName()).append("','").append(2).append("',").append("now()").append(",'").append("null")
                .append("',2,'").append(requestBody.getGlobalPassword()).append("','").append(requestBody.getGlobalUsageCount()).append("','").append("null").append("') ");
        dbService.excuteSql(sql.toString());

        String savePermissionsSql = "INSERT INTO tb_access_application_dev(apply_id,dev_id,door,floors,down_status) values (?,?,?,?,?)";
        if (!CollectionUtil.isEmpty(requestBody.getDevices())) {
            for (AuditApplyRequest.DeviceSetting device : requestBody.getDevices()) {
                dbService.excuteSql(savePermissionsSql,subscribeId,device.getDeviceId(),device.getDoorRight(),device.getFloors(),0);
            }
        }

    }

    /**
     * 推送访客信息至停车场系统
     */
    private void pushParking(String formId){
        String sql = "SELECT vv.uid,vv.name,vv.idcard as idCard,vv.sex,vv.mobile,vv.plateno as plateNo,leavetime leaveTime " +
                "FROM tb_visitor_visitorinfo as vv join tb_visitor_subscribe vs on vs.visitorid = vv.uid " +
                "where vs.uid='" + formId + "' ";
        VisitorSubscribeDTO visitorInfo = dbService.queryForOne(sql, VisitorSubscribeDTO.class);
        if (visitorInfo != null) {
            if (!StringUtils.isBlank(visitorInfo.getPlateNo())) {
                if ("1".equals(WebConfig.getServerid())) {
                    JSONObject message = new JSONObject();
                    message.put("code", visitorInfo.getPlateNo());
                    message.put("name", visitorInfo.getName());
                    message.put("idcode", visitorInfo.getIdCard());
                    message.put("phone", visitorInfo.getMobile());
                    message.put("datestart", DateHelper.format(new Date()));
                    message.put("dateend", visitorInfo.getLeaveTime());
                    redisPublish.Publish("visitor/visitorplate", message.toJSONString());
                } else {
                    addCard(visitorInfo.getPlateNo(), DateHelper.format(new Date()), DateHelper.format(visitorInfo.getLeaveTime()));
                }
            }
        }
    }
    public void addCard(String plateNo,String startTime,String endTime){
        String addCardUrl = String.format(ADD_CAR_URL,WebConfig.getIotServerAddress());
        //组装请求参数
        CarRequest carRequest = new CarRequest();
        carRequest.setVer(2);
        carRequest.setUid(UUID.randomUUID().toString());
        carRequest.setTimestamp(System.currentTimeMillis() * 1000);
        carRequest.setOp("car");
        try {
            // 签名
            // 1. 获取所有带有@carRequest注解且signed为true的字段
            TreeMap<String, String> params = new TreeMap<>();
            // 1. 获取所有带有@carRequest注解且signed为true的字段
            Field[] fields = getAllFields(carRequest.getClass());
            for (Field field : fields) {
                if (field.isAnnotationPresent(CarReqField.class)) {
                    CarReqField annotation = field.getAnnotation(CarReqField.class);
                    if (annotation.signed()) {
                        field.setAccessible(true);
                        Object value = field.get(carRequest);
                        if (!ObjectUtils.isEmpty(value)) {
                            String fieldName = annotation.value();
                            String fieldValue = value.toString();
                            params.put(fieldName, fieldValue);
                        }
                    }
                }
            }
            //进行md5加密
            String sign = unionSign(params, "2");
            carRequest.setSign(sign);
            CarRequest.DataField dataField = new CarRequest.DataField();
            dataField.setGid(UUID.randomUUID().toString());
            dataField.setLicense(plateNo);
            dataField.setStartDate(startTime);
            dataField.setEndDate(endTime);
            dataField.setChargeTypeGid("b3580bf8bf1911edb7f51c1b0dbbd56e");
            dataField.setCarTypeGid("8cb2e373567311eca1d21c1b0dbbd56e");

            carRequest.setData(dataField);
            // 将请求对象转为 JSON
            String requestBody = JSONObject.toJSONString(carRequest);
            // 发起 HTTP 请求
            JSONObject jsonObject = okHttpUtil.doPostJson(addCardUrl, requestBody);
            System.out.println("Response: " + jsonObject);
        } catch (Exception e) {
            System.err.println("HTTP Request failed: " + e.getMessage());
        }

    }


    /**
     * 将 Map 转换为 List<NameValuePair>
     */
    private List<NameValuePair> convertMapToNameValuePair(Map<String, Object> paramMap) {
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        paramMap.forEach((key, value) -> {
            if (value != null) {
                nameValuePairs.add(new org.apache.http.message.BasicNameValuePair(key, value.toString()));
            }
        });
        return nameValuePairs;
    }


    public String unionSign(TreeMap<String, String> params, String appkey) throws Exception {
        // TODO Auto-generated method stub

        params.remove("sign");
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (entry.getValue() != null && entry.getValue().length() > 0) {
                sb.append(entry.getKey()).append("=").append(entry.getValue())
                        .append("&");
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        String stringSignTemp = sb.toString() + "&key=" + appkey;
        System.out.println(stringSignTemp);
        return DigestUtils.md5Hex(sb.toString()).toUpperCase();// 记得是md5编码的加签
    }

    // 获取类及其父类的所有字段
    private Field[] getAllFields(Class<?> clazz) {
        if (clazz == null) {
            return new Field[0];
        }
        Field[] fields = clazz.getDeclaredFields();
        Field[] parentFields = getAllFields(clazz.getSuperclass());
        Field[] allFields = new Field[fields.length + parentFields.length];
        System.arraycopy(fields, 0, allFields, 0, fields.length);
        System.arraycopy(parentFields, 0, allFields, fields.length, parentFields.length);
        return allFields;
    }

    public static Map<String, Object> objectToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true); // 允许访问私有字段
            map.put(field.getName(), field.get(obj));
        }
        return map;
    }

    /**
     * 推送下一个节点的信息
     */
    public void pushEmailAndSMSForManager(String uid,String name,List<WorkFlowNodeDto> dtoList) {
        String isUseEmail = sysCfg.get("emailusername");
        String isUseAliSms = sysCfg.get("AliyunSmsSignName");
        for (WorkFlowNodeDto workFlowNodeDto : dtoList) {
            //获取到推送人详细信息
            List<pushHandlerInfoDTO> handlerInfo = getHandlerInfo(workFlowNodeDto.getInfoId());
            String email = handlerInfo.get(0).getEmail();
            if (StringUtils.isNotBlank(isUseEmail) && StringUtils.isNotBlank(email)) {
                new Thread(new Runnable() {
                    public void run() {
                        Log.info(this.getClass(), "推送邮件");
                        SimpleMailMessage message = new SimpleMailMessage();
                        message.setTo(email);
                        message.setSubject("您好，您有一份邀请信息待审核");
                        message.setText("您好，您有一份邀请信息待审核，请及时处理审核。");
                        message.setFrom(sysCfg.get("emailusername"));
                        JavaMailSenderImpl jms = new JavaMailSenderImpl();
                        jms.setHost(sysCfg.get("emailhost"));
                        jms.setUsername(sysCfg.get("emailusername"));
                        jms.setPassword(sysCfg.get("emailpassword"));
                        jms.send(message);
                    }
                }).start();
            }

            String smsTempSQL = "SELECT code FROM tb_sms_template WHERE name='访客预约待审核'";
            JSONArray tempja = dbService.QueryList(smsTempSQL);
            if (StringUtils.isNotBlank(isUseAliSms)) {
                Log.info(this.getClass(), "推送短信");
                String mobile = handlerInfo.get(0).getMobile();
                if (StringUtils.isNotBlank(mobile)) {
                    if (!tempja.isEmpty()) {
                        JSONObject cfg = sysCfg.get("AliyunAccessKeyId", "AliyunAccessSecret", "AliyunSmsSignName", "AliyunSmsTemplateCode");
                        AliyunSmsEntity smsentity = new AliyunSmsEntity();
                        smsentity.setAccessKeyId(cfg.getString("AliyunAccessKeyId"));
                        smsentity.setAccessSecret(cfg.getString("AliyunAccessSecret"));
                        smsentity.setPhoneNumbers(mobile);
                        smsentity.setSignName(cfg.getString("AliyunSmsSignName"));
                        String templateCode = tempja.getJSONObject(0).getString("code");
                        smsentity.setTemplateCode(templateCode);
                        smg.SenSms(smsentity);
                    }
                }
            }
            for (pushHandlerInfoDTO pushHandlerInfoDto : handlerInfo) {
                if (StringUtils.isNotBlank(pushHandlerInfoDto.getOpenid()) && StringUtils.isNotBlank(pushHandlerInfoDto.getWxUid())) {
                    //查询出访客表单信息
                    VisitorSubscribeDTO visitorFormId = getVisitorFormId(uid);
                    String visitorStartTime = DateHelper.format(visitorFormId.getVisitTime(), "yyyy-MM-dd HH:mm:ss");
                    String visitorEndTime = DateHelper.format(visitorFormId.getLeaveTime(), "yyyy-MM-dd HH:mm:ss");
                    String reason = visitorFormId.getReason();
                    String visitorName = visitorFormId.getName();
                    //推送微信
                    Log.info(this.getClass(), "推送微信");
                    //推送微信消息
                    String openid = pushHandlerInfoDto.getOpenid();
                    String wxuserid = pushHandlerInfoDto.getWxUid();
                    String topic = "/campus/weixintemplatemsg";
                    RedisMessageEntity message = new RedisMessageEntity();
                    JSONObject data = new JSONObject();
                    data.put("msgid", UUID.randomUUID().toString());
                    data.put("openid", openid);
                    data.put("wxuserid", wxuserid);
                    data.put("cfgcode", "OPENTM409889155");
                    JSONObject wxdata = new JSONObject();
                    if (WebConfig.getStartNew()) {
                        data.put("url", String.format("%s/visitor/subAuditDetail?uid=%s&time=%s", WebConfig.getDomain(), uid, System.currentTimeMillis()));
                        wxdata.put("thing1", new TemplateMessageData(name).toJSONString());
                        wxdata.put("time3", new TemplateMessageData(visitorStartTime).toJSONString());
                        wxdata.put("time4", new TemplateMessageData(visitorEndTime).toJSONString());
                        wxdata.put("thing5", new TemplateMessageData(visitorName).toJSONString());
                    } else {
                        data.put("url", String.format("%s/visitor/authorizeVisitor?uid=%s&time=%s", WebConfig.getDomain(), uid, System.currentTimeMillis()));
                        wxdata.put("first", new TemplateMessageData("您好！您有待审核访客预约信息，请审核").toJSONString());
                        wxdata.put("keyword1", new TemplateMessageData(reason).toJSONString());
                        wxdata.put("keyword2", new TemplateMessageData(visitorStartTime + "到" + visitorEndTime).toJSONString());
                        wxdata.put("keyword3", new TemplateMessageData(visitorName).toJSONString());
                        wxdata.put("remark", new TemplateMessageData("请尽快审核。").toJSONString());
                    }
                    data.put("wxtempdata", wxdata);
                    message.setCmd("weixintemplatemsg");
                    message.setData(data);
                    message.setDevtype(0);
                    redisPublish.Publish(topic, message);
                }
            }

        }
    }

    //推送预约成功给访客
    public void pushSubSuccessToVisitor(String uid,String visitorId,String byVisitorName,String visitTime,String leaveTime) {
        String sql3 = "SELECT wu.uid,wu.openid,info.name FROM tb_weixin_user wu inner join tb_visitor_visitorinfo info on info.uid=wu.docid where info.uid='" + visitorId + "' AND wu.status=1;";
        JSONArray visitorWXJa = dbService.QueryList(sql3);
        if (visitorWXJa.size() > 0) {
            String openid = visitorWXJa.getJSONObject(0).getString("openid");
            String wxuserid = visitorWXJa.getJSONObject(0).getString("uid");
            String name = visitorWXJa.getJSONObject(0).getString("name");
            //审核完成以后推送微信消息给访客
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("cfgcode", "OPENTM407908379");
            data.put("url", String.format("%s/visitor/subDetailVisit?uid=%s&time=%s", WebConfig.getDomain(), uid,System.currentTimeMillis()));
            JSONObject wxdata = new JSONObject();
            //推送微信
            Log.info(this.getClass(), "推送微信");
            if (WebConfig.isStartNew()) {
                wxdata.put("thing2", new TemplateMessageData(name).toJSONString());
                wxdata.put("thing3", new TemplateMessageData(byVisitorName).toJSONString());
                wxdata.put("time14", new TemplateMessageData(visitTime));
                wxdata.put("time15", new TemplateMessageData(leaveTime));
                wxdata.put("thing1", new TemplateMessageData("已通过"));
            }else {
                wxdata.put("first", new TemplateMessageData("您好，您已预约成功。", "#4caf50").toJSONString());
                wxdata.put("keyword1", new TemplateMessageData(name).toJSONString());
                wxdata.put("keyword2", new TemplateMessageData(byVisitorName).toJSONString());
                wxdata.put("keyword3", new TemplateMessageData(visitTime));
                wxdata.put("keyword4", new TemplateMessageData(leaveTime));
                wxdata.put("remark", new TemplateMessageData("请在预约时间内来访，点击查看访客二维码", "#ce0404").toJSONString());
            }
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redisPublish.Publish(topic, message);
        }
    }

    //推送预约失败给访客
    public void pushSubFailToVisitor(String uid, String remark, String visitorId,String byVisitorName,String visitTime,String leaveTime) {
        String sql3 = "SELECT wu.uid,wu.openid,info.name FROM tb_weixin_user wu inner join tb_visitor_visitorinfo info on info.uid=wu.docid where info.uid='" + visitorId + "' AND wu.status=1;";
        JSONArray list = dbService.QueryList(sql3);
        if (list.size() > 0) {
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            String openid = list.getJSONObject(0).getString("openid");
            String wxuserid = list.getJSONObject(0).getString("uid");
            String name = list.getJSONObject(0).getString("name");
            String topic = "/campus/weixintemplatemsg";
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("url", String.format("%s/visitor/subDetailVisit?uid=%s&time=%s", WebConfig.getDomain(), uid, System.currentTimeMillis()));
            JSONObject wxdata = new JSONObject();
            if (WebConfig.isStartNew()) {
                data.put("cfgcode", "OPENTM407908379");
                wxdata.put("thing2", new TemplateMessageData(name).toJSONString());
                wxdata.put("thing3", new TemplateMessageData(byVisitorName).toJSONString());
                wxdata.put("time14", new TemplateMessageData(visitTime));
                wxdata.put("time15", new TemplateMessageData(leaveTime));
                wxdata.put("thing1", new TemplateMessageData("未通过"));
            } else {
                data.put("cfgcode", "OPENTM403107564");
                wxdata.put("first", new TemplateMessageData(String.format("%s，您提交的预约来访信息已审核完毕！", name)).toJSONString());
                wxdata.put("keyword1", new TemplateMessageData("未通过", "#ca4440").toJSONString());
                wxdata.put("keyword2", new TemplateMessageData(remark).toJSONString());
                wxdata.put("keyword3", new TemplateMessageData(DateHelper.format(new Date())).toJSONString());
                wxdata.put("remark", new TemplateMessageData("谢绝来访，感谢您的使用!").toJSONString());
            }
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redisPublish.Publish(topic, message);
        }
    }

    /**
     * 获取访客表单信息
     * @param uid
     * @return
     */
    private VisitorSubscribeDTO getVisitorFormId(String uid) {
        String sql = "SELECT vs.uid, vs.visitorid as visitorId, vs.byvisitor as byVisitor, vs.byvisitorid byVisitorId, vs.plateno plateNo," +
                " vs.personnum personNum, vs.reason, vs.things, vs.visittime visitTime, vs.leavetime leaveTime, vs.areaposition areaPosition," +
                " vs.isdefault as isDefault, vs.creatorid creatorId, vs.createdate createDate, vs.status, ct.name AS name " +
                "FROM tb_visitor_subscribe vs " +
                "join tb_card_teachstudinfo ct on vs.byvisitorid = ct.uid " +
                "WHERE vs.uid='" + uid + "'";
        VisitorSubscribeDTO visitorSubscribe = dbService.queryForOne(sql, VisitorSubscribeDTO.class);
        return visitorSubscribe;
    }

    private List<pushHandlerInfoDTO> getHandlerInfo(String infoId) {
        String sql = "SELECT ct.name,ct.code,ct.mobile,ct.email,ct.linkmobile1,ct.linkmobile1,wu.openid,wu.uid as wxUid FROM tb_card_teachstudinfo ct " +
                " left join tb_weixin_user wu on ct.uid = wu.docid " +
                " WHERE ct.uid='" + infoId + "'";
        List<pushHandlerInfoDTO> handlerInfoDtos = dbService.queryList(sql, pushHandlerInfoDTO.class);
        return handlerInfoDtos;

    }
}
