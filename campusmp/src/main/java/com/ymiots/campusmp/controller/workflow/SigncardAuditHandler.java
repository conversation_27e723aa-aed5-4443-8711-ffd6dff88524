package com.ymiots.campusmp.controller.workflow;

import cn.hutool.core.collection.CollectionUtil;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.redis.UserRedis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:10:26
 */
@Service
public class SigncardAuditHandler implements AuditHandler{

    @Autowired
    PushMessageHandler pushMessageHandler;

    @Autowired
    DBService dbService;

    @Autowired
    UserRedis userRedis;

    @Override
    public String getHandlerName() {
        return "tb_time_signcard";
    }


    @Override
    public void handleSuccess(String uid, String name, List<WorkFlowNodeDto> dtoList,String areaPosition,String remark) {
        CompletableFuture.runAsync(() -> {
            if (CollectionUtil.isEmpty(dtoList)) {
                pushMessageHandler.pushSigncardResultApply(uid, name,"通过");
            } else {
                pushMessageHandler.pushNextTimeSigncardApply(uid, dtoList);
            }
        });
    }

    @Override
    public void handleSuccess(String uid, List<WorkFlowNodeDto> dtoList) {

    }


    @Override
    public void handleFailure(String uid,String name,String remark) {
        CompletableFuture.runAsync(() -> {
            pushMessageHandler.pushSigncardResultApply(uid, name,"拒绝");
        });
    }
}
