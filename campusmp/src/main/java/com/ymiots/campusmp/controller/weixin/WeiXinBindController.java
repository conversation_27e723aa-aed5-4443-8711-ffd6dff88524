package com.ymiots.campusmp.controller.weixin;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.config.redis.RedisClient;
import com.ymiots.campusmp.controller.BaseController;
import com.ymiots.campusmp.service.AliyunSmsService;
import com.ymiots.campusmp.service.OAuthService;
import com.ymiots.campusmp.utils.weixin.WeiXinOAuth;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.MD5;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/weixin")
public class WeiXinBindController extends BaseController {

	@Autowired
	WeiXinOAuth weixinoauth;

	@Autowired
	RedisClient redisclient;

	@Autowired
	OAuthService oauth;

	@Autowired
	AliyunSmsService ymaliyunsmsservice;

	/**
	 * 人员信息页面绑定人员（学生、家长、教职工、访客）
	 * @return
	 */
	@RequestMapping(value = "/weixinbind", method = RequestMethod.POST)
	public JsonResult WeiXinBind(HttpServletRequest request, HttpServletResponse response, String openid, int usertype, String name, String vcode,
							   String infocode, String mobile, String linkname, String linktel, String code, String pwd, String headimgurl, String nickname, String sex) {
		try {
			return oauth.WeiXinBind(request, response, openid, usertype, name, vcode, infocode, mobile, linkname, linktel, code, pwd,headimgurl, nickname, sex);
		} catch (Exception ex) {
			return Json.getJsonResult(false,ex.getMessage());
		}
	}

	/**
	 * 注册加图片二维码验证
	 * @return
	 */
	@RequestMapping(value = "/weChatLogin", method = RequestMethod.POST)
	public JsonResult weChatLogin(HttpServletRequest request, HttpServletResponse response, String openid, int usertype, String name, String vcode,
								 String infocode, String mobile, String linkname, String linktel, String code, String pwd, String headimgurl, String nickname, String sex) {
		try {
			return oauth.weChatLogin(request, response, openid, usertype, name, vcode, infocode, mobile, linkname, linktel, code, pwd,headimgurl, nickname, sex);
		} catch (Exception ex) {
			ex.printStackTrace();
			return Json.getJsonResult(false,ex.getMessage());
		}
	}

	/**
	 *学生信息页面绑定学生（身份证+姓名）信息
	 * @return
	 */
	@RequestMapping(value = "/weixinbind1", method = RequestMethod.POST)
	public JsonResult WeiXinBind1(HttpServletRequest request, HttpServletResponse response, String openid, int usertype, String name,String vcode,
			String idcard, String mobile, String linkname, String linktel, String code, String pwd, String headimgurl, String nickname, String sex) {
		try {
			return oauth.WeiXinBind1(request, response, openid, usertype, name, vcode, idcard, mobile, linkname, linktel, code, pwd,headimgurl, nickname, sex);
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}

	/**
	 *学生信息页面绑定学生（手机号+姓名）信息
	 * @return
	 */
	@RequestMapping(value = "/weixinbind2", method = RequestMethod.POST)
	public JsonResult WeiXinBind2(HttpServletRequest request, HttpServletResponse response, String openid, int usertype, String name,String vcode,
			String idcard, String mobile, String linkname, String linktel, String code, String pwd, String headimgurl, String nickname, String sex) {
		try {
			return oauth.WeiXinBind2(request, response, openid, usertype, name, vcode, idcard, mobile, linkname, linktel, code, pwd,headimgurl, nickname, sex);
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}

	/**
	 * 教职工信息页面绑定教职工（姓名+工号）信息
	 * @return
	 */
	@RequestMapping(value = "/weixinbind3", method = RequestMethod.POST)
	public JsonResult WeiXinBind3(HttpServletRequest request, HttpServletResponse response, String openid, int usertype, String name,String vcode,
			String idcard, String mobile, String linkname, String linktel, String code, String pwd, String headimgurl, String nickname, String sex) {
		try {
			return oauth.WeiXinBind3(request, response, openid, usertype, name, vcode, idcard, mobile, linkname, linktel, code, pwd,headimgurl, nickname, sex);
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}

	/**
	 *家长绑定学生信息，验证学生信息
	 */
	@RequestMapping(value = "/WeiXinParentsBindIsOk", method = RequestMethod.POST)
	public JsonResult WeiXinParentsBindIsOk(HttpServletRequest request, HttpServletResponse response, String openid, int usertype, String name, String vcode,
								 String infocode, String mobile, String linkname, String linktel, String code, String pwd, String headimgurl, String nickname, String sex,
											String pName,String pMobile,String pSex,String relation,String newPwd) {
		try {
			return oauth.WeiXinParentsBindIsOk(request, response, openid, usertype, name, vcode, infocode, mobile, linkname, linktel, code, pwd,headimgurl, nickname, sex,pName,pMobile,pSex,relation,newPwd);
		} catch (Exception ex) {
			return Json.getJsonResult(false,ex.getMessage());
		}
	}
    @RequestMapping(value = "/weixinvisitorbind", method = RequestMethod.POST)
    public JsonResult WeiXinVisitorBind(HttpServletRequest request, HttpServletResponse response, String name, String mobile,
                String idtype, String idcard, String company, String position, String department, String openid, int usertype,
                String headimgurl, String nickname, String sex, String visitorsex, String plateno) {
        try {
            return oauth.WeiXinVisitorBind(request, response, name, mobile, idtype, idcard, company, position, department, openid, usertype, headimgurl, nickname, sex, visitorsex,plateno);
        } catch (Exception ex) {
            return Json.getJsonResult(ex.getMessage());
        }
    }

	@RequestMapping(value = "/addweixinbind", method = RequestMethod.POST)
	public JsonResult AddWeiXinBind(HttpServletRequest request, HttpServletResponse response, String code, String name,String passWord) {
		try {
			return oauth.AddWeiXinBind(request, response, code, name, passWord);
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}

	@RequestMapping(value = "/weixindisbind", method = RequestMethod.POST)
	public JsonResult WeiXinDisBind(HttpServletRequest request, HttpServletResponse response, String uid) {
		try {
			JsonResult result=oauth.WeiXinDisBind(request, response, uid);
			if(WebConfig.EnableCampusCPService()) {
				String timestamp=String.valueOf(System.currentTimeMillis());
				String temp="appid=%s&nodeid=%s&timestamp=%s&weixinid=%s&key=%s";
				String signature=String.format(temp, WebConfig.getCampusCPAppId(),WebConfig.getNodeid(),timestamp,uid,WebConfig.getCampusCPAppSecret());
				signature=MD5.MD5Encode(signature, "utf-8");
				String disbindurltemp="%s/weixin/disbind?appid=%s&nodeid=%s&timestamp=%s&weixinid=%s&signature=%s";
				String cpdisbindurl=String.format(disbindurltemp, WebConfig.getCampusCPDomain(),WebConfig.getCampusCPAppId(),WebConfig.getNodeid(),timestamp,uid,signature);
				result.setMsg(cpdisbindurl);
			}
			return result;
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}

	/**
	 * 发送验证码
	 * @param request
	 * @param response
	 * @param mobile
	 * @return
	 */
    @ResponseBody
    @RequestMapping(value = "/sendmobilecode", method = RequestMethod.POST)
    public JsonResult sendmobilecode(HttpServletRequest request, HttpServletResponse response, String mobile) {
        JSONObject jsonobj = ymaliyunsmsservice.SendSmsMessage(mobile);
        if (!jsonobj.isEmpty()) {
            //手机验证码暂存redis
            redisclient.setString("vc" + mobile, jsonobj.get("code").toString(), 10, TimeUnit.MINUTES);
        } else {
            return Json.getJsonResult(false,"请勿频繁发送");
        }
        return Json.getJsonResult(true);
    }
}
