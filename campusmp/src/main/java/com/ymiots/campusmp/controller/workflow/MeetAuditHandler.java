package com.ymiots.campusmp.controller.workflow;

import com.ymiots.campusmp.entity.ApprovalContext;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:16:23
 */
@Service
public class MeetAuditHandler implements AuditHandler{
    @Override
    public String getHandlerName() {
        return "tb_meeting_book_record";
    }

    @Override
    public void handleSuccess(String uid, String name, List<WorkFlowNodeDto> dtoList,String areaPosition,String remark) {

    }

    @Override
    public void handleSuccess(ApprovalContext ctx) {

    }

    @Override
    public void handleFailure(String uid,String name,String remark) {

    }

    @Override
    public void handleFailure(ApprovalContext ctx) {

    }
}
