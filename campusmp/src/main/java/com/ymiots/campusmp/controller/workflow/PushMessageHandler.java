package com.ymiots.campusmp.controller.workflow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.entity.WeiXinUser;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.redis.RedisMessageEntity;
import com.ymiots.campusmp.redis.RedisPublish;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessageData;
import com.ymiots.framework.common.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/11/20 14:45:48
 */
@Service
public class PushMessageHandler {

    @Autowired
    DBService dbService;

    @Autowired
    RedisPublish redispublish;


    @Async
    public void pushLabApplyAsync(String uid, List<WorkFlowNodeDto> dtoList) throws ParseException {
        // 异步执行的逻辑
        pushLabApply(uid, dtoList);
    }

    @Async
    public void pushLabSuccessApplyAsync(String uid) throws ParseException {
        // 异步执行的逻辑
        pushLabSuccessApply(uid);
    }

    @Async
    public void pushLabFailApplyAsync(String uid) throws ParseException {
        // 异步执行的逻辑
        pushLabSuccessApply(uid);
    }

    @Async
    public void pushMeetApplyAsync(String uid, List<WorkFlowNodeDto> dtoList) {
        // 异步执行的逻辑
        pushMeetApply(uid, dtoList);
    }

    @Async
    public void pushMeetSuccessApplyAsync(String uid) throws ParseException {
        // 异步执行的逻辑
        pushMeetSuccessApply(uid);
    }

    @Async
    public void pushMeetFailAsync(String uid) throws ParseException {
        // 异步执行的逻辑
        pushMeetFailApply(uid);
    }


    //推送待审核会议室消息
    public void pushMeetApply(String uid, List<WorkFlowNodeDto> workFlowNodeDTOs) {
        JSONObject jsonObject = getMeetInfo(uid);
        //推送消息
        for (WorkFlowNodeDto workFlowNodeDTO : workFlowNodeDTOs) {
            JSONArray weChatUser = getWeChatUser(workFlowNodeDTO.getInfoId());
            if (weChatUser.size() != 0) {
                String openid = weChatUser.getJSONObject(0).getString("openid");
                String wxuserid = weChatUser.getJSONObject(0).getString("uid");
                String topic = "/campus/weixintemplatemsg";
                RedisMessageEntity message = new RedisMessageEntity();
                JSONObject data = new JSONObject();
                data.put("msgid", UUID.randomUUID().toString());
                data.put("openid", openid);
                data.put("wxuserid", wxuserid);
                data.put("cfgcode", "OPENTM417832652");
                JSONObject wxdata = new JSONObject();
                wxdata.put("thing1", new TemplateMessageData(jsonObject.getString("roomname")).toJSONString());
                wxdata.put("thing5", new TemplateMessageData(jsonObject.getString("infoname")).toJSONString());
                wxdata.put("time2", new TemplateMessageData(jsonObject.getString("daily")).toJSONString());
                data.put("url", String.format("%s/meeting/bookaudit_detail?uid=%s&time=%s", WebConfig.getDomain(), uid, System.currentTimeMillis()));
                data.put("wxtempdata", wxdata);
                message.setCmd("weixintemplatemsg");
                message.setData(data);
                message.setDevtype(0);
                redispublish.Publish(topic, message);
            }
        }
    }

    //推送待审核实验室消息
    public void pushLabApply(String appid, List<WorkFlowNodeDto> workFlowNodeDTOs) {
        JSONObject jsonObject = getLabInfo(appid);
        //推送消息
        for (WorkFlowNodeDto workFlowNodeDTO : workFlowNodeDTOs) {
            JSONArray weChatUser = getWeChatUser(workFlowNodeDTO.getInfoId());
            if (weChatUser.size() != 0) {
                String openid = weChatUser.getJSONObject(0).getString("openid");
                String wxuserid = weChatUser.getJSONObject(0).getString("uid");
                String topic = "/campus/weixintemplatemsg";
                RedisMessageEntity message = new RedisMessageEntity();
                JSONObject data = new JSONObject();
                data.put("msgid", UUID.randomUUID().toString());
                data.put("openid", openid);
                data.put("wxuserid", wxuserid);
                data.put("cfgcode", "OPENTM417832640");
                JSONObject wxdata = new JSONObject();
                wxdata.put("thing1", new TemplateMessageData(jsonObject.getString("roomname")).toJSONString());
                wxdata.put("thing2", new TemplateMessageData(jsonObject.getString("infoname")).toJSONString());
                wxdata.put("time4", new TemplateMessageData(jsonObject.getString("starttime")).toJSONString());
                wxdata.put("time5", new TemplateMessageData(jsonObject.getString("endtime")).toJSONString());
                data.put("url", String.format("%s/lab/applyAudit_detail?uid=%s&time=%s", WebConfig.getDomain(), appid, System.currentTimeMillis()));
                data.put("wxtempdata", wxdata);
                message.setCmd("weixintemplatemsg");
                message.setData(data);
                message.setDevtype(0);
                redispublish.Publish(topic, message);
            }
        }
    }

    //实验室成功消息
    public void pushLabSuccessApply(String appid) {
        JSONObject jsonObject = getLabInfo(appid);
        //推送消息
        JSONArray weChatUser = getWeChatUser(jsonObject.getString("infoid"));
        if (weChatUser.size() != 0) {
            String openid = weChatUser.getJSONObject(0).getString("openid");
            String wxuserid = weChatUser.getJSONObject(0).getString("uid");
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("cfgcode", "OPENTM417832640");
            JSONObject wxdata = new JSONObject();
            wxdata.put("thing1", new TemplateMessageData(jsonObject.getString("roomname")).toJSONString());
            wxdata.put("thing2", new TemplateMessageData(jsonObject.getString("infoname")).toJSONString());
            wxdata.put("time4", new TemplateMessageData(jsonObject.getString("starttime")).toJSONString());
            wxdata.put("time5", new TemplateMessageData(jsonObject.getString("endtime")).toJSONString());
            data.put("url", String.format("%s/lab/applyRecord_detail?uid=%s&time=%s", WebConfig.getDomain(), appid, System.currentTimeMillis()));
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);
        }
    }

    //实验室失败消息
    public void pushLabFailApply(String appid) {
        JSONObject jsonObject = getLabInfo(appid);
        //推送消息
        JSONArray weChatUser = getWeChatUser(jsonObject.getString("infoid"));
        if (weChatUser.size() != 0) {
            String openid = weChatUser.getJSONObject(0).getString("openid");
            String wxuserid = weChatUser.getJSONObject(0).getString("uid");
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("cfgcode", "OPENTM417832640");
            JSONObject wxdata = new JSONObject();
            wxdata.put("thing1", new TemplateMessageData(jsonObject.getString("roomname")).toJSONString());
            wxdata.put("thing2", new TemplateMessageData(jsonObject.getString("infoname")).toJSONString());
            wxdata.put("time4", new TemplateMessageData(jsonObject.getString("starttime")).toJSONString());
            wxdata.put("time5", new TemplateMessageData(jsonObject.getString("endtime")).toJSONString());
            data.put("url", String.format("%s/lab/applyRecord_detail?uid=%s&time=%s", WebConfig.getDomain(), appid, System.currentTimeMillis()));
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);
        }
    }

    //会议室成功消息
    public void pushMeetSuccessApply(String appid) {
        JSONObject jsonObject = getMeetInfo(appid);
        //推送消息
        JSONArray weChatUser = getWeChatUser(jsonObject.getString("infoid"));
        if (weChatUser.size() != 0) {
            String openid = weChatUser.getJSONObject(0).getString("openid");
            String wxuserid = weChatUser.getJSONObject(0).getString("uid");
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("cfgcode", "OPENTM417832651");
            JSONObject wxdata = new JSONObject();
            wxdata.put("thing1", new TemplateMessageData(jsonObject.getString("roomname")).toJSONString());
            wxdata.put("thing5", new TemplateMessageData(jsonObject.getString("infoname")).toJSONString());
            wxdata.put("time2", new TemplateMessageData(jsonObject.getString("daily")).toJSONString());
            data.put("url", String.format("%s/meeting/bookrecord.detail?uid=%s&time=%s", WebConfig.getDomain(), appid, System.currentTimeMillis()));
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);
        }
    }

    //会议室失败消息
    public void pushMeetFailApply(String appid) {
        JSONObject jsonObject = getMeetInfo(appid);
        //推送消息
        JSONArray weChatUser = getWeChatUser(jsonObject.getString("infoid"));
        if (weChatUser.size() != 0) {
            String openid = weChatUser.getJSONObject(0).getString("openid");
            String wxuserid = weChatUser.getJSONObject(0).getString("uid");
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("cfgcode", "OPENTM417832650");
            JSONObject wxdata = new JSONObject();
            wxdata.put("thing1", new TemplateMessageData(jsonObject.getString("roomname")).toJSONString());
            wxdata.put("thing5", new TemplateMessageData(jsonObject.getString("infoname")).toJSONString());
            wxdata.put("time2", new TemplateMessageData(jsonObject.getString("daily")).toJSONString());
            data.put("url", String.format("%s/meeting/bookrecord.detail?uid=%s&time=%s", WebConfig.getDomain(), appid, System.currentTimeMillis()));
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);
        }
    }

    public JSONArray getWeChatUser(String key) {
        String getWeChatUser = "SELECT wu.uid,wu.openid,ct.name  " +
                "  FROM tb_weixin_user as wu  " +
                "  LEFT JOIN tb_card_teachstudinfo as ct on ct.uid = wu.docid " +
                "  where wu.docid='" + key + "' AND wu.status=1 ";
        return dbService.QueryList(getWeChatUser);
    }

    public JSONObject getLabInfo(String formId) {
        String sql = "select da.name as roomname,ar.infoid,date_format(at.starttime,'%Y-%m-%d %H:%i:%s') as starttime,date_format(at.endtime,'%Y-%m-%d %H:%i:%s') as endtime,ct.name as infoname from tb_laboratory_apply_record ar " +
                "join tb_dev_areaframework da on da.code = ar.areacode " +
                "join tb_laboratory_apply_time at on ar.uid = at.recordid " +
                "join tb_card_teachstudinfo ct on ct.uid = ar.infoid " +
                "where ar.uid = '" + formId + "'";
        return dbService.QueryJSONObject(sql);
    }

    public JSONObject getMeetInfo(String formId) {
        String sql = "select distinct da.name as roomname,ar.infoid,ct.name as infoname,bt.daily from " +
                "tb_meeting_book_record ar " +
                "join tb_dev_areaframework da on da.code = ar.areacode " +
                "join tb_meeting_book_time bt on ar.uid = bt.recordid " +
                "join tb_card_teachstudinfo ct on ct.uid = ar.infoid " +
                "where ar.uid = '" + formId + "'";
        return dbService.QueryJSONObject(sql);
    }

    public void pushAskResultApply(String uid,String name,String result) {
        String infoSQL = "select infoname,date_format(starttime, '%Y-%m-%d %H:%i') as starttime,date_format(endtime, '%Y-%m-%d %H:%i') as endtime,infoid from tb_card_ask_for_leave where uid = '" + uid + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(infoSQL);
        String sql = "select openid,uid as uid from tb_weixin_user where docid = '" + jsonObject.getString("infoid") + "'";
        List<WeiXinUser> weiXinUsers = dbService.queryList(sql, WeiXinUser.class);
        for (WeiXinUser weiXinUser : weiXinUsers) {
            String openid = weiXinUser.getOpenid();
            String wxuserid = weiXinUser.getUid();
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("cfgcode", "OPENTM417832643");
            data.put("url", String.format("%s/askforleave/detailedLeaveRecord?uid=%s&time=%s", WebConfig.getDomain(), uid, System.currentTimeMillis()));
            JSONObject wxdata = new JSONObject();
            if (WebConfig.getStartNew()) {
                wxdata.put("thing1", new TemplateMessageData(jsonObject.getString("infoname")));
                wxdata.put("thing4", new TemplateMessageData(name));
                wxdata.put("time2", new TemplateMessageData(jsonObject.getString("starttime")));
                wxdata.put("time5", new TemplateMessageData(DateHelper.format(new Date(), "yyyy-MM-dd")).toJSONString());
                wxdata.put("const3", new TemplateMessageData(result));
            } else {
                wxdata.put("first", new TemplateMessageData("请假申请结果通知", "#4caf50").toJSONString());
                wxdata.put("keyword1", new TemplateMessageData("如有疑问，请联系学校管理员").toJSONString());
                wxdata.put("keyword2", new TemplateMessageData(result));
                wxdata.put("keyword3", new TemplateMessageData(DateHelper.format(new Date(), "yyyy-MM-dd")).toJSONString());
                wxdata.put("keyword4", new TemplateMessageData(name).toJSONString());
                wxdata.put("remark", new TemplateMessageData("请处理请假请求").toJSONString());
            }
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);
        }

    }

    public void pushDoorResultApply(String uid,String name,String result) {
        String infoSQL = "select applicant_name infoname,date_format(open_time_start, '%Y-%m-%d %H:%i') as starttime,date_format(open_time_end, '%Y-%m-%d %H:%i') as endtime,infoid from tb_access_application where id = '" + uid + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(infoSQL);
        String sql = "select openid,uid as uid from tb_weixin_user where docid = '" + jsonObject.getString("infoid") + "'";
        List<WeiXinUser> weiXinUsers = dbService.queryList(sql, WeiXinUser.class);
        for (WeiXinUser weiXinUser : weiXinUsers) {
            String openid = weiXinUser.getOpenid();
            String wxuserid = weiXinUser.getUid();
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("cfgcode", "OPENTM20250417");
            data.put("url", String.format("%s/alleyway/detailedDoorFromRecord?uid=%s&time=%s", WebConfig.getDomain(), uid, System.currentTimeMillis()));
            JSONObject wxdata = new JSONObject();
            if (WebConfig.getStartNew()) {
                wxdata.put("thing1", new TemplateMessageData(jsonObject.getString("infoname")));
                wxdata.put("thing2", new TemplateMessageData(result));
            } else {
                wxdata.put("first", new TemplateMessageData("开门申请结果通知", "#4caf50").toJSONString());
                wxdata.put("keyword1", new TemplateMessageData("如有疑问，请联系公司管理员").toJSONString());
                wxdata.put("keyword2", new TemplateMessageData(result));
                wxdata.put("keyword3", new TemplateMessageData(DateHelper.format(new Date(), "yyyy-MM-dd")).toJSONString());
                wxdata.put("keyword4", new TemplateMessageData(name).toJSONString());
                wxdata.put("remark", new TemplateMessageData("请处理开门请求").toJSONString());
            }
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);
        }

    }

    public void pushNextAskApply(String uid, List<WorkFlowNodeDto> dtoList) {
        String infoSQL = "select afl.infoname,date_format(afl.starttime, '%Y-%m-%d %H:%i') as starttime,date_format(afl.endtime, '%Y-%m-%d %H:%i') as endtime,date_format(afl.createdate, '%Y-%m-%d %H:%i') as createdate,afl.infoid,ct.linkmobile1,afl.reason from tb_card_ask_for_leave afl " +
                " left join tb_card_teachstudinfo ct on ct.uid = afl.infoid where afl.uid = '" + uid + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(infoSQL);
        String infoname = jsonObject.getString("infoname");
        String reason = jsonObject.getString("reason");
        String starttime = jsonObject.getString("starttime");
        String endtime = jsonObject.getString("endtime");
        String createdate = jsonObject.getString("createdate");
        for (WorkFlowNodeDto nodeDto : dtoList) {
            String sql = "select openid,uid as uid from tb_weixin_user where docid = '" + nodeDto.getInfoId() + "'";
            List<WeiXinUser> weiXinUsers = dbService.queryList(sql, WeiXinUser.class);
            for (WeiXinUser weiXinUser : weiXinUsers) {
                String openid = weiXinUser.getOpenid();
                String wxuserid = weiXinUser.getUid();
                String topic = "/campus/weixintemplatemsg";
                RedisMessageEntity message = new RedisMessageEntity();
                JSONObject data = new JSONObject();
                data.put("msgid", UUID.randomUUID().toString());
                data.put("openid", openid);
                data.put("wxuserid", wxuserid);
                data.put("cfgcode", "OPENTM415931101");
                data.put("url", String.format("%s/askforleave/auditleave?uid=%s&time=%s", WebConfig.getDomain(), uid, System.currentTimeMillis()));
//                JSONObject wxdata = new JSONObject();
//                wxdata.put("first", new TemplateMessageData("您收到了" + infoname + "的请假申请", "#4caf50").toJSONString());
//                wxdata.put("keyword1", new TemplateMessageData(infoname).toJSONString());
//                wxdata.put("keyword2", new TemplateMessageData(reason).toJSONString());
//                wxdata.put("keyword3", new TemplateMessageData(starttime + "至" + endtime).toJSONString());
//                wxdata.put("remark", new TemplateMessageData("请处理请假请求").toJSONString());
//                data.put("wxtempdata", wxdata);
                JSONObject wxdata = new JSONObject();
                if (WebConfig.getStartNew()) {
                    wxdata.put("thing1", new TemplateMessageData(infoname).toJSONString());
                    wxdata.put("time3", new TemplateMessageData(starttime).toJSONString());
                    wxdata.put("time4", new TemplateMessageData(endtime).toJSONString());
                }else {
                    wxdata.put("first", new TemplateMessageData("您收到了" + infoname + "的请假申请", "#4caf50").toJSONString());
                    wxdata.put("keyword1", new TemplateMessageData(infoname).toJSONString());
                    wxdata.put("keyword2", new TemplateMessageData(reason));
                    wxdata.put("keyword3", new TemplateMessageData(starttime + "至" + endtime).toJSONString());
                    wxdata.put("remark", new TemplateMessageData("请处理请假请求").toJSONString());
                }
                data.put("wxtempdata", wxdata);
                message.setCmd("weixintemplatemsg");
                message.setData(data);
                message.setDevtype(0);
                redispublish.Publish(topic, message);
            }

        }
    }

    public void pushNextDoorApply(String uid, List<WorkFlowNodeDto> dtoList) {
        String infoSQL = "select afl.applicant_name infoname ,date_format(afl.open_time_start, '%Y-%m-%d %H:%i') as starttime,date_format(afl.open_time_end, '%Y-%m-%d %H:%i') as endtime,date_format(afl.created_at, '%Y-%m-%d %H:%i') as createdate,afl.infoid,afl.reason from tb_access_application afl " +
                " where afl.id = '" + uid + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(infoSQL);
        String infoname = jsonObject.getString("infoname");
        String reason = jsonObject.getString("reason");
        String starttime = jsonObject.getString("starttime");
        String endtime = jsonObject.getString("endtime");
        String createdate = jsonObject.getString("createdate");
        for (WorkFlowNodeDto nodeDto : dtoList) {
            String sql = "select openid,uid as uid from tb_weixin_user where docid = '" + nodeDto.getInfoId() + "'";
            List<WeiXinUser> weiXinUsers = dbService.queryList(sql, WeiXinUser.class);
            for (WeiXinUser weiXinUser : weiXinUsers) {
                String openid = weiXinUser.getOpenid();
                String wxuserid = weiXinUser.getUid();
                String topic = "/campus/weixintemplatemsg";
                RedisMessageEntity message = new RedisMessageEntity();
                JSONObject data = new JSONObject();
                data.put("msgid", UUID.randomUUID().toString());
                data.put("openid", openid);
                data.put("wxuserid", wxuserid);
                data.put("cfgcode", "OPENTM20250417");
                data.put("url", String.format("%s/alleyway/auditdoorfrom?uid=%s&time=%s", WebConfig.getDomain(), uid, System.currentTimeMillis()));
//                JSONObject wxdata = new JSONObject();
//                wxdata.put("first", new TemplateMessageData("您收到了" + infoname + "的请假申请", "#4caf50").toJSONString());
//                wxdata.put("keyword1", new TemplateMessageData(infoname).toJSONString());
//                wxdata.put("keyword2", new TemplateMessageData(reason).toJSONString());
//                wxdata.put("keyword3", new TemplateMessageData(starttime + "至" + endtime).toJSONString());
//                wxdata.put("remark", new TemplateMessageData("请处理请假请求").toJSONString());
//                data.put("wxtempdata", wxdata);
                JSONObject wxdata = new JSONObject();
                if (WebConfig.getStartNew()) {
                    wxdata.put("thing1", new TemplateMessageData(infoname).toJSONString());
                    wxdata.put("thing2", new TemplateMessageData(reason).toJSONString());
                }else {
                    wxdata.put("first", new TemplateMessageData("您收到了" + infoname + "的开门申请", "#4caf50").toJSONString());
                    wxdata.put("keyword1", new TemplateMessageData(infoname).toJSONString());
                    wxdata.put("keyword2", new TemplateMessageData(reason));
                    wxdata.put("keyword3", new TemplateMessageData(starttime + "至" + endtime).toJSONString());
                    wxdata.put("remark", new TemplateMessageData("请处理开门请求").toJSONString());
                }
                data.put("wxtempdata", wxdata);
                message.setCmd("weixintemplatemsg");
                message.setData(data);
                message.setDevtype(0);
                redispublish.Publish(topic, message);
            }

        }
    }
}
