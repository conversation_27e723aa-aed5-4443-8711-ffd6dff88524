package com.ymiots.campusmp.task;

import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.entity.card.OrgFramework;
import com.ymiots.campusmp.service.SysTaskService;
import com.ymiots.campusmp.service.weixinwork.WorkUserService;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.common.ScheduledInterface;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository("WeiXinWorkUserSycnTask")
public class WeiXinWorkUserSycnTask extends BaseService implements ScheduledInterface{

    @Autowired
    SysTaskService taskservice;
    
    @Autowired
    WorkUserService workuser;
    
	@Override
	public Runnable doTask(final String code,String sqlcode) {
		// TODO Auto-generated method stub
		return new Runnable() {
            @Override
            public void run() {
            	Log.info(this.getClass(), "开始同步企业微信用户数据");
            	long startTimeMillis=System.currentTimeMillis();
            	try {
                   
            		String sql="select uid,orgcode orgCode,code from tb_card_orgframework where creatorid = '企业微信自动同步'";
					List<OrgFramework> orgFrameworks = dbService.queryList(sql, OrgFramework.class);
					Log.info(this.getClass(), "同步用户的企业微信部门共:" + String.valueOf(orgFrameworks.size()) + "个");
					for (OrgFramework orgFramework : orgFrameworks) {
						workuser.SyncNewWeiXinWorkOrgUserData(orgFramework);
					}
                    taskservice.UpdateSysTaskLastExeTime(code, "执行正常",startTimeMillis);
                } catch (Exception ex) {
                    Log.error(this.getClass(), "微信企业号用户档案同步异常：" + ex.getMessage());
                    taskservice.UpdateSysTaskLastExeTime(code, "执行失败："+ex.getMessage(),startTimeMillis);
                }
            }
		};
	}
	
}
