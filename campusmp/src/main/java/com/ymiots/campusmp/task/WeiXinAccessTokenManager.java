package com.ymiots.campusmp.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.common.OsConstant;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.config.redis.RedisClient;
import com.ymiots.campusmp.service.AccessTokenService;
import com.ymiots.campusmp.service.ConsumeService;
import com.ymiots.campusmp.utils.weixin.entity.AccessToken;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.Log;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class WeiXinAccessTokenManager extends BaseService{

	@Autowired
	AccessTokenService atservice;
	
	@Autowired
	RedisClient redisclient;

	@Autowired
	private DBService dbService;

	@Autowired
	private ConsumeService consumeService;

	/**
	 * 每隔115分钟刷新一次access_token
	 */
	@Scheduled(cron = "0 0/115 * * * ?")
	public void RenewAccessToken() {
		
		if(StringUtils.isBlank(WebConfig.getWeixinAppid()) || StringUtils.isBlank(WebConfig.getWeixinAppSecret())) {
			return;
		}
		
		Log.info(WeiXinAccessTokenManager.class, "刷新access_token");
		
		AccessToken at=	atservice.GetNewAccessToken();
		if (at == null) {
			Log.error(WeiXinAccessTokenManager.class, "刷新access_token错误：AccessToken is null");
		}else if(at.getErrcode()!=0) {
			Log.error(WeiXinAccessTokenManager.class, "刷新access_token错误："+at.getErrmsg());
		}else {
			redisclient.setString(AccessTokenService.ACCESS_TOKEN_REDISKEY, at.getAccess_token(), at.getExpires_in(), TimeUnit.SECONDS);
		}
	}
	
	/**
	 * 每隔118分钟刷新一次access_token
	 */
//	@Scheduled(cron = "0 0/118 * * * ?")
//	public void RenewWorkAccessToken() {
//
//		if(StringUtils.isBlank(WebConfig.getWeixinWorkCorpid()) || StringUtils.isBlank(WebConfig.getWeixinWorkSecret())) {
//			return;
//		}
//
//		Log.info(WeiXinAccessTokenManager.class, "刷新企业access_token");
//
//		AccessToken at=	atservice.GetNewWorkAccessToken();
//		if (at == null) {
//			Log.error(WeiXinAccessTokenManager.class, "刷新企业access_token错误：AccessToken is null");
//		}else if(at.getErrcode()!=0) {
//			Log.error(WeiXinAccessTokenManager.class, "刷新企业access_token错误："+at.getErrmsg());
//		}else {
//			redisclient.setString(AccessTokenService.WORKACCESS_TOKEN_REDISKEY, at.getAccess_token(), at.getExpires_in(), TimeUnit.SECONDS);
//		}
//	}
	@Data
	public static class WeXinTransation {
		private String uid;
		private String transactionId;
		private String deviceInfo;
		private String infoId;
		private String cardId;
		private Long amount;
		private Integer isPayed;
		private String wxuserid;
		@JsonFormat(pattern = OsConstant.DATE_FORMAT, timezone = OsConstant.TIME_ZONE)
		private Date tradeDate;
	}

//	@Scheduled(cron = "0 0/5 * * * ?")
	public void checkRecharge(){
		Log.info(this.getClass(), "------开始核对充值------");
		// 获取当前日期时间
		LocalDateTime currentDateTime = LocalDateTime.now();

		// 计算三天前的日期时间
		LocalDateTime threeDaysAgoDateTime = currentDateTime.minus(2, ChronoUnit.DAYS);

		// 定义日期时间格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String time = threeDaysAgoDateTime.format(formatter);
		//查询充值状态的逻辑
		String sql = "select wx.uid,wx.ispayed as isPayed,ff.amount,wx.totalfee as totalFee,wx.deviceinfo as deviceInfo,ff.opt_date,fo.transaction_id as transactionId from" +
				" tb_weixin_transaction wx inner join fy_flow ff on ff.top_os_order_no = wx.uid " +
				" inner join fy_order fo on fo.mchnt_order_no = ff.top_mchnt_order_no " +
				" where ispayed=0 and wx.createdate>'" + time+"'";
		List<WeXinTransation> trans = dbService.queryList(sql, WeXinTransation.class);
		for (WeXinTransation tran : trans) {
			updateTransaction(tran.getUid(), tran.getTransactionId(), Math.toIntExact(tran.getAmount()), tran.getTradeDate(), "1");
		}
		Log.info(this.getClass(), "------结束核对充值------");
	}

//	@Scheduled(cron = "0 0/30 * * * ?")
	public void sycnWeiXinTask() {
		// 获取当前日期时间
		LocalDateTime currentDateTime = LocalDateTime.now();

		// 计算三天前的日期时间
		LocalDateTime threeDaysAgoDateTime = currentDateTime.minus(2, ChronoUnit.DAYS);

		// 定义日期时间格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String time = threeDaysAgoDateTime.format(formatter);
		String queryFlow = "select top_os_order_no as uid,ff.amount,cc.uid as cardId,wu.uid as wxuserid,cc.infoid as infoId from fy_flow ff" +
				" inner join fy_order fo on fo.mchnt_order_no = ff.top_mchnt_order_no " +
				" inner join tb_weixin_user wu on wu.openid = fo.reserved_buyer_login_id " +
				" inner join tb_card_cardinfo cc on wu.docid = cc.infoid where opt_date >'" + time + "'";
		List<WeXinTransation> list = dbService.queryList(queryFlow, WeXinTransation.class);
		String queryTran = "select uid from tb_weixin_transaction where createdate>'" + time + "'";
		List<WeXinTransation> transactions = dbService.queryList(queryTran, WeXinTransation.class);
		//过滤掉已经存在的
		// 使用流过滤出不在transactions中的对象
		List<WeXinTransation> weXinTransations = list.stream()
				.filter(item -> transactions.stream().noneMatch(transaction -> item.getUid().equals(transaction.getUid())))
				.collect(Collectors.toList());
		//将失败的交易插入mp交易表中
		StringBuilder insertTransaction = new StringBuilder("INSERT INTO tb_weixin_transaction (uid,wxuserid,infoid,cardid,remark,ispayed,createdate,status) VALUES ");
		for (WeXinTransation weXinTransation : weXinTransations) {
			insertTransaction.append("('").append(weXinTransation.getUid()).append("','").append(weXinTransation.getWxuserid()).append("','").append(weXinTransation.getInfoId()).append("','").append(weXinTransation.getCardId()).append("','").append("手动充值").append("',0,now(),1),");
		}
		// 去除最后一个逗号
		insertTransaction = new StringBuilder(insertTransaction.substring(0, insertTransaction.length() - 1));
		if (weXinTransations.size() > 0) {
			dbService.excuteSql(insertTransaction.toString());
		}
	}

	public void updateTransaction(String osOrderNo, String transactionId, Integer total, Date tradeDate, String wallet) {
		double money = total / 100.00;
		if (tradeDate == null) {
			Log.error(ConsumeService.class, "支付完成时间为null，赋值默认值为当前时间");
			tradeDate = new Date();
		}
		String sql = "select wt.cardid,card.cardno,wt.ispayed,card.infoid from tb_weixin_transaction wt inner join tb_card_cardinfo card on card.uid=wt.cardid where wt.uid='" + osOrderNo + "' ";
		JSONArray cardlist = dbService.QueryList(sql);
		if (cardlist.size() == 0) {
			Json.getJsonResult("无有效支付订单");
			return;
		}
		JSONObject item = cardlist.getJSONObject(0);
		String cardid = item.getString("cardid");
		String cardno = item.getString("cardno");
		String infoid = item.getString("infoid");
		int ispayed = item.getIntValue("ispayed");
		if (ispayed == 1) {
			Json.getJsonResult(true, "订单已完成状态更新，无需重复处理");
			return;
		}

		String timeendstr = DateHelper.format(tradeDate);
		String update = "UPDATE tb_weixin_transaction set transactionid=?,totalfee=?,timeend=?,ispayed=1 WHERE uid=? and ispayed=0";
		int count = dbService.excuteSql(update, transactionId, money, timeendstr, osOrderNo);
		if (count > 0) {
			count = dbService.getCount("tb_card_transaction_detail", "interfixid='" + osOrderNo + "'");
			if (count == 0) {
				String orderno = UUID.randomUUID().toString();
				if (StringUtils.isNotBlank(wallet) && "1".equals(wallet)) {
					sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,interfixid,tradedate,createdate,status) "
							+ "values(?,?,?,?,1,?,1,2,1,1,'微信充值',?,now(),now(),1)";
					dbService.excuteSql(sql, orderno, cardid, infoid, cardno, money, osOrderNo);
					Log.info(this.getClass(), "补充金额:" + money);
				} else if (StringUtils.isNotBlank(wallet) && "2".equals(wallet)) {
					sql = "insert into tb_waterctrl_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,orderstatus,payway,paytype,des,interfixid,tradedate,createdate,status) "
							+ "values(?,?,?,?,1,?,2,1,1,'微信水控充值',?,now(),now(),1)";
					dbService.excuteSql(sql, orderno, cardid, infoid, cardno, money, osOrderNo);
					Log.info(this.getClass(), "补充金额:" + money);
				}
			}
			Json.getJsonResult(true);
		} else {
			Json.getJsonResult("订单更新失败");
		}
	}
}
