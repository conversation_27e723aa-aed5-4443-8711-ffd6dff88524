package com.ymiots.campusmp.task;

import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.common.ScienerConfig;
import com.ymiots.campusmp.constant.ScienerAPI;
import com.ymiots.campusmp.model.dto.http.sciener.ScienerGetToken;
import com.ymiots.campusmp.utils.OkHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

/**
 * 赛脑 门锁云平台获取Token
 *
 * <AUTHOR>
 * @date 2023/03/16
 */
@Slf4j
@Component
public class ScienerGetTokenTask {

    @Autowired
    private ScienerConfig config;

    @Autowired
    private DBService dbService;

    @Autowired
    private OkHttpUtil okHttpUtil;

    @PostConstruct
    public void init() {
        new Thread(() -> {
            Map<String, Object> getTokenParam = new HashMap<>();
            getTokenParam.put("clientId", config.getClientId());
            getTokenParam.put("clientSecret", config.getClientSecret());
            getTokenParam.put("username", config.getUsername());
            getTokenParam.put("password", config.getPassword());

            Map<String, Object> refreshTokenParam = new HashMap<>();
            refreshTokenParam.put("clientId", config.getClientId());
            refreshTokenParam.put("clientSecret", config.getClientSecret());
            refreshTokenParam.put("grant_type", "refresh_token");
            if (config.isStart()) {
                while (true) {
                    ScienerGetToken token = getToken(getTokenParam);
                    refreshTokenParam.put("refresh_token", token.getRefreshToken());
                    // 提前5分钟刷新token
                    int refreshIn = token.getExpiresIn() - (5 * 60);
                    if (refreshIn <= 0) {
                        // 刷新时间小于5分钟，直接刷新token
                        token = getToken(refreshTokenParam);
                        refreshIn = token.getExpiresIn() - (5 * 60);
                    }
                    config.setToken(token.getAccessToken());
                    try {
                        Thread.sleep(refreshIn);
                    } catch (Exception e) {
                        log.error("门锁刷新Token线程被中断");
                        break;
                    }
                }
            }
        }).start();
    }

    public ScienerGetToken getToken(Map<String, Object> param) {
        ScienerGetToken tokenRes = null;
        String sql = "update tb_dev_accesscontroller set devstatus = ? where devmodel = 50 ";
        try {
            tokenRes = okHttpUtil.postFormUrlEncoder(String.format(ScienerAPI.GET_TOKEN, config.getUrl()), param, ScienerGetToken.class);
            dbService.excuteSql(sql, 1);
            log.info("门锁刷新Token");
        } catch (IOException e) {
            dbService.excuteSql(sql, 0);
            log.error("赛脑云平台获取token失败: {}", e.getMessage());
            e.printStackTrace();
        }
        return tokenRes;
    }
}
