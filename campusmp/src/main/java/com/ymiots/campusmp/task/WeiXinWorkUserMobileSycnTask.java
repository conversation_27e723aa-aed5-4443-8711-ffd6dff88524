package com.ymiots.campusmp.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.service.SysTaskService;
import com.ymiots.campusmp.service.weixinwork.WorkUserService;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.common.ScheduledInterface;


@Repository("WeiXinWorkUserMobileSycnTask")
public class WeiXinWorkUserMobileSycnTask extends BaseService implements ScheduledInterface{

    @Autowired
    SysTaskService taskservice;
    
    @Autowired
    WorkUserService workuser;
    
	@Override
	public Runnable doTask(final String code,String sqlcode) {
		// TODO Auto-generated method stub
		return new Runnable() {
            @Override
            public void run() {
            	Log.info(this.getClass(), "开始同步企业微信用户手机号等数据");
            	long startTimeMillis=System.currentTimeMillis();
            	try {
            		String sql="SELECT uid FROM tb_weixin_workuser where mobile is null limit 500;";
            		JSONArray list= dbService.QueryList(sql);
            		Log.info(this.getClass(), "同步企业微信用户手机号等数据共:"+String.valueOf(list.size())+"个");
            		for(int i=0;i<list.size();i++) {
            			workuser.SyncWeiXinWorkUserData(list.getJSONObject(i).getString("uid"));
            		}
                    taskservice.UpdateSysTaskLastExeTime(code, "执行正常",startTimeMillis);
                } catch (Exception ex) {
                    Log.error(this.getClass(), "企业微信用户手机号等数据异常：" + ex.getMessage());
                    taskservice.UpdateSysTaskLastExeTime(code, "执行失败："+ex.getMessage(),startTimeMillis);
                }
            }
		};
	}
	
}
