package com.ymiots.campusmp.task;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.service.SysTaskService;
import com.ymiots.framework.common.ComHelper;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;

@Configuration
public class ScheduleConfig implements SchedulingConfigurer {

	@Autowired
	SysTaskService taskservice;
	
	@Autowired(required=true)
	private SysDynamicTaskService task;
	
	@Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        //设定一个长度10的定时任务线程池
        taskRegistrar.setScheduler(Executors.newScheduledThreadPool(20));
        
        AutoRunTask();
    }
	
	/**
	 * 自动启动任务
	 */
	public void AutoRunTask() {
		JSONArray list= taskservice.getSysCanRunTask();
		List<String> uids=new ArrayList<String>();
		for(int i=0;i<list.size();i++) {
			JSONObject item=list.getJSONObject(i);
			String uid=item.getString("uid");
			String name=item.getString("name");
			String code=item.getString("code");
			String taskclass=item.getString("taskclass");
			String cron=item.getString("cron");
			String sqlcode=item.getString("sqlcode");
			JsonResult resul= task.StartTask(code, taskclass, cron, sqlcode);
			if(resul.isSuccess()) {
			   uids.add(uid);
			   Log.info(ScheduleConfig.class, String.format("启动任务[%s]成功", name));
			}else {
				Log.error(ScheduleConfig.class, String.format("启动任务[%s]失败，错误：%s", name, resul.getMsg()));
				taskservice.UpdateSysTaskStatus(uid, "0", resul.getMsg());
			}
		}
		taskservice.UpdateSysTaskStatus(ComHelper.ListToArray(uids));
	}
}
