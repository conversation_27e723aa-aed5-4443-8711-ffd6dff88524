package com.ymiots.campusmp.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.ymiots.campusmp.service.SysTaskService;
import com.ymiots.campusmp.service.weixinwork.WorkOrgService;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.common.ScheduledInterface;


@Repository("WeiXinWorkOrgSycnTask")
public class WeiXinWorkOrgSycnTask implements ScheduledInterface{

    @Autowired
    SysTaskService taskservice;
    
    @Autowired
    WorkOrgService workorg;
    
	@Override
	public Runnable doTask(final String code,String sqlcode) {
		// TODO Auto-generated method stub
		return new Runnable() {
            @Override
            public void run() {
            	long startTimeMillis=System.currentTimeMillis();
            	try {
                   
            		workorg.SyncNewWeiXinWorkOrgData();
            		
                    taskservice.UpdateSysTaskLastExeTime(code, "执行正常",startTimeMillis);
                } catch (Exception ex) {
                    Log.error(this.getClass(), "微信企业号部门同步异常：" + ex.getMessage());
                    taskservice.UpdateSysTaskLastExeTime(code, "执行失败："+ex.getMessage(),startTimeMillis);
                }
            }
		};
	}
	
}
