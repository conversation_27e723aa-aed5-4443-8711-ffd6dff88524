//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.ymiots.campusmp.task;

import com.ymiots.campusmp.config.redis.RedisClient;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;

@Repository
public class SysDynamicTaskService extends ThreadPoolTaskScheduler {
    
    @Autowired
    @Lazy
    RedisClient redis;
    @Autowired
    SpringContextUtil springcontextutil;
    private static final long serialVersionUID = 1L;
    private Map<String, ScheduledFuture<?>> ScheduledFuturelist = new HashMap();

    public SysDynamicTaskService() {
    }

    public JsonResult CancelTask(String code, String taskclass) {
        String taskkey = String.format("%s_%s", taskclass, code);
        if (this.ScheduledFuturelist.containsKey(taskkey)) {
            ScheduledFuture<?> future = (ScheduledFuture)this.ScheduledFuturelist.get(taskkey);
            if (null != future) {
                future.cancel(true);
                this.ScheduledFuturelist.remove(taskkey);
                return Json.getJsonResult(true, String.format("计划任务[%s]停止成功", taskkey));
            } else {
                this.ScheduledFuturelist.remove(taskkey);
                return Json.getJsonResult(false, "停止任务失败");
            }
        } else {
            return Json.getJsonResult(false, "任务未启动");
        }
    }

    public JsonResult StartTask(String code, String taskclass, String Cron, String sqlcode) {
        String taskkey = String.format("%s_%s", taskclass, code);
        if (!this.ScheduledFuturelist.containsKey(taskkey)) {
            ScheduledInterface task = (ScheduledInterface)this.springcontextutil.getBean(taskclass);
            Trigger trigger = this.getCron(Cron);
            if (null == trigger) {
                return Json.getJsonResult(false, "任务Cron表达式格式错误");
            } else {
                ScheduledFuture<?> future = this.schedule(task.doTask(code, sqlcode), trigger);
                this.ScheduledFuturelist.put(taskkey, future);
                return Json.getJsonResult(true, String.format("计划任务[%s]启动成功", taskkey));
            }
        } else {
            return Json.getJsonResult(true, String.format("计划任务[%s]启动失败，请先停止", taskkey));
        }
    }

    public JsonResult ExecuteTask(String code, String taskclass, String sqlcode) {
        ScheduledInterface task = (ScheduledInterface)this.springcontextutil.getBean(taskclass);
        Runnable runnable = task.doTask(code, sqlcode);
        runnable.run();
        return Json.getJsonResult(true);
    }

    public JsonResult ChangeCron(String code, String taskclass, String Cron, String sqlcode) {
        JsonResult result = this.CancelTask(code, taskclass);
        if (!result.isSuccess()) {
            return result;
        } else {
            result = this.StartTask(code, taskclass, Cron, sqlcode);
            return result;
        }
    }

    public Trigger getCron(String cron) {
        try {
            if (StringUtils.isBlank(cron)) {
                return null;
            } else {
                Trigger trigger = new CronTrigger(cron);
                return trigger;
            }
        } catch (Exception var3) {
            Log.error(SysDynamicTaskService.class, var3.getMessage());
            return null;
        }
    }
}
