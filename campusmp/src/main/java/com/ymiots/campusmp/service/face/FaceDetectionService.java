package com.ymiots.campusmp.service.face;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.utils.OkHttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
public class FaceDetectionService {

    private static final Logger logger = LoggerFactory.getLogger(FaceDetectionService.class);


    @Autowired
    private OkHttpUtil okHttpUtil;

    public boolean pf2pMatch(String img1,String img2){
        HashMap<String, String> map = new HashMap<>();
        map.put("img1", img1);
        map.put("img2", img2);
        String doPost = okHttpUtil.doPost("http://127.0.0.1:2024/config/picture/pf2pMatch", map);
        JSONObject parseObject = JSON.parseObject(doPost);
        Integer code = parseObject.getInteger("code");
        String msg = parseObject.getString("msg");
        if (code == 500) {
            logger.info("照片1{}，照片2{}，识别阀值：{}", img1, img2, msg);
            return false;
        } else {
            return true;
        }
    }

    public boolean faceDetection(String img1) {
        HashMap<String, String> map = new HashMap<>();
        map.put("img1", img1);
        String doPost = okHttpUtil.doPost("http://127.0.0.1:2024/config/picture/detectFaces", map);
        JSONObject parseObject = JSON.parseObject(doPost);
        Integer code = parseObject.getInteger("code");
        String msg = parseObject.getString("msg");
        if (code == 500) {
            logger.info("照片{}未识别到人脸", img1);
            return false;
        } else {
            return true;
        }
    }
}
