package com.ymiots.campusmp.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.controller.workflow.VisitorHandler;
import com.ymiots.campusmp.controller.workflow.WorkFlowUtil;
import com.ymiots.campusmp.controller.workflow.entity.VisitorInfo;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.entity.dto.visitor.AuditApplyRequest;
import com.ymiots.campusmp.entity.elevator.Elevator;
import com.ymiots.campusmp.redis.RedisMessageEntity;
import com.ymiots.campusmp.redis.RedisMessagePublish;
import com.ymiots.campusmp.redis.RedisPublish;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.utils.weixin.WeiXinWork;
import com.ymiots.framework.common.*;
import com.ymiots.framework.service.AliyunSendSmsService;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class VisitorService extends BaseService {

    @Autowired
    UserRedis userredis;

    @Autowired
    RedisMessagePublish redismsgpublish;

    @Autowired
    SysConfigService syscfg;

    @Autowired
    RedisPublish redispublish;

    @Autowired
    WeiXinWork weixinwork;

    @Autowired
    WorkFlowUtil workFlowUtil;

    @Autowired
    VisitorHandler handler;

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Autowired
    AccessTokenService token;

    @Autowired
    AliyunSendSmsService smg;

    //访客机提交预约
    public JsonResult  submitVisitorInfo(HttpServletRequest request, HttpServletResponse response, String visitorinfo) throws Exception {
        JSONObject visitor = JSONObject.parseObject(visitorinfo);
        String idCard = visitor.getString("idcard");
        String position = request.getHeader("position");
        String name = visitor.getString("name");
        String personInfoId = userredis.get(request).getDocid();
        String sex = visitor.getString("sex");
        String nation = visitor.getString("nation");
        String address = visitor.getString("address");
        String birthday = visitor.getString("birthday");
        String mobile = visitor.getString("mobile");
        String reason = visitor.getString("reason");
        String byVisitor = visitor.getString("byvisitor");
        String byVisitorId = visitor.getString("byvisitorid");
        String plateNo = visitor.getString("plateno");
        String things = visitor.getString("things");
        String areaposition = visitor.getString("areaposition");
        String areapositionName = visitor.getString("areapositionName");
        int hour = visitor.getIntValue("hour");
        Integer personNum = visitor.getInteger("personnum");
        String company = visitor.getString("company");
        if (hour == 0) {
            hour = 8;
        }

        String endTime = DateHelper.format(DateHelper.addHours(new Date(), hour));
        //添加人员信息
        if (idCard.length() > 15) {
            birthday = idCard.substring(7, 15);
        }
        //3.创建预约信息tb_visitor_subscribe
        String visitorFromId = UUID.randomUUID().toString();
        //判断是否默认审核通过
        String isDefaultAudit = syscfg.get("visitormachineneedauth");
        String status = "0";
        if ("0".equals(isDefaultAudit)) {
            status="1";
        }

        //创建二维码
        //生成访客二维码（无卡号时按照十六进制）
        String qrcode = RandomHelper.CreateQRcode();
        //设置过期时间
        String qrCodeTimeOut = syscfg.get("qrcodetimeout");
        if (StringUtils.isBlank(qrCodeTimeOut)) {
            qrCodeTimeOut = "60";
        }

        //上传照片
        //处理人证比对后的照片
        final StringBuilder photo = new StringBuilder();

        final StringBuilder idcardphoto = new StringBuilder();

        String finalphoto = "";

        List<String> visitorList = new ArrayList<>();

        //主访客uid
        String visitorInfoId = UUID.randomUUID().toString();
        if (StringUtils.isNotBlank(visitor.getString("photo"))) {
            if (!ComHelper.IsRemotResource(WebConfig.getDomain(), WebConfig.getResourceDomain())) {
                photo.append(FileHelper.Base64ToImageFile(visitor.getString("photo"), WebConfig.getUploaddir(), "face", String.format("%s.jpg", visitorInfoId)));
            } else {
                String url = ComHelper.getResourceUrl(WebConfig.getResourceDomain(), "/API/Face/SubmitFaceBase64");
                Map<String, String> params = new HashMap<String, String>();
                params.put("facedata", visitor.getString("photo"));
                params.put("faceuid", visitorInfoId);
                JsonResult rs = HttpRequest.HttpPost(url, params);
                if (rs.isSuccess()) {
                    if (rs.getData().getBoolean("success")) {
                        photo.append(rs.getData().getString("msg"));
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(visitor.getString("idcardphoto"))) {
            idcardphoto.append(FileHelper.Base64ToImageFile(visitor.getString("idcardphoto"), WebConfig.getUploaddir(), "idcard", String.format("%s.jpg", visitorInfoId)));
        }
        if (StringUtils.isNotEmpty(visitor.getString("photo"))){
            finalphoto = photo.toString();
        }else if (StringUtils.isNotEmpty(visitor.getString("idcardphoto"))){
            finalphoto = idcardphoto.toString();
        }
        //1. 如果visitorid（访客uid）为空，先创建访客信息
        String sql = "SELECT uid FROM tb_visitor_visitorinfo WHERE idcard='" + idCard + "'";
        String uid = dbService.queryOneField(sql, String.class);
        if (StringUtils.isNotBlank(uid)) {
            visitorInfoId = uid;
            String alterSQL = "UPDATE tb_visitor_visitorinfo SET name=?, idtype=?, idcard=?, sex=?, mobile=?, company=?, position=?, department=? WHERE uid = ?";
            dbService.excuteSql(alterSQL, name, 1, idCard, sex, mobile, company, position, null, visitorInfoId);
        }else {
            String createVisitor = "insert into tb_visitor_visitorinfo (uid,name,idtype,idcard,sex,birthday,mobile,"
                    + "company,position,department,createdate,creatorid,status,plateno,photo,idcardphoto,nation,address)values(?,?,?,?,?,?,?,?,?,?,now(),?,1,?,?,?,?,?)";
            dbService.excuteSql(createVisitor, visitorInfoId, name, 1, idCard, sex, birthday, mobile, company, position, null,visitorInfoId , plateNo, photo, finalphoto,nation,address);
        }
        visitorList.add(visitorInfoId);


        //判断能否预约
        JsonResult isAllowSubscribe = isAllowSubscribe(byVisitorId, name, mobile, visitorInfoId,null, idCard);
        if (!isAllowSubscribe.isSuccess()) {
            return isAllowSubscribe;
        }
        String createVisitorSubscribe = "insert into tb_visitor_subscribe (uid,visitorid,byvisitor,byvisitorid,plateno,areaposition,areapositionName,personnum,reason,things,visittime,leavetime,isinvited,"
                + "creatorid, createdate,status,isdefault,isleave) values(?,?,?,?,?,?,?,?,?,?,now(),?,0,?,now(),?,?,0)";
        dbService.excuteSql(createVisitorSubscribe, visitorFromId, visitorInfoId, byVisitor, byVisitorId, plateNo,areaposition, areapositionName,personNum, reason, things,  endTime, personInfoId, status, isDefaultAudit);
        //4.创建其他访客信息
        String createOtherVisitor = "insert into tb_visitor_subscribe_person (uid,visitor_subscribe_id,visitor_id,create_time,qrcode,qrcodetimeout) values(uuid(),'" + visitorFromId + "',?,now(),?,date_add(now(), interval " + qrCodeTimeOut + " second))";
        List<Object[]> args = new ArrayList<>();
        args.add(new Object[]{visitorInfoId, qrcode});
        for (String s : visitorList) {
            //生成访客二维码（无卡号时按照十六进制）
            if (!visitorInfoId.equals(s)){
                qrcode = RandomHelper.CreateQRcode();
                args.add(new Object[]{s, qrcode});
            }
        }
        jdbcTemplate.batchUpdate(createOtherVisitor, args);
        //判断是否需要审核
        List<WorkFlowNodeDto> workFlowNodeDTOs = new ArrayList<>();
        if ("1".equals(syscfg.get("visitormachineneedauth"))) {
            //获取工作流模板为请假的模板id
            String sqlMouldId = "SELECT uid FROM tb_workflow_mould WHERE mouldtype = 3 ";
            JSONArray ja = dbService.QueryList(sqlMouldId);
            String mouldId = ja.getJSONObject(0).getString("uid");

            if (ja.isEmpty()) {
                throw new RuntimeException("未配置审批模板");
            }

            workFlowNodeDTOs = workFlowUtil.selSubPerson(mouldId, byVisitorId, byVisitorId, "", "", 1);
            if (workFlowNodeDTOs.size() == 0) {
                throw new RuntimeException("未配置审核人，请联系管理员！");
            }
            //判断是否是审核人提交默认审核通过
            WorkFlowNodeDto nodeLevel = workFlowUtil.getNodeLevel(request, visitorInfoId, 3);
            if (nodeLevel != null) {
                String updateSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate,auditdate,auditid,remark) values (uuid(),?,?,?,?,now(),now(),?,?)";
                dbService.excuteSql(updateSql, visitorFromId, mouldId, 4, nodeLevel.getNodeLevel(), visitorInfoId, "审核人提交默认审核通过");
                if (nodeLevel.getIsFinal() == 1) {
                    String updateVisitorSubscribe = "update tb_visitor_subscribe set status = 1 where uid = ?";
                    dbService.excuteSql(updateVisitorSubscribe, visitorFromId);
                    return Json.getJsonResult(true, "提交成功");
                }
            }
            String insertRecordSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate) values (uuid(),?,?,?,1,now())";
            dbService.excuteSql(insertRecordSql, visitorFromId, mouldId, 0);
            handler.handleSuccess(visitorFromId, name,workFlowNodeDTOs,"","");
        }

        JSONObject jo = new JSONObject();
        jo.put("uid", visitorFromId);
        redispublish.Publish("/campus/visitorsubmsg", jo);

        JSONObject datajo = new JSONObject();
        datajo.put("qrcode", qrcode);
        datajo.put("visittime", DateHelper.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        handler.handleSuccess(visitorFromId, name,workFlowNodeDTOs,areaposition,"");
        return Json.getJsonResult(true,datajo);
    }

    public JsonResult addVisitorToBlack(HttpServletRequest request, HttpServletResponse response, String uid, String blackremark) {
        String userid = userredis.getUserId(request);
        JSONArray list = dbService.QueryList("select name,idcard,sex from tb_visitor_visitorinfo where uid='" + uid + "' ");
        if (list.size() == 0) {
            return Json.getJsonResult("访客信息不存在");
        }
        JSONObject item = list.getJSONObject(0);
        String idcard = item.getString("idcard");
        String name = item.getString("name");
        String sex = item.getString("sex");
        int count = dbService.getCount("tb_visitor_black", "idcard='" + idcard + "' ");
        if (count > 0) {
            return Json.getJsonResult("访客已列入黑名单");
        }
        String sql = "insert into tb_visitor_black(uid,name,idcard,sex,reason,createdate,creatorid,status) values(uuid(),?,?,?,?,now(),?,1) ";
        dbService.excuteSql(sql, name, idcard, sex, blackremark, userid);
        return Json.getJsonResult(true);
    }

    public JsonData getVisitorPassRecordList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit, String startTime, String endTime) {
        if(StringUtils.isBlank(key) && "1".equals(syscfg.get("visitorHelpOneself"))){
            JsonData jd = new JsonData();
            jd.setSuccess(true);
            return jd;
        }
        String fields = "vi.name,vi.sex,pr.facepath,pr.uid,date_format(pr.recordtime,'%Y-%m-%d %H:%i:%s') as recordtime,dev.devname,getareaname(dev.areacode) as areaname";
        String table = " tb_visitor_passrecord pr " +
                "inner join tb_dev_accesscontroller dev on dev.uid=pr.devid " +
                "inner join tb_visitor_visitorinfo vi on vi.uid=pr.visitorid ";
        String where = " 1=1 ";
        if (!StringUtils.isBlank(startTime) && !StringUtils.isBlank(endTime)) {
            where += " and (pr.recordtime between '" + startTime + "' and '" + endTime + "') ";
        }

        if (!StringUtils.isBlank(key)) {
            where += " and (vi.name like '%" + key + "%' or vi.idcard='"+key+"' or vi.mobile='"+key+"')";
        }
        String orderby = "pr.recordtime desc";
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    /**
     * 更新访客照片信息
     */
    public JsonResult UpdateVisitorFace(HttpServletRequest request, HttpServletResponse response, final String uid, final String visitorid, String photo) throws IOException {
        final StringBuffer photosb = new StringBuffer();
        if (!ComHelper.IsRemotResource(WebConfig.getDomain(), WebConfig.getResourceDomain())) {
            photosb.append(FileHelper.Base64ToImageFile(photo, WebConfig.getUploaddir(), "face", String.format("%s.jpg", visitorid)));
            String insertSQL = "update tb_visitor_visitorinfo set photo = ? where uid = ? ";
            dbService.excuteSql(insertSQL, photosb, visitorid);
        } else {
            String url = ComHelper.getResourceUrl(WebConfig.getResourceDomain(), "/API/Face/SubmitFaceBase64");
            Map<String, String> params = new HashMap<String, String>();
            params.put("facedata", photo);
            params.put("faceuid", visitorid);
            JsonResult rs = HttpRequest.HttpPost(url, params);
            if (rs.isSuccess()) {
                if (rs.getData().getBoolean("success")) {
                    photosb.append(rs.getData().getString("msg"));
                }
            }
        }
        String sql = "update tb_visitor_record_namelist set status=5,downnum=0 where visitorid='" + visitorid + "' ";
        dbService.excuteSql(sql);
        new Thread() {
            public void run() {
                try {
                    JSONObject data = new JSONObject();
                    data.put("uid", visitorid);
                    RedisMessageEntity rme = new RedisMessageEntity();
                    rme.setCmd("visitorfacedel");
                    rme.setData(data);
                    rme.setDevtype(4);
                    rme.setServer_uid("");
                    redismsgpublish.VisitorFaceDel(rme);
                } catch (Exception e) {
                    Log.error(VisitorService.class, e.getMessage());
                }
            }
        }.start();
        return Json.getJsonResult(true);
    }


    public JsonResult reDownFace(HttpServletRequest request, HttpServletResponse response, String recordid,String visitorId) {
//        String sql = "SELECT visitorid FROM tb_visitor_record where uid='" + recordid + "';";
//        JSONArray list = dbService.QueryList(sql);
//        if (list.size() == 0) {
//            return Json.getJsonResult(false, "来访记录不存在");
//        }
//        String visitorid = list.getJSONObject(0).getString("visitorid");
        String sql = "update tb_visitor_record_namelist set status=5,downnum=0 where visitorid='" + visitorId + "' and subscribeid = '" + recordid + "'";
        dbService.excuteSql(sql);
        return Json.getJsonResult(true);
    }

    public JsonData pcGetVisitorRecord(HttpServletRequest request, HttpServletResponse response, String idcard) {
        String sql = "SELECT vr.uid,vr.byvisitor,vi.name,vi.idcard "
                + " FROM tb_visitor_record vr inner join tb_visitor_visitorinfo vi on vi.uid=vr.visitorid LEFT JOIN tb_visitor_subscribe vs ON vs.uid=vr.subscribeid "
                + " where vr.isleave=0 and vi.idcard='" + idcard + "' and DATE_FORMAT(vs.visittime,'%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d') order by vr.createdate desc limit 1;";
        JsonData list = dbService.QueryJsonData(sql);
        return list;
    }

    public JsonData GetVisitorRecord(HttpServletRequest request, HttpServletResponse response, String idcard) {
        String sql = "SELECT vr.uid,vr.reason,vr.byvisitor,vr.plateno,vr.things,vi.mobile,vi.name,vi.uid vuid,vi.idcard,vi.sex,vi.nation," +
                " vi.address,vi.birthday,vi.photo,date_format(vr.createdate,'%Y-%m-%d %H:%i:%s') as createdate " +
                " FROM tb_visitor_subscribe vr inner join tb_visitor_visitorinfo vi on vi.uid=vr.visitorid " +
                " where vr.isleave=0 and vi.idcard='" + idcard + "' order by vr.createdate desc limit 1;";
        JsonData list = dbService.QueryJsonData(sql);
        return list;
    }

    public JsonResult SubmitVisiterLeave(HttpServletRequest request, HttpServletResponse response, final String recordid,String visitorid) {
        //String visitorid = userredis.get(request).getDocid();
        //获取当前的访客表单信息
        String sql = "select uid from tb_visitor_subscribe where uid = '" + recordid + "' and isleave=0";
        List<String> list = dbService.queryFields(sql, String.class);
        if (CollectionUtil.isEmpty(list)) {
            return Json.getJsonResult(false, "离场失败，当前时间您没有预约单！");
        }
        for (String formId : list) {
            String updateSQL2 = "UPDATE tb_visitor_record_namelist SET status=3,downnum=0 WHERE subscribeid='"+formId+"' and visitorid = '"+visitorid+"' and status in(1,4,5,6) ";
            dbService.excuteSql(updateSQL2);
            String updateSQL3 = "delete from tb_visitor_record_namelist where subscribeid = '"+formId+"' and visitorid='"+visitorid+"'  and status in(0,2) ";
            dbService.excuteSql(updateSQL3);
            String updateSQL4 = " update tb_visitor_subscribe set actual_departure_time = ?,isleave = 1 where uid=? ";
            dbService.excuteSql(updateSQL4,new Date(),formId);
            String updatePlateNo = "update tb_park_plateno set ishandle=3,handlenum=0 where infoid=? and ishandle in(1,4,5,6) ";
            dbService.excuteSql(updatePlateNo, visitorid);
            String delPlateNo = "delete from tb_park_plateno where infoid=? and ishandle in(0,2) ";
            dbService.excuteSql(delPlateNo, visitorid);
        }
        return Json.getJsonResult(true);
    }

    public JsonData getVisitorRecordNameList(HttpServletRequest request, HttpServletResponse response, String recordid) {
//        String sql = "SELECT visitorid FROM tb_visitor_record where uid='" + recordid + "';";
//        JSONArray list = dbService.QueryList(sql);
//        if (list.size() == 0) {
//            return Json.getJsonData(false, "来访记录不存在");
//        }
//        String visitorid = list.getJSONObject(0).getString("visitorid");
        String sql = "SELECT nl.uid,ac.devname,ac.machineid,dic.name as statusname,getareaname(ac.areacode) as areaname "
                + " FROM tb_visitor_record_namelist nl inner join tb_dev_accesscontroller ac on ac.uid=nl.devid "
                + " inner join tb_sys_dictionary dic on dic.code=cast(nl.status as char(10)) and dic.groupcode='SYS0000025' "
                + " where nl.subscribeid='" + recordid + "';";
        JsonData result = dbService.QueryJsonData(sql);
        return result;
    }

    public JsonData getByVisitorList(HttpServletRequest request, HttpServletResponse response, String key) {
        //是否指定人员审核，0-否，可查询所有人员；1-是，仅能查询指定的人员
        String sql = "";
        if ("0".equals(syscfg.get("designatedPerson"))) {
            sql = "SELECT ct.uid,ct.name,ct.mobile,getminiorgname(ct.orgcode) as orgname,1 as status " +
                    "FROM tb_card_teachstudinfo ct " +
                    "WHERE ct.status=1 and (ct.name like '%" + key + "%' or ct.mobile='" + key + "' or mobile like '%" + key + "') ";
            return dbService.QueryJsonData(sql);
        } else {
            sql = "SELECT ct.uid,ct.name,ct.mobile,getminiorgname(ct.orgcode) as orgname,1 as status " +
                    "FROM (SELECT infoid FROM tb_visitor_administrators GROUP BY infoid) va " +
                    "INNER JOIN tb_card_teachstudinfo ct ON ct.uid=va.infoid " +
                    "left join tb_face_infoface face on face.infoid=ct.uid " +
                    "WHERE ct.status=1 and (ct.name like '%" + key + "%' or ct.mobile='" + key + "' or mobile like '%" + key + "') ";
            JSONArray objects = dbService.QueryList(sql);
            sql =   " select ct.uid,CONCAT(ct.name, '(审核人)') AS name,ct.mobile,getminiorgname(ct.orgcode) as orgname,0 as status " +
                    " from tb_workflow_approvar wa " +
                    " join tb_workflow_node wn on wa.nodeid = wn.uid " +
                    " join tb_workflow_mould wm on wm.uid = wn.mouldid " +
                    " INNER JOIN tb_card_teachstudinfo ct ON ct.uid=wa.infoid " +
                    " where wa.status = 1 and wn.nodelevel = 1 and wm.mouldtype = 3 ";
            JSONArray objects1 = dbService.QueryList(sql);
            objects.addAll(objects1);
            JsonData jsonData = new JsonData();
            jsonData.setData(objects);
            jsonData.setSuccess(true);
            return jsonData;
        }
    }

    public JsonData getByVisitorWithMobileList(HttpServletRequest request, HttpServletResponse response, String key, String mobile) {
        if (StringUtils.isBlank(key) && StringUtils.isBlank(mobile)) {
            return Json.getJsonData(false, "请输入被访人姓名或手机号");
        }

        String sql = "";
        //是否指定人员审核，0-否，可查询所有人员；1-是，仅能查询指定的人员
        if ("0".equals(syscfg.get("designatedPerson"))) {
            sql = "SELECT info.uid,info.name,info.mobile,getminiorgname(info.orgcode) as orgname,face.imgpath FROM tb_card_teachstudinfo info left join tb_face_infoface face on face.infoid=info.uid WHERE info.status=1 ";
            if (!StringUtils.isBlank(key) && StringUtils.isBlank(mobile)) {
                sql += " and (name like '%" + key + "%')";
            } else if (StringUtils.isBlank(key) && !StringUtils.isBlank(mobile)) {
                sql += " and (mobile like '%" + mobile + "' or mobile='" + mobile + "')";
            } else if (!StringUtils.isBlank(key) && !StringUtils.isBlank(mobile)) {
                sql += " and (name like '%" + key + "%' or mobile like '%" + mobile + "' or mobile='" + mobile + "')";
            }
        } else {
            sql = "SELECT ct.uid,ct.name,ct.mobile,getminiorgname(ct.orgcode) as orgname,face.imgpath " +
                    "FROM (SELECT infoid FROM tb_visitor_administrators GROUP BY infoid) va " +
                    "INNER JOIN tb_card_teachstudinfo ct ON ct.uid=va.infoid " +
                    "left join tb_face_infoface face on face.infoid=ct.uid " +
                    "WHERE ct.status=1 ";
            if (!StringUtils.isBlank(key) && StringUtils.isBlank(mobile)) {
                sql += " and (ct.name like '%" + key + "%')";
            } else if (StringUtils.isBlank(key) && !StringUtils.isBlank(mobile)) {
                sql += " and (ct.mobile like '%" + mobile + "' or ct.mobile='" + mobile + "')";
            } else if (!StringUtils.isBlank(key) && !StringUtils.isBlank(mobile)) {
                sql += " and (ct.name like '%" + key + "%' or ct.mobile like '%" + mobile + "' or ct.mobile='" + mobile + "')";
            }
        }

        return dbService.QueryJsonData(sql);
    }

    //手机端提交预约信息
    public JsonResult SubmitSubVisitor(HttpServletRequest request, HttpServletResponse response, String byVisitorId, String visitTime, String leaveTime,String byVisitor,
                                       String reason, String plateNo, String visitMobile, String personNumDefault, String things, String otherVisitor) {
        SysUser user = userredis.get(request);
        String empId = user.getDocid();
        String name = user.getName();
        JSONArray otherVisitorList = JSONArray.parseArray(otherVisitor);

        String selInfoSQL = "SELECT idcard as idCard,company,name,sex,idType,mobile," +
                "department,position,plateno,uid " +
                " FROM tb_visitor_visitorinfo WHERE uid='" + empId + "'";
        VisitorInfo visitorInfo = dbService.queryForOne(selInfoSQL, VisitorInfo.class);
        if (visitorInfo == null) {
            return Json.getJsonResult(false, "访客信息异常");
        }
        //判断能否预约
        JsonResult isAllowSubscribe = isAllowSubscribe(byVisitorId, visitorInfo.getName(), visitMobile, empId, otherVisitorList, visitorInfo.getIdCard());
        if (!isAllowSubscribe.isSuccess()) {
            return isAllowSubscribe;
        }

        //添加人员信息
        //主访客uid
        String visitorInfoId = UUID.randomUUID().toString();
        String birthday = "";
        String idCard = visitorInfo.getIdCard();
        if (idCard.length() > 15) {
            birthday = idCard.substring(7, 15);
        }
        int personNum = 1;
        List<String> visitorList = new ArrayList<>();
        personNum = getPersonNumAndSaveInfo(visitorInfo.getName(), visitorInfo.getSex(), visitorInfo.getIdType(), idCard, visitorInfo.getMobile(), "", visitorInfo.getCompany(), visitorInfo.getDepartment(), visitorInfo.getPosition(), otherVisitor, plateNo, visitorInfoId, birthday, empId, personNum, visitorList);

        //3.创建预约信息tb_visitor_subscribe
        String visitorFromId = UUID.randomUUID().toString();
        //判断是否默认审核通过
        String isDefaultAudit = syscfg.get("visitormachineneedauth");
        String status = "0";
        if ("0".equals(isDefaultAudit)) {
            status="1";
        }
        //生成访客二维码（无卡号时按照十六进制）
        String qrcode = RandomHelper.CreateQRcode();
        //设置过期时间
        String qrCodeTimeOut = syscfg.get("qrcodetimeout");
        if (StringUtils.isBlank(qrCodeTimeOut)) {
            qrCodeTimeOut = "60";
        }
        if (StringUtils.isNotBlank(empId)) {
            visitorInfoId = empId;
        }
        //保存预约信息
        return saveVisitorSubscribe(request, byVisitorId, visitTime, leaveTime, byVisitor, reason, plateNo, things, empId, name, visitorInfoId, personNum, visitorList, visitorFromId, isDefaultAudit, status, qrcode, qrCodeTimeOut);
    }

    @Transactional(isolation = Isolation.READ_COMMITTED,rollbackFor = Exception.class)
    public JsonResult saveVisitorSubscribe(HttpServletRequest request, String byVisitorId, String visitTime, String leaveTime, String byVisitor, String reason, String plateNo, String things, String empId, String name, String visitorInfoId, int personNum, List<String> visitorList, String visitorFromId, String isDefaultAudit, String status, String qrcode, String qrCodeTimeOut) {
        String createVisitorSubscribe = "insert into tb_visitor_subscribe (uid,visitorid,byvisitor,byvisitorid,plateno,personnum,reason,things,visittime,leavetime,isinvited,"
                + "creatorid, createdate,status,isdefault,delay_count,isleave)values(?,?,?,?,?,?,?,?,?,?,0,?,now(),?,?,0,0)";
        dbService.excuteSql(createVisitorSubscribe, visitorFromId, visitorInfoId, byVisitor, byVisitorId, plateNo, personNum, reason, things, visitTime, leaveTime, empId, status, isDefaultAudit);
        //4.创建其他访客信息
        String createOtherVisitor = "insert into tb_visitor_subscribe_person (uid,visitor_subscribe_id,visitor_id,create_time,qrcode,qrcodetimeout,card_no) values(uuid(),'" + visitorFromId + "',?,now(),?,date_add(now(), interval " + qrCodeTimeOut + " second),CONCAT('1',LPAD(FLOOR(RAND() * 99999999), 7, '0')))";
        List<Object[]> args = new ArrayList<>();
        args.add(new Object[]{visitorInfoId, qrcode});
        if (!visitorList.isEmpty()) {
            for (String s : visitorList) {
                //生成访客二维码（无卡号时按照十六进制）
                qrcode = RandomHelper.CreateQRcode();
                args.add(new Object[]{s, qrcode});
            }
        }
        jdbcTemplate.batchUpdate(createOtherVisitor, args);
        //判断是否需要审核
        if ("1".equals(syscfg.get("visitormachineneedauth"))) {
            //获取工作流模板为请假的模板id
            String sqlMouldId = "SELECT uid FROM tb_workflow_mould WHERE mouldtype = 3 ";
            JSONArray ja = dbService.QueryList(sqlMouldId);
            String mouldId = ja.getJSONObject(0).getString("uid");

            if (ja.isEmpty()) {
                throw new RuntimeException("未配置审批模板");
            }

            List<WorkFlowNodeDto> workFlowNodeDTOs = workFlowUtil.selSubPerson(mouldId, byVisitorId, byVisitorId, "", "", 1);
            if (workFlowNodeDTOs.size() == 0) {
                throw new RuntimeException("未配置审核人，请联系管理员！");
            }
            //判断是否是审核人提交默认审核通过
            WorkFlowNodeDto nodeLevel = workFlowUtil.getNodeLevel(request, empId, 3);
            if (nodeLevel != null) {
                String updateSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate,auditdate,auditid,remark) values (uuid(),?,?,?,?,now(),now(),?,?)";
                dbService.excuteSql(updateSql, visitorFromId, mouldId, 4, nodeLevel.getNodeLevel(), empId, "审核人提交默认审核通过");
                if (nodeLevel.getIsFinal() == 1) {
                    String updateVisitorSubscribe = "update tb_visitor_subscribe set status = 1 where uid = ?";
                    dbService.excuteSql(updateVisitorSubscribe, visitorFromId);
                    return Json.getJsonResult(true, "提交成功");
                }
            }
            String insertRecordSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate) values (uuid(),?,?,?,1,now())";
            dbService.excuteSql(insertRecordSql, visitorFromId, mouldId, 0);
            handler.handleSuccess(visitorFromId, name, workFlowNodeDTOs, "", "");
            return Json.getJsonResult(true, "提交成功,请等待审核");
        } else {
            List<WorkFlowNodeDto> workFlowNodeDTOs = new ArrayList<>();
            handler.handleSuccess(visitorFromId, name, workFlowNodeDTOs, "-1", "");
            return Json.getJsonResult(true, "提交成功");
        }
    }

    private Integer getPersonNumAndSaveInfo(String name, String sex, String idType, String idCard, String mobile, String qrcode, String company, String department, String position, String otherVisitorVal, String plateNo, String visitorInfoId, String birthday, String empId, int personNum, List<String> otherVisitorList) {
        try {
            //判断是否有其他访客
            if (StringUtils.isNotBlank(otherVisitorVal)) {
                JSONArray otherVisitorArr = JSONArray.parseArray(otherVisitorVal);
                personNum += otherVisitorArr.size();
                if (!otherVisitorArr.isEmpty()) {
                    for (int i = 0; i < otherVisitorArr.size(); i++) {
                        //处理其他访客的身份证信息，并将该信息处理结果存入访客表中
                        JSONObject otherVisitors = otherVisitorArr.getJSONObject(i);
                        String otherName = otherVisitors.getString("name");
                        String otherIdCard = otherVisitors.getString("idcard");
                        String otherUid = UUID.randomUUID().toString();
                        String otherBirthday = "";
                        String otherSex = "男";
                        if (idCard.length() > 15) {
                            otherBirthday = otherIdCard.substring(6, 14);
                            int sexNum = Integer.parseInt(otherIdCard.substring(16, 17));
                            if (sexNum % 2 == 0) {
                                otherSex = "女";
                            } else {
                                otherSex = "男";
                            }
                        }
                        String selVisitorSQL = "SELECT uid FROM tb_visitor_visitorinfo WHERE idcard='" + otherIdCard + "'";
                        String otherVisitor = dbService.queryOneField(selVisitorSQL, String.class);
                        if (StringUtils.isNotBlank(otherVisitor)) {
                            otherVisitorList.add(otherVisitor);
                        } else {
                            String createOtherVisitor = "insert into tb_visitor_visitorinfo (uid,name,idtype,idcard,sex,birthday,"
                                    + "company,position,department,createdate,creatorid,status,plateno) values(?,?,?,?,?,?,?,?,?,now(),?,1,?)";
                            dbService.excuteSql(createOtherVisitor, otherUid, otherName, idType, otherIdCard, otherSex, otherBirthday, company, position, department, empId, plateNo);
                            otherVisitorList.add(otherUid);
                        }
                    }
                }
            }
            //1. 如果visitorid（访客uid）为空，先创建访客信息
            String sql = "SELECT uid FROM tb_visitor_visitorinfo WHERE idcard='" + idCard + "'";
            String uid = dbService.queryOneField(sql, String.class);

            if (StringUtils.isNotBlank(uid)) {
                String alterSQL = "UPDATE tb_visitor_visitorinfo SET name=?, idtype=?, idcard=?, sex=?, mobile=?, company=?, position=?, department=? WHERE uid = ?";
                dbService.excuteSql(alterSQL, name, idType, idCard, sex, mobile, company, position, department, uid);
            } else {
                String createVisitor = "insert into tb_visitor_visitorinfo (uid,name,idtype,idcard,sex,birthday,mobile,"
                        + "company,position,department,createdate,creatorid,status,plateno)values(?,?,?,?,?,?,?,?,?,?,now(),?,1,?)";
                dbService.excuteSql(createVisitor, visitorInfoId, name, idType, idCard, sex, birthday, mobile, company, position, department, empId, plateNo);
            }
            return personNum;
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return personNum;
    }


    //手机端提交判断能否预约
    public JsonResult isAllowSubscribe(String byvisitorid, String visitorname, String visitmobile, String visitorid, JSONArray othervisitorlist, String idcard) {
        if (StringUtils.isBlank(byvisitorid)) {
            return Json.getJsonResult(false, "被访人id为空");
        }

        if (StringUtils.isBlank(visitorname)) {
            return Json.getJsonResult(false, "来访人姓名为空");
        }
        if (StringUtils.isBlank(visitmobile)) {
            return Json.getJsonResult(false, "访客手机号为空");
        }

        int auditCount = dbService.getCount("tb_visitor_subscribe", "visitorid='" + visitorid + "' AND status=0");
        if (auditCount > 0) {
            return Json.getJsonResult(false, "已存在未审核预约记录，不可重复申请");
        }
        int leaveCount = dbService.getCount("tb_visitor_subscribe", "visitorid='" + visitorid + "' AND isleave=0");
        if (leaveCount > 0) {
            return Json.getJsonResult(false, "存在未离场来访记录，不可重复申请");
        }
        //判断访客是否在黑名单
        int blackcount = dbService.getCount("tb_visitor_black", "idcard='" + idcard + "'");
        if (blackcount > 0) {
            return Json.getJsonResult(false, "您已被拉黑,不可预约!");
        }

        //判断随访人员是否在黑名单
        if (othervisitorlist!=null) {
            for (int i = 0; i < othervisitorlist.size(); i++) {
                String otherIdcard = othervisitorlist.getJSONObject(i).getString("idcard");
                String othername = othervisitorlist.getJSONObject(i).getString("name");
                int otherBlackcount = dbService.getCount("tb_visitor_black", "idcard='" + otherIdcard + "'");
                if (otherBlackcount > 0) {
                    return Json.getJsonResult(false, "随访人员" + othername + "已被拉黑,不可预约!");
                }
            }
        }
        return Json.getJsonResult(true);
    }

    public JsonData getMyVisitList(HttpServletRequest request, HttpServletResponse response, String uid, String starttime, String endtime, int start, int limit, boolean isDetail) {
        String fields = " select distinct vs.uid, vs.visitorid,vs.areaposition, vm.name as areapositionname, vv.name, vv.sex, vv.mobile, vv.company, vv.position, vv.department, vs.reason, date_format(vs.visittime, '%Y-%m-%d %H:%i:%s') as visittime, date_format(vs.leavetime, '%Y-%m-%d %H:%i:%s') as leavetime,vs.isleave,vs.byvisitor,vs.status,date_format(vs.createdate, '%Y-%m-%d %H:%i:%s') as createdate," +
                "vs.createdate,vv.photo,vs.plateno,vs.personnum,vs.things,vm.imagefile ";
        String table = " from tb_visitor_subscribe vs " +
                "left join tb_visitor_subscribe_person vsp on vsp.visitor_subscribe_id = vs.uid " +
                "LEFT JOIN tb_visitor_visitorinfo vv ON vv.uid = vs.visitorid " +
                "LEFT JOIN tb_visitor_machine vm ON vm.position = vs.areaposition";
        String where = " where 1=1 ";
        if (userredis.get(request).getUsertype() == 4) {
            fields += ",4 as usertype";
            where += " and vsp.visitor_id='" + userredis.get(request).getDocid() + "' ";
        }
        if (StringUtils.isNotBlank(uid)) {
            where += " and vs.uid='" + uid + "'";
        }
        if (!StringUtils.isBlank(starttime) && !StringUtils.isBlank(endtime)) {
            where += " and (vs.visittime between '" + starttime + " 00:00:00' and '" + endtime + " 23:59:59') ";
        }
        where += " order by vs.createdate desc ";
        if (!isDetail) {
            where += "limit " + start + "," + limit + "";
            JsonData result = dbService.QueryJsonData(fields+table+where);
            return result;
        } else {
            String selVisitor = "SELECT visitor_id FROM tb_visitor_subscribe_person WHERE visitor_subscribe_id='" + uid + "'";
            List<String> queryFields = dbService.queryFields(selVisitor, String.class);
            StringJoiner joiner = new StringJoiner("','", "('", "')");
            for (String queryField : queryFields) {
                joiner.add(queryField);
            }
            JSONObject jo = new JSONObject();
            if (CollectionUtil.isNotEmpty(queryFields)) {
//                String selOther = "SELECT uid as visitorid,name FROM tb_visitor_visitorinfo WHERE uid in " + joiner;
                String fieldsOtherSQL = "uid as visitorid,name";
                String tableOtherSQL = " tb_visitor_visitorinfo";
                String whereOtherSQL = " uid in " + joiner;
                JSONArray otherja = dbService.QueryJSONArray(fieldsOtherSQL, tableOtherSQL, whereOtherSQL,"",0,0);
                jo.put("othervisitor", otherja);
            }
            JsonData result = dbService.QueryJsonData(fields+table+where);
            JsonData jd = new JsonData();
            if (!jo.isEmpty()) {
                JSONArray data = result.getData();
                data.getJSONObject(0).putAll(jo);
                jd.setData(data);
                jd.setSuccess(true);
            }
            return jd;
        }
    }

    //取消访客预约
    public JsonResult cancelVisitSub(HttpServletRequest request, HttpServletResponse response, String uid) {
        int count = dbService.getCount("tb_workflow_approval_record", "formid = '" + uid + "' and auditcondition  in (1,4)");
        if (count > 0) {
            return Json.getJsonResult(false, "该预约已经审核，不能取消");
        }
        String updateSQL = "delete from tb_visitor_subscribe WHERE uid=?";
        int i = dbService.excuteSql(updateSQL, uid);
        if (i != -1) {
            return Json.getJsonResult(true);
        }
        return Json.getJsonResult(false);
    }



    //创建二维码，每次点开都会刷新
    public JsonResult CreateVisitorQRCode(HttpServletRequest request) throws Exception {
        SysUser user = userredis.get(request);
        String infoId = user.getDocid();
        String selQrcodeSQL = "select qrcode,vsp.uid from tb_visitor_subscribe vs " +
                "join tb_visitor_subscribe_person vsp on vs.uid = vsp.visitor_subscribe_id " +
                " where visitor_id='" + infoId + "' and vs.status=1 and vs.isleave = 0 and leavetime >= now() and visittime <= now()  limit 1";
        JSONObject jsonObject = dbService.QueryJSONObject(selQrcodeSQL);
        if (jsonObject == null) {
            return Json.getJsonResult(false, "未到预约时间,生成二维码失败");
        }
        String qrcode = jsonObject.getString("qrcode");
        String uid = jsonObject.getString("uid");
        String qrcodetimeout = syscfg.get("qrcodetimeout");
        if (StringUtils.isBlank(qrcodetimeout)) {
            qrcodetimeout = "60";
        }
        JSONObject data = new JSONObject();
        String sql = "select uid from tb_visitor_visitorinfo where uid='" + infoId + "' and status=1 ";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            return Json.getJsonResult(false, "访客信息不存在或无效");
        }
        data = list.getJSONObject(0);
        String timeout = DateHelper.format(DateHelper.addSeconds(new Date(), Integer.parseInt(qrcodetimeout)));
        sql = "update tb_visitor_subscribe_person set qrcodetimeout=?  where visitor_id=? and uid = ?";
        dbService.excuteSql(sql, timeout, infoId,uid);

        byte[] qrcodebyte = QRcodeUtils.createQrCodeByte(qrcode, 800, "png");
        data.put("qrcode", FileHelper.ImageByte2Base64(qrcodebyte));
        data.put("qrcodetimeout", timeout);
        return Json.getJsonResult(true, data);
    }

    //修改访客离场时间
    public JsonResult editleavetime(HttpServletRequest request, HttpServletResponse response, String uid, String leavetime) {
        String updateSQL = "UPDATE tb_visitor_subscribe SET leavetime=? WHERE uid=?";
        int result = dbService.excuteSql(updateSQL, leavetime, uid);
        if (result == -1) {
            return Json.getJsonResult(false, "修改失败");
        }
        return Json.getJsonResult(true);
    }

    //访客离场
//    @Transactional
    public JsonResult visitorleave(HttpServletRequest request, HttpServletResponse response, String uid, String visitorid) {
        if (StringUtils.isBlank(visitorid)) {
            visitorid = userredis.get(request).getDocid();
        }

//        String updateSubSQL = "UPDATE tb_visitor_subscribe_person SET card_no=null,qrcode=null WHERE uid=? and visitor_subscribe_id = ?";
//        int count = dbService.excuteSql(updateSubSQL, visitorid, subscribeid);
//
//        if (count == 0) {
//            return Json.getJsonResult(false, "离场失败！");
//        }
        //获取当前的访客表单信息
        String sql = "select uid from tb_visitor_subscribe where visitorid = '" + visitorid + "'";
        List<String> list = dbService.queryFields(sql, String.class);
        if (CollectionUtil.isEmpty(list)) {
            return Json.getJsonResult(false, "离场失败，当前时间您没有预约单！");
        }
        for (String formId : list) {
            String updateSQL2 = "UPDATE tb_visitor_record_namelist SET status=3,downnum=0 WHERE subscribeid='"+formId+"' and visitorid = '"+visitorid+"' and status in(1,4,5,6) ";
            dbService.excuteSql(updateSQL2);
            String updateSQL3 = "delete from tb_visitor_record_namelist where subscribeid = '"+formId+"' and visitorid='"+visitorid+"'  and status in(0,2) ";
            dbService.excuteSql(updateSQL3);
            String updateSQL4 = " update tb_visitor_subscribe set actual_departure_time = ?,isleave = 1 where uid=? ";
            dbService.excuteSql(updateSQL4,new Date(),formId);
            String updatePlateNo = "update tb_park_plateno set ishandle=3,handlenum=0 where infoid=? and ishandle in(1,4,5,6) ";
            dbService.excuteSql(updatePlateNo, visitorid);
            String delPlateNo = "delete from tb_park_plateno where infoid=? and ishandle in(0,2) ";
            dbService.excuteSql(delPlateNo, visitorid);
        }
        return Json.getJsonResult(true);
    }

    /**
     * 获取待审访客预约记录(企业微信)
     *
     * @param request
     * @param response
     * @param uid
     * @param starttime
     * @param endtime
     * @param start
     * @param limit
     * @return
     */
    public JsonData getAuditVisitorSubscribeList(HttpServletRequest request, HttpServletResponse response, String uid, String auditstatus, String starttime, String endtime, int start, int limit) {
        String fields = "vs.uid, vs.visitorid, vv.name, vv.sex, vv.mobile, vv.company, vv.position, vv.department, vs.reason, date_format(vs.visittime, '%Y-%m-%d %H:%i:%s') as visittime, date_format(vs.leavetime, '%Y-%m-%d %H:%i:%s') as leavetime,vs.byvisitor,vs.byagree,vs.auditstatus,vs.remark,vs.status,vr.isleave,date_format(vs.createdate, '%Y-%m-%d %H:%i:%s') as createdate,vv.photo  ";
        String table = "tb_visitor_subscribe vs " +
                "LEFT JOIN tb_visitor_visitorinfo vv ON vv.uid = vs.visitorid " +
                "LEFT JOIN tb_visitor_record vr ON vr.subscribeid=vs.uid ";
        String where = " vs.auditstatus=" + auditstatus + " and vs.byvisitorid='" + userredis.get(request).getDocid() + "' ";
        if (StringUtils.isNotBlank(uid)) {
            where += " and vs.uid='" + uid + "'";
        }
        if (!StringUtils.isBlank(starttime) && !StringUtils.isBlank(endtime)) {
            where += " and (vs.visittime between '" + starttime + "' and '" + endtime + "') ";
        }
        String orderby = "vs.visittime desc";
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    /**
     * 是否上传照片
     *
     * @param request
     * @return
     */
    public boolean ExistsVisitorFace(HttpServletRequest request) {
        SysUser user = userredis.get(request);
        int c = dbService.getCount("tb_visitor_visitorinfo", " uid='" + user.getDocid() + "' and ifnull(photo,'')<>'' ");
        if (c > 0) {
            return true;
        }
        return false;
    }

    /**
     * 获取访客信息
     *
     * @param request
     * @return
     */
    public JSONObject GetVisitorInfo(HttpServletRequest request) {
        SysUser user = userredis.get(request);
        String sql = "SELECT v.uid,v.name,v.idcard,v.mobile,v.plateno,d.name as idtypename,v.company,v.position,v.department FROM tb_visitor_visitorinfo v left join tb_sys_dictionary d on d.groupcode='SYS0000044' and v.idtype=d.code where v.uid='" + user.getDocid() + "' ";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            return null;
        } else {
            return list.getJSONObject(0);
        }
    }

    /**
     * 获取被访人信息
     *
     * @param request
     * @return
     */
    public JSONObject GetByVisitorInfo(HttpServletRequest request, String byvisitorid) {
        JSONObject jo = new JSONObject();
        jo.put("uid", byvisitorid);
        redispublish.Publish("/campus/visitorsubmsg", jo.toJSONString());

        String sql = "SELECT uid,name,getminiorgname(orgcode) as orgname FROM tb_card_teachstudinfo where uid='" + byvisitorid + "' ";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            return null;
        } else {
            return list.getJSONObject(0);
        }
    }

    public JsonResult getUnauditedAndUnLeaveRecord(HttpServletRequest request) {
        String visitorid = userredis.get(request).getDocid();
        int unauditedCount = dbService.getCount("tb_visitor_subscribe", "visitorid='" + visitorid + "' AND status = 0 ");
        if (unauditedCount > 0) {
            return Json.getJsonResult(false, "unaudited");
        }
        int leaveCount = dbService.getCount("tb_visitor_subscribe", "visitorid='" + visitorid + "' AND isleave = 0 ");
        if (leaveCount > 0) {
            return Json.getJsonResult(false, "unleave");
        }
        return Json.getJsonResult(true);
    }

    //更新访客公司信息
    public JsonResult UpdatevVsitorCompany(HttpServletRequest request, HttpServletResponse response, String company, String department, String position) {
        SysUser user = userredis.get(request);
        String sql = "update tb_visitor_visitorinfo set company=?,department=?,position=? where uid=?";
        dbService.excuteSql(sql, company, department, position, user.getDocid());
        return Json.getJsonResult(true);
    }

    //获取可通行的区域
    public JSONArray GetVisitArea(HttpServletRequest request, HttpServletResponse response) {
        String sql = "SELECT position as value,name as title FROM tb_visitor_machine ";
        JSONArray ja = dbService.QueryList(sql);
        JSONObject jo = new JSONObject();
        jo.put("value", "-1");
        jo.put("title", "全部");
        ja.add(0, jo);
        return ja;
    }

    public JsonData getVisitorFormList(HttpServletRequest request, HttpServletResponse response, String uid) {
        String sql = "select distinct vv.photo,vv.name,vs.areapositionName,vv.uid,sp.visitor_subscribe_id as visitorFormId,card_no as cardNo " +
                " from tb_visitor_subscribe_person sp " +
                " join tb_visitor_visitorinfo vv on vv.uid = sp.visitor_id " +
                " join tb_visitor_subscribe vs on vs.uid = sp.visitor_subscribe_id " +
                " where sp.visitor_subscribe_id = '" + uid + "' ";
        JsonData jsonData = dbService.QueryJsonData(sql);
        return jsonData;
    }

    @Transactional
    public JsonResult addVisitorCard(HttpServletRequest request, HttpServletResponse response, String cardNo, String infoId,String visitorFormId) {
        String sql = "update tb_visitor_subscribe_person set card_no=?,qrcode=? where visitor_subscribe_id=? and visitor_id=?";
        dbService.excuteSql(sql, cardNo,cardNo, visitorFormId, infoId);
        String updateSQL = "update tb_visitor_record_namelist set status=5,downnum=0 where visitorid='" + infoId + "' and subscribeid = '" + visitorFormId + "'";
        dbService.excuteSql(updateSQL);
        return Json.getJsonResult(true);
    }

    //获取访客信息
    public JsonData printQrcode(HttpServletRequest request, HttpServletResponse response, String recordid, String visitorId) {
        String sql = "select vsp.visitor_subscribe_id,vs.uid,vv.name,getminiorgname(ct.orgcode) orgname,vsp.qrcode,vs.byvisitor,date_format(vs.leavetime,'%Y-%m-%d %H:%i:%s') as leavetime " +
                " from tb_visitor_subscribe vs " +
                " join tb_visitor_subscribe_person vsp on vs.uid = vsp.visitor_subscribe_id " +
                " join tb_visitor_visitorinfo vv on vv.uid = vsp.visitor_id " +
                " join tb_card_teachstudinfo ct on ct.uid = vs.byvisitorid " +
                " where vsp.visitor_subscribe_id = '" + recordid + "' and vsp.visitor_id = '" + visitorId + "' ";
        JsonData jsonData = dbService.QueryJsonData(sql);
        return jsonData;
    }

    public JsonData getVisitorRecordList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit, String startTime, String endTime) {
//        String position = request.getHeader("position");
        String fields = "vs.uid,vs.visitorid,vi.name,vm.position,vm.uid as machineUid,vm.name as machineName,vi.sex,vi.mobile,vs.status,vs.things,vi.photo,vs.reason,ct.name as byvisitor,getminiorgname(ct.orgcode) as orgname,vs.plateno,vs.things,vs.isleave,date_format(vs.visittime,'%Y-%m-%d %H:%i:%s') as visittime, date_format(vs.leavetime,'%Y-%m-%d %H:%i:%s') as leavetime,date_format(vs.actual_departure_time,'%Y-%m-%d %H:%i:%s') as departuretime,date_format(vs.createdate,'%Y-%m-%d %H:%i:%s') as createdate";
        String table = "tb_visitor_subscribe vs " +
                "LEFT JOIN tb_card_teachstudinfo ct ON ct.uid=vs.byvisitorid " +
                "left join tb_visitor_machine vm on vm.position = vs.areaposition " +
                "inner join tb_visitor_visitorinfo vi on vi.uid=vs.visitorid ";
        String where = "1=1 ";
        if (userredis.get(request).getUsertype()==0){
            table+=" inner join tb_card_orgframework_user ou on ct.orgcode = ou.orgcode and ou.userid = '"+userredis.get(request).getUid()+"' ";
        }
//        if (!syscfg.get("visitormachineneedauth").equals("1")) {
//            where = " AND vs.areaposition='" + position + "' ";
//        }

        String orderby = "vs.createdate desc";
        if(syscfg.get("visitorHelpOneself").equals("1") && StringUtils.isBlank(key)){
            JsonData result = new JsonData();
            result.setSuccess(true);
            return result;
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (vi.name like '%" + key + "%' or vi.idcard='"+key+"' or vi.mobile='"+key+"')";
        }
        if (!StringUtils.isBlank(startTime) && !StringUtils.isBlank(endTime)) {
            where += " and (vs.createdate between '" + startTime + "' and '" + endTime + "') ";
        }
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData getVisitorRecordOne(HttpServletRequest request, HttpServletResponse response,String uid) {
        String fields = "vs.uid,vs.visitorid,vi.name,vs.areaposition,vs.areapositionName ,vi.sex,vi.mobile,vs.status,vs.things,vi.photo,vs.reason,ct.name as byvisitor,getminiorgname(ct.orgcode) as orgname,vs.plateno,vs.things,vs.isleave,date_format(vs.visittime,'%Y-%m-%d %H:%i:%s') as visittime, date_format(vs.leavetime,'%Y-%m-%d %H:%i:%s') as leavetime,date_format(vs.actual_departure_time,'%Y-%m-%d %H:%i:%s') as departuretime,date_format(vs.createdate,'%Y-%m-%d %H:%i:%s') as createdate";
        String table = "tb_visitor_subscribe vs " +
                "LEFT JOIN tb_card_teachstudinfo ct ON ct.uid=vs.byvisitorid " +
                //"left join tb_visitor_machine vm on vm.position = vs.areaposition " +
                "inner join tb_visitor_visitorinfo vi on vi.uid=vs.visitorid";
        String where = "1=1 ";
        String orderby = "vs.createdate desc";
        where+=" and vs.uid='"+uid+"'";
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby);
        return result;
    }

    public JsonData getIdentityVisitorRecordList(HttpServletRequest request, HttpServletResponse response, String idCard) {
//        String position = request.getHeader("position");
        String fields = "vs.uid as visitorFormId,vi.uid,vs.visitorid,vi.name, " +
                "vs.areapositionName as machineName,vi.sex,vi.mobile,vs.status,vs.things,vi.photo,vs.reason,vs.personnum,ct.name as byVisitor," +
                "getminiorgname(ct.orgcode) as orgname,vs.plateno,vs.things,vs.isleave,date_format(vs.visittime,'%Y-%m-%d %H:%i:%s') as visittime, " +
                "date_format(vs.leavetime,'%Y-%m-%d %H:%i:%s') as leavetime,date_format(vs.createdate,'%Y-%m-%d %H:%i:%s') as createdate,vi.department," +
                "sp.card_no as cardNo";
        String table = "tb_visitor_subscribe vs " +
                " join tb_visitor_subscribe_person sp on vs.uid = sp.visitor_subscribe_id " +
                "LEFT JOIN tb_card_teachstudinfo ct ON ct.uid=vs.byvisitorid " +
                //"left join tb_visitor_machine vm on vm.position = vs.areaposition " +
                "inner join tb_visitor_visitorinfo vi on vi.uid=vs.visitorid";
        String where = "vi.idcard = '" + idCard + "' and vs.isleave = 0";
        String orderby = "vs.createdate desc";
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, 0, 0);
        return result;
    }

    public JsonData getAlleyArea(HttpServletRequest request, HttpServletResponse response, String recordid, String visitorId) {
        String sql = "select name as positionName,position" +
                " from tb_visitor_machine";
        JsonData jsonData = dbService.QueryJsonData(sql);
        return jsonData;
    }

    public JsonData getUserAlleyArea(HttpServletRequest request, HttpServletResponse response) {
        SysUser sysUser = userredis.get(request);
        String uid = sysUser.getSysuserid();
        String sql = "select name as positionName,position" +
                " from tb_visitor_machine where position in (SELECT position FROM `tb_visitor_machine_device` vmd join tb_dev_areaframework_user dau on vmd.areacode=dau.areacode and dau.userid ='" + uid + "' GROUP BY position)";
        JsonData jsonData = dbService.QueryJsonData(sql);
        return jsonData;
    }

    /**
     *更新区域
     */
    @Transactional
    public JsonResult updateAreaList(HttpServletRequest request, HttpServletResponse response, String visitorFormId, String position) {
        String sql = "update tb_visitor_subscribe set areaposition=? where uid=?";
        dbService.excuteSql(sql, position, visitorFormId);
        String updateSQL = "update tb_visitor_record_namelist set status=5,downnum=0 where subscribeid = '" + visitorFormId + "'";
        dbService.excuteSql(updateSQL);
        return Json.getJsonResult(true);
    }

    /**
     *更新区域
     */
    @Transactional
    public JsonResult updateAreaLists(HttpServletRequest request, HttpServletResponse response, String visitorFormId, String positions,String positionname) {
        String sql = "update tb_visitor_subscribe set areaposition=?,areapositionName=? where uid=?";
        dbService.excuteSql(sql, positions,positionname, visitorFormId);
        String updateSQL = "update tb_visitor_record_namelist set status=5,downnum=0 where subscribeid = '" + visitorFormId + "'";
        dbService.excuteSql(updateSQL);
        return Json.getJsonResult(true);
    }

    public JsonData getApprovalRecord(HttpServletRequest request, String formid, int start, int limit) {
        String fields = "ct.name, date_format(war.createdate, '%Y-%m-%d %H:%i') as createdate,war.auditcondition,war.remark ";
        String table = "tb_workflow_approval_record as war " +
                "left join tb_workflow_mould wm on wm.uid = war.mouldid " +
                "LEFT JOIN tb_card_teachstudinfo as ct on ct.uid = war.auditid";
        String where = "formid = '" + formid + "' and war.auditcondition in ('1','4') and mouldtype = 3";
        return dbService.QueryJsonData(fields, table, where, "createdate DESC", start, limit);
    }

    public JsonData getVisitorApprovalList(HttpServletRequest request, HttpServletResponse response, Integer mouldType,String uid, String starttime, String endtime, int start, int limit, boolean b) {
        String docId = userredis.get(request).getDocid();
        WorkFlowNodeDto nodeLevel = workFlowUtil.getNodeLevel(request, docId, mouldType);
        if (nodeLevel == null) {
            return Json.getJsonData(false, "该用户没有审批权限");
        }
        String fields = "vs.uid, vs.visitorid,vs.areaposition, vv.name, vv.sex, vv.mobile," +
                "date_format(vs.visittime, '%Y-%m-%d %H:%i:%s') as visittime,vs.byvisitor," +
                "date_format(vs.createdate, '%Y-%m-%d %H:%i:%s') as createdate,vs.status";
        String table = "tb_visitor_subscribe vs " +
                "left join tb_workflow_approval_record war on war.formid = vs.uid and war.mouldid = '" + nodeLevel.getMouldId() + "'" +
                "LEFT JOIN tb_visitor_visitorinfo vv ON vv.uid = vs.visitorid ";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(uid)) {
            where += " and vs.uid='" + uid + "'";
        }
        if (!StringUtils.isBlank(starttime) && !StringUtils.isBlank(endtime)) {
            where += " and (vs.visittime between '" + starttime + " 00:00:00' and '" + endtime + " 23:59:59') ";
        }
        String recordsWhereSQL = workFlowUtil.getAuditRecordsWhereSQL(request, mouldType, nodeLevel,"vs.byvisitorid","vs.byvisitorid");
        if (StringUtils.isNotBlank(recordsWhereSQL)) {
            where += recordsWhereSQL;
        }
        String orderby = "vs.createdate desc";
        if (!b) {
            JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
            return result;
        } else {
            String selVisitor = "SELECT visitor_id FROM tb_visitor_subscribe_person WHERE visitor_subscribe_id='" + uid + "'";
            List<String> queryFields = dbService.queryFields(selVisitor, String.class);
            StringJoiner joiner = new StringJoiner("','", "('", "')");
            for (String queryField : queryFields) {
                joiner.add(queryField);
            }
            JSONObject jo = new JSONObject();
            if (CollectionUtil.isNotEmpty(queryFields)) {
                String selOther = "SELECT uid as visitorid,name FROM tb_visitor_visitorinfo WHERE uid in " + joiner;
                JSONArray otherja = dbService.QueryList(selOther);
                jo.put("othervisitor", otherja);
            }
            JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
            JsonData jd = new JsonData();
            if (!jo.isEmpty()) {
                JSONArray data = result.getData();
                data.getJSONObject(0).putAll(jo);
                jd.setData(data);
                jd.setSuccess(true);
            }
            return jd;
        }
    }

    public JsonData getVisitorApprovalList(HttpServletRequest request, HttpServletResponse response, Integer mouldType,String uid, int start, int limit, boolean b) {
        String docId = userredis.get(request).getDocid();
        WorkFlowNodeDto nodeLevel = workFlowUtil.getNodeLevel(request, docId, mouldType);
        if (nodeLevel == null) {
            return Json.getJsonData(false, "该用户没有审批权限");
        }
        String fields = "vs.uid, vs.visitorid,vs.areaposition, vm.name as areapositionname, vv.name, vv.sex, vv.mobile," +
                " vv.company, vv.position, vv.department, vs.reason, " +
                "date_format(vs.visittime, '%Y-%m-%d %H:%i:%s') as visittime," +
                " date_format(vs.leavetime, '%Y-%m-%d %H:%i:%s') as leavetime,vs.status, " +
                "vs.isleave,vs.byvisitor,vs.delay_status,date_format(vs.createdate, '%Y-%m-%d %H:%i:%s') as createdate," +
                "vv.photo,vs.plateno,vs.personnum,vs.things,date_format(vs.delay_time, '%Y-%m-%d %H:%i:%s') as delayTime,vs.remark";
        String table = "tb_visitor_subscribe vs " +
                "left join tb_workflow_approval_record war on war.formid = vs.uid and war.mouldid = '" + nodeLevel.getMouldId() + "'" +
                "left join tb_visitor_subscribe_person vsp on vsp.visitor_subscribe_id = vs.uid " +
                "LEFT JOIN tb_visitor_visitorinfo vv ON vv.uid = vsp.visitor_id " +
                "LEFT JOIN tb_visitor_machine vm ON vm.position = vs.areaposition";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(uid)) {
            where += " and vs.uid='" + uid + "'";
        }
        String recordsWhereSQL = workFlowUtil.getAuditRecordsWhereSQL(request, mouldType, nodeLevel,"vs.byvisitorid","vs.byvisitorid");
        if (StringUtils.isNotBlank(recordsWhereSQL)) {
            where += recordsWhereSQL;
        }
        String orderby = "vs.createdate desc";
        if (!b) {
            JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
            return result;
        } else {
            String selVisitor = "SELECT visitor_id FROM tb_visitor_subscribe_person WHERE visitor_subscribe_id='" + uid + "'";
            List<String> queryFields = dbService.queryFields(selVisitor, String.class);
            StringJoiner joiner = new StringJoiner("','", "('", "')");
            for (String queryField : queryFields) {
                joiner.add(queryField);
            }
            JSONObject jo = new JSONObject();
            if (CollectionUtil.isNotEmpty(queryFields)) {
                String selOther = "SELECT uid as visitorid,name FROM tb_visitor_visitorinfo WHERE uid in " + joiner;
                JSONArray otherja = dbService.QueryList(selOther);
                jo.put("othervisitor", otherja);
            }
            JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
            JsonData jd = new JsonData();
            if (!jo.isEmpty()) {
                JSONArray data = result.getData();
                data.getJSONObject(0).putAll(jo);
                jd.setData(data);
                jd.setSuccess(true);
            }
            return jd;
        }
    }

    public Integer isReviewed(String formId, String docId) {
        int count = dbService.getCount("tb_workflow_approval_record", "formid = '" + formId + "' and auditcondition in (1,4) and auditid = '" + docId + "'");
        if (count > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     *审核表单
     */
    public JsonResult auditApply(HttpServletRequest request, String uid, Integer status, String remark, String areaPosition, AuditApplyRequest requestBody, String day) throws Exception {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "当前访客申请被取消！");
        }
        if (StringUtils.isBlank(areaPosition)) {
            return Json.getJsonResult(false, "请选择通行的区域位置！");
        }
        String table = "tb_visitor_subscribe";
        String field = "";
        //默认先修改
        String sql = "update tb_visitor_subscribe set areaposition = ? where uid = ?";
        dbService.excuteSql(sql, areaPosition, uid);
        String userId = userredis.get(request).getDocid();
        String name = userredis.get(request).getName();
        return workFlowUtil.submitForApproval(request, uid,userId,name, status, remark, table, field,areaPosition,day,requestBody);
    }

    /**
     * 获取访客区域
     * @return
     */
    public JsonData getVisitorPosition(HttpServletRequest request) {
        String SQL = "select position,name from tb_visitor_machine";
        if ("1".equals(syscfg.get("positionPerson"))) {
            SQL += " where position in (select areaposition from tb_visitor_administrators where infoid = '" + userredis.get(request).getDocid() + "')";
        }
        JsonData jsonData = dbService.QueryJsonData(SQL);
        return jsonData;
    }

    // 添加获取通道关联设备的接口
    public JsonData getChannelDevices(String position) {
        String SQL = "SELECT devid, devclass, devname,doorid doorNo,relays,doorname doorName" +
                " FROM tb_visitor_machine_device md " +
                " join tb_dev_accesscontroller da on da.uid = md.devid " +
                " WHERE position = '"+position+"'";
        JsonData jsonData = dbService.QueryJsonData(SQL);
        return jsonData;
    }

    // 获取梯控楼层信息
    public JsonData getElevatorFloors(HttpServletRequest request,String deviceId,String infoType) {

        //是否开启梯控权限过滤
        JsonData jsonData = new JsonData();
        if ("1".equals(syscfg.get("elevatorFloors"))&&StringUtils.isBlank(infoType)) {
            String docId = userredis.get(request).getDocid();
            String sql = "SELECT code as id, name " +
                    " FROM tb_elevator_devicestorey " +
                    " WHERE devid = '" + deviceId + "' " +
                    " order by code desc";
            List<Elevator> elevators = dbService.queryList(sql, Elevator.class);
            // 假设 Elevator 有 getId() 和 getName() 方法
            Map<Integer, String> floorNameMap = elevators.stream().collect(Collectors.toMap(
                    Elevator::getId, // key
                    Elevator::getName                 // value
            ));
            //判断人员楼层权限
            String SQL = "SELECT storeys " +
                    " FROM tb_elevator_t_authrize " +
                    " WHERE devid = '" + deviceId + "' " +
                    " and infoid = '" + docId + "'";
            String storeys = dbService.queryOneField(SQL, String.class);
            if (StringUtils.isNotBlank(storeys)) {
                List<String> floorList = Arrays.stream(storeys.split(",")).collect(Collectors.toList());
                JSONArray jsonArray = new JSONArray();
                int i = 0;
                for (String floor : floorList) {
                    i++;
                    if ("1".equals(floor)) {
                        JSONObject object = new JSONObject();
                        object.put("id", i);
                        object.put("name", floorNameMap.get(i));
                        jsonArray.add(object);
                    }
                }
                jsonData.setData(jsonArray);
            }
            jsonData.setSuccess(true);
        } else {
            String SQL = "SELECT code as id, name " +
                    " FROM tb_elevator_devicestorey " +
                    " WHERE devid = '" + deviceId + "' " +
                    " order by code desc";
            jsonData = dbService.QueryJsonData(SQL);
        }
        return jsonData;
    }

    public JsonData getDelayedDeparture(HttpServletRequest request, HttpServletResponse response, String uid, int start, int limit, boolean b) {
        String docId = userredis.get(request).getDocid();
        WorkFlowNodeDto nodeLevel = workFlowUtil.getNodeLevel(request, docId, 3);
        if (nodeLevel == null) {
            return Json.getJsonData(false, "该用户没有审批权限");
        }
        String fields = "vs.uid, vs.visitorid,vs.areaposition, vm.name as areapositionname, vv.name, vv.sex, vv.mobile, vv.company, vv.position, vv.department, vs.reason, date_format(vs.visittime, '%Y-%m-%d %H:%i:%s') as visittime, date_format(vs.leavetime, '%Y-%m-%d %H:%i:%s') as leavetime,vs.isleave,vs.byvisitor,vs.status,date_format(vs.createdate, '%Y-%m-%d %H:%i:%s') as createdate,vv.photo,vs.plateno,vs.personnum,vs.things";
        String table = "tb_visitor_subscribe vs " +
                "left join tb_workflow_approval_record war on war.formid = vs.uid " +
                "left join tb_visitor_subscribe_person vsp on vsp.visitor_subscribe_id = vs.uid " +
                "LEFT JOIN tb_visitor_visitorinfo vv ON vv.uid = vsp.visitor_id " +
                "LEFT JOIN tb_visitor_machine vm ON vm.position = vs.areaposition";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(uid)) {
            where += " and vs.uid='" + uid + "'";
        }
        LocalDateTime dateTime = LocalDateTime.now();
        String time = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        where += " and (vs.visittime between '" + time + " 00:00:00' and '" + time + " 23:59:59') ";
        String recordsWhereSQL = workFlowUtil.getAuditRecordsWhereSQL(request, 3, nodeLevel,"vs.byvisitorid","vs.byvisitorid");
        if (StringUtils.isNotBlank(recordsWhereSQL)) {
            where += recordsWhereSQL;
        }
        String orderby = "vs.createdate desc";
        if (!b) {
            JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
            return result;
        } else {
            String selVisitor = "SELECT visitor_id FROM tb_visitor_subscribe_person WHERE visitor_subscribe_id='" + uid + "'";
            List<String> queryFields = dbService.queryFields(selVisitor, String.class);
            StringJoiner joiner = new StringJoiner("','", "('", "')");
            for (String queryField : queryFields) {
                joiner.add(queryField);
            }
            JSONObject jo = new JSONObject();
            if (CollectionUtil.isNotEmpty(queryFields)) {
                String selOther = "SELECT uid as visitorid,name FROM tb_visitor_visitorinfo WHERE uid in " + joiner;
                JSONArray otherja = dbService.QueryList(selOther);
                jo.put("othervisitor", otherja);
            }
            JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
            JsonData jd = new JsonData();
            if (!jo.isEmpty()) {
                JSONArray data = result.getData();
                data.getJSONObject(0).putAll(jo);
                jd.setData(data);
                jd.setSuccess(true);
            }
            return jd;
        }
    }

    /**
     * 手机端获取延迟访客列表
     */
    public JsonData getMyDelayedDepartureList(HttpServletRequest request, HttpServletResponse response, String uid, String starttime, String endtime, int start, int limit, boolean isDetail) {
        String fields = "distinct vs.uid, vs.visitorid,vs.areaposition, vm.name as areapositionname, " +
                "vv.name, vv.sex, vv.mobile, vv.company, vv.position, vv.department, vs.reason, " +
                "date_format(vs.visittime, '%Y-%m-%d %H:%i:%s') as visittime, " +
                "date_format(vs.leavetime, '%Y-%m-%d %H:%i:%s') as leavetime,vs.isleave,vs.byvisitor," +
                "vs.delay_status status,date_format(vs.createdate, '%Y-%m-%d %H:%i:%s') as createdate,vv.photo,vs.plateno," +
                "vs.personnum,vs.things,vs.remark,vs.delay_count as delayCount,vs.createdate";
        String table = "tb_visitor_subscribe vs " +
                "LEFT JOIN tb_visitor_visitorinfo vv ON vv.uid = vs.visitorid " +
                "LEFT JOIN tb_visitor_machine vm ON vm.position = vs.areaposition";
        table += " left join tb_visitor_subscribe_person vsp on vsp.visitor_subscribe_id = vs.uid ";
        String where = " vs.status=1  ";
        if (userredis.get(request).getUsertype() == 4) {
            fields += ",4 as usertype";
            where += " and vsp.visitor_id='" + userredis.get(request).getDocid() + "' ";
        }
        if (StringUtils.isNotBlank(uid)) {
            where += " and vs.uid='" + uid + "'";
        }
        if (!StringUtils.isBlank(starttime) && !StringUtils.isBlank(endtime)) {
            where += " and (vs.visittime between '" + starttime + " 00:00:00' and '" + endtime + " 23:59:59') ";
        }
        String orderby = "vs.createdate desc";
        if (!isDetail) {
            JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
            return result;
        } else {
            String selVisitor = "SELECT visitor_id FROM tb_visitor_subscribe_person WHERE visitor_subscribe_id='" + uid + "'";
            List<String> queryFields = dbService.queryFields(selVisitor, String.class);
            StringJoiner joiner = new StringJoiner("','", "('", "')");
            for (String queryField : queryFields) {
                joiner.add(queryField);
            }
            JSONObject jo = new JSONObject();
            if (CollectionUtil.isNotEmpty(queryFields)) {
                String selOther = "SELECT uid as visitorid,name FROM tb_visitor_visitorinfo WHERE uid in " + joiner;
                JSONArray otherja = dbService.QueryList(selOther);
                jo.put("othervisitor", otherja);
            }
            JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
            JsonData jd = new JsonData();
            if (!jo.isEmpty()) {
                JSONArray data = result.getData();
                data.getJSONObject(0).putAll(jo);
                jd.setData(data);
                jd.setSuccess(true);
            }
            return jd;
        }
    }

    public JsonData getDelayApprovalRecord(HttpServletRequest request, String formid, int start, int limit) {
        String fields = "ct.name, date_format(war.createdate, '%Y-%m-%d %H:%i') as createdate,war.auditcondition,war.remark ";
        String table = "tb_workflow_approval_record as war " +
                "left join tb_workflow_mould wm on wm.uid = war.mouldid " +
                "LEFT JOIN tb_card_teachstudinfo as ct on ct.uid = war.auditid";
        String where = "formid = '" + formid + "' and war.auditcondition in ('1','4') and mouldtype = 4";
        JsonData data = dbService.QueryJsonData(fields, table, where, "createdate DESC", start, limit);
        return data;
    }

    /**
     *审核表单
     */
    public JsonResult delayedDepartureSubmit(HttpServletRequest request, String uid, String infoId, String oldLeaveTime,String delayTime,Integer delayCount){
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "当前访客申请被取消！");
        }
        String delayTimeStr = delayTime.replace("T", " ");
        //判断是否还有离场次数
        Integer count = Integer.valueOf(syscfg.get("delayCount"));
        if (count <= delayCount) {
            return Json.getJsonResult(false, "离场延迟次数已用完！");
        }
        String remark = "原离场时间 " + oldLeaveTime;
        // 将原离场时间字符串解析为 LocalDate 对象
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(oldLeaveTime, formatter);
        LocalDate oldLocalDate = dateTime.toLocalDate();
        // 获取当天的结束时间，即当天的23:59:59
        LocalDateTime endOfDay = oldLocalDate.atTime(23, 59, 59);
        String format = endOfDay.format(formatter);
        int beginDepartureDate = oldLeaveTime.compareTo(delayTimeStr);
        int nowDate = format.compareTo(delayTimeStr);
        if (nowDate <= 0 || beginDepartureDate >= 0) {
            return Json.getJsonResult(false, "延迟离场时间必须介于原离场时间和离场当天时间之间！");
        }
        return saveDelayedDeparture(uid, delayCount, delayTimeStr);
    }

    @Nullable
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public JsonResult saveDelayedDeparture(String uid, Integer delayCount, String delayTimeStr) {
        //更新表单离场时间
        String delayedSQL = "update tb_visitor_subscribe set delay_time  = ?,delay_status = ? ,delay_count = ? where uid = ? ";
        dbService.excuteSql(delayedSQL, delayTimeStr, 0, delayCount + 1, uid);
        if ("1".equals(syscfg.get("visitormachineneedauth"))) {
            //获取到审核模板id
            String sql = "select uid from tb_workflow_mould where mouldtype = 4";
            JSONObject jsonObject = dbService.QueryJSONObject(sql);
            if (jsonObject == null) {
                return Json.getJsonResult(false, "未找到审核模板！");
            }
            String mouldId = jsonObject.getString("uid");
            //插入一条更改记录
            String insertAuditRecordSQL = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate) values  " +
                    "(uuid(),'" + uid + "','" + mouldId + "',0,1,now()) ";
            dbService.excuteSql(insertAuditRecordSQL);
        } else {
            String updateSQL = "update tb_visitor_record_namelist set status = 5 where uid = '" + uid + "'";
            dbService.excuteSql(updateSQL);
        }
        return Json.getJsonResult(true);
    }

    public JsonData getVisitorDepartureRecordList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit, String startTime, String endTime) {
        String docId = userredis.get(request).getDocid();
        WorkFlowNodeDto nodeLevel = workFlowUtil.getNodeLevel(request, docId, 4);
        if (nodeLevel == null) {
            return Json.getJsonData(false, "该用户没有审批权限");
        }
        String fields = "vs.uid, vs.visitorid,vs.areaposition, vm.name as areapositionname, vv.name, vv.sex, vv.mobile," +
                " vv.company, vv.position, vv.department, vs.reason, " +
                "date_format(vs.visittime, '%Y-%m-%d %H:%i:%s') as visittime," +
                " date_format(vs.leavetime, '%Y-%m-%d %H:%i:%s') as leavetime," +
                "vs.isleave,vs.byvisitor,vs.delay_status,date_format(vs.createdate, '%Y-%m-%d %H:%i:%s') as createdate," +
                "vv.photo,vs.plateno,vs.personnum,vs.things,date_format(vs.delay_time, '%Y-%m-%d %H:%i:%s') as delayTime,vs.remark";
        String table = "tb_visitor_subscribe vs " +
                "left join tb_workflow_approval_record war on war.formid = vs.uid and war.mouldid = '" + nodeLevel.getMouldId() + "'" +
                "left join tb_visitor_subscribe_person vsp on vsp.visitor_subscribe_id = vs.uid " +
                "LEFT JOIN tb_visitor_visitorinfo vv ON vv.uid = vsp.visitor_id " +
                "LEFT JOIN tb_visitor_machine vm ON vm.position = vs.areaposition";
        String where = " vs.status = 1 ";
        if (!StringUtils.isBlank(startTime) && !StringUtils.isBlank(endTime)) {
            where += " and (vs.visittime between '" + startTime + " 00:00:00' and '" + endTime + " 23:59:59') ";
        }
        String recordsWhereSQL = workFlowUtil.getAuditRecordsWhereSQL(request, 4, nodeLevel,"vs.byvisitorid","vs.byvisitorid");
        if (StringUtils.isNotBlank(recordsWhereSQL)) {
            where += recordsWhereSQL;
        }
        String orderby = "vs.createdate desc";
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    //审核延期表单
    @Transactional(rollbackFor = Exception.class)
    public JsonResult getVisitorDepartureAudit(HttpServletRequest request, HttpServletResponse response, Integer status, String formId, String leaveTime, String delayTime) {
        String selSQL = "select delay_status from tb_visitor_subscribe where uid = '" + formId + "'";
        Integer integer = dbService.queryOneField(selSQL, Integer.class);
        if (integer == null || integer != 0) {
            return Json.getJsonResult(false, "该表单已审核！");
        }
        String sql = "update tb_visitor_subscribe set delay_status = " + status + ",leavetime = ?,remark = ?  where uid = '" + formId + "'";
        dbService.excuteSql(sql, delayTime, "原离场时间：" + leaveTime);
        String updateWorkForm = "update tb_workflow_approval_record ap join tb_workflow_mould wm on ap.mouldid = wm.uid " +
                " set ap.auditcondition = ?  , ap.remark = ?,ap.modifydate = ? ,ap.auditid = ?, ap.modifyid = ? , ap.auditdate = ?  " +
                " where formid = '" + formId + "' " +
                " and node_level = " + 1 +
                " and auditcondition = 0 " +
                " and wm.mouldtype = 4 ";
        dbService.excuteSql(updateWorkForm,
                1,
                userredis.get(request).getName() + "   " + (status == 1 ? "  同意" : "  不同意"),
                DateHelper.format(new Date(), " yyyy-MM-dd HH:mm:ss"),
                userredis.get(request).getDocid(),
                userredis.get(request).getDocid(),
                DateHelper.format(new Date(), " yyyy-MM-dd HH:mm:ss"));
        //更新下载状态
        if (status == 1) {
            String updateSQL = "update tb_visitor_record_namelist set status = 5 where uid = '" + formId + "'";
            dbService.excuteSql(updateSQL);
        }
        return Json.getJsonResult(true);
    }

    public List<ElevatorDevicesVo> getElevatorDevices(String uid) {
        String sql = "select aa.password,aa.usage_count usageCount,case when ifnull(aad.floors,'') = '' then vmd.floor else aad.floors end," +
                "aad.dev_id devId,aad.door doors,doorname doorName,vmd.name devName,aad.down_status as status " +
                "from tb_access_application aa " +
                "join tb_access_application_dev aad on aad.apply_id = aa.id " +
                "join tb_visitor_machine_device vmd on vmd.devid = aad.dev_id " +
                " where aa.id = '" + uid + "'";
        List<ElevatorDevicesVo> devicesVos = dbService.queryList(sql, ElevatorDevicesVo.class);
        return devicesVos;
    }

    public JsonData saveDeviceSettings(AuditApplyRequest requestBody) {
        String sql = "update tb_access_application set usage_count =? ,password = ? where id = ?";
        dbService.excuteSql(sql, requestBody.getGlobalUsageCount(), requestBody.getGlobalPassword(), requestBody.getUid());

        for (AuditApplyRequest.DeviceSetting device : requestBody.getDevices()) {
            if (StringUtils.isNotBlank(device.getFloors())) {
                String savaSql = "update tb_access_application_dev set floors =? where apply_id = ? and dev_id = ?";
                dbService.excuteSql(savaSql, device.getFloors(), requestBody.getUid(),device.getDeviceId());
            }
        }
        return Json.getJsonData(true);
    }

    @Data
    public static class ElevatorDevicesVo {
        private String password;
        private String usageCount;
        private String floors;
        private String devId;
        private String doors;
        private String doorName;
        private String devName;
        private Integer status;
    }
}

