package com.ymiots.campusmp.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.ApprovalProcessUtil;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.controller.workflow.PushMessageHandler;
import com.ymiots.campusmp.controller.workflow.WorkFlowUtil;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.entity.dto.Location;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.model.R;
import com.ymiots.campusmp.redis.RedisMessageEntity;
import com.ymiots.campusmp.redis.RedisPublish;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.face.FaceDetectionService;
import com.ymiots.campusmp.service.workflow.ApprovalFormService;
import com.ymiots.campusmp.utils.GeoUtils;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessageData;
import com.ymiots.framework.common.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Repository
public class TimeService extends BaseService {

    @Autowired
    UserRedis userredis;

    @Autowired
    RedisPublish redispublish;


    @Autowired
    private ApprovalFormService approvalFormService;

    @Autowired
    private ApprovalProcessUtil approvalProcessUtil;

    @Autowired
    SysLogsService sysLogsService;

    @Autowired
    private WorkFlowUtil workFlowUtil;

    @Autowired
    private PushMessageHandler pushMessageHandler;


    public JsonData getPeople(HttpServletRequest request, HttpServletResponse response) {
        JsonData jd = new JsonData();
        SysUser user = userredis.get(request);
        int usertype = user.getUsertype();

//        if (usertype == 2) {
        String idcode = user.getIdcode();
        String sql = "SELECT ct.name as title,wu.docid as value " +
                "FROM tb_weixin_user wu INNER JOIN tb_card_teachstudinfo ct ON wu.docid=ct.uid " +
                "WHERE wu.idcode='" + idcode + "' ";
        jd = dbService.QueryJsonData(sql);
//        } else if (usertype == 3) {
//            String useridSQL = "SELECT uid FROM tb_sys_user WHERE empid='" + user.getDocid() + "'";
//            JSONArray useridja = dbService.QueryList(useridSQL);
//            if (useridja.isEmpty()) {
//                JsonData jsonData = new JsonData();
//                jsonData.setSuccess(false);
//                return jsonData;
//            }
//            String userid = useridja.getJSONObject(0).getString("uid");
//            String sxu = "SELECT distinct ct.name as title,ct.uid as value " +
//                    "FROM tb_card_teachstudinfo ct " +
//                    "join tb_card_orgframework co on ct.orgcode = co.code " +
//                    "join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid = '" + userid + "'" +
//                    " order by ct.name asc";
//            JSONArray xuja = dbService.QueryList(sxu);
//            jd.setSuccess(true);
//            jd.setData(xuja);
//        }
        return jd;
    }

    public JsonData getAbsenceRecord(HttpServletRequest request, String docid, int start, int limit, String starttime, String endtime) {
        String fields = "uid,infoid,date_format(starttime, '%Y-%m-%d %H:%i') as starttime,status ";
        String table = "tb_time_absence";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND starttime BETWEEN '" + starttime + ":00' AND '" + endtime + ":59'";
        }
        where += " and infoid = '" + docid + "' ";
        return dbService.QueryJsonData(fields, table, where, "createdate DESC", start, limit);
    }

    public JsonData getSigncardRecord(HttpServletRequest request, String docid, int start, int limit, String starttime, String endtime) {
        String fields = "uid,infoid,date_format(signtime, '%Y-%m-%d %H:%i') as signtime,status ";
        String table = "tb_time_signcard";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND signtime BETWEEN '" + starttime + ":00' AND '" + endtime + ":59'";
        }
        where += " and infoid = '" + docid + "' ";
        return dbService.QueryJsonData(fields, table, where, "createdate DESC", start, limit);
    }

    public JsonData getTypes(HttpServletRequest request, HttpServletResponse response) {
        String fields = " code value,name ";
        String table = "tb_sys_dictionary";
        String where = " 1=1 ";
        where += " and groupcode = 'SYS0000081' and status=1";
        return dbService.QueryJsonData(fields, table, where, "sort ASC", 0, 1000);
    }

    public JsonData saveabsence(HttpServletRequest request, HttpServletResponse response,String uid,String applyDay,String starttime,String endtime,String xvjiaday,String linianday,String bennianday) {
        dbService.excuteSql("update tb_time_absence set real_day=?,starttime=?,endtime=?,xvjiaday=?,linianday=?,bennianday=? where uid =?",applyDay,starttime,endtime,xvjiaday,linianday,bennianday,uid);
        return Json.getJsonData(true);
    }

    public JsonData getpsitoin(HttpServletRequest request, HttpServletResponse response) {
        String fields = " label name ";
        String table = "tb_card_infolabel";
        String where = " infotype = 1 ";
        return dbService.QueryJsonData(fields, table, where, "label ASC", 0, 1000);
    }

    public JsonData searchaduit(HttpServletRequest request, HttpServletResponse response,String type,String name) {
        if (StringUtils.isEmpty(type)){
            return Json.getJsonData(true);
        }
        String fields = "  uid,name ";
        String table = "tb_card_teachstudinfo";
        String where = " status = 1 and name like '%"+name+"%' and infolabel = '"+type+"'";
        return dbService.QueryJsonData(fields, table, where, "code ASC", 0, 1000);
    }

    public JsonData getTimeRecord(HttpServletRequest request, HttpServletResponse response, int start, int limit, String startTime, String endTime) throws ParseException {
        SysUser sysUser = userredis.get(request);
        String fields = "a.uid,date_format(a.recordtime, '%Y-%m-%d %H:%i:%s')as recordtime,CASE a.status WHEN '1' THEN ac.devname WHEN '2' THEN '定位考勤' WHEN '3' THEN '消费考勤' ELSE '' END AS timetypename  ";
        String table = "tb_time_records a LEFT JOIN tb_dev_accesscontroller ac ON a.machineid = ac.machineid";
        String where = " a.isdel = 0 and a.infoid = '" + sysUser.getDocid() + "'";
        String signcardWhere = "infoid = '" + sysUser.getDocid() + "'";
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            where += " AND a.recordtime BETWEEN '" + startTime + ":00' AND '" + endTime + ":59'";
            signcardWhere += " AND signtime BETWEEN '" + startTime + ":00' AND '" + endTime + ":59'";
        }
        JsonData jsonData = Json.getJsonData(true);
        JsonData jd = dbService.QueryJsonData(fields, table, where, "recordtime DESC", start, limit);
        if (start != 0) {
            return jd;
        }
        JSONArray array = dbService.QueryList("SELECT uid,date_format(signtime, '%Y-%m-%d %H:%i:%s')as recordtime,CASE worktype WHEN '7' THEN '缺卡考勤-上班' WHEN '8' THEN '缺卡考勤-下班' ELSE '缺卡考勤' END AS timetypename   from tb_time_signcard where `status` = 1 and " + signcardWhere);
        for (int i = 0; i < jd.getData().size(); i++) {
            array.add(jd.getData().getJSONObject(i));
        }
        sortJsonArrayByRecordTime(array);
        jsonData.setData(array);
        return jsonData;
    }

    public static void sortJsonArrayByRecordTime(JSONArray array) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 显式转换为 List<JSONObject>
        List<JSONObject> list = new ArrayList<>();
        for (Object obj : array) {
            list.add((JSONObject) obj);
        }

        Collections.sort(list, new Comparator<JSONObject>() {
            @Override
            public int compare(JSONObject o1, JSONObject o2) {
                try {
                    Date date1 = sdf.parse(o1.getString("recordtime"));
                    Date date2 = sdf.parse(o2.getString("recordtime"));
                    // 降序排序
                    return date2.compareTo(date1);
                } catch (ParseException e) {
                    e.printStackTrace();
                    return 0;
                }
            }
        });

        // 将排序后的列表放回 JSONArray
        array.clear();
        array.addAll(list);
    }


    public JsonData getLeaveRecordTeach(HttpServletRequest request, String key, int start, int limit, String starttime, String endtime) {
        String docId = userredis.get(request).getDocid();
        String sql = "select uid from tb_sys_user where empid = '" + docId + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(sql);
        String fields = "cafl.uid,cafl.infoid,date_format(cafl.starttime, '%Y-%m-%d %H:%i') as starttime,cafl.status ";
        String table = "tb_card_ask_for_leave cafl";
        String where = " 1=1 ";
        String userId = "";
        if (StringUtils.isNotBlank(jsonObject.getString("uid"))) {
            userId = jsonObject.getString("uid");
        }
        table += " left join tb_card_teachstudinfo ct on ct.uid = cafl.infoid " +
                "join tb_card_orgframework co on co.code = ct.orgcode " +
                "join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid = '" + userId + "'";

        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND starttime BETWEEN '" + starttime + ":00' AND '" + endtime + ":59'";
        }
        if (StringUtils.isNotBlank(key)) {
            where += " And (ct.code = '" + key + "%' or ct.name like '" + key + "%')";
        }
        return dbService.QueryJsonData(fields, table, where, "cafl.createdate DESC", start, limit);
    }


    public JSONArray getDetailedAbsenceRecord(String uid, HttpServletRequest request) {
        String sql = "SELECT DISTINCT  asl.uid,asl.img_path imgPath,asl.infoid,(case asl.sfxvjia when '1' then '是' when '0' then '否' else '否' end) as sfxvjia,asl.xvjiaday,asl.linianday,asl.bennianday,ct.name as infoname,asl.real_day day,ct.code as infocode,asl.remark reason,date_format( asl.starttime, '%Y-%m-%d %H:%i:%s' ) AS starttime,date_format( asl.endtime, '%Y-%m-%d %H:%i:%s' ) AS endtime,asl.status,asl.absencetype,dic.NAME AS absencetypename , war.auditcondition as auditcondition, war.remark as warremark ,war.auditid as auditid, war.modifyid as modifyid " +
                " FROM tb_time_absence asl " +
                " left join tb_card_teachstudinfo ct on ct.uid = asl.infoid " +
                " LEFT JOIN tb_sys_dictionary dic ON dic.CODE = asl.absencetype  AND dic.groupcode = 'SYS0000081' " +
                " LEFT JOIN tb_workflow_approval_record as war on war.formid = asl.uid" +
                " WHERE  asl.uid = '" + uid + "' order by auditcondition ";
        return dbService.QueryList(sql);
    }


    public JSONArray getDetailedSigncardRecord(String uid, HttpServletRequest request) {
        String sql = "SELECT DISTINCT  asl.uid,asl.infoid,asl.ftype,asl.worktype,ct.name as infoname,ct.code as infocode," +
                "asl.remark reason,date_format( asl.signtime, '%Y-%m-%d %H:%i:%s' ) AS signtime,date_format( asl.workdate, '%Y-%m-%d' ) AS workdate," +
                "asl.status, war.auditcondition as auditcondition, war.remark as warremark ,war.auditid as auditid, war.modifyid as modifyid ," +
                "date_format( asl.createdate, '%Y-%m-%d %H:%i:%s' ) AS createdate" +
                " FROM tb_time_signcard asl " +
                " left join tb_card_teachstudinfo ct on ct.uid = asl.infoid " +
                " LEFT JOIN tb_workflow_approval_record as war on war.formid = asl.uid" +
                " WHERE  asl.uid = '" + uid + "' order by auditcondition ";
        return dbService.QueryList(sql);
    }

    public JSONObject getClassStudentDetailList(HttpServletRequest request) {
        String uid = userredis.get(request).getDocid();
        String sql = "select ct.uid,ct.code,ct.idcard,ct.name,ct.sex,ct.card,ct.mobile,getminiorgname(ct.orgcode) orgName,fi.imgpath " +
                "from tb_card_teachstudinfo ct " +
                "left join tb_face_infoface fi on fi.infoid = ct.uid " +
                " where ct.uid = '" + uid + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(sql);
        return jsonObject;
    }


    @Data
    public static class WeiXinUser {
        private String uid;
        private String openId;
        private String name;
        private String code;
        private Date startTime;
        private Date endTime;
        private String reason;
        private String infoId;
        private Integer status;
    }

    public JsonResult cancelAbsence(String uid) {
        JSONArray array = dbService.QueryList("select * from tb_workflow_approval_record where formid = ? order by createdate asc limit 1", uid);
        if (array.size()>0){
            if (StringUtils.isNotEmpty(array.getJSONObject(0).getString("auditdate"))){
                return Json.getJsonResult(false, "已有审核节点被审核，无法取消！");
            }
        }
        String delSQL = "update tb_time_absence set status = 2  WHERE uid=?";
        dbService.excuteSql(delSQL, uid);
        String updateSQl = "update tb_workflow_approval_record set auditcondition = 2 ,remark = '" + "单据已被取消" + "' where formid = '" + uid + "' and auditcondition = 0";
        dbService.excuteSql(updateSQl);
        return Json.getJsonResult(true);
    }

    public JsonResult cancelSigncard(String uid) {
        int record = dbService.getCount("tb_workflow_approval_record", "formid = '" + uid + "' and  auditcondition = 1 ");
        if (record >= 1) {
            return Json.getJsonResult(false, "当前提交已被审核");
        }
        String delSQL = "update tb_time_signcard set status = 2  WHERE uid=?";
        dbService.excuteSql(delSQL, uid);
        String updateSQl = "update tb_workflow_approval_record set auditcondition = 2 ,remark = '" + "单据已被取消" + "' where formid = '" + uid + "' and auditcondition = 0";
        dbService.excuteSql(updateSQl);
        return Json.getJsonResult(true);
    }


    public JsonData getAuditAbsenceList(HttpServletRequest request, HttpServletResponse response, String key, String status, String endtime, String starttime, int start, int limit) {
        String docId = userredis.get(request).getDocid();
        String fields = " distinct afl.uid,ct.code as infocode,afl.infoid,ct.name as infoname,afl.remark reason,date_format(afl.starttime,'%Y-%m-%d %H:%i')as starttime,date_format(afl.endtime,'%Y-%m-%d %H:%i')as endtime,date_format(afl.createdate,'%Y-%m-%d %H:%i')as createdate,afl.absencetype,dic.name as absencetypename,afl.status,war.auditcondition";
        String table = "tb_time_absence afl " +
                "INNER JOIN tb_card_teachstudinfo ct ON ct.uid = afl.infoid   AND  afl.starttime between  ' " + starttime + " ' and '" + endtime + "' " +
                "LEFT JOIN tb_workflow_approval_record as war on war.formid = afl.uid  " +
                "LEFT JOIN tb_sys_dictionary dic on dic.code=afl.absencetype and dic.groupcode='SYS0000081' ";
        String where = "1=1";
        if (StringUtils.isNotBlank(status) && !("2").equals(status)) {
            where += " AND afl.status=" + status;
        }
        List<WorkFlowNodeDto> levelList = workFlowUtil.getNodeLevelList(request, docId, null);
        if (CollectionUtil.isEmpty(levelList)) {
            return Json.getJsonData(false, "该用户没有审批权限");
        }
        String condition = workFlowUtil.getAuditRecordsWhereSQL(request, levelList, "afl.infoid", "war.auditid");
        if (StringUtils.isNotBlank(condition)) {
            where += condition;
        }
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND  afl.starttime between  '" + starttime + "' and '" + endtime + "'";
        }
        if (StringUtils.isNotBlank(key)) {
            where += " AND (ct.name like '" + key + "%' or ct.code='" + key + "')";
        }

        where += " or war.auditid = '" + docId + "' ";
        String orderby = ExtSort.Orderby(request, "createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData getAuditSigncardList(HttpServletRequest request, HttpServletResponse response, String key, String status, String endtime, String starttime, int start, int limit) {
        String docId = userredis.get(request).getDocid();
        String fields = " distinct afl.uid,ct.code as infocode,afl.infoid,ct.name as infoname,afl.remark reason," +
                "date_format(afl.signtime,'%Y-%m-%d %H:%i')as signtime,date_format(afl.workdate,'%Y-%m-%d %H:%i')as workdate," +
                "date_format(afl.createdate,'%Y-%m-%d %H:%i')as createdate,afl.status,war.auditcondition";
        String table = "tb_time_signcard afl " +
                "INNER JOIN tb_card_teachstudinfo ct ON ct.uid = afl.infoid " +
                "LEFT JOIN tb_workflow_approval_record as war on war.formid = afl.uid  ";
        String where = "( 1=1";
        if (StringUtils.isNotBlank(status) && !("2").equals(status)) {
            where += " AND afl.status=" + status;
        }

        List<WorkFlowNodeDto> levelList = workFlowUtil.getNodeLevelList(request, docId, 8);
        if (CollectionUtil.isEmpty(levelList)) {
            return Json.getJsonData(false, "该用户没有审批权限");
        }
        String condition = workFlowUtil.getAuditRecordsWhereSQL(request, levelList, "afl.infoid", "war.auditid");
        if (StringUtils.isNotBlank(condition)) {
            where += condition;
        }
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND  afl.signtime between  '" + starttime + "' and '" + endtime + "'";
        }
        where += " or war.auditid = '" + docId + "' )";
        if (StringUtils.isNotBlank(key)) {
            where += " AND (ct.name like '" + key + "%' or ct.code='" + key + "')";
        }
        String orderby = ExtSort.Orderby(request, "createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

//        public String getAuditRecordsWhereSQL(HttpServletRequest request, Integer mouldType, WorkFlowNodeDto nodeLevel,String infoField) {
//
//            return where;
//        }

    /**
     * 老师提交家长请假单
     */
    public JsonData getAuditLeaveList1(HttpServletRequest request, HttpServletResponse response, String leavetype, String key, String status, String endtime, String starttime, int start, int limit) {

        String fields = " distinct afl.uid,ct.code as infocode,afl.infoid,ct.name as infoname,afl.reason,date_format(afl.starttime,'%Y-%m-%d %H:%i')as starttime,date_format(afl.endtime,'%Y-%m-%d %H:%i')as endtime,afl.leavetype,dic.name as leavetypename,afl.remark,afl.status,afl.isfinish,afl.leavefinishtime,date_format(afl.createdate, '%Y-%m-%d %H:%i') as createdate";
        String table = "tb_card_ask_for_leave afl " +
                "INNER JOIN tb_card_teachstudinfo ct ON ct.uid = afl.infoid   AND  afl.starttime between  ' " + starttime + " ' and '" + endtime + "' LEFT JOIN tb_workflow_approval_record as war on war.formid = afl.uid  and war.auditcondition = afl.status " +
                "LEFT JOIN tb_sys_dictionary dic on dic.code=afl.leavetype and dic.groupcode='SYS0000081' ";
        String where = "1=1";
        if (StringUtils.isNotBlank(leavetype) && !("0").equals(leavetype)) {
            where += " and afl.leavetype=" + leavetype;
        }
        if (StringUtils.isNotBlank(status) && !("2").equals(status)) {
            where += " AND afl.status=" + status;
        }
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND  afl.starttime between  '" + starttime + "' and '" + endtime + "'";

        }
        if (StringUtils.isNotBlank(key)) {
            where += " AND (ct.name like '%" + key + "%' or ct.code='" + key + "')";
        }
        String docid = userredis.get(request).getDocid();
        where += "and afl.infoid = '" + docid + "' and afl.is_approval = 0 ";
        String orderby = ExtSort.Orderby(request, "createdate  desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }


    public JsonResult auditabsencefrom(HttpServletRequest request, String uid, String infoId, Integer status, String remark,String auditInfo) throws Exception {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "当前缺勤申请被取消！");
        }
        String table = "tb_time_absence";
        String field = "real_day";
        String sqlLabId = "select real_day,absencetype,bennianday sum_day from " + table + " where uid = '" + uid + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(sqlLabId);
        String realDay = jsonObject.getString("real_day");
        String absencetype = jsonObject.getString("absencetype");
        String sumDay = jsonObject.getString("sum_day");
        String name = userredis.get(request).getName();
        String userId = userredis.get(request).getDocid();
        return workFlowUtil.submitForApproval(request, uid, userId, name, status, remark, table, field, "", realDay,auditInfo,absencetype,sumDay);
    }

    public JsonResult auditsigncardfrom(HttpServletRequest request, String uid, String infoId, Integer status, String remark,String dUserId) throws Exception {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "当前签卡申请被取消！");
        }
        String table = "tb_time_signcard";
        String field = "";
        String name = userredis.get(request).getName();
        String userId = userredis.get(request).getDocid();
        return workFlowUtil.submitForApproval(request, uid, userId, name, status, remark, table, field, "", "");
    }

    /**
     * 销假表单
     */
    public JsonResult leaveFinish(HttpServletRequest request, String uid, String infoid, String status, String remark) {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "当前请假申请被取消！");
        }
        String updateRequisitionSql = "update tb_card_ask_for_leave set isfinish = 1,leavefinishtime = now() where uid = '" + uid + "'";
        dbService.excuteSql(updateRequisitionSql);
        return Json.getJsonResult(true);
    }

    /**
     * 家长同意老师表单
     */
    public JsonResult auditLeave2(HttpServletRequest request, String uid, String infoid, String status, String remark) {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "当前请假申请被取消！");
        }
        String name = userredis.get(request).getName();
        int count = dbService.getCount("tb_card_ask_for_leave", "uid = '" + uid + "' and status = 0");
        if (count == 0) {
            return Json.getJsonResult(false, "当前请假申请已被审批！");
        }
//        String getInfoNameSql = "select linkman1 from tb_card_teachstudinfo where uid = '" + uid + "'";
//        String linkman1 = dbService.QueryJSONObject(getInfoNameSql).getString("linkman1");
        String sql = "update tb_card_ask_for_leave set status = ? ,remark = ?,isfinish = 0 where uid = '" + uid + "'";
        String info = "通过";
        if (status.equals("2")) {
            info = "拒绝";
        }
        int ok = dbService.excuteSql(sql, status, remark);
        if (ok > 0) {
            //推送给上级
            boolean parent = getParent(infoid, uid, name, info);
            if (parent) {
                Log.info(this.getClass(), "推送成功");
            } else {
                Log.info(this.getClass(), "推送失败");

            }

        }
        return Json.getJsonResult(true, "审批成功");
    }

    public boolean getParent(String infoId, String formId, String name, String info) {
        String sql = "select ct.name,cafl.status,date_format(cafl.starttime,'%Y-%m-%d %H:%i:%s') as starttime,created_by as createdBy from " +
                " tb_card_teachstudinfo ct " +
                " left join tb_card_ask_for_leave cafl on cafl.infoid = ct.uid " +
                " where ct.uid = '" + infoId + "' and cafl.uid = '" + formId + "'";
        JSONArray jsonArray = dbService.QueryList(sql);
        if (jsonArray.size() != 0) {
            JSONObject jo = jsonArray.getJSONObject(0);
            String weiXinSQL = "select openid,uid from tb_weixin_user where docid = '" + jo.getString("createdBy") + "'";
            if (jo.isEmpty()) {
                return false;
            }

            JSONObject jsonObject = dbService.QueryJSONObject(weiXinSQL);
            String openid = jsonObject.getString("openid");
            String wxuserid = jsonObject.getString("uid");
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("cfgcode", "OPENTM417832643");
            data.put("url", String.format("%s/askforleave/detailedLeaveRecord?uid=%s&time=%s", WebConfig.getDomain(), formId, System.currentTimeMillis()));
            JSONObject wxdata = new JSONObject();
            wxdata.put("thing1", new TemplateMessageData(jo.getString("name")));
            wxdata.put("thing4", new TemplateMessageData(name));
            wxdata.put("time2", new TemplateMessageData(jo.getString("starttime")));
            wxdata.put("time5", new TemplateMessageData(DateHelper.format(new Date(), "yyyy-MM-dd")).toJSONString());
            wxdata.put("const3", new TemplateMessageData(info));
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);
            return true;
        }
        return false;
    }


    public JsonResult saveabsencefromdutyfrom(HttpServletRequest request, HttpServletResponse response, String docid, String starttime, String endtime, String des, String type, String applyDay, MultipartFile[] files,
                                              String dUserId,String sfxvjia,String xvjiaDay,String qunianshu,String bennianshu) throws IOException {
        SysUser sysUser = userredis.get(request);
        // 上传图片
        String imgPath = "";
        // 1. 验证数据
//        if (files.length == 0) {
//            return Json.getJsonResult(false,"至少上传一张照片");
//        }

        // 2. 存储照片
        List<String> photoUrls = new ArrayList<>();
        if (files!=null) {
            for (MultipartFile file : files) {
                if (!file.isEmpty()) {
                    String originalFilename = file.getOriginalFilename();
                    String fileExtension = getFileExtension(originalFilename);
                    if (!fileExtension.equals("jpg") && !fileExtension.equals("jpeg")) {
                        return Json.getJsonResult(false, "图片格式不正确,请上传jpg或jpeg格式的图片");
                    }

                    String uniqueFilename = generateUniqueFilename(fileExtension);
                    //保存图片
                    imgPath = saveFile(file, WebConfig.getUploaddir(), uniqueFilename);
                    if (imgPath.length() == 0) {
                        Log.error(FaceService.class, "微信端照片附件上传：文件保存失败");
                        return Json.getJsonResult(false, "文件保存失败");
                    }
                    if (file.getSize() > 204800) {
                        ImageUtil.changeImageWH(WebConfig.getUploaddir() + imgPath, 1024, 1024);
                    }
                    photoUrls.add(imgPath);
                }
            }

        }
        if (StringUtils.isBlank(dUserId)) {
            return Json.getJsonResult(false, "未指定正确审核人");
        }
        String docId = sysUser.getDocid();
        String orgCode = sysUser.getOrgcode();
        if (sysUser.getUsertype() == 3) {
            //根据1级指定人获取审核模版
            String sqlMouldId = "select mouldid,behavior_status behaviorStatus from tb_workflow_node wn " +
                    "join tb_workflow_approvar wa on wa.nodeid = wn.uid " +
                    "where infoid = ?";
            JSONArray ja = dbService.QueryList(sqlMouldId, dUserId);
            //获取工作流模板为请假的模板id
            if (ja.isEmpty()) {
                return Json.getJsonResult(false, "未配置审批模板");
            }
            int level = 1;
            String mouldId = ja.getJSONObject(0).getString("mouldid");
            int behaviorStatus = ja.getJSONObject(0).getIntValue("behaviorStatus");
            List<WorkFlowNodeDto> workFlowNodeDTOs = workFlowUtil.selSubPerson(mouldId, docId, dUserId, orgCode, "", level);
            if (workFlowNodeDTOs.size() == 0) {
                return Json.getJsonResult(false, "审核人未配置，请联系管理员！");
            }
            String auditId = null;
            if (behaviorStatus == 4) {
                auditId = dUserId;
            }
            StringBuilder sql = new StringBuilder();
            String appid = UUID.randomUUID().toString().replace("-", "");
            // 将路径列表转为逗号分隔字符串
            String dbImagePaths = String.join(",", photoUrls);

            sql.append("INSERT INTO tb_time_absence(uid,infoid,remark,starttime,endtime,createdate,creatorid,status,absencetype,real_day,img_path,sfxvjia,xvjiaday,linianday,bennianday) ").append("values('").append(appid).append("','").append(docid).append("','").append(des).append("','").append(starttime).append("','").append(endtime).append("',now(),'").append(sysUser.getUid()).append("',0,'")
                    .append(type).append("','").append(applyDay).append("','").append(dbImagePaths).append("',").append(sfxvjia).append(",'").append(xvjiaDay).append("','").append(qunianshu).append("','").append(bennianshu).append("') ");
            dbService.excuteSql(sql.toString());
            String insertRecordSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate,auditid) values (uuid(),?,?,?,?,now(),?)";
            dbService.excuteSql(insertRecordSql, appid, mouldId, 0, level,auditId);
            //推送消息
            pushMessageHandler.pushNextTimeAbsenceApply(appid, workFlowNodeDTOs);
        }
        return Json.getJsonResult(true);
    }

    public JsonData getDetailedRecord(HttpServletRequest request, HttpServletResponse response, String uid) {
        String fields = "ifnull(ac.devname,'非机器打卡') devname,a.isupload imgpath,date_format(a.recordtime,'%Y-%m-%d %H:%i:%s') recordtime,getorgname(a.orgcode) as orgname,a.infoname name,a.infocode code, CASE a.status WHEN '1' THEN '机器考勤' WHEN '2' THEN '定位考勤' WHEN '3' THEN '消费考勤' ELSE '' END AS timetypename  ";
        String table = "tb_time_records a " +
                "LEFT JOIN tb_dev_accesscontroller ac ON a.machineid = ac.machineid ";
        String where = "a.uid = '" + uid + "'";
        JsonData jd = dbService.QueryJsonData(fields, table, where, "recordtime DESC");
        if (jd.getData().size() == 0) {
            fields = "'非机器打卡' devname,'' imgpath,date_format(a.signtime,'%Y-%m-%d %H:%i:%s') recordtime,getorgname(ac.orgcode) as orgname,ac.name,ac.code, CASE a.worktype WHEN '7' THEN '缺卡考勤-上班' WHEN '8' THEN '缺卡考勤-下班' ELSE '缺卡考勤' END AS timetypename  ";
            table = "tb_time_signcard a " +
                    "LEFT JOIN tb_card_teachstudinfo ac ON a.infoid = ac.uid ";
            where = "a.uid = '" + uid + "'";
            return dbService.QueryJsonData(fields, table, where, "signtime DESC");
        }
        return jd;
    }

    public JsonResult savesigncardfromdutyfrom(HttpServletRequest request, HttpServletResponse response, String docid, String signtime, String des, String worktype, String ftype,String dUserId) {
        SysUser sysUser = userredis.get(request);
        String docId = sysUser.getDocid();
        String orgCode = sysUser.getOrgcode();
        if (sysUser.getUsertype() == 3) {
            String sqlMouldId = "select mouldid,behavior_status behaviorStatus from tb_workflow_node wn " +
                    "join tb_workflow_mould wam on wam.uid = wn.mouldid " +
                    "where mouldtype = 8 and nodelevel = 1 ";
            JSONArray ja = dbService.QueryList(sqlMouldId);
            if (ja.isEmpty()) {
                return Json.getJsonResult(false, "未配置审批模板");
            }
            String mouldId = ja.getJSONObject(0).getString("mouldid");
            int behaviorStatus = ja.getJSONObject(0).getIntValue("behaviorStatus");
            List<WorkFlowNodeDto> workFlowNodeDTOs = workFlowUtil.selSubPerson(mouldId, docId, dUserId, orgCode, "", 1);
            if (workFlowNodeDTOs.size() == 0) {
                return Json.getJsonResult(false, "未配置审核人，请联系管理员！");
            }
            String auditId = null;
            if (behaviorStatus == 4) {
                auditId = dUserId;
            }
            StringBuilder sql = new StringBuilder();
            String appid = UUID.randomUUID().toString().replace("-", "");
            sql.append("INSERT INTO tb_time_signcard(uid,infoid,remark,signtime,workdate,createdate,creatorid,status,worktype,ftype) ").append("values('" + appid + "','").append(docid).append("','").append(des).append("','").append(signtime).append("','").append(signtime.substring(0, 10)).append("',").append("now(),'").append(sysUser.getUid()).append("',0,'").append(worktype).append("','").append(ftype).append("') ");
            dbService.excuteSql(sql.toString());
            String insertRecordSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate,auditid) values (uuid(),?,?,?,?,now(),?)";
            dbService.excuteSql(insertRecordSql, appid, mouldId, 0, 1, auditId);
            //推送消息
            pushMessageHandler.pushNextTimeSigncardApply(appid, workFlowNodeDTOs);
        }
        return Json.getJsonResult(true);
    }


    public R<?> getAbsenceRecordDetails(HttpServletRequest request, HttpServletResponse response, String uid) {


        String auditRecordSQL = "SELECT ct.code,ct.name,wap.remark,wap.auditcondition,date_format(wap.auditdate, '%Y-%m-%d %H:%i:%s') as auditdate,wap.node_level as currentauditnodelevel " +
                "FROM tb_workflow_approval_record  as wap " +
                "JOIN  tb_card_teachstudinfo as ct on wap.auditid = ct.uid " +
                "WHERE formid=? and auditcondition != 0 " +
                "order by wap.auditdate desc";
        JSONArray auditRecord = dbService.QueryList(auditRecordSQL, uid);
        return R.ok(auditRecord);
    }

    public R<?> getSigncardRecordDetails(HttpServletRequest request, HttpServletResponse response, String uid) {


        String auditRecordSQL = "SELECT ct.code,ct.name,wap.remark,wap.auditcondition,date_format(wap.auditdate, '%Y-%m-%d %H:%i:%s') as auditdate,wap.node_level as currentauditnodelevel " +
                "FROM tb_workflow_approval_record  as wap " +
                "JOIN  tb_card_teachstudinfo as ct on wap.auditid = ct.uid " +
                "WHERE formid=? and auditcondition != 0 " +
                "order by wap.auditdate desc";
        JSONArray auditRecord = dbService.QueryList(auditRecordSQL, uid);
        return R.ok(auditRecord);
    }

    public boolean pushToUser(String formId, String userId) {
        //判断家长是否被绑定
        String sql = "SELECT wu.uid,wu.openid as openId,ct.name,ct.code,ct.linkmobile1 as linkMobile1,cafl.status,cafl.starttime as startTime,cafl.endtime as endTime,cafl.createdate as createDate,cafl.reason,cafl.infoid  as infoId,cafl.created_by" +
                "  FROM tb_weixin_user as wu  " +
                "  LEFT JOIN tb_card_teachstudinfo as ct on ct.uid = wu.docid " +
                "  left join tb_card_ask_for_leave as cafl on cafl.infoid = ct.uid " +
                "  where wu.docid='" + userId + "' AND wu.status=1 and cafl.uid = '" + formId + "'";
        List<WeiXinUserDto> weiXinUsers = dbService.queryList(sql, WeiXinUserDto.class);
        if (weiXinUsers.size() == 0) {
            return false;
        }
        for (WeiXinUserDto weiXinUser : weiXinUsers) {
            String openid = weiXinUser.getOpenId();
            String wxUid = weiXinUser.getUid();
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxUid);
            data.put("cfgcode", "OPENTM415931101");
            data.put("url", String.format("%s/askforleave/auditLeave2?uid=%s&time=%s", WebConfig.getDomain(), formId, System.currentTimeMillis()));
            JSONObject wxdata = new JSONObject();
            wxdata.put("first", new TemplateMessageData("您收到了" + weiXinUser.getName() + "的请假申请", "#4caf50").toJSONString());
            wxdata.put("keyword1", new TemplateMessageData(weiXinUser.getCreateDate() + ""));
            wxdata.put("keyword2", new TemplateMessageData(weiXinUser.getName()).toJSONString());
            wxdata.put("keyword3", new TemplateMessageData(weiXinUser.getLinkMobile1()));
            wxdata.put("keyword4", new TemplateMessageData(weiXinUser.getStartTime() + "至" + weiXinUser.getEndTime()));
            wxdata.put("keyword5", new TemplateMessageData(weiXinUser.getReason()));
            wxdata.put("remark", new TemplateMessageData("请处理请假请求").toJSONString());
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);
        }
        return true;
    }

    @Data
    public static class WeiXinUserDto {
        private String uid;
        private String openId;
        private String name;
        private String code;
        private Date startTime;
        private Date endTime;
        private Date createDate;
        private String reason;
        private String infoId;
        private String linkMobile1;
        private Integer status;
        private String created_by;
    }

    public JSONObject getFromDataByFormidAndOaType(String formid) {
        String getFromDataByFormidAndOaType = "select cafl.infoname, cafl.reason ,date_format(cafl.starttime,'%Y-%m-%d %H:%i:%s') as starttime ,date_format(cafl.endtime,'%Y-%m-%d %H:%i:%s') as endtime,date_format(cafl.createdate,'%Y-%m-%d %H:%i:%s') as createdate,ct.linkmobile1 from tb_card_ask_for_leave cafl " +
                "left join tb_card_teachstudinfo ct on ct.uid = cafl.infoid where cafl.uid = '" + formid + "'";
        return dbService.QueryJSONObject(getFromDataByFormidAndOaType);
    }

    public JsonData getApprovalRecord(HttpServletRequest request, String formid, int start, int limit) {
        String fields = "ct.name, date_format(war.createdate, '%Y-%m-%d %H:%i') as createdate,war.auditcondition,war.remark ";
        String table = "tb_workflow_approval_record as war  LEFT JOIN tb_card_teachstudinfo as ct on ct.uid = war.auditid";
        String where = "formid = '" + formid + "' ";
        return dbService.QueryJsonData(fields, table, where, "createdate DESC", start, limit);
    }

    public JSONObject getWeiXinDocid(String sql) {
        return dbService.QueryJSONObject(sql);
    }

    @Autowired
    private FaceDetectionService faceDetectionService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    /**
     * 考勤打卡
     */
    public JsonResult checkSignIn(HttpServletRequest request, HttpServletResponse response, double accuracy, String daily, double longitude, double latitude, MultipartFile photoFile, String image) throws IOException {
        String userId = userredis.get(request).getDocid();
        String lockKey = "sign:lock:" + userId;

        // 尝试获取分布式锁（锁定30秒）
        Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", 10, TimeUnit.SECONDS);
        if (locked == null || !locked) {
            return Json.getJsonResult(false, "操作过于频繁，请10秒后再试");
        }

        try {
//            // 检查5分钟内是否已打卡（数据库查询）
//            String checkSql = "SELECT COUNT(*) FROM `tb_time_records` WHERE `infoid` = '" + userId + "' AND `recordtime` > DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
//            Integer recentRecords = dbService.queryOneField(checkSql, Integer.class);
//            if (recentRecords > 0) {
//                return Json.getJsonResult(false, "您已在5分钟内打卡过!!!");
//            }
            if (!checkIn(latitude, longitude, accuracy)) {
                return Json.getJsonResult(false, "未在签到范围内，请在室外打开GPS精准定位重试");
            }
            if (StringUtils.isBlank(image)) {
                return Json.getJsonResult(false, "请先注册照片信息");
            }
            // 上传图片
            if (photoFile == null) {
                return Json.getJsonResult(false, "请选择更新的图片");
            }
            String originalFilename = photoFile.getOriginalFilename();
            String fileExtension = getFileExtension(originalFilename);
            if (!fileExtension.equals("jpg") && !fileExtension.equals("jpeg")) {
                return Json.getJsonResult(false, "图片格式不正确,请上传jpg或jpeg格式的图片");
            }

            String uniqueFilename = generateUniqueFilename(fileExtension);
            //保存图片
            String imgFace = saveFile(photoFile, WebConfig.getUploaddir(), uniqueFilename);
            if (imgFace.length() == 0) {
                Log.error(FaceService.class, "微信端人脸上传：文件保存失败");
                return Json.getJsonResult(false, "文件保存失败");
            }
            if (photoFile.getSize() > 204800) {
                ImageUtil.changeImageWH(WebConfig.getUploaddir() + imgFace, 1024, 1024);
            }
            String uploadDir = WebConfig.getUploaddir();
            if (uploadDir.endsWith("/")) {
                // 去掉最后一个字符
                uploadDir = uploadDir.substring(0, uploadDir.length() - 1);
            }
            //发送人脸验证
            boolean match = faceDetectionService.pf2pMatch(uploadDir + imgFace, uploadDir + image);
            if (!match) {
                return Json.getJsonResult(false, "人脸比对失败");
            }
            String sql = "INSERT INTO `tb_time_records` (`uid`, `infoid`, `infoname`, `infocode`, `orgcode`, `cardno`, " +
                    "`recordtime`, `machineid`, `tiwen`, `ftype`, `isdel`, `createdate`, `isupload`, `status`, `uploadstatus`) " +
                    "select uuid(),uid,name,code,orgcode,card,now(),null,0.0,1,0,now(),?,2,0 from tb_card_teachstudinfo where uid = ? ";
            int excuteSql = dbService.excuteSql(sql, imgFace, userId);
            if (excuteSql > 0) {
                return Json.getJsonResult(true);
            }
        } catch (Exception ex){
            ex.printStackTrace();
        }
        return Json.getJsonResult(false, "打卡失败,请稍后重试");
    }

    public static String saveFile(MultipartFile file, String uploadDir, String filename) throws IOException {
        // 构建目标文件
        File targetFile = new File(uploadDir + "face/", filename);

        // 创建目标文件夹
        if (!targetFile.getParentFile().exists()) {
            targetFile.getParentFile().mkdirs();
        }

        // 保存文件
        try (FileOutputStream fos = new FileOutputStream(targetFile)) {
            fos.write(file.getBytes());
            return "/face/" + filename + "";
        } catch (IOException e) {
            throw new IOException("Failed to save file: " + filename, e);
        }
    }

    private final Logger logger = LoggerFactory.getLogger(TimeService.class);

    public boolean checkIn(double userLatitude, double userLongitude, double accuracy) {
        logger.info(userLatitude + ":" + userLongitude + ":" + accuracy);
        String sql = "select name,attr1 userLatitude ,attr2 userLongitude,attr3 distance from tb_sys_dictionary where groupcode='SYS0000082' and status=1";
        List<Location> locationsList = dbService.queryList(sql, Location.class);
        boolean flag = false;
        for (Location locations : locationsList) {
            double distance = GeoUtils.getDistance(userLatitude, userLongitude, locations.getUserLatitude(), locations.getUserLongitude());
            logger.info("相差距离：{}",distance);
            if (distance <= locations.getDistance() + accuracy) { // 判断是否在100米范围内
                // 签到成功，记录签到信息
                flag = true;
            }
        }
        return flag;
    }

    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return (lastDotIndex == -1) ? "" : filename.substring(lastDotIndex + 1);
    }

    private String generateUniqueFilename(String fileExtension) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid + "." + fileExtension;
    }

}
