package com.ymiots.campusmp.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.config.redis.RedisClient;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.weixinwork.WorkUserService;
import com.ymiots.campusmp.utils.OkHttpUtil;
import com.ymiots.campusmp.utils.weixin.entity.OAuthUserInfo;
import com.ymiots.framework.common.*;
import com.ymiots.framework.datafactory.CallbackTransaction;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.MessageDigest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Repository
public class OAuthService extends BaseService {

    @Autowired
    UserRedis userredis;

    @Autowired
    WorkUserService workuser;

    @Autowired
    private VisitorService visitorService;

    @Autowired
    RedisClient redisclient;

    @Autowired
    SysConfigService syscfg;

    @Autowired
    private RedisTemplate<String, Boolean> redisTemplate;

    @Autowired
    private OkHttpUtil okHttpUtil;

    public SysUser getSysUserByMobilePwd(HttpServletRequest request, HttpServletResponse response, String mobile, String pwd) {
        // 1学生本人
        JSONArray userlist = dbService.QueryList(
                "select uid as docid, code,name,sex,orgcode,getorgname(orgcode) as orgname,1 as usertype,status from tb_card_teachstudinfo where mobile='"
                        + mobile + "' and passpassword='" + pwd + "' and infotype=2 ");
        if (userlist.size() == 0) {
            Log.error(OAuthService.class, "根据手机号和密码无法找到档案信息");
            return null;
        }
        JSONObject useritem = userlist.getJSONObject(0);

        String docid = useritem.getString("docid");
        String sql = "select uid,openid,headimgurl,nickname,sex,usertype,docid,idcode,status from tb_weixin_user where docid='"
                + docid + "' and usertype=1";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            Log.error(OAuthService.class, "档案信息未绑定手机微信，禁止登录");
            return null;
        }
        JSONObject item = list.getJSONObject(0);

        SysUser user = new SysUser();
        user.setCode(useritem.getString("code"));
        user.setDocid(useritem.getString("docid"));
        user.setOpenid(item.getString("openid"));
        user.setIdcode(item.getString("idcode"));
        user.setOrgcode(useritem.getString("orgcode"));
        user.setOrgname(useritem.getString("orgname"));
        user.setName(useritem.getString("name"));
        user.setNickname(item.getString("nickname"));
        user.setSex(useritem.getString("sex"));
        user.setStatus(useritem.getIntValue("status"));
        user.setTokenid(UUID.randomUUID().toString());
        user.setUid(item.getString("uid"));
        user.setUsertype(1);
        user.setHeadimgurl(item.getString("headimgurl"));
        userredis.add(request, response, user);
        return user;
    }

    /**
     * 根据调试代码获取在线用户身份
     *
     * @param response
     * @param weixinuserid
     * @return
     */
    public SysUser getOAuthSysUser(HttpServletRequest request, HttpServletResponse response, String weixinuserid) {
        String sql = "select uid,openid,headimgurl,nickname,sex,usertype,docid,idcode,status,parent_id from tb_weixin_user where uid='"
                + weixinuserid + "'";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            return null;
        } else {
            JSONObject item = list.getJSONObject(0);
            if (item.getIntValue("status") == 0) {
                Log.error(OAuthService.class, "用户微信登录已被禁用");
                return null;
            }

            //sql = "update tb_weixin_user set lastlogintime=now(),logintimes=logintimes+1 where uid=?";
            //dbService.excuteSql(sql, weixinuserid);

            // 0前端管理员,1学生本人,2关注者（验证学生联系人信息绑定的）,3教职工本人，4访客
            int usertype = item.getIntValue("usertype");
            String docid = item.getString("docid");
            JSONArray userlist = new JSONArray();
            if (usertype == 0) {
                userlist = dbService.QueryList(
                        "select uid as docid, code,name,orgcode,getorgname(orgcode) as orgname,0 as usertype,status from tb_card_teachstudinfo where uid='"
                                + docid + "' ");
            } else if (usertype == 1 || usertype == 2) {
                userlist = dbService.QueryList("select uid as docid, code,name,orgcode,getorgname(orgcode) as orgname,"
                        + String.valueOf(usertype) + " as usertype,status from tb_card_teachstudinfo where uid='"
                        + docid + "' and infotype=2");
            } else if (usertype == 3) {
                userlist = dbService.QueryList(
                        "select uid as docid, code,name,orgcode,getorgname(orgcode) as orgname,3 as usertype,status from tb_card_teachstudinfo where uid='"
                                + docid + "' and infotype=1 ");
            } else if (usertype == 4) {
                userlist = dbService.QueryList(
                        "SELECT uid as docid,'' as code,name,'' as orgcode,company as orgname,4 as usertype,1 as status FROM tb_visitor_visitorinfo where uid='"
                                + docid + "' ");
            }
            if (userlist.size() == 0) {
                return null;
            }
            JSONObject useritem = userlist.getJSONObject(0);
            SysUser user = new SysUser();
            user.setCode(useritem.getString("code"));
            user.setDocid(useritem.getString("docid"));
            user.setParentId(item.getString("parent_id"));
            user.setOpenid(item.getString("openid"));
            user.setIdcode(item.getString("idcode"));
            user.setOrgcode(useritem.getString("orgcode"));
            user.setOrgname(useritem.getString("orgname"));
            user.setName(useritem.getString("name"));
            user.setNickname(item.getString("nickname"));
            user.setSex(item.getString("sex"));
            user.setStatus(useritem.getIntValue("status"));
            user.setTokenid(UUID.randomUUID().toString());
            user.setUid(item.getString("uid"));
            user.setUsertype(usertype);
            user.setHeadimgurl(item.getString("headimgurl"));
            userredis.add(request, response, user);
            return user;
        }
    }

    /**
     * 根据微信授权用户信息自动登录
     *
     * @param response
     * @param userinfo
     * @return
     */
    public SysUser getOAuthSysUser(HttpServletRequest request, HttpServletResponse response, OAuthUserInfo userinfo) {
        String sql = "select uid,openid,headimgurl,nickname,sex,usertype,docid,idcode,orgcode,status,parent_id from tb_weixin_user where openid='"
                + userinfo.getOpenid() + "' order by lastlogintime desc";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            Log.error(OAuthService.class, "微信用户信息未绑定");
            return null;
        } else {
            JSONObject item = list.getJSONObject(0);
            if (item.getIntValue("status") == 0) {
                Log.error(OAuthService.class, "用户微信登录已被禁用");
                return null;
            }

//            sql = "update tb_weixin_user set lastlogintime=now(),nickname=?,sex=?,logintimes=logintimes+1,headimgurl=? where uid=?";
//            dbService.excuteSql(sql, EmojiUtils.filterEmoji(userinfo.getNickname()), userinfo.getSex(),
//                    userinfo.getHeadimgurl(), item.getString("uid"));

            // 0前端管理员,1学生本人,2关注者（验证学生联系人信息绑定的）,3教职工本人
            int usertype = item.getIntValue("usertype");
            String docid = item.getString("docid");
            String parentid = item.getString("parent_id");
            JSONArray userlist = new JSONArray();
            if (usertype == 0) {
                userlist = dbService.QueryList(
                        "select uid as docid, code,name,orgcode,getorgname(orgcode) as orgname,0 as usertype,status from tb_card_teachstudinfo where uid='"
                                + docid + "' ");
            } else if (usertype == 1 || usertype == 2) {
                userlist = dbService.QueryList("select uid as docid, code,name,orgcode,getorgname(orgcode) as orgname,"
                        + String.valueOf(usertype) + " as usertype,status from tb_card_teachstudinfo where uid='"
                        + docid + "' and infotype=2 ");
            } else if (usertype == 3) {
                userlist = dbService.QueryList(
                        "select uid as docid, code,name,orgcode,getorgname(orgcode) as orgname,3 as usertype,status from tb_card_teachstudinfo where uid='"
                                + docid + "' and infotype=1 ");
            } else if (usertype == 4) {
                userlist = dbService.QueryList(
                        "SELECT uid as docid,'' as code,name,'' as orgcode,company,4 as usertype,1 as status FROM tb_visitor_visitorinfo where uid='"
                                + docid + "' ");
            } else if (usertype == 5) {
                //免密登录者
                SysUser user = new SysUser();
                user.setOpenid(item.getString("openid"));
                user.setIdcode(item.getString("idcode"));
                user.setOrgcode(item.getString("orgcode"));
                user.setNickname(EmojiUtils.filterEmoji(userinfo.getNickname()));
                user.setSex(userinfo.getSex());
                user.setStatus(1);
                user.setTokenid(UUID.randomUUID().toString());
                user.setUid(item.getString("uid"));
                user.setUsertype(usertype);
                user.setHeadimgurl(userinfo.getHeadimgurl());
                userredis.add(request, response, user);
                return user;
            }
            if (userlist.size() == 0) {
                Log.error(OAuthService.class, "用户档案信息不存在");
                return null;
            }
            JSONObject useritem = userlist.getJSONObject(0);
            SysUser user = new SysUser();
            user.setCode(useritem.getString("code"));
            user.setParentId(parentid);
            user.setDocid(useritem.getString("docid"));
            user.setOpenid(item.getString("openid"));
            user.setIdcode(item.getString("idcode"));
            user.setOrgcode(useritem.getString("orgcode"));
            user.setOrgname(useritem.getString("orgname"));
            user.setName(useritem.getString("name"));
            user.setNickname(EmojiUtils.filterEmoji(userinfo.getNickname()));
            user.setSex(userinfo.getSex());
            user.setStatus(useritem.getIntValue("status"));
            user.setTokenid(UUID.randomUUID().toString());
            user.setUid(item.getString("uid"));
            user.setUsertype(usertype);
            user.setHeadimgurl(userinfo.getHeadimgurl());
            userredis.add(request, response, user);

            return user;
        }
    }

    /**
     * 获取redis验证码，然后和表单提交上来的验证码比较
     *
     * @param mobile
     * @return
     */
    public Boolean checkmobilenum(String mobile, String mobilecode) {
        if (!redisclient.hasStringKey("vc" + mobile)) {
            return false;
        }
        String vcode = redisclient.getString("vc" + mobile);
        if (!vcode.equals(mobilecode)) {
            return false;
        }
        return true;
    }

    /**
     * 跳过鉴权直接进入系统
     */
    public SysUser getOAuthSysUser(HttpServletRequest request, HttpServletResponse response, OAuthUserInfo userinfo, String passwordFreeLogin) {
        String sql = "select uid,openid,headimgurl,nickname,usertype,status from tb_weixin_user where openid='"
                + userinfo.getOpenid() + "' order by lastlogintime desc";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            Log.error(OAuthService.class, "微信用户信息未绑定");
            return null;
        } else {
            JSONObject item = list.getJSONObject(0);
            if (item.getIntValue("status") == 0) {
                Log.error(OAuthService.class, "用户微信登录已被禁用");
                return null;
            }

//            sql = "update tb_weixin_user set lastlogintime=now(),nickname=?,sex=?,logintimes=logintimes+1,headimgurl=? where uid=?";
//            dbService.excuteSql(sql, EmojiUtils.filterEmoji(userinfo.getNickname()), userinfo.getSex(),
//                    userinfo.getHeadimgurl(), item.getString("uid"));

            // 0前端管理员,1学生本人,2关注者（验证学生联系人信息绑定的）,3教职工本人
            int usertype = item.getIntValue("usertype");
            String docid = item.getString("docid");
            JSONArray userlist = new JSONArray();
            if (usertype == 0) {
                userlist = dbService.QueryList(
                        "select uid as docid, code,name,orgcode,getorgname(orgcode) as orgname,0 as usertype,status from tb_card_teachstudinfo where uid='"
                                + docid + "' ");
            } else if (usertype == 1 || usertype == 2) {
                userlist = dbService.QueryList("select uid as docid, code,name,orgcode,getorgname(orgcode) as orgname,"
                        + String.valueOf(usertype) + " as usertype,status from tb_card_teachstudinfo where uid='"
                        + docid + "' and infotype=2 ");
            } else if (usertype == 3) {
                userlist = dbService.QueryList(
                        "select uid as docid, code,name,orgcode,getorgname(orgcode) as orgname,3 as usertype,status from tb_card_teachstudinfo where uid='"
                                + docid + "' and infotype=1 ");
            } else if (usertype == 4) {
                userlist = dbService.QueryList(
                        "SELECT uid as docid,'' as code,name,'' as orgcode,company,4 as usertype,1 as status FROM tb_visitor_visitorinfo where uid='"
                                + docid + "' ");
            }
            if (userlist.size() == 0) {
                Log.error(OAuthService.class, "用户档案信息不存在");
                return null;
            }
            JSONObject useritem = userlist.getJSONObject(0);
            SysUser user = new SysUser();
            user.setCode(useritem.getString("code"));
            user.setDocid(useritem.getString("docid"));
            user.setOpenid(item.getString("openid"));
            user.setIdcode(item.getString("idcode"));
            user.setOrgcode(useritem.getString("orgcode"));
            user.setOrgname(useritem.getString("orgname"));
            user.setName(useritem.getString("name"));
            user.setNickname(EmojiUtils.filterEmoji(userinfo.getNickname()));
            user.setSex(userinfo.getSex());
            user.setStatus(useritem.getIntValue("status"));
            user.setTokenid(UUID.randomUUID().toString());
            user.setUid(item.getString("uid"));
            user.setUsertype(usertype);
            user.setHeadimgurl(userinfo.getHeadimgurl());
            userredis.add(request, response, user);

            return user;
        }
    }

    /**
     * 绑定身份
     *
     * @param request
     * @param response
     * @param openid
     * @param usertype
     * @param name
     * @param linkname
     * @param linktel
     * @param code
     * @param pwd
     * @param headimgurl
     * @param nickname
     * @param sex
     * @return
     */
    public JsonResult WeiXinBind(HttpServletRequest request, HttpServletResponse response, String openid, int usertype,
                                 String name, String vcode, String infocode, String mobile, String linkname, String linktel, String code, String pwd,
                                 String headimgurl, String nickname, String sex) {
        JSONArray userlist = new JSONArray();
        String idcode = "";
        String isVerificationCode = syscfg.get("isVerificationCode");
        if ("1".equals(isVerificationCode)) {
            if (!checkmobilenum(mobile, vcode)) {
                return Json.getJsonResult(false, "验证码错误");
            }
        }
        if (usertype == 0) {
            idcode = code;
            String sql = "select i.uid as docid, i.code,i.name,i.orgcode,getorgname(i.orgcode) as orgname,0 as usertype,i.status " +
                    "from tb_card_teachstudinfo i inner join tb_sys_user u on u.empid=i.uid" +
                    " where u.code=? and u.password=? and u.usertype=0 ";
            userlist = dbService.QueryList(sql, code, MD5.MD5Encode(pwd));

        } else if (usertype == 1) {
            // 学生
            idcode = code;
            String sql = "select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,1 as usertype,status from tb_card_teachstudinfo " +
                    "where name=? and code=? and infotype=2 ";
            userlist = dbService.QueryList(sql, name, code);
        } else if (usertype == 2) {
            // 家长 需要学生信息中填写过家长信息和手机号
            idcode = linktel;
            String sql = "select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,2 as usertype,status from tb_card_teachstudinfo " +
                    "where name=? and code=? and infotype=2 ";
            userlist = dbService.QueryList(sql, name, code);
        } else if (usertype == 3) {
            // 教职工
            String sql = "select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,3 as usertype,status from tb_card_teachstudinfo " +
                    "where name=? and mobile=? and infotype=1 ";
            userlist = dbService.QueryList(sql, name, mobile);
        }
        JsonResult result = WeiXinBindUser(request, response, userlist, openid, usertype, idcode, headimgurl, nickname, sex, pwd);
        return result;
    }

    public JsonResult weChatLogin(HttpServletRequest request, HttpServletResponse response, String openId, int userType,
                                  String name, String vCode, String infoCode, String mobile, String linkName, String linkTel, String code, String pwd,
                                  String headImgUrl, String nickName, String sex) {
        String inputCaptcha = request.getParameter("captcha").toLowerCase();
        String sessionCaptcha = ((String) request.getSession().getAttribute("captcha")).toLowerCase();
        String sql = "select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,3 as usertype,status from tb_card_teachstudinfo " +
                "where name=? and mobile=? and infotype=1 ";
        JSONArray userJsonArr = dbService.QueryList(sql, name, mobile);

        if (!sessionCaptcha.equals(inputCaptcha)) {
            // 验证失败，返回错误信息
            return Json.getJsonResult(false, "验证码错误");
        }

        // 验证成功，处理登录逻辑
        if (userType == 5) {
            //插入人事档案表发卡
            String outsideDep = syscfg.get("outsideDep");
            if (StringUtils.isBlank(outsideDep)) {
                return Json.getJsonResult(false, "外来人员注册未开放");
            }
            if (userJsonArr.size() == 0) {
                String card = ComHelper.LeftPad(mobile.substring(0, 10), 12, '0');
                int cardCount = dbService.getCount("tb_card_cardinfo", "cardno='" + card + "' AND status=1");
                if (cardCount > 0) {
                    return Json.getJsonResult(false, "卡号已存在！");
                }
                int count = dbService.getCount("tb_card_teachstudinfo", "code='" + card + "' ");
                if (count > 0) {
                    return Json.getJsonResult("工号已存在");
                }
                String info_Id = UUID.randomUUID().toString();
                String saveInfoSql = "insert into tb_card_teachstudinfo(uid,code,name,sex,mobile,orgcode,nation,birthday,address,card,cardsn,position,posrank,startdate,enddate,email,idcard,infotype,intoyear,linkman1,linkmobile1,linkdes1,linkman2,linkmobile2,linkdes2,focus,plateno,yunfei,volume,passpassword,createdate,creatorid,modifydate,modifierid,status,endsisdaily,infolabel,property) " +
                        "values(?,?,?,?,?,?,?,?,?,?,?,?,?," +
                        "null," +
                        "null," +
                        "?,?,1,?,?,?,?,?,?,?,0,?,?,?,?,now(),?,now(),?,1,99999999,?,?)";
                dbService.excuteSql(saveInfoSql, info_Id, mobile, name, sex+1, mobile, outsideDep, null, null, null, card, card, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "vip用户", null);
                if (StringUtils.isNotBlank(card)) {
                    String cardId = UUID.randomUUID().toString();
                    String str = "insert into tb_card_cardinfo(uid,infoid,usedes,cardno,cardsn,deposit,ismain,balance,vicewallet,waterwallet,timescount,enddate,createdate,creatorid,modifydate,modifierid,status) values " +
                            "('" + cardId + "','" + info_Id + "','本人', '" + card + "', '" + card + "',0,1,0,0,0,0,'2050-01-01',now(),'" + "微信公众号" + "',now(),'" + "微信公众号" + "',1)";
                    dbService.excuteSql(str);
                    String str2 = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,des,createdate,creatorid) values" +
                            "(uuid(),'" + info_Id + "','" + cardId + "','" + card + "','" + card + "',0,1,'本人','" + "微信公众号" + "','0','微信公众号创建发卡',now(),'" + "微信公众号" + "')";
                    dbService.excuteSql(str2);
                    String dopsql = "update tb_card_cardinfo_oprecord op inner join tb_card_teachstudinfo info on info.uid=op.infoid set op.infocode=info.code,op.infoname=info.name,op.orgname=getorgname(info.orgcode) where op.infocode='0' ";
                    dbService.excuteSql(dopsql);
                }
                userJsonArr = dbService.QueryList(sql, name, mobile);
            }
            JSONObject jsonObject = (JSONObject) userJsonArr.get(0);
            return saveWeChatLogin(request, response, openId, userType, name, mobile, code, headImgUrl, nickName, sex, outsideDep, jsonObject.getString("docid"));
        } else {
            return WeiXinBindUser(request, response, userJsonArr, openId, userType, infoCode, headImgUrl, nickName, sex, pwd);
        }
    }

    @NotNull
    private JsonResult saveWeChatLogin(HttpServletRequest request, HttpServletResponse response, String openId, int userType, String name, String mobile, String code, String headImgUrl, String nickName, String sex, String outsideDep, String info_Id) {
        int countWeChat = dbService.getCount("tb_weixin_user", "docid='" + info_Id + "' and usertype=" + userType + " ");
        if (countWeChat > 1) {
            return Json.getJsonResult("关注人数已达上限");
        }
        countWeChat = dbService.getCount("tb_weixin_user", "docid='" + info_Id + "' and openid='" + openId + "'");
        if (countWeChat >= 1) {
            return Json.getJsonResult(true);
        }
        String userid = UUID.randomUUID().toString();
        String saveWeChatSql = "insert into tb_weixin_user(uid,openid,usertype,docid,idcode,headimgurl,nickname,sex,lastlogintime,logintimes,status,createdate) values(?,?,?,?,?,?,?,?,now(),1,1,now())";
        dbService.excuteSql(saveWeChatSql, userid, openId, userType, info_Id, code, headImgUrl, nickName, sex);
        SysUser user = new SysUser();
        user.setCode(mobile);
        user.setDocid(info_Id);
        user.setOpenid(openId);
        user.setIdcode(code);
        user.setOrgcode(outsideDep);
        user.setOrgname("vip人员");
        user.setName(name);
        user.setNickname(nickName);
        user.setSex(sex);
        user.setStatus(1);
        user.setTokenid(UUID.randomUUID().toString());
        user.setUid(userid);
        user.setUsertype(5);
        user.setHeadimgurl(headImgUrl);
        userredis.add(request, response, user);
        return Json.getJsonResult(true);
    }

    public JsonResult WeiXinBind1(HttpServletRequest request, HttpServletResponse response, String openid, int usertype,
                                  String name, String vcode, String idcard, String mobile, String linkname, String linktel, String code, String pwd,
                                  String headimgurl, String nickname, String sex) {
        String isVerificationCode = syscfg.get("isVerificationCode");
        if ("1".equals(isVerificationCode)) {
            if (!checkmobilenum(mobile, vcode)) {
                return Json.getJsonResult(false, "验证码错误");
            }
        }
        JSONArray userlist = dbService.QueryList(
                "select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,1 as usertype,status from tb_card_teachstudinfo where name='"
                        + name + "' and idcard='" + idcard + "' and infotype=2 ");
        JsonResult result = WeiXinBindUser(request, response, userlist, openid, usertype, mobile, headimgurl, nickname, sex, pwd);
        return result;
    }

    public JsonResult WeiXinBind2(HttpServletRequest request, HttpServletResponse response, String openid, int usertype,
                                  String name, String vcode, String idcard, String mobile, String linkname, String linktel, String code, String pwd,
                                  String headimgurl, String nickname, String sex) {
        String isVerificationCode = syscfg.get("isVerificationCode");
        if ("1".equals(isVerificationCode)) {
            if (!checkmobilenum(mobile, vcode)) {
                return Json.getJsonResult(false, "验证码错误");
            }
        }
        JSONArray userlist = dbService.QueryList(
                "select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,1 as usertype,status from tb_card_teachstudinfo where name='"
                        + name + "' and mobile='" + mobile + "' and infotype=2 ");
        JsonResult result = WeiXinBindUser(request, response, userlist, openid, usertype, mobile, headimgurl, nickname, sex, pwd);
        return result;
    }

    public JsonResult WeiXinBind3(HttpServletRequest request, HttpServletResponse response, String openid, int usertype,
                                  String name, String vcode, String idcard, String mobile, String linkname, String linktel, String code, String pwd,
                                  String headimgurl, String nickname, String sex) {
        String isVerificationCode = syscfg.get("isVerificationCode");
        if ("1".equals(isVerificationCode)) {
            if (!checkmobilenum(mobile, vcode)) {
                return Json.getJsonResult(false, "验证码错误");
            }
        }
        JSONArray userlist = dbService.QueryList(
                "select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,1 as usertype,status from tb_card_teachstudinfo where name='"
                        + name + "' and code='" + code + "' and infotype=1 ");
        JsonResult result = WeiXinBindUser(request, response, userlist, openid, usertype, mobile, headimgurl, nickname, sex, pwd);
        return result;
    }

    public JsonResult WeiXinBindUser(HttpServletRequest request, HttpServletResponse response, JSONArray userlist,
                                     String openid, int usertype, String idcode, String headimgurl, String nickname, String sex, String pwd) {
        if (userlist.size() == 0) {
            return Json.getJsonResult("身份信息错误");
        }
        JSONObject item = userlist.getJSONObject(0);
        String docid = item.getString("docid");
        int count = dbService.getCount("tb_weixin_user", "docid='" + docid + "' and usertype=" + String.valueOf(usertype) + " ");
        String pwdSql = "select mp_password as password from tb_card_teachstudinfo where uid = '" + docid + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(pwdSql);
        String passWord = syscfg.get("defaultPassWord");
        if (StringUtils.isNotBlank(passWord)) {
            if (jsonObject != null) {
                String password = jsonObject.getString("password");
                if (password == null || password.equals("")) {
                    if (!passWord.equals(pwd)) {
                        return Json.getJsonResult("密码错误");
                    }
                } else {
                    if (!pwd.equals(password)) {
                        return Json.getJsonResult("密码错误");
                    }
                }
            } else {
                if (!passWord.equals(pwd)) {
                    return Json.getJsonResult("密码错误");
                }
            }
        }
        if ((usertype == 2 || usertype == 3) && "1".equals(WebConfig.getApptype())) {
            if (count >= 3) {
                return Json.getJsonResult("关注人数已达上限");
            }
        } else {
            if (count >= 1) {
                return Json.getJsonResult("其他微信已绑定");
            }
        }
        count = dbService.getCount("tb_weixin_user", "docid='" + docid + "' and openid='" + openid + "'");
        if (count >= 1) {
            return Json.getJsonResult("请勿重复绑定");
        }

        String userid = UUID.randomUUID().toString();
        String sql = "insert into tb_weixin_user(uid,openid,usertype,docid,idcode,headimgurl,nickname,sex,lastlogintime,logintimes,status,createdate) values(?,?,?,?,?,?,?,?,now(),1,1,now())";
        dbService.excuteSql(sql, userid, openid, usertype, docid, idcode, headimgurl, nickname, sex);
        SysUser user = new SysUser();
        user.setCode(item.getString("code"));
        user.setDocid(docid);
        user.setOpenid(openid);
        user.setIdcode(idcode);
        user.setOrgcode(item.getString("orgcode"));
        user.setOrgname(item.getString("orgname"));
        user.setName(item.getString("name"));
        user.setNickname(nickname);
        user.setSex(sex);
        user.setStatus(item.getIntValue("status"));
        user.setTokenid(UUID.randomUUID().toString());
        user.setUid(userid);
        user.setUsertype(usertype);
        user.setHeadimgurl(headimgurl);
        userredis.add(request, response, user);
        return Json.getJsonResult(true, jsonObject);
    }

    public void WeiXinBindUserNoLogin(HttpServletRequest request, HttpServletResponse response, String openid, int usertype, String headimgurl, String nickname, String sex, String orgCode) {
        int count = dbService.getCount("tb_weixin_user", "openid='" + openid + "' and orgcode = '" + orgCode + "'");
        if (count >= 1) {
            return;
        }
        String userid = UUID.randomUUID().toString();
        String sql = "insert into tb_weixin_user(uid,openid,usertype,docid,idcode,headimgurl,nickname,sex,lastlogintime,logintimes,status,orgcode,createdate) values(?,?,?,?,?,?,?,?,now(),1,1,?,now())";
        dbService.excuteSql(sql, userid, openid, usertype, null, null, headimgurl, nickname, sex, orgCode);
        SysUser user = new SysUser();
        user.setOpenid(openid);
        user.setOrgcode(orgCode);
        user.setNickname(nickname);
        user.setSex(sex);
        user.setStatus(1);
        user.setTokenid(UUID.randomUUID().toString());
        user.setUid(userid);
        user.setUsertype(usertype);
        user.setHeadimgurl(headimgurl);
        userredis.add(request, response, user);
        Json.getJsonResult(true);
    }

    public JsonResult WeiXinVisitorBind(HttpServletRequest request, HttpServletResponse response, final String name,
                                        final String mobile, final String idtype, final String idcard, final String company, final String position,
                                        final String department, final String openid, final int usertype, final String headimgurl,
                                        final String nickname, final String sex, final String visitorsex, final String plateno) {

        try {
            final StringBuffer visitoridx = new StringBuffer();
            final String userid = UUID.randomUUID().toString();
            if (mobile.length() != 11) {
                return Json.getJsonResult(false, "手机号码不正确");
            }
//            if (idcard.length() != 18) {
//                return Json.getJsonResult(false, "身份证号码不正确");
//            }
            dbService.excuteTransaction(new CallbackTransaction() {
                @Override
                public void execute(Connection connection) throws SQLException {
                    JSONArray list = dbService.QueryList("select uid from tb_visitor_visitorinfo where idcard='" + idcard + "'");
                    if (list.size() > 0) {
                        visitoridx.append(list.getJSONObject(0).getString("uid"));
                        String updateSQL = "UPDATE tb_visitor_visitorinfo SET name=?, sex=?, mobile=?, company=?, position=?, department=?, plateno=? WHERE uid=?";
                        PreparedStatement ps = connection.prepareStatement(updateSQL);
                        ps.setString(1, name);
                        ps.setString(2, visitorsex);
                        ps.setString(3, mobile);
                        ps.setString(4, company);
                        ps.setString(5, position);
                        ps.setString(6, department);
                        ps.setString(7, plateno);
                        ps.setString(8, visitoridx.toString());
                        ps.executeUpdate();
                        ps.close();
                    } else {
                        visitoridx.append(UUID.randomUUID().toString());
                        String inserSQL = "INSERT INTO tb_visitor_visitorinfo(uid, name, idtype, idcard, sex, mobile, company, position, department, plateno, createdate, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now(), 1)";
                        PreparedStatement ps = connection.prepareStatement(inserSQL);
                        ps.setString(1, visitoridx.toString());
                        ps.setString(2, name);
                        ps.setString(3, idtype);
                        ps.setString(4, idcard);
                        ps.setString(5, visitorsex);
                        ps.setString(6, mobile);
                        ps.setString(7, company);
                        ps.setString(8, position);
                        ps.setString(9, department);
                        ps.setString(10, plateno);
                        ps.executeUpdate();
                        ps.close();
                    }
                    String sql = "insert into tb_weixin_user(uid,openid,usertype,docid,idcode,headimgurl,nickname,sex,lastlogintime,logintimes,status,createdate) values(?,?,?,?,?,?,?,?,now(),1,1,now())";
                    PreparedStatement ps = connection.prepareStatement(sql);
                    ps.setString(1, userid);
                    ps.setString(2, openid);
                    ps.setInt(3, usertype);
                    ps.setString(4, visitoridx.toString());
                    ps.setString(5, idcard);
                    ps.setString(6, headimgurl);
                    ps.setString(7, nickname);
                    ps.setString(8, sex);
                    ps.executeUpdate();
                    ps.close();
                }
            });

            SysUser user = new SysUser();
            user.setCode("");
            user.setDocid(visitoridx.toString());
            user.setOpenid(openid);
            user.setIdcode(idcard);
            user.setOrgcode("");
            user.setOrgname(company);
            user.setName(name);
            user.setNickname(nickname);
            user.setSex(sex);
            user.setStatus(1);
            user.setTokenid(UUID.randomUUID().toString());
            user.setUid(userid);
            user.setUsertype(4);
            user.setHeadimgurl(headimgurl);
            userredis.add(request, response, user);


            return Json.getJsonResult(true);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 新增绑定
     *
     * @param request
     * @param response
     * @param code
     * @param name
     * @return
     */
    public JsonResult AddWeiXinBind(HttpServletRequest request, HttpServletResponse response, String code, String name, String pwd) throws Exception {
        SysUser userinfo = userredis.get(request);
        if (userinfo.getUsertype() != 2 && userinfo.getUsertype() != 3) {
            return Json.getJsonResult("禁止增加绑定");
        }
        JSONArray userlist = new JSONArray();
        String idcode = "";
        String userUid = userinfo.getDocid();

        if (userinfo.getUsertype() == 2) {
            idcode = userinfo.getIdcode();
            userlist = dbService.QueryList(
                    "select uid,code,name,status from tb_card_teachstudinfo where name='" + name + "' and infotype=2 and code ='" + code + "'");
        } else if (userinfo.getUsertype() == 3) {
            JSONArray doclist = dbService.QueryList("select name,mobile from tb_card_teachstudinfo where uid='" + userUid + "'");
            if (doclist.size() == 0) {
                return Json.getJsonResult("学号信息错误");
            }
            idcode = doclist.getJSONObject(0).getString("mobile");
            if (StringUtils.isBlank(idcode)) {
                return Json.getJsonResult("职工未完善手机号，无法绑定学生");
            }
            userlist = dbService.QueryList(
                    "select uid,code,name,status from tb_card_teachstudinfo where name='" + name + "' and infotype=2 and code ='" + code + "'");
        }
        if (userlist.size() == 0) {
            return Json.getJsonResult("学号信息错误");
        }
        JSONObject item = userlist.getJSONObject(0);
        String docid = item.getString("uid");
        String queryParentId = "select parent_id from tb_weixin_user where docid='" + userUid + "' and usertype=2 ";
        List<String> infoList = dbService.queryFields(queryParentId, String.class);
        String infoId;
        if (CollectionUtil.isEmpty(infoList)) {
            infoId = null;
        } else {
            infoId = infoList.get(0);

        }
        int count = dbService.getCount("tb_weixin_user", "docid='" + docid + "' and usertype=2 ");

        if (count >= 3) {
            return Json.getJsonResult("关注人数已达上限");
        }

        count = dbService.getCount("tb_weixin_user", "docid='" + docid + "' and openid='" + userinfo.getOpenid() + "'");
        if (count >= 1) {
            return Json.getJsonResult("请勿重复绑定");
        }
        String pwdSql = "select mp_password as password from tb_card_teachstudinfo where uid = '" + docid + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(pwdSql);
        String passWord = syscfg.get("defaultPassWord");
        if (StringUtils.isNotBlank(passWord)) {
            if (jsonObject != null) {
                String password = jsonObject.getString("password");
                if (password == null || password.equals("")) {
                    if (!passWord.equals(pwd)) {
                        return Json.getJsonResult("密码错误");
                    }
                } else {
                    if (!pwd.equals(password)) {
                        return Json.getJsonResult("密码错误");
                    }
                }
            } else {
                if (!passWord.equals(pwd)) {
                    return Json.getJsonResult("密码错误");
                }
            }
        }
            String finalIdcode = idcode;
            dbService.excuteTransaction(new CallbackTransaction() {
                @Override
                public void execute(Connection connection) throws SQLException {
                    // 插入tb_weixin_user表的操作
                    String insertWeixinUserSql = "insert into tb_weixin_user(uid,openid,usertype,docid,idcode,headimgurl,nickname,sex,lastlogintime,logintimes,status,createdate,parent_id) values(uuid(),?,?,?,?,?,?,?,now(),1,1,now(),?)";
                    PreparedStatement insertWeixinUserPs = connection.prepareStatement(insertWeixinUserSql);
                    insertWeixinUserPs.setString(1, userinfo.getOpenid());
                    insertWeixinUserPs.setInt(2, 2);
                    insertWeixinUserPs.setString(3, docid);
                    insertWeixinUserPs.setString(4, finalIdcode);
                    insertWeixinUserPs.setString(5, userinfo.getHeadimgurl());
                    insertWeixinUserPs.setString(6, userinfo.getNickname());
                    insertWeixinUserPs.setString(7, userinfo.getSex());
                    insertWeixinUserPs.setString(8, infoId);
                    insertWeixinUserPs.executeUpdate();
                    insertWeixinUserPs.close();

                    // 插入tb_card_parentinfo表的操作
                    String insertCardParentInfoSql = "insert into tb_card_parentinfo(parent_id,student_id,create_dt) VALUES(?,?,now())";
                    PreparedStatement insertCardParentInfoPs = connection.prepareStatement(insertCardParentInfoSql);
                    insertCardParentInfoPs.setString(1, infoId);
                    insertCardParentInfoPs.setString(2, docid);
                    insertCardParentInfoPs.executeUpdate();
                    insertCardParentInfoPs.close();
                }
            });
        return Json.getJsonResult(true);
    }

    public JsonResult WeiXinDisBind(HttpServletRequest request, HttpServletResponse response, String uid) {
        String sql = "delete from tb_weixin_user where uid=?";
        dbService.excuteSql(sql, uid);
//        //删除绑定人员相关联具体信息
//        String delSql = "delect from tb_card_teachstudinfo_detail where uid = ?";
//        dbService.excuteSql(delSql, uid);
        userredis.remove(request);
        return Json.getJsonResult(true);
    }

    public SysUser getSysUserBySysUser(HttpServletRequest request, HttpServletResponse response, String code, String pwd) {
        // 2系统账号和密码
        pwd = MD5.MD5Encode(pwd);
        JSONArray userlist = dbService.QueryList(
                "SELECT uid,code,name,empid FROM tb_sys_user where code='" + code + "' and password='" + pwd + "'; ");
        if (userlist.size() == 0) {
            Log.error(OAuthService.class, "账号或密码错误");
            return null;
        }
        JSONObject useritem = userlist.getJSONObject(0);

        JSONArray list = new JSONArray();
        JSONArray wxlist = new JSONArray();
        String docid = useritem.getString("empid");
        if (!StringUtils.isBlank(docid)) {
            String sql = "select uid,code,name,sex,orgcode,getorgname(orgcode) as orgname from tb_card_teachstudinfo where uid='"
                    + docid + "' ";
            list = dbService.QueryList(sql);

            sql = "select uid,openid,headimgurl,nickname,sex,usertype,docid,idcode,status from tb_weixin_user where docid='"
                    + docid + "' ";
            wxlist = dbService.QueryList(sql);
        }

        SysUser user = new SysUser();
        user.setCode(useritem.getString("code"));
        user.setDocid(useritem.getString("empid"));
        user.setName(useritem.getString("name"));
        user.setStatus(useritem.getIntValue("status"));
        user.setSysuserid(useritem.getString("uid"));

        if (list.size() > 0) {
            JSONObject item = list.getJSONObject(0);
            user.setOrgcode(item.getString("orgcode"));
            user.setOrgname(item.getString("orgname"));
            user.setSex(item.getString("sex"));
        }

        if (wxlist.size() > 0) {
            JSONObject wxitem = wxlist.getJSONObject(0);
            user.setOpenid(wxitem.getString("openid"));
            user.setIdcode(wxitem.getString("idcode"));
            user.setUid(wxitem.getString("uid"));
            user.setNickname(wxitem.getString("nickname"));
            user.setHeadimgurl(wxitem.getString("headimgurl"));
        } else {
            user.setIdcode(code);
            user.setUid(UUID.randomUUID().toString());
            user.setNickname("访客");
            user.setHeadimgurl("/ui/img/icon-man.png");
        }

        user.setTokenid(UUID.randomUUID().toString());
        user.setUsertype(0);
        userredis.add(request, response, user);
        return user;
    }

    /**
     * 根据微信授权用户信息自动登录
     *
     * @param response
     * @param userid
     * @return
     */
    public SysUser getOAuthWorkSysUser(HttpServletRequest request, HttpServletResponse response, String userid) {
        String sql = "select uid,docid from tb_weixin_workuser where uid=? ";
        JSONArray list = dbService.QueryList(sql, userid);
        if (list.size() == 0) {
            Log.error(OAuthService.class, "微信企业用户信息未绑定");
            return null;
        } else {
            JSONObject item = list.getJSONObject(0);
            String docid = item.getString("docid");
            sql = "select uid as docid, code,name,sex,orgcode,getorgname(orgcode) as orgname,0 as usertype,status from tb_card_teachstudinfo where uid=? ";
            JSONArray userlist = dbService.QueryList(sql, docid);
            if (userlist.size() == 0) {
                Log.error(OAuthService.class, "用户档案信息不存在");
                return null;
            }
            JSONObject useritem = userlist.getJSONObject(0);
            SysUser user = new SysUser();

            user.setCode(useritem.getString("code"));
            user.setDocid(useritem.getString("docid"));
            user.setOrgcode(useritem.getString("orgcode"));
            user.setOrgname(useritem.getString("orgname"));
            user.setName(useritem.getString("name"));
            user.setNickname(useritem.getString("name"));
            user.setSex(useritem.getString("sex"));
            user.setStatus(item.getIntValue("status"));
            user.setTokenid(UUID.randomUUID().toString());
            user.setUid(userid);
            user.setUsertype(99);
            userredis.add(request, response, user);

            return user;
        }
    }

    public JsonResult CreateDocument(HttpServletRequest request, String mobile, String name) {
        SysUser user = userredis.get(request);
        String userid = user.getUid();
        String docid = user.getDocid();
        String weixinid = user.getOpenid();
        String headimgurl = user.getHeadimgurl();
        String nickname = user.getNickname();
        String sex = user.getSex();
        String orgcode = user.getOrgcode();
        String areacode = user.getAreacode();

        String sql = "insert into tb_weixin_user(uid,openid,usertype,docid,idcode,headimgurl,nickname,sex,lastlogintime,logintimes,status,createdate) values(?,?,?,?,?,?,?,?,now(),1,1,now())";
        dbService.excuteSql(sql, userid, weixinid, 3, docid, mobile, headimgurl, nickname, sex);
        String docsex = "1";
        if (!StringUtils.isBlank(sex)) {
            if (sex.equals("2")) {
                docsex = "2";
            }
        }
        String code = mobile.substring(1);
        if (Long.parseLong(code) > 4294967295L) {
            code = mobile.substring(2);
        }
        String card = ComHelper.LeftPad(code, 12, '0');
        sql = "INSERT INTO tb_card_teachstudinfo(uid,code,name,sex,card,cardsn,mobile,orgcode,infotype,createdate,modifydate,status) VALUES(?,?,?,?,?,?,?,?,1,now(),now(),1);";
        dbService.excuteSql(sql, docid, code, name, docsex, card, card, mobile, orgcode);

        int count = dbService.getCount("tb_biz_info_scheme", " infoid='" + docid + "' and areacode='" + areacode + "' and status=1 ");
        if (count == 0) {
            sql = "INSERT INTO tb_biz_info_scheme(uid,infoid,areacode,allowother,createdate,modifydate,status) VALUES(uuid(),?,?,0,now(),now(),1)";
            dbService.excuteSql(sql, docid, areacode);
        }

        String cardid = UUID.randomUUID().toString();
        String str = "insert into tb_card_cardinfo(uid,infoid,usedes,cardno,cardsn,deposit,ismain,balance,vicewallet,waterwallet,timescount,enddate,createdate,modifydate,status) values " +
                "('" + cardid + "','" + docid + "','本人', '" + card + "', '" + card + "',0,1,0,0,0,0,'2050-01-01',now(),now(),1)";
        dbService.excuteSql(str);

        String str2 = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,infocode,des,createdate) values" +
                "(uuid(),'" + docid + "','" + cardid + "','" + card + "','" + card + "',0,1,'本人','0','微信注册自动发卡',now())";
        dbService.excuteSql(str2);

        String daily = DateHelper.format(new Date(), "yyyy-MM-dd");
        int dailycount = dbService.getCount("tb_biz_daily_register", "orgcode='" + orgcode + "' and daily='" + daily + "'");
        if (dailycount == 0) {
            dbService.excuteSql("INSERT INTO tb_biz_daily_register(uid,daily,orgcode,total,createdate) VALUES(uuid(),'" + daily + "','" + orgcode + "',1,now())");
        } else {
            dbService.excuteSql("update tb_biz_daily_register set total=total+1 where daily='" + daily + "' and orgcode='" + orgcode + "' ");
        }

        user.setIdcode(mobile);
        user.setName(name);
        redisclient.setObject(user.getUid(), user, 86400, TimeUnit.SECONDS);

        return Json.getJsonResult(true);
    }


    public JsonResult BizBind(HttpServletRequest request, HttpServletResponse response, String name, String mobile) {
        SysUser user = userredis.get(request);
        String userid = user.getUid();
        String docid = user.getDocid();
        String weixinid = user.getOpenid();

        if (StringUtils.isBlank(mobile)) {
            return Json.getJsonResult("手机号必须填写");
        }
        if (mobile.length() != 11) {
            return Json.getJsonResult("手机号格式错误");
        }
        if (StringUtils.isBlank(name)) {
            return Json.getJsonResult("请填写真实姓名");
        }
        if (mobile.substring(0, 2).equals("29")) {
            return Json.getJsonResult("系统不支持该号段");
        }
		/*
		if(!redisclient.hasStringKey("vc"+mobile)) {
			return Json.getJsonResult(false,"手机验证码错误");
		}
		String vcode=redisclient.getString("vc"+mobile);
		if(!vcode.equals(mobilecode)) {
			return Json.getJsonResult(false,"手机验证码错误");
		}
		*/
        int wxUserCount = dbService.getCount("tb_weixin_user", "openid='" + weixinid + "'");
        if (wxUserCount == 0) {
            int count = dbService.getCount("tb_card_teachstudinfo", "mobile='" + mobile + "' ");
            if (count > 0) {
                return Json.getJsonResult("请勿提交他人手机号");
            }
            CreateDocument(request, mobile, name);
        } else {
            String sql = "update tb_card_teachstudinfo set name=?,mobile=?,infolabel='',modifydate=now() where uid=? ";
            dbService.excuteSql(sql, name, mobile, docid);
            sql = "update tb_weixin_user set idcode=? where uid=? ";
            dbService.excuteSql(sql, mobile, userid);

            user.setIdcode(mobile);
            user.setName(name);
            redisclient.setObject(user.getUid(), user, 86400, TimeUnit.SECONDS);
        }
        return Json.getJsonResult(true);
    }

    /**
     * 绑定企业微信
     *
     * @param request
     * @param response
     * @param userid
     * @param code
     * @param name
     * @return
     */
    public JsonResult WeiXinWorkBind(HttpServletRequest request, HttpServletResponse response, String userid, String code, String name) {
        JSONArray userlist = dbService.QueryList("select uid as docid,code,name,sex,orgcode,getorgname(orgcode) as orgname,3 as usertype,status from tb_card_teachstudinfo where name='" + name + "' and code='" + code + "' and infotype=1 ");
        if (userlist.size() == 0) {
            return Json.getJsonResult("身份信息错误");
        }
        JSONObject item = userlist.getJSONObject(0);
        String docid = item.getString("docid");
        int count = dbService.getCount("tb_weixin_workuser", "docid='" + docid + "' and uid='" + userid + "'");
        if (count > 0) {
            return Json.getJsonResult("已绑定其他微信");
        }
        String sql = "insert into tb_weixin_workuser(uid,docid,createdate) values(?,?,now())";
        dbService.excuteSql(sql, userid, docid);

        workuser.SyncWeiXinWorkUserData(userid);

        SysUser user = new SysUser();
        user.setCode(item.getString("code"));
        user.setDocid(docid);
        user.setOrgcode(item.getString("orgcode"));
        user.setOrgname(item.getString("orgname"));
        user.setName(item.getString("name"));
        user.setNickname(item.getString("name"));
        user.setSex(item.getString("sex"));
        user.setStatus(item.getIntValue("status"));
        user.setTokenid(UUID.randomUUID().toString());
        user.setUid(userid);
        user.setUsertype(99);
        userredis.add(request, response, user);

        return Json.getJsonResult(true);
    }


    public JsonResult WeiXinWorkDisBind(HttpServletRequest request, HttpServletResponse response) {
        SysUser user = userredis.get(request);
        if (user == null) {
            return Json.getJsonResult(false, "企业微信用户未在线");
        }
        if (user.getUsertype() != 99) {
            return Json.getJsonResult(false, "非企业微信用户");
        }
        String sql = "delete from tb_weixin_workuser where uid=?";
        dbService.excuteSql(sql, user.getUid());
        userredis.remove(request);
        return Json.getJsonResult(true);
    }


    /**
     * 根据调试代码获取在线用户身份
     *
     * @param response
     * @param weixinworkuserid
     * @return
     */
    public SysUser getOAuthWeiXinWorkSysUser(HttpServletRequest request, HttpServletResponse response, String weixinworkuserid) {
        String sql = "SELECT uid,docid FROM tb_weixin_workuser where uid='" + weixinworkuserid + "'";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            return null;
        } else {
            JSONObject item = list.getJSONObject(0);
            String docid = item.getString("docid");
            JSONArray userlist = dbService.QueryList("select uid as docid,code,name,sex,orgcode,getorgname(orgcode) as orgname,3 as usertype,status from tb_card_teachstudinfo where uid='" + docid + "' ");
            if (userlist.size() == 0) {
                return null;
            }
            JSONObject useritem = userlist.getJSONObject(0);
            SysUser user = new SysUser();
            user.setCode(useritem.getString("code"));
            user.setDocid(docid);
            user.setOrgcode(useritem.getString("orgcode"));
            user.setOrgname(useritem.getString("orgname"));
            user.setName(useritem.getString("name"));
            user.setNickname(useritem.getString("name"));
            user.setSex(useritem.getString("sex"));
            user.setStatus(useritem.getIntValue("status"));
            user.setTokenid(UUID.randomUUID().toString());
            user.setUid(weixinworkuserid);
            user.setUsertype(99);
            userredis.add(request, response, user);
            return user;
        }
    }

    /**
     * 98类型内部人员：运营平台用户
     *
     * @param request
     * @param response
     * @param userinfo
     * @return
     */
    public JsonResult getOAuthSysUser98(HttpServletRequest request, HttpServletResponse response, OAuthUserInfo userinfo, String areacode, String orgcode) {
        String sql = "select uid,openid,headimgurl,nickname,sex,usertype,docid,idcode,status from tb_weixin_user where openid='" + userinfo.getOpenid() + "' order by lastlogintime desc";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            SysUser user = new SysUser();
            String name = String.format("临%s", RandomHelper.GetDifferentNum(10));
            String mobile = String.format("29%s", RandomHelper.GetDifferentNum(9));
            String code = mobile.substring(1);
            if (Long.parseLong(code) > 4294967295L) {
                code = mobile.substring(2);
            }
            String orgname = "";
            sql = "select name from tb_card_orgframework where code='" + orgcode + "'";
            JSONArray orglist = dbService.QueryList(sql);
            if (orglist.size() > 0) {
                orgname = orglist.getJSONObject(0).getString("name");
            }
            if (StringUtils.isBlank(areacode) || StringUtils.isBlank(orgcode)) {
                return Json.getJsonResult(false, "请扫码注册用户");
            }
            user.setCode(code);
            user.setDocid(UUID.randomUUID().toString());
            user.setOpenid(userinfo.getOpenid());
            user.setIdcode(mobile);
            user.setOrgcode(orgcode);
            user.setOrgname(orgname);
            user.setAreacode(areacode);
            user.setName(name);
            user.setNickname(EmojiUtils.filterEmoji(userinfo.getNickname()));
            user.setSex(userinfo.getSex());
            user.setStatus(1);
            user.setTokenid(UUID.randomUUID().toString());
            user.setUid(UUID.randomUUID().toString());
            user.setUsertype(98);
            user.setHeadimgurl(userinfo.getHeadimgurl());
            userredis.add(request, response, user);
            return Json.getJsonResult(true, "1");
        } else {
            JSONObject item = list.getJSONObject(0);
            if (item.getIntValue("status") == 0) {
                return Json.getJsonResult("用户微信登录已被禁用");
            }
//            sql = "update tb_weixin_user set lastlogintime=now(),nickname=?,sex=?,logintimes=logintimes+1,headimgurl=? where uid=?";
//            dbService.excuteSql(sql, EmojiUtils.filterEmoji(userinfo.getNickname()), userinfo.getSex(), userinfo.getHeadimgurl(), item.getString("uid"));
            String docid = item.getString("docid");
            JSONArray userlist = dbService.QueryList("select uid as docid, code,name,orgcode,getorgname(orgcode) as orgname,0 as usertype,status from tb_card_teachstudinfo where uid='" + docid + "' ");
            if (userlist.size() == 0) {
                dbService.excuteSql("delete from tb_weixin_user where openid=?", userinfo.getOpenid());
                JsonResult result = getOAuthSysUser98(request, response, userinfo, areacode, orgcode);
                return result;
            }
            if (StringUtils.isBlank(areacode)) {
                JSONArray clist = dbService.QueryList("select areacode from tb_biz_info_scheme where infoid='" + docid + "' order by modifydate desc limit 1");
                if (clist.size() == 0) {
                    return Json.getJsonResult(false, "请扫码注册用户");
                } else {
                    areacode = clist.getJSONObject(0).getString("areacode");
                }
            }
            JSONObject useritem = userlist.getJSONObject(0);
            SysUser user = new SysUser();
            user.setCode(useritem.getString("code"));
            user.setDocid(useritem.getString("docid"));
            user.setOpenid(item.getString("openid"));
            user.setIdcode(item.getString("idcode"));
            user.setOrgcode(useritem.getString("orgcode"));
            user.setOrgname(useritem.getString("orgname"));
            user.setName(useritem.getString("name"));
            user.setAreacode(areacode);
            user.setNickname(EmojiUtils.filterEmoji(userinfo.getNickname()));
            user.setSex(userinfo.getSex());
            user.setStatus(useritem.getIntValue("status"));
            user.setTokenid(UUID.randomUUID().toString());
            user.setUid(item.getString("uid"));
            user.setUsertype(98);
            user.setHeadimgurl(userinfo.getHeadimgurl());
            userredis.add(request, response, user);
            return Json.getJsonResult(true, "0");
        }
    }

    public SysUser getOAuthSysUser98ByUserid(HttpServletRequest request, HttpServletResponse response, String userid) {
        String sql = "select uid,docid,openid,idcode,headimgurl,nickname,sex from tb_weixin_user where uid=? ";
        JSONArray list = dbService.QueryList(sql, userid);
        if (list.size() == 0) {
            Log.error(OAuthService.class, "微信企业用户信息未绑定");
            return null;
        } else {
            JSONObject item = list.getJSONObject(0);
            String docid = item.getString("docid");
            sql = "select uid as docid, code,name,sex,orgcode,getorgname(orgcode) as orgname,98 as usertype,status from tb_card_teachstudinfo where uid=? ";
            JSONArray userlist = dbService.QueryList(sql, docid);
            if (userlist.size() == 0) {
                Log.error(OAuthService.class, "用户档案信息不存在");
                return null;
            }
            JSONObject useritem = userlist.getJSONObject(0);
            SysUser user = new SysUser();

            user.setCode(useritem.getString("code"));
            user.setDocid(useritem.getString("docid"));
            user.setOpenid(item.getString("openid"));
            user.setIdcode(item.getString("idcode"));
            user.setOrgcode(useritem.getString("orgcode"));
            user.setOrgname(useritem.getString("orgname"));
            user.setName(useritem.getString("name"));
            user.setNickname(item.getString("nickname"));
            user.setSex(useritem.getString("sex"));
            user.setStatus(useritem.getIntValue("status"));
            user.setTokenid(UUID.randomUUID().toString());
            user.setUid(item.getString("uid"));
            user.setUsertype(98);
            user.setHeadimgurl(item.getString("headimgurl"));
            userredis.add(request, response, user);
            return user;
        }
    }

    public JsonResult WeiXinParentsBindIsOk(HttpServletRequest request, HttpServletResponse response, String openId, int usertype, String name, String vCode,
                                            String infoCode, String mobile, String linkName, String linkTel, String code, String pwd, String headImgUrl, String nickname, String sex, String pName, String pMobile, String pSex, String relation, String newPwd) throws Exception {
        String isVerificationCode = syscfg.get("isVerificationCode");
        if ("1".equals(isVerificationCode)) {
            if (!checkmobilenum(mobile, vCode)) {
                return Json.getJsonResult(false, "验证码错误");
            }
        }
        // 家长 需要学生信息中填写过家长信息和手机号
        String sql = "select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,2 as usertype,status from tb_card_teachstudinfo " +
                "where name=? and code=? and infotype=2 ";
        JSONArray userlist = dbService.QueryList(sql, name, code);
        JsonResult result = WeiXinBindParents(request, response, userlist, openId, usertype, linkTel, headImgUrl, nickname, sex, pwd, pName, pMobile, pSex, relation, newPwd);
        return result;
    }

    private JsonResult WeiXinBindParents(HttpServletRequest request, HttpServletResponse response, JSONArray userList, String openId, int usertype, String linkTel, String headImgUrl, String nickname, String sex, String pwd, String pName, String pMobile, String pSex, String relation, String newPwd) throws Exception {
        if (userList.size() == 0) {
            return Json.getJsonResult("身份信息错误");
        }
        JSONObject item = userList.getJSONObject(0);
        String docid = item.getString("docid");
        int count = dbService.getCount("tb_weixin_user", "docid='" + docid + "' and usertype=" + String.valueOf(usertype) + " ");
        String pwdSql = "select mp_password as password from tb_card_teachstudinfo where uid = '" + docid + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(pwdSql);
        if ((usertype == 2 || usertype == 3) && "1".equals(WebConfig.getApptype())) {
            if (count >= 3) {
                return Json.getJsonResult("关注人数已达上限");
            }
            if (jsonObject != null) {
                String password = jsonObject.getString("password");
                if (password == null || password.equals("")) {
                    String passWord = syscfg.get("defaultPassWord");
                    if (!passWord.equals(pwd)) {
                        return Json.getJsonResult("密码错误");
                    }
                } else {
                    if (!pwd.equals(password)) {
                        return Json.getJsonResult("密码错误");
                    }
                }
            } else {
                String passWord = syscfg.get("defaultPassWord");
                if (!passWord.equals(pwd)) {
                    return Json.getJsonResult("密码错误");
                }
            }
        } else {
            if (count >= 1) {
                return Json.getJsonResult("其他微信已绑定");
            }
            if (jsonObject != null) {
                String password = jsonObject.getString("password");
                if (password == null || password.equals("")) {
                    String passWord = syscfg.get("defaultPassWord");
                    if (!passWord.equals(pwd)) {
                        return Json.getJsonResult("密码错误");
                    }
                } else {
                    if (!pwd.equals(password)) {
                        return Json.getJsonResult("密码错误");
                    }
                }
            } else {
                String passWord = syscfg.get("defaultPassWord");
                if (!passWord.equals(pwd)) {
                    return Json.getJsonResult("密码错误");
                }
            }
        }
        String idCode = item.getString("code");
        String orgCode = item.getString("orgcode");
        String userId = UUID.randomUUID().toString();
        //判断家长是否已经录入系统
        String selParents = "select uid from tb_card_teachstudinfo where mobile='" + pMobile + "' and infotype=3";
        final String[] parentsUid = {dbService.queryOneField(selParents, String.class)};
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                if (StringUtils.isBlank(parentsUid[0])) {
                    String finalParentsCode = generateCodeRecursive();
                    String card = generateCardRecursive();
                    parentsUid[0] = UUID.randomUUID().toString();
                    String parentSql = "insert into tb_card_teachstudinfo(uid,code,sex,name,mobile,card,cardsn,infotype,linkdes1,createdate,creatorid,modifydate,modifierid,status,endsisdaily,orgcode,linkid) values(?,?,?,?,?,?,?,3,?,now(),?,now(),?,1,99999999,?,?)";
                    PreparedStatement ps = connection.prepareStatement(parentSql);
                    int sex = Integer.parseInt(relation.split("\\|")[1]);
                    ps.setString(1, parentsUid[0]);
                    ps.setString(2, finalParentsCode);
                    ps.setInt(3, sex);
                    ps.setString(4, pName);
                    ps.setString(5, pMobile);
                    ps.setString(6, card);
                    ps.setString(7, card);
                    ps.setString(8, relation.split("\\|")[0]);
                    ps.setString(9, parentsUid[0]);
                    ps.setString(10, parentsUid[0]);
                    ps.setString(11, orgCode);
                    ps.setString(12, docid);
                    ps.executeUpdate();
                    ps.close();
                    String cardflag = syscfg.get("autoCard");
                    if (StringUtils.isNotEmpty(cardflag)&&"1".equals(cardflag)){
                        if (StringUtils.isNotBlank(card)) {
                            String cardId = UUID.randomUUID().toString();
                            String str = "insert into tb_card_cardinfo(uid,infoid,usedes,cardno,cardsn,deposit,ismain,balance,vicewallet,waterwallet,timescount,enddate,createdate,creatorid,modifydate,modifierid,status) values " +
                                    "('" + cardId + "','" + parentsUid[0] + "','本人', '" + card + "', '" + card + "',0,1,0,0,0,0,'2050-01-01',now(),'" + "微信公众号家属绑定" + "',now(),'" + "微信公众号家属绑定" + "',1)";
                            dbService.excuteSql(str);
                            String str2 = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,des,createdate,creatorid) values" +
                                    "(uuid(),'" + parentsUid[0] + "','" + cardId + "','" + card + "','" + card + "',0,1,'本人','" + "微信公众号家属绑定" + "','0','微信公众号家属绑定发卡',now(),'" + "微信公众号家属绑定" + "')";
                            dbService.excuteSql(str2);
                            String dopsql = "update tb_card_cardinfo_oprecord op inner join tb_card_teachstudinfo info on info.uid=op.infoid set op.infocode=info.code,op.infoname=info.name,op.orgname=getorgname(info.orgcode) where op.infocode='0' ";
                            dbService.excuteSql(dopsql);
                        }
                    }

                    String insert = "INSERT INTO tb_card_parentinfo (parent_id,student_id,create_dt)  VALUES (?, ?, now())";
                    ps = connection.prepareStatement(insert);
                    ps.setString(1,parentsUid[0]);
                    ps.setString(2,docid);
                    ps.executeUpdate();
                    ps.close();
                }else {
                    String insert = "INSERT INTO tb_card_parentinfo (parent_id,student_id,create_dt)  VALUES (?, ?, now())";
                    PreparedStatement ps3 = connection.prepareStatement(insert);
                    ps3.setString(1,parentsUid[0]);
                    ps3.setString(2,docid);
                    ps3.executeUpdate();
                    ps3.close();
                }
//                //更新学生密码
//                String updatePwd = "update tb_card_teachstudinfo set mp_password = ? where uid = ?";
//                PreparedStatement ps1 = connection.prepareStatement(updatePwd);
//                ps1.setString(1, newPwd);
//                ps1.setString(2, docid);
//                ps1.executeUpdate();
//                ps1.close();

                String sql = "insert into tb_weixin_user(uid,openid,usertype,docid,idcode,headimgurl,nickname,sex,lastlogintime,logintimes,status,createdate,parent_id) values(?,?,?,?,?,?,?,?,now(),1,1,now(),?)";
                PreparedStatement ps2 = connection.prepareStatement(sql);
                ps2.setString(1, userId);
                ps2.setString(2, openId);
                ps2.setInt(3, usertype);
                ps2.setString(4, docid);
                ps2.setString(5, idCode);
                ps2.setString(6, headImgUrl);
                ps2.setString(7, nickname);
                ps2.setString(8, sex);
                ps2.setString(9, parentsUid[0]);
                ps2.executeUpdate();
                ps2.close();
            }
        });

        SysUser user = new SysUser();
        user.setCode(item.getString("code"));
        user.setDocid(docid);
        user.setOpenid(openId);
        user.setIdcode(idCode);
        user.setOrgcode(item.getString("orgcode"));
        user.setOrgname(item.getString("orgname"));
        user.setName(item.getString("name"));
        user.setNickname(nickname);
        user.setSex(sex);
        user.setStatus(item.getIntValue("status"));
        user.setTokenid(UUID.randomUUID().toString());
        user.setUid(userId);
        user.setUsertype(usertype);
        user.setHeadimgurl(headImgUrl);
        user.setParentId(parentsUid[0]);
        userredis.add(request, response, user);
        return Json.getJsonResult(true, jsonObject);
    }


    private String generateCodeRecursive() {
        // 生成一个随机数字，模拟 parentsCode 的生成
        int randomNum = new Random().nextInt(10000);
        String parentsCode = String.valueOf(randomNum);

        // 检查 parentsCode 是否重复
        int ctcount = dbService.getCount("tb_card_teachstudinfo", "code='" + parentsCode + "' ");
        if (ctcount > 0) {
            // 如果 parentsCode 重复，递归调用重新生成
            return generateCodeRecursive();
        }

        // 如果没有重复，返回最终的 parentsCode 和 card
        return parentsCode;
    }

    private String generateCardRecursive() {

        // 生成 card
        String card = ComHelper.LeftPad(String.valueOf(new Random().nextInt(1000000000)), 12, '0');

        // 检查 card 是否重复
        int cardCount = dbService.getCount("tb_card_cardinfo", "cardno='" + card + "' AND status=1");
        if (cardCount > 0) {
            // 如果 card 重复，递归调用重新生成
            return generateCardRecursive();
        }

        // 如果没有重复，返回最终的 parentsCode 和 card
        return card;
    }


    public String isServicePaid(HttpServletRequest request) throws IOException {
        String key = "WECHAT_SERVICE_PAID";
        if (Boolean.TRUE.equals(redisTemplate.opsForValue().get(key))) {
            return "1";
        } else {
            long timeMillis = System.currentTimeMillis();
            HashMap<String, Object> param = new HashMap<>();
            param.put("mpAppId", WebConfig.getWeixinAppid());
            param.put("appId", "fbd0057b18bd457a8cbc25383e6e54a3");
            param.put("timestamp", timeMillis);
            String signature = MD5Encrypt("appId=fbd0057b18bd457a8cbc25383e6e54a3&mpAppId=wx9fa8023ab8b47b04&timestamp="+timeMillis+"&key=HySsf1xw4Ihs1rCMlEgNNJ2QDJQoH49J");
            param.put("signature", signature);
            JSONObject jsonObject = null;
            try {
                jsonObject = okHttpUtil.postFormUrlEncoderJson("http://47.112.126.84:2019/weChat/getServicePaid", param);
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (jsonObject != null) {
                if (!jsonObject.getBoolean("success")) {
                    return "0";
                }
            }
            redisTemplate.opsForValue().set(key, true, 1, TimeUnit.DAYS);
            return "1";
        }
    }

    public static String MD5Encrypt(String value) {
        char[] hexDigits ={'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'};
        try {
            byte[] btInput = value.getBytes("utf-8");
            // 获得MD5摘要算法的 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(btInput);
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i  < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0  >>>  4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
