package com.ymiots.campusmp.service;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.config.redis.RedisClient;
import com.ymiots.campusmp.utils.weixin.WeiXinAccessToken;
import com.ymiots.campusmp.utils.weixin.WeiXinRequest;
import com.ymiots.campusmp.utils.weixin.entity.AccessToken;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Repository
public class AccessTokenService {

	public static String ACCESS_TOKEN_REDISKEY="campusmp_weixin_accesstoken";
	public static String ACCESS_WORK_TOKEN="campus_weixinwork_accesstoken";
	
	@Autowired
	WeiXinAccessToken wxat;
	
	@Autowired
	RedisClient redisclient;
	
	public AccessToken GetNewAccessToken() {
		try {
			if(WebConfig.getIsyuntoken().equals("1")) {
				String LOCAL_ACCESS_TOKEN = "http://pay.ymiots.com/weixinouth/getaccesstoken";
				Map<String, String> params=new HashMap<String,String>();
				params.put("campusid", WebConfig.getCampusid());
				JsonResult result= WeiXinRequest.HttpPost(LOCAL_ACCESS_TOKEN,params);
				if(!result.isSuccess()) {
					Log.error(WeiXinAccessToken.class, "获取微信accesstoken云服务接口错误："+result.getMsg());
					return null;
				}
				JsonResult accessToken=JSONObject.parseObject(result.getMsg(), JsonResult.class);
				if(!accessToken.isSuccess()) {
					Log.error(WeiXinAccessToken.class, "获取微信accesstoken错误："+result.getMsg());
					return null;
				}
				JSONObject token=accessToken.getData();
				AccessToken at=JSONObject.parseObject(token.toJSONString(), AccessToken.class);
				return at;
			}else {
				AccessToken at=wxat.GetAccessToken(WebConfig.getWeixinAppid(), WebConfig.getWeixinAppSecret());
				return at;
			}
		} catch (Exception e) {
			Log.error(AccessTokenService.class, "获取access_token错误："+e.getMessage());
			return null;
		}
	}
	
	public String GetAccessToken() {
		if(redisclient.hasStringKey(ACCESS_TOKEN_REDISKEY)) {
			return redisclient.getString(ACCESS_TOKEN_REDISKEY);
		}else {
			AccessToken at=	GetNewAccessToken();
			if (at == null) {
				if(!StringUtils.isBlank(WebConfig.getWeixinAppid()) && !StringUtils.isBlank(WebConfig.getWeixinAppSecret())) {
					Log.error(AccessTokenService.class, "刷新access_token错误：未获取到AccessToeken对象");
				}
				return "";
			}else if(at.getErrcode()!=0) {
				Log.error(AccessTokenService.class, "刷新access_token错误："+at.getErrmsg());
				return "";
			}else {
				//提前五分钟过期
				redisclient.setString(ACCESS_TOKEN_REDISKEY, at.getAccess_token(), at.getExpires_in()-1800, TimeUnit.SECONDS);
				return at.getAccess_token();
			}
		}
	}
	
	/**
	 *  获取企业微信
	 * @return
	 */
	public AccessToken GetNewWorkAccessToken() {
		try {
			AccessToken at=wxat.GetWorkAccessToken(WebConfig.getWeixinWorkCorpid(), WebConfig.getWeixinWorkSecret());
			return at;
		} catch (Exception e) {
			Log.error(AccessTokenService.class, "获取access_token错误："+e.getMessage());
			return null;
		}
	}
	
	/**
	 * 获取企业微信
	 * @return
	 */
//	public String GetWorkAccessToken() {
//		if(redisclient.hasStringKey(WORKACCESS_TOKEN_REDISKEY)) {
//			return redisclient.getString(WORKACCESS_TOKEN_REDISKEY);
//		}else {
//			AccessToken at=	GetNewWorkAccessToken();
//			if (at == null) {
//				if(!StringUtils.isBlank(WebConfig.getWeixinWorkCorpid()) && !StringUtils.isBlank(WebConfig.getWeixinWorkSecret())) {
//					Log.error(AccessTokenService.class, "刷新企业access_token错误：未获取到AccessToeken对象");
//				}
//				return "";
//			}else if(at.getErrcode()!=0) {
//				Log.error(AccessTokenService.class, "刷新企业access_token错误："+at.getErrmsg());
//				return "";
//			}else {
//				redisclient.setString(WORKACCESS_TOKEN_REDISKEY, at.getAccess_token(), at.getExpires_in(), TimeUnit.SECONDS);
//				return at.getAccess_token();
//			}
//		}
//	}

	public String GetWorkAccessToken() {
		if(redisclient.hasStringKey(ACCESS_WORK_TOKEN)) {
			return redisclient.getString(ACCESS_WORK_TOKEN);
		}else {
			AccessToken at=	GetWorkAccess();
			if (at == null) {
				if(!StringUtils.isBlank(WebConfig.getWeixinAppid()) && !StringUtils.isBlank(WebConfig.getWeixinAppSecret())) {
					Log.error(AccessTokenService.class, "刷新access_token错误：未获取到AccessToeken对象");
				}
				return "";
			}else if(at.getErrcode()!=0) {
				Log.error(AccessTokenService.class, "刷新access_token错误："+at.getErrmsg());
				return "";
			}else {
				redisclient.setString(ACCESS_WORK_TOKEN, at.getAccess_token(), at.getExpires_in(), TimeUnit.SECONDS);
				return at.getAccess_token();
			}
		}
	}

	/**
	 * 获取企业微信
	 * @return
	 */
	public AccessToken GetWorkAccess() {
		try {
			if(WebConfig.getIsWorkYunToken().equals("1")) {
				String LOCAL_ACCESS_TOKEN = "http://47.112.126.84:2031/weixinouth/getWorkAccessToken";
				Map<String, String> params=new HashMap<String,String>();
				params.put("campusid", WebConfig.getCampusid());
				JsonResult result= WeiXinRequest.HttpPost(LOCAL_ACCESS_TOKEN,params);
				if(!result.isSuccess()) {
					Log.error(WeiXinAccessToken.class, "获取企业微信accesstoken云服务接口错误："+result.getMsg());
					return null;
				}
				JsonResult accessToken=JSONObject.parseObject(result.getMsg(), JsonResult.class);
				if(!accessToken.isSuccess()) {
					Log.error(WeiXinAccessToken.class, "获取企业微信accesstoken错误："+result.getMsg());
					return null;
				}
				JSONObject token=accessToken.getData();
				AccessToken at=JSONObject.parseObject(token.toJSONString(), AccessToken.class);
				return at;
			}else {
				AccessToken at=wxat.GetWorkAccessToken(WebConfig.getWeixinWorkCorpid(), WebConfig.getWeixinWorkSecret());
				return at;
			}
		} catch (Exception e) {
			Log.error(AccessTokenService.class, "获取access_token错误："+e.getMessage());
			return null;
		}
	}
}
