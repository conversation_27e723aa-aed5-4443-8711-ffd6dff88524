package com.ymiots.campusmp.service.weixinwork;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.RestRequest;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.entity.card.OrgFramework;
import com.ymiots.campusmp.service.AccessTokenService;
import com.ymiots.campusmp.utils.weixin.WeiXinAccessToken;
import com.ymiots.campusmp.utils.weixin.WeiXinWork;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.datafactory.CallbackTransaction;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/*
 * 企业微信
 */

@Repository
public class WorkOrgService extends BaseService {

    @Autowired
    AccessTokenService token;

    @Autowired
    WeiXinWork weixinwork;

    @Autowired
    RestRequest request;

    public void SyncWeiXinWorkOrgData() throws Exception {
        String ACCESS_TOKEN = token.GetWorkAccessToken();
        if (StringUtils.isBlank(ACCESS_TOKEN)) {
            Log.error(this.getClass(), "企业ACCESSTOEKN为空，企业组织数据未同步");
            throw new Exception("企业ACCESSTOEKN为空，企业组织数据未同步");
        }
        JSONArray list = weixinwork.GetWeiXinWorkDepartmentData(ACCESS_TOKEN, "");
        if (list.size() == 0) {
            Log.info(this.getClass(), "没有获取到企业微信部门数据");
            throw new Exception("没有获取到企业微信部门数据");
        }
        final StringBuffer sql = new StringBuffer();
        sql.append(" INSERT INTO tb_weixin_workorg(uid,name,parentid,`order`) VALUES ");
        for (int i = 0; i < list.size(); i++) {
            JSONObject item = list.getJSONObject(i);
            if (i > 0) {
                sql.append(",");
            }
            sql.append(String.format("(%s,'%s',%s,%s)", item.getIntValue("id"), item.getString("name"), item.getIntValue("parentid"), item.getIntValue("order")));
        }
        Log.info(this.getClass(), sql.toString());
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                // TODO Auto-generated method stub
                PreparedStatement ps = connection.prepareStatement("truncate table tb_weixin_workorg;");
                ps.executeUpdate();
                ps.close();

                ps = connection.prepareStatement(sql.toString());
                ps.executeUpdate();
                ps.close();
            }
        });
    }

    public void SyncNewWeiXinWorkOrgData() throws Exception {
        String ACCESS_TOKEN = token.GetWorkAccessToken();
        if (StringUtils.isBlank(ACCESS_TOKEN)) {
            Log.error(this.getClass(), "企业ACCESSTOEKN为空，企业组织数据未同步");
            throw new Exception("企业ACCESSTOEKN为空，企业组织数据未同步");
        }
        JSONArray list = new JSONArray();
        if ("1".equals(WebConfig.getIsWorkYunToken())) {
            String LOCAL_ACCESS_TOKEN = "http://pay.ymiots.com/weixinouth/getWorkDepartment";
            Map<String, String> params = new HashMap<String, String>();
            params.put("campusid", WebConfig.getCampusid());
            params.put("ACCESS_TOKEN", ACCESS_TOKEN);
            list = request.doPostFormRequest(LOCAL_ACCESS_TOKEN, params, JSONArray.class);
        } else {
            list = weixinwork.GetWeiXinWorkDepartmentData(ACCESS_TOKEN, "");
        }
        if (list == null) {
            Log.error(WeiXinAccessToken.class, "获取企业微信部门云服务接口错误");
            throw new Exception("获取企业微信部门云服务接口错误");
        }
        List<Object> departmentList = new ArrayList<>(list);
        // 使用 Stream API 分组并排序
        Map<Integer, List<JSONObject>> departmentListMap = departmentList.stream()
                .filter(obj -> obj instanceof JSONObject)
                .map(obj -> (JSONObject) obj)
                .collect(Collectors.groupingBy(
                        json -> json.getInteger("parentid"),
                        TreeMap::new, // 使用 TreeMap 来保持键的自然顺序
                        Collectors.mapping(json -> json, Collectors.toList())
                ));

        if (CollectionUtil.isEmpty(departmentListMap)) {
            Log.info(this.getClass(), "没有获取到企业微信部门数据");
            throw new Exception("没有获取到企业微信部门数据");
        }
        // 打印分组结果并按照parentid从小到大排序wechatParentId-osUid
        departmentListMap.forEach((parentId, departList) -> {
            System.out.println("父部门ID: " + parentId);
            //查询父节点id
            String selOsOrgFramework = "select uid,code from tb_card_orgframework where orgcode = '" + parentId + "'";
            OrgFramework orgFrameworkParent = dbService.queryForOne(selOsOrgFramework, OrgFramework.class);
            AtomicInteger beginNum = new AtomicInteger(1); // 使用AtomicInteger类型
            // 对departList按照order字段降序排序
            departList.sort((json1, json2) -> json2.getInteger("order").compareTo(json1.getInteger("order")));
            departList.forEach(json -> {
                //部门id
                Integer id = json.getInteger("id");
                //部门名字
                String name = json.getString("name");
                //查询是否部门已经存在
                String framework = "select uid,code from tb_card_orgframework where orgcode = '" + id + "'";
                OrgFramework orgFramework = dbService.queryForOne(framework, OrgFramework.class);
                if (0 == parentId) {
                    if (Optional.ofNullable(orgFramework).isPresent()) {
                        String updateSQL = "update tb_card_orgframework set name = ?,parentid = null where orgcode = '" + id + "'";
                        dbService.excuteSql(updateSQL, name);
                    } else {
                        String updateSQL = "insert into  tb_card_orgframework(uid,code,orgcode,orgtype,name,parentid,createdate,creatorid,status) " +
                                "values(uuid(),'001',?,?,?,?,now(),'企业微信自动同步',1)";
                        dbService.excuteSql(updateSQL, id, 1, name, null);
                    }
                } else {
                    if (Optional.ofNullable(orgFrameworkParent).isPresent()) {
                        //处理最新的组织编号
                        String lastCode = orgFrameworkParent.getCode();
                        if (StringUtils.isNotBlank(lastCode)) {
                            String code = lastCode + String.format("%03d", beginNum.get());
                            if (Optional.ofNullable(orgFramework).isPresent()) {
                                String updateSQL = "update tb_card_orgframework set name = ?,code = ?,parentid = ? where orgcode = ?";
                                dbService.excuteSql(updateSQL, name,code,orgFrameworkParent.getUid(),id);
                            } else {
                                String updateSQL = "insert into  tb_card_orgframework(uid,code,orgcode,orgtype,name,parentid,createdate,creatorid,status) " +
                                        "values(uuid(),?,?,?,?,?,now(),'企业微信自动同步',1)";
                                dbService.excuteSql(updateSQL,code, id, 2, name,orgFrameworkParent.getUid());
                            }
                            beginNum.incrementAndGet(); // 自增
                        }
                    }
                }
            });
        });
    }

}
