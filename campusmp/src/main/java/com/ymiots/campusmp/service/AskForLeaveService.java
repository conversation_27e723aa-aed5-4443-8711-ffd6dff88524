package com.ymiots.campusmp.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.ApprovalProcessUtil;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.controller.workflow.PushMessageHandler;
import com.ymiots.campusmp.controller.workflow.WorkFlowUtil;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.redis.RedisMessageEntity;
import com.ymiots.campusmp.redis.RedisPublish;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.workflow.ApprovalFormService;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessageData;
import com.ymiots.framework.common.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Repository
public class AskForLeaveService extends BaseService {

    @Autowired
    UserRedis userredis;

    @Autowired
    RedisPublish redispublish;


    @Autowired
    private ApprovalFormService approvalFormService;

    @Autowired
    private ApprovalProcessUtil approvalProcessUtil;

    @Autowired
    SysLogsService sysLogsService;

    @Autowired
    private WorkFlowUtil workFlowUtil;

    @Autowired
    private PushMessageHandler pushMessageHandler;


    public JsonData getPeople(HttpServletRequest request, HttpServletResponse response) {
        JsonData jd = new JsonData();
        SysUser user = userredis.get(request);
        int usertype = user.getUsertype();

        if (usertype == 2) {
            String idcode = user.getIdcode();
            String sql = "SELECT ct.name as title,wu.docid as value " +
                    "FROM tb_weixin_user wu INNER JOIN tb_card_teachstudinfo ct ON wu.docid=ct.uid " +
                    "WHERE wu.idcode='" + idcode + "' ";
            jd = dbService.QueryJsonData(sql);
        } else if (usertype == 3) {
            String useridSQL = "SELECT uid FROM tb_sys_user WHERE empid='" + user.getDocid() + "'";
            JSONArray useridja = dbService.QueryList(useridSQL);
            if (useridja.isEmpty()) {
                JsonData jsonData = new JsonData();
                jsonData.setSuccess(false);
                return jsonData;
            }
            String userid = useridja.getJSONObject(0).getString("uid");
            String sxu = "SELECT distinct ct.name as title,ct.uid as value " +
                    "FROM tb_card_teachstudinfo ct " +
                    "join tb_card_orgframework co on ct.orgcode = co.code " +
                    "join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid = '" + userid + "'" +
                    " order by ct.name asc";
            JSONArray xuja = dbService.QueryList(sxu);
            jd.setSuccess(true);
            jd.setData(xuja);
        }
        return jd;
    }

    public JsonData getLeaveRecord(HttpServletRequest request, String docid, int start, int limit, String starttime, String endtime) {
        String fields = "uid,infoid,date_format(starttime, '%Y-%m-%d %H:%i') as starttime,status ";
        String table = "tb_card_ask_for_leave";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND starttime BETWEEN '" + starttime + ":00' AND '" + endtime + ":59'";
        }
        where += " and infoid = '" + docid + "' ";
        return dbService.QueryJsonData(fields, table, where, "createdate DESC", start, limit);
    }


    public JsonData getLeaveRecordTeach(HttpServletRequest request, String key, int start, int limit, String starttime, String endtime) {
        String  docId = userredis.get(request).getDocid();
        String sql = "select uid from tb_sys_user where empid = '" + docId + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(sql);
        String fields = "cafl.uid,cafl.infoid,date_format(cafl.starttime, '%Y-%m-%d %H:%i') as starttime,cafl.status ";
        String table = "tb_card_ask_for_leave cafl";
        String where = " 1=1 ";
        String userId = "";
        if (StringUtils.isNotBlank(jsonObject.getString("uid"))) {
            userId = jsonObject.getString("uid");
        }
        table += " left join tb_card_teachstudinfo ct on ct.uid = cafl.infoid " +
                "join tb_card_orgframework co on co.code = ct.orgcode " +
                "join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid = '" + userId + "'";

        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND starttime BETWEEN '" + starttime + ":00' AND '" + endtime + ":59'";
        }
        if (StringUtils.isNotBlank(key)) {
            where += " And (ct.code = '" + key + "%' or ct.name like '" + key + "%')";
        }
        return dbService.QueryJsonData(fields, table, where, "cafl.createdate DESC", start, limit);
    }


    public JSONArray getDetailedLeaveRecord(String uid, HttpServletRequest request) {
        String sql = "SELECT DISTINCT  asl.uid,asl.infoid,ct.name as infoname,ct.code as infocode,asl.reason,date_format( asl.starttime, '%Y-%m-%d %H:%i:%s' ) AS starttime,date_format( asl.endtime, '%Y-%m-%d %H:%i:%s' ) AS endtime,asl.remark,asl.status,asl.leavetype,dic.NAME AS leavetypename , war.auditcondition as auditcondition, war.remark as warremark ,war.auditid as auditid, war.modifyid as modifyid," +
                " asl.isfinish,date_format(asl.leavefinishtime, '%Y-%m-%d %H:%i:%s') AS leavefinishtime " +
                " FROM tb_card_ask_for_leave asl " +
                " left join tb_card_teachstudinfo ct on ct.uid = asl.infoid " +
                " LEFT JOIN tb_sys_dictionary dic ON dic.CODE = asl.leavetype  AND dic.groupcode = 'SYS0000035' " +
                " LEFT JOIN tb_workflow_approval_record as war on war.formid = asl.uid" +
                " WHERE  asl.uid = '" + uid + "' order by auditcondition ";
        return dbService.QueryList(sql);
    }

    public List<WeiXinUser> getDetailedLeave2Record(String formId, String infoId) {
        String sql = "SELECT cafl.uid,wu.openid as openId,ct.name,ct.code,cafl.status,cafl.starttime as startTime,cafl.endtime as endTime,cafl.reason,cafl.infoid  as infoId" +
                "  FROM tb_weixin_user as wu  " +
                "  LEFT JOIN tb_card_teachstudinfo as ct on ct.uid = wu.docid " +
                "  left join tb_card_ask_for_leave as cafl on cafl.infoid = ct.uid " +
                "  where wu.docid='" + infoId + "' AND wu.status=1 and cafl.uid = '" + formId + "'";
        return dbService.queryList(sql, WeiXinUser.class);
    }

    @Data
    public static class WeiXinUser {
        private String uid;
        private String openId;
        private String name;
        private String code;
        private Date startTime;
        private Date endTime;
        private String reason;
        private String infoId;
        private Integer status;
    }

    public JsonResult cancelLeave(String uid) {
        String delSQL = "update tb_card_ask_for_leave set status = 2  WHERE uid=?";
        dbService.excuteSql(delSQL, uid);
        String updateSQl = "update tb_workflow_approval_record set auditcondition = 1 ,remark = '" + "单据已被取消" + "' where formid = '" + uid + "' and auditcondition = 0";
        dbService.excuteSql(updateSQl);
        return Json.getJsonResult(true);
    }

//    public JsonData getAuditLeaveList(HttpServletRequest request, HttpServletResponse response, String leavetype, String key, String status, String endtime, String starttime, int start, int limit) {
//
//        String fields = " distinct afl.uid,ct.code as infocode,afl.infoid,ct.name as infoname,afl.reason,date_format(afl.starttime,'%Y-%m-%d %H:%i')as starttime,date_format(afl.endtime,'%Y-%m-%d %H:%i')as endtime,date_format(afl.createdate,'%Y-%m-%d %H:%i')as createdate,afl.leavetype,dic.name as leavetypename,afl.remark,afl.status,war.auditcondition,afl.isfinish,afl.leavefinishtime";
//        String table = "tb_card_ask_for_leave afl " +
//                "INNER JOIN tb_card_teachstudinfo ct ON ct.uid = afl.infoid   AND  afl.starttime between  ' " + starttime + " ' and '" + endtime + "' LEFT JOIN tb_workflow_approval_record as war on war.formid = afl.uid  and war.auditcondition = afl.status " +
//                "LEFT JOIN tb_sys_dictionary dic on dic.code=afl.leavetype and dic.groupcode='SYS0000035' ";
//        String where = "1=1";
//        if (StringUtils.isNotBlank(leavetype) && !("0").equals(leavetype)) {
//            where += " and afl.leavetype=" + leavetype;
//        }
//        if (StringUtils.isNotBlank(status) && !("2").equals(status)) {
//            where += " AND afl.status=" + status;
//        }
//
//        String sqlMouldId = "SELECT uid FROM tb_workflow_mould WHERE mouldtype = 0 ";
//        JSONArray ja = dbService.QueryList(sqlMouldId);
//        if (!ja.isEmpty() && userredis.get(request).getUsertype() != 2 && userredis.get(request).getUsertype() != 9) {
//            JSONArray approvalFormList = approvalFormService.getApprovalFormList(userredis.get(request).getDocid(), 0);
//            if (approvalFormList.isEmpty()) {
//                return Json.getJsonData(true);
//            }
//            ArrayList<String> uidList = new ArrayList<>();
//            for (int i = 0; i < approvalFormList.size(); i++) {
//                uidList.add(approvalFormList.getJSONObject(i).getString("uid"));
//            }
//            where += " AND afl.uid IN ('" + String.join("','", uidList) + "')";
//        }
//        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
//            where += " AND  afl.starttime between  '" + starttime + "' and '" + endtime + "'";
//
//        }
//        if (StringUtils.isNotBlank(key)) {
//            where += " AND (ct.name like '" + key + "%' or ct.code='" + key + "')";
//        }
//
//        where += " or war.auditid = '" + userredis.get(request).getDocid() + "'";
//        String orderby = ExtSort.Orderby(request, "createdate desc");
//        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
//        return result;
//    }

    public JsonData getAuditLeaveList(HttpServletRequest request, HttpServletResponse response, String leavetype, String key, String status, String endtime, String starttime, int start, int limit) {
        String docId = userredis.get(request).getDocid();
        String name = userredis.get(request).getName();
        String fields = " distinct afl.uid,ct.code as infocode,afl.infoid,ct.name as infoname,afl.reason,date_format(afl.starttime,'%Y-%m-%d %H:%i')as starttime,date_format(afl.endtime,'%Y-%m-%d %H:%i')as endtime,date_format(afl.createdate,'%Y-%m-%d %H:%i')as createdate,afl.leavetype,dic.name as leavetypename,afl.remark,afl.status,war.auditcondition,afl.isfinish,afl.leavefinishtime";
        String table = "tb_card_ask_for_leave afl " +
                "INNER JOIN tb_card_teachstudinfo ct ON ct.uid = afl.infoid   AND  afl.starttime between  ' " + starttime + " ' and '" + endtime + "' LEFT JOIN tb_workflow_approval_record as war on war.formid = afl.uid  and war.auditcondition = afl.status " +
                "LEFT JOIN tb_sys_dictionary dic on dic.code=afl.leavetype and dic.groupcode='SYS0000035' ";
        String where = "1=1";
        if (StringUtils.isNotBlank(leavetype) && !("0").equals(leavetype)) {
            where += " and afl.leavetype=" + leavetype;
        }
        if (StringUtils.isNotBlank(status) && !("2").equals(status)) {
            where += " AND afl.status=" + status;
        }
        WorkFlowNodeDto nodeLevel = workFlowUtil.getNodeLevel(request, docId, 0);
        if (nodeLevel == null) {
           return Json.getJsonData(false,"该用户没有审批权限");
        }
        String recordsWhereSQL = workFlowUtil.getAuditRecordsWhereSQL(request, 0, nodeLevel,"afl.infoid","");
        if (StringUtils.isNotBlank(recordsWhereSQL)) {
            where += recordsWhereSQL;
        }
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND  afl.starttime between  '" + starttime + "' and '" + endtime + "'";
        }
        if (StringUtils.isNotBlank(key)) {
            where += " AND (ct.name like '" + key + "%' or ct.code='" + key + "')";
        }

        where += " or war.auditid = '" + docId + "' and afl.created_by !='" + name + "'";
        String orderby = ExtSort.Orderby(request, "createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    /**
     *  老师提交家长请假单
     */
    public JsonData getAuditLeaveList1(HttpServletRequest request, HttpServletResponse response, String leavetype, String key, String status, String endtime, String starttime, int start, int limit) {

        String fields = " distinct afl.uid,ct.code as infocode,afl.infoid,ct.name as infoname,afl.reason,date_format(afl.starttime,'%Y-%m-%d %H:%i')as starttime,date_format(afl.endtime,'%Y-%m-%d %H:%i')as endtime,afl.leavetype,dic.name as leavetypename,afl.remark,afl.status,afl.isfinish,afl.leavefinishtime,date_format(afl.createdate, '%Y-%m-%d %H:%i') as createdate";
        String table = "tb_card_ask_for_leave afl " +
                "INNER JOIN tb_card_teachstudinfo ct ON ct.uid = afl.infoid   AND  afl.starttime between  ' " + starttime + " ' and '" + endtime + "' LEFT JOIN tb_workflow_approval_record as war on war.formid = afl.uid  and war.auditcondition = afl.status " +
                "LEFT JOIN tb_sys_dictionary dic on dic.code=afl.leavetype and dic.groupcode='SYS0000035' ";
        String where = "1=1";
        if (StringUtils.isNotBlank(leavetype) && !("0").equals(leavetype)) {
            where += " and afl.leavetype=" + leavetype;
        }
        if (StringUtils.isNotBlank(status) && !("2").equals(status)) {
            where += " AND afl.status=" + status;
        }
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND  afl.starttime between  '" + starttime + "' and '" + endtime + "'";

        }
        if (StringUtils.isNotBlank(key)) {
            where += " AND (ct.name like '%" + key + "%' or ct.code='" + key + "')";
        }
        String docid = userredis.get(request).getDocid();
        where += "and afl.infoid = '" + docid + "' and afl.is_approval = 0 ";
        String orderby = ExtSort.Orderby(request, "createdate  desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    /**
     *审批流表单
     */
//    public JsonResult auditLeave(HttpServletRequest request, String uid, String infoid, String status, String remark) {
//        if (StringUtils.isBlank(uid)) {
//            return Json.getJsonResult(false, "当前请假申请被取消！");
//        }
//        ApprovalPushHandler pushHandler = new AskForLeavePushHandler(this, approvalFormService, redispublish);
//        return approvalProcessUtil.ApprovalFunction(userredis.get(request).getDocid(), uid, Integer.parseInt(status), remark, 0, pushHandler);
//    }

    public JsonResult auditLeave(HttpServletRequest request, String uid, String infoId, Integer status, String remark,String day) throws Exception {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "当前请假申请被取消！");
        }
        String table = "tb_card_ask_for_leave";
        String field = "";
        String name = userredis.get(request).getName();
        String userId = userredis.get(request).getDocid();
        return workFlowUtil.submitForApproval(request, uid,userId,name, status, remark, table, field,"",day);
    }

    /**
     *销假表单
     */
    public JsonResult leaveFinish(HttpServletRequest request, String uid, String infoid, String status, String remark) {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "当前请假申请被取消！");
        }
        String updateRequisitionSql = "update tb_card_ask_for_leave set isfinish = 1,leavefinishtime = now() where uid = '" + uid + "'";
        dbService.excuteSql(updateRequisitionSql);
        return Json.getJsonResult(true);
    }

    /**
     *  家长同意老师表单
     */
    public JsonResult auditLeave2(HttpServletRequest request, String uid, String infoid, String status, String remark) {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "当前请假申请被取消！");
        }
        String name = userredis.get(request).getName();
        int count = dbService.getCount("tb_card_ask_for_leave", "uid = '" + uid + "' and status = 0");
        if (count == 0) {
            return Json.getJsonResult(false, "当前请假申请已被审批！");
        }
//        String getInfoNameSql = "select linkman1 from tb_card_teachstudinfo where uid = '" + uid + "'";
//        String linkman1 = dbService.QueryJSONObject(getInfoNameSql).getString("linkman1");
        String sql = "update tb_card_ask_for_leave set status = ? ,remark = ?,isfinish = 0 where uid = '" + uid + "'";
        String info = "通过";
        if (status.equals("2")) {
            info = "拒绝";
        }
        int ok = dbService.excuteSql(sql, status, remark);
        if (ok > 0) {
            //推送给上级
            boolean parent = getParent(infoid, uid,name,info);
            if (parent) {
                Log.info(this.getClass(), "推送成功");
            } else {
                Log.info(this.getClass(), "推送失败");

            }

        }
        return Json.getJsonResult(true, "审批成功");
    }

    public boolean getParent(String infoId, String formId,String name,String info) {
        String sql = "select ct.name,cafl.status,date_format(cafl.starttime,'%Y-%m-%d %H:%i:%s') as starttime,created_by as createdBy from " +
                " tb_card_teachstudinfo ct " +
                " left join tb_card_ask_for_leave cafl on cafl.infoid = ct.uid " +
                " where ct.uid = '" + infoId + "' and cafl.uid = '" + formId + "'";
        JSONArray jsonArray = dbService.QueryList(sql);
        if (jsonArray.size() != 0) {
            JSONObject jo = jsonArray.getJSONObject(0);
            String weiXinSQL = "select openid,uid from tb_weixin_user where docid = '" + jo.getString("createdBy") + "'";
            if (jo.isEmpty()) {
                return false;
            }

            JSONObject jsonObject = dbService.QueryJSONObject(weiXinSQL);
            String openid = jsonObject.getString("openid");
            String wxuserid = jsonObject.getString("uid");
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("cfgcode", "OPENTM417832643");
            data.put("url", String.format("%s/askforleave/detailedLeaveRecord?uid=%s&time=%s", WebConfig.getDomain(), formId, System.currentTimeMillis()));
            JSONObject wxdata = new JSONObject();
            wxdata.put("thing1", new TemplateMessageData(jo.getString("name")));
            wxdata.put("thing4", new TemplateMessageData(name));
            wxdata.put("time2", new TemplateMessageData(jo.getString("starttime")));
            wxdata.put("time5", new TemplateMessageData(DateHelper.format(new Date(), "yyyy-MM-dd")).toJSONString());
            wxdata.put("const3", new TemplateMessageData(info));
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);
            return true;
        }
        return false;
    }


    /**
     * 提交表单
     */
//    public JsonResult saveleaveform(HttpServletRequest request, HttpServletResponse response, String docid, String starttime, String endtime, String reason,String leaveschool) {
//        String name = userredis.get(request).getName();
//        if (userredis.get(request).getUsertype() == 2) {
//            ApprovalPushHandler askForLeavePushHandler = new AskForLeavePushHandler(this, approvalFormService, redispublish);
//            StringBuffer sql = new StringBuffer();
//            String appid = UUID.randomUUID().toString().replace("-", "");
//            sql.append("INSERT INTO tb_card_ask_for_leave(uid,infoid,infoname,infocode,reason,starttime,endtime,createdate,created_by,is_approval,status,leaveschool)" +
//                    "SELECT '" + appid + "',uid,name,code,'" + reason + "','" + starttime + "','" + endtime + "',now(),'" + name + "',1,0,"+leaveschool+" FROM tb_card_teachstudinfo where uid = ?");
//            dbService.excuteSql(sql.toString(), docid);
//
//            //获取工作流模板为请假的模板id
//            String sqlMouldId = "SELECT uid FROM tb_workflow_mould WHERE mouldtype = 0 ";
//            JSONArray ja = dbService.QueryList(sqlMouldId);
//            if (!ja.isEmpty()) {
//                String mouldid = ja.getJSONObject(0).getString("uid");
//                return approvalProcessUtil.ApprovalProcess(appid, mouldid, docid, askForLeavePushHandler, 0);
//            }
//
//            //推送消息
//            String selParent = "SELECT ct.name,ctc.parentinfoid FROM tb_card_teachstudinfo_child ctc INNER JOIN tb_card_teachstudinfo ct ON ct.uid=ctc.infoid WHERE ctc.infoid='" + docid + "'";
//            JSONArray parentja = dbService.QueryList(selParent);
//            if (parentja.size() > 0) {
//                String infoname = parentja.getJSONObject(0).getString("name");
//                List<String> parentinfoidList = new ArrayList<String>();
//                for (int j = 0; j < parentja.size(); j++) {
//                    parentinfoidList.add(parentja.getJSONObject(j).getString("parentinfoid"));
//                }
//
//                String sql3 = "SELECT uid,openid FROM tb_weixin_user where docid IN ('" + String.join("','", parentinfoidList) + "') AND status=1;";
//                JSONArray list = dbService.QueryList(sql3);
//                if (list.size() > 0) {
//                    for (int i = 0; i < list.size(); i++) {
//                        String openid = list.getJSONObject(i).getString("openid");
//                        String wxuserid = list.getJSONObject(i).getString("uid");
//                        String topic = "/campus/weixintemplatemsg";
//                        RedisMessageEntity message = new RedisMessageEntity();
//                        JSONObject data = new JSONObject();
//                        data.put("msgid", UUID.randomUUID().toString());
//                        data.put("openid", openid);
//                        data.put("wxuserid", wxuserid);
//                        data.put("cfgcode", "OPENTM415931101");
//                        data.put("url", String.format("%s/askforleave/auditleave?uid=%s&time=%s", WebConfig.getDomain(), appid, System.currentTimeMillis()));
//                        JSONObject wxdata = new JSONObject();
//                        wxdata.put("first", new TemplateMessageData("您收到了" + infoname + "的请假申请", "#4caf50").toJSONString());
//                        wxdata.put("keyword1", new TemplateMessageData(infoname).toJSONString());
//                        wxdata.put("keyword2", new TemplateMessageData(reason).toJSONString());
//                        wxdata.put("keyword3", new TemplateMessageData(starttime + "至" + endtime).toJSONString());
//                        wxdata.put("remark", new TemplateMessageData("请处理请假请求").toJSONString());
//                        data.put("wxtempdata", wxdata);
//                        message.setCmd("weixintemplatemsg");
//                        message.setData(data);
//                        message.setDevtype(0);
//                        redispublish.Publish(topic, message);
//                    }
//                }
//            }
//        } else {
//            String formId = UUID.randomUUID().toString();
//            String delSql = "delete from tb_card_ask_for_leave where uid = '" + formId + "'";
//            String sql = "INSERT INTO tb_card_ask_for_leave(uid,infoid,reason,starttime,endtime,createdate,leavetype,status,is_approval,leaveschool,created_by) values ('" + formId + "','" + docid + "','" + reason + "','" + starttime + "','" + endtime + "',now()," + 1 + ",0,0,'"+leaveschool+"','" + name +
//                    "')";
//            dbService.excuteSql(sql);
//            boolean b = pushToUser(formId, docid);
//            if (!b) {
//                dbService.excuteSql(delSql);
//                return Json.getJsonResult(false, "该学生未绑定家长");
//            }
//            sysLogsService.Write(request, "请假管理", String.format("添加请假信息,人员：%s", docid));
//        }
//        return Json.getJsonResult(true);
//    }
    public JsonResult saveleaveform(HttpServletRequest request, HttpServletResponse response, String docid, String starttime, String endtime, String reason, String leaveschool) {
        SysUser sysUser = userredis.get(request);
        String name = sysUser.getName();
        String docId = sysUser.getDocid();
        String orgCode = sysUser.getOrgcode();
        if (sysUser.getUsertype() == 2) {
            //获取工作流模板为请假的模板id
            String sqlMouldId = "SELECT uid FROM tb_workflow_mould WHERE mouldtype = 0 ";
            JSONArray ja = dbService.QueryList(sqlMouldId);
            if (ja.isEmpty()) {
                return Json.getJsonResult(false, "未配置审批模板");
            }
            String mouldId = ja.getJSONObject(0).getString("uid");
            List<WorkFlowNodeDto> workFlowNodeDTOs = workFlowUtil.selSubPerson(mouldId, docId, "",orgCode, "", 1);
            if (workFlowNodeDTOs.size() == 0) {
                return Json.getJsonResult(false, "未配置审核人，请联系管理员！");
            }
            StringBuilder sql = new StringBuilder();
            String appid = UUID.randomUUID().toString().replace("-", "");
            sql.append("INSERT INTO tb_card_ask_for_leave(uid,infoid,infoname,infocode,reason,starttime,endtime,createdate,created_by,is_approval,status,leaveschool)" + "SELECT '").append(appid).append("',uid,name,code,'").append(reason).append("','").append(starttime).append("','").append(endtime).append("',now(),'").append(docId).append("',1,0,").append(leaveschool).append(" FROM tb_card_teachstudinfo where uid = ?");
            dbService.excuteSql(sql.toString(), docid);
            String insertRecordSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate) values (uuid(),?,?,?,?,now())";
            dbService.excuteSql(insertRecordSql, appid, mouldId, 0, 1);
            //推送消息
            pushMessageHandler.pushNextAskApply(appid,workFlowNodeDTOs);
//            String selParent = "SELECT ct.name,ctc.parentinfoid FROM tb_card_teachstudinfo_child ctc INNER JOIN tb_card_teachstudinfo ct ON ct.uid=ctc.infoid WHERE ctc.infoid='" + docid + "'";
//            JSONArray parentja = dbService.QueryList(selParent);
//            if (parentja.size() > 0) {
//                String infoname = parentja.getJSONObject(0).getString("name");
//                List<String> parentinfoidList = new ArrayList<String>();
//                for (int j = 0; j < parentja.size(); j++) {
//                    parentinfoidList.add(parentja.getJSONObject(j).getString("parentinfoid"));
//                }
//
//                String sql3 = "SELECT uid,openid FROM tb_weixin_user where docid IN ('" + String.join("','", parentinfoidList) + "') AND status=1;";
//                JSONArray list = dbService.QueryList(sql3);
//                if (list.size() > 0) {
//                    for (int i = 0; i < list.size(); i++) {
//                        String openid = list.getJSONObject(i).getString("openid");
//                        String wxuserid = list.getJSONObject(i).getString("uid");
//                        String topic = "/campus/weixintemplatemsg";
//                        RedisMessageEntity message = new RedisMessageEntity();
//                        JSONObject data = new JSONObject();
//                        data.put("msgid", UUID.randomUUID().toString());
//                        data.put("openid", openid);
//                        data.put("wxuserid", wxuserid);
//                        data.put("cfgcode", "OPENTM415931101");
//                        data.put("url", String.format("%s/askforleave/auditleave?uid=%s&time=%s", WebConfig.getDomain(), appid, System.currentTimeMillis()));
//                        JSONObject wxdata = new JSONObject();
//                        wxdata.put("first", new TemplateMessageData("您收到了" + infoname + "的请假申请", "#4caf50").toJSONString());
//                        wxdata.put("keyword1", new TemplateMessageData(infoname).toJSONString());
//                        wxdata.put("keyword2", new TemplateMessageData(reason).toJSONString());
//                        wxdata.put("keyword3", new TemplateMessageData(starttime + "至" + endtime).toJSONString());
//                        wxdata.put("remark", new TemplateMessageData("请处理请假请求").toJSONString());
//                        data.put("wxtempdata", wxdata);
//                        message.setCmd("weixintemplatemsg");
//                        message.setData(data);
//                        message.setDevtype(0);
//                        redispublish.Publish(topic, message);
//                    }
//                }
//            }
        } else {
            String formId = UUID.randomUUID().toString();
            String delSql = "delete from tb_card_ask_for_leave where uid = '" + formId + "'";
            String sql = "INSERT INTO tb_card_ask_for_leave(uid,infoid,reason,starttime,endtime,createdate,leavetype,status,is_approval,leaveschool,created_by) values ('" + formId + "','" + docid + "','" + reason + "','" + starttime + "','" + endtime + "',now()," + 1 + ",0,0,'" + leaveschool + "','" + docId +
                    "')";
            dbService.excuteSql(sql);
            boolean b = pushToUser(formId, docid);
            if (!b) {
                dbService.excuteSql(delSql);
                return Json.getJsonResult(false, "该学生未绑定家长");
            }
            sysLogsService.Write(request, "请假管理", String.format("添加请假信息,人员：%s", docid));
        }
        return Json.getJsonResult(true);
    }

    public boolean pushToUser(String formId, String userId) {
        //判断家长是否被绑定
        String sql = "SELECT wu.uid,wu.openid as openId,ct.name,ct.code,ct.linkmobile1 as linkMobile1,cafl.status,cafl.starttime as startTime,cafl.endtime as endTime,cafl.createdate as createDate,cafl.reason,cafl.infoid  as infoId,cafl.created_by" +
                "  FROM tb_weixin_user as wu  " +
                "  LEFT JOIN tb_card_teachstudinfo as ct on ct.uid = wu.docid " +
                "  left join tb_card_ask_for_leave as cafl on cafl.infoid = ct.uid " +
                "  where wu.docid='" + userId + "' AND wu.status=1 and cafl.uid = '" + formId + "'";
        List<WeiXinUserDto> weiXinUsers = dbService.queryList(sql, WeiXinUserDto.class);
        if (weiXinUsers.size() == 0) {
            return false;
        }
        for (WeiXinUserDto weiXinUser : weiXinUsers) {
            String openid = weiXinUser.getOpenId();
            String wxUid = weiXinUser.getUid();
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxUid);
            data.put("cfgcode", "OPENTM415931101");
            data.put("url", String.format("%s/askforleave/auditLeave2?uid=%s&time=%s", WebConfig.getDomain(), formId, System.currentTimeMillis()));
            JSONObject wxdata = new JSONObject();
            wxdata.put("first", new TemplateMessageData("您收到了" + weiXinUser.getName() + "的请假申请", "#4caf50").toJSONString());
            wxdata.put("keyword1", new TemplateMessageData(weiXinUser.getCreateDate() + ""));
            wxdata.put("keyword2", new TemplateMessageData(weiXinUser.getName()).toJSONString());
            wxdata.put("keyword3", new TemplateMessageData(weiXinUser.getLinkMobile1()));
            wxdata.put("keyword4", new TemplateMessageData(weiXinUser.getStartTime() + "至" + weiXinUser.getEndTime()));
            wxdata.put("keyword5", new TemplateMessageData(weiXinUser.getReason()));
            wxdata.put("remark", new TemplateMessageData("请处理请假请求").toJSONString());
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);
        }
        return true;
    }

    @Data
    public static class WeiXinUserDto {
        private String uid;
        private String openId;
        private String name;
        private String code;
        private Date startTime;
        private Date endTime;
        private Date createDate;
        private String reason;
        private String infoId;
        private String linkMobile1;
        private Integer status;
        private String created_by;
    }

    public JSONObject getFromDataByFormidAndOaType(String formid) {
        String getFromDataByFormidAndOaType = "select cafl.infoname, cafl.reason ,date_format(cafl.starttime,'%Y-%m-%d %H:%i:%s') as starttime ,date_format(cafl.endtime,'%Y-%m-%d %H:%i:%s') as endtime,date_format(cafl.createdate,'%Y-%m-%d %H:%i:%s') as createdate,ct.linkmobile1 from tb_card_ask_for_leave cafl " +
                "left join tb_card_teachstudinfo ct on ct.uid = cafl.infoid where cafl.uid = '" + formid + "'";
        return dbService.QueryJSONObject(getFromDataByFormidAndOaType);
    }

    public JsonData getApprovalRecord(HttpServletRequest request, String formid, int start, int limit) {
        String fields = "ct.name, date_format(war.createdate, '%Y-%m-%d %H:%i') as createdate,war.auditcondition,war.remark ";
        String table = "tb_workflow_approval_record as war  LEFT JOIN tb_card_teachstudinfo as ct on ct.uid = war.auditid";
        String where = "formid = '" + formid + "' ";
        return dbService.QueryJsonData(fields, table, where, "createdate DESC", start, limit);
    }

    public JSONObject getWeiXinDocid(String sql) {
        return dbService.QueryJSONObject(sql);
    }
}
