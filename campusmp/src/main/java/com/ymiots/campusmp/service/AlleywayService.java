package com.ymiots.campusmp.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.controller.workflow.PushMessageHandler;
import com.ymiots.campusmp.controller.workflow.WorkFlowUtil;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.entity.dto.visitor.AuditApplyRequest;
import com.ymiots.campusmp.entity.elevator.Elevator;
import com.ymiots.campusmp.model.R;
import com.ymiots.campusmp.redis.RedisMessageEntity;
import com.ymiots.campusmp.redis.RedisPublish;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessageData;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class AlleywayService extends BaseService {

    @Autowired
    UserRedis userredis;

    @Autowired
    RedisPublish redispublish;

    @Autowired
    private WorkFlowUtil workFlowUtil;

    @Autowired
    private PushMessageHandler pushMessageHandler;

	/**
	 *  获取进出宿舍记录
	 * @return
	 */
	public JsonData getDormitoryList(HttpServletRequest request, HttpServletResponse response,int start,int limit,String starttime,String endtime) {
		SysUser userinfo= userredis.get(request);
		String fields="date_format(r.recordtime,'%Y-%m-%d %H:%i:%s') as recordtime,r.direction,door.name as doorname,r.recordimg,r.uid ";
		String table = "tb_alleyway_records r inner join tb_dev_door door on door.uid=r.doorid ";
		String where ="action='2' and infoid='"+userinfo.getDocid()+"'";
		    if(StringUtils.isNotBlank(starttime)&&StringUtils.isNotBlank(endtime)){
		    	where  +="and r.recordtime>'"+starttime+":00' AND r.recordtime<'"+endtime+"':59";
		    }
		JsonData list= dbService.QueryJsonData(fields, table, where, " r.recordtime desc ", start, limit);
		return list;
	}

	public JsonData getDetailedDorm(HttpServletRequest request, HttpServletResponse response, String uid){
        String fields="r.uid,r.direction ,date_format(r.recordtime, '%Y-%m-%d %H:%i:%s')as dromtime, r.cardsn ,getareaname(r.areacode) as areacode," +
                "i.name,i.code,of.name as orgname,door.name as doorname,d3.name as inouttype,d1.name as usedname,r.recordimg";
        String table="tb_alleyway_records r " +
                "inner join tb_dev_accesscontroller ac on ac.uid=r.controllerid " +
                "inner join tb_dev_door door on door.controllerid=r.controllerid and door.readhead=r.readhead " +
                "left join tb_card_teachstudinfo i on i.uid=r.infoid " +
                "left join tb_card_orgframework of on of.code=i.orgcode " +
                "left join tb_sys_dictionary d1 on d1.code=ac.used and d1.groupcode='SYS0000020' " +
                "left join tb_sys_dictionary d3 on d3.code=r.direction and d3.groupcode='SYS0000018' ";
        String where = "r.uid = '"+uid+"'";
        JsonData ja = dbService.QueryJsonData(fields, table, where, "");
        return ja;
	}

	public JsonData getPeople(HttpServletRequest request, HttpServletResponse response){
        JsonData jd = new JsonData();
        SysUser user=userredis.get(request);
        int usertype=user.getUsertype();
        if (usertype == 2) {
            String idcode = user.getIdcode();
            String sql = "SELECT ct.name as title,wu.docid as value " +
                    "FROM tb_weixin_user wu INNER JOIN tb_card_teachstudinfo ct ON wu.docid=ct.uid " +
                    "WHERE wu.idcode='"+idcode+"' ";
            jd = dbService.QueryJsonData(sql);
        }else if(usertype == 3){
            String sql = "SELECT name,mobile FROM tb_card_teachstudinfo WHERE uid='"+user.getDocid()+"'";
            JSONArray ja = dbService.QueryList(sql);
            String name = ja.getJSONObject(0).getString("name");
            String mobile = ja.getJSONObject(0).getString("mobile");
            String sxu = "SELECT ct.name as title,wu.docid as value " +
                    "FROM tb_weixin_user wu INNER JOIN tb_card_teachstudinfo ct ON wu.docid=ct.uid " +
                    "WHERE wu.idcode='"+mobile+"' AND wu.openid='"+user.getOpenid()+"'";
            JSONArray xuja = dbService.QueryList(sxu);
            Map<String,String> map = new HashMap<String,String>();
            map.put("title",name);
            map.put("value",userredis.get(request).getDocid());
            xuja.add(0,map);
            jd.setSuccess(true);
            jd.setData(xuja);
        }
        return jd;
    }

    /**
     *  查询出入记录
     * @param request
     * @param response
     * @param start
     * @param limit
     * @param startTime
     * @param endTime
     * @return
     */
    public JsonData getRecordList(HttpServletRequest request, HttpServletResponse response,String docid, int start, int limit,String startTime, String endTime) {
        if (StringUtils.isBlank(docid)) {
            docid = userredis.get(request).getDocid();
        }
        String fields="r.uid,r.direction,door.name as doorname,date_format(r.recordtime, '%Y-%m-%d %H:%i:%s')as recordtime,r.recordimg,r.cardsn,r.infoname ";
        String table="tb_alleyway_records r "
                + " inner join tb_dev_accesscontroller ac on ac.uid=r.controllerid "
                + " inner join tb_dev_door door on door.controllerid=r.controllerid and door.readhead=r.readhead ";
        String where = "r.infoid = '"+docid+"' AND r.action IN(1,2,3,4,5,6,7,10)";
        if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
            where += " AND r.recordtime BETWEEN '"+startTime+":00' AND '"+endTime+":59'";
        }
        JsonData jd = dbService.QueryJsonData(fields, table, where, "recordtime DESC", start, limit);
        return jd;
    }

    public JsonData getDetailedRecord(HttpServletRequest request, HttpServletResponse response, String uid){
    	String sql="select uid,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordtime, infoid, cardsn, machineid, readhead, recordimg from tb_alleyway_records where uid = '"+uid+"' ";
    	JSONArray list= dbService.QueryList(sql);
    	if(list.size()==0) {
    		return Json.getJsonData(false,"通行记录不存在");
    	}
    	JSONObject item=list.getJSONObject(0);
    	String infoid=item.getString("infoid");
    	String cardsn=item.getString("cardsn");
    	String machineid=item.getString("machineid");
    	String readhead=item.getString("readhead");

    	if(StringUtils.isBlank(cardsn) && StringUtils.isBlank(infoid)) {
    		return Json.getJsonData(false,"通行记录卡号和档案代码为空");
    	}
    	if(!StringUtils.isBlank(cardsn) && StringUtils.isBlank(infoid)) {
    		sql="select infoid from tb_card_cardinfo where cardsn = '"+cardsn+"' ";
        	JSONArray list2= dbService.QueryList(sql);
        	if(list2.size()==0) {
        		return Json.getJsonData(false,"人员卡号信息不存在");
        	}
        	infoid=list2.getJSONObject(0).getString("infoid");
        	item.put("infoid", infoid);
    	}

    	sql="select i.name,i.code,of.name as orgname from tb_card_teachstudinfo i left join tb_card_orgframework of on of.code=i.orgcode where i.uid='"+infoid+"' ";
    	JSONArray list3= dbService.QueryList(sql);
    	if(list3.size()==0) {
    		return Json.getJsonData(false,"人员信息不存在");
    	}
    	JSONObject item3=list3.getJSONObject(0);
    	item.putAll(item3);

    	sql="select door.inouttype,getareaname(door.areacode) as areacode,ac.devname,door.name as doorname,d3.name as inouttype,d1.name as usedname from tb_dev_accesscontroller ac left join tb_dev_door door on door.controllerid=ac.uid and door.readhead='"+readhead+"' left join tb_sys_dictionary d1 on d1.code=ac.used and d1.groupcode='SYS0000020' left join tb_sys_dictionary d3 on d3.code=door.inouttype and d3.groupcode='SYS0000018' where ac.machineid='"+machineid+"' ";
    	JSONArray list4= dbService.QueryList(sql);
    	if(list4.size()==0) {
    		return Json.getJsonData(false,"设备通道信息不存在");
    	}
    	JSONObject item4=list4.getJSONObject(0);
    	item.putAll(item4);

    	JSONArray lastlist=new JSONArray();
    	lastlist.add(item);
    	return Json.getJsonData(true,item);
    }

    public JSONArray getTrack(String infoId, String date){
        String sql = "select t2.name as areaname,DATE_FORMAT(t1.recordtime,'%Y-%m-%d %H:%i:%s') as recordtime " +
                "from tb_alleyway_records as t1 " +
                "left join tb_dev_door as t2 on t1.doorid=t2.uid " +
                "where t1.infoid = ? and t1.recordtime between ? and ? order by t1.recordtime";
        String start = date +" 00:00:00";
        String end = date + " 23:59:59";
        return dbService.QueryList(sql, infoId, start, end);
    }

    public JsonData getOutInSchoolRecordList(HttpServletRequest request, HttpServletResponse response, int start, int limit,String startTime, String endTime) {
        SysUser sysUser = userredis.get(request);
        String fields="r.uid,r.direction,door.name as doorname,date_format(r.recordtime, '%Y-%m-%d %H:%i:%s')as recordtime,r.recordimg";
        String table="tb_alleyway_records r "
                + " inner join tb_dev_accesscontroller ac on ac.uid=r.controllerid "
                + " inner join tb_dev_door door on door.controllerid=r.controllerid and door.readhead=r.readhead ";
        String where = "infoid = '"+sysUser.getDocid()+"' AND r.action=3";
        if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
            where += " AND r.recordtime BETWEEN '"+startTime+"' AND '"+endTime+"'";
        }
        JsonData jd = dbService.QueryJsonData(fields, table, where, "recordtime DESC", start, limit);
        return jd;
    }

    public JsonData getOutInSchoolDetailedRecord(HttpServletRequest request, HttpServletResponse response, String uid){
        String fields="r.uid,r.direction ,date_format(r.recordtime, '%Y-%m-%d %H:%i:%s')as recordtime, r.cardsn ,getareaname(r.areacode) as areacode," +
                "ac.devname,i.name,i.code,of.name as orgname,door.name as doorname,d3.name as inouttype,d1.name as usedname,r.recordimg";
        String table="tb_alleyway_records r " +
                "inner join tb_dev_accesscontroller ac on ac.uid=r.controllerid " +
                "inner join tb_dev_door door on door.controllerid=r.controllerid and door.readhead=r.readhead " +
                "left join tb_card_teachstudinfo i on i.uid=r.infoid " +
                "left join tb_card_orgframework of on of.code=i.orgcode " +
                "left join tb_sys_dictionary d1 on d1.code=ac.used and d1.groupcode='SYS0000020' " +
                "left join tb_sys_dictionary d3 on d3.code=r.direction and d3.groupcode='SYS0000018' ";
        String where = "r.uid = '"+uid+"'";
        JsonData jd = dbService.QueryJsonData(fields, table, where, "recordtime DESC", 0, 0);
        return jd;
    }

    //临时授权

    /**
     * 获取人员类型(普通员工、学生、家长不可使用)
     * @param request
     * @param response
     * @param infoid
     * @return
     */
    public String getInfoPosRank(HttpServletRequest request, HttpServletResponse response,String infoid){
        String sql="select ifnull(posrank,'') as posrank from tb_card_teachstudinfo where uid=?";
        JSONArray list= dbService.QueryList(sql, infoid);
        if(list.size()==0) {
            return "";
        }
        JSONObject item=list.getJSONObject(0);
        String posrank=item.getString("posrank");
        return posrank;
    }

    /**
     * 获取人员信息
     * @param request
     * @param response
     * @param key
     * @param start
     * @param limit
     * @return
     */
    public JsonData getInfoMsg(HttpServletRequest request, HttpServletResponse response,String key,int start,int limit){
        JsonData jd = new JsonData();
        if ("1".equals(WebConfig.getApptype())) {
            String fields = "ct.uid,ct.code,ct.name,ct.orgcode,co.name as orgname,ct.intoyear,fi.imgpath";
            String table = "tb_card_teachstudinfo ct " +
                    "LEFT JOIN tb_face_infoface fi ON fi.infoid=ct.uid " +
                    "LEFT JOIN tb_card_orgframework co ON co.code=ct.orgcode";
            String where = "ct.name like '%"+key+"%'";
            jd = dbService.QueryJsonData(fields, table, where, "ct.createdate DESC", start, limit);
        }else if("2".equals(WebConfig.getApptype())){
            String docid = userredis.get(request).getDocid();
            String fields = "ct.uid,ct.code,ct.name,ct.orgcode,co.name as orgname,ct.mobile,fi.imgpath ";
            String table = "tb_card_teachstudinfo_child ctc " +
                    "LEFT JOIN tb_card_teachstudinfo ct ON ct.uid=ctc.infoid " +
                    "LEFT JOIN tb_face_infoface fi ON fi.infoid=ctc.infoid " +
                    "LEFT JOIN tb_card_orgframework co ON co.code = ct.orgcode";
            String where = "ctc.parentinfoid='"+docid+"' AND ct.name like '%"+key+"%'";
            jd = dbService.QueryJsonData(fields, table, where, "ct.createdate DESC", start, limit);
        }
        return jd;
    }

    /**
     * 获取公共区域
     * @param request
     * @param response
     * @return
     */
    public JsonData getPublicArea(HttpServletRequest request, HttpServletResponse response){
        String fields = "code,name";
        String table = "tb_dev_areaframework";
        String where = "status = 1 AND sex='0'";
        return dbService.QueryJsonData(fields, table, where, "code ASC");
    }

    public JsonData getSomeTempAuthMsg(HttpServletRequest request, HttpServletResponse response,String infoid,String areacode){
        String infoSQL = "SELECT ct.uid,ct.code,ct.name,co.name as orgname " +
                "FROM tb_card_teachstudinfo ct " +
                "LEFT JOIN tb_card_orgframework co ON co.code=ct.orgcode " +
                "WHERE ct.uid=?";
        JSONArray result = dbService.QueryList(infoSQL, infoid);
        String areasql = "SELECT name FROM tb_dev_areaframework WHERE code IN ('"+String.join("','",areacode.split(","))+"')";
        JSONArray areaja = dbService.QueryList(areasql);
        StringBuilder areaname = new StringBuilder();
        if (!areaja.isEmpty()) {
            for (int i = 0; i < areaja.size(); i++) {
                areaname.append(areaja.getJSONObject(i).getString("name")).append(",");
            }
            areaname.deleteCharAt(areaname.length() - 1);
        }
        result.getJSONObject(0).put("areaname", areaname.toString());
        JsonData jd = new JsonData();
        jd.setData(result);
        jd.setSuccess(true);
        return jd;
    }

    /**
     * 保存临时授权
     * @param request
     * @param response
     * @param infoid        人员id
     * @param areacode      区域代码
     * @param starttime     开始时间
     * @param endtime       结束时间
     * @param allewaytimes  通行次数
     * @return
     */
    public JsonResult saveTempAuth(HttpServletRequest request, HttpServletResponse response,String infoid,String areacode,String starttime,String endtime,String allewaytimes,String remark){
        String creatorid = userredis.get(request).getDocid();
        String sql = "INSERT INTO tb_alleyway_tempauthrize (uid,infoid,infocode,infoname,infoindex,cardsn,areacode,starttime,endtime,allewaytimes,createdate,creatorid,modifydate,modifierid,remark,status)" +
                "SELECT uuid(),uid,code,name,infoindex,cardsn,'"+areacode+"','"+starttime+"','"+endtime+"','"+allewaytimes+"',now(),'"+creatorid+"',now(),'"+creatorid+"','"+remark+"',1 FROM tb_card_teachstudinfo WHERE uid = '"+infoid+"'";
        int i = dbService.excuteSql(sql);
        if (i == -1) {
            return Json.getJsonResult(false);
        }
        String selInfo = "SELECT name FROM tb_card_teachstudinfo WHERE uid='"+infoid+"'";
        JSONArray infoja = dbService.QueryList(selInfo);
        if (infoja.size() > 0) {
            String infoname = infoja.getJSONObject(0).getString("name");
            String sql3 = "SELECT uid,openid FROM tb_weixin_user where docid='"+infoid+"' AND usertype=2 AND status=1;";
            JSONArray list = dbService.QueryList(sql3);
            if (list.size() > 0) {
                for (int y = 0; y < list.size(); y++) {
                    String openid = list.getJSONObject(y).getString("openid");
                    String wxuserid = list.getJSONObject(y).getString("uid");
                    String topic = "/campus/weixintemplatemsg";
                    RedisMessageEntity message = new RedisMessageEntity();
                    JSONObject data = new JSONObject();
                    data.put("msgid", UUID.randomUUID().toString());
                    data.put("openid", openid);
                    data.put("wxuserid", wxuserid);
                    data.put("cfgcode", "OPENTM415847654");
                    data.put("url", "");
                    JSONObject wxdata = new JSONObject();
                    wxdata.put("first", new TemplateMessageData("您的孩子已请假", "#4caf50").toJSONString());
                    wxdata.put("keyword1", new TemplateMessageData(infoname).toJSONString());
                    wxdata.put("keyword2", new TemplateMessageData("临时请假").toJSONString());
                    wxdata.put("keyword3", new TemplateMessageData(DateHelper.format(new Date())));
                    wxdata.put("remark", new TemplateMessageData(remark).toJSONString());
                    data.put("wxtempdata", wxdata);
                    message.setCmd("weixintemplatemsg");
                    message.setData(data);
                    message.setDevtype(0);
                    redispublish.Publish(topic, message);
                }
            }
        }
        return Json.getJsonResult(true);
    }

    //出入查询
    /**
     * 出入查询
     * @param request
     * @param response
     * @param key
     * @param date
     * @return
     */
    public JsonData getAccessQuery(HttpServletRequest request, HttpServletResponse response,String key,String date,int start,int limit){
        if (StringUtils.isBlank(key)) {
            return Json.getJsonData(false, "请输入查询条件");
        }
        String fields = "r.uid,r.direction,door.name as doorname,date_format(r.recordtime, '%Y-%m-%d %H:%i:%s')as recordtime,r.recordimg,r.cardsn,r.infoname,co.name as orgname ";
        String table = "tb_alleyway_records r "
                + " inner join tb_dev_accesscontroller ac on ac.uid=r.controllerid "
                + " inner join tb_dev_door door on door.controllerid=r.controllerid and door.readhead=r.readhead "
                + "inner join tb_card_orgframework co ON co.code=r.orgcode ";
        String where = "date_format(recordtime,'%Y-%m-%d') = '"+date+"' ";
        if (StringUtils.isNotBlank(key)) {
            where += " AND (infocode='"+key+"' or infoname like '%"+key+"%')";
        }
        return dbService.QueryJsonData(fields,table, where, "recordtime DESC",start, limit);
    }

    public JSONObject getWeiXinDocid(String sql) {
        return dbService.QueryJSONObject(sql);
    }

    public JSONArray getDetailedDoorFromRecord(String uid, HttpServletRequest request) {
        String sql = "SELECT   asl.id uid,asl.*,getminiorgname(asl.department) departmentname,war.auditcondition,war.auditid,date_format(asl.open_time_start,'%Y-%m-%d %H:%i')as starttime,date_format(asl.open_time_end,'%Y-%m-%d %H:%i')as endtime" +
                " FROM tb_access_application asl " +
                " LEFT JOIN tb_workflow_approval_record as war on war.formid = asl.id" +
                " WHERE  asl.id = '" + uid + "' order by auditcondition ";
        return dbService.QueryList(sql);
    }

    public JsonData getDoorFromRecord(HttpServletRequest request, String docid, int start, int limit, String starttime, String endtime) {
        String fields = "id uid,infoid,date_format(open_time_start, '%Y-%m-%d %H:%i') as starttime,status ";
        String table = "tb_access_application";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND open_time_start BETWEEN '" + starttime + ":00' AND '" + endtime + ":59'";
        }
        where += " and infoid = '" + docid + "' ";
        return dbService.QueryJsonData(fields, table, where, "created_at DESC", start, limit);
    }

    public JsonData getAuditDoorFromList(HttpServletRequest request, HttpServletResponse response, String key, String status, String endtime, String starttime, int start, int limit) {
        String docId = userredis.get(request).getDocid();
        String fields = " distinct afl.id uid ,afl.*,date_format(afl.open_time_start,'%Y-%m-%d %H:%i')as starttime,date_format(afl.open_time_end,'%Y-%m-%d %H:%i')as endtime,war.auditcondition";
        String table = "tb_access_application afl " +
                "LEFT JOIN tb_workflow_approval_record as war on war.formid = afl.id  ";
        String where = "1=1";
        if (StringUtils.isNotBlank(status) && !("2").equals(status)) {
            where += " AND afl.status=" + status;
        }
        WorkFlowNodeDto nodeLevel = workFlowUtil.getNodeLevel(request, docId, 9);
        if (nodeLevel==null) {
            return Json.getJsonData(false, "该用户没有审批权限");
        }
        String condition = workFlowUtil.getAuditRecordsWhereSQL(request, 9,nodeLevel, "war.auditid", "war.auditid");
        if (StringUtils.isNotBlank(condition)) {
            where += condition;
        }
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND  afl.open_time_start between  '" + starttime + "' and '" + endtime + "'";
        }
        if (StringUtils.isNotBlank(key)) {
            where += " AND (afl.applicant_name like '" + key + "%' )";
        }

        where += " or war.auditid = '" + docId + "' ";
        String orderby = ExtSort.Orderby(request, " afl.created_at desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData getDoors(HttpServletRequest request, HttpServletResponse response) {
        String fields = " uid id, name ";
        String table = "tb_dev_door_name";
        String where = " 1=1 ";
        return dbService.QueryJsonData(fields, table, where, "name ASC", 0, 1000);
    }

    public JsonData getDepartmentPerson(HttpServletRequest request, HttpServletResponse response,String orgCode) {
        String sql = "select  su.code, su.name,su.empid infoId  from " +
                " tb_workflow_approvar wa join tb_workflow_node wn on wa.nodeid = wn.uid " +
                " join tb_workflow_mould wm on wm.uid = wn.mouldid " +
                " join tb_sys_user su on su.empid = wa.infoid " +
                " join tb_card_orgframework_user cou on cou.userid = su.uid " +
                " where orgcode = '" + orgCode + "' and wm.mouldtype = 9 and wn.nodelevel = 1 and wa.status is null ";
        JSONArray objects = dbService.QueryList(sql);
        if (objects.isEmpty()) {
             sql = "select  ct.code, ct.name,ct.uid infoId " +
                    " from tb_workflow_approvar wa " +
                    " join tb_workflow_node wn on wa.nodeid = wn.uid " +
                    " join tb_workflow_mould wm on wm.uid = wn.mouldid " +
                    " join tb_card_teachstudinfo ct on ct.uid = wa.infoid " +
                    " where wm.mouldtype = 9 and wn.nodelevel = 1 and wa.status = 1 ";
            objects = dbService.QueryList(sql);
        }
        JsonData jsonData = new JsonData();
        jsonData.setSuccess(true);
        jsonData.setData(objects);
        return jsonData;
    }

    public JsonData getSelectDoors(HttpServletRequest request, HttpServletResponse response,String uid) {
        JSONArray array = dbService.QueryList("select * from tb_access_application_dev where apply_id = ?", uid);
        List<String> doors =  new ArrayList<>();
        for (int i = 0; i < array.size(); i++) {
            JSONObject object = array.getJSONObject(i);
            String dev_id = object.getString("dev_id");
            String door = object.getString("door");
            if (StringUtils.isNotBlank(dev_id) && StringUtils.isNotBlank(door)) {
                for (int j = 4; j < door.length(); j++) {
                    if (door.charAt(j)==49){
                        JSONObject jsonObject = dbService.QueryJSONObject("select uid from tb_dev_door_name where controllerid = ? and relays=?", dev_id, 7 - j);
                        if (jsonObject!=null){
                            doors.add(jsonObject.getString("uid"));
                        }
                    }
                }
            }
        }
        String fields = " uid id, name,'' isSelected";
        String table = "tb_dev_door_name";
        String where = " 1=1 ";
        JsonData data = dbService.QueryJsonData(fields, table, where, "name ASC", 0, 1000);
        JSONArray dataData = data.getData();
        for (int i = 0; i < dataData.size(); i++) {
            if (doors.contains(dataData.getJSONObject(i).getString("id"))){
                dataData.getJSONObject(i).put("isSelected","true");
            }
        }
        return data;
    }

    public JsonResult saveDoorFrom(HttpServletRequest request, HttpServletResponse response, String docid, String deptcode, String starttime, String endtime,String infoId,
                                   String des, String devids, String count,String password, MultipartFile[] files,@RequestBody List<AuditApplyRequest.DeviceSetting> devices) throws IOException {
        SysUser sysUser = userredis.get(request);
        deptcode = sysUser.getOrgcode();
        // 上传图片
        String imgPath = "";
        // 1. 验证数据
//        if (files.length == 0) {
//            return Json.getJsonResult(false,"至少上传一张照片");
//        }

        // 2. 存储照片
        List<String> photoUrls = new ArrayList<>();
        if (files!=null) {
            for (MultipartFile file : files) {
                if (!file.isEmpty()) {
                    String originalFilename = file.getOriginalFilename();
                    String fileExtension = getFileExtension(originalFilename);
                    if (!fileExtension.equals("jpg") && !fileExtension.equals("jpeg")) {
                        return Json.getJsonResult(false, "图片格式不正确,请上传jpg或jpeg格式的图片");
                    }

                    String uniqueFilename = generateUniqueFilename(fileExtension);
                    //保存图片
                    imgPath = saveFile(file, WebConfig.getUploaddir(), uniqueFilename);
                    if (imgPath.length() == 0) {
                        Log.error(FaceService.class, "微信端照片附件上传：文件保存失败");
                        return Json.getJsonResult(false, "文件保存失败");
                    }
                    if (file.getSize() > 204800) {
                        ImageUtil.changeImageWH(WebConfig.getUploaddir() + imgPath, 1024, 1024);
                    }
                    photoUrls.add(imgPath);
                }
            }

        }

        String docId = sysUser.getDocid();
        String orgCode = sysUser.getOrgcode();
        if (sysUser.getUsertype() == 3) {
            //获取工作流模板为请假的模板id
            String sqlMouldId = "SELECT uid FROM tb_workflow_mould WHERE mouldtype = 9 ";
            JSONArray ja = dbService.QueryList(sqlMouldId);
            if (ja.isEmpty()) {
                return Json.getJsonResult(false, "未配置审批模板");
            }
            String mouldId = ja.getJSONObject(0).getString("uid");
            List<WorkFlowNodeDto> workFlowNodeDTOs = workFlowUtil.selSubPerson(mouldId, docId, infoId,orgCode, "", 1);
            if (workFlowNodeDTOs.isEmpty()) {
                return Json.getJsonResult(false, "未配置审核人，请联系管理员！");
            }
            StringBuilder sql = new StringBuilder();
            String appid = UUID.randomUUID().toString().replace("-", "");
            // 将路径列表转为逗号分隔字符串
            String dbImagePaths = String.join(",", photoUrls);
            if (StringUtils.isEmpty(password)||"undefined".equals(password)){
                password="";
            }

            sql.append("INSERT INTO tb_access_application(id,infoid,reason,open_time_start,open_time_end,created_at,status," +
                            "applicant_name,applicant_type,application_time,department,access_type,password,usage_count,attachment) ")
                    .append("values('").append(appid).append("','").append(docid).append("','").append(des).append("','").append(starttime)
                    .append("','").append(endtime).append("',now(),").append("0,'")
                    .append(sysUser.getName()).append("','").append(1).append("',").append("now()").append(",'").append(deptcode)
                    .append("',1,'").append(password).append("','").append(count).append("','").append(dbImagePaths).append("') ");
            dbService.excuteSql(sql.toString());
            if (StringUtils.isNotBlank(devids)) {
                Map<String,int[]> map = new HashMap<>();
                String[] devidlist = devids.split(",");
                for (int i = 0; i < devidlist.length; i++) {
                    JSONObject object = dbService.QueryJSONObject("select controllerid,name,relays from tb_dev_door_name where uid= ?", devidlist[i]);
                    String controllerid = object.getString("controllerid");
                    Integer relays = object.getInteger("relays");
                    if (!map.containsKey(controllerid)){
                        int[] a = new int[4];
                        a[0]=0;
                        a[1]=0;
                        a[2]=0;
                        a[3]=0;
                        a[3 - relays] = 1;
                        map.put(controllerid,a);
                    }else {
                        int[] ints = map.get(controllerid);
                        ints[3 - relays] = 1;
                        map.put(controllerid,ints);
                    }
                }
                for (String devid : map.keySet()) {
                    int[] ints = map.get(devid);
                    String door = "0000";
                    for (int anInt : ints) {
                        door+=anInt;
                    }
                    String insetsql = "INSERT INTO tb_access_application_dev(apply_id,dev_id,door,down_status) values (?,?,?,?)";
                    dbService.excuteSql(insetsql,appid,devid,door,0);
                }
            }
            if (!CollectionUtil.isEmpty(devices)) {
                for (AuditApplyRequest.DeviceSetting device : devices) {
                    String insertSql = "INSERT INTO tb_access_application_dev(apply_id,dev_id,floors,down_status) values (?,?,?,?)";
                    dbService.excuteSql(insertSql,appid,device.getDeviceId(),device.getFloors(),0);
                }
            }
            String insertRecordSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate,auditid) values (uuid(),?,?,?,?,now(),?)";
            dbService.excuteSql(insertRecordSql, appid, mouldId, 0, 1,infoId);
            //推送消息
            pushMessageHandler.pushNextDoorApply(appid, workFlowNodeDTOs);
        }
        if (sysUser.getUsertype() == 4) {
            //获取工作流模板为请假的模板id
            String sqlMouldId = "SELECT uid FROM tb_workflow_mould WHERE mouldtype = 9 ";
            JSONArray ja = dbService.QueryList(sqlMouldId);
            if (ja.isEmpty()) {
                return Json.getJsonResult(false, "未配置审批模板");
            }
            String mouldId = ja.getJSONObject(0).getString("uid");
            List<WorkFlowNodeDto> workFlowNodeDTOs = workFlowUtil.selSubPerson(mouldId, docId, infoId,orgCode, "", 1);
            if (workFlowNodeDTOs.isEmpty()) {
                return Json.getJsonResult(false, "未配置审核人，请联系管理员！");
            }
            StringBuilder sql = new StringBuilder();
            String appid = UUID.randomUUID().toString().replace("-", "");
            // 将路径列表转为逗号分隔字符串
            String dbImagePaths = String.join(",", photoUrls);
            if (StringUtils.isEmpty(password)){
                password="";
            }

            sql.append("INSERT INTO tb_access_application(id,infoid,reason,open_time_start,open_time_end,created_at,status,applicant_name,applicant_type,application_time,department,access_type,password,usage_count,attachment) ").append("values('").append(appid).append("','").append(docid).append("','").append(des).append("','").append(starttime).append("','").append(endtime).append("',now(),").append("0,'")
                    .append(sysUser.getName()).append("','").append(2).append("',").append("now()").append(",'").append(sysUser.getOrgname()).append("',2,'").append(password).append("','").append(count).append("','").append(dbImagePaths).append("') ");
            dbService.excuteSql(sql.toString());
            Map<String,int[]> map = new HashMap<>();
            String[] devidlist = devids.split(",");
            for (int i = 0; i < devidlist.length; i++) {
                JSONObject object = dbService.QueryJSONObject("select controllerid,name,relays from tb_dev_door_name where uid= ?", devidlist[i]);
                String controllerid = object.getString("controllerid");
                Integer relays = object.getInteger("relays");
                if (!map.containsKey(controllerid)){
                    int[] a = new int[4];
                    a[0]=0;
                    a[1]=0;
                    a[2]=0;
                    a[3]=0;
                    a[3-relays] = 1;
                    map.put(controllerid,a);
                }else {
                    int[] ints = map.get(controllerid);
                    ints[3-relays] = 1;
                    map.put(controllerid,ints);
                }
            }
            for (String devid : map.keySet()) {
                int[] ints = map.get(devid);
                String door = "0000";
                for (int anInt : ints) {
                    door+=anInt;
                }
                String insetsql = "INSERT INTO tb_access_application_dev(apply_id,dev_id,door,down_status) values (?,?,?,?)";
                dbService.excuteSql(insetsql,appid,devid,door,0);
            }
            String insertRecordSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate,auditid) values (uuid(),?,?,?,?,now(),?)";
            dbService.excuteSql(insertRecordSql, appid, mouldId, 0, 1,infoId);
            //推送消息
            pushMessageHandler.pushNextDoorApply(appid, workFlowNodeDTOs);
        }
        return Json.getJsonResult(true);
    }

    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return (lastDotIndex == -1) ? "" : filename.substring(lastDotIndex + 1);
    }

    private String generateUniqueFilename(String fileExtension) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid + "." + fileExtension;
    }
    public static String saveFile(MultipartFile file, String uploadDir, String filename) throws IOException {
        // 构建目标文件
        File targetFile = new File(uploadDir + "face/", filename);

        // 创建目标文件夹
        if (!targetFile.getParentFile().exists()) {
            targetFile.getParentFile().mkdirs();
        }

        // 保存文件
        try (FileOutputStream fos = new FileOutputStream(targetFile)) {
            fos.write(file.getBytes());
            return "/face/" + filename + "";
        } catch (IOException e) {
            throw new IOException("Failed to save file: " + filename, e);
        }
    }

    public R<?> getDoorRecordDetails(HttpServletRequest request, HttpServletResponse response, String uid) {


        String auditRecordSQL = "SELECT ct.code,ct.name,wap.remark,wap.auditcondition,date_format(wap.auditdate, '%Y-%m-%d %H:%i:%s') as auditdate,wap.node_level as currentauditnodelevel " +
                "FROM tb_workflow_approval_record  as wap " +
                "JOIN  tb_card_teachstudinfo as ct on wap.auditid = ct.uid " +
                "WHERE formid=? and auditcondition != 0 " +
                "order by wap.auditdate desc";
        JSONArray auditRecord = dbService.QueryList(auditRecordSQL, uid);
        return R.ok(auditRecord);
    }

    public JsonData savedoorfrom(HttpServletRequest request, HttpServletResponse response,String uid,String starttime,String endtime,String password,String usagecount,String devids) {
        dbService.excuteSql("update tb_access_application set open_time_start=?,open_time_end=?,password=?,usage_count=? where id =?",starttime,endtime,password,usagecount,uid);
        dbService.excuteSql("delete from tb_access_application_dev where apply_id = ?",uid);
        Map<String,int[]> map = new HashMap<>();
        String[] devidlist = devids.split(",");
        for (int i = 0; i < devidlist.length; i++) {
            JSONObject object = dbService.QueryJSONObject("select controllerid,name,relays from tb_dev_door_name where uid= ?", devidlist[i]);
            String controllerid = object.getString("controllerid");
            Integer relays = object.getInteger("relays");
            if (!map.containsKey(controllerid)){
                int[] a = new int[4];
                a[0]=0;
                a[1]=0;
                a[2]=0;
                a[3]=0;
                a[relays] = 1;
                map.put(controllerid,a);
            }else {
                int[] ints = map.get(controllerid);
                ints[relays] = 1;
                map.put(controllerid,ints);
            }
        }
        for (String devid : map.keySet()) {
            int[] ints = map.get(devid);
            String door = "0000";
            for (int anInt : ints) {
                door+=anInt;
            }
            String insetsql = "INSERT INTO tb_access_application_dev(apply_id,dev_id,door,down_status) values (?,?,?,?)";
            dbService.excuteSql(insetsql,uid,devid,door,0);
        }
        return Json.getJsonData(true);
    }

    public JsonResult auditdoor(HttpServletRequest request, String uid, String infoId, Integer status, String remark, List<AuditApplyRequest.DeviceSetting> deviceList) throws Exception {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "当前申请被取消！");
        }
        if (!CollectionUtil.isEmpty(deviceList)) {
            for (AuditApplyRequest.DeviceSetting deviceSetting : deviceList) {
                if (StringUtils.isNotBlank(deviceSetting.getFloors())) {
                    //修改授权楼层
                    String editSql = "update tb_access_application_dev set floors = ? where dev_id = ? and apply_id = ? ";
                    dbService.excuteSql(editSql, deviceSetting.getFloors(), deviceSetting.getDeviceId(), uid);
                } else {
                    //修改授权楼层
                    String editSql = "update tb_access_application_dev set floors = ? ,down_status = 3 where dev_id = ? and apply_id = ? ";
                    dbService.excuteSql(editSql, null,deviceSetting.getDeviceId(), uid);
                }

            }
        }
        String table = "tb_access_application";
        String field = "";
        String name = userredis.get(request).getName();
        String userId = userredis.get(request).getDocid();
        return workFlowUtil.submitForApproval(request, uid,userId,name, status, remark, table, field,"","");
    }

    public JsonResult cancelDoor(String uid) {
        JSONArray array = dbService.QueryList("select * from tb_workflow_approval_record where formid = ? order by createdate asc limit 1", uid);
        if (array.size()>0){
            if (StringUtils.isNotEmpty(array.getJSONObject(0).getString("auditdate"))){
                return Json.getJsonResult(false, "已有审核节点被审核，无法取消！");
            }
        }
        String delSQL = "update tb_access_application set status = 2  WHERE uid=?";
        dbService.excuteSql(delSQL, uid);
        String updateSQl = "update tb_workflow_approval_record set auditcondition = 2 ,remark = '" + "单据已被取消" + "' where formid = '" + uid + "' and auditcondition = 0";
        dbService.excuteSql(updateSQl);
        return Json.getJsonResult(true);
    }

    @Autowired
    private SysConfigService sysConfigService;

    public JsonData getElevatorFloors(HttpServletRequest request, String deviceId, String infoType, String uid) {
        //获取申请订单楼层
        String queryOrderSql = "select floors from tb_access_application_dev where apply_id = '" + uid + "' and dev_id = '" + deviceId + "' ";
        String floorsStr = dbService.queryOneField(queryOrderSql, String.class);
        List<String> selFloorList = Arrays.asList(floorsStr.split(","));
        //是否开启梯控权限过滤
        JsonData jsonData = new JsonData();
        if ("1".equals(sysConfigService.get("elevatorFloors")) && StringUtils.isBlank(infoType)) {
            String docId = userredis.get(request).getDocid();
            String sql = "SELECT code as id, name " +
                    " FROM tb_elevator_devicestorey " +
                    " WHERE devid = '" + deviceId + "' " +
                    " order by code";
            List<Elevator> elevators = dbService.queryList(sql, Elevator.class);
            // 假设 Elevator 有 getId() 和 getName() 方法
            Map<Integer, String> floorNameMap = elevators.stream().collect(Collectors.toMap(
                    Elevator::getId, // key
                    Elevator::getName                 // value
            ));
            //判断人员楼层权限
            String SQL = "SELECT storeys " +
                    " FROM tb_elevator_t_authrize " +
                    " WHERE devid = '" + deviceId + "' " +
                    " and infoid = '" + docId + "'";
            String storeys = dbService.queryOneField(SQL, String.class);
            if (StringUtils.isNotBlank(storeys)) {
                List<String> floorList = Arrays.stream(storeys.split(",")).collect(Collectors.toList());
                JSONArray jsonArray = new JSONArray();
                int i = 0;
                for (String floor : floorList) {
                    i++;
                    if ("1".equals(floor)) {
                        JSONObject object = new JSONObject();
                        object.put("id", i);
                        object.put("name", floorNameMap.get(i));
                        // 新增：判断是否为申请订单楼层
                        boolean isSelected = selFloorList.size() >= i && "1".equals(selFloorList.get(i - 1));
                        object.put("selected", isSelected);
                        jsonArray.add(object);
                    }
                }
                jsonData.setData(jsonArray);
            }
            jsonData.setSuccess(true);
        } else {
            String SQL = "SELECT code as id, name " +
                    " FROM tb_elevator_devicestorey " +
                    " WHERE devid = '" + deviceId + "' " +
                    " order by code";
            List<Elevator> elevators = dbService.queryList(SQL, Elevator.class);
            JSONArray jsonArray = new JSONArray();
            int i = 0;
            // 假设 Elevator 有 getId() 和 getName() 方法
            for (Elevator elevator : elevators) {
                i++;
                JSONObject object = new JSONObject();
                object.put("id", elevator.getId());
                object.put("name", elevator.getName());
                // 新增：判断是否为申请订单楼层
                boolean isSelected = selFloorList.size() >= i && "1".equals(selFloorList.get(i - 1));
                object.put("selected", isSelected);
                jsonArray.add(object);
            }
            jsonData.setData(jsonArray);
            jsonData.setSuccess(true);
        }
        return jsonData;

    }

    public JsonData getAllElevatorDevices() {
        String SQL = "SELECT uid, devclass, devname" +
                " FROM tb_dev_accesscontroller" +
                " WHERE devclass = '12'";
        JsonData jsonData = dbService.QueryJsonData(SQL);
        return jsonData;
    }
}
