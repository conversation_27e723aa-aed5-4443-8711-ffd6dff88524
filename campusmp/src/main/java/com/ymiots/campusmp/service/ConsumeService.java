package com.ymiots.campusmp.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.entity.CardInfo;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.entity.Transactions;
import com.ymiots.campusmp.model.R;
import com.ymiots.campusmp.redis.RedisMessagePublish;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.utils.weixin.entity.WeiXinPayNotify;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Repository
public class ConsumeService extends BaseService {

    @Autowired
    UserRedis userredis;

    @Autowired
    CardManageService cardManageService;

    @Autowired
    RedisMessagePublish redismsg;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 插入第三方系统支付表
     */
    public R<?> insertPayRecord(HttpServletRequest request, HttpServletResponse response, String out_trade_no, String amount, String docId, String body, String photoOptionId) {
        String saveOrderSql = "insert into fy_order(mchnt_order_no,os_order_no,mchnt_code,order_type," +
                "total_amount,trans_dt,term_id) values (?,?,?,?,?,now(),?)";
        int count = dbService.excuteSql(saveOrderSql, out_trade_no, out_trade_no, WebConfig.getWeixinMchid(), "JSAPI",amount, docId);
        if (count > 0) {
            return R.ok();
        } else {
            return R.fail("订单创建失败");
        }
    }

    /**
     * @param request
     * @param response
     * @param out_trade_no
     * @param cardid
     * @param docid
     * @param body         充值备注
     * @return
     */
    public JsonResult insertTransaction(HttpServletRequest request, HttpServletResponse response, String out_trade_no, String cardid, String docid, String body, String wallet, String photoOptionId) {
        if (!"3".equals(wallet)) {
            String sql = "select uid from tb_card_cardinfo where uid='" + cardid + "' and infoid='" + docid + "' and enddate>=now() and status=1 ";
            JSONArray cardlist = dbService.QueryList(sql);
            if (cardlist.size() == 0) {
                return Json.getJsonResult("卡片已过期或已挂失");
            }
        }
        String wxuserid = userredis.get(request).getUid();
        saveToRedis(out_trade_no, cardid);
        String insertTransaction = "INSERT INTO tb_weixin_transaction (uid,wxuserid,infoid,cardid,remark,ispayed,createdate,status,photo_option_id) VALUES (?,?,?,?,?,0,now(),1,?)";
        int count = dbService.excuteSql(insertTransaction, out_trade_no, wxuserid, docid, cardid, body, photoOptionId);
        if (count > 0) {
            return Json.getJsonResult(true);
        } else {
            return Json.getJsonResult("订单创建失败");
        }
    }

    public void saveToRedis(String outTradeNo, String cardId) {
        // 生成一个唯一的键（例如：使用 out_trade_no 作为键）
        String redisKey = "transaction:" + outTradeNo;
        // 将对象保存到 Redis 中
        redisTemplate.opsForValue().set(redisKey, cardId, 3, TimeUnit.MINUTES);
    }

    public JsonResult updateTransactionBak(WeiXinPayNotify weiXinPayNotify, String wallet) {
        String uid = weiXinPayNotify.getOut_trade_no();
        int totalFee = weiXinPayNotify.getTotal_fee();
        double money = totalFee / 100.00;
        String cardId = getFromRedis(uid);
        if (StringUtils.isBlank(cardId)) {
            String sql = "select cardid from tb_weixin_transaction where uid = '" + weiXinPayNotify.getOut_trade_no() + "'";
            cardId = dbService.queryOneField(sql, String.class);
            if (StringUtils.isBlank(cardId)) {
                String sql2 = "select cc.uid from tb_weixin_user wu " +
                        "join tb_card_cardinfo cc on cc.infoid = wu.docid " +
                        " where openid = '" + weiXinPayNotify.getOpenid() + "' " +
                        " and cc.status = 1 and ismain = 1 and enddate>=now() ";
                cardId = dbService.queryOneField(sql2, String.class);
            }
        }
        if (StringUtils.isBlank(cardId)) {
            Log.error(ConsumeService.class, "找不到相关人员信息");
            return Json.getJsonResult("找不到相关人员信息");
        }
        String selCardInfoSql = "select infoid infoId,cardno cardNo from tb_card_cardinfo where uid = '" + cardId + "'";
        cardInfoDTO cardInfoDTO = dbService.queryForOne(selCardInfoSql, cardInfoDTO.class);
        String cardNo = cardInfoDTO.getCardNo();
        String infoId = cardInfoDTO.getInfoId();
        if (StringUtils.isBlank(wallet)) {
            wallet = "1";
        }
        if ("1".equals(wallet)) {
            String sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,interfixid,tradedate,createdate,status) "
                    + "values(?,?,?,?,1,?,1,2,1,1,'微信充值',?,now(),now(),1)";
            dbService.excuteSql(sql, weiXinPayNotify.getOut_trade_no(), cardId, infoId, cardNo, money, weiXinPayNotify.getTransaction_id());
        } else if ("2".equals(wallet)) {
            String sql = "insert into tb_waterctrl_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,orderstatus,payway,paytype,des,interfixid,tradedate,createdate,status) "
                    + "values(?,?,?,?,1,?,2,1,1,'微信充值',?,now(),now(),1)";
            dbService.excuteSql(sql, weiXinPayNotify.getOut_trade_no(), cardId, infoId, cardNo, money, weiXinPayNotify.getTransaction_id());
        } else {
            String sql = "insert into tb_phone_transaction_detail(uid,info_id,money,pay_type,des,interfix_id,trade_date,create_date,creator_id,status) "
                    + "values(?,?,?,1,'微信充值',?,now(),now(),?,1)";
            dbService.excuteSql(sql, weiXinPayNotify.getOut_trade_no(),infoId, money, weiXinPayNotify.getTransaction_id(), infoId);
        }
//        String sql = "insert into " +
//                "fy_flow(flow_no, top_mchnt_order_no, top_os_order_no, mchnt_code, amount, order_type," +
//                " opt_type, opt_date, result_msg) " +
//                "values(uuid(),?,?,?,?,?,?,now(),?)";
//        dbService.excuteSql(sql,
//                weiXinPayNotify.getTransaction_id(),
//                weiXinPayNotify.getOut_trade_no(),
//                weiXinPayNotify.getMch_id(),
//                money,
//                weiXinPayNotify.getTrade_type(),
//                "SUCCESS",
//                "SUCCESS"
//        );
        return Json.getJsonResult(true);
    }

    @Data
    public static class cardInfoDTO {
        private String infoId;
        private String cardNo;
    }

    /**
     * 第三方充值
     */
    public JsonResult updateTransaction(String osOrderNo, String transactionId, Integer total, Date tradeDate, String wallet) {
        return updateTransaction(osOrderNo, transactionId, total, tradeDate, wallet, "");
    }

    public JsonResult updateTransaction(String osOrderNo, String transactionId, Integer total, Date tradeDate, String wallet, String mchNtOrderNo) {
        double money = total / 100.00;
        String cardId = getFromRedis(osOrderNo);
        if (tradeDate == null) {
            tradeDate = new Date();
        }
        if (StringUtils.isBlank(cardId)) {
            String sql = "select cardid from tb_weixin_transaction where uid = '" + osOrderNo + "'";
            cardId = dbService.queryOneField(sql, String.class);
        }
        if (StringUtils.isBlank(cardId)) {
            Log.error(ConsumeService.class, "找不到相关人员信息");
            return Json.getJsonResult("找不到相关人员信息");
        }
        String selCardInfoSql = "select infoid infoId,cardno cardNo from tb_card_cardinfo where uid = '" + cardId + "'";
        cardInfoDTO cardInfoDTO = dbService.queryForOne(selCardInfoSql, cardInfoDTO.class);
        String cardNo = cardInfoDTO.getCardNo();
        String infoId = cardInfoDTO.getInfoId();
        if (StringUtils.isBlank(wallet)) {
            wallet = "1";
        }
        if ("1".equals(wallet)) {
            String sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,interfixid,tradedate,createdate,status) "
                    + "values(?,?,?,?,1,?,1,2,1,1,'微信充值',?,?,now(),1)";
            dbService.excuteSql(sql, osOrderNo, cardId, infoId, cardNo, money, osOrderNo,tradeDate);
        } else if ("2".equals(wallet)) {
            String sql = "insert into tb_waterctrl_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,orderstatus,payway,paytype,des,interfixid,tradedate,createdate,status) "
                    + "values(?,?,?,?,1,?,2,1,1,'微信充值',?,?,now(),1)";
            dbService.excuteSql(sql, osOrderNo, cardId, infoId, cardNo, money, osOrderNo,tradeDate);
        } else {
            String sql = "insert into tb_phone_transaction_detail(uid,info_id,money,pay_type,des,interfix_id,trade_date,create_date,creator_id,use_time,status) "
                    + "values(uuid(),?,?,1,'微信充值',?,?,now(),?,0,1)";
            dbService.excuteSql(sql, infoId, money, osOrderNo, tradeDate,infoId);
        }
        //更新为充值成功
        String updateSql = "update fy_order set order_type = 'JSAPI',result_msg = '充值成功',trans_stat = 'SUCCESS' where os_order_no = '" + osOrderNo + "'";
        dbService.excuteSql(updateSql);
        return Json.getJsonResult(true);
    }

    public String getFromRedis(String outTradeNo) {
        // 生成与保存时相同的键
        String redisKey = "transaction:" + outTradeNo;

        // 从 Redis 中获取对象
        return (String) redisTemplate.opsForValue().get(redisKey);
    }

    @Data
    public static class WeChatChargeDto {
        private Integer isPayed;
        private String infoId;
    }


    public JsonData getWeiXinPayResult(HttpServletRequest request, HttpServletResponse response, String outtradeno) {
        String sql = "SELECT t.uid,t.transactionid,IFNULL(td.money, '订单异常，24小时内到账') AS totalfee,date_format(td.tradedate, '%Y-%m-%d %H:%i:%s') as timeend ,t.deviceinfo,t.remark " +
                "FROM tb_weixin_transaction t left join tb_card_transaction_detail td on t.uid = td.uid " +
                "WHERE t.uid = '" + outtradeno + "'";
        JsonData jd = dbService.QueryJsonData(sql);
        return jd;
    }

    public JsonData getPayRecordsList(HttpServletRequest request, HttpServletResponse response, String cardid, int start, int limit, String startTime, String endTime, String uid) {

        String fields = "t.uid, date_format(t.recordtime, '%Y-%m-%d %H:%i:%s') as recordtime, t.money, t.paytype ";
        String where = " infoid = '" + uid + "'";
        //String where = " infoid = '" + uid + "' and cardid='" + cardid + "' ";
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            where += "and (recordtime between '" + startTime + ":00' AND '" + endTime + ":59') ";
        }
        String table = " (select uid,money,recordtime,paytype from tb_consume_payrecords where " + where + " "
                + " union all "
                + " select uid,money,recordtime,paytype from tb_consume_payrecords_sleep where " + where + ") t ";

        String orderby = " t.recordtime DESC ";
        String money = dbService.QueryJSONObject("select sum(t.money) as money from " + table).getString("money");
        JsonData db = dbService.QueryJsonData(fields, table, "", orderby, start, limit);
        db.setMsg(money);
        return db;
    }

    public List<Transactions> getTransactionsList(HttpServletRequest request, HttpServletResponse response, String type,String start_date, String end_date,String infoid) {
        List<Transactions> transactionsList = new ArrayList<>();
        if ("all".equals(type)) {
            List<Transactions> list = dbService.queryList("select a.uid,date_format(recordtime, '%Y-%m-%d') date,date_format(recordtime, '%H:%i:%s') time,-money amount, case when recordtype =2 then '刷卡' when recordtype=1 then '刷脸' else '刷卡' end AS paymentType,CONCAT(b.name,'消费') as type,1 as flagtype  from tb_consume_payrecords a left join tb_consume_scheme b on a.schemeid= b.uid where date_format(recordtime, '%Y-%m-%d') between '" + start_date + "' and '" + end_date + "' and infoid = '" + infoid + "' " +
                    "UNION ALL " +
                    "select a.uid,date_format(recordtime, '%Y-%m-%d') date,date_format(recordtime, '%H:%i:%s') time,-money amount, case when recordtype =2 then '卡' when recordtype=1 then '刷脸' else '刷卡' end AS paymentType,CONCAT(b.name,'消费') as type,1 as flagtype  from tb_consume_payrecords_sleep a left join tb_consume_scheme b on a.schemeid= b.uid where date_format(recordtime, '%Y-%m-%d') between '" + start_date + "' and '" + end_date + "' and infoid = '" + infoid + "' ORDER BY date DESC, time DESC", Transactions.class);

            List<Transactions> transactions = dbService.queryList("select a.uid,date_format(tradedate, '%Y-%m-%d') date,date_format(tradedate, '%H:%i:%s') time,money amount, LEFT(b.name, 1) AS paymentType,b.name as type,2 as flagtype  from tb_card_transaction_detail a " +
                    "LEFT JOIN tb_sys_dictionary b on a.tradetype=b.code and b.groupcode='SYS0000009' where date_format(tradedate, '%Y-%m-%d') between '" + start_date + "' and '" + end_date + "' and infoid = '" + infoid + "' " +
                    "UNION ALL " +
                    "select a.uid,date_format(tradedate, '%Y-%m-%d') date,date_format(tradedate, '%H:%i:%s') time,money amount, LEFT(b.name, 1) AS paymentType,b.name as type,2 as flagtype  from tb_card_transaction_detail_sleep a " +
                    "LEFT JOIN tb_sys_dictionary b on a.tradetype=b.code and b.groupcode='SYS0000009' where date_format(tradedate, '%Y-%m-%d') between '" + start_date + "' and '" + end_date + "' and infoid = '" + infoid + "' ORDER BY date DESC, time DESC", Transactions.class);

            transactionsList.addAll(list);
            transactionsList.addAll(transactions);
        } else if ("spend".equals(type)) {
            List<Transactions> list = dbService.queryList("select a.uid,date_format(recordtime, '%Y-%m-%d') date,date_format(recordtime, '%H:%i:%s') time,-money amount, case when recordtype =2 then '刷卡' when recordtype=1 then '刷脸' else '刷卡' end AS paymentType,CONCAT(b.name,'消费') as type,1 as flagtype  from tb_consume_payrecords a left join tb_consume_scheme b on a.schemeid= b.uid where date_format(recordtime, '%Y-%m-%d') between '" + start_date + "' and '" + end_date + "' and infoid = '" + infoid + "' " +
                    "UNION ALL " +
                    "select a.uid,date_format(recordtime, '%Y-%m-%d') date,date_format(recordtime, '%H:%i:%s') time,-money amount, case when recordtype =2 then '卡' when recordtype=1 then '刷脸' else '刷卡' end AS paymentType,CONCAT(b.name,'消费') as type,1 as flagtype  from tb_consume_payrecords_sleep a left join tb_consume_scheme b on a.schemeid= b.uid where date_format(recordtime, '%Y-%m-%d') between '" + start_date + "' and '" + end_date + "' and infoid = '" + infoid + "' ORDER BY date DESC, time DESC", Transactions.class);

            transactionsList.addAll(list);
        } else if ("recharge".equals(type)) {
            List<Transactions> transactions = dbService.queryList("select a.uid,date_format(tradedate, '%Y-%m-%d') date,date_format(tradedate, '%H:%i:%s') time,money amount, LEFT(b.name, 1) AS paymentType,b.name as type,2 as flagtype  from tb_card_transaction_detail a " +
                    "LEFT JOIN tb_sys_dictionary b on a.tradetype=b.code and b.groupcode='SYS0000009' where date_format(tradedate, '%Y-%m-%d') between '" + start_date + "' and '" + end_date + "' and infoid = '" + infoid + "' " +
                    "UNION ALL " +
                    "select a.uid,date_format(tradedate, '%Y-%m-%d') date,date_format(tradedate, '%H:%i:%s') time,money amount, LEFT(b.name, 1) AS paymentType,b.name as type,2 as flagtype  from tb_card_transaction_detail_sleep a " +
                    "LEFT JOIN tb_sys_dictionary b on a.tradetype=b.code and b.groupcode='SYS0000009' where date_format(tradedate, '%Y-%m-%d') between '" + start_date + "' and '" + end_date + "' and infoid = '" + infoid + "' ORDER BY date DESC, time DESC", Transactions.class);

            transactionsList.addAll(transactions);
        }


        return transactionsList;
    }

    public JsonData getpayDetail(HttpServletRequest request, HttpServletResponse response, String uid) {
        String fields = "pay.uid, pay.cardno, date_format(pay.recordtime, '%Y-%m-%d %H:%i:%s')as recordtime, pay.money, pay.machineid,pay.paytype,pay.paywallet, " +
                " dev.devname,mer.mername,mer.name, place.name as placename,info.name as infoname,info.code,date_format(pay.createdate, '%Y-%m-%d %H:%i:%s')as createdate,pay.status  ";

        String where = " uid = '" + uid + "' ";
        String table = " (select uid, cardid, cardno, recordtime, money, machineid, eventcode, paytype, devid, merid, placeid, schemeid, infoid, createdate, status, serviceuid, paywallet, des, backid, recordimg from tb_consume_payrecords where " + where + " union all select uid, cardid, cardno, recordtime, money, machineid, eventcode, paytype, devid, merid, placeid, schemeid, infoid, createdate, status, serviceuid, paywallet, des, backid, recordimg from tb_consume_payrecords_sleep where " + where + ") pay " +
                "LEFT JOIN tb_card_cardinfo card ON card.uid = pay.cardid " +
                "LEFT JOIN tb_dev_accesscontroller dev ON dev.uid = pay.devid " +
                "LEFT JOIN tb_consume_merchant mer ON mer.uid = pay.merid " +
                "LEFT JOIN tb_consume_merchant_place place ON place.uid = pay.placeid " +
                "LEFT JOIN tb_card_teachstudinfo info ON info.uid = pay.infoid ";

        JsonData ja = dbService.QueryJsonData(fields, table, "", "");
        return ja;
    }

    public JSONObject getDetail(HttpServletRequest request, HttpServletResponse response, String uid,int flagtype) {
        JSONObject object = new JSONObject();
        if (flagtype==1){
            String fields = "pay.uid, pay.cardno, date_format(pay.recordtime, '%Y-%m-%d %H:%i:%s')as recordtime, pay.money, pay.machineid,pay.paytype,case when pay.paywallet = 1 then '主钱包' when pay.paywallet = 2 then '副钱包' when pay.paywallet = 0 then '计次' end as paywalletname , " +
                    " dev.devname,mer.mername,mer.name, place.name as placename,info.name as infoname,info.code,date_format(pay.createdate, '%Y-%m-%d %H:%i:%s')as createdate,pay.status  ";

            String where = " uid = '" + uid + "' ";
            String table = " (select uid, cardid, cardno, recordtime, money, machineid, eventcode, paytype, devid, merid, placeid, schemeid, infoid, createdate, status, serviceuid, paywallet, des, backid, recordimg from tb_consume_payrecords where " + where + " union all select uid, cardid, cardno, recordtime, money, machineid, eventcode, paytype, devid, merid, placeid, schemeid, infoid, createdate, status, serviceuid, paywallet, des, backid, recordimg from tb_consume_payrecords_sleep where " + where + ") pay " +
                    "LEFT JOIN tb_card_cardinfo card ON card.uid = pay.cardid " +
                    "LEFT JOIN tb_dev_accesscontroller dev ON dev.uid = pay.devid " +
                    "LEFT JOIN tb_consume_merchant mer ON mer.uid = pay.merid " +
                    "LEFT JOIN tb_consume_merchant_place place ON place.uid = pay.placeid " +
                    "LEFT JOIN tb_card_teachstudinfo info ON info.uid = pay.infoid ";

            JsonData ja = dbService.QueryJsonData(fields, table, "", "");
            object = ja.getData().getJSONObject(0);
        }

        if (flagtype==2){
            String fields = "ct.name,ctd.cardno,ct.code,ctd.money,sd.name payway,tsd.name paytype,tssd.name tradetype,date_format(ctd.tradedate, '%Y-%m-%d %H:%i:%s') as tradedate,ctd.des,wt.uid wtuid,wt.transactionid  ";
            String where = " uid = '" + uid + "' ";

            String table = "(select * from tb_card_transaction_detail where " + where + "  union all select * from tb_card_transaction_detail_sleep where " + where + ") ctd " +
                    "LEFT JOIN tb_card_teachstudinfo ct on ct.uid = ctd.infoid " +
                    "LEFT JOIN tb_sys_dictionary sd on  ctd.payway = sd.code and sd.groupcode = 'SYS0000008' " +
                    "LEFT JOIN tb_sys_dictionary tsd on ctd.paytype = tsd.code and tsd.groupcode = 'SYS0000030' " +
                    "LEFT JOIN tb_sys_dictionary tssd on ctd.tradetype = tssd.code and tssd.groupcode = 'SYS0000009' " +
                    "LEFT JOIN tb_weixin_transaction wt on wt.uid = ctd.interfixid ";

            JsonData ja = dbService.QueryJsonData(fields, table, "", "");
            object = ja.getData().getJSONObject(0);
        }


        return object;
    }

    public JsonData getTrandetailList(HttpServletRequest request, HttpServletResponse response, String cardid, int start, int limit, String startTime, String endTime, String uid) {

        String fields = "t.uid,t.money,date_format(t.tradedate, '%Y-%m-%d %H:%i:%s') as tradedate";

        String where = " infoid= '" + uid + "' ";
        //String where = " infoid= '" + uid + "' and cardid='" + cardid + "' ";
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            startTime = startTime.replace("T", " ");
            endTime = endTime.replace("T", " ");
            where += "and tradedate>'" + startTime + ":00' AND tradedate<'" + endTime + ":59' ";
        }
        String table = " (select uid,money,tradedate from tb_card_transaction_detail where " + where + " "
                + " union all "
                + " select uid,money,tradedate from tb_card_transaction_detail_sleep  where " + where + " "
                + " ) t";
        String money = dbService.QueryJSONObject("select sum(t.money) as money from " + table).getString("money");
        JsonData list = dbService.QueryJsonData(fields, table, "", " t.tradedate desc ", start, limit);
        list.setMsg(money);
        return list;
    }

    public JsonData getTranDetail(HttpServletRequest request, HttpServletResponse response, String uid) {
        String fields = "ct.name,ctd.cardno,ct.code,ctd.money,sd.name payway,tsd.name paytype,tssd.name tradetype,date_format(ctd.tradedate, '%Y-%m-%d %H:%i:%s') as tradedate,ctd.des,wt.uid wtuid,wt.transactionid  ";
        String where = " uid = '" + uid + "' ";

        String table = "(select * from tb_card_transaction_detail where " + where + "  union all select * from tb_card_transaction_detail_sleep where " + where + ") ctd " +
                "LEFT JOIN tb_card_teachstudinfo ct on ct.uid = ctd.infoid " +
                "LEFT JOIN tb_sys_dictionary sd on  ctd.payway = sd.code and sd.groupcode = 'SYS0000008' " +
                "LEFT JOIN tb_sys_dictionary tsd on ctd.paytype = tsd.code and tsd.groupcode = 'SYS0000030' " +
                "LEFT JOIN tb_sys_dictionary tssd on ctd.tradetype = tssd.code and tssd.groupcode = 'SYS0000009' " +
                "LEFT JOIN tb_weixin_transaction wt on wt.uid = ctd.interfixid ";

        JsonData ja = dbService.QueryJsonData(fields, table, "", "");
        return ja;
    }


    public JsonData getCardList(HttpServletRequest request, HttpServletResponse response) {
        SysUser user = userredis.get(request);
        String sql = "SELECT uid,usedes,cardno,ismain,status FROM tb_card_cardinfo where infoid='" + user.getDocid() + "' and status>0;";
        JsonData result = dbService.QueryJsonData(sql);
        return result;
    }

    public BigDecimal getAccountMoney(String infoid) {
        String sql = "SELECT uid,usedes,cardno,ismain,status FROM tb_card_cardinfo where infoid='" + infoid + "' and status=1 limit 1;";
        JSONObject object = dbService.QueryJSONObject(sql);
        BigDecimal bigDecimal = BigDecimal.ZERO;
        if (object!=null){
            CardInfo cardInfo = cardManageService.GetCardInfoByNo(object.getString("cardno"), object.getString("uid"));
            bigDecimal=cardInfo.getBalance();
        }

        return bigDecimal;
    }

}
