package com.ymiots.campusmp.service;

import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.MD5;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.redis.UserRedis;

@Repository
public class ExperienceService extends BaseService{

	@Autowired
	UserRedis userredis;
	
	public JsonResult WeixinBind(HttpServletRequest request, HttpServletResponse response, String openid, int usertype, String name,
			String idcard, String linkname, String linktel, String code, String pwd, String headimgurl, String nickname, String sex) {
		
		int existscount=dbService.getCount("tb_weixin_user","openid='"+openid+"'");
		if(existscount>0) {
			return Json.getJsonResult("您已经申请了体验账号，请先解除绑定！");
		}
		JSONArray userlist=new JSONArray();
		String idcode="";
		if(usertype==0) {
			idcode=code;
			userlist=dbService.QueryList("select i.uid as docid, i.code,i.name,i.orgcode,getorgname(i.orgcode) as orgname,0 as usertype,i.status from tb_card_teachstudinfo i inner join tb_sys_user u on u.empid=i.uid where u.code='"+code+"' and u.password='"+MD5.MD5Encode(pwd)+"' and u.usertype=0 ");
			if(userlist.size()==0) {
				int count=dbService.getCount("tb_card_teachstudinfo", "");
				String docid=UUID.randomUUID().toString();
				name=String.format("访客%s",count+1);
				String sql="insert into tb_card_teachstudinfo(uid,code,name,infotype,orgcode,createdate,status) values(?,?,?,1,'001',now(),1)";
				dbService.excuteSql(sql, docid,count+1,name);
				sql="insert into tb_sys_user(uid,code,password,name,empid,groupid,usertype,logintime,createdate,status) ";
				sql+=" values(uuid(),?,?,?,?,1,0,0,now(),1)";
				dbService.excuteSql(sql, code, MD5.MD5Encode(pwd), name, docid);
				userlist=dbService.QueryList("select i.uid as docid, i.code,i.name,i.orgcode,getorgname(i.orgcode) as orgname,0 as usertype,i.status from tb_card_teachstudinfo i inner join tb_sys_user u on u.empid=i.uid where u.code='"+code+"' and u.password='"+MD5.MD5Encode(pwd)+"' and u.usertype=0 ");
			}
		}else if(usertype==1) {
			idcode=idcard;
			userlist=dbService.QueryList("select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,1 as usertype,status from tb_card_teachstudinfo where name='"+name+"' and idcard='"+idcard+"' and infotype=2 ");
			if(userlist.size()==0) {
				int count=dbService.getCount("tb_card_teachstudinfo", "");
				String docid=UUID.randomUUID().toString();
				code=String.valueOf(count+1);
				
				String sql="insert into tb_card_teachstudinfo(uid,code,name,idcard,infotype,orgcode,createdate,status) values(?,?,?,?,2,'001',now(),1)";
				dbService.excuteSql(sql, docid,code,name,idcard);
				
				userlist=dbService.QueryList("select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,1 as usertype,status from tb_card_teachstudinfo where name='"+name+"' and idcard='"+idcard+"' and infotype=2 ");
			}
		}else if(usertype==2) {
			idcode=linktel;
			userlist=dbService.QueryList("select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,2 as usertype,status from tb_card_teachstudinfo where name='"+name+"' and idcard='"+idcard+"' and infotype=2 and ((linkman1='"+linkname+"' and linkmobile1='"+linktel+"') or (linkman2='"+linkname+"' and linkmobile2='"+linktel+"')) ");
		
			if(userlist.size()==0) {
				int count=dbService.getCount("tb_card_teachstudinfo", "");
				String docid=UUID.randomUUID().toString();
				code=String.valueOf(count+1);
				String sql="insert into tb_card_teachstudinfo(uid,code,name,idcard,linkman1,linkmobile1,infotype,orgcode,createdate,status) values(?,?,?,?,?,?,2,'001',now(),1)";
				dbService.excuteSql(sql,docid,code,name,idcard,linkname,linktel);

				userlist=dbService.QueryList("select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,2 as usertype,status from tb_card_teachstudinfo where name='"+name+"' and idcard='"+idcard+"' and infotype=2 and ((linkman1='"+linkname+"' and linkmobile1='"+linktel+"') or (linkman2='"+linkname+"' and linkmobile2='"+linktel+"')) ");
				
			}
		}else if(usertype==3) {
			idcode=code;
			userlist=dbService.QueryList("select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,3 as usertype,status from tb_card_teachstudinfo where name='"+name+"' and code='"+code+"' and infotype=1 ");
			if(userlist.size()==0) {
				String docid=UUID.randomUUID().toString();
				String sql="insert into tb_card_teachstudinfo(uid,code,name,infotype,orgcode,createdate,status) values(?,?,?,1,'001',now(),1)";
				dbService.excuteSql(sql,docid,code,name);

				userlist=dbService.QueryList("select uid as docid,code,name,orgcode,getorgname(orgcode) as orgname,3 as usertype,status from tb_card_teachstudinfo where name='"+name+"' and code='"+code+"' and infotype=1 ");
			}
		}
		
		JSONObject item=userlist.getJSONObject(0);
		String docid=item.getString("docid");
		int count= dbService.getCount("tb_weixin_user", "docid='"+docid+"' and usertype="+String.valueOf(usertype)+" ");
		if(usertype==2 && count>=2) {
			return Json.getJsonResult("关注人数已达上限");
		}else {
			if(count>=1) {
				return Json.getJsonResult("其他微信已绑定");
			}
		}
		count= dbService.getCount("tb_weixin_user", "docid='"+docid+"' and openid='"+openid+"'");
		if(count>=1) {
			return Json.getJsonResult("请勿重复绑定");
		}
		String userid=UUID.randomUUID().toString();
		String sql="insert into tb_weixin_user(uid,openid,usertype,docid,idcode,headimgurl,nickname,sex,lastlogintime,logintimes,status,createdate) values(?,?,?,?,?,?,?,?,now(),1,1,now())";
		dbService.excuteSql(sql, userid,openid,usertype,docid,idcode,headimgurl,nickname,sex);
		
		SysUser user=new SysUser();
		user.setCode(item.getString("code"));
		user.setDocid(docid);
		user.setOpenid(openid);
		user.setIdcode(idcode);
		user.setOrgcode(item.getString("orgcode"));
		user.setOrgname(item.getString("orgname"));
		user.setName(item.getString("name"));
		user.setNickname(nickname);
		user.setSex(sex);
		user.setStatus(item.getIntValue("status"));
		user.setTokenid(UUID.randomUUID().toString());
		user.setUid(userid);
		user.setUsertype(usertype);
		user.setHeadimgurl(headimgurl);
		userredis.add(request,response,user);
		
		return Json.getJsonResult(true);
		
	}
	
	public JsonResult DisBind(HttpServletRequest request, HttpServletResponse response, String openid) {
		String sql="delete from tb_weixin_user where openid=?";
		dbService.excuteSql(sql, openid);
		userredis.remove(request);
		return Json.getJsonResult(true);
	}
	
}
