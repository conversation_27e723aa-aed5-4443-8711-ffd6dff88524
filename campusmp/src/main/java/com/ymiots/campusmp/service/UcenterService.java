package com.ymiots.campusmp.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.entity.*;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Repository
public class UcenterService extends BaseService {

    @Autowired
    UserRedis userredis;

    @Autowired
    OAuthService oauthservice;

    @Autowired
    SysConfigService sysconfig;

    @Autowired
    CardManageService cardManageService;

    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    public List<BindUserInfo> getOtherUser(HttpServletRequest request, HttpServletResponse response, String openid, int usertype) {
        if (usertype == 4) {
            //访客
            String sql = "SELECT wu.uid,info.name,wu.headimgurl FROM tb_weixin_user wu inner join tb_visitor_visitorinfo info on info.uid=wu.docid where wu.openid='" + openid + "';";
            List<BindUserInfo> list = dbService.QueryObjectList(sql, BindUserInfo.class);
            return list;
        } else if (usertype == 99) {
            //职工
            String sql = "SELECT wu.uid,info.name,'' as headimgurl FROM tb_weixin_workuser wu inner join tb_card_teachstudinfo info on info.uid=wu.docid where wu.uid='" + openid + "';";
            List<BindUserInfo> list = dbService.QueryObjectList(sql, BindUserInfo.class);
            return list;
        } else if (usertype == 5) {
            //关注者
            String sql = "SELECT uid,nickname,headimgurl,openid FROM tb_weixin_user where openid='" + openid + "'";
            List<BindUserInfo> list = dbService.QueryObjectList(sql, BindUserInfo.class);
            return list;
        } else {
            String sql = "SELECT wu.uid,info.name,wu.headimgurl FROM tb_weixin_user wu inner join tb_card_teachstudinfo info on info.uid=wu.docid where wu.openid='" + openid + "';";
            List<BindUserInfo> list = dbService.QueryObjectList(sql, BindUserInfo.class);
            return list;
        }
    }


    public JSONArray getInfoLinkMan(HttpServletRequest request, HttpServletResponse response) {
        final SysUser userinfo = userredis.get(request);
        String sql = "SELECT linkman1,linkmobile1,linkdes1,linkman2,linkmobile2,linkdes2 FROM tb_card_teachstudinfo where uid='" + userinfo.getDocid() + "';";
        JSONArray list = dbService.QueryList(sql);
        return list;
    }

    public JSONArray getUserPhotoNumber(HttpServletRequest request, HttpServletResponse response) {
        SysUser userinfo = userredis.get(request);
        String sql = "";
        if (userinfo.getUsertype() == 4) {
            sql = "SELECT mobile FROM tb_visitor_visitorinfo where uid='" + userinfo.getDocid() + "'";
        } else {
            sql = "SELECT mobile FROM tb_card_teachstudinfo where uid='" + userinfo.getDocid() + "'";
        }
        JSONArray list = dbService.QueryList(sql);
        return list;
    }

    public JsonResult updatePhotoNumber(HttpServletRequest request, HttpServletResponse response, String number) {
        SysUser userinfo = userredis.get(request);
        String updateSQL = "";
        if (userinfo.getUsertype() != 4) {
            updateSQL = "UPDATE tb_card_teachstudinfo SET mobile=? WHERE uid=?";
        } else {
            updateSQL = "UPDATE tb_visitor_visitorinfo SET mobile=? WHERE uid=?";
        }
        int i = dbService.excuteSql(updateSQL, number, userinfo.getDocid());
        if (i == -1) {
            return Json.getJsonResult(false);
        }
        return Json.getJsonResult(true);
    }

    public JSONArray getUserEmail(HttpServletRequest request, HttpServletResponse response) {
        SysUser userinfo = userredis.get(request);
        String sql = "";
        if (userinfo.getUsertype() == 4) {
            sql = "SELECT email FROM tb_visitor_visitorinfo where uid='" + userinfo.getDocid() + "'";
        } else {
            sql = "SELECT email FROM tb_card_teachstudinfo where uid='" + userinfo.getDocid() + "'";
        }
        JSONArray list = dbService.QueryList(sql);
        return list;
    }

    public JsonResult updateEmail(HttpServletRequest request, HttpServletResponse response, String email) {
        SysUser userinfo = userredis.get(request);
        String updateSQL = "";
        if (userinfo.getUsertype() != 4) {
            updateSQL = "UPDATE tb_card_teachstudinfo SET email=? WHERE uid=?";
        } else {
            updateSQL = "UPDATE tb_visitor_visitorinfo SET email=? WHERE uid=?";
        }
        int i = dbService.excuteSql(updateSQL, email, userinfo.getDocid());
        if (i == -1) {
            return Json.getJsonResult(false);
        }
        return Json.getJsonResult(true);
    }


    public JsonResult updateSex(HttpServletRequest request, HttpServletResponse response, String sex) {
        SysUser sysUser = userredis.get(request);
        String updateSQL = "UPDATE tb_weixin_user SET sex=? WHERE docid=?";
        int i = dbService.excuteSql(updateSQL, sex, sysUser.getDocid());
        if (i == -1) {
            return Json.getJsonResult(false);
        }
        String updateInfo = "UPDATE tb_card_teachstudinfo SET sex=? WHERE uid=?";
        int j = dbService.excuteSql(updateInfo, sex, sysUser.getDocid());
        if (j == -1) {
            return Json.getJsonResult(false);
        }
        sysUser.setSex(sex);
        userredis.add(request, response, sysUser);
        return Json.getJsonResult(true);
    }

    public JsonResult SaveLinkMan(HttpServletRequest request, HttpServletResponse response, String linkman1, String linkmobile1, String linkdes1,
                                  String linkman2, String linkmobile2, String linkdes2) {
        final SysUser userinfo = userredis.get(request);
        String sql = "SELECT linkman1,linkmobile1,linkdes1,linkman2,linkmobile2,linkdes2 FROM tb_card_teachstudinfo where uid='" + userinfo.getDocid() + "';";
        JSONArray list = dbService.QueryList(sql);
        JSONObject item = list.getJSONObject(0);
        if (StringUtils.isBlank(linkmobile1) && !StringUtils.isBlank(item.getString("linkmobile1"))) {
            int count = dbService.getCount("tb_weixin_user", "idcode='" + item.getString("linkmobile1") + "' and docid='" + userinfo.getDocid() + "'");
            if (count > 0) {
                return Json.getJsonResult("已绑定，禁止设置为空");
            }
        }
        if (StringUtils.isBlank(linkmobile2) && !StringUtils.isBlank(item.getString("linkmobile2"))) {
            int count = dbService.getCount("tb_weixin_user", "idcode='" + item.getString("linkmobile2") + "' and docid='" + userinfo.getDocid() + "'");
            if (count > 0) {
                return Json.getJsonResult("已绑定，禁止设置为空");
            }
        }
        SysUser sysUser = userredis.get(request);
        String name = sysUser.getName();
        String sqlQuery = "select uid,name,mobile from tb_card_teachstudinfo where linkid = ?";
        JSONArray jsonArray = dbService.QueryList(sqlQuery, sysUser.getDocid());
        int size = jsonArray.size();
        if (!StringUtils.isBlank(item.getString("linkmobile1")) && !StringUtils.isBlank(linkmobile1)) {
            if (!item.getString("linkmobile1").equals(linkmobile1)) {
                //联系手机号有改动，更新绑定微信表里的标识
                sql = "update tb_weixin_user set idcode=? where idcode=? and docid=?";
                dbService.excuteSql(sql, linkmobile1, item.getString("linkmobile1"), userinfo.getDocid());
                //更新人员信息表中家长信息
                sql = "update tb_card_teachstudinfo set name =? ,mobile = ? where linkid = ? ";
                dbService.excuteSql(sql, name + linkdes1, linkmobile1, sysUser.getDocid());
            }
        }
        if (!StringUtils.isBlank(item.getString("linkmobile2")) && !StringUtils.isBlank(linkmobile2)) {
            if (!item.getString("linkmobile2").equals(linkmobile2)) {
                //联系手机号有改动，更新绑定微信表里的标识
                sql = "update tb_weixin_user set idcode=? where idcode=? and docid=?";
                dbService.excuteSql(sql, linkmobile2, item.getString("linkmobile2"), userinfo.getDocid());
                //更新人员信息表中家长信息
                sql = "update tb_card_teachstudinfo set name =? ,mobile = ? where linkid = ? ";
                dbService.excuteSql(sql, name + linkdes1, linkmobile1, sysUser.getDocid());
            }
        }

        sql = "update tb_card_teachstudinfo set linkman1=?,linkmobile1=?,linkdes1=?,linkman2=?,linkmobile2=?,linkdes2=? where uid=?";
        dbService.excuteSql(sql, linkman1, linkmobile1, linkdes1, linkman2, linkmobile2, linkdes2, userinfo.getDocid());
        return Json.getJsonResult(true);
    }

    public JsonData getOtherInfoLink(HttpServletRequest request, HttpServletResponse response) {
        String infoid = userredis.get(request).getDocid();
        String sql = "SELECT uid,linkman,linkmobile,linkdes FROM tb_card_teachstudinfo_otherlink WHERE infoid='" + infoid + "'";
        return dbService.QueryJsonData(sql);
    }

    public JsonResult saveOtherInfoLink(HttpServletRequest request, HttpServletResponse response, String linklist) {
        JSONArray linkja = JSONArray.parseArray(linklist);
        String infoid = userredis.get(request).getDocid();
        for (int i = 0; i < linkja.size(); i++) {
            String linkid = linkja.getJSONObject(i).getString("linkid");
            String linkman = linkja.getJSONObject(i).getString("linkman");
            String linkmobile = linkja.getJSONObject(i).getString("linkmobile");
            String linkdes = linkja.getJSONObject(i).getString("linkdes");
            if (StringUtils.isBlank(linkid)) {
                String insertSQL = "INSERT INTO tb_card_teachstudinfo_otherlink (uid,infoid,linkman,linkmobile,linkdes,createdate,creatorid,modifydate,modifierid,status) VALUES (uuid(),?,?,?,?,now(),?,now(),?,1)";
                dbService.excuteSql(insertSQL, infoid, linkman, linkmobile, linkdes, infoid, infoid);
            } else {
                String updateSQL = "UPDATE tb_card_teachstudinfo_otherlink SET linkman=?,linkmobile=?,linkdes=?,modifydate=now() WHERE uid=?";
                dbService.excuteSql(updateSQL, linkman, linkmobile, linkdes, linkid);
            }
        }
        return Json.getJsonResult(true);
    }

    public JsonResult delOtherInfoLink(HttpServletRequest request, HttpServletResponse response, String uid) {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "联系人记录不存在");
        }
        String delSQL = "DELETE FROM tb_card_teachstudinfo_otherlink WHERE uid=?";
        dbService.excuteSql(delSQL, uid);
        return Json.getJsonResult(true);
    }

    public JsonResult setPasswordCenter(HttpServletRequest request, HttpServletResponse response, String password, String oldpassword) {
        SysUser sysUser = userredis.get(request);
        String pwdSql = "select mp_password as password from tb_card_teachstudinfo where uid = '" + sysUser.getDocid() + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(pwdSql);
        if (jsonObject != null) {
            String passwordMp = jsonObject.getString("password");
            if (StringUtils.isNotBlank(passwordMp)) {
                if (!passwordMp.equals(oldpassword)) {
                    return Json.getJsonResult("密码错误");
                }
            } else {
                String passWord = sysconfig.get("defaultPassWord");
                if (!passWord.equals(oldpassword)) {
                    return Json.getJsonResult("密码错误");
                }
            }
        } else {
            String passWord = sysconfig.get("defaultPassWord");
            if (!passWord.equals(oldpassword)) {
                return Json.getJsonResult("密码错误");
            }
        }
        String update = "UPDATE tb_card_teachstudinfo SET mp_password = ? WHERE uid = ? ";
        dbService.excuteSql(update, password, sysUser.getDocid());
        return Json.getJsonResult(true);
    }


    public JsonResult setPassword(HttpServletRequest request, HttpServletResponse response, String password, String oldpassword) {
        SysUser sysUser = userredis.get(request);
        String select = "SELECT mp_password FROM tb_card_teachstudinfo WHERE uid = ? ";
        JSONArray list = dbService.QueryList(select, sysUser.getDocid());
        if (list.size() > 0) {
            String mp_password = list.getJSONObject(0).getString("mp_password");
            if (StringUtils.isNotEmpty(mp_password)){
                JSONArray ja = dbService.QueryList(select+" and mp_password = ?", sysUser.getDocid(), oldpassword);
                if (ja.size() == 0) {
                    return Json.getJsonResult(false, "旧密码错误");
                }
            }else {
                if (!oldpassword.equals(sysconfig.get("defaultPassWord"))){
                    return Json.getJsonResult(false, "旧密码错误");
                }
            }
        }else {
            String passWord = sysconfig.get("defaultPassWord");
            if (!passWord.equals(oldpassword)) {
                return Json.getJsonResult("旧密码错误");
            }
        }

        String update = "UPDATE tb_card_teachstudinfo SET mp_password = ? WHERE uid = ? ";
        dbService.excuteSql(update, password, sysUser.getDocid());
        return Json.getJsonResult(true);
    }

    public boolean isSubscribe(HttpServletRequest request) {
        String nowdate = DateHelper.format(new Date());
        String selSubConfig = "SELECT enableconfirmdoorclosed FROM tb_subscribe_config ";
        JSONArray configja = dbService.QueryList(selSubConfig);
        if (!configja.isEmpty()) {
            int enableconfirmdoorclosed = configja.getJSONObject(0).getIntValue("enableconfirmdoorclosed");
            if (enableconfirmdoorclosed == 1) {
                int count = dbService.getCount("tb_subscribe_record", "infoid='" + userredis.get(request).getDocid() + "' AND auditstatus=1 AND status=2 AND '" + nowdate + "' BETWEEN starttime AND endtime");
                if (count > 0) {
                    return true;
                }
            }
        }
        return false;
    }


    private JsonResult GetQrcodeByCard(String infoid) {
        String qrcode = "";
        String sql = "SELECT ifnull(card,'') as card FROM tb_card_teachstudinfo where uid='" + infoid + "'";
        JSONArray cardlist = dbService.QueryList(sql);
        if (cardlist.size() == 0) {
            return Json.getJsonResult(false, "档案信息不存在");
        }
        JSONObject card = cardlist.getJSONObject(0);
        qrcode = card.getString("card");
        if (StringUtils.isBlank(qrcode)) {
            return Json.getJsonResult(false, "卡号为空");
        }
        return Json.getJsonResult(true, qrcode);
    }

    private JsonResult GetQrcode(String infoid) {
        JSONObject qrcodecfg = sysconfig.get("createqrcodeway", "createqrcodefromat");
        String qrcode = "";
        String createqrcodeway = qrcodecfg.getString("createqrcodeway");
        String createqrcodefromat = qrcodecfg.getString("createqrcodefromat");
        if (createqrcodeway.equals("1")) {
            //先按卡号生成，如果卡号生成失败再随机生成
            JsonResult result = GetQrcodeByCard(infoid);
            if (result.isSuccess()) {
                qrcode = result.getMsg();
                if (createqrcodefromat.equals("16")) {
                    long qrcodenumber = Long.parseLong(qrcode);
                    if (qrcodenumber < 4294967295L) {
                        qrcode = Long.toHexString(qrcodenumber).toUpperCase();
                        if (qrcode.length() % 2 == 1) {
                            qrcode = ComHelper.LeftPad(qrcode, qrcode.length() + 1, '0');
                        }
                    } else {
                        return Json.getJsonResult(false, "卡号十六进制字符大于FFFFFFFF,无法生存二维码");
                    }
                } else {
                    long qrcodenumber = Long.parseLong(qrcode);
                    qrcode = String.valueOf(qrcodenumber);
                }
            } else {
                if (createqrcodefromat.equals("16")) {
                    while (true) {
                        qrcode = RandomHelper.randomHexString(8).toUpperCase();
                        if (qrcode.length() % 2 == 1) {
                            qrcode = ComHelper.LeftPad(qrcode, qrcode.length() + 1, '0');
                        }
                        int count = dbService.getCount("tb_card_qrcode", "qrcode='" + qrcode + "' ");
                        if (count == 0) {
                            break;
                        }
                    }
                } else {
                    qrcode = RandomHelper.GetDifferentNum(9);
                    long qrcodenumber = Long.parseLong(qrcode);
                    qrcode = String.valueOf(qrcodenumber);
                }
            }
        } else if (createqrcodeway.equals("2")) {
            if (createqrcodefromat.equals("16")) {
                //按随机字符
                while (true) {
                    qrcode = RandomHelper.randomHexString(8).toUpperCase();
                    if (qrcode.length() % 2 == 1) {
                        qrcode = ComHelper.LeftPad(qrcode, qrcode.length() + 1, '0');
                    }
                    int count = dbService.getCount("tb_card_qrcode", "qrcode='" + qrcode + "' ");
                    if (count == 0) {
                        break;
                    }
                }
            } else {
                qrcode = RandomHelper.GetDifferentNum(9);
                long qrcodenumber = Long.parseLong(qrcode);
                qrcode = String.valueOf(qrcodenumber);
            }
        } else {
            //按卡号生成
            JsonResult result = GetQrcodeByCard(infoid);
            if (result.isSuccess()) {
                qrcode = result.getMsg();
                if (createqrcodefromat.equals("16")) {
                    long qrcodenumber = Long.parseLong(qrcode);
                    if (qrcodenumber < 4294967295L) {
                        qrcode = Long.toHexString(qrcodenumber).toUpperCase();
                        if (qrcode.length() % 2 == 1) {
                            qrcode = ComHelper.LeftPad(qrcode, qrcode.length() + 1, '0');
                        }
                    } else {
                        return Json.getJsonResult(false, "卡号十六进制字符大于FFFFFFFF,无法生存二维码");
                    }
                } else {
                    long qrcodenumber = Long.parseLong(qrcode);
                    qrcode = String.valueOf(qrcodenumber);
                }
            } else {
                return result;
            }
        }
        return Json.getJsonResult(true, qrcode);
    }


    public JsonResult CreateDoorQRCode(HttpServletRequest request) throws Exception {
        SysUser user = userredis.get(request);
        String infoid = user.getDocid();
        String endtime = DateHelper.format(DateHelper.addMinutes(new Date(), 1));
        JSONObject data = new JSONObject();

        String sql = "select uid,qrcode,date_format(endtime, '%Y-%m-%d %H:%i:%s') as endtime from tb_card_qrcode where infoid='" + infoid + "' and status=1 ";
        JSONArray list = dbService.QueryList(sql);

        if (list.size() > 0) {
            data = list.getJSONObject(0);
            String qrcode = data.getString("qrcode");
            sql = "update tb_card_qrcode set endtime =?,usecount=usecount+1,usedatetime=now() where infoid=?";
            dbService.excuteSql(sql, endtime, infoid);
            byte[] qrcodebyte = QRcodeUtils.createQrCodeByte(qrcode, 800, "png");
            data.put("qrcode", FileHelper.ImageByte2Base64(qrcodebyte));
        } else {
            JsonResult qrcoders = GetQrcode(infoid);
            if (!qrcoders.isSuccess()) {
                return Json.getJsonResult(true, qrcoders.getMsg());
            }
            String qrcode = qrcoders.getMsg();
            String uid = UUID.randomUUID().toString();
            sql = "insert into tb_card_qrcode(uid, qrcode, endtime, infoid, usecount,usedatetime, createdate, status) values(?,?,?,?,1,now(),now(),1)";
            dbService.excuteSql(sql, uid, qrcode, endtime, infoid);
            byte[] qrcodebyte = QRcodeUtils.createQrCodeByte(qrcode, 800, "png");
            data.put("qrcode", FileHelper.ImageByte2Base64(qrcodebyte));
        }
        data.put("endtime", endtime);
        return Json.getJsonResult(true, data);
    }

    public JsonResult logincheck(HttpServletRequest request, HttpServletResponse response, String usercode, String pwd) {
        String loginmodel = request.getHeader("loginmodel");
        if (StringUtils.isBlank(loginmodel)) {
            loginmodel = "1";
        }

        SysUser user = null;
        if (loginmodel.equals("1")) {
            user = oauthservice.getSysUserByMobilePwd(request, response, usercode, pwd);
        } else if (loginmodel.equals("2")) {
            user = oauthservice.getSysUserBySysUser(request, response, usercode, pwd);
        }
        if (user != null) {
            return Json.getJsonResult(true, "登录成功");
        } else {
            return Json.getJsonResult(false, "登录失败");
        }
    }

    public JsonResult logout(HttpServletRequest request, HttpServletResponse response) {
        String tokenid = userredis.getTokenId(request);
        userredis.remove(request);
        if (!StringUtils.isBlank(tokenid)) {
            Cookie cookie = new Cookie(userredis.tokenkey, tokenid);
            cookie.setMaxAge(0);
            cookie.setPath("/");
            response.addCookie(cookie);
        }
        return Json.getJsonResult(true);
    }

    public List<WeiXinMenu> gettotalmenu(HttpServletRequest request, HttpServletResponse response) {
        List<WeiXinMenu> menuList = (List<WeiXinMenu>) redisTemplate.opsForValue().get("weixintotalmenu");
        if (menuList==null){
            menuList = new ArrayList<>();
        }
        if (menuList.size()>0){
            return menuList;
        }
        JSONArray list = dbService.QueryList("select uid ,name from tb_winxin_menu where name != '常用功能' order by createdate");
        if (list.size()>0){
            for (int i = 0; i < list.size(); i++) {
                List<WeiXinMenuItem> weiXinMenuItems = dbService.queryList("select name,icon,type,url from tb_winxin_menu_item where status = 1 and menuid = '"+list.getJSONObject(i).getString("uid")+"' order by sort,createdate",WeiXinMenuItem.class);
                WeiXinMenu weiXinMenu = new WeiXinMenu();
                weiXinMenu.setFunctions(weiXinMenuItems);
                weiXinMenu.setName(list.getJSONObject(i).getString("name"));
                menuList.add(weiXinMenu);
            }
            redisTemplate.opsForValue().set("weixintotalmenu",menuList,7, TimeUnit.DAYS);
        }
        return menuList;
    }

    public List<WeiXinMenuItem> getcommonmenu(HttpServletRequest request, HttpServletResponse response) {

        JSONObject object = dbService.QueryJSONObject("select uid ,name from tb_winxin_menu where name = '常用功能'");
        List<WeiXinMenuItem> weiXinMenuItems = new ArrayList<>();
        weiXinMenuItems = (List<WeiXinMenuItem>) redisTemplate.opsForValue().get("weixincommonmenu");
        if (weiXinMenuItems!=null&&weiXinMenuItems.size()>0){
            return weiXinMenuItems;
        }

        weiXinMenuItems = dbService.queryList("select name,icon,type,url from tb_winxin_menu_item where status = 1 and menuid = '"+object.getString("uid")+"' order by sort,createdate",WeiXinMenuItem.class);
        redisTemplate.opsForValue().set("weixincommonmenu",weiXinMenuItems,7, TimeUnit.DAYS);
        return weiXinMenuItems;
    }

    public UserInfo getinfo(HttpServletRequest request, HttpServletResponse response) {
        //String docid = userredis.get(request).getDocid();
        String flag = sysconfig.get("notificationsflag");
        if (StringUtils.isEmpty(flag)){
            return null;
        }else {
            if ("1".equals(flag)){
                List<Notifications> notifications = dbService.queryList("select uid,title,createdate date,article content from tb_weixin_article order by createdate desc limit 3", Notifications.class);
                //UserInfo userInfo = dbService.queryForOne("select ct.name studentName,ct.card,ct.code studentNumber,getorgname(ct.orgcode) studentClass,face.imgpath studentPhoto from tb_card_teachstudinfo ct left join tb_face_infoface face on ct.uid = face.infoid where ct.uid = '" + docid + "'", UserInfo.class);
                UserInfo userInfo = new UserInfo();
                userInfo.setNotifications(notifications);
                //JSONObject object = dbService.QueryJSONObject("select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1");
                //if (object!=null){
                //    userInfo.setSchoolName(object.getString("schoolName"));
                //    userInfo.setSchoolLogo(object.getString("schoolLogo"));
                //}
                //String cardId = cardManageService.GetCardId(request, "");
                //if (StringUtils.isNotEmpty(cardId)){
                //    userInfo.setAccount(cardManageService.GetCardInfoByNo(userInfo.getCard(),cardId).getBalance().toString());
                //}else {
                //    userInfo.setAccount("0");
                //}
                return userInfo;
            }
        }
        return null;
    }

    /**
     * 完善信息
     */
    public JSONObject getInfoDetail(HttpServletRequest request, HttpServletResponse response) {
        String docId = userredis.get(request).getDocid();
        String sql = "select td.link1_employer," +
                " td.link2_employer,td.is_local,td.local_address as localAddress," +
                "td.registration_num as registrationNum,td.registration_code as registrationCode,td.address,td.registered,td.grade,td.schoolnumber,td.oldschool,td.isTransfer,td.transferreason,td.transfertype," +
                " ct.name,ct.sex,getminiorgname(ct.orgcode) orgName," +
                " linkman1,linkmobile1,linkdes1,linkman2,linkmobile2,linkdes2" +
                " from tb_card_teachstudinfo_detail td " +
                " left join tb_card_teachstudinfo ct on td.uid = ct.uid " +
                " where td.uid = '" + docId + "'";
        JSONObject jsonObject = dbService.QueryJSONObject(sql);
        if (jsonObject == null) {
            String insertSQL = "insert into tb_card_teachstudinfo_detail (uid,create_time,modify_time) values(?,now(),now())";
            dbService.excuteSql(insertSQL, docId);
        }
        return dbService.QueryJSONObject(sql);
    }

    public JsonResult saveInfoDetail(HttpServletRequest request, HttpServletResponse response, String studentName,String sex,String registrationNum,String registrationCode,String address,String registered,String linkman1,String linkmobile1,String linkdes1,String link1Employer,String linkman2,String linkmobile2,String linkdes2,String link2Employer,String isLocal,String localAddress,String grade,String schoolnumber,String oldschool,String isTransfer,String transferreason,String transfertype) {
        String docId = userredis.get(request).getDocid();
        String sql = "update tb_card_teachstudinfo_detail set address=?,link1_employer=?,link2_employer=?,is_local = ?,modify_time = now(),local_address = ?,registration_num = ?,registration_code = ?,registered = ?,grade = ?,schoolnumber = ?,oldschool=?,isTransfer=?,transferreason=?,transfertype=?  where uid=?";
        dbService.excuteSql(sql, address, link1Employer, link2Employer, isLocal, localAddress, registrationNum, registrationCode, registered,grade,schoolnumber,oldschool,isTransfer,transferreason,transfertype, docId);
        sql = "update tb_card_teachstudinfo set name=?,sex = ?,linkman1=?,linkmobile1=?,linkdes1=?,linkman2=?,linkmobile2=?,linkdes2=? where uid=?";
        dbService.excuteSql(sql, studentName, sex,linkman1, linkmobile1, linkdes1, linkman2, linkmobile2, linkdes2, docId);
        return Json.getJsonResult(true);
    }
}
