package com.ymiots.campusmp.service.weixinwork;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.RestRequest;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.entity.card.OrgFramework;
import com.ymiots.campusmp.service.AccessTokenService;
import com.ymiots.campusmp.utils.weixin.WeiXinWork;
import com.ymiots.framework.common.Log;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Repository
public class WorkUserService extends BaseService {

    @Autowired
    AccessTokenService token;

    @Autowired
    WeiXinWork weixinwork;

    @Autowired
    RestRequest request;

    public void SyncWeiXinWorkOrgUserData(String orgid) {
        try {
            Log.info(this.getClass(), "开始部门[" + orgid + "]员工数据同步");
            String ACCESS_TOKEN = token.GetWorkAccessToken();
            if (StringUtils.isBlank(ACCESS_TOKEN)) {
                Log.error(this.getClass(), "企业ACCESSTOEKN为空，部门员工未同步");
                return;
            }
            JSONArray list = weixinwork.GetWeiXinWorkDepartmentUserData(ACCESS_TOKEN, orgid);
            if (list.size() == 0) {
                Log.error(this.getClass(), "没有获取到部门员工数据");
                return;
            }
            Log.info(this.getClass(), "获取部门[" + orgid + "]员工数据" + String.valueOf(list.size()) + "条");
            for (int i = 0; i < list.size(); i++) {
                JSONObject item = list.getJSONObject(i);
                Log.info(this.getClass(), item.toJSONString());
                String userid = item.getString("userid");
                String name = item.getString("name");
                String mobile = item.getString("mobile");
                String email = item.getString("email");
                String thumb_avatar = item.getString("thumb_avatar");

                String sql = "select uid,docid from tb_weixin_workuser where uid='" + userid + "' ";
                JSONArray worklist = dbService.QueryList(sql);
                if (worklist.size() > 0) {
                    String docid = worklist.getJSONObject(0).getString("docid");
                    if (!StringUtils.isBlank(mobile)) {
                        sql = "update tb_card_teachstudinfo set mobile=? where uid=?";
                        dbService.excuteSql(sql, mobile, docid);
                    }

                    sql = "update tb_weixin_workuser set name=?,mobile=?,email=?,headimgurl=?,modifydate=now() where uid=?";
                    dbService.excuteSql(sql, name, mobile, email, thumb_avatar, userid);
                } else {
                    if (!StringUtils.isBlank(mobile)) {
                        sql = "select uid from tb_card_teachstudinfo where mobile='" + mobile + "' ";
                        JSONArray infolist = dbService.QueryList(sql);
                        if (infolist.size() > 0) {
                            String docid = infolist.getJSONObject(0).getString("uid");
                            sql = "insert into tb_weixin_workuser(uid,docid,name,mobile,email,headimgurl,createdate) values(?,?,?,?,?,?,now())";
                            dbService.excuteSql(sql, userid, docid, name, mobile, email, thumb_avatar);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            Log.error(this.getClass(), "同步企业微信用户数据异常：" + ex.getMessage());
        }
    }

    public void SyncNewWeiXinWorkOrgUserData(OrgFramework orgFramework) {
        try {
            Log.info(this.getClass(), "开始部门[" + orgFramework.getOrgCode() + "]员工数据同步");
            String ACCESS_TOKEN = token.GetWorkAccessToken();
            if (StringUtils.isBlank(ACCESS_TOKEN)) {
                Log.error(this.getClass(), "企业ACCESSTOEKN为空，部门员工未同步");
                return;
            }
            //获取该部门已经存在的人员
            String existInfoSql = "select uid from tb_card_teachstudinfo where orgcode='" + orgFramework.getOrgCode() + "'";
            List<String> existInfoList = dbService.queryFields(existInfoSql, String.class);

            JSONArray list = new JSONArray();
            if ("1".equals(WebConfig.getIsWorkYunToken())) {
                String LOCAL_ACCESS_TOKEN = "http://47.112.126.84:2031/weixinouth/getWorkDepartmentUser";
                Map<String, String> params = new HashMap<String, String>();
                params.put("campusid", WebConfig.getCampusid());
                params.put("ACCESS_TOKEN", ACCESS_TOKEN);
                params.put("orgCode", orgFramework.getOrgCode());
                list = request.doPostFormRequest(LOCAL_ACCESS_TOKEN, params, JSONArray.class);
            } else {
                list = weixinwork.GetWeiXinWorkDepartmentUserData(ACCESS_TOKEN, orgFramework.getOrgCode());
            }
            if (list.size() == 0) {
                Log.error(this.getClass(), "没有获取到部门员工数据");
                return;
            }
            Log.info(this.getClass(), "获取部门[" + orgFramework.getOrgCode() + "]员工数据" + String.valueOf(list.size()) + "条");
            List<String> infoList = new ArrayList<String>();
            for (int i = 0; i < list.size(); i++) {
                JSONObject item = list.getJSONObject(i);
                Log.info(this.getClass(), item.toJSONString());
                String userid = item.getString("userid");
                String name = item.getString("name");
                String mobile = item.getString("mobile");
                String email = item.getString("email");
                String thumb_avatar = item.getString("thumb_avatar");

                String sql = "select uid,docid from tb_weixin_workuser where uid='" + userid + "' ";
                JSONArray worklist = dbService.QueryList(sql);
                String querySql = "select uid from tb_card_teachstudinfo where code='" + userid + "' ";
                String infoId = dbService.queryOneField(querySql, String.class);
                if (StringUtils.isBlank(infoId)) {
                    infoId = UUID.randomUUID().toString();
                    sql = "insert into tb_card_teachstudinfo(uid,mobile,code,orgcode,name,email,createdate,creatorid,modifydate,modifierid,status,infotype) values (?,?,?,?,?,?,now(),'企业微信自动同步',now(),'企业微信自动同步',1,1)";
                    dbService.excuteSql(sql, infoId, mobile, userid, orgFramework.getCode(), name, email);
                } else {
                    sql = "update tb_card_teachstudinfo set mobile=?, orgcode = ?,modifydate = now() where uid=?";
                    dbService.excuteSql(sql, mobile, orgFramework.getCode(), infoId);
                }
                infoList.add(infoId);
                if (worklist.size() > 0) {
                    sql = "update tb_weixin_workuser set name=?,mobile=?,email=?,headimgurl=?,modifydate=now() where uid=?";
                    dbService.excuteSql(sql, name, mobile, email, thumb_avatar, userid);
                } else {
                    sql = "insert into tb_weixin_workuser(uid,docid,name,mobile,email,headimgurl,createdate) values(?,?,?,?,?,?,now())";
                    dbService.excuteSql(sql, userid, infoId, name, mobile, email, thumb_avatar);
                }
            }
            //待离职人员信息
            List<String> exitInfoSql = existInfoList.stream().filter(infoId -> !infoList.contains(infoId)).collect(Collectors.toList());
            for (String exitInfoId : exitInfoSql) {
                String updateStatusSql = "update tb_card_teachstudinfo set status = 0, modifydate = now() where uid = ?";
                dbService.excuteSql(updateStatusSql, exitInfoId);
                Log.info(this.getClass(), "员工[" + exitInfoId + "]已被标记为离职");
            }
        } catch (Exception ex) {
            Log.error(this.getClass(), "同步企业微信用户数据异常：" + ex.getMessage());
        }
    }


    public void SyncWeiXinWorkUserData(String userid) {
        try {
            Log.info(this.getClass(), "开始员工[" + userid + "]数据同步");
            String ACCESS_TOKEN = token.GetWorkAccessToken();
            if (StringUtils.isBlank(ACCESS_TOKEN)) {
                Log.error(this.getClass(), "企业ACCESSTOEKN为空，部门员工未同步");
                return;
            }
            JSONObject item = new JSONObject();
            if ("1".equals(WebConfig.getIsWorkYunToken())) {
                String LOCAL_ACCESS_TOKEN = "http://47.112.126.84:2031/weixinouth/getWorkDepartmentUserDetail";
                Map<String, String> params = new HashMap<String, String>();
                params.put("campusid", WebConfig.getCampusid());
                params.put("ACCESS_TOKEN", ACCESS_TOKEN);
                params.put("userId", userid);
                item = request.doPostFormRequest(LOCAL_ACCESS_TOKEN, params, JSONObject.class);
            } else {
                item = weixinwork.GetWeiXinWorkUserData(ACCESS_TOKEN, userid);
            }
//            JSONObject item = weixinwork.GetWeiXinWorkUserData(ACCESS_TOKEN, userid);
            if (item == null) {
                Log.error(this.getClass(), "没有获取到员工[" + userid + "]数据");
                return;
            }
            Log.info(this.getClass(), item.toJSONString());
            String name = item.getString("name");
            String userId = item.getString("userid");
            String mobile = "";
            if (StringUtils.isNotBlank(userId) && userId.length() == 11) {
                mobile = userId.substring(0, 11);
            }
            String email = item.getString("email");
            String thumb_avatar = item.getString("thumb_avatar");
            if (StringUtils.isBlank(mobile)) {
                Log.error(this.getClass(), "企业微信员工[" + userid + "]手机号为空");
                return;
            }

            String sql = "select uid,docid from tb_weixin_workuser where uid='" + userid + "' ";
            JSONArray worklist = dbService.QueryList(sql);
            if (worklist.size() > 0) {
                String docid = worklist.getJSONObject(0).getString("docid");
                if (!StringUtils.isBlank(mobile)) {
                    sql = "update tb_card_teachstudinfo set mobile=? where uid=?";
                    dbService.excuteSql(sql, mobile, docid);
                }

                sql = "update tb_weixin_workuser set name=?,mobile=?,email=?,headimgurl=?,modifydate=now() where uid=?";
                dbService.excuteSql(sql, name, mobile, email, thumb_avatar, userid);
            } else {
                if (!StringUtils.isBlank(mobile)) {
                    sql = "select uid from tb_card_teachstudinfo where mobile='" + mobile + "' ";
                    JSONArray infolist = dbService.QueryList(sql);
                    if (infolist.size() > 0) {
                        String docid = infolist.getJSONObject(0).getString("uid");
                        sql = "insert into tb_weixin_workuser(uid,docid,name,mobile,email,headimgurl,createdate) values(?,?,?,?,?,?,now())";
                        dbService.excuteSql(sql, userid, docid, name, mobile, email, thumb_avatar);
                    }
                }
            }
        } catch (Exception ex) {
            Log.error(this.getClass(), "同步企业微信用户数据异常：" + ex.getMessage());
        }
    }

}
