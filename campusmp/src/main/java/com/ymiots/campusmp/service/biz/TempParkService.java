package com.ymiots.campusmp.service.biz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.entity.SysUser;
import com.ymiots.campusmp.redis.UserRedis;
import com.ymiots.campusmp.service.ConsumeService;
import com.ymiots.campusmp.utils.weixin.entity.WeiXinPayNotify;
import com.ymiots.framework.common.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.UUID;

@Repository
public class TempParkService extends BaseService {

    @Autowired
    UserRedis userredis;

    public boolean isNeedToPay(HttpServletRequest request, HttpServletResponse response, String areacode) {
        String infoid = userredis.get(request).getDocid();
        String sql = "SELECT btc.maxentry,btr.begintime,btr.endtime,btr.exittimes " +
                "FROM tb_biz_temppark_records btr " +
                "LEFT JOIN tb_biz_temppark_cfg btc ON btc.areacode=btr.areacode " +
                "WHERE infoid='" + infoid + "' AND btr.areacode='"+areacode+"' AND now() BETWEEN btr.begintime AND btr.endtime";
        JSONArray ja = dbService.QueryList(sql);
        if (!ja.isEmpty()) {
            int maxentry = ja.getJSONObject(0).getIntValue("maxentry");
            int exittimes = ja.getJSONObject(0).getIntValue("exittimes");
            return exittimes > maxentry;
        }
        return true;
    }

    public JsonData getTempParkMsg(HttpServletRequest request, HttpServletResponse response, String areacode) {
        String sql = "SELECT da.name as areaname,btc.price,btc.prehoures,btc.maxentry " +
                "FROM tb_biz_temppark_cfg btc " +
                "LEFT JOIN tb_dev_areaframework da ON da.code = btc.areacode " +
                "WHERE btc.areacode='" + areacode + "'";
        return dbService.QueryJsonData(sql);
    }
    
    public JsonResult CreateDocument(HttpServletRequest request, String areacode) {
    	SysUser user=userredis.get(request);
    	String userid =user.getUid();
		String docid=user.getDocid();
		String weixinid=user.getOpenid();
		String mobile=user.getIdcode();
		String headimgurl=user.getHeadimgurl();
		String nickname=user.getNickname();
		String sex=user.getSex();
		String name=user.getName();
		String orgcode=user.getOrgcode();
		
		int count=dbService.getCount("tb_weixin_user", "openid='"+weixinid+"'");
		if(count==0) {
			String sql = "insert into tb_weixin_user(uid,openid,usertype,docid,idcode,headimgurl,nickname,sex,lastlogintime,logintimes,status,createdate) values(?,?,?,?,?,?,?,?,now(),1,1,now())";
			dbService.excuteSql(sql, userid, weixinid, 3, docid, mobile, headimgurl,nickname, sex);
			String docsex="1";
			if(!StringUtils.isBlank(sex)) {
				if(sex.equals("2")) {
					docsex="2";
				}
			}
			String code=mobile.substring(1);
			if(Long.parseLong(code) > 4294967295L){
	            code=mobile.substring(2);
	        }
			String card=ComHelper.LeftPad(code, 12, '0');
			sql="INSERT INTO tb_card_teachstudinfo(uid,code,name,sex,card,cardsn,mobile,orgcode,infotype,infolabel,createdate,modifydate,status) VALUES(?,?,?,?,?,?,?,?,1,'临时',now(),now(),1);";
			dbService.excuteSql(sql, docid,code,name,docsex,card,card,mobile,orgcode);

			sql="INSERT INTO tb_biz_info_scheme(uid,infoid,areacode,allowother,createdate,modifydate,status) VALUES(uuid(),?,?,0,now(),now(),1)";
			dbService.excuteSql(sql, docid, areacode);

	        String cardid = UUID.randomUUID().toString();
	        String str="insert into tb_card_cardinfo(uid,infoid,usedes,cardno,cardsn,deposit,ismain,balance,vicewallet,waterwallet,timescount,enddate,createdate,modifydate,status) values " +
	                "('"+cardid+"','"+docid+"','本人', '"+card+"', '"+card+"',0,1,0,0,0,0,'2050-01-01',now(),now(),1)";
	        dbService.excuteSql(str);

	        String str2="insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,infocode,des,createdate) values" +
	                "(uuid(),'"+docid+"','"+cardid+"','"+card+"','"+card+"',0,1,'本人','0','微信注册自动发卡',now())";
	        dbService.excuteSql(str2);

	        String daily=DateHelper.format(new Date(), "yyyy-MM-dd");
	        int dailycount=dbService.getCount("tb_biz_daily_register", "orgcode='"+orgcode+"' and daily='"+daily+"'");
			if(dailycount==0) {
				 dbService.excuteSql("INSERT INTO tb_biz_daily_register(uid,daily,orgcode,total,createdate) VALUES(uuid(),'"+daily+"','"+orgcode+"',1,now())");
			}else {
				dbService.excuteSql("update tb_biz_daily_register set total=total+1 where daily='"+daily+"' and orgcode='"+orgcode+"' ");
			}
		}
		return Json.getJsonResult(true);
    }

    public JsonResult updateTransaction(HttpServletRequest request, HttpServletResponse response, WeiXinPayNotify weiXinPayNotify) {
        String uid = weiXinPayNotify.getOut_trade_no();
        String transactionid = weiXinPayNotify.getTransaction_id();
        int totalfee = weiXinPayNotify.getTotal_fee();
        String attach = weiXinPayNotify.getAttach();
        String[] attachs = attach.split(";");
        String areacode = attachs[0];
        String infoid = attachs[1];
        

        double money = totalfee / 100.00;
        Date timeend = DateHelper.parse(weiXinPayNotify.getTime_end(), "yyyyMMddHHmmss");
        if (timeend == null) {
            Log.error(ConsumeService.class, "支付完成时间转换失败");
            timeend = new Date();
        }

        int payexists = dbService.getCount("tb_biz_temppark_records", "uid='" + uid + "'");
        if (payexists > 0) {
            return Json.getJsonResult(true, "订单已回写，无需重复处理");
        }

        String sqlPrehoures = "SELECT prehoures FROM tb_biz_temppark_cfg WHERE areacode='" + areacode + "'";
        JSONArray prehouresja = dbService.QueryList(sqlPrehoures);
        if (prehouresja.isEmpty()) {
            return Json.getJsonResult(false, "临时停车配置不存在");
        }
        int prehoures = prehouresja.getJSONObject(0).getIntValue("prehoures");
        String endtime = DateHelper.format(DateHelper.addHours(new Date(), prehoures));
        //tb_biz_temppark_records
        String insertSQL = "INSERT INTO tb_biz_temppark_records(uid,areacode,infoid,money,begintime,endtime,paytime,exittimes,transactionid,createdate,modifydate,status) VALUES (?,?,?,?,now(),?,?,0,?,now(),now(),1)";
        dbService.excuteSql(insertSQL, uid, areacode, infoid, money, endtime, DateHelper.format(timeend),transactionid);

        int isSuccess = dbService.getCount("tb_biz_temppark_records", "transactionid='" + transactionid + "'");
        if (isSuccess > 0) {
            return Json.getJsonResult(true);
        }else {
            return Json.getJsonResult(false);
        }
    }

    public JsonData getWeiXinPayResult(HttpServletRequest request, HttpServletResponse response, String outtradeno) {
        String sql = "SELECT btr.uid,btr.infoid,btr.transactionid,btr.money as totalfee,date_format(btr.paytime, '%Y-%m-%d %H:%i:%s') as timeend ,da.name as parkname " +
                "FROM tb_biz_temppark_records btr " +
                "LEFT JOIN tb_dev_areaframework da ON da.code=btr.areacode " +
                " WHERE btr.uid = '" + outtradeno + "'";
        JsonData jd = dbService.QueryJsonData(sql);
        return jd;
    }
}
