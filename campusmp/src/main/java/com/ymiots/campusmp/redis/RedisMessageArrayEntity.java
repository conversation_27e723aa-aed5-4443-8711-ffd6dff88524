package com.ymiots.campusmp.redis;

import com.alibaba.fastjson.JSONArray;

public class RedisMessageArrayEntity{
	
	private String Server_uid;
	
	private int Devtype;
	
	private String Cmd;
	
	private JSONArray Data;

	public String getServer_uid() {
		return Server_uid;
	}

	public void setServer_uid(String server_uid) {
		Server_uid = server_uid;
	}

	public int getDevtype() {
		return Devtype;
	}

	public void setDevtype(int devtype) {
		Devtype = devtype;
	}

	public String getCmd() {
		return Cmd;
	}

	public void setCmd(String cmd) {
		Cmd = cmd;
	}

	public JSONArray getData() {
		return Data;
	}

	public void setData(JSONArray data) {
		Data = data;
	}
	
	
	

}
