//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.ymiots.campusmp.redis;

import com.ymiots.campusmp.config.redis.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Component
@Service
public class RedisPublish {

    @Autowired
    RedisClient redis;

    public RedisPublish() {
    }

    public void Publish(String topic, String message) {
        this.redis.stringRedisTemplate.convertAndSend(topic, message);
    }

    public void Publish(String topic, Object message) {
        this.redis.redisTemplate.convertAndSend(topic, message);
    }
}
