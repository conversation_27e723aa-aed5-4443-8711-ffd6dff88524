package com.ymiots.campusmp.redis;

import java.util.concurrent.TimeUnit;

import javax.servlet.http.<PERSON>ie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymiots.campusmp.config.redis.RedisClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ymiots.framework.common.Log;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.entity.SysUser;

@Service
public class UserRedis extends BaseService{

	@Autowired
	RedisClient redisclient;
	
	public final String tokenkey="campus_mp_tokenid";
	public final String onlineuserkey="campus_mp_activeuser";

	public void add(HttpServletRequest request, HttpServletResponse response,SysUser user) {
		//如果用户在线，则清除在线的用户
		if(redisclient.hasObjectKey(user.getUid())){
			redisclient.deleteObjectKey(user.getUid());
			redisclient.deleteStringKey(user.getTokenid());
		}
		String oldtoken=getTokenId(request);
		if(!StringUtils.isBlank(oldtoken)) {
			user.setTokenid(oldtoken);
		}
		redisclient.setString(user.getTokenid(), user.getUid(), 86400, TimeUnit.SECONDS);
		redisclient.setObject(user.getUid(), user, 86400, TimeUnit.SECONDS);
		
		if(!redisclient.SetInValue(onlineuserkey, user.getUid())){
			redisclient.SetAdd(onlineuserkey, user.getUid());
		}
		Cookie cookie = new Cookie(tokenkey, user.getTokenid());
		// 一年有效期
		cookie.setMaxAge(525600);
		cookie.setPath("/");
		response.addCookie(cookie);
	}

	public String getTokenId(HttpServletRequest request) {
		Cookie[] cookies = request.getCookies();
		String tokenId = "";
		if (null != cookies) {
			for (Cookie item : cookies) {
				if (item.getName().equals(tokenkey)) {
					tokenId = item.getValue();
					break;
				}
			}
		}else {
			String Cookie=request.getHeader("Cookie");
			if(!StringUtils.isBlank(Cookie)) {
				String[] cookiex=Cookie.split("; ");
				for (String item : cookiex) {
					if (item.indexOf(tokenkey)!=-1) {
						tokenId = item.split("=")[1];
						break;
					}
				}
			}
		}
		return tokenId;
	}

	public Cookie getTokenCookie(HttpServletRequest request) {
		Cookie[] cookies = request.getCookies();
		Cookie tokenCookie = null;
		if (null != cookies) {
			for (Cookie item : cookies) {
				if (item.getName().equals(tokenkey)) {
					tokenCookie = item;
					break;
				}
			}
		}
		return tokenCookie;
	}
	
	public String getUserId(HttpServletRequest request) {
		String tokenId = getTokenId(request);
		if(!StringUtils.isBlank(tokenId)){
			String userid=redisclient.getString(tokenId);
			return userid;
		}else{
			return "";
		}
	}
	
	public SysUser get(HttpServletRequest request) {
		String userid=getUserId(request);
		if(!StringUtils.isBlank(userid)){
			SysUser user= redisclient.getEntity(userid, SysUser.class);
			return user;
		}else{
			return null;
		}
	}

	public boolean exists(HttpServletRequest request) {
		String tokenId = getTokenId(request);
		boolean result=false;
		if (!tokenId.equals("")) {
			result= redisclient.hasStringKey(tokenId);
		}
		return result;
	}
	
	public boolean exists(HttpServletRequest request, String UserId) {
		boolean haskey= redisclient.hasObjectKey(UserId);
		if(!haskey) {
			redisclient.SetRemoveValue(onlineuserkey, UserId);
		}
		return haskey;
	}
	
	public boolean remove(HttpServletRequest request) {
		String tokenId=getTokenId(request);
		String userid=getUserId(request);
		if(!StringUtils.isBlank(userid)){
			redisclient.deleteObjectKey(userid);		
			redisclient.SetRemoveValue(onlineuserkey, userid);
		}
		if (!StringUtils.isBlank(tokenId)) {
			redisclient.deleteStringKey(tokenId);
			return true;
		} else {
			return false;
		}
	}

	public String keepAlive(HttpServletRequest request) {
		String tokenId = getTokenId(request);
		String userId=getUserId(request);
		if (StringUtils.isBlank(tokenId)){
			return "0/0";
		}
		if (StringUtils.isBlank(userId)){
			return "0/0";
		}

		redisclient.setStringExpire(tokenId, 86400, TimeUnit.SECONDS);
		redisclient.setExpire(userId, 86400, TimeUnit.SECONDS);
		
		long tokenidexpire=redisclient.getStringExpire(tokenId, TimeUnit.SECONDS); 
		long useriddexpire=redisclient.getExpire(userId, TimeUnit.SECONDS); 
		String result= String.format("%s/%s", tokenidexpire,useriddexpire);
		if (result.equals("0/0")){
			Log.error(UserRedis.class, "用户心跳失败，无法获取tokenId和userId已过期");
		}
		return result;
	}
}
