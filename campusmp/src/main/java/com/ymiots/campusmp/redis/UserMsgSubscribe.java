package com.ymiots.campusmp.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.service.AccessTokenService;
import com.ymiots.campusmp.utils.weixin.WeiXinTemplateMessage;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessage;
import com.ymiots.campusmp.utils.weixin.entity.WeiXinResult;
import com.ymiots.framework.common.Log;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class UserMsgSubscribe extends BaseService implements RedisMessageHandler {

    @Autowired
    WeiXinTemplateMessage wxmsg;

    @Autowired
    AccessTokenService accesstoken;

    @Override
    public String getChannel() {
        return "/campus/userwxmsgpush";
    }

    @Override
    public void handleMessage(String message) {
    	//推送后台配置的用户微信消息
        JSONObject msgobj = JSON.parseObject(message);
        JSONObject msgdata = msgobj.getJSONObject("Data");
        String cfgcode = msgdata.getString("cfgcode");
        
        int isvistor=msgdata.getIntValue("isvistor");
        String infoid = msgdata.getString("infoid");
        
        String isParent = msgdata.getString("isParent");
        String orgcode = msgdata.getString("orgcode");
        
        JSONArray list =new JSONArray();
        
        if(isvistor==0) {
            String fields = "wu.uid as wxuserid,wu.openid,wu.docid,wu.idcode,wu.nickname, wu.usertype ";
            String table = "tb_weixin_user wu " +
                    "INNER JOIN tb_card_teachstudinfo ct ON wu.docid = ct.uid ";
            String where = "1=1 ";
            if ("1".equals(isParent)) {
                where += " AND wu.usertype = 2";
            } else {
                where += " AND wu.usertype in(0,1,3)";
            }
            if (StringUtils.isBlank(infoid) && StringUtils.isNotBlank(orgcode)) {
                where += " AND ct.orgcode LIKE '" + orgcode + "%'";
            } else if (StringUtils.isNotBlank(infoid)) {
                where += " AND find_in_set(wu.docid,'"+infoid+"')>0";
            }
            list = dbService.QueryJSONArray(fields, table, where, "wu.createdate DESC", 0, 0);
        }
        if(isvistor==1) {
        	String fields = "wu.uid as wxuserid,wu.openid,wu.docid,wu.idcode,wu.nickname, wu.usertype ";
            String table = "tb_weixin_user wu INNER JOIN tb_visitor_visitorinfo ct ON wu.docid = ct.uid ";
            String where = " wu.usertype=4 and find_in_set(wu.docid,'"+infoid+"')>0 ";
            list = dbService.QueryJSONArray(fields, table, where, "wu.createdate DESC", 0, 0);
        }
        
        String sql = "SELECT msg.uid as msgid,msg.title,msg.templatecfg,cfg.templateid,cfg.template " +
                "FROM tb_weixin_msg msg " +
                "INNER JOIN tb_weixin_msgcfg cfg on cfg.uid=msg.cfgid " +
                "WHERE msg.status=1 AND cfg.code='" + cfgcode + "' AND cfg.status=1 AND cfg.templateid<>'' AND not cfg.templateid is null AND msg.templatecfg<>'[]' AND msg.msgtype=2";
        JSONArray msgList = dbService.QueryList(sql);
        if (list.size() > 0 && msgList.size() > 0) {
            for (int j = 0; j < msgList.size(); j++) {
                JSONObject msljo = msgList.getJSONObject(j);
                TemplateMessage templatemsg = new TemplateMessage();
                templatemsg.setTemplate_id(msljo.getString("templateid"));
                JSONObject msg = new JSONObject();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject item = list.getJSONObject(i);
                    JSONArray cfg = msljo.getJSONArray("templatecfg");
                    templatemsg.setTouser(item.getString("openid"));
                    templatemsg.setData(wxmsg.ConvertToTemplateMsgData(cfg, msg));
                    try {
                        WeiXinResult wxresult = wxmsg.SendTemplateMessage(templatemsg, accesstoken.GetAccessToken());
                        if (wxresult.getErrcode() != 0) {
                            Log.error(UserMsgSubscribe.class, "用户微信消息推送失败：" + wxresult.getErrmsg());
                        } else {
                            String msgid = msljo.getString("msgid");
                            String wxuserid = item.getString("wxuserid");
                            String msgcontent = wxmsg.TemplateToMessage(msljo.getString("template"), templatemsg.getData());
                            String docid = item.getString("docid");
                            String idcode = item.getString("idcode");
                            String nickname = item.getString("nickname");
                            String usertype = item.getString("usertype");
                            sql = "insert into tb_weixin_msgrecord(uid,msgid,wxuserid,docid,idcode,nickname,usertype,msgcontent,createdate,status) values(uuid(),?,?,?,?,?,?,?,now(),1)";
                            dbService.excuteSql(sql, msgid, wxuserid, docid, idcode, nickname, usertype, msgcontent);
                        }
                    } catch (Exception e) {
                        Log.error(UserMsgSubscribe.class, e.getMessage());
                    }
                }
            }
        }
    }
}
