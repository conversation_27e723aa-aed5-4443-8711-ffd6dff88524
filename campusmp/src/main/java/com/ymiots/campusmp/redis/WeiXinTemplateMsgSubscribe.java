package com.ymiots.campusmp.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.ParserConfig;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.config.redis.RedisClient;
import com.ymiots.campusmp.service.AccessTokenService;
import com.ymiots.campusmp.utils.weixin.WeiXinTemplateMessage;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessage;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessageData;
import com.ymiots.campusmp.utils.weixin.entity.WeiXinResult;
import com.ymiots.framework.common.HttpRequest;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

@Repository
public class WeiXinTemplateMsgSubscribe extends BaseService implements RedisMessageHandler {

    @Autowired
	WeiXinTemplateMessage wxmsg;

    @Autowired
    AccessTokenService accesstoken;

	@Autowired
	RedisClient redisclient;

    @Override
    public String getChannel() {
        return "/campus/weixintemplatemsg";
    }

    @Override
    public void handleMessage(String message) {
    	//推送微信消息
        try {
			if (WebConfig.EnableCampusCPService()) {
				ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
				JSONObject jsonobj = JSONObject.parseObject(message);
				JSONObject msgdata = jsonobj.getJSONObject("Data");
				String wxuserid = msgdata.getString("wxuserid");
				msgdata.put("appid", WebConfig.getCampusCPAppId());
				msgdata.put("nodeid", WebConfig.getNodeid());
				String url = String.format("%s/weixin/send_wxtemplate_message", WebConfig.getCampusCPDomain());
				String json = msgdata.toJSONString();
				Log.info(this.getClass(), json);
				JsonResult result = HttpRequest.HttpPostJsonData(url, json);
				if (result.isSuccess()) {
					Log.info(this.getClass(), "调用CP平台微信消息推送接口成功");
					JSONObject data = result.getData();
					if (data.getBooleanValue("success")) {
						JSONObject msg = data.getJSONObject("data");
						String msgid = msg.getString("msgid");
						String msgcontent = msg.getString("msgcontent");
						String sql = "insert into tb_weixin_msgrecord(uid,msgid,wxuserid,docid,idcode,nickname,usertype,msgcontent,createdate,status) SELECT uuid(),?,uid,docid,idcode,nickname,usertype,?,now(),1 FROM tb_weixin_user where uid='" + wxuserid + "'";
						dbService.excuteSql(sql, msgid, msgcontent);
					} else {
						Log.error(this.getClass(), "调用CP平台微信消息推送接口失败：" + data.getString("msg"));
					}
				} else {
					Log.error(this.getClass(), "调用CP平台微信消息推送接口失败：" + result.getMsg());
				}
			} else {
				ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
				JSONObject msgobj = JSON.parseObject(message);
				JSONObject msgdata = msgobj.getJSONObject("Data");
				String wxuserid = msgdata.getString("wxuserid");
				String msgid = msgdata.getString("msgid");
				String cfgcode = msgdata.getString("cfgcode");
				String openid = msgdata.getString("openid");
				String url = msgdata.getString("url");
				JSONObject wxtempdata = msgdata.getJSONObject("wxtempdata");
				Map<String, TemplateMessageData> map = new HashMap<String, TemplateMessageData>();
				for (String key : wxtempdata.keySet()) {
					JSONObject item = wxtempdata.getJSONObject(key);
					map.put(key, new TemplateMessageData(item.getString("value"), item.getString("color")));
				}
				String sql = "SELECT templateid,template FROM tb_weixin_msgcfg WHERE code='" + cfgcode + "' ";
				JSONArray msgList = dbService.QueryList(sql);
				if (msgList.size() == 0) {
					Log.error(WeiXinTemplateMsgSubscribe.class, "用户微信消息推送失败：微信消息模板编号错误");
					return;
				}

				//第一步：根据uid和openid获取微信用户对应的人员信息docid
				String docidSQL = "SELECT docid FROM tb_weixin_user wu WHERE wu.uid='" + wxuserid + "' AND wu.openid='" + openid + "' AND wu.status=1 ";
				JSONArray docidja = dbService.QueryList(docidSQL);

				if (!docidja.isEmpty()) {
					String docid = docidja.getJSONObject(0).getString("docid");
					//第二步：根据人员信息docid 判断人员是否在黑名单。是-直接return不推送消息
					int count = dbService.getCount("tb_weixin_msgblack wm INNER JOIN tb_weixin_msgcfg cfg ON cfg.uid=wm.msgcfgid", "wm.infoid='" + docid + "' AND cfg.code='" + cfgcode + "'");
					if (count > 0) {
						Log.error(WeiXinTemplateMsgSubscribe.class, "用户微信消息推送失败：微信用户不可接收该消息模板");
						return;
					}

				} else {
					Log.error(WeiXinTemplateMsgSubscribe.class, "用户微信消息推送失败：微信用户不存在");
					return;
				}

				JSONObject msgcfg = msgList.getJSONObject(0);
				TemplateMessage templatemsg = new TemplateMessage();
				templatemsg.setTemplate_id(msgcfg.getString("templateid"));
				templatemsg.setTouser(openid);
				templatemsg.setUrl(url);
				templatemsg.setData(map);
				WeiXinResult wxresult = wxmsg.SendTemplateMessage(templatemsg, accesstoken.GetAccessToken());
				if (wxresult.getErrcode() != 0) {
					redisclient.deleteStringKey("campusmp_weixin_accesstoken");
					wxresult = wxmsg.SendTemplateMessage(templatemsg, accesstoken.GetAccessToken());
					if (wxresult.getErrcode() != 0) {
						Log.error(WeiXinTemplateMsgSubscribe.class, "用户微信消息推送失败："+wxresult.getErrmsg()+",微信模版："+msgcfg.getString("templateid")+"");
					}
				} else {
					if (!StringUtils.isBlank(wxuserid)) {
						String msgcontent = wxmsg.TemplateToMessage(msgcfg.getString("template"), templatemsg.getData());
						sql = "insert into tb_weixin_msgrecord(uid,msgid,wxuserid,docid,idcode,nickname,usertype,msgcontent,createdate,status) SELECT uuid(),?,uid,docid,idcode,nickname,usertype,?,now(),1 FROM tb_weixin_user where uid='" + wxuserid + "'";
						dbService.excuteSql(sql, msgid, msgcontent);
					}
					Log.info(WeiXinTemplateMsgSubscribe.class, "用户微信消息推送成功：" + msgid);
				}
			}
        }catch (Exception e) {
            Log.error(WeiXinTemplateMsgSubscribe.class, e.getMessage());
            Log.info(this.getClass(), message);
        }
    }
}
