package com.ymiots.campusmp.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.framework.common.Log;

@Component
@Service
public class RedisMessagePublish {

	@Autowired
	RedisPublish redispublish;

	
	/**
	 *  名单处理(下载和删除)
	 * @param message
	 */
	public void ListProcess(RedisMessageEntity message) {
		String topic="/campus/listprocess";
		redispublish.Publish(topic, message);	
		Log.info(RedisMessagePublish.class, String.format("发布主题【%s】消息：%s", topic, JSONObject.toJSONString(message)));
	}
	
	/**
	 *  远程门操作（开门，关门，常开）
	 * @param message
	 */
	public void DoorControl(String serveruid, RedisMessageArrayEntity message) {
		String topic="/campus/"+serveruid+"/door/control";
		redispublish.Publish(topic, message);	
		Log.info(RedisMessagePublish.class, String.format("发布主题【%s】消息：%s", topic,JSONObject.toJSONString(message)));
	}

	
	/**
	 *  通行时段下载
	 */
	public void PassDurations(RedisMessageEntity message) {
		String topic="/campus/passdurations";
		redispublish.Publish(topic, message);	
		Log.info(RedisMessagePublish.class, String.format("发布主题【%s】消息：%s", topic,JSONObject.toJSONString(message)));
	}
	
	/**
	 *  门设置（继电器对应门）
	 * @param message
	 */
	public void SetDoorReadhead(String serveruid,RedisMessageEntity message) {
		String topic="/campus/"+serveruid+"/setdoorreadhead";
		redispublish.Publish(topic, message);
		Log.info(RedisMessagePublish.class, String.format("发布主题【%s】消息：%s", topic,JSONObject.toJSONString(message)));
	}
	
	/**
	 *  超级用户(名单下载、删除)
	 * @param serveruid
	 * @param message
	 */
	public void SetPasswordPass(RedisMessageEntity message) {
		String topic="/campus/setpasswordpass";
		redispublish.Publish(topic, message);
		Log.info(RedisMessagePublish.class, String.format("发布主题【%s】消息：%s", topic,JSONObject.toJSONString(message)));
	}
	
	/**
	 *  火警报警(名单下载、删除)
	 * @param serveruid
	 * @param message
	 */
	public void FirAalarm(RedisMessageEntity message) {
		String topic="/campus/firealarm";
		redispublish.Publish(topic, message);
		Log.info(RedisMessagePublish.class, String.format("发布主题【%s】消息：%s", topic,JSONObject.toJSONString(message)));
	}
	
	
	/**
	 *  交易记录变化时通知服务端
	 * @param transactiondetail
	 */
	public void NotifyTransaction(JSONArray transactiondetail) {
		String topic="/campus/consume/notifytransaction";
		RedisMessageEntity message=new RedisMessageEntity();
		message.setCmd("notifytransaction");
		JSONObject data=new JSONObject();
		data.put("transaction", transactiondetail);
		message.setData(data);
        message.setDevtype(0);
        message.setServer_uid("");
		redispublish.Publish(topic, message);
		Log.info(RedisMessagePublish.class, String.format("发布主题【%s】消息：%s", topic,JSONObject.toJSONString(message)));
	}

	/**
	 *  访客登记记录通知
	 * @param serveruid
	 * @param message
	 */
	public void VisitorRecord(RedisMessageEntity message) {
		String topic="/campus/visitorrecordmsg";
		redispublish.Publish(topic, message);
		Log.info(RedisMessagePublish.class, String.format("发布主题【%s】消息：%s", topic,JSONObject.toJSONString(message)));
	}
	
	/**
	 *  通知删除访客缓存人脸
	 * @param serveruid
	 * @param message
	 */
	public void VisitorFaceDel(RedisMessageEntity message) {
		String topic="/campus/visitorfacedel";
		redispublish.Publish(topic, message);
		Log.info(RedisMessagePublish.class, String.format("发布主题【%s】消息：%s", topic,JSONObject.toJSONString(message)));
	}
	
	
}
