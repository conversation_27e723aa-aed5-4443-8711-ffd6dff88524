package com.ymiots.campusmp.redis;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class RegisterRedisSubscrib implements RedisSubscriber {

	public List<RedisMessageHandler> Subscriblist=new ArrayList<>();
	
	/**
	 *  设备通讯服务状态异常
	 */
	@Autowired
	DeviceServiceStatusSubscribe deviceservice;

	@Autowired
    UserMsgSubscribe usermsg;

	@Autowired
	WeiXinTemplateMsgSubscribe wxtempmsg;
	
	@Autowired
	ServerNodeStatusSubscribe servernodestatus;
	
	
	/***
	 * 返回订阅Redis的类
	 */
	@Override
	public List<RedisMessageHandler> subscribeConfig() {
		
		this.Subscriblist.add(deviceservice);
        this.Subscriblist.add(usermsg);
        this.Subscriblist.add(wxtempmsg);
        this.Subscriblist.add(servernodestatus);
		return this.Subscriblist;
	}


}
