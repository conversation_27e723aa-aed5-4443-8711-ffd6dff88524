package com.ymiots.campusmp.redis;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.config.redis.RedisClient;
import com.ymiots.campusmp.service.AccessTokenService;
import com.ymiots.campusmp.utils.weixin.WeiXinTemplateMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


@Repository
public class DeviceServiceStatusSubscribe  extends BaseService implements RedisMessageHandler {

	@Autowired
	WeiXinTemplateMessage wxmsg;
	
	@Autowired
	AccessTokenService accesstoken;
	
	@Autowired
	RedisClient redisclient;
	
	@Override
	public String getChannel() {
		// TODO Auto-generated method stub
		return "/campus/deviceserviceabnormal";
	}

	@Override
	public void handleMessage(String message) {
		// TODO Auto-generated method stub
		
		
		JSONObject msgobj=JSONObject.parseObject(message);
		JSONObject msgdata= msgobj.getJSONObject("Data");
		
		//运行状态通知微信消息推送
		/*
		String sql="SELECT ms.uid as subscribeid, msg.uid as msgid, msg.title,msg.templatecfg,cfg.templateid,cfg.template,wu.openid, wu.uid as wxuserid, wu.docid, wu.nickname, wu.usertype, wu.idcode FROM "
				+ " tb_weixin_msgsubscribe ms inner join "
				+ " tb_weixin_user wu on wu.uid=ms.wxuserid inner join "
				+ " tb_weixin_msg msg on msg.uid=ms.msgid inner join "
				+ " tb_weixin_msgcfg cfg on cfg.uid=msg.cfgid "
				+ " where msg.status=1 and cfg.code='OPENTM406928566' and cfg.status=1 and cfg.templateid<>'' and not cfg.templateid is null and msg.templatecfg<>'[]'";
		
		JSONArray list= dbService.QueryList(sql);
		if(list.size()>0) {
			for(int i=0;i<list.size();i++) {
				JSONObject item=list.getJSONObject(i);
				
				String subscribeid=item.getString("subscribeid");
				if(redisclient.hasStringKey(subscribeid)) {
					 break;
				}
				
				JSONArray cfg=item.getJSONArray("templatecfg");
				TemplateMessage templatemsg=new TemplateMessage();
				templatemsg.setTouser(item.getString("openid"));
				templatemsg.setTemplate_id(item.getString("templateid"));
				templatemsg.setData(wxmsg.ConvertToTemplateMsgData(cfg, msgdata));
				try {
					WeiXinResult wxresult=wxmsg.SendTemplateMessage(templatemsg, accesstoken.GetAccessToken());
					if(wxresult.getErrcode()!=0) {
						Log.error(DeviceServiceStatusSubscribe.class, "设备通讯服务状态异常微信消息推送失败："+wxresult.getErrmsg());
					}else {
						String msgid=item.getString("msgid");
						String wxuserid=item.getString("wxuserid");
						String msgcontent=wxmsg.TemplateToMessage(item.getString("template"), templatemsg.getData());
						String docid=item.getString("docid");
						String idcode=item.getString("idcode");
						String nickname=item.getString("nickname");
						String usertype=item.getString("usertype");
						sql="insert into tb_weixin_msgrecord(uid,msgid,wxuserid,docid,idcode,nickname,usertype,msgcontent,createdate,status) values(uuid(),?,?,?,?,?,?,?,now(),1)";
						dbService.excuteSql(sql, msgid,wxuserid,docid,idcode,nickname,usertype,msgcontent);
						redisclient.setString(subscribeid, DateHelper.format(new Date()), 2, TimeUnit.HOURS);
					}
				} catch (Exception e) {
					Log.error(DeviceServiceStatusSubscribe.class, e.getMessage());
				}
			}
		}
		*/
	}

}
