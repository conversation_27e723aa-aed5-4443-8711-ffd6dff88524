package com.ymiots.campusmp.redis;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * Redis消息结构体
 * <AUTHOR>
 *
 */
public class RedisMessageEntity{
	
	@JSONField  (name = "Server_uid")
	private String Server_uid;
	
	@J<PERSON><PERSON>ield  (name = "Devtype")
	private int Devtype;
	
	@JSONField  (name = "Cmd")
	private String Cmd;
	
	@JSONField  (name = "Data")
	private JSONObject Data;

	public String getServer_uid() {
		return Server_uid;
	}

	public void setServer_uid(String server_uid) {
		Server_uid = server_uid;
	}

	public int getDevtype() {
		return Devtype;
	}

	public void setDevtype(int devtype) {
		Devtype = devtype;
	}

	public String getCmd() {
		return Cmd;
	}

	public void setCmd(String cmd) {
		Cmd = cmd;
	}

	public JSONObject getData() {
		return Data;
	}

	public void setData(JSONObject data) {
		Data = data;
	}
}
