package com.ymiots.campusmp.redis;

import org.springframework.stereotype.Component;

import com.ymiots.campusmp.common.BaseService;

/**
 * 刷卡记录上传（用于实时监控）
 * <AUTHOR>
 *
 */
@Component
public class CardRecordMsgSubscribe extends BaseService implements RedisMessageHandler {

	@Override
	public String getChannel() {
		return "/service/recordlist";
	}

	@Override
	public void handleMessage(String message) {
		// TODO Auto-generated method stub
		
	}

}
