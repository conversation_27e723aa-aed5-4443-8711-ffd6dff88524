package com.ymiots.campusmp.redis;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.common.BaseService;
import com.ymiots.campusmp.common.WebConfig;
import com.ymiots.campusmp.service.SysConfigService;
import com.ymiots.campusmp.service.SysTaskService;
import com.ymiots.campusmp.task.SysDynamicTaskService;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.websocket.WebSocketSets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 节点服务消息订阅
 * 
 * <AUTHOR>
 *
 */
@Component
public class ServerNodeStatusSubscribe extends BaseService implements RedisMessageHandler {

	@Autowired
	RedisPublish redispublish;

	@Autowired
	SysConfigService sysconfig;

	@Autowired
	SysDynamicTaskService dynamictaskservice;
	
	@Autowired
	SysTaskService taskservice;
	
	@Override
	public String getChannel() {
		return "/service/servernodestatus";
	}

	@Override
	public void handleMessage(String message) {
		JSONObject msgobj = JSONObject.parseObject(message);
		Log.info(CardRecordMsgSubscribe.class, "订阅主题【" + getChannel() + "】接收到消息：" + msgobj.toJSONString());

		if (WebConfig.getProcesstask().equals("1")) {
			ServerStatusHandle(msgobj);
		} else {
			Log.info(CardRecordMsgSubscribe.class, "本节点未启用任务处理，不做任何处理");
		}
	}

	private void ServerStatusHandle(JSONObject msg) {
		// 状态编号
		try {
			String msgcode=msg.getString("code");
			String node=msg.getString("node");
			if(!node.equals("mp")) {
				return;
			}
			if(msgcode.equals("1")) {
				JSONObject data=msg.getJSONObject("data");
				//启动计划任务
				String code=data.getString("code");
				String cron=data.getString("cron");
				String taskclass=data.getString("taskclass");
				String sqlcode=data.getString("sqlcode");
				JsonResult result= dynamictaskservice.StartTask(code, taskclass, cron, sqlcode);
				if(!result.isSuccess()) {
					taskservice.UpdateSysTaskStatusByCode(code,"0",result.getMsg());
				}else {
					taskservice.UpdateSysTaskStatusByCode(code,"1",result.getMsg());
				}
			}else if(msgcode.equals("2")) {
				JSONObject data=msg.getJSONObject("data");
				//停止计划任务
				String code=data.getString("code");
				String taskclass=data.getString("taskclass");
				
				JsonResult result= dynamictaskservice.CancelTask(code, taskclass);
				if(!result.isSuccess()) {
					taskservice.UpdateSysTaskStatusByCode(code,"0",result.getMsg());
				}else {
					taskservice.UpdateSysTaskStatusByCode(code,"1",result.getMsg());
				}
			}else if(msgcode.equals("3")) {
				JSONObject data=msg.getJSONObject("data");
				//执行计划任务
				String code=data.getString("code");
				String taskclass=data.getString("taskclass");
				String sqlcode=data.getString("sqlcode"); 
				String name=data.getString("name");
				JsonResult result= dynamictaskservice.ExecuteTask(code, taskclass, sqlcode);
				JSONObject info=new JSONObject();
				if(result.isSuccess()) {
					info.put("msg", String.format("计划任务：%s(%s)执行成功！", name, code));
				}else {
					info.put("msg", String.format("计划任务：%s(%s)执行失败[%s]！", name, code,result.getMsg()));
				}
				WebSocketSets.getInstance().send("sysmsg", info.toJSONString());
			}else if(msgcode.equals("4")) {
				JSONObject data=msg.getJSONObject("data");
				//更新执行计划表达式
				String code=data.getString("code");
				String cron=data.getString("cron");
				String taskclass=data.getString("taskclass");
				String sqlcode=data.getString("sqlcode"); 
				JsonResult result= dynamictaskservice.ChangeCron(code, taskclass, cron, sqlcode);
				if(!result.isSuccess()) {
					taskservice.UpdateSysTaskStatusByCode(code,"",result.getMsg());
				}else {
					taskservice.UpdateSysTaskStatusByCode(code,"", "刷新cron并重启成功");
				}
			}else if(msgcode.equals("5")) {
				JSONObject data=msg.getJSONObject("data");
				//禁止自动运行并停止运行
				String uid=data.getString("uid");
				String code=data.getString("code");
				String name=data.getString("name");
				String taskclass=data.getString("taskclass");
			
				JsonResult rs=dynamictaskservice.CancelTask(code, taskclass);
				if(rs.isSuccess()) {
					String sql=" update tb_sys_schedule set runstatus=0 where uid='"+uid+"' ";
					dbService.excuteSql(sql);
					Log.info(SysTaskService.class, String.format("停止任务[%s]成功", name));
				}
				else {
					Log.error(SysTaskService.class, String.format("停止任务[%s]失败，错误：%s", name,rs.getMsg()));
				}
			}
		} catch (Exception e) {
			Log.error(this.getClass(), e.getMessage());
		}
	}

}
