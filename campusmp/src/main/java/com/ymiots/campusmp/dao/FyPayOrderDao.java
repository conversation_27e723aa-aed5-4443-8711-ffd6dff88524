package com.ymiots.campusmp.dao;

import com.ymiots.campusmp.common.DBService;
import com.ymiots.campusmp.model.domain.AllInFlow;
import com.ymiots.campusmp.model.domain.FyPayOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

/**
 * 支付平台订单操作，订单信息来自富友
 *
 * <AUTHOR>
 * @date 2022/11/19
 */
@Repository
public class FyPayOrderDao {

    @Autowired
    protected DBService dbService;

    /**
     * 富友支付商户号
     */
    @Value("${fuiou.mchnt-code}")
    private String fuiOuPayMchNtCode;

    public void insertFyPayOrder(FyPayOrder order) {
        String sql = "insert into " +
                "tb_fypay_order(order_no, mchnt_order_no, order_type, trade_date, trade_state, total, devid, buyer_id, ali_fund_bill_list, " +
                "fy_trace_no, channel_order_no, channel_flow_no) " +
                "values(?,?,?,?,?,?,?,?,?,?,?,?)";
        dbService.excuteSql(sql,
                order.getOrderNo(),
                order.getMchntOrderNo(),
                order.getOrderType(),
                order.getTradeDate(),
                order.getTradeState(),
                order.getTotal(),
                order.getDevid(),
                order.getBuyerId(),
                order.getAliFundBillList(),
                order.getFyTraceNo(),
                order.getChannelOrderNo(),
                order.getChannelFlowNo()
        );
    }

    public void insertFyPayFlow(FyPayOrder order) {
        String sql = "insert into " +
                "fy_flow(flow_no, top_mchnt_order_no, top_os_order_no, mchnt_code, amount, order_type," +
                " opt_type, opt_date, result_msg) " +
                "values(uuid(),?,?,?,?,?,?,now(),?)";
        dbService.excuteSql(sql,
                order.getMchntOrderNo(),
                order.getOrderNo(),
                fuiOuPayMchNtCode,
                order.getTotal(),
                order.getOrderType(),
                "SUCCESS",
                "SUCCESS"
        );
    }

    public void insertAllInPayFlow(AllInFlow order,String flowId) {
        String sql = "insert into " +
                "allin_flow(flow_no, top_mchnt_order_no, top_os_order_no, cus_id, amount, order_type," +
                " opt_type, trx_date,pay_time, result_msg) " +
                "values(?,?,?,?,?,?,?,?,now(),?)";
        dbService.excuteSql(sql,
                flowId,
                order.getTopMchntOrderNo(),
                order.getTopOsOrderNo(),
                order.getCusId(),
                order.getAmount(),
                order.getOrderType(),
                "交易成功(补充)",
                order.getTrxDate(),
                "交易成功(补充)"
        );
    }
}
