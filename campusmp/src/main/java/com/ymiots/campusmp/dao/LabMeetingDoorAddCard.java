package com.ymiots.campusmp.dao;

import lombok.Data;
import lombok.experimental.Accessors;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Date;
import java.util.List;

/**
 * 会议室 预约门锁下载卡
 *
 * <AUTHOR>
 * @date 2023/03/16
 */
@Data
@Accessors(chain = true)
public class LabMeetingDoorAddCard {
    /**
     * 会议室时间表单uid
     */
    private List<String> timeRecordUid;
    /**
     * os表单uid
     */
    private String osFormUid;

    /**
     * 下载成功返回id
     */
    private String cardId;

    /**
     * 卡名称
     */
    private String name;

    /**
     * 门序列号
     */
    private String doorSn;

    /**
     * 区域
     */
    private String areaCode;

    /**
     * 设备
     */
    private Integer devModel;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 人员索引下发卡
     */
    private Integer infoIndex;

    /**
     * 下载状态
     */
    private Integer status;

    /**
     * 电控下载状态
     */
    private Integer electricalStatus;


    /**
     * 表单id
     */
    private List<String> formId;

    /**
     * 日期
     */
    private String day;

    /**
     * 开始时间段
     */
    private String startTime;

    /**
     * 结束时间段
     */
    private String endTime;

    /**
     * 卡有效开始时间
     */
    private Date startDate;

    /**
     * 卡有效时间结束
     */
    private Date endDate;

    public LabMeetingDoorAddCard setCardNo(String cardNo) {
//        int cardNoInt = Integer.parseInt(cardNo);
//        this.cardNo = String.valueOf(toUnsignedInt(ByteBuffer.allocate(4).putInt(cardNoInt).array()));;
        this.cardNo = cardNo;
        return this;
    }

    public static long toUnsignedInt(byte[] buf) {
        ByteBuffer byteBuffer = ByteBuffer.wrap(buf);
        byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
        int signedInt = byteBuffer.getInt();
        return signedInt & 0xFFFFFFFFL; // Convert to unsigned integer
    }
}
