package com.ymiots.campusmp.common;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

@Service
@Component
@ConfigurationProperties(prefix = "webconfig")
public class WebConfig {
	
    private static String serverid;
	private static String uploaddir;
	private static String appname;
	private static String appversion;
	private static String apptype;
	private static String processtask;
	private static String domain;
	private static String resourceDomain;
	private static String bindmodel;
	private static String workbindmodel;
	private static boolean startNew;

	private static String payServerAddress;
	private static String osServerAddress;
	private static String iotServerAddress;

	public static String getPayServerAddress() {
		return payServerAddress;
	}

	public void setPayServerAddress(String payServerAddress) {
		WebConfig.payServerAddress = payServerAddress;
	}

	private static String isyunouth;
	private static String isWorkYunToken;
	private static String isyuntoken;
	private static String weixinAppid;
	private static String weixinAppSecret;
	private static String weixinToken;
	private static String weixinEncodingAESKey;
	private static String weixinMchid;
	private static String weixinMchKey;
	
	private static String debug;
	private static String debugid;
	
	private static String campusid;
	private static String wxpayid;
	private static String campusappsecret;
	
	private static String weixinWorkCorpid;
	private static String weixinWorkAgentId;
	private static String weixinWorkSecret;
	
	private static String weixinWorkDebug;
	private static String weixinWorkDebugid;
	
    private static String nodeid;
    private static String campusCPDomain;
    private static String campusCPAppId;
    private static String campusCPAppSecret;
    
	public static String getServerid() {
		return serverid;
	}

	public void setServerid(String serverid) {
		WebConfig.serverid = serverid;
	}

	public static String getUploaddir() {
		//return "/usr/campus/upload/";
		return uploaddir;
	}

	public void setUploaddir(String uploaddir) {
		WebConfig.uploaddir = uploaddir;
	}

	public static String getAppname() {
		return appname;
	}

	public void setAppname(String appname) {
		WebConfig.appname = appname;
	}

	public static String getApptypename() {
		if (StringUtils.isNotBlank(getApptype())) {
			return getAppname();
		}
		if (getApptype().equals("1")) {
			return "智慧校园";
		} else if (getApptype().equals("2")) {
			return "智慧企业";
		} else {
			return "智慧校企";
		}
	}

	public static boolean getStartNew() {
		return startNew;
	}

	public static String getApptype() {
		return apptype;
	}

	public void setApptype(String apptype) {
		WebConfig.apptype = apptype;
	}

	public static String getAppversion() {
		return appversion;
	}

	public void setAppversion(String appversion) {
		WebConfig.appversion = appversion;
	}

	public static String getProcesstask() {
		return processtask;
	}

	public void setProcesstask(String processtask) {
		WebConfig.processtask = processtask;
	}

	public static String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		WebConfig.domain = domain;
	}

	public static String getOsServerAddress() {
		return osServerAddress;
	}

	public void setOsServerAddress(String osServerAddress) {
		WebConfig.osServerAddress = osServerAddress;
	}

	public static String getResourceDomain() {
		return resourceDomain;
	}

	public void setResourceDomain(String resourceDomain) {
		WebConfig.resourceDomain = resourceDomain;
	}

	public static String getBindmodel() {
		return bindmodel;
	}

	public void setBindmodel(String bindmodel) {
		WebConfig.bindmodel = bindmodel;
	}

	
	public static String getWorkbindmodel() {
		return workbindmodel;
	}

	public void setWorkbindmodel(String workbindmodel) {
		WebConfig.workbindmodel = workbindmodel;
	}

	public static String getIsyunouth() {
		return isyunouth;
	}

	public void setIsyunouth(String isyunouth) {
		WebConfig.isyunouth = isyunouth;
	}

	public static String getIsyuntoken() {
		return isyuntoken;
	}

	public static String getIsWorkYunToken() {
		return isWorkYunToken;
	}

	public void setIsWorkYunToken(String isWorkYunToken) {
		WebConfig.isWorkYunToken = isWorkYunToken;
	}

	public void setIsyuntoken(String isyuntoken) {
		WebConfig.isyuntoken = isyuntoken;
	}

	public static String getWeixinAppid() {
		return weixinAppid;
	}

	public void setWeixinAppid(String weixinAppid) {
		WebConfig.weixinAppid = weixinAppid;
	}

	public static String getWeixinAppSecret() {
		return weixinAppSecret;
	}

	public void setWeixinAppSecret(String weixinAppSecret) {
		WebConfig.weixinAppSecret = weixinAppSecret;
	}

	public static String getWeixinToken() {
		return weixinToken;
	}

	public void setWeixinToken(String weixinToken) {
		WebConfig.weixinToken = weixinToken;
	}

	public static String getWeixinEncodingAESKey() {
		return weixinEncodingAESKey;
	}

	public void setWeixinEncodingAESKey(String weixinEncodingAESKey) {
		WebConfig.weixinEncodingAESKey = weixinEncodingAESKey;
	}

	
	public static String getWeixinMchid() {
		return weixinMchid;
	}

	public void setWeixinMchid(String weixinMchid) {
		WebConfig.weixinMchid = weixinMchid;
	}

	public static String getWeixinMchKey() {
		return weixinMchKey;
	}

	public void setWeixinMchKey(String weixinMchKey) {
		WebConfig.weixinMchKey = weixinMchKey;
	}

	public static String getDebug() {
		return debug;
	}

	public void setDebug(String debug) {
		WebConfig.debug = debug;
	}

	public static String getDebugid() {
		return debugid;
	}

	public void setDebugid(String debugid) {
		WebConfig.debugid = debugid;
	}

	public static String getCampusid() {
		return campusid;
	}

	public void setCampusid(String campusid) {
		WebConfig.campusid = campusid;
	}

	public static String getWxpayid() {
		return wxpayid;
	}

	public void setWxpayid(String wxpayid) {
		WebConfig.wxpayid = wxpayid;
	}

	public static boolean isStartNew() {
		return startNew;
	}

	public void setStartNew(boolean startNew) {
		WebConfig.startNew = startNew;
	}

	public static String getCampusappsecret() {
		return campusappsecret;
	}

	public void setCampusappsecret(String campusappsecret) {
		WebConfig.campusappsecret = campusappsecret;
	}




	public static String getWeixinWorkCorpid() {
		return weixinWorkCorpid;
	}

	public void setWeixinWorkCorpid(String weixinWorkCorpid) {
		WebConfig.weixinWorkCorpid = weixinWorkCorpid;
	}

	public static String getWeixinWorkAgentId() {
		return weixinWorkAgentId;
	}

	public void setWeixinWorkAgentId(String weixinWorkAgentId) {
		WebConfig.weixinWorkAgentId = weixinWorkAgentId;
	}

	public static String getWeixinWorkSecret() {
		return weixinWorkSecret;
	}

	public void setWeixinWorkSecret(String weixinWorkSecret) {
		WebConfig.weixinWorkSecret = weixinWorkSecret;
	}

	public static String getWeixinWorkDebug() {
		return weixinWorkDebug;
	}

	public void setWeixinWorkDebug(String weixinWorkDebug) {
		WebConfig.weixinWorkDebug = weixinWorkDebug;
	}

	public static String getWeixinWorkDebugid() {
		return weixinWorkDebugid;
	}

	public void setWeixinWorkDebugid(String weixinWorkDebugid) {
		WebConfig.weixinWorkDebugid = weixinWorkDebugid;
	}

	
	public static String getNodeid() {
		return nodeid;
	}

	public void setNodeid(String nodeid) {
		WebConfig.nodeid = nodeid;
	}

	public static String getCampusCPDomain() {
		return campusCPDomain;
	}

	public void setCampusCPDomain(String campusCPDomain) {
		WebConfig.campusCPDomain = campusCPDomain;
	}

	public static String getCampusCPAppId() {
		return campusCPAppId;
	}

	public void setCampusCPAppId(String campusCPAppId) {
		WebConfig.campusCPAppId = campusCPAppId;
	}

	public static String getCampusCPAppSecret() {
		return campusCPAppSecret;
	}

	public void setCampusCPAppSecret(String campusCPAppSecret) {
		WebConfig.campusCPAppSecret = campusCPAppSecret;
	}

	public static boolean EnableCampusCPService() {
		if(!StringUtils.isBlank(WebConfig.getNodeid()) && !StringUtils.isBlank(WebConfig.getCampusCPDomain()) && !StringUtils.isBlank(WebConfig.getCampusCPAppId()) && !StringUtils.isBlank(WebConfig.getCampusCPAppSecret())) {
			return true;
		}
		return false;
	}

	public static String thisPageUrl(HttpServletRequest request, boolean absolute) {
		String url = "";
		if (absolute) {
			url = getDomain();
		}
		url = String.format("%s%s", url, request.getContextPath() + request.getServletPath());
		String querystr = request.getQueryString();
		if (!StringUtils.isBlank(querystr)) {
			url += "?" + querystr;
		}
		return url;
	}
	public void setIotServerAddress(String iotServerAddress) {
		WebConfig.iotServerAddress = iotServerAddress;
	}

	public static String getIotServerAddress() {
		return iotServerAddress;
	}

}
