package com.ymiots.campusmp.entity.dto.visitor;

import lombok.Data;

import java.util.Date;

@Data
public class VisitorRecordDto {

    /**
     * 申请人id
     */
    private String uid;

    /**
     * 申请人
     */
    private String visitorName;

    /**
     * 来访时间
     */
    private Date visitTime;

    /**
     * 离场时间
     */
    private Date leaveTime;

    /**
     * 创建时间
     */
    private Date createDate;


}
