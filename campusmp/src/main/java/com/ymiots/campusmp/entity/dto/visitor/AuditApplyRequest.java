package com.ymiots.campusmp.entity.dto.visitor;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-22 12:06:39
 */
@Data
public class AuditApplyRequest {
    private String uid;
    private Integer status;
    private double day;
    private String remark;
    private String areaPosition;
    private String globalPassword;
    private Integer globalUsageCount = 1;
    private List<DeviceSetting> devices; // 这里是List

    @Data
    public static class DeviceSetting {
        private String deviceId;
        private String type;
        private String doorRight;
        private String floors;
    }
}
