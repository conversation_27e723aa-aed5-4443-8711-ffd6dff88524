package com.ymiots.campusmp.entity.charge.dto;

import com.ymiots.campusmp.model.dto.http.os.YmBaseReq;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 富友 回复消息公共对象
 */
@Data
@SuperBuilder
@NoArgsConstructor
public abstract class FuiouBaseRsp{

    /**
     * 错误代码, 000000 成功,其他详细参见错误列表
     */
    protected String resultCode;

    protected String resultMsg;

    /**
     * 机构号,接入机构在富友的唯一代码
     */
    protected String insCode;

    protected String mchntCode;

    /**
     * 签名
     */
    protected String sign;
}
