package com.ymiots.campusmp.entity;

import com.ymiots.campusmp.controller.workflow.AuditHandler;
import com.ymiots.campusmp.entity.dto.WorkFlowNodeDto;
import com.ymiots.campusmp.entity.dto.visitor.AuditApplyRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalContext {
    private String uid;
    private Integer status;
    private String remark;
    private String table;
    private String areaPosition;
    private String userId;
    private String name;
    private String orgCodeSQL;
    private WorkFlowNodeDto nodeLevel;
    private int nextNodeLevel;
    private String areaId;
    private List<WorkFlowNodeDto> dtoList;
    private AuditHandler auditHandler;
    private int isDefaultAudit;
    private String levelDay;
    private String applyDay;
    private int timeStatus;
    // 新增参数
    private AuditApplyRequest requestBody;
}