package com.ymiots.campusmp.utils.weixin;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.utils.weixin.entity.UnifiedOrderResult;
import com.ymiots.campusmp.utils.weixin.entity.WeiXinPayNotify;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Service
public class WeiXinPay {

	@Autowired
    WeiXinJsSDK wxjssdk;
	
	/**
	 * 统一下单地址
	 */
	private String WEIXIN_UNIFIEDORDER="https://api.mch.weixin.qq.com/pay/unifiedorder";
	
	
	public UnifiedOrderResult UnifiedOrder(String data) throws Exception{
		JsonResult result= WeiXinRequest.HttpPost(WEIXIN_UNIFIEDORDER, data);
		if(result.isSuccess()) {
			String xml=result.getMsg();
			JSONObject xmljson= XmlToJSONObject.ToJSONObject(xml);
			UnifiedOrderResult OrderResukt=JSONObject.parseObject(xmljson.toJSONString(), UnifiedOrderResult.class);
			return OrderResukt;
		}else {
			Log.error(WeiXinPay.class, result.getMsg());
			throw new Exception(result.getMsg());
		}
	}
	
	/**
	 *  获取支付通知结果
	 * @param request
	 * @return
	 * @throws Exception
	 */
	public WeiXinPayNotify GetWeiXinPayNotify(HttpServletRequest request, String WeixinMchKey) throws Exception{
		String data=HttpRequest.GetNoKeyData(request);
		Log.info(WeiXinPay.class, data);
		if(StringUtils.isBlank(data)) {
			throw new Exception("支付结果通知接口未接收到任何数据");
		}
		JSONObject xmljson= XmlToJSONObject.ToJSONObject(data);
		Map<String,String> paymap=new HashMap<String,String>();
		for(String key : xmljson.keySet()) {
			if(!key.equals("sign")) {
				paymap.put(key, xmljson.getString(key));
			}
		}
		String stringA=wxjssdk.MapToUrlString(paymap, false, false);
		String stringSignTemp=String.format("%s&key=%s", stringA, WeixinMchKey);
		String paySign=MD5.MD5Encode(stringSignTemp).toUpperCase();
		
		String sign=xmljson.getString("sign");
		if(paySign.equals(sign)) {
			//对数据签名验证
			Log.info(WeiXinPay.class, "签名验证通过");
			WeiXinPayNotify resukt=JSONObject.parseObject(xmljson.toJSONString(), WeiXinPayNotify.class);
			return resukt;
		}
		return null;
	}
}
