package com.ymiots.campusmp.utils.weixin.entity;

import com.alibaba.fastjson.JSONArray;

public class OAuthUserInfo extends WeiXinResult {

	public String openid;
	public String nickname;
	public String sex;
	public String province;
	public String city;
	public String country;
	public String headimgurl;
	public JSONArray privilege;
	public String unionid;

	/**
	 * 用户的唯一标识
	 * 
	 * @return
	 */
	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	/**
	 * 用户昵称
	 * 
	 * @return
	 */
	public String getNickname() {
		return nickname;
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

	/**
	 * 用户的性别，值为1时是男性，值为2时是女性，值为0时是未知
	 * 
	 * @return
	 */
	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	/**
	 * 用户个人资料填写的省份
	 * 
	 * @return
	 */
	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	/**
	 * 普通用户个人资料填写的城市
	 * 
	 * @return
	 */
	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	/**
	 * 国家，如中国为CN
	 * 
	 * @return
	 */
	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	/**
	 * 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效
	 * 
	 * @return
	 */
	public String getHeadimgurl() {
		return headimgurl;
	}

	public void setHeadimgurl(String headimgurl) {
		this.headimgurl = headimgurl;
	}

	/**
	 * 用户特权信息，json 数组，如微信沃卡用户为（chinaunicom）
	 * 
	 * @return
	 */
	public JSONArray getPrivilege() {
		return privilege;
	}

	public void setPrivilege(JSONArray privilege) {
		this.privilege = privilege;
	}

	/**
	 * 只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。
	 * 
	 * @return
	 */
	public String getUnionid() {
		return unionid;
	}

	public void setUnionid(String unionid) {
		this.unionid = unionid;
	}

}
