package com.ymiots.campusmp.utils.weixin;

import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.ByteArrayBuffer;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WeiXinRequest {

	public static JsonResult HttpGet(String url) {
		try {
			CloseableHttpClient httpclient = HttpClients.createDefault();
			HttpPut httpGet = new HttpPut(url);	
			CloseableHttpResponse response1 = httpclient.execute(httpGet);
			try {
				int statuscode = response1.getStatusLine().getStatusCode();
				if (statuscode == 200) {
					HttpEntity entity = response1.getEntity();
					if (entity != null) {
						InputStream stream = entity.getContent();
						ByteArrayBuffer buffer = new ByteArrayBuffer(0);
						int result = -1;
						while ((result = stream.read()) != -1) {
							buffer.append(result);
						}
						byte[] bytes = buffer.toByteArray();
						String strresult = new String(bytes, "UTF-8");
						return Json.getJsonResult(true,strresult);
					} else {
						return Json.getJsonResult("response entity is null");
					}
				} else {
					return Json.getJsonResult("service status is "+String.valueOf(statuscode));
				}
			} finally {
				response1.close();
				httpclient.close();
			}
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}


	
	public static JsonResult HttpPost(String url) {
		Map<String, String> params=new HashMap<String, String>();
		return HttpPost(url,params);
	}

	public static JsonResult HttpPost(String url, Map<String, String> params) {
		try {
			CloseableHttpClient httpclient = HttpClients.createDefault();
			HttpPost httpPost = new HttpPost(url);
			List<NameValuePair> nvps = new ArrayList<NameValuePair>();
			for (Map.Entry<String, String> param : params.entrySet()) {
				nvps.add(new BasicNameValuePair(param.getKey(), param.getValue()));
			}
			httpPost.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));
			CloseableHttpResponse response2 = httpclient.execute(httpPost);
			try {
				int statuscode = response2.getStatusLine().getStatusCode();
				if (statuscode == 200) {
					HttpEntity entity = response2.getEntity();
					if (entity != null) {
						InputStream stream = entity.getContent();
						ByteArrayBuffer buffer = new ByteArrayBuffer(0);
						int result = -1;
						while ((result = stream.read()) != -1) {
							buffer.append(result);
						}
						byte[] bytes = buffer.toByteArray();
						String strresult = new String(bytes, "UTF-8");
						return Json.getJsonResult(true,strresult);
					} else {
						return Json.getJsonResult("response entity is null");
					}
				} else {
					return Json.getJsonResult("service status is "+String.valueOf(statuscode));
				}
			} finally {
				response2.close();
				httpclient.close();
			}
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}


	public static JsonResult HttpPost(String url, String JsonParams) {
		try {
			CloseableHttpClient httpclient = HttpClients.createDefault();
			HttpPost httpPost = new HttpPost(url);
			StringEntity paramsEntity = new StringEntity(JsonParams,"UTF-8");
			paramsEntity.setContentEncoding("UTF-8");
			paramsEntity.setContentType("application/json");
			httpPost.setEntity(paramsEntity);
			CloseableHttpResponse response2 = httpclient.execute(httpPost);
			try {
				int statuscode = response2.getStatusLine().getStatusCode();
				if (statuscode == 200) {
					HttpEntity entity = response2.getEntity();
					if (entity != null) {
						InputStream stream = entity.getContent();
						ByteArrayBuffer buffer = new ByteArrayBuffer(0);
						int result = -1;
						while ((result = stream.read()) != -1) {
							buffer.append(result);
						}
						byte[] bytes = buffer.toByteArray();
						String strresult = new String(bytes, "UTF-8");
						return Json.getJsonResult(true,strresult);
					} else {
						return Json.getJsonResult("response entity is null");
					}
				} else {
					return Json.getJsonResult("service status is "+String.valueOf(statuscode));
				}
			} finally {
				response2.close();
				httpclient.close();
			}
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}

}
