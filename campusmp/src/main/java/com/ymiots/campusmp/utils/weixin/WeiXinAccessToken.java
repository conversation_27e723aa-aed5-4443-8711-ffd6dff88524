package com.ymiots.campusmp.utils.weixin;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.utils.weixin.entity.AccessToken;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class WeiXinAccessToken {

	private String ACCESS_TOKEN = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
	private String WORK_ACCESS_TOKEN = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s";

	public AccessToken GetAccessToken(String appid, String secret) throws Exception {
		if(StringUtils.isBlank(appid) || StringUtils.isBlank(secret)) {
			return null;
		}
		String url = String.format(ACCESS_TOKEN, appid, secret);
		JsonResult result= WeiXinRequest.HttpPost(url);
		if(!result.isSuccess()) {
			Log.error(WeiXinAccessToken.class, "获取微信accesstoken错误："+result.getMsg());
			return null;
		}
		AccessToken accessToken=JSONObject.parseObject(result.getMsg(), AccessToken.class);
		return accessToken;
	}
	
	
	public AccessToken GetWorkAccessToken(String corpid, String corpsecret) throws Exception {
		if(StringUtils.isBlank(corpid) || StringUtils.isBlank(corpsecret)) {
			return null;
		}
		String url = String.format(WORK_ACCESS_TOKEN, corpid, corpsecret);
		JsonResult result= WeiXinRequest.HttpPost(url);
		if(!result.isSuccess()) {
			Log.error(WeiXinAccessToken.class, "获取企业微信accesstoken错误："+result.getMsg());
			return null;
		}
		AccessToken accessToken=JSONObject.parseObject(result.getMsg(), AccessToken.class);
		return accessToken;
	}
}
