package com.ymiots.campusmp.utils.weixin;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.config.redis.RedisClient;
import com.ymiots.campusmp.utils.weixin.entity.JSapiTicket;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.common.RandomHelper;
import com.ymiots.framework.common.SHA1Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class WeiXinJsSDK {

	private String JSAPI_TICKET = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi";

	public String JSAPI_TICKET_REDISKEY="campusmp_weixin_jsapi_ticket";

	@Autowired
    RedisClient redisclient;

	/**
	 *  JS接口的临时票据
	 * @param access_token
	 * @return
	 * @throws Exception
	 */
	public String GetJSapiTicket(String access_token) throws Exception {
//        Log.info(this.getClass(),redisclient.hasStringKey(JSAPI_TICKET_REDISKEY));
		if(redisclient.hasStringKey(JSAPI_TICKET_REDISKEY)) {
			return redisclient.getString(JSAPI_TICKET_REDISKEY);
		}else {
			String url = String.format(JSAPI_TICKET, access_token);
			JsonResult result= WeiXinRequest.HttpPost(url);
//			Log.info(this.getClass(), result);
            if (!result.isSuccess()) {
                throw new Exception(result.getMsg());
            } else {
                JSONObject data = JSONObject.parseObject(result.getMsg());
                String errcode = data.getString("errcode");
                if ("0".equals(errcode)) {
                    JSapiTicket JsApiTicket = JSONObject.parseObject(result.getMsg(), JSapiTicket.class);
                    String jsapiticket = JsApiTicket.getTicket();
                    redisclient.setString(JSAPI_TICKET_REDISKEY, jsapiticket, JsApiTicket.getExpires_in(), TimeUnit.SECONDS);
                    return jsapiticket;
                } else {
                    Log.error(this.getClass(), data.getString("errmsg"));
                }
            }
            return "";
		}
	}

	/**
	 * 获取随机字符串
	 * @return
	 */
	public String GetNoncestr(){
		String noncestr=RandomHelper.generateString(16);
		return noncestr;
	}

	/**
	 *  获取时间戳
	 * @return
	 */
	public long GetTimeStamp(){
		return System.currentTimeMillis() / 1000;
	}

	/**
	 *  获取签名字符串
	 * @param noncestr  随机字符串
	 * @param jsapi_ticket JS接口的临时票据
	 * @param timestamp 时间戳
	 * @param url 当前网页的URL，不包含#及其后面部分
	 * @param key
	 * @return
	 * @throws Exception
	 */
	public String MakeSignature(String noncestr,String jsapi_ticket, long timestamp, String url) throws Exception{
		if(!StringUtils.isBlank(url)) {
			if(url.indexOf("#")!=-1) {
				url=url.substring(0,url.indexOf("#"));
			}
		}else {
			url="";
		}
		String parameters=String.format("jsapi_ticket=%s&noncestr=%s&timestamp=%s&url=%s", jsapi_ticket,noncestr,timestamp,url);
	    String signature=SHA1Utils.getSHA1(parameters);
	    return signature;
	}

	public String MapToXmlString(Map<String, String> map) {
		StringBuilder buf = new StringBuilder();
		buf.append("<xml>");
		 List<Map.Entry<String, String>> infoIds = new ArrayList<Map.Entry<String, String>>(map.entrySet());
        for (Map.Entry<String, String> item : infoIds)
        {
        	buf.append(String.format("<%s>%s</%s>", item.getKey(),item.getValue(),item.getKey()));
        }
        buf.append("</xml> ");
        return buf.toString();
	}

	public String MapToXmlString(Map<String, String> map, boolean CDATA) {
		StringBuilder buf = new StringBuilder();
		buf.append("<xml>");
		 List<Map.Entry<String, String>> infoIds = new ArrayList<Map.Entry<String, String>>(map.entrySet());
        for (Map.Entry<String, String> item : infoIds)
        {
        	buf.append(String.format("<%s><![CDATA[%s]]></%s>", item.getKey(),item.getValue(),item.getKey()));
        }
        buf.append("</xml> ");
        return buf.toString();
	}

	/**
     *
     * 方法用途: 对所有传入参数按照字段名的 ASCII 码从小到大排序（字典序），并且生成url参数串
     * 实现步骤:
     * @param paraMap   要排序的Map对象
     * @param urlEncode   是否需要URLENCODE
     * @param keyToLower    是否需要将Key转换为全小写  true:key转化成小写，false:不转化
     * @return
     */
    public String MapToUrlString(Map<String, String> paraMap, boolean urlEncode, boolean keyToLower){
        String buff = "";
        Map<String, String> tmpMap = paraMap;
        try
        {
            List<Map.Entry<String, String>> infoIds = new ArrayList<Map.Entry<String, String>>(tmpMap.entrySet());
            // 对所有传入参数按照字段名的 ASCII 码从小到大排序（字典序）
            Collections.sort(infoIds, new Comparator<Map.Entry<String, String>>()
            {
                @Override
                public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2)
                {
                    return (o1.getKey()).toString().compareTo(o2.getKey());
                }
            });
            // 构造URL 键值对的格式
            StringBuilder buf = new StringBuilder();
            for (Map.Entry<String, String> item : infoIds){
                if (StringUtils.isNotBlank(item.getKey()) && StringUtils.isNotBlank(item.getValue())){
                    String key = item.getKey();
                    String val = item.getValue();
                    if (urlEncode)
                    {
                        val = URLEncoder.encode(val, "utf-8");
                    }
                    if (keyToLower)
                    {
                        buf.append(key.toLowerCase() + "=" + val);
                    } else
                    {
                        buf.append(key + "=" + val);
                    }
                    buf.append("&");
                }
            }
            buff = buf.toString();
            if (buff.isEmpty() == false)
            {
                buff = buff.substring(0, buff.length() - 1);
            }
        } catch (Exception e)
        {
           return null;
        }
        return buff;
    }
}
