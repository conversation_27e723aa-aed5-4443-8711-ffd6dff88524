package com.ymiots.campusmp.utils.weixin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessage;
import com.ymiots.campusmp.utils.weixin.entity.TemplateMessageData;
import com.ymiots.campusmp.utils.weixin.entity.WeiXinResult;
import com.ymiots.framework.common.JsonResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class WeiXinTemplateMessage {

	private String TEMPLATE_MESSAGE ="https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s";
	
	public List<String> GetTemplateKeys(String template) {
		List<String> list=new ArrayList<String>();
		String regex="\\{\\{(.*)\\.DATA\\}\\}";
		Pattern r = Pattern.compile(regex);
		Matcher m = r.matcher(template);
	    while (m.find()) {
	    	String mvalue=m.group();
	    	String key=mvalue.substring(2,mvalue.indexOf("."));
	    	list.add(key);
        }
		return list;
	}
	
	
	public Map<String, TemplateMessageData> ConvertToTemplateMsgData(JSONArray cfg, JSONObject data) {
		Map<String, TemplateMessageData> msgdata=new HashMap<String, TemplateMessageData>();
		for(int i=0;i<cfg.size();i++) {
			JSONObject item=cfg.getJSONObject(i);
			TemplateMessageData md=new TemplateMessageData();
			String value=item.getString("value");
			if(StringUtils.isBlank(value)) {
				if(StringUtils.isBlank(value)) {
					value=item.getString("value");
				}
			}
			md.setValue(value);
			String color=item.getString("color");
			if(StringUtils.isBlank(color)) {
				color="#173177";
			}
			md.setColor(color);
			msgdata.put(item.getString("key"), md);
        }
		return msgdata;
	}
	
	/**
	 *  发送微信模板消息
	 * @param msg
	 * @param accessToken
	 * @return
	 * @throws Exception
	 */
	public WeiXinResult SendTemplateMessage(TemplateMessage msg, String accessToken) throws Exception {
		String url = String.format(TEMPLATE_MESSAGE, accessToken);
		String jsonparame=JSONObject.toJSONString(msg);
		JsonResult result= WeiXinRequest.HttpPost(url,jsonparame);
		if(!result.isSuccess()) {
			throw new Exception(result.getMsg());
		}
		WeiXinResult wxresult=JSONObject.parseObject(result.getMsg(), WeiXinResult.class);
		return wxresult;
	}
	
	/**
	 *  转换为模板消息内容字符串
	 * @param template
	 * @param msgdata
	 * @return
	 */
	public String TemplateToMessage(String template, Map<String, TemplateMessageData> msgdata){
		for(String key : msgdata.keySet()) {
			template=template.replace(String.format("{{%s.DATA}}", key), msgdata.get(key).getValue());
		}
		return template;
	}
}
