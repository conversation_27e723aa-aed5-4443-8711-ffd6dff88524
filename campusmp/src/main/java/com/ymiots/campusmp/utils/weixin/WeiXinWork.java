package com.ymiots.campusmp.utils.weixin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.framework.common.HttpRequest;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.springframework.stereotype.Repository;


/**
 * 企业微信接口
 * <AUTHOR>
 *
 */
@Repository
public class WeiXinWork {

	private String DEPARTMENTURL="https://qyapi.weixin.qq.com/cgi-bin/department/list?access_token=%s&id=%s";

	private String MESSAGEURL="https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s";

	private final String DEPARTMENT_DETAIL_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token=ACCESS_TOKEN&department_id=DEPARTMENT_ID";

	private final String DEPARTMENT_DETAIL_ID = "https://qyapi.weixin.qq.com/cgi-bin/department/get?access_token=ACCESS_TOKEN&id=ID";

	public JSONArray GetWeiXinWorkDepartmentData(String workaccesstoken, String departid) {
		DEPARTMENTURL=String.format(DEPARTMENTURL, workaccesstoken,departid);
		JsonResult result=HttpRequest.HttpGet(DEPARTMENTURL);
		if(!result.isSuccess()) {
			Log.error(this.getClass(), "获取企业微信部门信息失败："+result.getMsg());
			return new JSONArray();
		}
		JSONObject data=result.getData();
		if(data==null) {
			Log.error(this.getClass(), "获取企业微信部门信息失败：data is null");
			return new JSONArray();
		}
		if(data.getString("errmsg").contains("ok")) {
			JSONArray list=data.getJSONArray("department");
			return list;
		}else {
			Log.error(this.getClass(), String.format("获取企业微信部门信息失败：错误编号%s，错误信息%s", data.getString("errcode"),data.getString("errmsg")));
			return new JSONArray();
		}
	}

	public JSONArray GetWeiXinWorkDepartmentUserData(String workAccessToken, String departId) {
		String url = DEPARTMENT_DETAIL_URL.replace("ACCESS_TOKEN", workAccessToken).replace("DEPARTMENT_ID", departId);
		JsonResult result=HttpRequest.HttpGet(url);
		if(!result.isSuccess()) {
			Log.error(this.getClass(), "获取企业微信部门员工失败："+result.getMsg());
			return new JSONArray();
		}
		JSONObject data=result.getData();
		if(data==null) {
			Log.error(this.getClass(), "获取企业微信部门员工失败：data is null");
			return new JSONArray();
		}
		if(data.getString("errmsg").equals("ok")) {
			JSONArray list=data.getJSONArray("userlist");
			return list;
		}else {
			Log.error(this.getClass(), String.format("获取企业微信部门员工失败：错误编号%s，错误信息%s", data.getString("errcode"),data.getString("errmsg")));
			return new JSONArray();
		}
	}

	/**
	 * 根据用户id获取个人信息
	 */
	public JSONObject GetWeiXinWorkUserData(String workaccesstoken, String userid) {
		String url="https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=%s";
		url=String.format(url, workaccesstoken);
		url+=String.format("&userid=%s", userid);		
		JsonResult result=HttpRequest.HttpGet(url);
		if(!result.isSuccess()) {
			Log.error(this.getClass(), "获取企业微信员工失败："+result.getMsg());
			return null;
		}
		JSONObject data=result.getData();
		if(data==null) {
			Log.error(this.getClass(), "获取企业微信员工失败：data is null");
			return null;
		}
		if(data.getString("errmsg").equals("ok")) {
			return data;
		}else {
			Log.error(this.getClass(), String.format("获取企业微信员工失败：错误编号%s，错误信息%s", data.getString("errcode"),data.getString("errmsg")));
			return null;
		}
	}

	/**
	 * 根据用户id获取个人信息
	 */
	public JSONObject getWeiXinWorkDepartmentData(String workAccessToken, String id) {
		String url = DEPARTMENT_DETAIL_ID.replace("ACCESS_TOKEN", workAccessToken).replace("ID", id);
		JsonResult result=HttpRequest.HttpGet(url);
		if(!result.isSuccess()) {
			Log.error(this.getClass(), "获取企业微信部门失败："+result.getMsg());
			return null;
		}
		JSONObject data=result.getData();
		if(data==null) {
			Log.error(this.getClass(), "获取企业微信部门失败：data is null");
			return null;
		}
		if(data.getString("errmsg").equals("ok")) {
			return data;
		}else {
			Log.error(this.getClass(), String.format("获取企业微信部门失败：错误编号%s，错误信息%s", data.getString("errcode"),data.getString("errmsg")));
			return null;
		}
	}

	public JsonResult SendWeiXinWorkMessage(String workaccesstoken, JSONObject msg) {
		MESSAGEURL=String.format(MESSAGEURL, workaccesstoken);
		JsonResult result=HttpRequest.HttpPostData(MESSAGEURL,msg.toJSONString());
		if(!result.isSuccess()) {
			Log.error(this.getClass(), "企业微信消息推送接口访问失败："+result.getMsg());
			return Json.getJsonResult(result.getMsg());
		}
		JSONObject data=result.getData();
		if(data==null) {
			Log.error(this.getClass(), "企业微信消息推送接口访问失败：data is null");
			return Json.getJsonResult("企业微信消息推送接口访问失败：data is null");
		}
		if(data.getString("errmsg").equals("ok")) {
			Log.info(this.getClass(), "企业微信消息推送结果:"+data.toJSONString());
			return Json.getJsonResult(true);
		}else {
			Log.error(this.getClass(), String.format("企业微信消息推送失败:错误编号%s，错误信息%s", data.getString("errcode"), data.getString("errmsg")));
			return Json.getJsonResult("企业微信消息推送失败:"+data.getString("errmsg"));
		}
	}
	
}
