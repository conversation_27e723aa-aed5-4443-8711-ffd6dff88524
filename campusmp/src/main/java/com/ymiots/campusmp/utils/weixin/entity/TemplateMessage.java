package com.ymiots.campusmp.utils.weixin.entity;

import java.util.Map;

public class TemplateMessage{
	
	private String touser;
	
	private String template_id;
	
	private String url;
	
	private MiniProgram miniprogram;
	
	private Map<String, TemplateMessageData> data;

	public String getTouser() {
		return touser;
	}

	public void setTouser(String touser) {
		this.touser = touser;
	}

	public String getTemplate_id() {
		return template_id;
	}

	public void setTemplate_id(String template_id) {
		this.template_id = template_id;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public MiniProgram getMiniprogram() {
		return miniprogram;
	}

	public void setMiniprogram(MiniProgram miniprogram) {
		this.miniprogram = miniprogram;
	}

	public Map<String, TemplateMessageData> getData() {
		return data;
	}

	public void setData(Map<String, TemplateMessageData> data) {
		this.data = data;
	}
}
