package com.ymiots.campusmp.utils.weixin.entity;

public class AccessToken extends WeiXinResult {

	private String access_token;
	
	private int expires_in;

	/**
	 *  获取到的凭证
	 * @return
	 */
	public String getAccess_token() {
		return access_token;
	}

	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}

	/**
	 *  凭证有效时间，单位：秒
	 * @return
	 */
	public int getExpires_in() {
		return expires_in;
	}

	public void setExpires_in(int expires_in) {
		this.expires_in = expires_in;
	}
	
	
}
