package com.ymiots.campusmp.utils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;

public class IPUtils {
    private static final List<String> PRIVATE_NETWORKS = Arrays.asList(
            "10.0.0.0/8",
            "**********/12",
            "***********/16"
    );

    public static boolean isInternalIP(String ip) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            byte[] addressBytes = inetAddress.getAddress();
            for (String network : PRIVATE_NETWORKS) {
                if (isInRange(addressBytes, network)) {
                    return true;
                }
            }
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return false;
    }

    private static boolean isInRange(byte[] addressBytes, String network) {
        String[] parts = network.split("/");
        String ipPart = parts[0];
        int prefixLength = Integer.parseInt(parts[1]);

        InetAddress inetAddress;
        try {
            inetAddress = InetAddress.getByName(ipPart);
        } catch (UnknownHostException e) {
            e.printStackTrace();
            return false;
        }

        byte[] networkBytes = inetAddress.getAddress();

        int byteCount = prefixLength / 8;
        int bitCount = prefixLength % 8;

        for (int i = 0; i < byteCount; i++) {
            if (addressBytes[i] != networkBytes[i]) {
                return false;
            }
        }

        int mask = 0xFF00 >> bitCount;
        return (addressBytes[byteCount] & mask) == (networkBytes[byteCount] & mask);
    }
}
