package com.ymiots.campusmp.utils.weixin.entity;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

public class TemplateMessageData {
	
	@J<PERSON><PERSON>ield  (name = "value")
	private String value;

	@J<PERSON><PERSON>ield  (name = "color")
	private String color;

	public TemplateMessageData() {
		
	}
	
	public TemplateMessageData(String v, String c) {
		this.setValue(v);
		this.setColor(c);
	}

	public TemplateMessageData(String v) {
		this.setValue(v);
	}
	
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getColor() {
		return color;
	}

	public void setColor(String color) {
		this.color = color;
	}

	public String toJSONString() {
		JSONObject item=new JSONObject();
		item.put("value", this.getValue());
		item.put("color", this.getColor());
		return item.toJSONString();
	}
}
