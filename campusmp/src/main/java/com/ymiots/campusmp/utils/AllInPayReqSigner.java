package com.ymiots.campusmp.utils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ymiots.campusmp.annotation.AllInPayReqField;
import com.ymiots.campusmp.config.SignConfig;
import com.ymiots.campusmp.constant.AllInPayUrl;
import com.ymiots.campusmp.constant.SybConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Component
public class AllInPayReqSigner {

    @Autowired
    private OkHttpUtil okHttpUtil;

    private static final ObjectMapper objectMapper = new ObjectMapper();
    // 示例方法
    public <T> T postWithSign(AllInPayUrl url, Object req, Class<T> responseClass) throws Exception {
        // 签名
        // 1. 获取所有带有@AllInPayReqField注解且signed为true的字段
        TreeMap<String, String> filteredParams = new TreeMap<String, String>();
        // 1. 获取所有带有@AllInPayReqField注解且signed为true的字段
        Field[] fields = getAllFields(req.getClass());
        for (Field field : fields) {
            if (field.isAnnotationPresent(AllInPayReqField.class)) {
                AllInPayReqField annotation = field.getAnnotation(AllInPayReqField.class);
                if (annotation.signed()) {
                    field.setAccessible(true);
                    Object value = field.get(req);
                    if (!ObjectUtils.isEmpty(value)) {
                        String fieldName = annotation.value();
                        String fieldValue = value.toString();
                        filteredParams.put(fieldName, fieldValue);
                    }
                }
            }
        }
        // 4. 使用商户的RSA私钥进行签名
        String sign = SybUtil.unionSign(filteredParams, SignConfig.SIGN_KEY_PRIVATE, "RSA");
        filteredParams.put("sign", sign);
        String post = okHttpUtil.doPost(url.getUrl(), filteredParams);
        Map<String, String> resultMap  = handleResult(post);
        resultMap.remove("sign");
        String s = JSON.toJSONString(resultMap);
        return JSON.parseObject(s, responseClass);
    }

    public static Map<String,String> handleResult(String result) throws Exception{
        System.out.println("ret:"+result);
        Map map = SybUtil.json2Obj(result, Map.class);
        if(map == null){
            throw new Exception("返回数据错误");
        }
        if("SUCCESS".equals(map.get("retcode"))){
            TreeMap tmap = new TreeMap();
            tmap.putAll(map);
//			String appkey = SignConfig.SIGN_KEY_PUBLIC;
            String appkey = SybConstants.SYB_RSATLPUBKEY;
            if(SybUtil.validSign(tmap, appkey, "RSA")){
                System.out.println("签名成功");
                return map;
            }else{
                throw new Exception("验证签名失败");
            }

        }else{
            throw new Exception(map.get("retmsg").toString());
        }
    }
    // 获取类及其父类的所有字段
    private Field[] getAllFields(Class<?> clazz) {
        if (clazz == null) {
            return new Field[0];
        }
        Field[] fields = clazz.getDeclaredFields();
        Field[] parentFields = getAllFields(clazz.getSuperclass());
        Field[] allFields = new Field[fields.length + parentFields.length];
        System.arraycopy(fields, 0, allFields, 0, fields.length);
        System.arraycopy(parentFields, 0, allFields, fields.length, parentFields.length);
        return allFields;
    }
}
