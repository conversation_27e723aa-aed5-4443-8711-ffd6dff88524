package com.ymiots.campusmp.utils;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.model.R;
import com.ymiots.campusmp.model.dto.http.os.YmAllInCommonPlacePayReq;
import com.ymiots.campusmp.model.dto.http.os.YmBaseReq;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-04-09
 * https://www.nhooo.com/note/qah5ji.html
 */
@Slf4j
@Component
public class OkHttpUtil {
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final MediaType XML = MediaType.parse("application/xml; charset=utf-8");

    @Autowired
    private OkHttpClient okHttpClient;

    /**
     * get 请求
     *
     * @param url 请求url地址
     * @return string
     */
    public String doGet(String url) {
        return doGet(url, null, null);
    }

    /**
     * get 请求
     *
     * @param url    请求url地址
     * @param params 请求参数 map
     * @return string
     */
    public String doGet(String url, Map<String, String> params) {
        return doGet(url, params, null);
    }

    /**
     * get 请求
     *
     * @param url     请求url地址
     * @param headers 请求头字段 {k1, v1 k2, v2, ...}
     * @return string
     */
    public String doGet(String url, String[] headers) {
        return doGet(url, null, headers);
    }

    /**
     * get 请求
     *
     * @param url     请求url地址
     * @param params  请求参数 map
     * @param headers 请求头字段 {k1, v1 k2, v2, ...}
     * @return string
     */
    public String doGet(String url, Map<String, String> params, String[] headers) {
        StringBuilder sb = new StringBuilder(url);
        if (params != null && params.keySet().size() > 0) {
            boolean firstFlag = true;
            for (String key : params.keySet()) {
                if (firstFlag) {
                    sb.append("?").append(key).append("=").append(params.get(key));
                    firstFlag = false;
                } else {
                    sb.append("&").append(key).append("=").append(params.get(key));
                }
            }
        }
        Request.Builder builder = new Request.Builder();
        if (headers != null && headers.length > 0) {
            if (headers.length % 2 == 0) {
                for (int i = 0; i < headers.length; i = i + 2) {
                    builder.addHeader(headers[i], headers[i + 1]);
                }
            } else {
                log.warn("headers's length[{}] is error.", headers.length);
            }
        }
        Request request = builder.url(sb.toString()).build();
        log.info("do get request and url[{}]", sb.toString());
        return execute(request);
    }

    /**
     * post 请求
     *
     * @param params 请求参数 map
     * @return string
     */
    public String doPost(String url, Map<String, String> params) {
        FormBody.Builder builder = new FormBody.Builder();
        if (params != null && params.keySet().size() > 0) {
            for (String key : params.keySet()) {
                builder.add(key, params.get(key));
            }
        }
        Request request = new Request.Builder().url(url).post(builder.build()).build();
        log.info("do post request and url[{}]", url);
        return execute(request);
    }

    /**
     * post 请求, 请求数据为 json 的字符串
     */
    public R<?> doPostJsonR(String url, String json) {
        log.info("do post request and url[{}]", url);
        String res = exectePost(url, json, JSON);
        return JSONObject.parseObject(res, R.class);
    }

    public JSONObject doPostJson(String url, String json) {
        log.info("do post request and url[{}]", url);
        String res = exectePost(url, json, JSON);
        return JSONObject.parseObject(res);
    }

    public <T> R<T> doPost(String url, YmBaseReq req, Class<T> rspDataClass) {
        log.info("do post request and url[{}]", url);
        String rsp = exectePost(url, JSONObject.toJSONString(req), JSON);
        JSONObject res = JSONObject.parseObject(rsp);
        T data = res.getObject("data", rspDataClass);
        return R.restResult(data, res.getInteger("code"), res.getString("msg"));
    }

    public <T> R<T> doPost(String url, YmAllInCommonPlacePayReq req, Class<T> YmAllInCommonPlacePayReq) {
        log.info("do post request and url[{}]", url);
        String rsp = exectePost(url, JSONObject.toJSONString(req), JSON);
        JSONObject res = JSONObject.parseObject(rsp);
        T data = res.getObject("data", YmAllInCommonPlacePayReq);
        return R.restResult(data, res.getInteger("code"), res.getString("msg"));
    }


    /**
     * post 请求, 请求数据为 xml 的字符串
     *
     * @param url 请求url地址
     * @param xml 请求数据, xml 字符串
     * @return string
     */
    public String doPostXml(String url, String xml) {
        log.info("do post request and url[{}]", url);
        return exectePost(url, xml, XML);
    }

    private String exectePost(String url, String data, MediaType contentType) {
        RequestBody requestBody = RequestBody.create(contentType, data);
        Request request = new Request.Builder().url(url).post(requestBody).build();
        return execute(request);
    }

    private ResponseBody exectePostBody(String url, String data, MediaType contentType) {
        RequestBody requestBody = RequestBody.create(contentType, data);
        Request request = new Request.Builder().url(url).post(requestBody).build();
        return executeBody(request);
    }

    private String execute(Request request) {
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return "";
    }

    private ResponseBody executeBody(Request request) {
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body();
            }
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return null;
    }

    public <V> V postFormUrlEncoder(String url, Map<String,Object> param, Class<V> resType) throws IOException {
        String json = postFormUrlEncoder(url, param);
        return JSONObject.parseObject(json, resType);
    }

    public JSONObject postFormUrlEncoderJson(String url, Map<String, Object> param) throws IOException {
        String json = postFormUrlEncoder(url, param);
        return JSONObject.parseObject(json);
    }

    public String postFormUrlEncoder(String url, Map<String,Object> param) throws IOException {
        FormBody.Builder builder = new FormBody.Builder();
        param.forEach((k, v) -> builder.add(k, String.valueOf(v)));

        log.info("url={} \n param={}", url, JSONObject.toJSONString(param));
        Request request = new Request.Builder()
                .url(url)
                .post(builder.build())
                .build();
        Response response = okHttpClient.newCall(request).execute();
        return response.body().string();
    }

}