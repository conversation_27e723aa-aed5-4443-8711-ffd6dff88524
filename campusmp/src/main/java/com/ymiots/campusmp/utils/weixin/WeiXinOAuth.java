package com.ymiots.campusmp.utils.weixin;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusmp.utils.weixin.entity.OAuthAccessToken;
import com.ymiots.campusmp.utils.weixin.entity.OAuthUserInfo;
import com.ymiots.campusmp.utils.weixin.entity.OAuthWeiXinWorkUserInfo;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Service
public class WeiXinOAuth {

	private String WEIXIN_CODEURL = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=%s&state=%s#wechat_redirect";

	private String WORK_WEIXIN_CODEURL = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=%s&state=%s&agentid=%s#wechat_redirect";

	private String WEIXIN_ACCESSTOKEN="https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code";
	
	private String WEIXIN_USERINFO="https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN";
	
	
	private String WEIXIN_WORK_USERINFO="https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=%s&code=%s";
	
	
	/**
	 * 不弹出授权页面，直接跳转，只能获取用户openid
	 */
	public String SNSAPI_BASE = "snsapi_base";
	/**
	 * 弹出授权页面，可通过openid拿到昵称、性别、所在地。并且， 即使在未关注的情况下，只要用户授权，也能获取其信息
	 */
	public String SNSAPI_USERINFO = "snsapi_userinfo";

	/**
	 * 弹出授权页面，可通过openid拿到昵称、性别、所在地。并且， 即使在未关注的情况下，只要用户授权，也能获取其信息
	 */
	public String SNSAPI_PRIVATEINFO = "snsapi_privateinfo";

	
	/**
	 *  获取code的跳转地址
	 * @param appid 公众号的唯一标识
	 * @param redirect_uri 授权后重定向的回调链接地址
	 * @param scope 应用授权作用域
	 * @param state 重定向后会带上state参数，开发者可以填写a-zA-Z0-9的参数值，最多128字节
	 * @return
	 * @throws UnsupportedEncodingException 
	 */
	public String AuthorizeCode(String appid, String redirect_uri, String scope, String state) throws UnsupportedEncodingException {
		redirect_uri=URLEncoder.encode(redirect_uri,"UTF-8");
		String url = String.format(WEIXIN_CODEURL, appid, redirect_uri, scope, state);
		return url;
	}

	/**
	 *  企业微信获取code的跳转地址
	 * @param appid 公众号的唯一标识
	 * @param redirect_uri 授权后重定向的回调链接地址
	 * @param scope 应用授权作用域
	 * @param state 重定向后会带上state参数，开发者可以填写a-zA-Z0-9的参数值，最多128字节
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public String AuthorizeWorkCode(String appid, String redirect_uri, String scope, String state,String agentId) throws UnsupportedEncodingException {
		redirect_uri=URLEncoder.encode(redirect_uri,"UTF-8");
		String url = String.format(WORK_WEIXIN_CODEURL, appid, redirect_uri, scope, state,agentId);
		return url;
	}
	
	/**
	 *  
	 * @param appid
	 * @param redirect_uri
	 * @param scope
	 * @param state
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public OAuthAccessToken AuthorizeAccessToken(String appid, String secret, String code) throws Exception {
		String url = String.format(WEIXIN_ACCESSTOKEN, appid, secret, code);
		JsonResult result= WeiXinRequest.HttpPost(url);
		if(!result.isSuccess()) {
			Log.error(WeiXinOAuth.class, result.getMsg());
			return null;
		}
		OAuthAccessToken accessToken=JSONObject.parseObject(result.getMsg(), OAuthAccessToken.class);
		return accessToken;
	}
	
	public OAuthUserInfo AuthorizeUserInfo(String access_token, String openid) throws Exception {
		String url = String.format(WEIXIN_USERINFO, access_token, openid);
		JsonResult result= WeiXinRequest.HttpPost(url);
		if(!result.isSuccess()) {
			Log.error(WeiXinOAuth.class, result.getMsg());
			return null;
		}
		OAuthUserInfo userinfo=JSONObject.parseObject(result.getMsg(), OAuthUserInfo.class);
		return userinfo;
	}
	
	
	/**
	 * 企业微信身份信息获取
	 * @param access_token
	 * @param openid
	 * @return
	 * @throws Exception
	 */
	public OAuthWeiXinWorkUserInfo AuthorizeWeiXinWorkUserInfo(String access_token, String code) throws Exception {
		String url = String.format(WEIXIN_WORK_USERINFO, access_token, code);
		JsonResult result= WeiXinRequest.HttpGet(url);
		if(!result.isSuccess()) {
			Log.error(WeiXinOAuth.class, result.getMsg());
			return null;
		}
		OAuthWeiXinWorkUserInfo userinfo=JSONObject.parseObject(result.getMsg(), OAuthWeiXinWorkUserInfo.class);
		return userinfo;
	}
	
	
}
