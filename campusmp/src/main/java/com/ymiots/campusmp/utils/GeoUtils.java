package com.ymiots.campusmp.utils;

public class GeoUtils {
    private static final double EARTH_RADIUS = 6371.0; // 地球半径，单位：公里

    /**
     * 计算两点间的距离（单位：米）
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 返回两点间的距离（米）
     */
    public static double getDistance(double lat1, double lon1, double lat2, double lon2) {
        double radLat1 = Math.toRadians(lat1);
        double radLat2 = Math.toRadians(lat2);
        double radLon1 = Math.toRadians(lon1);
        double radLon2 = Math.toRadians(lon2);

        double dLat = radLat2 - radLat1;
        double dLon = radLon2 - radLon1;
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
                + Math.cos(radLat1) * Math.cos(radLat2)
                * Math.sin(dLon / 2) * Math.sin(dLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distance = EARTH_RADIUS * c * 1000; // 返回的是米
        return distance;
    }
}

