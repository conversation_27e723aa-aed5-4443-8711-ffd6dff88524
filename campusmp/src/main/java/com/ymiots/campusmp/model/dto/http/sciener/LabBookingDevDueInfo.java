package com.ymiots.campusmp.model.dto.http.sciener;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 实验室预约到期，下发数据DTO
 *
 * <AUTHOR>
 * @date 2023/03/07
 */
@Data
@Accessors(chain = true)
public class LabBookingDevDueInfo {

    /**
     * 实验室内的设备，包括实验室的门（通通锁）
     */
    private String devSn;

    private Integer infoIndex;

    private String cardSn;

    private List<String> formId;

//    private String password;

    private Date bookingDate;

//    private String name;

    private Date endTime;

    private Integer devModel;

}
