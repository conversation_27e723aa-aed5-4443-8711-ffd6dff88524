package com.ymiots.campusmp.model.dto.http.sciener;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 通通锁 锁记录通知
 *
 * <AUTHOR>
 * @date 2023/03/23
 */
@Data
@Accessors(chain = true)
public class LockRecordsCallback {

    /**
     * 锁id
     */
    Integer lockId;

    /**
     * 锁mac地址
     */
    String lockMac;

    /**
     * 记录列表
     */
    List<LockRecord> records;
}
