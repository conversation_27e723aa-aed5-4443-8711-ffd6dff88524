package com.ymiots.campusmp.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 会议室预约数据到期，下发数据DTO
 *
 * <AUTHOR>
 * @date 2023/03/18
 */
@Data
@Accessors(chain = true)
public class MeetingBookDevDueInfo {

    private String devSn;

    private String cardNo;

    private String cardId;

    private Integer devModel;

    private String formId;
    /**
     * 会议室时间表单uid
     */
    private String timeRecordUid;

    private String areaCode;

    private Integer status;

    private Integer infoIndex;

    private Date day;

    private String startTime;

    private String endTime;

    private Date startDate;

    private Date endDate;

}
