package com.ymiots.campusmp.model.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 通联支付订单 Mongo实体
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AllInOrder {

    /**
     * os订单号, 商户系统内部的订单号
     */
    private String mchntOrderNo;

    /**
     * os订单号, 商户系统内部的订单号
     */
    private String osTrxId;

    /**
     * 企业内部流水id
     */
    private String flId;

    /**
     * 商户订单号, 退款商户系统内部的订单号
     */
    private String osTrxIdCancel;

    /**
     * 集团/代理商商户号（共享集团号/代理商参数时必填）
     */
    private String orgId;

    /**
     * 商户号（实际交易的商户号）
     */
    private String cusId;

    /**
     * 收银宝平台的交易流水号
     */
    private String trxIdCancel;

    /**
     * 收银宝平台的交易流水号
     */
    private String trxId;

    /**
     * 交易金额（单位为分）
     */
    private Integer trxAmt;

    /**
     * 渠道平台交易单例如微信, 支付宝平台的交易单号
     */
    private String chnlTrxId;

    /**
     * 交易方式
     */
    private String payType;

    /**
     * 交易状态
     */
    private String trxStatus;
    /**
     * 订单标题（订单商品名称，为空则以商户名作为商品名称）
     */
    private String body;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 支付平台用户标识
     */
    private String acct;

    /**
     * 交易结果通知地址(公众号或者支付宝不是本平台)
     */
    private String notifyUrl;

    /**
     * 支付限制（no_credit--指定不能使用信用卡支付）
     */
    private String limitPay;

    /**
     * 微信子appid
     */
    private String subAppId;

    /**
     * 订单优惠标识
     */
    private String goodsTag;

    /**
     * 优惠信息（Benefitdetail的json字符串）
     */
    private String benefitDetail;

    /**
     * 渠道门店编号
     */
    private String chnlStoreId;

    /**
     * 门店号
     */
    private String subBranch;

    /**
     * 拓展参数（json字符串）
     */
    private String extendParams;

    /**
     * 终端ip（用户下单和调起支付的终端ip地址）
     */
    private String cusIp;

    /**
     * 支付完成跳转（必须为https协议地址，且不允许带参数）
     */
    private String frontUrl;

    /**
     * 证件号（实名交易必填.填了此字段就会验证证件号和姓名）
     */
    private String idNo;

    /**
     * 付款人真实姓名（实名交易必填.填了此字段就会验证证件号和姓名）
     */
    private String trueName;

    /**
     * 分账信息（格式: cusid:type:amount;cusid:type:amount…）
     */
    private String asInfo;

    /**
     * 分期（暂只支持支付宝花呗分期,支付宝信用卡分期）
     */
    private String fqNum;

    /**
     * 银联pid
     */
    private String unPid;

    /**
     * 金融机构号（当paytype=N03必填）
     */
    private String finOrg;

    /**
     * 终端信息（终端信息的json字符串）
     */
    private String termInfo;

    /**
     * 收银员号
     */
    private String operatorId;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
