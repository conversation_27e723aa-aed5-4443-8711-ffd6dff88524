package com.ymiots.campusmp.model.dto.http.os;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 下单支付：微信公众号支付
 */
@Data
@Accessors(chain = true)
public class YmAllInCommonPlacePayReq{

    /**
     * 支付平台用户标识
     */
    private String acct;

    /**
     * 回调地址
     */
    private String notifyUrl;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 商户号
     */
    protected String cusId;

    /**
     * 集团/代理商商户号
     */
    protected String orgId;

    /**
     * 平台分配的appId
     */
    protected String appId;


    /**
     * 平台生成的订单号
     */
    protected String uniReqSn;

    /**
     * 商品名称
     */
    protected String body;

    /**
     * 商品金额
     */
    protected Long trxAmt;

    /**
     * 微信appid
     */
    protected String subAppId;

    protected String payType;

}
