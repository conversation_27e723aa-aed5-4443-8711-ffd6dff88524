package com.ymiots.campusmp.model.dto.pay;

import com.ymiots.campusmp.annotation.AllInPayRspField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 富友 订单查询
 *
 * <AUTHOR>
 * @date 2022/11/02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AllInPayOrderQueryRsp extends AllInPayBaseRsp {

    /**
     * 交易类型
     * 交易类型
     * 非空，最大长度 8
     */
    @AllInPayRspField("trxcode")
    private String trxCode;

    /**
     * 交易金额
     * 单位为分
     * 非空，最大长度 16
     */
    @AllInPayRspField("trxamt")
    private Long trxAmt;

    /**
     * 支付平台用户标识
     * JS支付时使用, 微信支付-用户的微信openid, 支付宝支付-用户user_id
     * 可空，最大长度 32
     */
    @AllInPayRspField("acct")
    private String acct;

    /**
     * 渠道子商户号
     * 限微信/支付宝交易响应
     * 可空，最大长度 32
     */
    @AllInPayRspField("cmid")
    private String cmId;

    /**
     * 渠道号
     * 限微信交易响应
     * 可空，最大长度 32
     */
    @AllInPayRspField("chnlid")
    private String chnlId;

    /**
     * 原交易金额
     * 非空，最大长度 15
     */
    @AllInPayRspField("initamt")
    private Long initAmt;

    /**
     * 手续费
     * 单位：分
     * 可空，最大长度 15
     */
    @AllInPayRspField("fee")
    private Long fee;

    /**
     * 渠道信息
     * 目前返回云闪付/微信/支付宝的活动参数
     * 可空
     */
    @AllInPayRspField("chnldata")
    private String chnlData;

    /**
     * 借贷标识
     * 00-借记卡, 02-信用卡, 99-其他（花呗/余额等）
     * 可空，最大长度 2
     */
    @AllInPayRspField("accttype")
    private String acctType;

    /**
     * 所属银行
     * 可空
     */
    @AllInPayRspField("bankcode")
    private String bankCode;

    /**
     * 买家账号
     * 支付宝交易响应
     * 可空，最大长度 32
     */
    @AllInPayRspField("logonid")
    private String logonId;
}
