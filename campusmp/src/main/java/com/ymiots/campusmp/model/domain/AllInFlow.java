package com.ymiots.campusmp.model.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 通联流水表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AllInFlow {

    /**
     * 订单号
     */
    private String flowNo;

    /**
     * 顶层订单号，订单创建时的订单号
     */
    private String topMchntOrderNo;

    /**
     * 顶层平台订单号，创建订单时的平台生成的订单号
     */
    private String topOsOrderNo;

    /**
     * 商户号
     */
    private String cusId;

    /**
     * 金额
     */
    private Integer amount;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 订单操作类型：支付成功、退款...
     */
    private String optType;

    /**
     * 操作时间
     */
    private String trxDate;

    /**
     * 交易完成时间
     */
    private String payTime;

    /**
     * 结果信息
     */
    private String resultMsg;

    public static final String COL_FLOW_NO = "flow_no";

    public static final String COL_TOP_MCHNT_ORDER_NO = "top_mchnt_order_no";

    public static final String COL_TOP_OS_ORDER_NO = "top_os_order_no";

    public static final String COL_CUS_ID = "cus_id";

    public static final String COL_AMOUNT = "amount";

    public static final String COL_ORDER_TYPE = "order_type";

    public static final String COL_OPT_TYPE = "opt_type";

    public static final String COL_OPT_DATE = "opt_date";

    public static final String COL_RESULT_MSG = "result_msg";
}