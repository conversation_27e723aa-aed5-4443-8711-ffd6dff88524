package com.ymiots.campusmp.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AllInPayInfo {
    @JsonProperty("appId")
    private String appId;

    @JsonProperty("timeStamp")
    private Long timeStamp;

    @JsonProperty("nonceStr")
    private String nonceStr;

    @JsonProperty("package")
    private String packageStr;  // 使用 packageStr 作为 Java 字段名

    @JsonProperty("signType")
    private String signType;

    @JsonProperty("paySign")
    private String paySign;
}
