package com.ymiots.campusmp.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ymiots.campusmp.annotation.OsSignVerify;
import com.ymiots.campusmp.annotation.OsSigned;
import com.ymiots.campusmp.common.OsConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 支付平台签名统一入惨
 */
@Data
@OsSignVerify
@NoArgsConstructor
public class SignedParam<T> {

    @OsSigned
    private int code;

    @OsSigned
    private String msg;

    private String sign;

    private T data;

    @OsSigned
    @JsonFormat(pattern = OsConstant.DATE_FORMAT, timezone = OsConstant.TIME_ZONE)
    private Date timestamp;

}
