package com.ymiots.campusmp.model;

import java.io.Serializable;

import static com.ymiots.campusmp.constant.RCode.*;

/**
 * 公共的返回值对象
 *
 * @param <T>
 */
public class R<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    protected int code;

    protected String msg;

    protected T data;

    public R(boolean res, String msg, Object o) {
        if (res){
            this.code=200;
        }else {
            this.code=-1;
        }
        this.msg=msg;
        this.data= (T) o;
    }

    public R() {

    }

    public static <T> R<T> ok() {
        return restResult(null, SUCCESS.getCode(), SUCCESS.getMsg());
    }

    public static <T> R<T> ok(T data) {
        return restResult(data, SUCCESS.getCode(), SUCCESS.getMsg());
    }

    public static <T> R<T> ok(T data, String msg) {
        return restResult(data, SUCCESS.getCode(), msg);
    }

    public static <T> R<T> fail() {
        return restResult(null, FAIL.getCode(), FAIL.getMsg());
    }

    public static <T> R<T> fail(String msg) {
        return restResult(null, FAIL.getCode(), msg);
    }

    public static <T> R<T> fail(T data) {
        return restResult(data, FAIL.getCode(), FAIL.getMsg());
    }

    public static <T> R<T> fail(T data, String msg) {
        return restResult(data, FAIL.getCode(), msg);
    }

    public static <T> R<T> fail(int code, String msg) {
        return restResult(null, code, msg);
    }

    public static <T> R<T> restResult(T data, int code, String msg) {
        R<T> apiResult = new R<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        return apiResult;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}