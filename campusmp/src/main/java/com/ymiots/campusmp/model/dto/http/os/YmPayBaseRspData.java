package com.ymiots.campusmp.model.dto.http.os;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 富友 回复消息公共对象
 *
 * <AUTHOR>
 * @date 2022/10/31
 */
@Data
@NoArgsConstructor
public abstract class YmPayBaseRspData {

    /**
     * 错误代码, 000000 成功,其他详细参见错误列表
     */
    private String resultCode;

    private String resultMsg;

    /**
     * 机构号,接入机构在富友的唯一代码
     */
    private String insCode;

    /**
     * 商户号, 富友分配的商户号
     */
    private String mchntCode;

    /**
     * 签名
     */
    private String sign;
}
