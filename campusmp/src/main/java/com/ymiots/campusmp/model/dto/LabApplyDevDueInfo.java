package com.ymiots.campusmp.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 实验室预约数据到期，下发数据DTO
 */
@Data
@Accessors(chain = true)
public class LabApplyDevDueInfo {

    private String devSn;

    private String infoId;

    private String cardNo;

    private String cardId;

    private Integer devModel;

    private String formId;

    private String areaCode;

    private Integer status;
    /**
     * 电控下载状态
     */
    private Integer electricalStatus;

    private Integer infoIndex;

    private Date startTime;

    private Date endTime;

}
