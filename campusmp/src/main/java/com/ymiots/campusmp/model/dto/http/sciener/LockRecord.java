package com.ymiots.campusmp.model.dto.http.sciener;

import lombok.Data;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * 通通锁 锁记录
 *
 * <AUTHOR>
 * @date 2023/03/23
 */
@Data
public class LockRecord {

    /**
     * 记录类型，参考 云端锁记录类型(https://cnopen.sciener.com/document/doc?urlName=cloud%2FlockRecord%2FrecordTypeFromCloud.html)
     */
    private int recordType;

    private int success;

    /**
     * 操作人用户名：
     * APP蓝牙开关锁记录为开锁用户账号
     * 密码记录为密码名称
     * IC卡记录为IC卡名称 (下卡的时候不传姓名，则姓名为卡号)
     * 指纹记录为指纹名称
     */
    private String username;

    /**
     * 密码、IC卡号或指纹号
     */
    private String keyboardPwd;

    /**
     * 操作时锁上的时间
     */
    private long lockDate;

    /**
     * 锁电量
     */
    private int electricQuantity;

    /**
     * 记录上传到服务器的时间
     */
    private long serverDate;

    public LockRecord setKeyboardPwd(String keyboardPwd) {
        int cardNoInt = Integer.parseInt(keyboardPwd);
        long unsignedInt = toUnsignedInt(ByteBuffer.allocate(4).putInt(cardNoInt).array());
        this.keyboardPwd = String.valueOf(unsignedInt);
        return this;
    }

    public static long toUnsignedInt(byte[] buf) {
        ByteBuffer byteBuffer = ByteBuffer.wrap(buf);
        byteBuffer.order(ByteOrder.BIG_ENDIAN);
        int signedInt = byteBuffer.getInt();
        return signedInt & 0xFFFFFFFFL; // Convert to unsigned integer
    }
}
