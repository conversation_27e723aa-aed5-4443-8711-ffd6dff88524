package com.ymiots.campusmp.model.dto.http.os;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 云麦支付请求
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public abstract class YmPayBaseReq extends YmBaseReq {

    /**
     * 平台生成的订单号
     */
    protected String osOrderNo;

    /**
     * 商品名称
     */
    protected String goodsDes;

    /**
     * 商品金额
     */
    protected Long amount;

}
