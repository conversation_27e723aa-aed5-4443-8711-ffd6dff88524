package com.ymiots.campusmp.model.dto.http.os;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 下单支付：微信公众号支付
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class YmCommonPlacePayReq extends YmPayBaseReq {

    private String openId;

    /**
     * 回调地址
     */
    private String notifyUrl;

    /**
     * 附加信息
     */
    private String addInfo;

}
