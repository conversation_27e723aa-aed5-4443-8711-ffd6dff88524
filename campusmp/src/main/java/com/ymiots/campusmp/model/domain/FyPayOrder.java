package com.ymiots.campusmp.model.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.sql.Date;

/**
 * 支付平台订单消息，来自富友的数据
 *
 * <AUTHOR>
 * @date 2022/11/19
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class FyPayOrder {

    private String orderNo;

    private String mchntOrderNo;

    private String orderType;

    private Date tradeDate;

    private String tradeState;

    private Integer total;

    private String devid;

    private String buyerId;

    private String aliFundBillList;

    private String fyTraceNo;

    private String channelOrderNo;

    private String channelFlowNo;

}
