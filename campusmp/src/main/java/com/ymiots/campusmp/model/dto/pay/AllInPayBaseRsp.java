package com.ymiots.campusmp.model.dto.pay;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 通联支付 回复消息公共对象
 */
@Data
@SuperBuilder
@NoArgsConstructor
public abstract class AllInPayBaseRsp {

    /**
     * 返回码
     * SUCCESS/FAIL
     * 非空，最大长度 8
     * 此字段是通信标识，非交易结果，交易是否成功需要查看 trxStatus 来判断
     */
    @JsonProperty("retcode")
    private String retCode;

    /**
     * 返回码说明
     * 可空，最大长度 100
     */
    @JsonProperty("retmsg")
    private String retMsg;

    /**
     * 商户号
     * 平台分配的商户号
     * 非空，最大长度 15
     */
    @JsonProperty("cusid")
    private String cusId;

    /**
     * 应用ID
     * 平台分配的 APPID
     * 非空，最大长度 8
     */
    @JsonProperty("appid")
    private String appId;

    /**
     * 交易单号
     * 收银宝平台的交易流水号
     * 非空，最大长度 20
     */
    @JsonProperty("trxid")
    private String trxId;

    /**
     * 渠道平台交易单号
     * 例如微信, 支付宝平台的交易单号
     * 可空，最大长度 50
     */
    @JsonProperty("chnltrxid")
    private String chnlTrxId;

    /**
     * 商户交易单号
     * 商户的交易订单号
     * 非空，最大长度 32
     */
    @JsonProperty("reqsn")
    private String reqSn;

    /**
     * 随机字符串
     * 随机生成的字符串
     * 非空，最大长度 32
     */
    @JsonProperty("randomstr")
    private String randomStr;

    /**
     * 交易状态
     * 交易的状态, 对于刷卡支付，该状态表示实际的支付结果，其他为下单状态
     * 非空，最大长度 4
     */
    @JsonProperty("trxstatus")
    private String trxStatus;

    /**
     * 交易完成时间
     * yyyyMMddHHmmss
     * 可空，最大长度 14
     * 对于微信刷卡支付有效
     */
    @JsonProperty("fintime")
    private String finTime;

    /**
     * 错误原因
     * 失败的原因说明
     * 可空，最大长度 100
     */
    @JsonProperty("errmsg")
    private String errMsg;

    /**
     * 签名
     * 非空，最大长度 32
     * 详见安全规范
     */
    @JsonProperty("sign")
    private String sign;
}
