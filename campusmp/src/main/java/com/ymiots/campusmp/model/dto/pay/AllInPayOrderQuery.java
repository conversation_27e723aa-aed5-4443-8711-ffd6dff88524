package com.ymiots.campusmp.model.dto.pay;

import com.ymiots.campusmp.annotation.AllInPayReqField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 订单查询
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class AllInPayOrderQuery extends AllInPayOrderQueryBase {
    @AllInPayReqField("orgid")
    private String orgId;        // 集团/代理商商户号

    @AllInPayReqField("cusid")
    private String cusId;        // 商户号

    @AllInPayReqField("appid")
    private String appId;        // 应用ID

    @AllInPayReqField("reqsn")
    private String reqSn;        // 商户订单号

    @AllInPayReqField("trxid")
    private String trxId;        // 平台交易流水

    @AllInPayReqField("randomstr")
    private String randomStr;    // 随机字符串

    @AllInPayReqField("signtype")
    private String signType;     // 签名类型

    private String sign;         // 签名

//    @AllInPayReqField("fintime")
//    private String finTime;      // 交易完成时间，格式为 yyyyMMddHHmmss

}
