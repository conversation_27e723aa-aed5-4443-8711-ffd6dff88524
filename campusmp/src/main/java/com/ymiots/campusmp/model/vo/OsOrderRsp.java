package com.ymiots.campusmp.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ymiots.campusmp.common.OsConstant;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 平台订单操作（付款码付款、查询订单、微信公众号下单、...）回复
 *
 * <AUTHOR>
 * @date 2022/11/17
 */
@Data
@Accessors(chain = true)
public class OsOrderRsp {

    protected String osOrderNo;

    protected String mchntOrderNo;

    protected String orderType;

    @JsonFormat(pattern = OsConstant.DATE_FORMAT, timezone = OsConstant.TIME_ZONE)
    protected Date tradeDate;

    protected String tradeState;

    protected Long total;

    protected String devId;

    protected String buyerId;

    protected String aliFundBillList;

    protected String fyTraceNo;

    protected String channelOrderNo;

    protected String channelFlowNo;
}
