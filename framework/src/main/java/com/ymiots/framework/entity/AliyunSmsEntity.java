package com.ymiots.framework.entity;

import com.alibaba.fastjson.JSONObject;

public class AliyunSmsEntity {
    
	private String accessKeyId;
	private String accessSecret;
	private String PhoneNumbers;
	private String SignName;
	private String TemplateCode;
	private JSONObject TemplateParam;
	
	/**
	 * AccessKeyID
	 * @return
	 */
	public String getAccessKeyId() {
		return accessKeyId;
	}
	/**
	 * AccessKeyID
	 * @param accessKeyId
	 */
	public void setAccessKeyId(String accessKeyId) {
		this.accessKeyId = accessKeyId;
	}
	
	/**
	 *  Access Key Secret
	 * @return
	 */
	public String getAccessSecret() {
		return accessSecret;
	}
	/**
	 *  Access Key Secret
	 * @param accessSecret
	 */
	public void setAccessSecret(String accessSecret) {
		this.accessSecret = accessSecret;
	}
	/**
	 *  接收短信的手机号码，支持对多个手机号码发送短信，手机号码之间以英文逗号（,）分隔。上限为1000个手机号码。批量调用相对于单条调用及时性稍有延迟
	 * @return
	 */
	public String getPhoneNumbers() {
		return PhoneNumbers;
	}
	/**
	 *  接收短信的手机号码，支持对多个手机号码发送短信，手机号码之间以英文逗号（,）分隔。上限为1000个手机号码。批量调用相对于单条调用及时性稍有延迟
	 * @param phoneNumbers
	 */
	public void setPhoneNumbers(String phoneNumbers) {
		PhoneNumbers = phoneNumbers;
	}
	/**
	 * 短信签名名称。请在控制台签名管理页面签名名称一列查看。
	 * @return
	 */
	public String getSignName() {
		return SignName;
	}
	/**
	 *  短信签名名称。请在控制台签名管理页面签名名称一列查看。
	 * @param signName
	 */
	public void setSignName(String signName) {
		SignName = signName;
	}
	
	/**
	 *  短信模板ID。请在控制台模板管理页面模板CODE一列查看。
	 * @return
	 */
	public String getTemplateCode() {
		return TemplateCode;
	}
	/**
	 *  短信模板ID。请在控制台模板管理页面模板CODE一列查看。
	 * @param templateCode
	 */
	public void setTemplateCode(String templateCode) {
		TemplateCode = templateCode;
	}
	
	/**
	 *  短信模板变量对应的实际值，JSON格式。
	 * @return
	 */
	public JSONObject getTemplateParam() {
		return TemplateParam;
	}
	
	/**
	 *  短信模板变量对应的实际值，JSON格式。
	 * @param templateParam
	 */
	public void setTemplateParam(JSONObject templateParam) {
		TemplateParam = templateParam;
	}
	
	
	
}
