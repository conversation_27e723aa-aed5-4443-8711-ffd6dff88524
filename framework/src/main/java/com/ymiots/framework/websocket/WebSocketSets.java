package com.ymiots.framework.websocket;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArraySet;

import javax.websocket.SendHandler;
import javax.websocket.SendResult;
import javax.websocket.Session;

import com.ymiots.framework.common.Log;

public class WebSocketSets {

    private static WebSocketSets instance;

    public static WebSocketSets getInstance() {
        if (null == instance) {
            instance = new WebSocketSets();
        }
        return instance;
    }

    private CopyOnWriteArraySet<WebSocketClient> clientlist = new CopyOnWriteArraySet<WebSocketClient>();

    public WebSocketClient GetWebSocketClient(String sessoinid) {
        WebSocketClient clientrs = null;
        for (WebSocketClient client : this.clientlist) {
            if (client.getSession().getId().equals(sessoinid)) {
                clientrs = client;
                break;
            }
        }
        return clientrs;
    }

    public void add(WebSocketClient client) {
        this.clientlist.add(client);
    }

    public void remove(Session sessoin) {
        for (WebSocketClient client : this.clientlist) {
            if (client.getSession().getId().equals(sessoin.getId())) {
                this.clientlist.remove(client);
            }
        }
    }

    public List<WebSocketClient> get(String topic) {
        List<WebSocketClient> clients = new ArrayList<WebSocketClient>();
        for (WebSocketClient client : this.clientlist) {
            if (client.getSession().isOpen()) {
                if (client.getTopic().equals(topic)) {
                    clients.add(client);
                }
            } else {
                WebSocketSets.getInstance().remove(client.getSession());
            }
        }
        return clients;
    }

    /**
     * 根据主题推送字符串
     *
     * @param topic
     * @param text
     * @throws IOException
     */
    public void send(String topic, String text) {
        for (final WebSocketClient client : this.clientlist) {
            try {
                if (client.getTopic().equals(topic)) {
                    if (client.getSession().isOpen()) {
                        client.setIswork(true);
                        client.getSession().getAsyncRemote().sendText(text, new SendHandler() {
                            @Override
                            public void onResult(SendResult result) {
                                // TODO Auto-generated method stub
                                client.setIswork(false);
                            }
                        });
                    } else {
                        WebSocketSets.getInstance().remove(client.getSession());
                    }
                }
            } catch (Exception ex) {
                Log.error(this.getClass(), "异步推送WebSocket异常：" + ex.getMessage());
                WebSocketSets.getInstance().remove(client.getSession());
            }
        }
    }

    /**
     * 给特定用户推送特定主题的消息
     *
     * @param userid
     * @param topic
     * @param text
     * @return
     * @throws IOException
     */
    public boolean send(String userid, String topic, String text) throws IOException {
        boolean pushresult = false;
        for (final WebSocketClient client : this.clientlist) {
            try {
                if (client.getUserid().equals(userid) && client.getTopic().equals(topic)) {
                    if (client.getSession().isOpen()) {
                        client.setIswork(true);
                        client.getSession().getAsyncRemote().sendText(text, new SendHandler() {
                            @Override
                            public void onResult(SendResult result) {
                                client.setIswork(false);
                            }
                        });
                        pushresult = true;
                        break;
                    }
                }
            } catch (Exception e) {
                Log.error(this.getClass(), "给特定用户推送特定主题的WebSocket消息异常：" + e.getMessage());
                WebSocketSets.getInstance().remove(client.getSession());
                return false;
            }
        }
        return pushresult;
    }

    public void send(String text, List<WebSocketClient> clients) throws IOException {
        for (final WebSocketClient client : clients) {
            if (client.getSession().isOpen()) {
                try {
                    client.setIswork(true);
                    client.getSession().getAsyncRemote().sendText(text, new SendHandler() {
                        @Override
                        public void onResult(SendResult result) {
                            // TODO Auto-generated method stub
                            client.setIswork(false);
                        }
                    });
                } catch (Exception ex) {
                    Log.error(this.getClass(), "给所有主题推送WebSocket消息异常：" + ex.getMessage());
                    WebSocketSets.getInstance().remove(client.getSession());
                }
            }
        }
    }
}
