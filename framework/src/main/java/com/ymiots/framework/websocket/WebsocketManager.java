package com.ymiots.framework.websocket;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

@ServerEndpoint("/websocket/{userid}/{topic}")
@Component
public class WebsocketManager {

	   protected Logger logger = LoggerFactory.getLogger(this.getClass());


	    @OnOpen
	    public void onOpen(@PathParam("userid") String userid, @PathParam("topic") String topic, Session session){
//	        try {
//	        	session.setMaxBinaryMessageBufferSize(WebSocketConfig.MaxMessageBufferSize);
//	        	session.setMaxTextMessageBufferSize(WebSocketConfig.MaxMessageBufferSize);
//	        	session.setMaxIdleTimeout(-1);
//
//	        	WebSocketClient client=new WebSocketClient();
//	        	client.setUserid(userid);
//	        	client.setTopic(topic);
//	        	client.setSession(session);
//	            WebSocketSets.getInstance().add(client);
//	        } catch (Exception e) {
//	        	logger.error(String.format("WSS客户端[%s]用户[%s]连接并订阅主题[%s]失败，原因%s", session.getId(),userid,topic,e.getMessage()));
//            }
	    }

	    @OnClose
	    public void onClose(@PathParam("topic") String topic, Session session){
	    	WebSocketSets.getInstance().remove(session);
	    }

	    @OnError
	    public void onError(@PathParam("topic") String topic, @PathParam("userid") String userid, Session session, Throwable error) {
	    	logger.error(String.format("WSS客户端[%s]用户[%s]主题[%s]通讯异常：%s", session.getId(),userid,topic,error.getMessage()));
        }

	    /**
	     *
	     * @param session
	     * @param msg   结构体为：{ "topic":"authority", "msg":"{\"infotype\":[1,2,3],\"areacode\":\"001,002\"}" }
	     */
	     @OnMessage
	    public void onMessage(Session session,String msg){
	    	 JSONObject msgjson=JSONObject.parseObject(msg);
	    	 if(msgjson!=null){
	    		 if(msgjson.getString("topic").equals("authority")){
	    			 WebSocketClient client=WebSocketSets.getInstance().GetWebSocketClient(session.getId());
	    			 if(client!=null){
	    				 client.setAuthority(msgjson.getString("msg"));
	    			 }
	    		 }
	    	 }
	    }
}
