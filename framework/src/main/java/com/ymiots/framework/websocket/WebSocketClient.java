package com.ymiots.framework.websocket;

import javax.websocket.Session;

public class WebSocketClient {
	
	private String userid;	
	private String topic;
	private Session session;
	private String authority;
	private boolean iswork;
	
	public String getUserid() {
		return userid;
	}
	public void setUserid(String userid) {
		this.userid = userid;
	}
	public String getTopic() {
		return topic;
	}
	public void setTopic(String topic) {
		this.topic = topic;
	}
	public Session getSession() {
		return session;
	}
	public void setSession(Session session) {
		this.session = session;
	}
	public String getAuthority() {
		return authority;
	}
	public void setAuthority(String authority) {
		this.authority = authority;
	}
	public boolean isIswork() {
		return iswork;
	}
	public void setIswork(boolean iswork) {
		this.iswork = iswork;
	}
}
