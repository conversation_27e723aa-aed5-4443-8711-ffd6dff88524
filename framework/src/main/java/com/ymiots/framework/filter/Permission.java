package com.ymiots.framework.filter;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})  
@Retention(RetentionPolicy.RUNTIME)  
@Documented 
public @interface Permission {
	
	//需要授权
	boolean authorization() default true;
	
	String menu() default "";
	
	String opcode() default "";
	//99企业号，98运营平台
	int usertype() default 1;
	
	boolean allowpc() default true;
}
