package com.ymiots.framework.filter;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.framework.common.CallBackFilter;
import com.ymiots.framework.common.CampusLicense;
import com.ymiots.framework.common.CollectionUtils;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.MD5;

@Repository
public class PermissionFilterHandler {

	public boolean PreHandle(HttpServletRequest request, HttpServletResponse response, final Permission permission, JSONArray menus) throws Exception {
		return PreHandle(request, response, permission, menus, false);
	}
	
	public boolean PreHandle(HttpServletRequest request, HttpServletResponse response, final Permission permission, JSONArray menus, boolean dischecklicense) throws Exception {
		try {
			if(!dischecklicense) {
				if(StringUtils.isBlank(CampusLicense.getLicenseMd5())) {
					ReturnResult(request, response, "系统授权许可证校验失败");
					return false;
				}
				String md5license=MD5.MD5Encode(CampusLicense.getJSON());
				if(!CampusLicense.getLicenseMd5().equals(md5license)) {
					ReturnResult(request, response, "系统授权许可证校验失败");
					return false;
				}
			}
			// 临时去除鉴权模块，云平台数据库menuId有问题
			return true;

//			if (!StringUtils.isBlank(permission.menu()) && !StringUtils.isBlank(permission.opcode())) {
//				List<Object> list = CollectionUtils.where(menus, new CallBackFilter<Object>() {
//					public Boolean filter(Object model) {
//						JSONObject jsonitem = (JSONObject) model;
//						if (jsonitem.getString("menuid").equals(permission.menu())) {
//							JSONArray opbutton = jsonitem.getJSONArray("opbutton");
//							boolean exists = false;
//							for (int i = 0; i < opbutton.size(); i++) {
//								if (permission.opcode().indexOf(opbutton.getJSONObject(i).getString("itemId")) != -1) {
//									exists = true;
//								}
//							}
//							return exists;
//						} else {
//							return false;
//						}
//					}
//				});
//				if (list.size() > 0) {
//					return true;
//				} else {
//					ReturnResult(request, response, "操作未经授权");
//					return false;
//				}
//			} else if (!StringUtils.isBlank(permission.menu()) && StringUtils.isBlank(permission.opcode())) {
//				List<Object> list = CollectionUtils.where(menus, new CallBackFilter<Object>() {
//					public Boolean filter(Object model) {
//						JSONObject jsonitem = (JSONObject) model;
//						if (jsonitem.getString("menuid").equals(permission.menu())) {
//							return true;
//						} else {
//							return false;
//						}
//					}
//				});
//				if (list.size() > 0) {
//					return true;
//				} else {
//					ReturnResult(request, response, "操作未经授权");
//					return false;
//				}
//			} else {
//				return true;
//			}
		} catch (Exception ex) {
			ReturnResult(request, response, ex.getMessage());
			return false;
		}
	}

	public void ReturnResult(HttpServletRequest request, HttpServletResponse response, String msg) {
		if (!StringUtils.isBlank(request.getHeader("x-requested-with")) && request.getHeader("x-requested-with").equals("XMLHttpRequest")) {
			response.reset();
			response.setContentType("application/json;charset=UTF-8");
			try {
				PrintWriter out = response.getWriter();
				out.print(Json.getJsonResult(false, msg).toString());
				out.flush();
				out.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		} else {
			try {
				response.sendRedirect("/login");
				return;
			} catch (Exception ex) {
				return;
			}
		}
	}
}
