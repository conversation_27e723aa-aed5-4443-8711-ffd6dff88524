package com.ymiots.framework.service;

import java.io.File;
import java.nio.charset.StandardCharsets;

import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.framework.common.Base64Utils;
import com.ymiots.framework.common.CampusLicense;
import com.ymiots.framework.common.FileHelper;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.common.MD5;
import com.ymiots.framework.common.RSAUtils;

@Repository
public class LicenseService {

    public JsonResult InitLicenseObject(String licenseSN, String licenseCer, String publicKey) throws Exception {
        byte[] licensedata = RSAUtils.decryptByPublicKey(Base64Utils.decode(licenseCer), publicKey);
        String license = new String(licensedata, StandardCharsets.UTF_8);
        JSONObject licenseJson = JSONObject.parseObject(license);
        CampusLicense.setLicenseMaxuser(licenseJson.getIntValue("license_maxuser"));
        CampusLicense.setLicenseMaxDevice(licenseJson.getIntValue("license_maxdevice"));
        CampusLicense.setLicenseCpu(licenseJson.getString("license_cpu"));
        CampusLicense.setLicenseDisksn(licenseJson.getString("license_disksn"));
        CampusLicense.setLicenseBoardsn(licenseJson.getString("license_boardsn"));
        CampusLicense.setLicenseEnddate(licenseJson.getString("license_enddate"));
        CampusLicense.setLicenseDatetime(licenseJson.getString("license_datetime"));
        CampusLicense.setLicenseSN(licenseSN);
        CampusLicense.setOstitle(licenseJson.getString("ostitle"));
        CampusLicense.setCopyright(licenseJson.getString("copyright"));
        CampusLicense.setApptype(licenseJson.getIntValue("apptype"));
        CampusLicense.setName(licenseJson.getString("name"));
        CampusLicense.setLicenseMd5(MD5.MD5Encode(CampusLicense.getJSON()));
        licenseJson.put("licensecer", licenseCer);
        licenseJson.put("publickey", publicKey);
        return Json.getJsonResult(true, licenseJson);
    }

    public byte[] GetLicenseByte(String licenseCer, String publicKey) throws Exception {
        return RSAUtils.decryptByPublicKey(Base64Utils.decode(licenseCer), publicKey);
    }

    public JsonResult InitLicenseObject(String licenseSN, File licenseCerFile, File publicKeyFile) throws Exception {
        if (!licenseCerFile.exists()) {
            return Json.getJsonResult("许可证文件license.cer不存在！");
        }
        if (!publicKeyFile.exists()) {
            return Json.getJsonResult("公钥文件publickey.pem不存在！");
        }

        String licenseCer = FileHelper.ReadToString(licenseCerFile, "utf-8");
        String publicKey = FileHelper.ReadToString(publicKeyFile, "utf-8");
        return InitLicenseObject(licenseSN, licenseCer, publicKey);
    }
}
