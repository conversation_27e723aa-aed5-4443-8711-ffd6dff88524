package com.ymiots.framework.service;

import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.entity.AliyunSmsEntity;
import com.ymiots.framework.entity.AliyunSmsResult;

@Repository
public class AliyunSendSmsService {

	 public JsonResult SenSms(AliyunSmsEntity sms) {
		  DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", sms.getAccessKeyId(), sms.getAccessSecret());
	      IAcsClient client = new DefaultAcsClient(profile);
          CommonRequest request = new CommonRequest();
          request.setMethod(MethodType.POST);
	      request.setDomain("dysmsapi.aliyuncs.com");
	      request.setVersion("2017-05-25");
	      request.setAction("SendSms");
	      request.putQueryParameter("RegionId", "cn-hangzhou");
	      request.putQueryParameter("PhoneNumbers", sms.getPhoneNumbers());
	      request.putQueryParameter("SignName", sms.getSignName());
	      request.putQueryParameter("TemplateCode", sms.getTemplateCode());
	      if(null!=sms.getTemplateParam()) {
	    	  request.putQueryParameter("TemplateParam", sms.getTemplateParam().toJSONString());
	      }
	      try {
	         CommonResponse response = client.getCommonResponse(request);
	         AliyunSmsResult smsresult=JSONObject.parseObject(response.getData(), AliyunSmsResult.class);
	         if(smsresult.getCode().equals("OK")) {
	        	 return Json.getJsonResult(true);
	         }else {
	        	 return Json.getJsonResult(false,smsresult.getMessage());
	         }
	      } catch (ServerException e) {
	    	  return Json.getJsonResult(false,e.getMessage());
	      } catch (ClientException e) {
	    	  return Json.getJsonResult(false,e.getMessage());
	      }
	 }
}
