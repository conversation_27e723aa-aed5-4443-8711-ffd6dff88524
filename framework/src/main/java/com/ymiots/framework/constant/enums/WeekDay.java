package com.ymiots.framework.constant.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 星期 Enum
 *
 * <AUTHOR>
 * @date 2023/06/25
 */
public enum WeekDay {

    MONDAY("monday", "一", 1),
    TUESDAY("tuesday", "二", 2),
    WEDNESDAY("tuesday", "三", 3),
    THURSDAY("thursday", "四", 4),
    FRIDAY("friday", "五", 5),
    SATURDAY("saturday", "六", 6),
    SUNDAY("sunday", "日", 7),
    ;

    @Getter
    private final String code;

    @Getter
    private final String name;

    @Getter
    private final Integer index;

    private static final Map<String, WeekDay> nameMap = new HashMap<>();

    static {
        for (WeekDay value : WeekDay.values()) {
            nameMap.put(value.name, value);
        }
    }

    WeekDay(String code, String name, Integer index) {
        this.code = code;
        this.name = name;
        this.index = index;
    }

    public static WeekDay getByName(String name) {
        return nameMap.get(name);
    }
}
