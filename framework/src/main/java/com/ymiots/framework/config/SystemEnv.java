package com.ymiots.framework.config;

/**
 * 系统环境配置
 *
 * <AUTHOR>
 * @date 2023/06/27
 */
public class SystemEnv {

    /**
     * 当前运行路径
     */
    public static final String currentRunPath = System.getProperty("user.dir");

    /**
     * Java虚拟机临时路径
     */
    public static final String TEMP_DIR = System.getProperty("java.io.tmpdir");

    public static final int CPU_NUM = Runtime.getRuntime().availableProcessors();

    public static final int IO = CPU_NUM * 2;

    public static final int CALCULATE = CPU_NUM + 1;

}
