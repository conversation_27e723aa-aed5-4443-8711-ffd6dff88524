package com.ymiots.framework.common;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.record.DVALRecord;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor.HSSFColorPredefined;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sun.star.io.IOException;

public class ExcelUtil {
	private final static String excel2003L = ".xls"; // 2003- 版本的excel
	private final static String excel2007U = ".xlsx"; // 2007+ 版本的excel

	/**
	 * 描述：获取IO流中的数据，组装成List<List<Object>>对象
	 *
	 * @param in,fileName
	 * @return
	 * @throws IOException
	 */
	public static JSONArray getBankListByExcel(InputStream in, String fileName) throws Exception {
		JSONArray list = null;

		// 创建Excel工作薄
		Workbook work = ExcelUtil.getWorkbook(in, fileName);
		if (null == work) {
			throw new Exception("创建Excel工作薄为空！");
		}
		Sheet sheet = null;
		Row row = null;
		Cell cell = null;

		list = new JSONArray();
		// 读取Excel中第一个的sheet
		if(work.getNumberOfSheets()>0) {
			sheet = work.getSheetAt(0);
			if (sheet == null) {
				throw new Exception("无法读取第一个表！");
			}
			if(sheet.getLastRowNum()<1){
				throw new Exception("第一个表为空表！");
			}
			// 遍历当前sheet中的所有行
			for (int j = sheet.getFirstRowNum()+1; j <= sheet.getLastRowNum(); j++) {
				row = sheet.getRow(j);
				if (row == null) {
					continue;
				}
				// 遍历所有的列
				JSONObject rowjson = new JSONObject();
				for (int y = row.getFirstCellNum(); y < row.getLastCellNum(); y++) {
					cell = row.getCell(y);
					Object cellvalue="";
					if(cell!=null) {
						cellvalue=ExcelUtil.getCellValue(cell);
					}
					rowjson.put(String.valueOf(y), cellvalue);
				}

				boolean valid=false;
				for(String k:rowjson.keySet()) {
					if(!StringUtils.isBlank(rowjson.getString(k))) {
						valid=true;
					}
				}
				if(valid) {
					list.add(rowjson);
				}
			}
		}

		work.close();
		return list;
	}

	// 遍历Excel中所有的sheet
	public static JSONArray getBankListByExcelAllSheets(InputStream in, String fileName) throws Exception {
		JSONArray list = null;

		// 创建Excel工作薄
		Workbook work = ExcelUtil.getWorkbook(in, fileName);
		if (null == work) {
			throw new Exception("创建Excel工作薄为空！");
		}
		Sheet sheet = null;
		Row row = null;
		Cell cell = null;

		list = new JSONArray();
		// 遍历Excel中所有的sheet
		for (int i = 0; i < work.getNumberOfSheets(); i++) {
			sheet = work.getSheetAt(0);
			if (sheet == null) {
				continue;
			}
			if(sheet.getLastRowNum()<1){
				continue;
			}
			// 遍历当前sheet中的所有行
			for (int j = sheet.getFirstRowNum()+1; j <= sheet.getLastRowNum(); j++) {
				row = sheet.getRow(j);
				if (row == null) {
					continue;
				}
				// 遍历所有的列
				JSONObject rowjson = new JSONObject();
				for (int y = row.getFirstCellNum(); y < row.getLastCellNum(); y++) {
					cell = row.getCell(y);
					Object cellvalue="";
					if(cell!=null) {
						cellvalue=ExcelUtil.getCellValue(cell);
					}
					rowjson.put(String.valueOf(y), cellvalue);
				}

				boolean valid=false;
				for(String k:rowjson.keySet()) {
					if(!StringUtils.isBlank(rowjson.getString(k))) {
						valid=true;
					}
				}
				if(valid) {
					list.add(rowjson);
				}
			}
		}
		work.close();
		return list;
	}



	/**
	 * 获取第一行所有列
	 *
	 * @param in
	 * @param fileName
	 * @return
	 * @throws Exception
	 */
	public static JSONArray getFirstRow(InputStream in, String fileName) throws Exception {
		JSONArray list = new JSONArray();
		// 创建Excel工作薄
		Workbook work = ExcelUtil.getWorkbook(in, fileName);
		if (null == work) {
			throw new Exception("Excel工作薄为空！");
		}
		if (work.getNumberOfSheets() < 1) {
			throw new Exception("Excel工作薄为空！");
		}

		Sheet sheet = work.getSheetAt(0);
		if (sheet.getLastRowNum() < 1) {
			throw new Exception("Excel工作薄为空！");
		}

		Row row = sheet.getRow(0);
		if (row == null || row.getLastCellNum() < 1) {
			throw new Exception("Excel工作薄为空！");
		}

		Cell cell = null;
		for (int y = row.getFirstCellNum(); y < row.getLastCellNum(); y++) {
			cell = row.getCell(y);
			JSONObject cellitem = new JSONObject();
			cellitem.put("column", y);
			cellitem.put("value", ExcelUtil.getCellValue(cell).toString());
			list.add(cellitem);
		}
		work.close();
		return list;
	}

	/**
	 * 描述：根据文件后缀，自适应上传文件的版本
	 *
	 * @param inStr,fileName
	 * @return
	 * @throws Exception
	 */
	public static Workbook getWorkbook(InputStream inStr, String fileName) throws Exception {
		Workbook wb = null;
		String fileType = fileName.substring(fileName.lastIndexOf("."));
		if (excel2003L.equals(fileType)) {
			wb = new HSSFWorkbook(inStr); // 2003-
		} else if (excel2007U.equals(fileType)) {
			OPCPackage opcPackage = OPCPackage.open(inStr);
			wb = new XSSFWorkbook(opcPackage);// 2007+
		} else {
			throw new Exception("解析的文件格式有误！");
		}
		return wb;
	}

	/**
	 * 描述：对表格中数值进行格式化
	 *
	 * @param cell
	 * @return
	 */
	public static Object getCellValue(Cell cell) {
		Object value = null;
		DecimalFormat df = new DecimalFormat("0"); // 格式化number String字符
		SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd"); // 日期格式化
		DecimalFormat df2 = new DecimalFormat("2"); // 格式化数字
		DecimalFormat df4 = new DecimalFormat("###.###");
		switch (cell.getCellTypeEnum()) {
			case STRING:
				value = cell.getRichStringCellValue().getString();
				break;
			case NUMERIC:
				if ("General".equals(cell.getCellStyle().getDataFormatString())) {
					value = df.format(cell.getNumericCellValue());
				} else if ("m/d/yy".equals(cell.getCellStyle().getDataFormatString())) {
					value = sdf.format(cell.getDateCellValue());
				} else {
					value = df4.format(cell.getNumericCellValue());
				}
				break;
			case BOOLEAN:
				value = cell.getBooleanCellValue();
				break;
			case BLANK:
				value = "";
				break;
			default:
				value = "";
				break;
		}
		return value;
	}

	public static String ExportExcel(JSONArray list, JSONArray cells,String uploaddir, String tempPath,String fileName,String title) throws java.io.IOException{
		return ExportExcel(list,  cells, uploaddir,  tempPath, fileName,title,new ArrayList<CellRangeAddress>(),  new ArrayList<JSONArray>());
	}

	public static String ExportExcel(JSONArray list, JSONArray cells,String uploaddir, String tempPath,String filename,String title,String start,String end,String currentPage,String size) throws java.io.IOException{
		return ExportExcel(list,  cells, uploaddir,  tempPath, filename,title,new ArrayList<CellRangeAddress>(),  new ArrayList<JSONArray>(),start,end,currentPage,size);

	}

	public static String ExportExcel(JSONArray list, JSONArray cells,String uploaddir, String tempPath,String filename,String title, List<CellRangeAddress> cellrange, List<JSONArray> columnheads) throws java.io.IOException{
		if (cells.size() == 0) {
			return "";
		}
		if (list.size() == 0) {
			return "";
		}
		Integer value = list.getJSONObject(0).getIntValue("template");
		SXSSFWorkbook workbook = new SXSSFWorkbook(-1);
		Sheet sh = workbook.createSheet();

		int rowindex=0;
		//创建自定义列头
		CellStyle cStyle = workbook.createCellStyle();
		cStyle.setWrapText(false);
		Font font=workbook.createFont();
		font.setBold(true);
		font.setFontHeightInPoints((short)9);
		font.setFontName("宋体");
		font.setColor(HSSFColorPredefined.WHITE.getIndex());
		cStyle.setFont(font);

		if (value== 0) {
			Row titleRow = sh.createRow(rowindex);
			titleRow.setHeightInPoints(30);
			Cell titleCell = titleRow.createCell(0);
			titleCell.setCellValue(title);

			CellStyle titleCellStyle = workbook.createCellStyle();
			titleCellStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
			Font titleFont = workbook.createFont();
			titleFont.setBold(true); // 加粗
			titleFont.setFontHeightInPoints((short) 20);
			titleCellStyle.setFont(titleFont);

			titleCellStyle.setBorderBottom(BorderStyle.NONE); // 下边框
			titleCellStyle.setBorderLeft(BorderStyle.NONE);   // 左边框
			titleCellStyle.setBorderTop(BorderStyle.NONE);    // 上边框
			titleCellStyle.setBorderRight(BorderStyle.NONE);  // 右边框

			titleCell.setCellStyle(titleCellStyle);
			sh.addMergedRegion(new CellRangeAddress(rowindex, rowindex, 0, cells.size() - 1)); // 合并单元格
			rowindex++;
		}

		cStyle.setFillForegroundColor(HSSFColorPredefined.LIGHT_BLUE.getIndex());
		cStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		cStyle.setAlignment(HorizontalAlignment.CENTER);
		cStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		cStyle.setBorderBottom(BorderStyle.THIN); //下边框
		cStyle.setBorderLeft(BorderStyle.THIN);//左边框
		cStyle.setBorderTop(BorderStyle.THIN);//上边框
		cStyle.setBorderRight(BorderStyle.THIN);//右边框

		for (int rownum = 0; rownum < columnheads.size(); rownum++) {
			JSONArray headrowjson=columnheads.get(rownum);
			Row headrow = sh.createRow(rowindex);
			headrow.setHeightInPoints(20);
			for (int cellnum = 0; cellnum < headrowjson.size(); cellnum++) {
				Cell excelcell = headrow.createCell(cellnum);
				JSONObject record = headrowjson.getJSONObject(cellnum);
				excelcell.setCellValue(record.getString("name"));
				excelcell.setCellStyle(cStyle);
			}
			rowindex++;
		}

		Row row = sh.createRow(rowindex);
		row.setHeightInPoints(20);
		for (int cellnum = 0; cellnum < cells.size(); cellnum++) {
			JSONObject cell = cells.getJSONObject(cellnum);
			Cell excelcell = row.createCell(cellnum);

			excelcell.setCellValue(cell.getString("name"));
			int size= cell.getIntValue("size");
			if(size>0){
				sh.setColumnWidth(cellnum, size*256);
			}
			excelcell.setCellStyle(cStyle);
		}

		font.setColor(HSSFColorPredefined.BLACK.getIndex());
		font.setBold(false);
		cStyle.setFont(font);
		cStyle.setFillForegroundColor(HSSFColorPredefined.WHITE.getIndex());

		rowindex++;
		if (value == 0){
		for (int rownum = 0; rownum < list.size(); rownum++) {
			JSONObject record = list.getJSONObject(rownum);
			Row exccelrow = sh.createRow(rowindex);
			exccelrow.setHeightInPoints(20);
			for (int cellnum = 0; cellnum < cells.size(); cellnum++) {
				try {
					JSONObject cell = cells.getJSONObject(cellnum);
					Cell excelcell = exccelrow.createCell(cellnum);
					String dataindex=cell.getString("datakey");
					if(dataindex.indexOf("{")==-1 || dataindex.indexOf("}")==-1) {
						String val=record.getString(cell.getString("datakey"));
						if(!StringUtils.isBlank(val)) {
							excelcell.setCellValue(val);
						}else {
							excelcell.setCellValue("");
						}
					}else {
						Pattern reg = Pattern.compile("\\{([^}])*\\}");
						Matcher mat= reg.matcher(dataindex);
						String cellvalue=dataindex;
						while(mat.find()) {
							String matchvalue= mat.group();
							String key=matchvalue.substring(1, matchvalue.length()-1);
							String val=record.getString(key);
							if(!StringUtils.isBlank(val)) {
								cellvalue=cellvalue.replace(matchvalue, val);
							}else {
								cellvalue=cellvalue.replace(matchvalue, "");
							}
						}
						excelcell.setCellValue(cellvalue);
					}

					if(!StringUtils.isBlank(record.getString(cell.getString("datakey")+"align"))) {
						String align=record.getString(cell.getString("datakey")+"align");
						if(align.equals("center")) {
							cStyle.setAlignment(HorizontalAlignment.CENTER);
						}
						if(align.equals("right")) {
							cStyle.setAlignment(HorizontalAlignment.RIGHT);
						}
					}
					excelcell.setCellStyle(cStyle);
				}catch(Exception ex) {
					Log.error(ExcelUtil.class, ex.getMessage());
				}
			}
			// 一万行向磁盘写一次
			if (rownum % 10000 == 0) {
				((SXSSFSheet) sh).flushRows(100);
			}
			rowindex++;
		}
		}
		for(int i=0;i<cellrange.size();i++) {
			sh.addMergedRegion(cellrange.get(i));
		}
		uploaddir = String.format("%s%s/", uploaddir, tempPath);
		File dir = new File(uploaddir);
		if (!dir.exists()) {
			dir.mkdirs();
		}
		//String filename =  fileName + ".xlsx";
		FileOutputStream out = new FileOutputStream(uploaddir +filename);
		workbook.write(out);
		out.close();
		workbook.dispose();
		return String.format("/%s/%s", tempPath, filename);
	}

	public static String  ExportExcel(JSONArray list, JSONArray cells,String uploaddir, String tempPath,String filename,String title, List<CellRangeAddress> cellrange, List<JSONArray> columnheads,String startime,String endtime,String currenPage,String pageSize) throws java.io.IOException{
		if (cells.size() == 0) {
			return "";
		}
		if (list.size() == 0) {
			return "";
		}
		SXSSFWorkbook workbook = new SXSSFWorkbook(-1);
		Sheet sh = workbook.createSheet();

		int rowindex=0;
		//创建自定义列头
		CellStyle cStyle = workbook.createCellStyle();
		cStyle.setWrapText(false);
		Font font=workbook.createFont();
		font.setBold(true);
		font.setFontHeightInPoints((short)9);
		font.setFontName("宋体");
		font.setColor(HSSFColorPredefined.WHITE.getIndex());
		cStyle.setFont(font);

		cStyle.setFillForegroundColor(HSSFColorPredefined.LIGHT_BLUE.getIndex());
		cStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		cStyle.setAlignment(HorizontalAlignment.CENTER);
		cStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		cStyle.setBorderBottom(BorderStyle.THIN); //下边框
		cStyle.setBorderLeft(BorderStyle.THIN);//左边框
		cStyle.setBorderTop(BorderStyle.THIN);//上边框
		cStyle.setBorderRight(BorderStyle.THIN);//右边框

		Row titleRow = sh.createRow(rowindex);
		titleRow.setHeightInPoints(30);
		Cell titleCell = titleRow.createCell(0);
		titleCell.setCellValue(title);

		CellStyle titleCellStyle = workbook.createCellStyle();
		titleCellStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中

		Font titleFont = workbook.createFont();
		titleFont.setBold(true); // 加粗
		titleFont.setFontHeightInPoints((short) 20);
		titleCellStyle.setFont(titleFont);

		titleCellStyle.setBorderBottom(BorderStyle.NONE); // 下边框
		titleCellStyle.setBorderLeft(BorderStyle.NONE);   // 左边框
		titleCellStyle.setBorderTop(BorderStyle.NONE);    // 上边框
		titleCellStyle.setBorderRight(BorderStyle.NONE);  // 右边框

		titleCell.setCellStyle(titleCellStyle);
		sh.addMergedRegion(new CellRangeAddress(rowindex, rowindex, 0, cells.size() - 1)); // 合并单元格
		rowindex++;


		Row timeRow = sh.createRow(rowindex);
		Row row1 = null;
		timeRow.setHeightInPoints(20);
		Cell timeCell = null;
		if ((cells.size() - 8 )<0 ){
			rowindex++;
			row1 = sh.createRow(rowindex);
		}
		Cell yearMonthCell =null;
		Cell pageCell = null;
		if (row1==null){
			yearMonthCell = timeRow.createCell(cells.size() - 8);
			timeCell = timeRow.createCell(cells.size() - 4);
			pageCell = timeRow.createCell(cells.size() - 1);
		}else {
			yearMonthCell =row1.createCell(0);
			timeCell = timeRow.createCell(0);
			pageCell = row1.createCell(cells.size() - 1);
		}
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String currentTime = dateFormat.format(new Date());
		timeCell.setCellValue("导出时间：" + currentTime);
		if (startime!=null&&endtime!=null){
			yearMonthCell.setCellValue("导出月份："+startime+"至"+endtime);
		}
		currenPage = null;
		if (currenPage!=null&&pageSize!=null&&currenPage!=""&&pageSize!=""){
			pageCell.setCellValue("第"+currenPage+"页 共"+pageSize+"页");
		}

		// 设置导出时间行样式
		CellStyle timeCellStyle = workbook.createCellStyle();
		// 设置导出月份行样式
		CellStyle yearMonthCellStyle = workbook.createCellStyle();
		// 设置上边框
		timeCellStyle.setBorderTop(BorderStyle.NONE);
		yearMonthCellStyle.setBorderTop(BorderStyle.NONE);

		timeCell.setCellStyle(timeCellStyle);
		yearMonthCell.setCellStyle(yearMonthCellStyle);
		pageCell.setCellStyle(yearMonthCellStyle);
		//sh.addMergedRegion(new CellRangeAddress(rowindex, rowindex, 0, 2));
		//sh.addMergedRegion(new CellRangeAddress(rowindex, rowindex, 0, 2));

		rowindex++;

		for (int rownum = 0; rownum < columnheads.size(); rownum++) {
			JSONArray headrowjson=columnheads.get(rownum);
			Row headrow = sh.createRow(rowindex);
			headrow.setHeightInPoints(20);
			for (int cellnum = 0; cellnum < headrowjson.size(); cellnum++) {
				Cell excelcell = headrow.createCell(cellnum);
				JSONObject record = headrowjson.getJSONObject(cellnum);
				excelcell.setCellValue(record.getString("name"));
				excelcell.setCellStyle(cStyle);
			}
			rowindex++;
		}

		Row row = sh.createRow(rowindex);
		row.setHeightInPoints(20);
		for (int cellnum = 0; cellnum < cells.size(); cellnum++) {
			JSONObject cell = cells.getJSONObject(cellnum);
			Cell excelcell = row.createCell(cellnum);

			excelcell.setCellValue(cell.getString("name"));
			int size= cell.getIntValue("size");
			if(size>0){
				sh.setColumnWidth(cellnum, size*256);
			}
			excelcell.setCellStyle(cStyle);
		}

		font.setColor(HSSFColorPredefined.BLACK.getIndex());
		font.setBold(false);
		cStyle.setFont(font);
		cStyle.setFillForegroundColor(HSSFColorPredefined.WHITE.getIndex());

		rowindex++;
		for (int rownum = 0; rownum < list.size(); rownum++) {
			JSONObject record = list.getJSONObject(rownum);
			Row exccelrow = sh.createRow(rowindex);
			exccelrow.setHeightInPoints(20);
			for (int cellnum = 0; cellnum < cells.size(); cellnum++) {
				try {
					JSONObject cell = cells.getJSONObject(cellnum);
					Cell excelcell = exccelrow.createCell(cellnum);
					String dataindex=cell.getString("datakey");
					if(dataindex.indexOf("{")==-1 || dataindex.indexOf("}")==-1) {
						String val=record.getString(cell.getString("datakey"));
						if(!StringUtils.isBlank(val)) {
							excelcell.setCellValue(val);
						}else {
							excelcell.setCellValue("");
						}
					}else {
						Pattern reg = Pattern.compile("\\{([^}])*\\}");
						Matcher mat= reg.matcher(dataindex);
						String cellvalue=dataindex;
						while(mat.find()) {
							String matchvalue= mat.group();
							String key=matchvalue.substring(1, matchvalue.length()-1);
							String val=record.getString(key);
							if(!StringUtils.isBlank(val)) {
								cellvalue=cellvalue.replace(matchvalue, val);
							}else {
								cellvalue=cellvalue.replace(matchvalue, "");
							}
						}
						excelcell.setCellValue(cellvalue);
					}

					if(!StringUtils.isBlank(record.getString(cell.getString("datakey")+"align"))) {
						String align=record.getString(cell.getString("datakey")+"align");
						if(align.equals("center")) {
							cStyle.setAlignment(HorizontalAlignment.CENTER);
						}
						if(align.equals("right")) {
							cStyle.setAlignment(HorizontalAlignment.RIGHT);
						}
					}
					excelcell.setCellStyle(cStyle);
				}catch(Exception ex) {
					Log.error(ExcelUtil.class, ex.getMessage());
				}
			}
			// 一万行向磁盘写一次
			if (rownum % 10000 == 0) {
				((SXSSFSheet) sh).flushRows(100);
			}
			rowindex++;
		}
		for(int i=0;i<cellrange.size();i++) {
			CellRangeAddress cellAddresses = cellrange.get(i);
			cellAddresses.setFirstRow(cellAddresses.getFirstRow()+2);
			cellAddresses.setLastRow(cellAddresses.getLastRow()+2);
			sh.addMergedRegion(cellAddresses);
		}
		uploaddir = String.format("%s%s/", uploaddir, tempPath);
		File dir = new File(uploaddir);
		if (!dir.exists()) {
			dir.mkdirs();
		}
		FileOutputStream out = new FileOutputStream(uploaddir +filename);
		workbook.write(out);
		out.close();
		workbook.dispose();
		return String.format("/%s/%s", tempPath, filename);
	}

}
