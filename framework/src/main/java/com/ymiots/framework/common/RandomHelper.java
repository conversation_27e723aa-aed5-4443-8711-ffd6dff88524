package com.ymiots.framework.common;

import java.util.Random;

public class RandomHelper {
	public static final String ALLCHAR = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
	public static final String LETTERCHAR = "abcdefghijkllmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
	public static final String NUMBERCHAR = "0123456789";

	/**
	 * 返回一个定长的随机字符串(包含大小写字母、数字)
	 *
	 * @param length
	 *            随机字符串长度
	 * @return 随机字符串
	 */
	public static String generateString(int length) {
		StringBuffer sb = new StringBuffer();
		Random random = new Random();
		for (int i = 0; i < length; i++) {
			sb.append(ALLCHAR.charAt(random.nextInt(ALLCHAR.length())));
		}
		return sb.toString();
	}

	/**
	 * 返回一个定长的随机纯字母字符串(只包含大小写字母)
	 *
	 * @param length
	 *            随机字符串长度
	 * @return 随机字符串
	 */
	public static String generateUpperLowerString(int length) {
		StringBuffer sb = new StringBuffer();
		Random random = new Random();
		for (int i = 0; i < length; i++) {
			sb.append(LETTERCHAR.charAt(random.nextInt(LETTERCHAR.length())));
		}
		return sb.toString();
	}

	/**
	 * 返回一个定长的随机纯小写字母字符串(只包含小写字母)
	 *
	 * @param length
	 *            随机字符串长度
	 * @return 随机字符串
	 */
	public static String generateLowerString(int length) {
		return generateUpperLowerString(length).toLowerCase();
	}

	/**
	 * 返回一个定长的随机纯大写字母字符串(只包含大写字母)
	 *
	 * @param length
	 *            随机字符串长度
	 * @return 随机字符串
	 */
	public static String generateUpperString(int length) {
		return generateUpperLowerString(length).toUpperCase();
	}

	/**
	 * 生成一个定长的纯0字符串
	 *
	 * @param length
	 *            字符串长度
	 * @return 纯0字符串
	 */
	public static String generateZeroString(int length) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			sb.append('0');
		}
		return sb.toString();
	}

	/**
	 * 根据数字生成一个定长的字符串，长度不够前面补0
	 *
	 * @param num
	 *            数字
	 * @param fixdlenth
	 *            字符串长度
	 * @return 定长的字符串
	 */
	public static String generateFixdLengthString(long num, int fixedlenth) {
		StringBuffer sb = new StringBuffer();
		String strNum = String.valueOf(num);
		if (fixedlenth - strNum.length() >= 0) {
			sb.append(generateZeroString(fixedlenth - strNum.length()));
		} else {
			throw new RuntimeException("将数字" + num + "转化为长度为" + fixedlenth + "的字符串发生异常！");
		}
		sb.append(strNum);
		return sb.toString();
	}

	/**
	 * 每次生成的len位数都不相同(仅限于整型数组的每个元素都0-9这十个基本数字，且len的值还不能大于数组的长度)
	 *
	 * @return 定长的数字
	 */
	public static String GetDifferentNum(int len) {
		Random random = new Random();
		StringBuffer result=new StringBuffer();
		for (int i=0;i<len;i++)
		{
			result.append(random.nextInt(10));
		}
		return result.toString();
	}
	

	/**
	 * 获取16进制随机数
	 * @param len 指你要生成几位
	 * @return
	 * @throws CoderException
	 */
	public static String randomHexString(int len)  {
		try {
			StringBuffer result = new StringBuffer();
			for(int i=0;i<len;i++) {
				result.append(Integer.toHexString(new Random().nextInt(16)));
			}
			return result.toString().toUpperCase();
			
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			
		}
		return "";
	}
	/**
	 *  生成10位随机10进制二维码
	 */
	public static String CreateQRCode(int num){
		String str="0,1,2,3,4,5,6,7,8,9";
		String[] strx=str.split(",");
		StringBuffer sb=new StringBuffer();
		Random random = new Random();
		for(int i=1;i<=num;i++) {
			sb.append(strx[random.nextInt(strx.length)]);
		}
		return sb.toString();
	}
	/**
	 *  生成8位随机16进制二维码
	 * @return
	 */
	public static String CreateQRcode(){
		String str="A,B,C,D,E,F,0,1,2,3,4,5,6,7,8,9";
		String[] strx=str.split(",");
		StringBuffer sb=new StringBuffer();
		Random random = new Random();
		for(int i=1;i<=8;i++) {
			sb.append(strx[random.nextInt(strx.length)]);
		}
		return sb.toString();
	}
}
