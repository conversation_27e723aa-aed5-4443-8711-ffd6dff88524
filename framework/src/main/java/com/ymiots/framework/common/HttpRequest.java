package com.ymiots.framework.common;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletInputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.ByteArrayBuffer;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.util.ByteArrayBuilder;

public class HttpRequest {

	public static JsonResult HttpGet(String url) {
		List<Cookie> cookies=new ArrayList<Cookie>();
		return HttpGet(url,cookies);
	}
	
	public static JsonResult HttpGet(String url, List<Cookie> cookies) {
		try {
			CloseableHttpClient httpclient = HttpClients.createDefault();
			HttpPut httpGet = new HttpPut(url);
			httpGet.setHeader("Cookie", HttpRequest.getCookies(cookies));			
			CloseableHttpResponse response1 = httpclient.execute(httpGet);
			try {
				int statuscode = response1.getStatusLine().getStatusCode();
				if (statuscode == 200) {
					HttpEntity entity = response1.getEntity();
					if (entity != null) {
						InputStream stream = entity.getContent();
						ByteArrayBuffer buffer = new ByteArrayBuffer(0);
						int result = -1;
						while ((result = stream.read()) != -1) {
							buffer.append(result);
						}
						byte[] bytes = buffer.toByteArray();
						String strresult = new String(bytes, "UTF-8");
						return Json.getJsonResult(true,JSONObject.parseObject(strresult));
					} else {
						return Json.getJsonResult("response entity is null");
					}
				} else {
					return Json.getJsonResult("service status is "+String.valueOf(statuscode));
				}
			} catch (Exception ex) {
				Log.error(HttpRequest.class, ex.getMessage());
				return Json.getJsonResult(ex.getMessage());
			} finally {
				response1.close();
				httpclient.close();
			}
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}

	public static JsonResult HttpGetString(String url) {
		List<Cookie> cookies=new ArrayList<Cookie>();
		JsonResult result= HttpGetString(url, cookies);
		return result;
	}
	
	public static JsonResult HttpGetString(String url, List<Cookie> cookies) {
		try {
			CloseableHttpClient httpclient = HttpClients.createDefault();
			HttpPut httpGet = new HttpPut(url);
			httpGet.setHeader("Cookie", HttpRequest.getCookies(cookies));			
			CloseableHttpResponse response1 = httpclient.execute(httpGet);
			try {
				int statuscode = response1.getStatusLine().getStatusCode();
				if (statuscode == 200) {
					HttpEntity entity = response1.getEntity();
					if (entity != null) {
						InputStream stream = entity.getContent();
						ByteArrayBuffer buffer = new ByteArrayBuffer(0);
						int result = -1;
						while ((result = stream.read()) != -1) {
							buffer.append(result);
						}
						byte[] bytes = buffer.toByteArray();
						String strresult = new String(bytes, "UTF-8");
						return Json.getJsonResult(true,strresult);
					} else {
						return Json.getJsonResult("response entity is null");
					}
				} else {
					return Json.getJsonResult("service status is "+String.valueOf(statuscode));
				}
			} catch (Exception ex) {
				Log.error(HttpRequest.class, ex.getMessage());
				return Json.getJsonResult(ex.getMessage());
			} finally {
				response1.close();
				httpclient.close();
			}
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}
	
	public static byte[] HttpGetFile(String url, List<Cookie> cookies) {
		try {
			CloseableHttpClient httpclient = HttpClients.createDefault();
			org.apache.http.client.methods.HttpGet httpget=new org.apache.http.client.methods.HttpGet(url);
			httpget.setHeader("Cookie", HttpRequest.getCookies(cookies));			
			CloseableHttpResponse response1 = httpclient.execute(httpget);
			try {
				int statuscode = response1.getStatusLine().getStatusCode();
				if (statuscode == 200) {
					HttpEntity entity = response1.getEntity();
					if (entity != null) {
						InputStream stream = entity.getContent();
						ByteArrayBuffer buffer = new ByteArrayBuffer(0);
						int result = -1;
						while ((result = stream.read()) != -1) {
							buffer.append(result);
						}
						byte[] bytes = buffer.toByteArray();
						return bytes;
					} else {
						Log.error(HttpRequest.class, "请求失败："+url+"，未获取到数据：");
						return null;
					}
				} else {
					Log.error(HttpRequest.class, "请求失败："+url+"，状态："+String.valueOf(statuscode));
					return null;
				}
			} catch (Exception ex) {
				Log.error(HttpRequest.class, ex.getMessage());
				return null;
			}
			finally {
				response1.close();
				httpclient.close();
			}
		} catch (Exception ex) {
			Log.error(HttpRequest.class, ex.getMessage());
			return null;
		}
	}
	
	public static JsonResult HttpPost(String url, Map<String, String> params) {
		List<Cookie> cookies=new ArrayList<Cookie>();
		return HttpPost(url,params,cookies);
	}

	public static JsonResult HttpPost(String url) {
		List<Cookie> cookies=new ArrayList<Cookie>();
		Map<String, String> params=new HashMap<String, String>();
		return HttpPost(url,params,cookies);
	}

	public static JsonResult HttpPost(String url, List<Cookie> cookies) {
		Map<String, String> params=new HashMap<String, String>();
		return HttpPost(url,params,cookies);
	}

	public static JsonResult HttpPost(String url, Map<String, String> params, List<Cookie> cookies) {
		try {
			CloseableHttpClient httpclient = HttpClients.createDefault();
			org.apache.http.client.methods.HttpPost httpPost = new HttpPost(url);
			httpPost.setHeader("Cookie", HttpRequest.getCookies(cookies));
			List<NameValuePair> nvps = new ArrayList<NameValuePair>();
			for (Map.Entry<String, String> param : params.entrySet()) {
				nvps.add(new BasicNameValuePair(param.getKey(), param.getValue()));
			}
			httpPost.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));
			CloseableHttpResponse response2 = httpclient.execute(httpPost);
			try {
				int statuscode = response2.getStatusLine().getStatusCode();
				if (statuscode == 200) {
					HttpEntity entity = response2.getEntity();
					if (entity != null) {
						InputStream stream = entity.getContent();
						ByteArrayBuffer buffer = new ByteArrayBuffer(0);
						int result = -1;
						while ((result = stream.read()) != -1) {
							buffer.append(result);
						}
						byte[] bytes = buffer.toByteArray();
						String strresult = new String(bytes, "UTF-8");
						return Json.getJsonResult(true,JSONObject.parseObject(strresult));
					} else {
						return Json.getJsonResult("response entity is null");
					}
				} else {
					return Json.getJsonResult("service status is "+String.valueOf(statuscode));
				}
			} finally {
				response2.close();
				httpclient.close();
			}
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}



	public static JsonResult HttpPostData(String url, String data, Map<String, String> headers, List<Cookie> cookies) {
		try {
			CloseableHttpClient httpclient = HttpClients.createDefault();
			org.apache.http.client.methods.HttpPost httpPost = new HttpPost(url);
			httpPost.setHeader("Cookie", HttpRequest.getCookies(cookies));
			for(String key : headers.keySet()) {
				httpPost.setHeader(key, headers.get(key));
			}
			httpPost.setEntity(new ByteArrayEntity(data.getBytes("utf-8")));
			CloseableHttpResponse response2 = httpclient.execute(httpPost);
			try {
				int statuscode = response2.getStatusLine().getStatusCode();
				if (statuscode == 200) {
					HttpEntity entity = response2.getEntity();
					if (entity != null) {
						InputStream stream = entity.getContent();
						ByteArrayBuffer buffer = new ByteArrayBuffer(0);
						int result = -1;
						while ((result = stream.read()) != -1) {
							buffer.append(result);
						}
						byte[] bytes = buffer.toByteArray();
						String strresult = new String(bytes, "UTF-8");
						return Json.getJsonResult(true,JSONObject.parseObject(strresult));
					} else {
						return Json.getJsonResult("response entity is null");
					}
				} else {
					return Json.getJsonResult("service status is "+String.valueOf(statuscode));
				}
			} finally {
				response2.close();
				httpclient.close();
			}
		} catch (Exception ex) {
			return Json.getJsonResult(ex.getMessage());
		}
	}
	
	public static JsonResult HttpPostData(String url, String data) {
		List<Cookie> cookies=new ArrayList<Cookie>();
		Map<String, String> headers=new HashMap<String,String>();
		return HttpPostData(url,data,headers,cookies);
	}
	
	public static JsonResult HttpPostJsonData(String url, String data) {
		List<Cookie> cookies=new ArrayList<Cookie>();
		Map<String, String> headers=new HashMap<String,String>();
		headers.put("Content-Type", "application/json;charset=UTF-8");
		return HttpPostData(url,data,headers,cookies);
	}
	
	public static JsonResult HttpPostData(String url, String data, List<Cookie> cookies) {
		Map<String, String> headers=new HashMap<String,String>();
		return HttpPostData(url,data,headers,cookies);
	}
	
	private static String getCookies(List<Cookie> cookies) {
		StringBuilder sb = new StringBuilder();
		for (Cookie cookie : cookies) {
			sb.append(cookie.getName() + "=" + cookie.getValue() + ";");
		}
		return sb.toString();
	}

	public static String getCookieValue(HttpServletRequest request,String cookiename) {
		Cookie[] cookies = request.getCookies();
		String value = "";
		if (null != cookies) {
			for (Cookie item : cookies) {
				if (item.getName().equals(cookiename)) {
					value = item.getValue();
					break;
				}
			}
		}
		return value;
	}

	public static String getDomainString(HttpServletRequest request) {
		return String.format("http://%s:%s", request.getServerName(),request.getServerPort());
	}
	

	public static Map<String, String> GetParameterMap(HttpServletRequest request) {
		Map<String, String> keyvalue = new HashMap<String, String>();
		Enumeration enu = request.getParameterNames();
		while (enu.hasMoreElements()) {
			String paraName = (String) enu.nextElement();
			keyvalue.put(paraName, request.getParameter(paraName));
		}
		return keyvalue;
	}
	
	/**
	 *  获取没有参数名的数据包
	 * @param request
	 * @return
	 */
	public static String GetNoKeyData(HttpServletRequest request){
		try {
			ServletInputStream stream = request.getInputStream();
			ByteArrayBuilder buffer=new ByteArrayBuilder();
			int result = -1;
			while ((result = stream.read()) != -1) {
				buffer.append(result);
			}
			byte[] bytes = buffer.toByteArray();
			buffer.close();
			String strresult = new String(bytes, "UTF-8");
			return strresult;
		}catch (IOException e) {
			Log.error(HttpRequest.class, e.getMessage());
			return "";
		}
	}
}
