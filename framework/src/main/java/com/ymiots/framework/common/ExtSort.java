package com.ymiots.framework.common;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class ExtSort {
	
   public static String Orderby(HttpServletRequest request,String defaultsort){
	     String sort= request.getParameter("sort");
	     if(!StringUtils.isBlank(sort)){
	    	 JSONArray list=	JSONArray.parseArray(sort);
	    	 StringBuffer order=new StringBuffer();
	    	 for(int i=0;i<list.size();i++){
	    		 JSONObject item=list.getJSONObject(i);
	    		 if(order.length()==0){
	    			 order.append(String.format("%s %s", item.getString("property"),item.getString("direction")));
	    		 }else{
	    			 order.append(String.format(",%s %s", item.getString("property"),item.getString("direction")));
	    		 }
	    	 }
	    	 return order.toString();
	     }
	     return defaultsort;
   }
   
}
