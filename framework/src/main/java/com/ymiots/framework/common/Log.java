//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.ymiots.framework.common;


import reactor.util.Logger;
import reactor.util.Loggers;

public class Log {
    public Log() {
    }

    public static void debug(Class clazz, Object message) {
        Logger logger = Loggers.getLogger(clazz);
        logger.debug((String) message);
    }

    public static void info(Class clazz, Object message) {
        Logger logger = Loggers.getLogger(clazz);
        logger.info((String) message);
    }

    public static void error(Class clazz, Object message) {
        Logger logger = Loggers.getLogger(clazz);
        logger.error((String) message);
    }


    public static void warn(Class clazz, Object message) {
        Logger logger = Loggers.getLogger(clazz);
        logger.warn((String) message);
    }
}
