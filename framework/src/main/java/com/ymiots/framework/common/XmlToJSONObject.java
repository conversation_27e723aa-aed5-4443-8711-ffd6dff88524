package com.ymiots.framework.common;

import java.util.List;

import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 *  xml字符串转换为JSONObject
 * <AUTHOR>
 *
 */
public class XmlToJSONObject {

	public static JSONObject ToJSONObject(String xml){
		try {
			Document document= StrToDocument(xml);
			JSONObject json= ElementToJSONObject(document.getRootElement());
			return json;
		}catch(Exception ex) {
		   return null;	
		}
    }
	
	private static Document StrToDocument(String xml) throws DocumentException{
		return DocumentHelper.parseText(xml);
    }

	
	@SuppressWarnings("unchecked")
	private static JSONObject ElementToJSONObject(Element node) {
	        JSONObject result = new JSONObject();
	        // 当前节点的名称、文本内容和属性
	        List<Attribute> listAttr = node.attributes();// 当前节点的所有属性的list
	        for (Attribute attr : listAttr) {
	        	// 遍历当前节点的所有属性
	            result.put(attr.getName(), attr.getValue());
	        }
	        // 递归遍历当前节点所有的子节点
	        List<Element> listElement = node.elements();// 所有一级子节点的list
	        if (!listElement.isEmpty()) {
	            for (Element e : listElement) {// 遍历所有一级子节点
	                if (e.attributes().isEmpty() && e.elements().isEmpty()) // 判断一级节点是否有属性和子节点
	                    result.put(e.getName(), e.getTextTrim());// 沒有则将当前节点作为上级节点的属性对待
	                else {
	                    if (!result.containsKey(e.getName())) {
	                    	 result.put(e.getName(), new JSONArray());// 没有则创建
	                    }
	                    ((JSONArray) result.get(e.getName())).add(ElementToJSONObject(e));
	                    // 将该一级节点放入该节点名称的属性对应的值中
	                }
	            }
	        }
	        return result;
	    }
}
