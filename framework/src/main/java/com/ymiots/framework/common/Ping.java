package com.ymiots.framework.common;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.ymiots.framework.websocket.WebSocketSets;

public class Ping {

	   public static boolean ping(String ipAddress) throws Exception {
	        int  timeOut =  3000 ;  //超时应该在3钞以上        
	        boolean status = InetAddress.getByName(ipAddress).isReachable(timeOut);     // 当返回值是true时，说明host是可用的，false则不可。
	        return status;
	    }
	    
	    public static String ping02(String ipAddress,String websockettopic) {
	    	StringBuffer strbuf=new StringBuffer();
	        String line = null;
	        try {
	            Process pro = Runtime.getRuntime().exec("ping " + ipAddress);
	            InputStreamReader reader=new InputStreamReader(pro.getInputStream(),"GBK");
	            BufferedReader buf = new BufferedReader(reader);
	            while ((line = buf.readLine()) != null){
	            	strbuf.append(line);
	            	if(!StringUtils.isBlank(websockettopic)){
	            		WebSocketSets.getInstance().send(websockettopic, line);
	            	}
	            }
	        } catch (Exception ex) {
	        	strbuf.append(ex.getMessage());
	        }
	        return strbuf.toString();
	    }
	    
	    public static float ping(String ipAddress, int pingTimes, int timeOut) {  
	        BufferedReader in = null;  
	        Runtime r = Runtime.getRuntime();  // 将要执行的ping命令,此命令是windows格式的命令  
	        String pingCommand = "ping " + ipAddress + " -n " + pingTimes    + " -w " + timeOut;  
	        try {   // 执行命令并获取输出  
	            Process p = r.exec(pingCommand);   
	            if (p == null) {    
	                return 0;   
	            }
	            in = new BufferedReader(new InputStreamReader(p.getInputStream()));   // 逐行检查输出,计算类似出现=23ms TTL=62字样的次数  
	            int connectedCount = 0;   
	            String line = null;   
	            while ((line = in.readLine()) != null) {    
	                connectedCount += getCheckResult(line);   
	            }   // 如果出现类似=23ms TTL=62这样的字样,出现的次数=测试次数则返回真  
	            return connectedCount / (pingTimes*1.00f);  
	        } catch (Exception ex) {   
	            ex.printStackTrace();   // 出现异常则返回假  
	            return 0;  
	        } finally {   
	            try {    
	                in.close();   
	            } catch (IOException e) {    
	                e.printStackTrace();   
	            }  
	        }
	    }
	    
	    //若line含有=18ms TTL=16字样,说明已经ping通,返回1,否則返回0.
	    private static int getCheckResult(String line) {  // System.out.println("控制台输出的结果为:"+line);  
	        Pattern pattern = Pattern.compile("(\\d+ms)(\\s+)(TTL=\\d+)",    Pattern.CASE_INSENSITIVE);  
	        Matcher matcher = pattern.matcher(line);  
	        while (matcher.find()) {
	            return 1;
	        }
	        return 0; 
	    }
}
