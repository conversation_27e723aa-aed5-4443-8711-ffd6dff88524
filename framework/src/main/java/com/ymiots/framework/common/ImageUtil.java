package com.ymiots.framework.common;

import java.io.File;
import java.io.IOException;

import net.coobird.thumbnailator.Thumbnails;
import net.coobird.thumbnailator.geometry.Position;
import net.coobird.thumbnailator.geometry.Positions;

public class ImageUtil {

	/**
	 * 按固定宽高改变图片（遵循原图比例） 若图片横比 width 小，高比 height小，不变 若图片横比 width 小，高比 heigh 大，高缩小到
	 * heigh，图片比例不变 若图片横比 width 大，高比 heigh 小，横缩小到 width，图片比例不变 若图片横比 width 大，高比
	 * heigh 大，图片按比例缩小，横为width或高为heigh
	 * 
	 * @param picPath
	 * @param width
	 * @param height
	 * @throws IOException
	 */
	public static void changeImageWH(String picPath, int width, int height) throws IOException {
		File path = new File(picPath);
		Thumbnails.of(path).size(width, height).toFile(path);
		// Check the size of the file and compress if it's larger than 1MB
		if (path.length() > 1024 * 1024) {
			// Compress the image to target size around 200KB
			Thumbnails.of(path)
					.size(width, height)
					.outputQuality(calculateQuality(path, 500 * 1024))
					.toFile(path);
		}
	}


	private static double calculateQuality(File imageFile, long targetSize) throws IOException {
		long fileSize = imageFile.length();
		double quality = 1.0;

		// Adjust quality to get the file size close to the target size
		while (fileSize > targetSize && quality > 0.1) {
			quality -= 0.05;
			Thumbnails.of(imageFile)
					.scale(1)
					.outputQuality(quality)
					.toFile(imageFile);
			fileSize = imageFile.length();
		}

		return quality;
	}

	/**
	 * 按百分比缩小/放大图片（遵循原图比例）
	 * 
	 * @param picPath
	 * @param size
	 * @throws IOException
	 */
	public static void changeImgProportion(String picPath, double size) throws IOException {
		File path = new File(picPath);
		Thumbnails.of(path).scale(size).toFile(path);// 按比例缩放
	}

	/**
	 * 不遵循比例缩放
	 * 
	 * @param picPath
	 * @param width
	 * @param height
	 * @throws IOException
	 */
	public static void changeImgSize(String picPath, int width, int height) throws IOException {
		Thumbnails.of(picPath).size(width, height).keepAspectRatio(false).toFile(picPath);
	}

	/**
	 * 裁剪图片
	 * 
	 * @param region  裁剪区域
	 * @param picPath 路径
	 * @param width
	 * @param height
	 * @throws IOException
	 */
	public static void cutImg(Position region, String picPath, int width, int height) throws IOException {
		File path = new File(picPath);
		Thumbnails.of(path).sourceRegion(region, width, height).toFile(path);
	}

	/**
	 * 裁剪图片(按东南西北中)
	 * 
	 * @param region  裁剪区域
	 * @param picPath 路径
	 * @param width
	 * @param height
	 * @throws IOException
	 */
	public static void cutImg(Positions region, String picPath, int width, int height) throws IOException {
		File path = new File(picPath);
		Thumbnails.of(path).sourceRegion(region, width, height).size(width, height).toFile(path);
	}
}
