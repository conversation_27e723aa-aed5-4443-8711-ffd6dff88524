package com.ymiots.framework.common;

import java.security.MessageDigest;
import java.util.Arrays;

public class SHA1Utils {
    /**
     * 用SHA1算法生成安全签名
     *
     * @param array     字符串
     * @return 安全签名
     * @throws Exception 
     * @throws AesException
     */
    public static String getSHA1(String... array) throws Exception {
        StringBuffer sb = new StringBuffer();
        // 字符串排序
        Arrays.sort(array);
        for(String item: array) {
            sb.append(item);
        }
        return getSHA1(sb.toString());
    }

    public static String getSHA1(String string) throws Exception{
        // SHA1签名生成
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            md.update(string.getBytes("utf-8"));
            byte[] digest = md.digest();

            StringBuffer hexstr = new StringBuffer();
            String shaHex = "";
            for (int i = 0; i < digest.length; i++) {
                shaHex = Integer.toHexString(digest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexstr.append(0);
                }
                hexstr.append(shaHex);
            }
            return hexstr.toString();
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }
    
}
