package com.ymiots.framework.common;

import static com.alibaba.fastjson.util.TypeUtils.castToBigDecimal;

import java.math.BigDecimal;

import com.alibaba.fastjson.JSONObject;

public class JSONObjectPlus extends JSONObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	
	 public BigDecimal getBigDecimal(String key) {
	        Object value = get(key);
	        BigDecimal result= castToBigDecimal(value);
	        if(result==null) {
	        	return new BigDecimal(0);
	        }
	        return result;
	 }
	
	 public static JSONObjectPlus ToPlus(JSONObject json) {
		 JSONObjectPlus plus=JSONObject.parseObject(json.toJSONString(), JSONObjectPlus.class);
		 return plus;
	 }
}
