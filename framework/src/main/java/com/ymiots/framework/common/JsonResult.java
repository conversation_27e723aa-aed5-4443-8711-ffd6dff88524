package com.ymiots.framework.common;

import com.alibaba.fastjson.JSONObject;

public class JsonResult {

	private boolean success = false;

	private String msg = "";
	
	private JSONObject data;

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public JSONObject getData() {
		return data;
	}

	public void setData(JSONObject data) {
		this.data = data;
	}

	@Override
	public String toString(){
		return JSONObject.toJSONString(this);
	}
}
