package com.ymiots.framework.common;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * 校园系统许可证
 *
 * <AUTHOR>
 *
 */
public class CampusLicense {

	private static int licenseMaxuser;
	private static int licenseMaxDevice;
	private static String licenseCpu;
	private static String licenseDisksn;
	private static String licenseBoardsn;
	private static String licenseEnddate;
	private static String licenseDatetime;
	private static String licenseSN;
	private static String licenseMd5;
	private static String licenseJSON;
	private static String ostitle;
	private static String copyright;
	private static int apptype;
	private static String name;
	private static boolean initLicense;

	public static int getLicenseMaxuser() {
		return licenseMaxuser;
	}
	public static void setLicenseMaxuser(int licenseMaxuser) {
		CampusLicense.licenseMaxuser = licenseMaxuser;
	}
    public static int getLicenseMaxDevice() {
        return licenseMaxDevice;
    }
    public static void setLicenseMaxDevice(int licenseMaxDevice) {
        CampusLicense.licenseMaxDevice = licenseMaxDevice;
    }
	public static String getLicenseCpu() {
		return licenseCpu;
	}
	public static void setLicenseCpu(String licenseCpu) {
		CampusLicense.licenseCpu = licenseCpu;
	}
	public static String getLicenseDisksn() {
		return licenseDisksn;
	}
	public static void setLicenseDisksn(String licenseDisksn) {
		CampusLicense.licenseDisksn = licenseDisksn;
	}
	public static String getLicenseBoardsn() {
		return licenseBoardsn;
	}
	public static void setLicenseBoardsn(String licenseBoardsn) {
		CampusLicense.licenseBoardsn = licenseBoardsn;
	}
	public static String getLicenseEnddate() {
		return licenseEnddate;
	}
	public static void setLicenseEnddate(String licenseEnddate) {
		CampusLicense.licenseEnddate = licenseEnddate;
	}
	public static String getLicenseDatetime() {
		return licenseDatetime;
	}
	public static void setLicenseDatetime(String licenseDatetime) {
		CampusLicense.licenseDatetime = licenseDatetime;
	}
	public static String getLicenseSN() {
		return licenseSN;
	}
	public static void setLicenseSN(String licenseSN) {
		CampusLicense.licenseSN = licenseSN;
	}
	public static String getLicenseMd5() {
		return licenseMd5;
	}
	public static void setLicenseMd5(String licenseMd5) {
		CampusLicense.licenseMd5 = licenseMd5;
	}

	public static String getOstitle() {
		return ostitle;
	}
	public static void setOstitle(String ostitle) {
		CampusLicense.ostitle = ostitle;
	}
	public static String getCopyright() {
		return copyright;
	}
	public static void setCopyright(String copyright) {
		CampusLicense.copyright = copyright;
	}
	public static int getApptype() {
		return apptype;
	}
	public static void setApptype(int apptype) {
		CampusLicense.apptype = apptype;
	}
	public static String getName() {
		return name;
	}
	public static void setName(String name) {
		CampusLicense.name = name;
	}

	public static boolean isInitLicense() {
		return initLicense;
	}
	public static void setInitLicense(boolean initLicense) {
		CampusLicense.initLicense = initLicense;
	}

	public static String getJSON() {
		if(StringUtils.isBlank(CampusLicense.licenseJSON)) {
			JSONObject item=new JSONObject();
			item.put("licenseMaxuser", CampusLicense.getLicenseMaxuser());
			item.put("licenseMaxDevice", CampusLicense.getLicenseMaxDevice());
			item.put("licenseCpu", CampusLicense.getLicenseCpu());
			item.put("licenseDisksn", CampusLicense.getLicenseDisksn());
			item.put("licenseBoardsn", CampusLicense.getLicenseBoardsn());
			item.put("licenseEnddate", CampusLicense.getLicenseEnddate());
			item.put("licenseDatetime", CampusLicense.getLicenseDatetime());
			item.put("licenseSN", CampusLicense.getLicenseSN());
			item.put("licenseEnddate", CampusLicense.getLicenseEnddate());
			item.put("licenseDatetime", CampusLicense.getLicenseDatetime());
			item.put("ostitle", CampusLicense.getOstitle());
			item.put("copyright", CampusLicense.getCopyright());
			item.put("apptype", CampusLicense.getApptype());
			item.put("name", CampusLicense.getName());
			CampusLicense.licenseJSON= item.toJSONString();
		}
		return CampusLicense.licenseJSON;
	}


}
