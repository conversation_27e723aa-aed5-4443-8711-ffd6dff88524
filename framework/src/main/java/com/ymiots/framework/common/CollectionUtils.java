package com.ymiots.framework.common;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class CollectionUtils {

	public static <T> List<T> where(List<T> list, CallBackFilter<T> callBack) {
		List<T> newList = new ArrayList<T>();
		for (T model : list) {
			if (callBack.filter(model))
				newList.add(model);
		}
		return newList;
	}

	public static <T> T firstOrDefault(List<T> list, CallBackFilter<T> callBack) {
		T newModel = null;
		for (T model : list) {
			if (callBack.filter(model))
				return model;
		}
		return newModel;

	}

	public static <T> int Count(List<T> list, CallBackFilter<T> callBack) {
		int i = 0;
		for (T model : list) {
			if (callBack.filter(model))
				i++;
		}
		return i;
	}

	public static JSONArray where(JSONArray list, CallBackFilter<JSONObject> callBackFilter) {
		JSONArray newList = new JSONArray();
		for (Object model : list) {
			if (callBackFilter.filter((JSONObject)model))
				newList.add(model);
		}
		return newList;
	}

	public static JSONObject firstOrDefault(JSONArray list, CallBackFilter<JSONObject> callBackFilter) {
		JSONObject newModel = null;
		for (int i=0;i<list.size();i++) {
			JSONObject model = list.getJSONObject(i);
			if (callBackFilter.filter(model))
				return model;
		}
		return newModel;
	}
}
