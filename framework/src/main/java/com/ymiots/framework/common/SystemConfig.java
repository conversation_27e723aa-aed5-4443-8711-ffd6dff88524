package com.ymiots.framework.common;

import java.io.IOException;
import java.util.Properties;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.stereotype.Component;

@Component
public class SystemConfig {
//
//	private static Properties props ;
//
//    public static void Init(){
//        try {
//            ClassPathResource resource = new ClassPathResource("/application.properties");
//            props = PropertiesLoaderUtils.loadProperties(resource);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//
//    public static String getProperty(String key){
//    	if(SystemConfig.props== null){
//    		SystemConfig.Init();
//    	}
//        return props == null ? null :  props.getProperty(key);
//    }
//
//
//    public static String getProperty(String key,String defaultValue){
//    	if(SystemConfig.props== null){
//    		SystemConfig.Init();
//    	}
//         return props == null ? null : props.getProperty(key, defaultValue);
//    }
//
//
//    public static Properties getProperties(){
//    	if(SystemConfig.props== null){
//    		SystemConfig.Init();
//    	}
//        return props;
//    }
}
