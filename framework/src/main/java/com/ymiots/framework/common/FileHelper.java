package com.ymiots.framework.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import com.aliyuncs.utils.IOUtils;
import com.ymiots.framework.baidu.ConfigManager;
import com.ymiots.framework.baidu.FileExistsException;


public class FileHelper {

    /**
     * The Windows separator character.
     */
    private static final char WINDOWS_SEPARATOR = '\\';
    
    /**
     * The system separator character.
     */
    private static final char SYSTEM_SEPARATOR = File.separatorChar;
    
    /**
     * The number of bytes in a kilobyte.
     */
    public static final long ONE_KB = 1024;
    
    /**
     * The number of bytes in a megabyte.
     */
    public static final long ONE_MB = ONE_KB * ONE_KB;
    
    /**
     * The file copy buffer size (30 MB)
     */
    private static final long FILE_COPY_BUFFER_SIZE = ONE_MB * 30;
    
    
	public static byte[] getFileFromNetByUrl(String strUrl) {
	    try {
	        URL url = new URL(strUrl);
	        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
	        conn.setRequestMethod("GET");
	        conn.setConnectTimeout(5 * 1000);
	        InputStream inStream = conn.getInputStream();// 通过输入流获取图片数据
	        byte[] btImg = readInputStream(inStream);// 得到图片的二进制数据
	        return btImg;
	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	    return null;
	}
	
	private static byte[] readInputStream(InputStream inStream) throws Exception {
	    ByteArrayOutputStream outStream = new ByteArrayOutputStream();
	    byte[] buffer = new byte[10240];
	    int len = 0;
	    while ((len = inStream.read(buffer)) != -1) {
	        outStream.write(buffer, 0, len);
	    }
	    inStream.close();
	    return outStream.toByteArray();
	}
	
	public static List<String> SaveMultipartFile(MultipartFile[] files, String uploaddir, String path) throws IOException{	
		List<String> fileurl=new ArrayList<String>();
		if(files.length>0){
			String daily=DateHelper.format(new Date(), "yyyy-MM-dd");
			if(!StringUtils.isBlank(path)){
				daily=path;
			}
			uploaddir=String.format("%s%s/", uploaddir, daily);
			File dir=new File(uploaddir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			for(MultipartFile file :files){
				if(!file.isEmpty()){
					String suffix=file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
					String filename=String.format("%s%s", UUID.randomUUID().toString(),suffix);
					File fileinfo=new File(String.format("%s%s", uploaddir,filename));
					FileUtils.writeByteArrayToFile(fileinfo, file.getBytes());	
					String url=String.format("/%s/%s", daily,filename);
					fileurl.add(url);
				}
			}
		}
		return fileurl;
	}
	
	/**
	 *  保存上传文件
	 * @param files
	 * @return
	 * @throws IOException
	 */
	public static String SaveMultipartFile(MultipartFile file, String uploaddir, String path, String filename) throws IOException{
		String fileurl="";
		if(null!=file){
			String daily=DateHelper.format(new Date(), "yyyy-MM-dd");
			if(!StringUtils.isBlank(path)){
				daily=path;
			}
			uploaddir=String.format("%s%s/", uploaddir, daily);
			File dir=new File(uploaddir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			if(!file.isEmpty()){
				File fileinfo=new File(String.format("%s%s", uploaddir,filename));
				FileUtils.writeByteArrayToFile(fileinfo, file.getBytes());	
				fileurl=String.format("/%s/%s", daily,filename);
			}
		}
		return fileurl;
	}
	
	//图片转化成base64字符串  
    public static String ImageByte2Base64(byte[] data) throws IOException {
       return Base64.encodeBase64String(data);
    } 
	
	//图片转化成base64字符串  
    public static String Image2Base64(String imgpath) {
        try {
        	InputStream in = new FileInputStream(imgpath); 
            byte[] data = new byte[in.available()];  
            in.read(data);  
            in.close();  
            return Base64.encodeBase64String(data);
        }catch(Exception ex) {
        	Log.error(FileHelper.class, ex.getMessage());
        	return "";
        }
    }
	
	//base64字符串保存为图片
    public static String Base64ToImageFile(String base64Str,String uploaddir, String filepath,String filename) throws IOException  {
    	if(base64Str.indexOf("data:image/jpeg;base64,")!=-1){
    		base64Str=base64Str.replace("data:image/jpeg;base64,", "");
    	}
    	if(base64Str.indexOf("data:image/bmp;base64,")!=-1){
    		base64Str=base64Str.replace("data:image/bmp;base64,", "");
    	}
//        if (base64Str.indexOf("/") != 0) {
//            base64Str = "/" + base64Str;
//        }
        byte[] b = Base64.decodeBase64(base64Str);
        for(int i=0;i<b.length;++i)  
        {  
            if(b[i]<0)  
            {
                b[i]+=256;  
            }  
        } 
        
    	uploaddir=String.format("%s%s", uploaddir,filepath);
    	File dir=new File(uploaddir);
		if(!dir.exists()){
			dir.mkdirs();
		}
    	String filepathname=String.format("%s/%s", uploaddir,filename);
        OutputStream out = new FileOutputStream(filepathname);      
        out.write(b);  
        out.flush();  
        out.close(); 
        return String.format("/%s/%s", filepath,filename);
    } 
    
    /**
     *  保存byte[]为文件
     * @param file
     * @param path
     * @param filename
     * @return
     * @throws IOException
     */
    public static String SaveByte(byte[] file,String uploaddir, String path, String filename) throws IOException{
    	String fileurl="";
		if(file.length>0){
			String daily=DateHelper.format(new Date(), "yyyy-MM-dd");
			if(!StringUtils.isBlank(path)){
				daily=path;
			}
			uploaddir=String.format("%s%s/", uploaddir, daily);
			File dir=new File(uploaddir);
			if(!dir.exists()){
				dir.mkdirs();
			}
			File fileinfo=new File(String.format("%s%s", uploaddir,filename));
			FileUtils.writeByteArrayToFile(fileinfo, file);	
			fileurl=String.format("/%s/%s", daily, filename);
		}
		return fileurl;
	}
    
    public static boolean SaveByte(byte[] file, String path, String filename){
    	try {
    		File dir=new File(path);
			if(!dir.exists()){
				dir.mkdirs();
			}
			File fileinfo=new File(path+filename);
			FileUtils.writeByteArrayToFile(fileinfo, file);
			return true;
    	}catch(Exception ex) {
    		return false;
    	}
	}
    
    public static String ReadToString(File file,String encoding) {
        Long filelength = file.length();  
        byte[] filecontent = new byte[filelength.intValue()];  
        try {  
            FileInputStream in = new FileInputStream(file);  
            in.read(filecontent);  
            in.close();
            String s = new String(filecontent, encoding);
            return s;
        } catch (Exception e) {  
            e.printStackTrace();  
        }
        return "";
    }
    
    public static byte[] ReadToByte(File file) {
        Long filelength = file.length();  
        byte[] filecontent = new byte[filelength.intValue()];  
        try {  
            FileInputStream in = new FileInputStream(file);  
            in.read(filecontent);  
            in.close();
            return filecontent;      
        } catch (Exception e) {  
            e.printStackTrace();  
        }
        return null;
    }
    
    /**
     * 读取jar包内部文件
     * @param rul
     * @return
     */
    public static byte[] ReadResourceFile(String url)  {
    	try {
    		ClassPathResource resource = new ClassPathResource(url);
    		InputStream inputStream = resource.getInputStream();
    		byte[] buffer = new byte[inputStream.available()];
    		inputStream.read(buffer);
    		return buffer;
		} catch (IOException e) {
			Log.error(FileHelper.class, e.getMessage());
			return null;
		}
    }
    
    public static String getTempDirectoryPath() {  	  
        return System.getProperty("java.io.tmpdir");  
    }
    
    public static File getTempDirectory() {    	  
        return new File(getTempDirectoryPath());
    }
    
    /**
     * Deletes a file, never throwing an exception. If file is a directory, delete it and all sub-directories.
     * <p>
     * The difference between File.delete() and this method are:
     * <ul>
     * <li>A directory to be deleted does not have to be empty.</li>
     * <li>No exceptions are thrown when a file or directory cannot be deleted.</li>
     * </ul>
     *
     * @param file  file or directory to delete, can be {@code null}
     * @return {@code true} if the file or directory was deleted, otherwise
     * {@code false}
     *
     * @since 1.4
     */
    public static boolean deleteQuietly(File file) {
        if (file == null) {
            return false;
        }
        try {
            if (file.isDirectory()) {
                cleanDirectory(file);
            }
        } catch (Exception ignored) {
        }

        try {
            return file.delete();
        } catch (Exception ignored) {
            return false;
        }
    }

    //-----------------------------------------------------------------------
    /**
     * Determines if Windows file system is in use.
     * 
     * @return true if the system is Windows
     */
    static boolean isSystemWindows() {
        return SYSTEM_SEPARATOR == WINDOWS_SEPARATOR;
    }

    
    /**
     * Determines whether the specified file is a Symbolic Link rather than an actual file.
     * <p>
     * Will not return true if there is a Symbolic Link anywhere in the path,
     * only if the specific file is.
     * <p>
     * <b>Note:</b> the current implementation always returns {@code false} if the system
     * is detected as Windows using {@link FilenameUtils#isSystemWindows()}
     * 
     * @param file the file to check
     * @return true if the file is a Symbolic Link
     * @throws IOException if an IO error occurs while checking the file
     * @since 2.0
     */
    public static boolean isSymlink(File file) throws IOException {
        if (file == null) {
            throw new NullPointerException("File must not be null");
        }
        if (isSystemWindows()) {
            return false;
        }
        File fileInCanonicalDir = null;
        if (file.getParent() == null) {
            fileInCanonicalDir = file;
        } else {
            File canonicalDir = file.getParentFile().getCanonicalFile();
            fileInCanonicalDir = new File(canonicalDir, file.getName());
        }
        
        if (fileInCanonicalDir.getCanonicalFile().equals(fileInCanonicalDir.getAbsoluteFile())) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * Schedules a directory recursively for deletion on JVM exit.
     *
     * @param directory  directory to delete, must not be {@code null}
     * @throws NullPointerException if the directory is {@code null}
     * @throws IOException in case deletion is unsuccessful
     */
    private static void deleteDirectoryOnExit(File directory) throws IOException {
        if (!directory.exists()) {
            return;
        }

        directory.deleteOnExit();
        if (!isSymlink(directory)) {
            cleanDirectoryOnExit(directory);
        }
    }
    
    /**
     * Schedules a file to be deleted when JVM exits.
     * If file is directory delete it and all sub-directories.
     *
     * @param file  file or directory to delete, must not be {@code null}
     * @throws NullPointerException if the file is {@code null}
     * @throws IOException in case deletion is unsuccessful
     */
    public static void forceDeleteOnExit(File file) throws IOException {
        if (file.isDirectory()) {
            deleteDirectoryOnExit(file);
        } else {
            file.deleteOnExit();
        }
    }
    
    /**
     * Cleans a directory without deleting it.
     *
     * @param directory directory to clean
     * @throws IOException in case cleaning is unsuccessful
     */
    public static void cleanDirectory(File directory) throws IOException {
        if (!directory.exists()) {
            String message = directory + " does not exist";
            throw new IllegalArgumentException(message);
        }

        if (!directory.isDirectory()) {
            String message = directory + " is not a directory";
            throw new IllegalArgumentException(message);
        }

        File[] files = directory.listFiles();
        if (files == null) {  // null if security restricted
            throw new IOException("Failed to list contents of " + directory);
        }

        IOException exception = null;
        for (File file : files) {
            try {
                forceDelete(file);
            } catch (IOException ioe) {
                exception = ioe;
            }
        }

        if (null != exception) {
            throw exception;
        }
    }

    //-----------------------------------------------------------------------
    /**
     * Deletes a directory recursively. 
     *
     * @param directory  directory to delete
     * @throws IOException in case deletion is unsuccessful
     */
    public static void deleteDirectory(File directory) throws IOException {
        if (!directory.exists()) {
            return;
        }

        if (!isSymlink(directory)) {
            cleanDirectory(directory);
        }

        if (!directory.delete()) {
            String message ="Unable to delete directory " + directory + ".";
            throw new IOException(message);
        }
    }
    
    /**
     * Deletes a file. If file is a directory, delete it and all sub-directories.
     * <p>
     * The difference between File.delete() and this method are:
     * <ul>
     * <li>A directory to be deleted does not have to be empty.</li>
     * <li>You get exceptions when a file or directory cannot be deleted.
     *      (java.io.File methods returns a boolean)</li>
     * </ul>
     *
     * @param file  file or directory to delete, must not be {@code null}
     * @throws NullPointerException if the directory is {@code null}
     * @throws FileNotFoundException if the file was not found
     * @throws IOException in case deletion is unsuccessful
     */
    public static void forceDelete(File file) throws IOException {
        if (file.isDirectory()) {
            deleteDirectory(file);
        } else {
            boolean filePresent = file.exists();
            if (!file.delete()) {
                if (!filePresent){
                    throw new FileNotFoundException("File does not exist: " + file);
                }
                String message =
                    "Unable to delete file: " + file;
                throw new IOException(message);
            }
        }
    }  
    
    public static void moveFile(File srcFile, File destFile) throws IOException {
        if (srcFile == null) {
            throw new NullPointerException("Source must not be null");
        }
        if (destFile == null) {
            throw new NullPointerException("Destination must not be null");
        }
        if (!srcFile.exists()) {
            throw new FileNotFoundException("Source '" + srcFile + "' does not exist");
        }
        if (srcFile.isDirectory()) {
            throw new IOException("Source '" + srcFile + "' is a directory");
        }
        if (destFile.exists()) {
            throw new FileExistsException("Destination '" + destFile + "' already exists");
        }
        if (destFile.isDirectory()) {
            throw new IOException("Destination '" + destFile + "' is a directory");
        }
        boolean rename = srcFile.renameTo(destFile);
        if (!rename) {
            copyFile(srcFile, destFile, true);
            if (!srcFile.delete()) {
                deleteQuietly(destFile);
                throw new IOException("Failed to delete original file '" + srcFile +"' after copy to '" + destFile + "'");
            }
        }
    }
    
    /**
     * Copies a file to a new location.
     * <p>
     * This method copies the contents of the specified source file
     * to the specified destination file.
     * The directory holding the destination file is created if it does not exist.
     * If the destination file exists, then this method will overwrite it.
     * <p>
     * <strong>Note:</strong> Setting <code>preserveFileDate</code> to
     * {@code true} tries to preserve the file's last modified
     * date/times using {@link File#setLastModified(long)}, however it is
     * not guaranteed that the operation will succeed.
     * If the modification operation fails, no indication is provided.
     *
     * @param srcFile  an existing file to copy, must not be {@code null}
     * @param destFile  the new file, must not be {@code null}
     * @param preserveFileDate  true if the file date of the copy
     *  should be the same as the original
     *
     * @throws NullPointerException if source or destination is {@code null}
     * @throws IOException if source or destination is invalid
     * @throws IOException if an IO error occurs during copying
     * @see #copyFileToDirectory(File, File, boolean)
     */
    public static void copyFile(File srcFile, File destFile,  boolean preserveFileDate) throws IOException {
        if (srcFile == null) {
            throw new NullPointerException("Source must not be null");
        }
        if (destFile == null) {
            throw new NullPointerException("Destination must not be null");
        }
        if (srcFile.exists() == false) {
            throw new FileNotFoundException("Source '" + srcFile + "' does not exist");
        }
        if (srcFile.isDirectory()) {
            throw new IOException("Source '" + srcFile + "' exists but is a directory");
        }
        if (srcFile.getCanonicalPath().equals(destFile.getCanonicalPath())) {
            throw new IOException("Source '" + srcFile + "' and destination '" + destFile + "' are the same");
        }
        File parentFile = destFile.getParentFile();
        if (parentFile != null) {
            if (!parentFile.mkdirs() && !parentFile.isDirectory()) {
                throw new IOException("Destination '" + parentFile + "' directory cannot be created");
            }
        }
        if (destFile.exists() && destFile.canWrite() == false) {
            throw new IOException("Destination '" + destFile + "' exists but is read-only");
        }
        doCopyFile(srcFile, destFile, preserveFileDate);
    }
    
    /**
     * Cleans a directory without deleting it.
     *
     * @param directory  directory to clean, must not be {@code null}
     * @throws NullPointerException if the directory is {@code null}
     * @throws IOException in case cleaning is unsuccessful
     */
    private static void cleanDirectoryOnExit(File directory) throws IOException {
        if (!directory.exists()) {
            String message = directory + " does not exist";
            throw new IllegalArgumentException(message);
        }

        if (!directory.isDirectory()) {
            String message = directory + " is not a directory";
            throw new IllegalArgumentException(message);
        }

        File[] files = directory.listFiles();
        if (files == null) {  // null if security restricted
            throw new IOException("Failed to list contents of " + directory);
        }

        IOException exception = null;
        for (File file : files) {
            try {
                forceDeleteOnExit(file);
            } catch (IOException ioe) {
                exception = ioe;
            }
        }

        if (null != exception) {
            throw exception;
        }
    }

    /**
     * Internal copy file method.
     * 
     * @param srcFile  the validated source file, must not be {@code null}
     * @param destFile  the validated destination file, must not be {@code null}
     * @param preserveFileDate  whether to preserve the file date
     * @throws IOException if an error occurs
     */
    private static void doCopyFile(File srcFile, File destFile, boolean preserveFileDate) throws IOException {
        if (destFile.exists() && destFile.isDirectory()) {
            throw new IOException("Destination '" + destFile + "' exists but is a directory");
        }

        FileInputStream fis = null;
        FileOutputStream fos = null;
        FileChannel input = null;
        FileChannel output = null;
        try {
            fis = new FileInputStream(srcFile);
            fos = new FileOutputStream(destFile);
            input  = fis.getChannel();
            output = fos.getChannel();
            long size = input.size();
            long pos = 0;
            long count = 0;
            while (pos < size) {
                count = size - pos > FILE_COPY_BUFFER_SIZE ? FILE_COPY_BUFFER_SIZE : size - pos;
                pos += output.transferFrom(input, pos, count);
            }
        } finally {
            IOUtils.closeQuietly(output);
            IOUtils.closeQuietly(fos);
            IOUtils.closeQuietly(input);
            IOUtils.closeQuietly(fis);
        }

        if (srcFile.length() != destFile.length()) {
            throw new IOException("Failed to copy full contents from '" +
                    srcFile + "' to '" + destFile + "'");
        }
        if (preserveFileDate) {
            destFile.setLastModified(srcFile.lastModified());
        }
    }
    
}
