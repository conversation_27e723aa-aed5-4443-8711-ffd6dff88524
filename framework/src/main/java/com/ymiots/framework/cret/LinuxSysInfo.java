package com.ymiots.framework.cret;

import com.ymiots.framework.cret.SysInfo;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 获取Linux设备硬件信息
 *
 * <AUTHOR>
 * @date 2023/06/28
 */
public class LinuxSysInfo implements SysInfo {

    private final String drive;

    LinuxSysInfo(String drive) {
        this.drive = drive;
    }

    @Override
    public String getMainboardSn() {
        String result = null;
        try {
            Process process = Runtime.getRuntime().exec("sudo dmidecode -t 2");
            BufferedReader input = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = input.readLine();
            while (line != null) {
                if (line.trim().startsWith("Serial Number:")) {
                    result = line.trim().substring("Serial Number:".length()).trim();
                    break;
                }
                line = input.readLine();
            }
            input.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public String getCpuSn() {
        String result = null;
        try {
            Process process = Runtime.getRuntime().exec("cat /proc/cpuinfo");
            BufferedReader input = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = input.readLine();
            while (line != null) {
                if (line.startsWith("Serial")) {
                    result = line.split(":")[1].trim();
                    break;
                }
                line = input.readLine();
            }
            input.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public String getDiskSn() {
        String result = null;
        try {
            Process process = Runtime.getRuntime().exec("sudo hdparm -i " + drive);
            BufferedReader input = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = input.readLine();
            while (line != null) {
                Pattern pattern = Pattern.compile("SerialNo=([^\\s]*)");
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    result = matcher.group(1);
                    break;
                }
                line = input.readLine();
            }
            input.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

}
