package com.ymiots.framework.cret;

import com.ymiots.framework.cret.SysInfo;
import lombok.extern.log4j.Log4j2;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * MacOS 系统信息获取
 *
 * <AUTHOR>
 * @date 2023/06/28
 */
@Log4j2
public class MacOSSysInfo implements SysInfo {

    private static final String SN;

    MacOSSysInfo() {}

    static {
        SN = getSn();
    }

    @Override
    public String getMainboardSn() {
        return SN;
    }

    @Override
    public String getCpuSn() {
        return SN;
    }

    @Override
    public String getDiskSn() {
        return SN;
    }

    private static String getSn() {
        String result = null;
        try {
            Process process = Runtime.getRuntime().exec(
                    new String[] {"system_profiler", "SPHardwareDataType"}
            );
            BufferedReader input = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = input.readLine();
            log.info("系统信息: \n");
            while (line != null) {
                // 获取Mac序列号
                if (line.contains("Serial Number (system):")) {
                    result = line.split(":")[1].trim();
                    break;
                }
                line = input.readLine();
                log.info(line);
            }
            input.close();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("MacOS 序列号信息获取出错");
            System.exit(0);
        }
        return result;
    }
}
