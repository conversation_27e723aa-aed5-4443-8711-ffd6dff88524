package com.ymiots.framework.cret;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;

import java.util.Objects;

/**
 * 验证工具
 *
 * <AUTHOR>
 * @date 2023/06/28
 */
@Log4j2
public class CertConfig {

    public static final String CPU_SN;

    public static final String MAINBOARD_SN;

    public static final String DISK_SN;

    static {
        String os = System.getProperty("os.name").toLowerCase();
        SysInfo sysInfo = null;
        if (os.contains("windows")) {
            sysInfo = new WindowsSysInfo();
        } else if (os.contains("linux")) {
            sysInfo = new LinuxSysInfo("/dev/sda");
        } else if (os.contains("mac")) {
            sysInfo = new MacOSSysInfo();
        } else {
            log.error("不受支持的系统类型");
            System.exit(0);
        }
        String cpuSn = null;
        String mainboardSn = null;
        String diskSn = null;
        try {
            cpuSn = sysInfo.getCpuSn();
        } catch (Exception e) {
            log.error("无法获取到CPU信息");
//            System.exit(0);
        }
        try {
            mainboardSn = sysInfo.getMainboardSn();
        } catch (Exception e) {
            log.error("无法获取到主板信息");
//            System.exit(0);
        }
        try {
            diskSn = sysInfo.getDiskSn();
        } catch (Exception e) {
            log.error("无法获取到磁盘信息");
//            System.exit(0);
        }

        if (cpuSn == null || diskSn == null || mainboardSn == null) {
            log.error("硬件信息为null; \nDISK_SN:" + diskSn + "\nCPU_SN:" + cpuSn + "\nMAINBOARD_SN:" + mainboardSn);
//            System.exit(0);
        }
        CPU_SN = cpuSn;
        DISK_SN = diskSn;
        MAINBOARD_SN = mainboardSn;
    }

    public static boolean isCert(JSONObject certInfo) {

        String licenseCpu = certInfo.getString("license_cpu");
        String licenseDiskSn = certInfo.getString("license_disksn");
        String licenseMainboardSn = certInfo.getString("license_boardsn");

        if (!Objects.equals(licenseCpu, "*") && !Objects.equals(CPU_SN, licenseCpu)) {
            return false;
        }
        if (!Objects.equals(licenseDiskSn, "*") && !Objects.equals(DISK_SN, licenseDiskSn)) {
            return false;
        }
        if (!Objects.equals(licenseMainboardSn, "*")
                && !Objects.equals(MAINBOARD_SN, licenseMainboardSn)) {
            return false;
        }

        return true;
    }



}
