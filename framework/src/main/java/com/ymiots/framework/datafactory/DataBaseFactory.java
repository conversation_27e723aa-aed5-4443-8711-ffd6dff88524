package com.ymiots.framework.datafactory;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.Log;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.CallableStatementCallback;
import org.springframework.jdbc.core.CallableStatementCreator;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
public class DataBaseFactory {

	public int excuteSql(JdbcTemplate jdbcTemplate, String sql, Object... args) {
		try {
			int count =jdbcTemplate.update(sql, args);
			return count;
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, ex.getMessage());
			return -1;
		}
	}

	public  int[] excuteBatchSql(JdbcTemplate jdbcTemplate, String... sql)  {
		try {
			if(sql.length==0){
				return new int[]{0};
			}
			int[] result = jdbcTemplate.batchUpdate(sql);
			return result;
		} catch (DataAccessException ex) {
			Log.error(DataBaseFactory.class, ex.getMessage());
			return new int[]{-1};
		}
	}

	public void excuteTransaction(JdbcTemplate jdbcTemplate, CallbackTransaction callback, Integer isolationLevel) throws Exception {
		TransactionSynchronizationManager.initSynchronization();
		DataSource dataSource = jdbcTemplate.getDataSource();
		Connection connection = DataSourceUtils.getConnection(dataSource);
		try {
			connection.setAutoCommit(false);

			// 设置事务隔离级别
			if (isolationLevel != null) {
				connection.setTransactionIsolation(isolationLevel);
			}

			callback.execute(connection);
			connection.commit();
		} catch (Exception e) {
			Log.error(DataBaseFactory.class, e.getMessage());
			// 执行错误时回滚事务
			connection.rollback();
			throw new Exception(e.getMessage());
		} finally {
			try {
				TransactionSynchronizationManager.clearSynchronization();
				connection.setAutoCommit(true);
				// 关闭连接（如果需要）
				DataSourceUtils.releaseConnection(connection, dataSource);
			} catch (java.sql.SQLException e) {
				Log.error(DataBaseFactory.class, e.getMessage());
			}
		}
	}


	public void excuteTransaction(JdbcTemplate jdbcTemplate, CallbackTransaction callback) throws Exception {
		TransactionSynchronizationManager.initSynchronization();
		DataSource dataSource = jdbcTemplate.getDataSource();
		Connection connection = DataSourceUtils.getConnection(dataSource);
		try {
		    connection.setAutoCommit(false);
		    callback.execute(connection);
		    connection.commit();
		} catch (Exception e) {
			Log.error(DataBaseFactory.class, e.getMessage());
			//执行错误时回滚事务
			connection.rollback();
			throw new Exception(e.getMessage());
		} finally {
			try {
				TransactionSynchronizationManager.clearSynchronization();
			    connection.setAutoCommit(true);
			} catch (java.sql.SQLException e) {
				// TODO Auto-generated catch block
				Log.error(DataBaseFactory.class, e.getMessage());
			}
			
		}
	}
	

	public  JSONArray QueryList(JdbcTemplate jdbcTemplate, String sql) {
		return QueryList(jdbcTemplate,sql, new Object[]{});
	}

	public JSONArray QueryList(JdbcTemplate jdbcTemplate, String sql, Object... args) {
		try {
			List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, args); 
			return JSONArray.parseArray(JSONArray.toJSONString(list));
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, ex.getMessage());
			return new JSONArray();
		}
	}

	public  JSONObject QueryJSONObject(JdbcTemplate jdbcTemplate, String sql) {
		return QueryJSONObject(jdbcTemplate,sql, new Object[]{});
	}

	public  JSONObject QueryJSONObject(JdbcTemplate jdbcTemplate, String sql, Object... args) {
		try {
			Map<String, Object> map = jdbcTemplate.queryForMap(sql,args);
			return JSONObject.parseObject(JSONObject.toJSONString(map));
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, ex.getMessage());
			return null;
		}
	}

	public  <T> T QueryObject(JdbcTemplate jdbcTemplate, String sql, Class<T> requiredType) {
		return QueryObject(jdbcTemplate,sql, requiredType, new Object[]{});
	}

	public  <T> T QueryObject(JdbcTemplate jdbcTemplate, String sql, Class<T> requiredType, Object... args) {
		try {
			JSONArray list= QueryList(jdbcTemplate,sql,args);
			if(list.size()>0){
				String json=JSONObject.toJSONString(list.get(0));
				T object = JSONObject.parseObject(json, requiredType);
				return object;
			}
			else{
				return null;
			}
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, ex.getMessage());
			return null;
		}
	}

	
	public  <T> List<T> QueryObjectList(JdbcTemplate jdbcTemplate, String sql, Class<T> requiredType) {
		return QueryObjectList(jdbcTemplate,sql, requiredType, new Object[]{});
	}

	public  <T> List<T> QueryObjectList(JdbcTemplate jdbcTemplate, String sql, Class<T> requiredType, Object... args) {
		try {
			JSONArray list= QueryList(jdbcTemplate,sql,args);
			if(list.size()>0){
				List<T> objlist=new ArrayList<T>();	
				for(int i=0;i<list.size();i++){
					String json=JSONObject.toJSONString(list.get(i));
					T object = JSONObject.parseObject(json, requiredType);
					objlist.add(object);
				}
				return objlist;
			}
			else{
				return new ArrayList<T>();
			}
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, ex.getMessage());
			return new ArrayList<T>();
		}
	}
	

	public  JsonData QueryJsonData(JdbcTemplate jdbcTemplate, String sql) {
		try {			
			List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);		
			return Json.getJsonData(true, "", JSONArray.parseArray(JSONArray.toJSONString(list)), list.size());
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, new Object[]{});
			Log.error(DataBaseFactory.class, ex.getMessage());
			return Json.getJsonData(false,"数据读取失败");
		}
	}
	
	public JsonData QueryJsonData(JdbcTemplate jdbcTemplate, String fields, String table, String where, String orderby) {
		String totalsql ="";
		String sql ="";
		try {
			where = (StringUtils.isEmpty(where) ? "" : String.format("where %s", where));
			totalsql = String.format("select count(1) as total from %s %s", table, where.split("GROUP")[0]);
			Map<String, Object> map = jdbcTemplate.queryForMap(totalsql);
			int total = Integer.parseInt(map.get("total").toString());
			orderby = (StringUtils.isEmpty(orderby) ? "" : String.format("ORDER BY %s", orderby));
			sql = String.format("select %s from %s %s %s", fields, table, where, orderby);
			List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);
			return Json.getJsonData(true, "", JSONArray.parseArray(JSONArray.toJSONString(list)), total);
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(totalsql, new Object[]{});
			WriteDataBaseErrorLog(sql, new Object[]{});
			Log.error(DataBaseFactory.class, ex.getMessage());
			return Json.getJsonData(false,"数据读取失败");
		}
	}

	public  JSONArray QueryJSONArray(JdbcTemplate jdbcTemplate, String fields, String table, String where, String orderby, int start, int limit) {
		String sql ="";
		try {		
			where = (StringUtils.isEmpty(where) ? "" : String.format("where %s", where));
			orderby = (StringUtils.isEmpty(orderby) ? "order by uid desc" : String.format("order by %s", orderby));
			if (limit > 0) {
				sql = String.format("select %s from %s %s %s limit %s,%s", fields, table, where, orderby, start, limit);
			}
			else{
				sql = String.format("select %s from %s %s %s", fields, table, where, orderby);
			}
			List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);		
			return JSONArray.parseArray(JSONArray.toJSONString(list));
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, new Object[]{});
			Log.error(DataBaseFactory.class, ex.getMessage());
			return new JSONArray();
		}
	}
	
	public JsonData QueryJsonData(JdbcTemplate jdbcTemplate, String fields, String table, String where, String orderby, int start, int limit) {
		String sql ="";
		String totalsql ="";
		try {	
			where = (StringUtils.isEmpty(where) ? "" : String.format("where %s", where));
			totalsql = String.format("select count(1) as total from %s %s", table, where);
			Map<String, Object> map = jdbcTemplate.queryForMap(totalsql);
			int total = Integer.parseInt(map.get("total").toString());
			orderby = (StringUtils.isEmpty(orderby) ? "order by uid desc" : String.format("order by %s", orderby));
			if (limit > 0) {
				sql = String.format("select %s from %s %s %s limit %s,%s",  fields, table, where,orderby, start, limit);
			}
			else{
				sql = String.format("select %s from %s %s %s", fields, table, where, orderby);
			}
			List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);		
			return Json.getJsonData(true, "", JSONArray.parseArray(JSONArray.toJSONString(list)), total);
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, new Object[]{});
			Log.error(DataBaseFactory.class, ex.getMessage());
			return Json.getJsonData(false,"SQL执行错误，详情请查询日志");
		}
	}

	/**
	 * 获取表记录数量（不带参数）
	 * @param jdbcTemplate JDBC模板
	 * @param table 表名
	 * @param where WHERE条件（不包含where关键字）
	 * @return 记录数量
	 * @deprecated 建议使用带参数的重载方法以防止SQL注入
	 */
	@Deprecated
	public int getCount(JdbcTemplate jdbcTemplate, String table, String where){
		String sql ="";
		try {
			where = (StringUtils.isEmpty(where) ? "" : String.format("where %s", where));
			sql = String.format("select count(1) as total from %s %s", table, where);
			Map<String, Object> map = jdbcTemplate.queryForMap(sql);
			int total = Integer.parseInt(map.get("total").toString());
			return total;
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, new Object[]{});
			Log.error(DataBaseFactory.class, ex.getMessage());
			return 0;
		}
	}

	/**
	 * 获取表记录数量（带参数化查询）
	 * @param jdbcTemplate JDBC模板
	 * @param table 表名
	 * @param where WHERE条件（不包含where关键字，使用?作为占位符）
	 * @param args 参数值
	 * @return 记录数量
	 */
	public int getCount(JdbcTemplate jdbcTemplate, String table, String where, Object... args){
		String sql = "";
		try {
			if (StringUtils.isEmpty(where)) {
				sql = String.format("select count(1) as total from %s", table);
				Map<String, Object> map = jdbcTemplate.queryForMap(sql);
				int total = Integer.parseInt(map.get("total").toString());
				return total;
			} else {
				sql = String.format("select count(1) as total from %s where %s", table, where);
				Map<String, Object> map = jdbcTemplate.queryForMap(sql, args);
				int total = Integer.parseInt(map.get("total").toString());
				return total;
			}
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, ex.getMessage());
			return 0;
		}
	}

	/**
	 * 通过完整SQL获取记录数量（带参数化查询）
	 * @param jdbcTemplate JDBC模板
	 * @param sql 完整的COUNT查询SQL（使用?作为占位符）
	 * @param args 参数值
	 * @return 记录数量
	 */
	public int getCountBySQL(JdbcTemplate jdbcTemplate, String sql, Object... args) {
		try {
			Map<String, Object> map = jdbcTemplate.queryForMap(sql, args);
			// 尝试获取常见的count列名
			Object countValue = null;
			if (map.containsKey("total")) {
				countValue = map.get("total");
			} else if (map.containsKey("count")) {
				countValue = map.get("count");
			} else if (map.containsKey("cnt")) {
				countValue = map.get("cnt");
			} else {
				// 如果没有找到预期的列名，取第一个值
				countValue = map.values().iterator().next();
			}

			if (countValue != null) {
				return Integer.parseInt(countValue.toString());
			}
			return 0;
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, ex.getMessage());
			return 0;
		}
	}
	
	/**
	 * 通过完整SQL获取记录数量（指定列名，不带参数）
	 * @param jdbcTemplate JDBC模板
	 * @param sql 完整的COUNT查询SQL
	 * @param totalcolumen 结果列名
	 * @return 记录数量
	 * @deprecated 建议使用带参数的重载方法以防止SQL注入
	 */
	@Deprecated
	public int getCountBySQL(JdbcTemplate jdbcTemplate, String sql, String totalcolumen) {
		try {
			Map<String, Object> map = jdbcTemplate.queryForMap(sql);
			int total = Integer.parseInt(map.get(totalcolumen).toString());
			return total;
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, new Object[]{totalcolumen});
			Log.error(DataBaseFactory.class, ex.getMessage());
			return 0;
		}
	}

	/**
	 * 查询单个值（不带参数）
	 * @param jdbcTemplate JDBC模板
	 * @param sql SQL语句
	 * @return 查询结果的第一行第一列值，如果没有结果返回null
	 */
	public Object queryObject(JdbcTemplate jdbcTemplate, String sql) {
		return queryObject(jdbcTemplate, sql, new Object[]{});
	}

	/**
	 * 查询单个值（带参数化查询）
	 * @param jdbcTemplate JDBC模板
	 * @param sql SQL语句（使用?作为占位符）
	 * @param args 参数值
	 * @return 查询结果的第一行第一列值，如果没有结果返回null
	 */
	public Object queryObject(JdbcTemplate jdbcTemplate, String sql, Object... args) {
		try {
			List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, args);
			if (list != null && !list.isEmpty()) {
				Map<String, Object> firstRow = list.get(0);
				if (firstRow != null && !firstRow.isEmpty()) {
					// 返回第一行的第一列值
					return firstRow.values().iterator().next();
				}
			}
			return null;
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, ex.getMessage());
			return null;
		}
	}

	/**
	 * 查询单个值并转换为指定类型（不带参数）
	 * @param jdbcTemplate JDBC模板
	 * @param sql SQL语句
	 * @param requiredType 目标类型
	 * @return 转换后的值，如果没有结果或转换失败返回null
	 */
	public <T> T queryObject(JdbcTemplate jdbcTemplate, String sql, Class<T> requiredType) {
		return queryObject(jdbcTemplate, sql, requiredType, new Object[]{});
	}

	/**
	 * 查询单个值并转换为指定类型（带参数化查询）
	 * @param jdbcTemplate JDBC模板
	 * @param sql SQL语句（使用?作为占位符）
	 * @param requiredType 目标类型
	 * @param args 参数值
	 * @return 转换后的值，如果没有结果或转换失败返回null
	 */
	public <T> T queryObject(JdbcTemplate jdbcTemplate, String sql, Class<T> requiredType, Object... args) {
		try {
			Object result = queryObject(jdbcTemplate, sql, args);
			if (result == null) {
				return null;
			}

			// 类型转换
			if (requiredType.isInstance(result)) {
				return requiredType.cast(result);
			}

			// 字符串类型转换
			if (requiredType == String.class) {
				return requiredType.cast(result.toString());
			}

			// 数值类型转换
			String resultStr = result.toString();
			if (requiredType == Integer.class || requiredType == int.class) {
				return requiredType.cast(Integer.parseInt(resultStr));
			} else if (requiredType == Long.class || requiredType == long.class) {
				return requiredType.cast(Long.parseLong(resultStr));
			} else if (requiredType == Double.class || requiredType == double.class) {
				return requiredType.cast(Double.parseDouble(resultStr));
			} else if (requiredType == Float.class || requiredType == float.class) {
				return requiredType.cast(Float.parseFloat(resultStr));
			} else if (requiredType == Boolean.class || requiredType == boolean.class) {
				return requiredType.cast(Boolean.parseBoolean(resultStr));
			} else if (requiredType == java.math.BigDecimal.class) {
				return requiredType.cast(new java.math.BigDecimal(resultStr));
			}

			// 如果无法转换，返回null
			Log.warn(DataBaseFactory.class, "无法将查询结果转换为类型: " + requiredType.getName());
			return null;

		} catch (Exception ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, "查询单个值失败: " + ex.getMessage());
			return null;
		}
	}

	/**
	 * 查询Map对象（不带参数）
	 * @param jdbcTemplate JDBC模板
	 * @param sql SQL语句
	 * @return 查询结果的第一行Map，如果没有结果返回null
	 */
	public Map<String, Object> queryMap(JdbcTemplate jdbcTemplate, String sql) {
		return queryMap(jdbcTemplate, sql, new Object[]{});
	}

	/**
	 * 查询Map对象（带参数化查询）
	 * @param jdbcTemplate JDBC模板
	 * @param sql SQL语句（使用?作为占位符）
	 * @param args 参数值
	 * @return 查询结果的第一行Map，如果没有结果返回null
	 */
	public Map<String, Object> queryMap(JdbcTemplate jdbcTemplate, String sql, Object... args) {
		try {
			List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, args);
			if (list != null && !list.isEmpty()) {
				return list.get(0);
			}
			return null;
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, ex.getMessage());
			return null;
		}
	}

	/**
	 * 查询Map列表（不带参数）
	 * @param jdbcTemplate JDBC模板
	 * @param sql SQL语句
	 * @return 查询结果的Map列表
	 */
	public List<Map<String, Object>> queryMapList(JdbcTemplate jdbcTemplate, String sql) {
		return queryMapList(jdbcTemplate, sql, new Object[]{});
	}

	/**
	 * 查询Map列表（带参数化查询）
	 * @param jdbcTemplate JDBC模板
	 * @param sql SQL语句（使用?作为占位符）
	 * @param args 参数值
	 * @return 查询结果的Map列表
	 */
	public List<Map<String, Object>> queryMapList(JdbcTemplate jdbcTemplate, String sql, Object... args) {
		try {
			return jdbcTemplate.queryForList(sql, args);
		} catch (DataAccessException ex) {
			WriteDataBaseErrorLog(sql, args);
			Log.error(DataBaseFactory.class, ex.getMessage());
			return new ArrayList<>();
		}
	}

	public JsonData QueryStoreList(JdbcTemplate jdbcTemplate, final String stored, final CallCallableStatement callback) {
		try {
			JsonData result= jdbcTemplate.execute(new CallableStatementCreator() {
				@Override
				public CallableStatement createCallableStatement(Connection con) throws java.sql.SQLException {
					java.sql.CallableStatement cs=con.prepareCall(stored);
					callback.before(cs);
					return cs;
				}
	        }, new CallableStatementCallback<JsonData>() {
				@Override
				public JsonData doInCallableStatement(CallableStatement cs) throws java.sql.SQLException, DataAccessException {
					JSONArray list=new JSONArray();
					 cs.execute();
					 int total=cs.getInt("total");
					 ResultSet rs =  cs.getResultSet();
			         while (rs.next()) {
			        	JSONObject row=new JSONObject();
			        	ResultSetMetaData rsmd = rs.getMetaData(); 
			        	for(int i=1;i<=rsmd.getColumnCount();i++) {
			        	   String name=rsmd.getColumnLabel(i);
			        	   row.put(name, rs.getObject(i));
			        	}
			        	list.add(row);
			           }   
			           rs.close(); 
			           
			         return Json.getJsonData(true, "", list, total);
				}
	        }); 
			return result;
		} catch (Exception ex) {
			Log.error(DataBaseFactory.class, ex.getMessage());
			return Json.getJsonData(false, ex.getMessage());
		}
	}
	
	public void WriteDataBaseErrorLog(String sql, Object... args) {
		StringBuffer sbsql=new StringBuffer();
		sbsql.append("SQL语句执行出错：[");
		sbsql.append(sql);
		sbsql.append("]");
		if(args.length>0) {
			sbsql.append("，参数=");
			for(Object o : args) {
				sbsql.append(",");
				sbsql.append(o);
			}
		}
		Log.error(DataBaseFactory.class, sbsql.toString());
	}
}
