package com.ymiots.framework.baidu;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;

import org.apache.tomcat.util.http.fileupload.FileItemIterator;
import org.apache.tomcat.util.http.fileupload.FileUploadException;
import org.apache.tomcat.util.http.fileupload.disk.DiskFileItemFactory;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

public class BinaryUploader {

	public static final State save(HttpServletRequest request, Map<String, Object> conf) {
		boolean isAjaxUpload = request.getHeader( "X_Requested_With" ) != null;

		if (!ServletFileUpload.isMultipartContent(request)) {
			return new BaseState(false, AppInfo.NOT_MULTIPART_CONTENT);
		}

		ServletFileUpload upload = new ServletFileUpload(new DiskFileItemFactory());

        if ( isAjaxUpload ) {
            upload.setHeaderEncoding( "UTF-8" );
        }

		try {
			
			FileItemIterator iterator = upload.getItemIterator(request);
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
			
			Set<String> keys=multipartRequest.getFileMap().keySet();
			if(keys.size()==0) {
				return new BaseState(false, AppInfo.NOTFOUND_UPLOAD_DATA);
			}
			
			for(String key:keys) {
				List<MultipartFile> files = multipartRequest.getFiles(key);
				for(MultipartFile file:files) {
					String savePath = (String) conf.get("savePath");
					String originFileName = file.getOriginalFilename();
					String suffix = FileType.getSuffixByFilename(originFileName);
					originFileName = originFileName.substring(0,originFileName.length() - suffix.length());
					savePath = savePath + suffix;
					long maxSize = ((Long) conf.get("maxSize")).longValue();
					if (!validType(suffix, (String[]) conf.get("allowFiles"))) {
						return new BaseState(false, AppInfo.NOT_ALLOW_FILE_TYPE);
					}
					savePath = PathFormat.parse(savePath, originFileName);
					String physicalPath = (String) conf.get("rootPath") + savePath;
					InputStream is = file.getInputStream();
					State storageState = StorageManager.saveFileByInputStream(is,physicalPath, maxSize);
					is.close();
					
					if (storageState.isSuccess()) {
						storageState.putInfo("url", PathFormat.format(savePath));
						storageState.putInfo("type", suffix);
						storageState.putInfo("original", originFileName + suffix);
					}
					return storageState;
				}
			}
		} catch (FileUploadException e) {
			return new BaseState(false, AppInfo.PARSE_REQUEST_ERROR);
		} catch (IOException e) {
		}
		return new BaseState(false, AppInfo.IO_ERROR);
	}

	private static boolean validType(String type, String[] allowTypes) {
		List<String> list = Arrays.asList(allowTypes);

		return list.contains(type);
	}
}
