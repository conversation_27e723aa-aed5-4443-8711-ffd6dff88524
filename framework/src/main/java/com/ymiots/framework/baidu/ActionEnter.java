package com.ymiots.framework.baidu;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class ActionEnter {
	
	private HttpServletRequest request = null;
	private String rootPath = null;
	private String contextPath = null;	
	private String actionType = null;
	
	@Autowired
	ConfigManager configManager;

	public void Init (HttpServletRequest request, String rootPath ) {
		this.request = request;
		this.rootPath = rootPath;
		this.actionType = request.getParameter("action");
		this.contextPath = request.getContextPath();
		configManager.InitConfigManager(this.rootPath, this.contextPath, request.getRequestURI());
	}
	
	public String exec () {	
		String callbackName = this.request.getParameter("callback");	
		if (callbackName != null ) {
			if ( !validCallbackName( callbackName ) ) {
				String result=new BaseState( false, AppInfo.ILLEGAL ).toJSONString();
				return result;
			}
			String result=callbackName+"("+this.GetinVoke()+");";
			return result;
		} else {
			String result= this.GetinVoke();
			return result;
		}
	}
	
	public String GetinVoke() {	
		if ( actionType == null || !ActionMap.mapping.containsKey( actionType ) ) {
			return new BaseState( false, AppInfo.INVALID_ACTION ).toJSONString();
		}
		if ( this.configManager == null || !this.configManager.valid() ) {
			return new BaseState( false, AppInfo.CONFIG_ERROR ).toJSONString();
		}	
		State state = null;	
		int actionCode = ActionMap.getType( this.actionType );		
		Map<String, Object> conf = null;		
		switch (actionCode) {
			case ActionMap.CONFIG:
				return this.configManager.getAllConfig().toString();
			case ActionMap.UPLOAD_IMAGE:
				conf = this.configManager.getConfig( actionCode );
				state = new Uploader( request, conf ).doExec();
				break;
			case ActionMap.UPLOAD_SCRAWL:
				conf = this.configManager.getConfig( actionCode );
				state = new Uploader( request, conf ).doExec();
				break;
			case ActionMap.UPLOAD_VIDEO:
				conf = this.configManager.getConfig( actionCode );
				state = new Uploader( request, conf ).doExec();
				break;
			case ActionMap.UPLOAD_FILE:
				conf = this.configManager.getConfig( actionCode );
				state = new Uploader( request, conf ).doExec();
				break;
				
			case ActionMap.CATCH_IMAGE:
				conf = configManager.getConfig( actionCode );
				String[] list = this.request.getParameterValues( (String)conf.get( "fieldName" ) );
				state = new ImageHunter( conf ).capture( list );
				break;
				
			case ActionMap.LIST_IMAGE:
			case ActionMap.LIST_FILE:
				conf = configManager.getConfig( actionCode );
				int start = this.getStartIndex();
				state = new FileManager( conf ).listFile( start );
				break;
		}	
		return state.toJSONString();
	}
	
	public int getStartIndex () {
		
		String start = this.request.getParameter( "start" );
		
		try {
			return Integer.parseInt( start );
		} catch ( Exception e ) {
			return 0;
		}
		
	}
	
	/**
	 * callback参数验证
	 */
	public boolean validCallbackName ( String name ) {
		
		if ( name.matches( "^[a-zA-Z_]+[\\w0-9_]*$" ) ) {
			return true;
		}
		
		return false;
		
	}
	
}