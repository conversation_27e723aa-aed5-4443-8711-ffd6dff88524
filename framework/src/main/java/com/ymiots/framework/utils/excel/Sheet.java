package com.ymiots.framework.utils.excel;


import java.util.*;
import java.util.stream.Collectors;

/**
 * 表格数据
 *
 * <AUTHOR>
 * @date 2023/06/25
 */
public class Sheet<E extends Row> {

    /**
     * Map<行号, 行数据>
     */
    private final LinkedHashMap<Integer, E> data;

    private final Class<E> rowClass;

    /**
     * 有效数据
     */
    private Map<Integer, E> validData;

    /**
     * 校验器责任链
     */
    private final List<Validator<E, ?, ? extends Collection<?>>> validators = new ArrayList<>();

    public Sheet(LinkedHashMap<Integer, E> data, Class<E> rowClass) {
        this.data = data;
        this.rowClass = rowClass;
    }

    public Sheet(List<E> data, Class<E> rowClass) {
        this.data = new LinkedHashMap<>((int) (data.size() * 1.5));
        int row = 0;
        for (E d : data) {
            this.data.put(row, d);
            row++;
        }
        this.rowClass = rowClass;
    }

    Class<E> getRowClass() {
        return rowClass;
    }

    public LinkedHashMap<Integer, E> getData() {
        return data;
    }

    public boolean validate() {
        validData = new HashMap<>(data);
        boolean allPass = true;
        LinkedHashMap<Integer, E> tmpData = data;
        for (Validator<E, ?, ?> validator : validators) {
            boolean allValid = validator
                    .setValidData(validData)
                    .validate(tmpData)
                    .isAllValid();
            boolean errorRowsContinueValid = validator.isErrorRowsContinueValid();
            if (!allValid && !errorRowsContinueValid) {
                // 有错误行，且错误行不参与后续校验
                tmpData = validator.getValidRows();
            }
            if (!allValid) {
                // 存在无效行
                for (Integer rowIndex : validator.errorRows.keySet()) {
                    // 剔除无效行
                    validData.remove(rowIndex);
                }
            }
            allPass &= allValid;
        }
        return allPass;
    }

    public Map<Integer, E> getValidData() {
        return this.validData;
    }

    public Sheet<E> addValidator(Validator<E, ?,  ? extends Collection<?>> validator) {
        validators.add(validator);
        return this;
    }
}
