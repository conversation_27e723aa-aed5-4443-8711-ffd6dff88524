package com.ymiots.framework.utils.excel.converter;

import com.ymiots.framework.constant.enums.WeekDay;


/**
 * 星期Enum 转换器
 *
 * <AUTHOR>
 * @date 2023/06/27
 */
public class WeekDayEnumConverter implements StringEnumConverter<WeekDay> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return WeekDay.class;
    }

    @Override
    public WeekDay getEnum(String val) {
        WeekDay weekDay = WeekDay.getByName(val);
        if (weekDay == null) {
            throw new UnsupportedOperationException("当前星期数值无法被识别");
        }
        return weekDay;
    }

    @Override
    public String getFillValue(WeekDay val) {
        return val == null ? null : val.getName();
    }
}
