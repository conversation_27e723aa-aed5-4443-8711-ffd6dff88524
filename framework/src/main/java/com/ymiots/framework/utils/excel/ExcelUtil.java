package com.ymiots.framework.utils.excel;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.ymiots.framework.utils.excel.handler.TitleSheetWriteHandler;

import java.io.InputStream;
import java.util.*;

/**
 * 基于EasyExcel操作的工具类
 *
 * <AUTHOR>
 * @date 2023/06/25
 */
public class ExcelUtil {

    private ExcelUtil() {}

    public static <E extends Row> Sheet<E> read(String fileName, Class<E> rowClass) {

        LinkedHashMap<Integer, E> data = new LinkedHashMap<>(128);
        EasyExcel.read(fileName, rowClass, new AnalysisEventListener<E>() {

            @Override
            public void invoke(E row, AnalysisContext analysisContext) {
                if (row != null) {
                    Integer currentRowNum = analysisContext.getCurrentRowNum();
                    row.setRowNum(currentRowNum);
                    data.put(currentRowNum, row);
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {}
        }).doReadAll();
        return new Sheet<>(data, rowClass);
    }

    public static <E extends Row> Sheet<E> read(InputStream inputStream, Class<E> rowClass) {
        LinkedHashMap<Integer, E> data = new LinkedHashMap<>(128);
        EasyExcel.read(inputStream, rowClass, new AnalysisEventListener<E>() {

            @Override
            public void invoke(E row, AnalysisContext analysisContext) {
                if (row != null) {
                    Integer currentRowNum = analysisContext.getCurrentRowNum();
                    row.setRowNum(currentRowNum);
                    data.put(currentRowNum, row);
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {}
        }).doReadAll();
        return new Sheet<>(data, rowClass);
    }

    public static <E extends Row, S, T extends Collection<S>> void write(Sheet<E> sheet, String fileName) {
        write(sheet, fileName, "default");
    }

    public static <E extends Row, S, T extends Collection<S>> void write(Sheet<E> sheet, String fileName, String sheetName) {
        FileUtil.touch(fileName);
        HorizontalCellStyleStrategy cellStyle = CommonCellStyleStrategy.getCellStyle();
        EasyExcel.write(fileName, sheet.getRowClass())
                .registerWriteHandler(cellStyle)
                .sheet(sheetName)
                .doWrite(() -> sheet.getData().values());
    }

    public static <E extends Row, S, T extends Collection<S>> void write(Sheet<E> sheet, String fileName,String title,int lastCol,String start, String end) {
        FileUtil.touch(fileName);
        HorizontalCellStyleStrategy cellStyle = CommonCellStyleStrategy.getCellStyle();
        EasyExcel.write(fileName,sheet.getRowClass())
                .registerWriteHandler(cellStyle)
                .registerWriteHandler(new TitleSheetWriteHandler(title, start, end, lastCol))
                .sheet(title)
                .relativeHeadRowIndex(3)
                .doWrite(() -> sheet.getData().values());
    }

}
