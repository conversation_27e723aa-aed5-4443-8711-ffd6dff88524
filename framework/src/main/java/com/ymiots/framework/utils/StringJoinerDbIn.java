package com.ymiots.framework.utils;


import java.util.Collection;
import java.util.StringJoiner;

/**
 * 数据库in查询的StringJoiner
 *
 * <AUTHOR>
 * @date 2023/09/13
 */
public class StringJoinerDbIn {

    private final StringJoiner joiner;

    public StringJoinerDbIn(Collection<String> data) {
        joiner = new StringJoiner("','", "('", "')");
        for (CharSequence c : data) {
            joiner.add(c);
        }
    }

    @Override
    public String toString() {
        return joiner.toString();
    }
}
