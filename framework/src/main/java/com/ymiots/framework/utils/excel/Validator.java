package com.ymiots.framework.utils.excel;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Getter;

import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 表格校验器
 *
 * <AUTHOR>
 * @date 2023/06/25
 */
public abstract class Validator<E extends Row, S, T extends Collection<S>> {

    private Map<Integer, E> sheetData;

    /**
     * 校验出错行
     */
    Map<Integer, E> errorRows;

    /**
     * 校验合法行
     */
    private LinkedHashMap<Integer, E> validRows;

    /**
     * 错误行继续参与后续校验
     */
    @Getter
    private boolean errorRowsContinueValid = true;

    /**
     * 从Excel行数据信息，去数据库中获取需要被校验的内容
     */
    public abstract T getSources(Map<Integer, E> validRows);

    public Validator<E, S, T> setErrorRowsContinueValid(boolean errorRowsContinueValid) {
        this.errorRowsContinueValid = errorRowsContinueValid;
        return this;
    }

    Validator<E, S, T> setValidData(Map<Integer, E> sheetData) {
        this.sheetData = sheetData;
        int size = this.sheetData.size();
        errorRows = new HashMap<>((int) (size * 1.5));
        validRows = new LinkedHashMap<>((int) (size * 1.5));
        return this;
    }

    /**
     * 数据校验
     */
    Validator<E, S, T> validate(LinkedHashMap<Integer, E> data) {
        T sources = getSources(data);
        sheetData.forEach((index, row) -> {
            if (!isValid(row, sources, index)) {
                onError(row);
                // 如果数据非法
                errorRows.put(index, row);
            }
            validRows.put(index, row);
        });
        return this;
    }

    /**
     * 数据非法时
     */
    public abstract void onError(E row);

    /**
     * 校验数据是否非法
     */
    public abstract boolean isValid(E row, T sources, Integer rowIndex);

    public LinkedHashMap<Integer, E> getValidRows() {
        return this.validRows;
    }

    boolean isAllValid() {
        return CollectionUtil.isEmpty(errorRows);
    }

}
