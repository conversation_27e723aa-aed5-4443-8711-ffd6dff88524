package com.ymiots.framework.utils;

import cn.hutool.core.util.ArrayUtil;

import java.util.StringJoiner;

import static com.ymiots.framework.config.SystemEnv.TEMP_DIR;
import static com.ymiots.framework.config.SystemEnv.currentRunPath;

/**
 * 文件路径工具
 *
 * <AUTHOR>
 * @date 2023/06/27
 */
public class FileUtil {

    private static final String[] RUN_PATH_ARRAY = new String[] {currentRunPath};

    /**
     * 文件路径拼接，相对路径为开头
     */
    public static String filePath(String... paths) {
        StringJoiner filePathJoiner = new StringJoiner(cn.hutool.core.io.FileUtil.FILE_SEPARATOR);
        for (String path : paths) {
            filePathJoiner.add(path);
        }
        return filePathJoiner.toString();
    }

    public static String runPathWith(String... paths) {
        paths = ArrayUtil.addAll(RUN_PATH_ARRAY, paths);
        return filePath(paths);
    }

    public static String tempPathWith(String... paths) {
        paths = ArrayUtil.addAll(new String[] {getTmpDirPath()}, paths);
        return filePath(paths);
    }

    /**
     * 获取虚拟机的临时路径
     */
    public static String getTmpDirPath() {
        return TEMP_DIR;
    }
}
