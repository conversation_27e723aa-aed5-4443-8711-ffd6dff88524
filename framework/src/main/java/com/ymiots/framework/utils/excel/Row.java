package com.ymiots.framework.utils.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ymiots.framework.utils.excel.converter.StringJoinerConverter;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.StringJoiner;


/**
 * 表格行数据
 *
 * <AUTHOR>
 * @date 2023/06/25
 */
@Data
@Accessors(chain = true)
public class Row {

    /**
     * 行号
     */
    @ExcelIgnore
    private Integer rowNum;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息", converter = StringJoinerConverter.class)
    private StringJoiner errorMsg = new StringJoiner(",");
}
