package com.ymiots.framework.utils.excel.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.StringJoiner;

/**
 * StringJoiner 转换器
 *
 * <AUTHOR>
 * @date 2023/07/10
 */
public class StringJoinerConverter implements Converter<StringJoiner> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return StringJoiner.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(StringJoiner value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(value == null ? "" : value.toString());
    }
}
