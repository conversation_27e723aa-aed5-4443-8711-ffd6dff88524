server:
#  port: 2018
#  tomcat:
#    max-http-form-post-size: 10485760
spring:
  datasource:
    #    url: *******************************************************************************************************************
    #    username: root
    #    password: yunmai2019
    #    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      initial-size: 20
      min-idle: 10
      max-wait: 60000
      max-active: 300
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 1800000 # 配置一个连接在池中最大生存的时间，单位：毫秒
      keep-alive-between-time-millis: 40000
      time-between-eviction-runs-millis: 30000
      validation-query: SELECT 1
      keep-alive: true

  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  mvc:
    converters:
      preferred-json-mapper: fastjson
  mail:
    default-encoding: utf-8
    protocol: smtp
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    mode: HTML5
    encoding: UTF-8
    cache: false
    servlet:
      content-type: text/html

#webconfig:
#  processtask: 1
#  serverid: ""
#  appversion: V2.0.0
#  uploaddir: D:/upload/
#  domain: http://*************:2018
#  resourceDomain: ""
#  weixinDomain: http://wxdemo.ymiots.com
#  parkDomain: ""
#

okhttp:
  connect-timeout: 30
  read-timeout: 30
  write-timeout: 30
  max-idle-connections: 10
  keep-alive-duration: 300

jetcache:
  statIntervalMinutes: 15  # 控制台输出统计数据，统计间隔，0表示不统计
  areaInCacheName: false
  local:
    default:
      keyConvertor: fastjson
      type: linkedhashmap
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson
      uri: redis://:${spring.redis.password}@${spring.redis.host}:${spring.redis.port}/${spring.redis.jet-cache-db-no}
