<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<artifactId>framework</artifactId>

	<parent>
		<groupId>com.yunmai</groupId>
		<artifactId>campus</artifactId>
		<version>5.0</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<dependencies>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>${apache.commons-lang3.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>com.artofsolving</groupId>
			<artifactId>jodconverter</artifactId>
			<version>${jodconverter.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>${apache-httpclient.version}</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>commons-net</groupId>-->
<!--			<artifactId>commons-net</artifactId>-->
<!--			<version>3.6</version>-->
<!--		</dependency>-->
		<!-- https://mvnrepository.com/artifact/com.google.zxing/core -->


		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
			<version>${jdbc.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>${spring-web.version}</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql.connector.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>${druid-spring-boot-starter.version}</version>
		</dependency>

		<!--		授权验证界面后端渲染-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>

<!--		二维码-->
		<dependency>
		    <groupId>com.google.zxing</groupId>
		    <artifactId>core</artifactId>
		    <version>${google-zxing-core.version}</version>
		</dependency>
<!--		JavaSE-->
		<dependency>
		    <groupId>com.google.zxing</groupId>
		    <artifactId>javase</artifactId>
		    <version>${javase.version}</version>
		</dependency>
<!--		压缩包操作-->
		<dependency>
		    <groupId>com.github.junrar</groupId>
		    <artifactId>junrar</artifactId>
		    <version>${junrar.version}</version>
		</dependency>
		<!--缩略图库-->
		<dependency>
		    <groupId>net.coobird</groupId>
		    <artifactId>thumbnailator</artifactId>
		    <version>${thumbnailator.version}</version>
		</dependency>

<!--		XML操作-->
		<dependency>
		    <groupId>dom4j</groupId>
		    <artifactId>dom4j</artifactId>
		    <version>${dom4j.version}</version>
		</dependency>

		<!-- excel操作 -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>${apache-poi.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>${apache-poi-ooxml.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>${apache-poi.version}</version>
		</dependency>
		<!-- excel操作end -->

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>${hutool.version}</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<dependency>
		    <groupId>com.aliyun</groupId>
		    <artifactId>aliyun-java-sdk-core</artifactId>
		    <version>4.0.6</version>
		</dependency>
		<dependency>
		    <groupId>com.aliyun</groupId>
		    <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
		    <version>1.1.0</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>${easy-excel.version}</version>
		</dependency>

		<!--         jetcache 缓存-->
		<dependency>
			<groupId>com.alicp.jetcache</groupId>
			<artifactId>jetcache-starter-redis-lettuce</artifactId>
			<version>${jetcache.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alicp.jetcache</groupId>
			<artifactId>jetcache-redis-lettuce</artifactId>
			<version>${jetcache.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alicp.jetcache</groupId>
			<artifactId>jetcache-anno</artifactId>
			<version>${jetcache-anno.version}</version>
		</dependency>

		<!--		邮件-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
			<version>${spring-boot-starter-mail.version}</version>
		</dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>${okhttp.version}</version>
		</dependency>

		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson</artifactId>
			<version>${redisson.version}</version>
		</dependency>
		<dependency>
			<groupId>org.openpnp</groupId>
			<artifactId>opencv</artifactId>
			<version>${opencv.version}</version>
		</dependency>
		<!-- 加载lib目录下的opencv包 -->
	</dependencies>


</project>
