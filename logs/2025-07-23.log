2025-07-23 09:03:13.467 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-23 09:03:13.467 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 34604 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-23 09:03:13.470 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-23 09:03:16.088 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 09:03:16.091 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 09:03:16.364 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 258 ms. Found 0 Redis repository interfaces.
2025-07-23 09:03:16.947 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$d5aa84d1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 09:03:17.039 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$54b0ca49] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 09:03:17.335 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-23 09:03:17.346 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-23 09:03:17.347 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 09:03:17.347 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-23 09:03:17.589 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-23 09:03:17.589 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4011 ms
2025-07-23 09:03:17.696 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-23 09:03:17.776 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-23 09:03:19.727 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-23 09:03:20.380 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-23 09:03:22.751 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-23 09:03:22.910 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-23 09:03:22.910 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-23 09:03:23.993 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 32 个待执行的重试任务
2025-07-23 09:03:24.232 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 32 个待执行的重试任务
2025-07-23 09:03:24.232 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-23 09:03:24.834 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-23 09:03:25.072 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-23 09:03:25.105 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-23 09:03:25.118 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-23 09:03:25.252 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-23 09:03:25.846 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-23 09:03:25.854 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-23 09:03:26.036 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$6f44bb9e - 计划任务最多执行：66个
2025-07-23 09:03:26.103 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-23 09:03:26.108 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-23 09:03:26.109 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-23 09:03:26.111 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-23 09:03:26.114 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-23 09:03:26.114 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-23 09:03:26.117 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-23 09:03:26.119 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-23 09:03:26.122 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-23 09:03:26.124 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-23 09:03:26.127 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-23 09:03:26.129 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-23 09:03:26.132 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-23 09:03:26.134 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-23 09:03:26.137 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-23 09:03:26.140 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-23 09:03:26.142 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-23 09:03:26.142 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-23 09:03:26.145 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-23 09:03:26.172 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 13.349 seconds (JVM running for 15.281)
2025-07-23 09:03:26.576 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-23 09:15:00.022 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 09:03:25,250 to 2025-07-23 09:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 09:17:34.992 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 09:17:34.993 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-23 09:17:34.995 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-23 09:17:42.656 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-23 09:20:00.003 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 09:20:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 09:20:00.042 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 09:20:00.061 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 09:20:00.061 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 09:30:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 09:15:00,015 to 2025-07-23 09:30:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 09:40:00.005 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 09:40:00.030 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 09:40:00.051 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 09:40:00.070 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 09:40:00.071 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 09:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 09:30:00,008 to 2025-07-23 09:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 10:00:00.010 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-23 10:00:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 09:45:00,010 to 2025-07-23 10:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 10:00:00.031 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-23 10:00:00.080 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-23 10:00:00.080 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-23 10:00:00.173 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 10:00:00.193 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 10:00:00.210 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 10:00:00.231 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 10:00:00.231 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 10:07:57.441 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 64700 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-23 10:07:57.445 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-07-23 10:07:58.658 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-23 10:07:58.660 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 10:07:58.781 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 108 ms. Found 0 Redis repository interfaces.
2025-07-23 10:07:59.730 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-07-23 10:07:59.744 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-07-23 10:07:59.744 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 10:07:59.745 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-23 10:07:59.994 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-23 10:07:59.994 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2493 ms
2025-07-23 10:08:00.111 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-23 10:08:00.175 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-23 10:08:02.121 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-23 10:08:02.718 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-23 10:08:03.227 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-23 10:08:03.806 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-23 10:08:03.806 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-23 10:08:10.040 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-23 10:08:10.583 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-23 10:08:10.680 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-23 10:08:10.712 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-23 10:08:12.238 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-23 10:08:13.616 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-07-23 10:08:13.662 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-07-23 10:08:14.626 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 17.624 seconds (JVM running for 18.844)
2025-07-23 10:10:35.549 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 10:10:35.549 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-23 10:10:35.552 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-23 10:10:40.928 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"816DD782F79DA8D9B93EE43C749CE514","mpAppId":"wx9fa8023ab8b47b04","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1753236640895}
2025-07-23 10:10:43.670 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK5-OTC1vmLgClSJLnEaqvUwc
2025-07-23 10:10:43.698 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-07-23 10:11:28.883 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\11d2cd9a-b8ac-4c8f-b11c-2a0db64bd778.jpg
2025-07-23 10:11:36.203 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\11d2cd9a-b8ac-4c8f-b11c-2a0db64bd778.jpg
2025-07-23 10:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 10:00:00,010 to 2025-07-23 10:15:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 10:15:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 10:08:12,232 to 2025-07-23 10:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 10:20:00.001 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 10:20:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 10:20:00.061 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 10:20:00.088 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 10:20:00.088 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 10:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 10:15:00,008 to 2025-07-23 10:30:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 10:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 10:15:00,010 to 2025-07-23 10:30:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 10:40:00.005 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 10:40:00.023 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 10:40:00.040 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 10:40:00.055 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 10:40:00.056 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 10:45:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 10:30:00,013 to 2025-07-23 10:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 10:45:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 10:30:00,013 to 2025-07-23 10:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 11:00:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 10:45:00,012 to 2025-07-23 11:00:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 11:00:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 11:00:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 10:45:00,012 to 2025-07-23 11:00:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 11:00:00.024 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-07-23 11:00:00.043 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 11:00:00.062 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 11:00:00.086 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 11:00:00.086 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 11:00:00.191 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-23 11:00:00.227 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-23 11:00:00.250 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-23 11:00:00.250 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-23 11:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 11:00:00,012 to 2025-07-23 11:15:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 11:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 11:00:00,012 to 2025-07-23 11:15:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 11:20:00.003 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 11:20:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 11:20:00.048 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 11:20:00.067 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 11:20:00.068 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 11:30:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 11:15:00,008 to 2025-07-23 11:30:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 11:30:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 11:15:00,008 to 2025-07-23 11:30:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 11:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 11:40:00.032 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 11:40:00.054 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 11:40:00.074 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 11:40:00.075 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 11:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 11:30:00,006 to 2025-07-23 11:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 11:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 11:30:00,006 to 2025-07-23 11:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 12:00:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 12:00:00.010 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-07-23 12:00:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 11:45:00,015 to 2025-07-23 12:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 12:00:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 11:45:00,015 to 2025-07-23 12:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 12:00:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 12:00:00.049 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 12:00:00.066 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 12:00:00.067 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 12:00:00.148 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-23 12:00:00.163 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-23 12:00:00.179 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-23 12:00:00.180 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-23 12:12:23.282 INFO  org.apache.coyote.http11.Http11Processor - Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in method name [0x160x030x010x000xee0x010x000x000xea0x030x030x090x810xb00x820x06a0x180xba.0xd00xb3P0xb78~,00xe00x190x030x14o%0x980x1e0x990xd60x88Am0xe30x0c ]. HTTP method names must be tokens
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:419)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:271)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-07-23 12:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 12:00:00,010 to 2025-07-23 12:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 12:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 12:00:00,010 to 2025-07-23 12:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 12:20:00.013 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 12:20:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 12:20:00.049 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 12:20:00.064 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 12:20:00.065 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 12:30:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 12:15:00,015 to 2025-07-23 12:30:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 12:30:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 12:15:00,015 to 2025-07-23 12:30:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 12:40:00.001 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 12:40:00.018 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 12:40:00.034 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 12:40:00.048 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 12:40:00.049 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 13:37:02.357 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 12:30:00,014 to 2025-07-23 13:37:02,357
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 13:37:02.357 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-07-23 13:37:02.357 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 12:30:00,014 to 2025-07-23 13:37:02,357
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 13:37:02.358 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 13:37:02,357 to 2025-07-23 13:37:02,358
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 13:37:02.358 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 13:37:02,358 to 2025-07-23 13:37:02,358
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 13:37:02.358 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 13:37:02,358 to 2025-07-23 13:37:02,358
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 13:37:02.359 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 13:37:02,357 to 2025-07-23 13:37:02,358
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 13:37:02.360 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 13:37:02,358 to 2025-07-23 13:37:02,360
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 13:37:02.361 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 13:37:02,360 to 2025-07-23 13:37:02,360
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 13:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 13:37:02,358 to 2025-07-23 13:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 13:45:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 13:37:02,360 to 2025-07-23 13:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 13:52:02.361 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 13:52:02.398 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 13:52:02.418 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 13:52:02.439 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 13:52:02.439 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 13:52:02.533 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-23 13:52:02.554 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-23 13:52:02.574 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-23 13:52:02.574 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-23 14:00:00.008 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-07-23 14:00:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 13:45:00,016 to 2025-07-23 14:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 14:00:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 13:45:00,016 to 2025-07-23 14:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 14:00:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-23 14:00:00.036 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-23 14:00:00.063 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-23 14:00:00.084 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-23 14:00:00.084 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-23 14:00:00.188 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-23 14:00:00.207 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-23 14:00:00.225 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-23 14:00:00.225 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-23 14:03:05.524 INFO  c.y.campusos.controller.hotel.WECostController - 生成打印内容 - 日期: 2025-07-23至2025-07-23, 区域: 001001002001
2025-07-23 14:03:05.536 INFO  c.y.campusos.service.waterctrl.WECostExportService - 生成打印内容 - 日期: 2025-07-23至2025-07-23, 区域: 001001002001
2025-07-23 14:03:05.537 INFO  c.y.campusos.service.waterctrl.WECostExportService - 执行查询SQL: SELECT * FROM tb_equipment_reading_records r join tb_equipment_cost_calculation c on r.uid = c.reading_record_id WHERE r.status = 1 AND DATE(r.reading_date) >= '2025-07-23' AND DATE(r.reading_date) <= '2025-07-23' AND r.area_code = '001001002001' ORDER BY r.area_code, r.equipment_code, r.reading_date
2025-07-23 14:15:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 14:00:00,008 to 2025-07-23 14:15:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-23 14:15:00.020 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-23 14:00:00,008 to 2025-07-23 14:15:00,020
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

