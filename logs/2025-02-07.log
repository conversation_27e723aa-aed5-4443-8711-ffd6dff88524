2025-02-07 08:48:06.627 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 08:48:06.627 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 57556 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 08:48:06.627 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 08:48:08.891 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 08:48:08.891 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 08:48:09.097 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 195 ms. Found 0 Redis repository interfaces.
2025-02-07 08:48:09.569 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1002cf41] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 08:48:09.631 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$8f0914b9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 08:48:09.837 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 08:48:09.844 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 08:48:09.845 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 08:48:09.845 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 08:48:10.028 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 08:48:10.028 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3355 ms
2025-02-07 08:48:10.111 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 08:48:10.434 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 08:48:12.318 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 08:48:12.881 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 08:48:12.881 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 08:48:13.568 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 08:48:13.731 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 08:48:13.758 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 08:48:13.763 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 08:48:13.926 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 08:48:14.274 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 08:48:14.279 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 08:48:14.334 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$a99d060e - 计划任务最多执行：0个
2025-02-07 08:48:14.353 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 8.102 seconds (JVM running for 9.455)
2025-02-07 08:48:14.762 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 09:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 08:48:13,924 to 2025-02-07 09:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 09:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 09:00:00,008 to 2025-02-07 09:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 09:30:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 09:15:00,015 to 2025-02-07 09:30:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 09:45:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 09:30:00,011 to 2025-02-07 09:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 10:00:00.001 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 09:45:00,015 to 2025-02-07 10:00:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 10:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 10:00:00,001 to 2025-02-07 10:15:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 10:30:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 10:15:00,009 to 2025-02-07 10:30:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 10:45:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 10:30:00,007 to 2025-02-07 10:45:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 11:00:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 10:45:00,004 to 2025-02-07 11:00:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 11:15:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 11:00:00,016 to 2025-02-07 11:15:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 11:30:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 11:15:00,006 to 2025-02-07 11:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 11:37:27.819 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 58184 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 11:37:27.819 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 11:37:27.819 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 11:37:29.688 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 11:37:29.688 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 11:37:29.876 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 172 ms. Found 0 Redis repository interfaces.
2025-02-07 11:37:30.302 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$67bc50a3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:37:30.351 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e6c2961b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:37:30.575 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 11:37:30.583 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 11:37:30.583 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 11:37:30.583 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 11:37:30.733 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 11:37:30.733 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2865 ms
2025-02-07 11:37:30.811 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 11:37:31.066 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 11:37:33.043 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 11:37:33.544 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 11:37:33.544 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 11:37:34.209 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 11:37:34.347 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 11:37:34.364 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 11:37:34.379 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 11:37:34.516 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 11:37:34.850 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 11:37:34.866 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 11:37:34.915 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$1568770 - 计划任务最多执行：0个
2025-02-07 11:37:34.942 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.454 seconds (JVM running for 8.496)
2025-02-07 11:37:35.292 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 11:41:32.413 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 11:41:32.413 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 60472 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 11:41:32.413 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 11:41:33.980 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 11:41:33.997 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 11:41:34.182 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 176 ms. Found 0 Redis repository interfaces.
2025-02-07 11:41:34.594 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$7a3a151] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:41:34.636 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$86a9e6c9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:41:34.827 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 11:41:34.835 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 11:41:34.836 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 11:41:34.836 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 11:41:34.974 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 11:41:34.975 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2514 ms
2025-02-07 11:41:35.050 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 11:41:35.286 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 11:41:37.019 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 11:41:37.476 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 11:41:37.476 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 11:41:38.077 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 11:41:38.211 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 11:41:38.232 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 11:41:38.237 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 11:41:38.382 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 11:41:38.717 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 11:41:38.721 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 11:41:38.782 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$a13dd81e - 计划任务最多执行：0个
2025-02-07 11:41:38.802 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.738 seconds (JVM running for 7.669)
2025-02-07 11:41:39.209 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 11:44:31.212 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 11:44:31.230 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 11:44:31.233 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 11:44:31.236 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-02-07 11:44:56.575 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 25996 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 11:44:56.575 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 11:44:56.584 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 11:44:58.161 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 11:44:58.161 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 11:44:58.338 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 157 ms. Found 0 Redis repository interfaces.
2025-02-07 11:44:58.763 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f9995075] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:44:58.794 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$789f95ed] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:44:58.984 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 11:44:58.984 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 11:44:58.984 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 11:44:58.984 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 11:44:59.126 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 11:44:59.126 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2499 ms
2025-02-07 11:44:59.208 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 11:44:59.442 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 11:45:01.313 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 11:45:01.789 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 11:45:01.789 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 11:45:02.437 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 11:45:02.566 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 11:45:02.601 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 11:45:02.601 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 11:45:02.754 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 11:45:03.155 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 11:45:03.155 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 11:45:03.213 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$93338742 - 计划任务最多执行：0个
2025-02-07 11:45:03.247 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.996 seconds (JVM running for 7.952)
2025-02-07 11:45:03.365 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 11:45:03.380 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 11:45:03.380 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 11:45:03.381 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-02-07 11:45:03.463 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-02-07 11:45:03.694 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 11:45:12.493 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-02-07 11:45:12.509 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-02-07 11:45:18.488 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-02-07 11:46:01.284 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 60864 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 11:46:01.284 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 11:46:01.303 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 11:46:02.872 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 11:46:02.872 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 11:46:03.061 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 171 ms. Found 0 Redis repository interfaces.
2025-02-07 11:46:03.528 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$763cd6a3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:46:03.564 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$f5431c1b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:46:03.752 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 11:46:03.752 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 11:46:03.752 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 11:46:03.752 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 11:46:03.893 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 11:46:03.893 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2546 ms
2025-02-07 11:46:03.956 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 11:46:04.177 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 11:46:05.858 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 11:46:06.329 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 11:46:06.329 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 11:46:06.941 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 11:46:07.067 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 11:46:07.101 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 11:46:07.101 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 11:46:07.251 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 11:46:07.573 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 11:46:07.573 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 11:46:07.642 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$fd70d70 - 计划任务最多执行：0个
2025-02-07 11:46:07.657 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.696 seconds (JVM running for 7.774)
2025-02-07 11:46:08.080 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 11:46:11.188 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 11:46:11.191 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 11:46:11.191 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 11:46:11.191 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-02-07 11:46:55.805 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 48644 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 11:46:55.805 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 11:46:55.805 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 11:46:57.335 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 11:46:57.335 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 11:46:57.509 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 162 ms. Found 0 Redis repository interfaces.
2025-02-07 11:46:57.916 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$582072ef] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:46:57.963 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d726b867] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:46:58.138 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 11:46:58.151 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 11:46:58.151 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 11:46:58.151 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 11:46:58.298 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 11:46:58.298 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2436 ms
2025-02-07 11:46:58.377 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 11:46:58.633 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 11:47:00.491 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 11:47:00.942 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 11:47:00.942 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 11:47:01.564 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 11:47:01.703 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 11:47:01.717 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 11:47:01.735 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 11:47:01.879 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 11:47:02.215 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 11:47:02.215 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 11:47:02.262 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$f1baa9bc - 计划任务最多执行：0个
2025-02-07 11:47:02.294 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.801 seconds (JVM running for 7.676)
2025-02-07 11:47:02.658 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 11:47:06.043 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 11:47:06.043 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 11:47:06.043 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 11:47:06.043 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-02-07 11:47:10.914 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-02-07 11:57:01.517 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 66960 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 11:57:01.517 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 11:57:01.536 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 11:57:03.113 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 11:57:03.113 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 11:57:03.318 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 182 ms. Found 0 Redis repository interfaces.
2025-02-07 11:57:03.790 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$689ad5da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:57:03.838 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e7a11b52] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:57:04.046 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 11:57:04.046 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 11:57:04.046 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 11:57:04.046 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 11:57:04.200 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 11:57:04.200 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2632 ms
2025-02-07 11:57:04.310 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 11:57:04.708 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 11:57:06.463 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 11:57:06.970 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 11:57:06.970 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 11:57:07.664 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 11:57:07.801 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 11:57:07.834 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 11:57:07.834 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 11:57:07.986 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 11:57:08.346 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 11:57:08.350 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 11:57:08.401 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$2350ca7 - 计划任务最多执行：0个
2025-02-07 11:57:08.416 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.224 seconds (JVM running for 8.242)
2025-02-07 11:57:08.817 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 11:57:10.940 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 11:57:10.946 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 11:57:10.946 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 11:57:10.947 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-02-07 11:57:18.245 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-02-07 11:57:39.225 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 65296 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 11:57:39.225 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 11:57:39.236 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 11:57:40.864 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 11:57:40.864 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 11:57:41.064 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 179 ms. Found 0 Redis repository interfaces.
2025-02-07 11:57:41.476 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$fe17e556] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:57:41.517 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7d1e2ace] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 11:57:41.701 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 11:57:41.708 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 11:57:41.708 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 11:57:41.708 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 11:57:41.854 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 11:57:41.854 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2580 ms
2025-02-07 11:57:41.929 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 11:57:42.161 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 11:57:43.807 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 11:57:44.307 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 11:57:44.307 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 11:57:44.979 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 11:57:45.122 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 11:57:45.144 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 11:57:45.151 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 11:57:45.306 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 11:57:45.763 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 11:57:45.767 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 11:57:45.839 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$97b21c23 - 计划任务最多执行：0个
2025-02-07 11:57:45.860 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.963 seconds (JVM running for 7.869)
2025-02-07 11:57:46.366 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 11:57:48.602 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 11:57:48.608 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 11:57:48.608 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 11:57:48.610 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-02-07 12:00:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 11:57:45,305 to 2025-02-07 12:00:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 12:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 12:00:00,006 to 2025-02-07 12:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 12:34:46.569 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 12:15:00,007 to 2025-02-07 12:34:46,569
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 12:45:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 12:34:46,569 to 2025-02-07 12:45:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 13:00:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 12:45:00,005 to 2025-02-07 13:00:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 13:15:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 13:00:00,017 to 2025-02-07 13:15:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 13:21:01.680 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 6980 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 13:21:01.682 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 13:21:01.682 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 13:21:03.544 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 13:21:03.544 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 13:21:03.718 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 152 ms. Found 0 Redis repository interfaces.
2025-02-07 13:21:04.157 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$cf5b4e2b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:21:04.204 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$4e6193a3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:21:04.409 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 13:21:04.409 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 13:21:04.409 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 13:21:04.409 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 13:21:04.613 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 13:21:04.613 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2899 ms
2025-02-07 13:21:04.693 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 13:21:05.025 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 13:21:06.933 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 13:21:07.412 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 13:21:07.412 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 13:21:08.076 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 13:21:08.234 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 13:21:08.250 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 13:21:08.265 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 13:21:08.429 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 13:21:08.748 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 13:21:08.764 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 13:21:08.812 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$68f584f8 - 计划任务最多执行：0个
2025-02-07 13:21:08.845 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.527 seconds (JVM running for 8.661)
2025-02-07 13:21:09.216 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 13:22:23.169 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 59024 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 13:22:23.169 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 13:22:23.175 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 13:22:24.678 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 13:22:24.678 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 13:22:24.835 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 148 ms. Found 0 Redis repository interfaces.
2025-02-07 13:22:25.247 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$dc163e14] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:22:25.290 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$5b1c838c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:22:25.472 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 13:22:25.480 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 13:22:25.480 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 13:22:25.480 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 13:22:25.642 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 13:22:25.642 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2425 ms
2025-02-07 13:22:25.721 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 13:22:25.962 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 13:22:27.556 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 13:22:28.015 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 13:22:28.015 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 13:22:28.665 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 13:22:28.798 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 13:22:28.823 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 13:22:28.829 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 13:22:28.974 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 13:22:29.290 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 13:22:29.295 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 13:22:29.356 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$75b074e1 - 计划任务最多执行：0个
2025-02-07 13:22:29.392 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.545 seconds (JVM running for 7.444)
2025-02-07 13:22:29.750 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 13:23:09.775 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 13:23:09.775 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 13:23:09.775 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 13:23:09.775 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-02-07 13:23:14.052 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-02-07 13:24:01.748 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 13:24:01.748 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 67308 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 13:24:01.753 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 13:24:03.350 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 13:24:03.350 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 13:24:03.515 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 154 ms. Found 0 Redis repository interfaces.
2025-02-07 13:24:03.906 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$e7e33306] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:24:03.954 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$66e9787e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:24:04.151 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 13:24:04.151 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 13:24:04.151 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 13:24:04.151 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 13:24:04.314 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 13:24:04.314 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2526 ms
2025-02-07 13:24:04.389 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 13:24:04.630 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 13:24:06.477 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 13:24:07.047 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 13:24:07.047 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 13:24:07.732 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 13:24:07.883 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 13:24:07.907 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 13:24:07.907 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 13:24:08.190 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 13:24:08.643 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 13:24:08.654 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 13:24:08.742 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$817d69d3 - 计划任务最多执行：0个
2025-02-07 13:24:08.754 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.346 seconds (JVM running for 8.451)
2025-02-07 13:24:09.197 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 13:24:12.457 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 13:24:12.457 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 13:24:12.457 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 13:24:12.457 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-02-07 13:24:58.376 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 18216 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 13:24:58.376 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 13:24:58.387 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 13:25:00.030 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 13:25:00.030 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 13:25:00.204 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 150 ms. Found 0 Redis repository interfaces.
2025-02-07 13:25:00.663 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$af7f18cf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:25:00.715 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$2e855e47] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:25:00.909 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 13:25:00.916 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 13:25:00.918 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 13:25:00.918 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 13:25:01.065 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 13:25:01.065 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2647 ms
2025-02-07 13:25:01.160 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 13:25:01.436 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 13:25:03.139 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 13:25:03.604 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 13:25:03.604 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 13:25:04.229 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 13:25:04.378 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 13:25:04.402 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 13:25:04.408 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 13:25:04.566 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 13:25:04.873 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 13:25:04.873 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 13:25:04.930 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$49194f9c - 计划任务最多执行：0个
2025-02-07 13:25:04.953 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.902 seconds (JVM running for 7.826)
2025-02-07 13:25:05.318 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 13:25:16.599 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 13:25:16.605 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 13:25:16.605 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 13:25:16.606 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-02-07 13:30:00.025 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 13:25:04,566 to 2025-02-07 13:30:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 13:32:39.676 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 58864 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 13:32:39.676 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 13:32:39.676 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 13:32:41.437 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 13:32:41.437 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 13:32:41.604 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 150 ms. Found 0 Redis repository interfaces.
2025-02-07 13:32:42.066 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b1acce1e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:32:42.111 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$30b31396] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:32:42.313 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 13:32:42.320 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 13:32:42.320 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 13:32:42.320 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 13:32:42.478 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 13:32:42.478 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2754 ms
2025-02-07 13:32:42.554 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 13:32:42.787 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 13:32:44.455 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 13:32:44.917 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 13:32:44.917 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 13:32:45.543 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 13:32:45.678 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 13:32:45.698 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 13:32:45.703 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 13:32:45.847 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 13:32:46.174 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 13:32:46.195 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 13:32:46.254 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$4b4704eb - 计划任务最多执行：0个
2025-02-07 13:32:46.272 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.942 seconds (JVM running for 7.925)
2025-02-07 13:32:46.795 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 13:33:01.621 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 13:33:01.626 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 13:33:01.626 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 13:33:01.628 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-02-07 13:34:23.875 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 45660 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 13:34:23.875 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 13:34:23.875 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 13:34:25.505 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 13:34:25.505 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 13:34:25.706 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 188 ms. Found 0 Redis repository interfaces.
2025-02-07 13:34:26.112 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$9b676562] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:34:26.154 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$1a6daada] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 13:34:26.336 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 13:34:26.342 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 13:34:26.342 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 13:34:26.342 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 13:34:26.502 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 13:34:26.502 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2584 ms
2025-02-07 13:34:26.578 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 13:34:26.834 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 13:34:28.844 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 13:34:29.351 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 13:34:29.351 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 13:34:30.010 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 13:34:30.161 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 13:34:30.181 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 13:34:30.187 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 13:34:30.333 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 13:34:30.645 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 13:34:30.649 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 13:34:30.702 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$35019c2f - 计划任务最多执行：0个
2025-02-07 13:34:30.721 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.188 seconds (JVM running for 8.241)
2025-02-07 13:34:31.078 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 13:34:33.604 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 13:34:33.610 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 13:34:33.610 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 13:34:33.612 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-02-07 14:51:35.323 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 14:51:35.323 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 62020 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 14:51:35.323 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 14:51:37.190 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 14:51:37.190 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 14:51:37.379 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 177 ms. Found 0 Redis repository interfaces.
2025-02-07 14:51:37.836 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$72b6c031] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 14:51:37.895 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$f1bd05a9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 14:51:38.112 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 14:51:38.121 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 14:51:38.121 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 14:51:38.121 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 14:51:38.288 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 14:51:38.288 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2917 ms
2025-02-07 14:51:38.370 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 14:51:38.686 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 14:51:40.443 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 14:51:40.897 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 14:51:40.897 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 14:51:41.593 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 14:51:41.736 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 14:51:41.765 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 14:51:41.770 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 14:51:41.933 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 14:51:42.277 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 14:51:42.283 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 14:51:42.339 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$c50f6fe - 计划任务最多执行：0个
2025-02-07 14:51:42.358 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.427 seconds (JVM running for 8.84)
2025-02-07 14:51:42.726 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 14:51:48.445 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 14:51:48.450 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 14:51:48.451 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 14:51:48.453 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-02-07 14:51:53.344 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-02-07 15:00:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 14:51:41,932 to 2025-02-07 15:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 15:15:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 15:00:00,008 to 2025-02-07 15:15:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 15:30:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 15:15:00,014 to 2025-02-07 15:30:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 15:45:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 15:30:00,002 to 2025-02-07 15:45:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 16:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 15:45:00,005 to 2025-02-07 16:00:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 16:15:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 16:00:00,015 to 2025-02-07 16:15:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 16:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 16:15:00,003 to 2025-02-07 16:30:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 16:45:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 16:30:00,016 to 2025-02-07 16:45:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 16:54:27.222 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-02-07 16:54:27.509 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-02-07 16:54:27.524 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-02-07 17:02:06.607 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 17:02:06.607 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 58400 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 17:02:06.607 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 17:02:08.570 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 17:02:08.586 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 17:02:08.825 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 226 ms. Found 0 Redis repository interfaces.
2025-02-07 17:02:09.392 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$fba62847] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 17:02:09.440 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7aac6dbf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 17:02:09.676 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 17:02:09.693 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 17:02:09.693 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 17:02:09.693 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 17:02:09.869 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 17:02:09.869 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3218 ms
2025-02-07 17:02:09.980 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 17:02:10.280 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 17:02:12.164 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 17:02:12.662 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 17:02:12.662 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 17:02:13.381 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 17:02:13.552 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 17:02:13.565 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 17:02:13.581 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 17:02:13.750 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 17:02:14.147 WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: bf3e62e8-4f7d-431f-ab3b-d3a5713f7a35

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-02-07 17:02:14.298 INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2279e7c7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4c5e123d, org.springframework.security.web.context.SecurityContextPersistenceFilter@65cf8da0, org.springframework.security.web.header.HeaderWriterFilter@7d31fb6c, org.springframework.security.web.csrf.CsrfFilter@5f0a5848, org.springframework.security.web.authentication.logout.LogoutFilter@7c4de811, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@4f7da3a2, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@2fb24ad8, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@69542112, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@5de32e00, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6195ce27, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@397b5b2d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@35a81281, org.springframework.security.web.session.SessionManagementFilter@cda988, org.springframework.security.web.access.ExceptionTranslationFilter@7641c4e7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@78c91d2a]
2025-02-07 17:02:14.346 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 17:02:14.346 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 17:02:14.425 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$95405f14 - 计划任务最多执行：0个
2025-02-07 17:02:14.448 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 8.231 seconds (JVM running for 9.94)
2025-02-07 17:02:14.826 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 17:02:33.790 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 17:02:33.813 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 17:02:33.813 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 17:02:33.818 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-02-07 17:15:00.019 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 17:02:13,750 to 2025-02-07 17:15:00,019
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 17:17:08.815 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 34424 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-07 17:17:08.815 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-07 17:17:08.815 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-07 17:17:10.674 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-07 17:17:10.674 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-07 17:17:10.869 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 179 ms. Found 0 Redis repository interfaces.
2025-02-07 17:17:11.308 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f74cea98] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 17:17:11.356 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$76533010] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-07 17:17:11.563 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-07 17:17:11.571 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-07 17:17:11.571 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-07 17:17:11.571 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-07 17:17:11.738 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-07 17:17:11.738 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2876 ms
2025-02-07 17:17:11.833 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-07 17:17:12.108 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-07 17:17:13.873 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-07 17:17:14.463 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-07 17:17:14.463 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-07 17:17:15.229 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-07 17:17:15.355 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-07 17:17:15.373 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-07 17:17:15.387 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-07 17:17:15.547 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-07 17:17:15.883 WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: fda1edbd-560f-4723-83de-52b2cc2334c2

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-02-07 17:17:16.003 INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@9cdf13e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3bede349, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d7888a5, org.springframework.security.web.header.HeaderWriterFilter@13288174, org.springframework.security.web.csrf.CsrfFilter@1cf7c055, org.springframework.security.web.authentication.logout.LogoutFilter@6abca7a6, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@585718dd, org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter@4a6d6308, org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter@501956f9, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@7d741200, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4accc3de, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1a23136f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@299c86f0, org.springframework.security.web.session.SessionManagementFilter@5b697993, org.springframework.security.web.access.ExceptionTranslationFilter@5b977aaa, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@d426e01]
2025-02-07 17:17:16.074 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-07 17:17:16.079 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-07 17:17:16.140 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$90e72165 - 计划任务最多执行：0个
2025-02-07 17:17:16.174 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.72 seconds (JVM running for 8.786)
2025-02-07 17:17:16.574 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-07 17:17:35.511 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-07 17:17:35.511 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-07 17:17:35.511 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-07 17:17:35.511 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-02-07 17:30:00.029 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 17:17:15,547 to 2025-02-07 17:30:00,029
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 18:04:09.482 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 17:30:00,029 to 2025-02-07 18:04:09,482
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 18:04:09.482 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 18:04:09,482 to 2025-02-07 18:04:09,482
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 18:16:24.925 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 18:04:09,482 to 2025-02-07 18:16:24,925
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 18:40:00.120 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 18:16:24,925 to 2025-02-07 18:40:00,120
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 18:55:43.209 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 18:40:00,120 to 2025-02-07 18:55:43,209
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 18:59:53.606 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 18:55:43,209 to 2025-02-07 18:59:53,606
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 20:46:44.478 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 18:59:53,606 to 2025-02-07 20:46:44,478
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 20:46:44.479 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 20:46:44,478 to 2025-02-07 20:46:44,479
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 20:46:44.479 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 20:46:44,479 to 2025-02-07 20:46:44,479
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 20:46:44.479 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 20:46:44,479 to 2025-02-07 20:46:44,479
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 20:46:44.479 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 20:46:44,479 to 2025-02-07 20:46:44,479
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 20:46:44.479 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 20:46:44,479 to 2025-02-07 20:46:44,479
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 20:46:44.479 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 20:46:44,479 to 2025-02-07 20:46:44,479
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-07 20:59:51.148 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-07 20:46:44,479 to 2025-02-07 20:59:51,148
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

