2025-01-13 08:35:41.201 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-13 08:35:41.204 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 54068 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-13 08:35:41.204 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-13 08:35:43.439 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-13 08:35:43.442 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-13 08:35:43.654 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 197 ms. Found 0 Redis repository interfaces.
2025-01-13 08:35:44.247 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$a497083a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-13 08:35:44.309 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$239d4db2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-13 08:35:44.753 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-13 08:35:44.764 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-13 08:35:44.765 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-13 08:35:44.765 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-13 08:35:45.025 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-13 08:35:45.026 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3760 ms
2025-01-13 08:35:45.126 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-13 08:35:49.642 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-13 08:35:52.496 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-13 08:35:53.164 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-13 08:35:53.164 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-13 08:35:54.061 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-13 08:35:54.222 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-13 08:35:54.255 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-13 08:35:54.262 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-13 08:35:54.442 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-13 08:35:54.870 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-13 08:35:54.875 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-13 08:35:54.995 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$3e313f07 - 计划任务最多执行：58个
2025-01-13 08:35:55.092 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-13 08:35:55.096 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-13 08:35:55.096 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-13 08:35:55.100 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[自动生成卡号]成功
2025-01-13 08:35:55.101 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-01-13 08:35:55.101 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-13 08:35:55.104 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-13 08:35:55.106 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-13 08:35:55.109 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-01-13 08:35:55.110 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[订餐定时删除人脸授权]成功
2025-01-13 08:35:55.112 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[大华人脸机时段下发]成功
2025-01-13 08:35:55.112 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[处理订餐人脸照片下发]成功
2025-01-13 08:35:55.115 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-13 08:35:55.117 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-13 08:35:55.119 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-13 08:35:55.122 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-01-13 08:35:55.124 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-13 08:35:55.126 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-01-13 08:35:55.126 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-13 08:35:55.128 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-01-13 08:35:55.181 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 14.429 seconds (JVM running for 16.013)
2025-01-13 08:35:55.717 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-13 08:40:00.001 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 08:40:00.063 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 08:40:00.149 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 08:40:00.194 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 08:40:00.194 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 08:45:00.028 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 08:35:54,440 to 2025-01-13 08:45:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 09:00:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 08:45:00,017 to 2025-01-13 09:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 09:00:00.011 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-13 09:00:00.048 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-13 09:00:00.075 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-13 09:00:00.075 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-13 09:00:00.210 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 09:00:00.243 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 09:00:00.295 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 09:00:00.333 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 09:00:00.335 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 09:15:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 09:00:00,008 to 2025-01-13 09:15:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 09:20:00.006 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 09:20:00.030 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 09:20:00.054 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 09:20:00.076 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 09:20:00.078 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 09:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 09:15:00,005 to 2025-01-13 09:30:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 09:40:00.005 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 09:40:00.054 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 09:40:00.126 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 09:40:00.174 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 09:40:00.176 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 09:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 09:30:00,016 to 2025-01-13 09:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 10:00:00.014 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-13 10:00:00.024 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 09:45:00,016 to 2025-01-13 10:00:00,023
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 10:00:00.043 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-13 10:00:00.075 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-13 10:00:00.077 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-13 10:00:00.224 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 10:00:00.253 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 10:00:00.274 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 10:00:00.292 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 10:00:00.293 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 10:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 10:00:00,023 to 2025-01-13 10:15:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 10:20:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 10:20:00.041 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 10:20:00.068 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 10:20:00.096 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 10:20:00.096 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 10:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 10:15:00,006 to 2025-01-13 10:30:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 10:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 10:40:00.044 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 10:40:00.069 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 10:40:00.094 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 10:40:00.094 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 10:45:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 10:30:00,013 to 2025-01-13 10:45:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 11:00:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 10:45:00,013 to 2025-01-13 11:00:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 11:00:00.013 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-13 11:00:00.099 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-13 11:00:00.144 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-13 11:00:00.145 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-13 11:00:00.365 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 11:00:00.400 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 11:00:00.428 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 11:00:00.458 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 11:00:00.458 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 11:15:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 11:00:00,004 to 2025-01-13 11:15:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 11:20:00.006 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 11:20:00.048 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 11:20:00.087 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 11:20:00.121 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 11:20:00.123 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 11:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 11:15:00,002 to 2025-01-13 11:30:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 11:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 11:40:00.047 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 11:40:00.086 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 11:40:00.118 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 11:40:00.119 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 11:45:00.003 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 11:30:00,014 to 2025-01-13 11:45:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 12:00:00.012 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-13 12:00:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 11:45:00,001 to 2025-01-13 12:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 12:00:00.074 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-13 12:00:00.105 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-13 12:00:00.107 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-13 12:00:00.235 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 12:00:00.254 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 12:00:00.274 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 12:00:00.301 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 12:00:00.301 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 12:15:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 12:00:00,010 to 2025-01-13 12:15:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 12:20:00.006 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 12:20:00.067 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 12:20:00.112 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 12:20:00.144 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 12:20:00.144 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 12:30:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 12:15:00,014 to 2025-01-13 12:30:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 12:30:32.808 ERROR com.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: *******************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1643)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1709)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2813)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 6 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:75)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
2025-01-13 13:01:15.891 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 13:01:16.035 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 13:01:16.062 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 13:01:16.089 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 13:01:16.090 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 13:01:16.219 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-13 13:01:16.257 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-13 13:01:16.291 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-13 13:01:16.291 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-13 13:11:15.905 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 12:30:00,004 to 2025-01-13 13:11:15,896
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 13:11:15.907 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 13:11:15,896 to 2025-01-13 13:11:15,906
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 13:15:00.003 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 13:11:15,906 to 2025-01-13 13:15:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 13:20:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 13:20:00.047 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 13:20:00.087 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 13:20:00.124 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 13:20:00.126 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 13:30:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 13:15:00,003 to 2025-01-13 13:30:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 13:40:00.013 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 13:40:00.054 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 13:40:00.092 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 13:40:00.125 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 13:40:00.125 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 13:45:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 13:30:00,003 to 2025-01-13 13:45:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 14:00:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 13:45:00,007 to 2025-01-13 14:00:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 14:00:00.011 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-13 14:00:00.214 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-13 14:00:00.239 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-13 14:00:00.239 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-13 14:00:00.891 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 14:00:00.942 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 14:00:01.000 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 14:00:01.053 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 14:00:01.053 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 14:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 14:00:00,009 to 2025-01-13 14:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 14:20:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 14:20:00.054 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 14:20:00.088 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 14:20:00.121 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 14:20:00.121 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 14:30:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 14:15:00,007 to 2025-01-13 14:30:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 14:40:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 14:40:00.045 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 14:40:00.075 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 14:40:00.105 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 14:40:00.105 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 14:45:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 14:30:00,008 to 2025-01-13 14:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 15:00:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 15:00:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 14:45:00,012 to 2025-01-13 15:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 15:00:00.055 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 15:00:00.093 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 15:00:00.120 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 15:00:00.121 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 15:00:00.234 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-13 15:00:00.274 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-13 15:00:00.315 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-13 15:00:00.316 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-13 15:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 15:00:00,008 to 2025-01-13 15:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 15:20:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 15:20:00.039 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 15:20:00.061 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 15:20:00.095 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 15:20:00.096 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 15:30:00.033 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 15:15:00,015 to 2025-01-13 15:30:00,020
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 15:40:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 15:40:00.040 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 15:40:00.066 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 15:40:00.091 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 15:40:00.092 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 15:45:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 15:30:00,020 to 2025-01-13 15:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 16:35:04.287 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 34104 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-13 16:35:04.287 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-13 16:35:04.289 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-13 16:35:06.272 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-13 16:35:06.274 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-13 16:35:06.507 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 221 ms. Found 0 Redis repository interfaces.
2025-01-13 16:35:06.954 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$a6a58b2c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-13 16:35:07.004 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$25abd0a4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-13 16:35:07.236 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-13 16:35:07.247 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-13 16:35:07.247 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-13 16:35:07.248 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-13 16:35:07.493 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-13 16:35:07.493 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3153 ms
2025-01-13 16:35:07.580 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-13 16:35:09.844 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-13 16:35:11.626 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-13 16:35:16.192 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'courseBookingController': Unsatisfied dependency expressed through field 'courseBooking'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'courseBooking': Unsatisfied dependency expressed through field 'locker'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'distributedLocker': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [com/ymiots/campusos/redission/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: /127.0.0.1:6379
2025-01-13 16:35:16.212 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-01-13 16:35:16.217 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-01-13 16:35:16.219 INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-13 16:35:16.227 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-01-13 16:35:16.264 ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'courseBookingController': Unsatisfied dependency expressed through field 'courseBooking'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'courseBooking': Unsatisfied dependency expressed through field 'locker'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'distributedLocker': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [com/ymiots/campusos/redission/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: /127.0.0.1:6379
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusos.CamPusOS.main(CamPusOS.java:29)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'courseBooking': Unsatisfied dependency expressed through field 'locker'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'distributedLocker': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [com/ymiots/campusos/redission/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: /127.0.0.1:6379
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'distributedLocker': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [com/ymiots/campusos/redission/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: /127.0.0.1:6379
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 34 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisson' defined in class path resource [com/ymiots/campusos/redission/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: /127.0.0.1:6379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:486)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 48 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redisson' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: /127.0.0.1:6379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 61 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: /127.0.0.1:6379
	at org.redisson.connection.pool.ConnectionPool$1.lambda$run$0(ConnectionPool.java:159)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:183)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:552)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:609)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at org.redisson.misc.RedissonPromise.tryFailure(RedissonPromise.java:96)
	at org.redisson.connection.pool.ConnectionPool.promiseFailure(ConnectionPool.java:329)
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$1(ConnectionPool.java:295)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:183)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:571)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:550)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:609)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at org.redisson.misc.RedissonPromise.tryFailure(RedissonPromise.java:96)
	at org.redisson.client.RedisClient$2$2.run(RedisClient.java:242)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:174)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:503)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: /127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:710)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:584)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:496)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-01-13 16:36:29.741 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 9400 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-13 16:36:29.743 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-13 16:36:29.744 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-13 16:36:31.615 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-13 16:36:31.617 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-13 16:36:31.844 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 216 ms. Found 0 Redis repository interfaces.
2025-01-13 16:36:32.241 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$26275628] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-13 16:36:32.287 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a52d9ba0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-13 16:36:32.561 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-13 16:36:32.570 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-13 16:36:32.571 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-13 16:36:32.571 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-13 16:36:32.778 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-13 16:36:32.778 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2991 ms
2025-01-13 16:36:32.871 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-13 16:36:35.221 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-13 16:36:36.856 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-13 16:36:37.438 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-13 16:36:37.438 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-13 16:36:38.198 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-13 16:36:38.352 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-13 16:36:38.383 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-13 16:36:38.390 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-13 16:36:38.587 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-13 16:36:38.949 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-13 16:36:38.954 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-13 16:36:39.031 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$bfc18cf5 - 计划任务最多执行：58个
2025-01-13 16:36:39.090 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-13 16:36:39.093 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-13 16:36:39.093 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-13 16:36:39.095 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[自动生成卡号]成功
2025-01-13 16:36:39.097 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-01-13 16:36:39.098 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-01-13 16:36:39.098 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-13 16:36:39.100 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-13 16:36:39.101 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-13 16:36:39.102 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-01-13 16:36:39.105 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[订餐定时删除人脸授权]成功
2025-01-13 16:36:39.106 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[大华人脸机时段下发]成功
2025-01-13 16:36:39.106 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[处理订餐人脸照片下发]成功
2025-01-13 16:36:39.108 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-13 16:36:39.109 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-13 16:36:39.111 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-13 16:36:39.112 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-01-13 16:36:39.114 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-13 16:36:39.115 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-01-13 16:36:39.115 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-13 16:36:39.116 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-01-13 16:36:39.148 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.875 seconds (JVM running for 11.029)
2025-01-13 16:36:39.582 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-13 16:37:02.310 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-13 16:37:02.319 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-13 16:37:02.319 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-13 16:37:02.321 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-01-13 16:37:05.554 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-01-13 16:37:10.358 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-01-13 16:37:10.442 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-01-13 16:40:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 16:40:00.079 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 16:40:00.157 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 16:40:00.202 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 16:40:00.202 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 16:45:00.045 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 16:36:38,586 to 2025-01-13 16:45:00,028
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 16:45:29.866 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 61128 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-13 16:45:29.868 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-13 16:45:29.869 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-13 16:45:32.373 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-13 16:45:32.375 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-13 16:45:32.669 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 276 ms. Found 0 Redis repository interfaces.
2025-01-13 16:45:33.142 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$e8cca445] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-13 16:45:33.199 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$67d2e9bd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-13 16:45:33.447 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-13 16:45:33.457 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-13 16:45:33.457 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-13 16:45:33.457 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-13 16:45:33.745 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-13 16:45:33.746 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3826 ms
2025-01-13 16:45:33.856 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-13 16:45:36.455 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-13 16:45:37.695 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-13 16:45:38.177 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-13 16:45:38.177 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-13 16:45:38.918 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-13 16:45:39.070 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-13 16:45:39.096 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-13 16:45:39.101 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-13 16:45:39.276 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-13 16:45:39.705 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-13 16:45:39.709 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-13 16:45:39.803 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$8266db12 - 计划任务最多执行：58个
2025-01-13 16:45:39.874 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-13 16:45:39.877 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-13 16:45:39.877 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-13 16:45:39.879 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[自动生成卡号]成功
2025-01-13 16:45:39.880 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-01-13 16:45:39.881 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-01-13 16:45:39.881 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-13 16:45:39.883 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-13 16:45:39.885 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-13 16:45:39.886 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-01-13 16:45:39.888 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[订餐定时删除人脸授权]成功
2025-01-13 16:45:39.889 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[大华人脸机时段下发]成功
2025-01-13 16:45:39.889 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[处理订餐人脸照片下发]成功
2025-01-13 16:45:39.891 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-13 16:45:39.892 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-13 16:45:39.893 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-13 16:45:39.895 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-01-13 16:45:39.896 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-13 16:45:39.897 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-01-13 16:45:39.898 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-13 16:45:39.899 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-01-13 16:45:39.951 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 10.508 seconds (JVM running for 11.998)
2025-01-13 16:45:40.485 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-13 16:45:51.660 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-13 16:45:51.661 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-13 16:45:51.667 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-01-13 16:45:53.278 ERROR c.y.campusos.controller.face.IntoFaceController - 120:null
2025-01-13 17:00:00.024 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-13 17:00:00.064 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 16:45:39,274 to 2025-01-13 17:00:00,046
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 17:00:00.083 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-13 17:00:00.144 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-13 17:00:00.144 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-13 17:00:00.462 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 17:00:00.514 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 17:00:00.567 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 17:00:00.611 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 17:00:00.611 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 17:15:00.018 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 17:00:00,046 to 2025-01-13 17:15:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 17:20:00.016 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 17:20:00.071 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 17:20:00.121 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 17:20:00.162 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 17:20:00.163 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 17:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 17:15:00,009 to 2025-01-13 17:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 17:40:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 17:40:00.055 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 17:40:00.088 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 17:40:00.116 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 17:40:00.116 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 17:45:00.030 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 17:30:00,015 to 2025-01-13 17:45:00,023
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 18:00:00.032 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-13 18:00:00.042 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-13 17:45:00,023 to 2025-01-13 18:00:00,026
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-13 18:00:00.168 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-13 18:00:00.274 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-13 18:00:00.274 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-13 18:00:00.633 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-13 18:00:00.693 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-13 18:00:00.736 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-13 18:00:00.789 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-13 18:00:00.790 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-13 18:00:01.151 INFO  com.ymiots.campusos.task.ConsumeFaceDownDelTask - 开始删除过期的人脸数据
2025-01-13 18:00:01.271 INFO  com.ymiots.campusos.task.ConsumeFaceDownDelTask - 外来人员人脸未找到预定人员，进行核对结算
2025-01-13 18:00:01.271 INFO  c.ymiots.campusos.task.ConsumeFaceDownDelTask$1$1 - 完成人脸权限删除
