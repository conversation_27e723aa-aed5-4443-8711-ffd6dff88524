2025-03-25 10:16:31.549 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 29836 (D:\yunmai\campusos5.0\campusos\target\classes started by <PERSON><PERSON> in D:\yunmai\campusos5.0)
2025-03-25 10:16:31.548 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-03-25 10:16:31.550 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-03-25 10:16:33.503 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-25 10:16:33.505 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-25 10:16:33.705 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 188 ms. Found 0 Redis repository interfaces.
2025-03-25 10:16:34.207 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$4cb948c5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 10:16:34.253 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$cbbf8e3d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 10:16:34.466 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-03-25 10:16:34.473 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-03-25 10:16:34.474 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-25 10:16:34.474 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-25 10:16:34.645 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-25 10:16:34.645 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3046 ms
2025-03-25 10:16:34.726 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-25 10:16:34.780 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-03-25 10:16:34.998 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-25 10:16:38.530 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dictCache': Invocation of init method failed; nested exception is org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 127.0.0.1:6379
2025-03-25 10:16:38.655 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-03-25 10:16:38.661 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-03-25 10:16:38.663 INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-03-25 10:16:38.671 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-03-25 10:16:38.701 ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dictCache': Invocation of init method failed; nested exception is org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 127.0.0.1:6379
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:160)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusos.CamPusOS.main(CamPusOS.java:29)
Caused by: org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 127.0.0.1:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1689)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1597)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1383)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1366)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:1093)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:421)
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:211)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:305)
	at com.ymiots.campusos.common.DictCache.initDictCache(DictCache.java:35)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	... 18 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 127.0.0.1:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:330)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:216)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1595)
	... 37 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: /127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:710)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:584)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:496)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-03-25 10:17:13.673 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 48772 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-25 10:17:13.673 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-03-25 10:17:13.684 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-03-25 10:17:15.461 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-25 10:17:15.463 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-25 10:17:15.729 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 253 ms. Found 0 Redis repository interfaces.
2025-03-25 10:17:16.178 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f9ad39bd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 10:17:16.226 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$78b37f35] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 10:17:16.440 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-03-25 10:17:16.447 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-03-25 10:17:16.447 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-25 10:17:16.447 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-25 10:17:16.621 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-25 10:17:16.621 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2888 ms
2025-03-25 10:17:16.707 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-25 10:17:16.757 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-03-25 10:17:16.940 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-25 10:17:18.717 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-25 10:17:18.804 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-25 10:17:18.804 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-25 10:17:19.488 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-03-25 10:17:19.623 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-25 10:17:19.650 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-25 10:17:19.655 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-25 10:17:19.726 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-25 10:17:20.097 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-03-25 10:17:20.102 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-03-25 10:17:20.149 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$9347708a - 计划任务最多执行：0个
2025-03-25 10:17:20.167 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.871 seconds (JVM running for 7.915)
2025-03-25 10:17:20.576 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-03-25 10:17:51.709 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-03-25 10:17:51.718 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-25 10:17:51.719 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-25 10:17:51.721 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-03-25 10:18:02.086 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-03-25 10:30:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 10:17:19,725 to 2025-03-25 10:30:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 10:33:32.011 ERROR com.ymiots.campusos.service.dev.DeviceService - 229:机号或序列号已被占用
2025-03-25 10:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 10:30:00,012 to 2025-03-25 10:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 11:00:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 10:45:00,016 to 2025-03-25 11:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 11:15:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 11:00:00,008 to 2025-03-25 11:15:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 11:30:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 11:15:00,009 to 2025-03-25 11:30:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 11:45:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 11:30:00,002 to 2025-03-25 11:45:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 12:00:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 11:45:00,004 to 2025-03-25 12:00:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 12:15:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 12:00:00,014 to 2025-03-25 12:15:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 12:31:39.074 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 12:15:00,006 to 2025-03-25 12:31:39,074
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 13:35:28.832 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 12:31:39,074 to 2025-03-25 13:35:28,832
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 13:35:28.832 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 13:35:28,832 to 2025-03-25 13:35:28,832
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 13:35:28.833 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 13:35:28,832 to 2025-03-25 13:35:28,833
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 13:35:28.833 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 13:35:28,833 to 2025-03-25 13:35:28,833
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 13:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 13:35:28,833 to 2025-03-25 13:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 13:56:59.573 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 54592 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-25 13:56:59.573 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-03-25 13:56:59.584 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-03-25 13:57:01.340 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-25 13:57:01.342 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-25 13:57:01.571 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 219 ms. Found 0 Redis repository interfaces.
2025-03-25 13:57:02.036 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$847b580d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 13:57:02.084 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$3819d85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 13:57:02.275 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-03-25 13:57:02.282 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-03-25 13:57:02.282 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-25 13:57:02.282 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-25 13:57:02.446 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-25 13:57:02.446 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2809 ms
2025-03-25 13:57:02.525 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-25 13:57:02.572 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-03-25 13:57:02.774 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-25 13:57:04.661 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-25 13:57:04.743 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-25 13:57:04.743 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-25 13:57:05.402 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-03-25 13:57:05.537 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-25 13:57:05.559 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-25 13:57:05.564 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-25 13:57:05.634 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-25 13:57:06.034 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-03-25 13:57:06.038 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-03-25 13:57:06.085 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$1e158eda - 计划任务最多执行：0个
2025-03-25 13:57:06.105 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.948 seconds (JVM running for 7.97)
2025-03-25 13:57:06.489 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-03-25 13:57:09.763 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-03-25 13:57:09.769 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-25 13:57:09.770 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-25 13:57:09.772 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-03-25 13:57:22.659 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-03-25 14:00:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 13:57:05,632 to 2025-03-25 14:00:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 14:15:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 14:00:00,009 to 2025-03-25 14:15:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 14:30:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 14:15:00,004 to 2025-03-25 14:30:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 14:45:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 14:30:00,005 to 2025-03-25 14:45:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 15:00:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 14:45:00,007 to 2025-03-25 15:00:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 15:15:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 15:00:00,004 to 2025-03-25 15:15:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 15:30:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 15:15:00,006 to 2025-03-25 15:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 15:34:29.450 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 42620 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-25 15:34:29.450 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-03-25 15:34:29.453 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-03-25 15:34:31.283 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-25 15:34:31.285 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-25 15:34:31.521 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 226 ms. Found 0 Redis repository interfaces.
2025-03-25 15:34:32.046 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f74cea98] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 15:34:32.091 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$76533010] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 15:34:32.280 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-03-25 15:34:32.287 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-03-25 15:34:32.288 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-25 15:34:32.288 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-25 15:34:32.463 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-25 15:34:32.463 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2963 ms
2025-03-25 15:34:32.542 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-25 15:34:32.590 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-03-25 15:36:23.840 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 54428 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-25 15:36:23.843 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-25 15:36:24.748 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-25 15:36:24.749 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-25 15:36:24.839 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 78 ms. Found 0 Redis repository interfaces.
2025-03-25 15:36:25.476 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-25 15:36:25.483 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-25 15:36:25.483 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-25 15:36:25.483 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-25 15:36:25.663 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-25 15:36:25.663 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1779 ms
2025-03-25 15:36:25.738 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-25 15:36:25.783 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-03-25 15:37:42.028 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-25 15:37:43.796 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-25 15:37:43.874 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-25 15:37:43.874 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-25 15:37:44.548 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-03-25 15:37:44.685 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-25 15:37:44.707 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-25 15:37:44.711 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-25 15:37:44.777 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-25 15:37:45.151 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-03-25 15:37:45.156 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-03-25 15:37:45.216 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$90e72165 - 计划任务最多执行：57个
2025-03-25 15:37:45.253 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-03-25 15:37:45.256 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-03-25 15:37:45.256 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-03-25 15:37:45.258 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-03-25 15:37:45.259 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-03-25 15:37:45.259 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-03-25 15:37:45.261 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-03-25 15:37:45.262 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-03-25 15:37:45.263 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-03-25 15:37:45.265 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[订餐定时删除人脸授权]成功
2025-03-25 15:37:45.266 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[大华人脸机时段下发]成功
2025-03-25 15:37:45.266 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[处理订餐人脸照片下发]成功
2025-03-25 15:37:45.267 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-03-25 15:37:45.269 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-03-25 15:37:45.271 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-03-25 15:37:45.272 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-03-25 15:37:45.273 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-03-25 15:37:45.274 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-03-25 15:37:45.274 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-03-25 15:37:45.275 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-03-25 15:37:45.291 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 196.203 seconds (JVM running for 197.263)
2025-03-25 15:37:45.675 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-03-25 15:39:33.196 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-25 15:39:34.029 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-25 15:39:34.459 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-25 15:39:34.459 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-25 15:39:35.613 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-25 15:39:35.713 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-25 15:39:35.736 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-25 15:39:35.741 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-25 15:39:35.886 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-25 15:39:36.157 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-25 15:39:36.164 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-25 15:39:36.265 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 192.779 seconds (JVM running for 193.897)
2025-03-25 15:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-25 15:40:00.024 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-25 15:40:00.030 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-25 15:40:00.036 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-25 15:40:00.036 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-25 15:45:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 15:37:44,776 to 2025-03-25 15:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 15:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 15:39:35,885 to 2025-03-25 15:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 16:00:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 15:45:00,011 to 2025-03-25 16:00:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 16:00:00.004 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-03-25 16:00:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 15:45:00,011 to 2025-03-25 16:00:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 16:00:00.025 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-03-25 16:00:00.025 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-25 16:00:00.030 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-03-25 16:00:00.031 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-03-25 16:00:00.056 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-25 16:00:00.061 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-25 16:00:00.066 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-25 16:00:00.072 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-25 16:00:00.072 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-25 16:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 16:00:00,004 to 2025-03-25 16:15:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 16:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 16:00:00,004 to 2025-03-25 16:15:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 16:20:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-25 16:20:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-25 16:20:00.030 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-25 16:20:00.036 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-25 16:20:00.037 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-25 16:30:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 16:15:00,008 to 2025-03-25 16:30:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 16:30:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 16:15:00,008 to 2025-03-25 16:30:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 16:40:00.004 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-25 16:40:00.017 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-25 16:40:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-25 16:40:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-25 16:40:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-25 16:45:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 16:30:00,004 to 2025-03-25 16:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 16:45:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 16:30:00,004 to 2025-03-25 16:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 17:00:00.003 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-25 17:00:00.003 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-25 17:00:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 16:45:00,012 to 2025-03-25 17:00:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 17:00:00.003 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 16:45:00,012 to 2025-03-25 17:00:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 17:00:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-25 17:00:00.019 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-25 17:00:00.024 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-25 17:00:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-25 17:00:00.059 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-03-25 17:00:00.066 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-03-25 17:00:00.072 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-03-25 17:00:00.074 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-03-25 17:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 17:00:00,003 to 2025-03-25 17:15:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 17:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 17:00:00,003 to 2025-03-25 17:15:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 17:20:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-25 17:20:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-25 17:20:00.035 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-25 17:20:00.044 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-25 17:20:00.044 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-25 17:30:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 17:15:00,014 to 2025-03-25 17:30:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 17:30:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 17:15:00,014 to 2025-03-25 17:30:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 17:31:39.833 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-03-25 17:31:39.842 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-25 17:31:39.842 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-25 17:31:39.864 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 22 ms
2025-03-25 17:31:45.875 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-03-25 17:31:45.877 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-03-25 17:31:51.365 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-03-25 17:40:00.015 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-25 17:40:00.030 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-25 17:40:00.041 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-25 17:40:00.050 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-25 17:40:00.050 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-25 17:43:56.207 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 23392 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-25 17:43:56.210 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-25 17:43:57.242 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-25 17:43:57.244 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-25 17:43:57.343 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 88 ms. Found 0 Redis repository interfaces.
2025-03-25 17:43:57.952 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-25 17:43:57.960 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-25 17:43:57.960 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-25 17:43:57.960 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-25 17:43:58.141 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-25 17:43:58.141 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1889 ms
2025-03-25 17:43:58.218 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-25 17:43:58.262 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-03-25 17:45:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 17:30:00,004 to 2025-03-25 17:45:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 17:47:06.184 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-25 17:47:07.073 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-25 17:47:07.462 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-25 17:47:07.462 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-25 17:47:08.697 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-25 17:47:08.808 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-25 17:47:08.833 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-25 17:47:08.839 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-25 17:47:08.992 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-25 17:47:09.262 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-25 17:47:09.269 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-25 17:47:09.484 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 193.691 seconds (JVM running for 194.983)
2025-03-25 17:48:04.897 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-25 17:48:04.898 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-25 17:48:04.900 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-03-25 17:49:23.034 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 32356 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-25 17:49:23.033 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-03-25 17:49:23.035 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-03-25 17:49:24.758 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-25 17:49:24.760 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-25 17:49:24.960 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 190 ms. Found 0 Redis repository interfaces.
2025-03-25 17:49:25.425 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$5794b035] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 17:49:25.471 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d69af5ad] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-25 17:49:25.665 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-03-25 17:49:25.672 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-03-25 17:49:25.673 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-25 17:49:25.673 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-25 17:49:25.855 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-25 17:49:25.855 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2773 ms
2025-03-25 17:49:25.935 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-25 17:49:25.986 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-03-25 17:52:34.796 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-25 17:52:36.550 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-25 17:52:36.629 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-25 17:52:36.629 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-25 17:52:37.261 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-03-25 17:52:37.391 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-25 17:52:37.413 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-25 17:52:37.418 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-25 17:52:37.487 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-25 17:52:37.830 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-03-25 17:52:37.997 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-03-25 17:52:38.050 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$f12ee702 - 计划任务最多执行：57个
2025-03-25 17:52:38.086 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-03-25 17:52:38.089 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-03-25 17:52:38.089 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-03-25 17:52:38.090 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-03-25 17:52:38.092 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-03-25 17:52:38.092 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-03-25 17:52:38.093 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-03-25 17:52:38.094 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-03-25 17:52:38.095 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-03-25 17:52:38.097 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[订餐定时删除人脸授权]成功
2025-03-25 17:52:38.099 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[大华人脸机时段下发]成功
2025-03-25 17:52:38.099 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[处理订餐人脸照片下发]成功
2025-03-25 17:52:38.101 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-03-25 17:52:38.102 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-03-25 17:52:38.104 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-03-25 17:52:38.105 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-03-25 17:52:38.106 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-03-25 17:52:38.107 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-03-25 17:52:38.108 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-03-25 17:52:38.109 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-03-25 17:52:38.121 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 195.472 seconds (JVM running for 196.452)
2025-03-25 17:52:38.516 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-03-25 17:54:57.674 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-03-25 17:54:57.679 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-25 17:54:57.681 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-25 17:54:57.682 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-03-25 17:55:02.647 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-03-25 18:00:00.006 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-03-25 18:00:00.010 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-25 18:00:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 17:52:37,485 to 2025-03-25 18:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 18:00:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-25 17:47:08,990 to 2025-03-25 18:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-25 18:00:00.016 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-03-25 18:00:00.022 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-03-25 18:00:00.022 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-03-25 18:00:00.041 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-25 18:00:00.046 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-25 18:00:00.049 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-25 18:00:00.053 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-25 18:00:00.053 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-25 18:00:00.077 INFO  com.ymiots.campusos.task.ConsumeFaceDownDelTask - 开始删除过期的人脸数据
2025-03-25 18:00:00.090 INFO  com.ymiots.campusos.task.ConsumeFaceDownDelTask - 外来人员人脸未找到预定人员，进行核对结算
2025-03-25 18:00:00.091 INFO  c.ymiots.campusos.task.ConsumeFaceDownDelTask$1$1 - 完成人脸权限删除
2025-03-25 18:03:52.116 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-03-25 18:03:52.147 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-03-25 18:03:52.262 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-03-25 18:03:52.266 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-03-25 18:03:52.291 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-03-25 18:03:52.294 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
