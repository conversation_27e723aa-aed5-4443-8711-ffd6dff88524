2025-08-19 16:05:56.317 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 21180 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-08-19 16:05:56.321 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-08-19 16:05:57.419 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-19 16:05:57.422 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-19 16:05:57.495 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 57 ms. Found 0 Redis repository interfaces.
2025-08-19 16:05:58.437 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tom<PERSON> initialized with port(s): 2022 (http)
2025-08-19 16:05:58.447 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-08-19 16:05:58.448 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-19 16:05:58.448 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-08-19 16:05:58.649 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-19 16:05:58.649 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2273 ms
2025-08-19 16:05:58.757 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-19 16:05:58.819 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-08-19 16:09:06.705 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-19 16:09:07.255 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-08-19 16:09:07.838 INFO  org.redisson.Version - Redisson 3.12.5
2025-08-19 16:09:08.408 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-08-19 16:09:08.408 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-08-19 16:09:10.247 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-19 16:09:10.450 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-08-19 16:09:10.495 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-08-19 16:09:10.507 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-08-19 16:09:10.958 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-08-19 16:09:11.388 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-08-19 16:09:11.406 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-08-19 16:09:11.750 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 195.925 seconds (JVM running for 197.686)
2025-08-19 16:15:00.036 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-08-19 16:09:10,956 to 2025-08-19 16:15:00,032
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-08-19 16:30:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-08-19 16:15:00,032 to 2025-08-19 16:30:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

