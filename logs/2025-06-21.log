2025-06-21 10:11:22.203 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 76804 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 10:11:22.206 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 10:11:23.359 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 10:11:23.360 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 10:11:23.464 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 92 ms. Found 0 Redis repository interfaces.
2025-06-21 10:11:24.129 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tom<PERSON> initialized with port(s): 2022 (http)
2025-06-21 10:11:24.135 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 10:11:24.136 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 10:11:24.136 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 10:11:24.334 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 10:11:24.334 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2087 ms
2025-06-21 10:11:24.405 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 10:11:24.448 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 10:11:24.645 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 10:11:25.059 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 10:11:25.375 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 10:11:25.711 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 10:11:25.711 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 10:11:26.820 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 10:11:26.923 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 10:11:26.945 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 10:11:26.951 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 10:11:27.104 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 10:11:27.409 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 10:11:27.417 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 10:11:27.516 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.689 seconds (JVM running for 7.07)
2025-06-21 10:12:01.085 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 10:12:01.085 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 10:12:01.086 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-21 10:12:13.346 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"B153E9E521BC72A2A627703FA9DAE138","mpAppId":"wx9fa8023ab8b47b04","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1750471933343}
2025-06-21 10:12:14.259 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK5-OTC1vmLgClSJLnEaqvUwc
2025-06-21 10:12:15.357 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 10:12:15.357 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 10:12:56.163 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 73580 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 10:12:56.163 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-06-21 10:12:56.166 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-06-21 10:12:58.266 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 10:12:58.268 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 10:12:58.643 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 335 ms. Found 0 Redis repository interfaces.
2025-06-21 10:12:59.163 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$4317fd4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 10:12:59.210 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$8337c54c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 10:12:59.435 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-06-21 10:12:59.442 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-06-21 10:12:59.443 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 10:12:59.443 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 10:12:59.659 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 10:12:59.660 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3424 ms
2025-06-21 10:12:59.746 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 10:12:59.798 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 10:13:00.010 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 10:13:00.429 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 10:13:01.595 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 10:13:01.672 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 10:13:01.672 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 10:13:02.349 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-21 10:13:02.483 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 10:13:02.504 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 10:13:02.509 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 10:13:02.599 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 10:13:03.173 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-06-21 10:13:03.177 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-06-21 10:13:03.237 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$9dcbb6a1 - 计划任务最多执行：40个
2025-06-21 10:13:03.265 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-06-21 10:13:03.267 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[访客自动签离]成功
2025-06-21 10:13:03.268 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-06-21 10:13:03.270 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-06-21 10:13:03.271 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-06-21 10:13:03.274 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-06-21 10:13:03.275 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-06-21 10:13:03.276 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-06-21 10:13:03.284 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.484 seconds (JVM running for 8.721)
2025-06-21 10:13:03.745 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-06-21 10:14:17.109 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 10:14:17.109 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 10:14:17.111 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-21 10:14:19.511 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-06-21 10:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 10:13:02,597 to 2025-06-21 10:15:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 10:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 10:11:27,102 to 2025-06-21 10:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 10:16:40.821 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 10:16:40.823 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 10:16:55.360 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 10:16:55.360 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 10:17:33.059 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK58yz6c5ZMEzd-gAyS7L3dJw
2025-06-21 10:17:33.715 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 10:17:33.716 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4c85c1ed-4e2d-4440-8d5c-a71f871e84fe.jpg
2025-06-21 10:20:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 10:20:00.034 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 10:20:00.035 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 10:20:00.036 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 10:20:00.036 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 10:30:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 10:15:00,004 to 2025-06-21 10:30:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 10:30:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 10:15:00,015 to 2025-06-21 10:30:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 10:40:00.002 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 10:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 10:40:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 10:40:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 10:40:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 10:45:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 10:30:00,006 to 2025-06-21 10:45:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 10:45:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 10:30:00,006 to 2025-06-21 10:45:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 11:00:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 11:00:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 10:45:00,004 to 2025-06-21 11:00:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 11:00:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 10:45:00,004 to 2025-06-21 11:00:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 11:00:00.013 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 11:00:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 11:00:00.015 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 11:00:00.015 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 11:00:00.015 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-06-21 11:00:00.033 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-06-21 11:00:00.033 INFO  com.ymiots.campusos.task.SysClearTempFileTask - 开始清除临时文件
2025-06-21 11:00:00.039 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-06-21 11:00:00.039 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-06-21 11:00:00.039 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-06-21 11:00:11.486 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 11:00:11.637 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 11:00:11.640 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 11:00:20.419 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-06-21 11:00:20.419 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 77068 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 11:00:20.422 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-06-21 11:00:22.692 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 11:00:22.694 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 11:00:23.046 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 314 ms. Found 0 Redis repository interfaces.
2025-06-21 11:00:23.574 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$48fa7e7d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 11:00:23.618 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$c800c3f5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 11:00:23.819 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-06-21 11:00:23.826 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-06-21 11:00:23.827 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 11:00:23.827 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 11:00:24.031 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 11:00:24.031 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3541 ms
2025-06-21 11:00:24.112 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 11:00:24.158 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 11:00:24.354 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 11:00:24.765 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 11:00:25.907 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 11:00:25.982 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 11:00:25.982 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 11:00:26.692 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-21 11:00:26.817 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 11:00:26.837 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 11:00:26.843 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 11:00:26.909 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 11:00:27.346 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-06-21 11:00:27.351 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-06-21 11:00:27.401 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$e294b54a - 计划任务最多执行：40个
2025-06-21 11:00:27.430 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-06-21 11:00:27.432 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[访客自动签离]成功
2025-06-21 11:00:27.433 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-06-21 11:00:27.435 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-06-21 11:00:27.436 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-06-21 11:00:27.438 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-06-21 11:00:27.439 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-06-21 11:00:27.440 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-06-21 11:00:27.446 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.426 seconds (JVM running for 8.901)
2025-06-21 11:00:27.898 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-06-21 11:15:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 11:00:00,012 to 2025-06-21 11:15:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 11:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 11:00:26,908 to 2025-06-21 11:15:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 11:20:00.005 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 11:20:00.016 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 11:20:00.017 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 11:20:00.018 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 11:20:00.018 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 11:30:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 11:15:00,003 to 2025-06-21 11:30:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 11:30:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 11:15:00,008 to 2025-06-21 11:30:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 11:33:32.256 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 11:33:32.257 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 11:33:32.260 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-21 11:33:39.666 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-06-21 11:37:13.864 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 11:37:14.010 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 11:37:14.013 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 11:37:17.695 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 84968 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 11:37:17.694 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-06-21 11:37:17.697 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-06-21 11:37:19.798 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 11:37:19.800 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 11:37:20.172 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 333 ms. Found 0 Redis repository interfaces.
2025-06-21 11:37:20.693 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$cf5b4e2b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 11:37:20.751 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$4e6193a3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 11:37:20.964 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-06-21 11:37:20.970 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-06-21 11:37:20.971 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 11:37:20.971 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 11:37:21.164 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 11:37:21.164 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3392 ms
2025-06-21 11:37:21.253 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 11:37:21.303 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 11:37:21.497 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 11:37:21.938 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 11:37:23.179 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 11:37:23.258 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 11:37:23.258 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 11:37:23.945 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-21 11:37:24.075 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 11:37:24.097 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 11:37:24.102 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 11:37:24.173 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 11:37:24.696 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-06-21 11:37:24.700 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-06-21 11:37:24.748 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$68f584f8 - 计划任务最多执行：40个
2025-06-21 11:37:24.777 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-06-21 11:37:24.778 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[访客自动签离]成功
2025-06-21 11:37:24.779 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-06-21 11:37:24.782 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-06-21 11:37:24.784 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-06-21 11:37:24.785 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-06-21 11:37:24.787 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-06-21 11:37:24.788 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-06-21 11:37:24.794 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.488 seconds (JVM running for 8.821)
2025-06-21 11:37:25.178 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-06-21 11:37:41.373 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 11:37:41.374 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 11:37:41.375 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 11:40:00.001 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 11:40:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 11:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 11:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 11:40:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 11:40:28.907 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-06-21 11:42:08.731 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 11:42:08.875 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 11:42:08.882 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 11:42:26.223 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 88192 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 11:42:26.223 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-06-21 11:42:26.226 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-06-21 11:42:28.116 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 11:42:28.117 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 11:42:28.506 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 339 ms. Found 0 Redis repository interfaces.
2025-06-21 11:42:29.044 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$97f1a009] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 11:42:29.089 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$16f7e581] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 11:42:29.290 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-06-21 11:42:29.298 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-06-21 11:42:29.299 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 11:42:29.299 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 11:42:29.495 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 11:42:29.496 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3192 ms
2025-06-21 11:42:29.582 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 11:42:29.638 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 11:42:29.829 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 11:42:30.254 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 11:42:31.420 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 11:42:31.501 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 11:42:31.501 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 11:42:32.319 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-21 11:42:32.587 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 11:42:32.610 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 11:42:32.615 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 11:42:32.691 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 11:42:33.120 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-06-21 11:42:33.125 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-06-21 11:42:33.175 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$318bd6d6 - 计划任务最多执行：40个
2025-06-21 11:42:33.201 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 11:42:33.201 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 11:42:33.202 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 11:42:33.207 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-06-21 11:42:33.209 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[访客自动签离]成功
2025-06-21 11:42:33.210 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-06-21 11:42:33.212 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-06-21 11:42:33.213 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-06-21 11:42:33.215 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-06-21 11:42:33.216 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-06-21 11:42:33.217 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-06-21 11:42:33.223 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.359 seconds (JVM running for 8.667)
2025-06-21 11:42:33.242 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-06-21 11:42:33.593 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-06-21 11:45:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 11:30:00,005 to 2025-06-21 11:45:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 11:45:00.018 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 11:42:32,689 to 2025-06-21 11:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 12:00:00.000 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-06-21 12:00:00.003 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-06-21 12:00:00.003 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-06-21 12:00:00.004 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-06-21 12:00:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 11:45:00,016 to 2025-06-21 12:00:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 12:00:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 11:45:00,014 to 2025-06-21 12:00:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 12:00:00.013 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-06-21 12:00:00.029 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 12:00:00.029 INFO  com.ymiots.campusos.task.SysClearTempFileTask - 开始清除临时文件
2025-06-21 12:00:00.029 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 12:00:00.030 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 12:00:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 12:00:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 12:01:49.984 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 88536 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 12:01:49.984 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-06-21 12:01:49.986 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-06-21 12:01:52.081 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 12:01:52.083 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 12:01:52.467 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 334 ms. Found 0 Redis repository interfaces.
2025-06-21 12:01:53.007 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b6939546] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 12:01:53.051 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$3599dabe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 12:01:53.253 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-06-21 12:01:53.262 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-06-21 12:01:53.262 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 12:01:53.263 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 12:01:53.480 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 12:01:53.480 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3417 ms
2025-06-21 12:01:53.571 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 12:01:53.625 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 12:01:53.823 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 12:01:54.361 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 12:01:55.511 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 12:01:55.590 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 12:01:55.590 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 12:01:56.263 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-21 12:01:56.389 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 12:01:56.409 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 12:01:56.413 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 12:01:56.481 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 12:01:56.882 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-06-21 12:01:56.886 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-06-21 12:01:56.934 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$502dcc13 - 计划任务最多执行：40个
2025-06-21 12:01:56.963 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-06-21 12:01:56.965 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[访客自动签离]成功
2025-06-21 12:01:56.966 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-06-21 12:01:56.968 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-06-21 12:01:56.969 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-06-21 12:01:56.971 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-06-21 12:01:56.973 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-06-21 12:01:56.974 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-06-21 12:01:56.979 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.367 seconds (JVM running for 8.706)
2025-06-21 12:01:57.332 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-06-21 12:02:04.412 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 12:02:04.412 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 12:02:04.414 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 12:15:00.003 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 12:00:00,013 to 2025-06-21 12:15:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 12:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 12:01:56,479 to 2025-06-21 12:15:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:37:32.414 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-06-21 13:37:32.414 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 13:37:32.423 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 13:37:32.424 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 13:37:32.424 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 13:37:32.424 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 13:37:32.440 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-06-21 13:37:32.446 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-06-21 13:37:32.447 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-06-21 13:37:32.447 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-06-21 13:37:32.459 INFO  com.ymiots.campusos.task.SysClearTempFileTask - 开始清除临时文件
2025-06-21 13:39:25.853 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-06-21 13:40:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 13:40:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 13:40:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 13:40:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 13:40:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 13:47:32.401 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 12:15:00,003 to 2025-06-21 13:47:32,401
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.401 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,401 to 2025-06-21 13:47:32,401
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.403 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,401 to 2025-06-21 13:47:32,403
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.403 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,403 to 2025-06-21 13:47:32,403
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.403 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,403 to 2025-06-21 13:47:32,403
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.403 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,403 to 2025-06-21 13:47:32,403
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.417 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 12:15:00,005 to 2025-06-21 13:47:32,417
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.417 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,417 to 2025-06-21 13:47:32,417
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.418 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,417 to 2025-06-21 13:47:32,418
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.418 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,418 to 2025-06-21 13:47:32,418
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.418 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,418 to 2025-06-21 13:47:32,418
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 13:47:32.418 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,418 to 2025-06-21 13:47:32,418
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 14:00:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,418 to 2025-06-21 14:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 14:00:00.008 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-06-21 14:00:00.008 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-06-21 14:00:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 13:47:32,403 to 2025-06-21 14:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 14:00:00.010 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-06-21 14:00:00.011 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-06-21 14:00:00.011 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-06-21 14:00:00.024 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 14:00:00.024 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 14:00:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 14:00:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 14:00:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 14:00:00.036 INFO  com.ymiots.campusos.task.SysClearTempFileTask - 开始清除临时文件
2025-06-21 14:01:53.585 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 14:01:53.733 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 14:01:53.738 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 14:01:57.985 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 91904 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 14:01:57.985 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-06-21 14:01:57.987 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-06-21 14:02:00.049 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 14:02:00.051 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 14:02:00.468 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 379 ms. Found 0 Redis repository interfaces.
2025-06-21 14:02:01.073 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$22432f1a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 14:02:01.121 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a1497492] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 14:02:01.332 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-06-21 14:02:01.339 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-06-21 14:02:01.340 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 14:02:01.340 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 14:02:01.577 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 14:02:01.577 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3521 ms
2025-06-21 14:02:01.655 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 14:02:01.700 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 14:02:01.895 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 14:02:02.410 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 14:02:03.571 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 14:02:03.651 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 14:02:03.651 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 14:02:04.337 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-21 14:02:04.461 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 14:02:04.482 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 14:02:04.486 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 14:02:04.553 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 14:02:05.043 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-06-21 14:02:05.046 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-06-21 14:02:05.096 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$bbdd65e7 - 计划任务最多执行：40个
2025-06-21 14:02:05.126 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-06-21 14:02:05.127 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[访客自动签离]成功
2025-06-21 14:02:05.128 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-06-21 14:02:05.129 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-06-21 14:02:05.131 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-06-21 14:02:05.132 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-06-21 14:02:05.135 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-06-21 14:02:05.136 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-06-21 14:02:05.140 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.514 seconds (JVM running for 8.942)
2025-06-21 14:02:05.363 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 14:02:05.363 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 14:02:05.365 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 14:02:05.404 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-06-21 14:02:05.509 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-06-21 14:02:32.970 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-06-21 14:04:35.104 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 14:04:35.240 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 14:04:35.248 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 14:04:39.093 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-06-21 14:04:39.095 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 6348 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 14:04:39.095 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-06-21 14:04:41.352 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 14:04:41.354 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 14:04:41.696 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 299 ms. Found 0 Redis repository interfaces.
2025-06-21 14:04:42.211 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$7f7a0d52] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 14:04:42.256 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$fe8052ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 14:04:42.453 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-06-21 14:04:42.461 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-06-21 14:04:42.463 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 14:04:42.463 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 14:04:42.681 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 14:04:42.681 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3518 ms
2025-06-21 14:04:42.761 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 14:04:42.807 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 14:04:42.995 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 14:04:43.411 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 14:04:44.556 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 14:04:44.631 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 14:04:44.631 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 14:04:45.334 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-21 14:04:45.474 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 14:04:45.494 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 14:04:45.499 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 14:04:45.581 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 14:04:46.077 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-06-21 14:04:46.081 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-06-21 14:04:46.130 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$1914441f - 计划任务最多执行：40个
2025-06-21 14:04:46.160 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-06-21 14:04:46.162 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[访客自动签离]成功
2025-06-21 14:04:46.163 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-06-21 14:04:46.165 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-06-21 14:04:46.166 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-06-21 14:04:46.168 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-06-21 14:04:46.169 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-06-21 14:04:46.170 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-06-21 14:04:46.176 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.483 seconds (JVM running for 8.842)
2025-06-21 14:04:46.617 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-06-21 14:06:51.596 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 14:06:51.596 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 14:06:51.598 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-21 14:06:56.112 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-06-21 14:07:15.331 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[update tb_workflow_approvar set status = 1 WHERE uid IN ('d7cc6f76-1a87-11f0-a82c-5254000802ef')]
2025-06-21 14:07:15.331 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [update tb_workflow_approvar set status = 1 WHERE uid IN ('d7cc6f76-1a87-11f0-a82c-5254000802ef')]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'status' in 'field list'
2025-06-21 14:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 14:00:00,008 to 2025-06-21 14:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 14:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 14:04:45,580 to 2025-06-21 14:15:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 14:20:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 14:20:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 14:20:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 14:20:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 14:20:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 14:30:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 14:15:00,012 to 2025-06-21 14:30:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 14:30:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 14:15:00,010 to 2025-06-21 14:30:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 14:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 14:40:00.020 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 14:40:00.020 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 14:40:00.021 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 14:40:00.021 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 14:45:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 14:30:00,016 to 2025-06-21 14:45:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 14:45:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 14:30:00,016 to 2025-06-21 14:45:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 15:00:00.002 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 15:00:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 14:45:00,009 to 2025-06-21 15:00:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 15:00:00.003 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-06-21 15:00:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 15:00:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 15:00:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 15:00:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 15:00:00.018 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 14:45:00,009 to 2025-06-21 15:00:00,018
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 15:00:00.031 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-06-21 15:00:00.032 INFO  com.ymiots.campusos.task.SysClearTempFileTask - 开始清除临时文件
2025-06-21 15:00:00.037 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-06-21 15:00:00.038 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-06-21 15:00:00.038 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-06-21 15:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 15:00:00,002 to 2025-06-21 15:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 15:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 15:00:00,018 to 2025-06-21 15:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 15:20:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 15:20:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 15:20:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 15:20:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 15:20:00.013 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 15:30:00.001 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 15:15:00,010 to 2025-06-21 15:30:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 15:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 15:15:00,010 to 2025-06-21 15:30:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 15:40:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 15:40:00.018 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 15:40:00.019 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 15:40:00.019 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 15:40:00.019 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 15:40:58.061 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 15:40:58.309 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 15:40:58.325 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 15:41:02.318 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 75944 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 15:41:02.315 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-06-21 15:41:02.318 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-06-21 15:41:04.896 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 15:41:04.897 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 15:41:05.288 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 345 ms. Found 0 Redis repository interfaces.
2025-06-21 15:41:05.836 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$2b7f1718] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 15:41:05.880 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$aa855c90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 15:41:06.102 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-06-21 15:41:06.110 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-06-21 15:41:06.111 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 15:41:06.111 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 15:41:06.341 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 15:41:06.341 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3942 ms
2025-06-21 15:41:06.422 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 15:41:06.481 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 15:41:06.665 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 15:41:07.136 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 15:41:08.429 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 15:41:08.536 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 15:41:08.536 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 15:41:09.304 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-21 15:41:09.447 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 15:41:09.473 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 15:41:09.479 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 15:41:09.560 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 15:41:10.338 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-06-21 15:41:10.342 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-06-21 15:41:10.398 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$c5194de5 - 计划任务最多执行：40个
2025-06-21 15:41:10.422 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 15:41:10.422 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 15:41:10.424 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-21 15:41:10.433 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-06-21 15:41:10.435 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[访客自动签离]成功
2025-06-21 15:41:10.436 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-06-21 15:41:10.437 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-06-21 15:41:10.439 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-06-21 15:41:10.441 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-06-21 15:41:10.443 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-06-21 15:41:10.444 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-06-21 15:41:10.449 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 8.535 seconds (JVM running for 9.973)
2025-06-21 15:41:10.466 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-06-21 15:41:10.906 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-06-21 15:41:48.219 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-06-21 15:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 15:30:00,017 to 2025-06-21 15:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 15:45:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 15:41:09,559 to 2025-06-21 15:45:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 15:51:39.564 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 15:51:39.711 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 15:51:39.714 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 15:51:43.869 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 95148 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 15:51:43.867 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-06-21 15:51:43.870 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-06-21 15:51:46.158 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 15:51:46.160 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 15:51:46.651 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 442 ms. Found 0 Redis repository interfaces.
2025-06-21 15:51:47.262 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$7f7a0d52] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 15:51:47.309 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$fe8052ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-21 15:51:47.514 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-06-21 15:51:47.523 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-06-21 15:51:47.524 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 15:51:47.524 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 15:51:47.776 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 15:51:47.776 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3817 ms
2025-06-21 15:51:47.877 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 15:51:47.937 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 15:51:48.125 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 15:51:48.671 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 15:51:49.932 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 15:51:50.024 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 15:51:50.024 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 15:51:50.760 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-06-21 15:51:50.897 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 15:51:50.918 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 15:51:50.924 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 15:51:50.995 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 15:51:51.482 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-06-21 15:51:51.486 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-06-21 15:51:51.538 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$1914441f - 计划任务最多执行：40个
2025-06-21 15:51:51.570 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-06-21 15:51:51.572 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[访客自动签离]成功
2025-06-21 15:51:51.573 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-06-21 15:51:51.575 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-06-21 15:51:51.576 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-06-21 15:51:51.578 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-06-21 15:51:51.581 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-06-21 15:51:51.582 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-06-21 15:51:51.587 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 8.104 seconds (JVM running for 9.484)
2025-06-21 15:51:51.987 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-06-21 15:52:05.409 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 15:52:05.409 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 15:52:05.411 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-21 15:52:39.460 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 15:52:39.469 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 15:52:42.706 ERROR com.ymiots.campusmp.service.AlleywayService - 419:该用户没有审批权限
2025-06-21 15:52:44.495 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 15:52:44.503 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 15:53:07.906 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 15:53:07.906 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 15:53:27.917 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 15:53:28.097 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 15:53:28.104 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 15:53:37.905 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 87784 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 15:53:37.907 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 15:53:39.113 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 15:53:39.114 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 15:53:39.251 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 126 ms. Found 0 Redis repository interfaces.
2025-06-21 15:53:39.876 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 15:53:39.883 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 15:53:39.883 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 15:53:39.884 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 15:53:40.095 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 15:53:40.096 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2149 ms
2025-06-21 15:53:40.174 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 15:53:40.218 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 15:53:40.412 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 15:53:40.855 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 15:53:41.182 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 15:53:41.532 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 15:53:41.532 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 15:53:42.682 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 15:53:42.783 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 15:53:42.805 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 15:53:42.810 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 15:53:42.966 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 15:53:43.283 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 15:53:43.290 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 15:53:43.370 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.839 seconds (JVM running for 7.174)
2025-06-21 15:54:49.072 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 15:54:49.073 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 15:54:49.074 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 15:55:35.988 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select  su.code, su.name  from  tb_workflow_approvar wa join tb_workflow_node wn on wa.nodeid = wn.uid  join tb_workflow_mould wm on wm.uid = wn.mouldid  join tb_sys_user su on su.empid = wa.infoid  join tb_card_orgframework_user cou on cou.userid = su.uid  where orgcode = 'null' and wm.mouldtype = 9 and wn.nodelevel = 1 and status != 1 ]
2025-06-21 15:55:35.988 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; SQL [select  su.code, su.name  from  tb_workflow_approvar wa join tb_workflow_node wn on wa.nodeid = wn.uid  join tb_workflow_mould wm on wm.uid = wn.mouldid  join tb_sys_user su on su.empid = wa.infoid  join tb_card_orgframework_user cou on cou.userid = su.uid  where orgcode = 'null' and wm.mouldtype = 9 and wn.nodelevel = 1 and status != 1 ]; Column 'status' in where clause is ambiguous; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'status' in where clause is ambiguous
2025-06-21 15:55:35.989 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select  ct.code, ct.name  from tb_workflow_approvar wa  join tb_workflow_node wn on wa.nodeid = wn.uid  join tb_workflow_mould wm on wm.uid = wn.mouldid  join tb_card_teachstudinfo ct on ct.uid = wa.infoid  where wm.mouldtype = 9 and wn.nodelevel = 1 and status = 1 ]
2025-06-21 15:55:35.990 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; SQL [select  ct.code, ct.name  from tb_workflow_approvar wa  join tb_workflow_node wn on wa.nodeid = wn.uid  join tb_workflow_mould wm on wm.uid = wn.mouldid  join tb_card_teachstudinfo ct on ct.uid = wa.infoid  where wm.mouldtype = 9 and wn.nodelevel = 1 and status = 1 ]; Column 'status' in where clause is ambiguous; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'status' in where clause is ambiguous
2025-06-21 15:56:31.563 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 15:56:31.701 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 15:56:31.706 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 15:56:39.494 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 92788 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 15:56:39.496 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 15:56:40.699 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 15:56:40.701 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 15:56:40.839 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 128 ms. Found 0 Redis repository interfaces.
2025-06-21 15:56:41.454 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 15:56:41.460 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 15:56:41.461 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 15:56:41.461 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 15:56:41.716 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 15:56:41.716 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2179 ms
2025-06-21 15:56:41.812 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 15:56:41.862 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 15:56:42.056 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 15:56:42.640 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 15:56:42.962 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 15:56:43.309 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 15:56:43.309 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 15:56:44.431 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 15:56:44.538 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 15:56:44.562 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 15:56:44.567 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 15:56:44.728 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 15:56:45.055 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 15:56:45.062 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 15:56:45.146 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.014 seconds (JVM running for 7.335)
2025-06-21 15:56:56.357 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 15:56:56.357 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 15:56:56.358 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 15:58:42.551 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 15:58:42.680 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 15:58:42.684 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 15:58:50.062 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 37360 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 15:58:50.064 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 15:58:51.187 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 15:58:51.188 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 15:58:51.294 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 97 ms. Found 0 Redis repository interfaces.
2025-06-21 15:58:51.939 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 15:58:51.946 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 15:58:51.947 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 15:58:51.947 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 15:58:52.209 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 15:58:52.209 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2105 ms
2025-06-21 15:58:52.284 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 15:58:52.330 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 15:58:52.511 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 15:58:52.954 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 15:58:53.272 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 15:58:53.599 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 15:58:53.599 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 15:58:54.718 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 15:58:54.822 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 15:58:54.847 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 15:58:54.853 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 15:58:55.010 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 15:58:55.356 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 15:58:55.364 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 15:58:55.448 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.749 seconds (JVM running for 7.037)
2025-06-21 16:00:00.008 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-06-21 16:00:00.008 INFO  com.ymiots.campusos.task.SysClearTempFileTask - 开始清除临时文件
2025-06-21 16:00:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 15:58:55,009 to 2025-06-21 16:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 16:00:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 15:51:50,994 to 2025-06-21 16:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 16:00:00.017 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-06-21 16:00:00.020 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-06-21 16:00:00.021 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-06-21 16:00:00.021 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-06-21 16:00:00.036 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 16:00:00.041 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 16:00:00.043 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 16:00:00.043 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 16:00:00.043 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 16:04:20.708 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 16:04:20.869 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 16:04:20.871 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 16:04:24.516 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 93024 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 16:04:24.518 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 16:04:25.718 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 16:04:25.720 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 16:04:25.841 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 112 ms. Found 0 Redis repository interfaces.
2025-06-21 16:04:26.470 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 16:04:26.478 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 16:04:26.478 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 16:04:26.479 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 16:04:26.657 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 16:04:26.657 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2100 ms
2025-06-21 16:04:26.736 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 16:04:26.781 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 16:04:26.969 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 16:04:27.543 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 16:04:27.863 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 16:04:28.246 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 16:04:28.246 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 16:04:29.352 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 16:04:29.454 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 16:04:29.477 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 16:04:29.482 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 16:04:29.641 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 16:04:29.970 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 16:04:29.979 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 16:04:30.060 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.905 seconds (JVM running for 7.27)
2025-06-21 16:05:24.076 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 16:05:24.076 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 16:05:24.078 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 16:06:02.397 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK5-OTC1vmLgClSJLnEaqvUwc
2025-06-21 16:06:03.417 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 16:06:03.420 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 16:07:08.972 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 16:07:09.110 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 16:07:09.114 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 16:07:13.236 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 96440 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 16:07:13.240 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 16:07:14.361 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 16:07:14.363 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 16:07:14.489 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 115 ms. Found 0 Redis repository interfaces.
2025-06-21 16:07:15.101 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 16:07:15.108 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 16:07:15.108 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 16:07:15.109 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 16:07:15.343 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 16:07:15.343 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2063 ms
2025-06-21 16:07:15.420 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 16:07:15.464 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 16:07:15.647 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 16:07:16.148 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 16:07:16.491 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 16:07:16.853 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 16:07:16.853 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 16:07:18.036 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 16:07:18.141 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 16:07:18.162 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 16:07:18.167 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 16:07:18.324 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 16:07:18.645 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 16:07:18.653 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 16:07:18.735 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.899 seconds (JVM running for 7.348)
2025-06-21 16:12:08.346 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 16:12:08.346 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 16:12:08.347 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 16:12:22.735 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK5-OTC1vmLgClSJLnEaqvUwc
2025-06-21 16:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 16:00:00,008 to 2025-06-21 16:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 16:15:00.018 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 16:07:18,323 to 2025-06-21 16:15:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 16:15:34.313 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 16:15:34.455 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 16:15:34.457 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 16:15:38.335 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 9628 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 16:15:38.338 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 16:15:39.532 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 16:15:39.534 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 16:15:39.676 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 132 ms. Found 0 Redis repository interfaces.
2025-06-21 16:15:40.300 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 16:15:40.306 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 16:15:40.306 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 16:15:40.307 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 16:15:40.534 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 16:15:40.534 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2156 ms
2025-06-21 16:15:40.610 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 16:15:40.656 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 16:15:40.835 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 16:15:41.305 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 16:15:41.628 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 16:15:42.116 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 16:15:42.116 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 16:15:43.350 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 16:15:43.454 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 16:15:43.478 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 16:15:43.484 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 16:15:43.646 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 16:15:43.950 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 16:15:43.957 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 16:15:44.039 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.083 seconds (JVM running for 7.529)
2025-06-21 16:16:21.320 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 16:16:21.320 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 16:16:21.321 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 16:16:25.632 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK5-OTC1vmLgClSJLnEaqvUwc
2025-06-21 16:20:00.003 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 16:20:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 16:20:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 16:20:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 16:20:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 16:20:17.204 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 16:20:17.346 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 16:20:17.350 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 16:20:24.851 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 93776 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 16:20:24.853 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 16:20:26.091 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 16:20:26.093 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 16:20:26.254 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 150 ms. Found 0 Redis repository interfaces.
2025-06-21 16:20:26.889 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 16:20:26.896 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 16:20:26.897 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 16:20:26.897 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 16:20:27.145 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 16:20:27.145 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2252 ms
2025-06-21 16:20:27.222 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 16:20:27.269 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 16:20:27.451 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 16:20:27.932 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 16:20:28.262 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 16:20:28.610 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 16:20:28.610 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 16:20:29.717 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 16:20:29.821 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 16:20:29.843 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 16:20:29.849 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 16:20:30.009 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 16:20:30.342 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 16:20:30.351 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 16:20:30.438 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.971 seconds (JVM running for 7.225)
2025-06-21 16:21:50.087 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 16:21:50.248 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 16:21:50.253 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 16:21:54.053 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 78720 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 16:21:54.056 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 16:21:55.226 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 16:21:55.229 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 16:21:55.371 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 131 ms. Found 0 Redis repository interfaces.
2025-06-21 16:21:55.986 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 16:21:55.992 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 16:21:55.993 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 16:21:55.993 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 16:21:56.222 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 16:21:56.222 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2126 ms
2025-06-21 16:21:56.297 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 16:21:56.343 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 16:21:56.523 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 16:21:56.947 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 16:21:57.271 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 16:21:57.606 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 16:21:57.606 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 16:21:58.780 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 16:21:58.883 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 16:21:58.906 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 16:21:58.912 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 16:21:59.073 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 16:21:59.395 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 16:21:59.403 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 16:21:59.483 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.819 seconds (JVM running for 7.213)
2025-06-21 16:22:02.807 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 16:22:02.807 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 16:22:02.808 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-21 16:25:33.365 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 16:25:33.510 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 16:25:33.518 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 16:25:37.494 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 96404 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 16:25:37.497 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 16:25:38.684 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 16:25:38.685 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 16:25:38.832 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 137 ms. Found 0 Redis repository interfaces.
2025-06-21 16:25:39.440 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 16:25:39.447 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 16:25:39.448 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 16:25:39.448 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 16:25:39.694 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 16:25:39.694 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2159 ms
2025-06-21 16:25:39.774 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 16:25:39.820 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 16:25:40.008 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 16:25:40.502 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 16:25:40.826 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 16:25:41.174 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 16:25:41.175 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 16:25:42.365 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 16:25:42.477 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 16:25:42.499 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 16:25:42.505 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 16:25:42.663 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 16:25:43.009 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 16:25:43.019 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 16:25:43.109 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.982 seconds (JVM running for 7.35)
2025-06-21 16:26:40.753 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 16:26:40.753 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 16:26:40.754 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-21 16:27:33.249 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 16:27:33.490 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 16:27:33.495 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 16:27:37.633 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 33192 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 16:27:37.635 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 16:27:38.852 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 16:27:38.853 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 16:27:39.007 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 143 ms. Found 0 Redis repository interfaces.
2025-06-21 16:27:39.649 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 16:27:39.656 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 16:27:39.656 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 16:27:39.657 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 16:27:39.882 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 16:27:39.882 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2205 ms
2025-06-21 16:27:39.957 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 16:27:40.004 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 16:27:40.185 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 16:27:40.637 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 16:27:40.957 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 16:27:41.333 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 16:27:41.333 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 16:27:42.563 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 16:27:42.668 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 16:27:42.692 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 16:27:42.698 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 16:27:42.856 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 16:27:43.181 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 16:27:43.189 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 16:27:43.278 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.024 seconds (JVM running for 7.43)
2025-06-21 16:27:49.584 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 16:27:49.585 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 16:27:49.587 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-21 16:27:54.198 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK5-OTC1vmLgClSJLnEaqvUwc
2025-06-21 16:28:30.724 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-06-21 16:30:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 16:15:00,015 to 2025-06-21 16:30:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 16:30:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 16:27:42,855 to 2025-06-21 16:30:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 16:31:45.801 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 16:31:45.801 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 16:39:36.613 ERROR com.ymiots.campusmp.common.GlobalExceptionHandler - 16:java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
2025-06-21 16:39:36.615 ERROR com.ymiots.campusmp.common.GlobalExceptionHandler - 16:java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
2025-06-21 16:39:36.615 ERROR com.ymiots.campusmp.common.GlobalExceptionHandler - 16:java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
2025-06-21 16:39:36.615 ERROR com.ymiots.campusmp.common.GlobalExceptionHandler - 16:java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
2025-06-21 16:39:36.623 ERROR com.ymiots.campusmp.common.GlobalExceptionHandler - 16:java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
2025-06-21 16:39:36.629 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalStateException: getOutputStream() has already been called for this response] with root cause
java.lang.IllegalStateException: getOutputStream() has already been called for this response
	at org.apache.catalina.connector.Response.getWriter(Response.java:584)
	at org.apache.catalina.connector.ResponseFacade.getWriter(ResponseFacade.java:227)
	at org.thymeleaf.spring5.view.ThymeleafView.renderFragment(ThymeleafView.java:364)
	at org.thymeleaf.spring5.view.ThymeleafView.render(ThymeleafView.java:190)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1404)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1148)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-06-21 16:39:36.629 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalStateException: getOutputStream() has already been called for this response] with root cause
java.lang.IllegalStateException: getOutputStream() has already been called for this response
	at org.apache.catalina.connector.Response.getWriter(Response.java:584)
	at org.apache.catalina.connector.ResponseFacade.getWriter(ResponseFacade.java:227)
	at org.thymeleaf.spring5.view.ThymeleafView.renderFragment(ThymeleafView.java:364)
	at org.thymeleaf.spring5.view.ThymeleafView.render(ThymeleafView.java:190)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1404)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1148)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-06-21 16:39:36.629 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalStateException: getOutputStream() has already been called for this response] with root cause
java.lang.IllegalStateException: getOutputStream() has already been called for this response
	at org.apache.catalina.connector.Response.getWriter(Response.java:584)
	at org.apache.catalina.connector.ResponseFacade.getWriter(ResponseFacade.java:227)
	at org.thymeleaf.spring5.view.ThymeleafView.renderFragment(ThymeleafView.java:364)
	at org.thymeleaf.spring5.view.ThymeleafView.render(ThymeleafView.java:190)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1404)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1148)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-06-21 16:39:36.629 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalStateException: getOutputStream() has already been called for this response] with root cause
java.lang.IllegalStateException: getOutputStream() has already been called for this response
	at org.apache.catalina.connector.Response.getWriter(Response.java:584)
	at org.apache.catalina.connector.ResponseFacade.getWriter(ResponseFacade.java:227)
	at org.thymeleaf.spring5.view.ThymeleafView.renderFragment(ThymeleafView.java:364)
	at org.thymeleaf.spring5.view.ThymeleafView.render(ThymeleafView.java:190)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1404)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1148)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-06-21 16:39:36.629 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.IllegalStateException: getOutputStream() has already been called for this response] with root cause
java.lang.IllegalStateException: getOutputStream() has already been called for this response
	at org.apache.catalina.connector.Response.getWriter(Response.java:584)
	at org.apache.catalina.connector.ResponseFacade.getWriter(ResponseFacade.java:227)
	at org.thymeleaf.spring5.view.ThymeleafView.renderFragment(ThymeleafView.java:364)
	at org.thymeleaf.spring5.view.ThymeleafView.render(ThymeleafView.java:190)
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1404)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1148)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-06-21 16:39:37.200 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 16:39:37.337 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 16:39:37.339 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 16:39:44.855 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 88268 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 16:39:44.857 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 16:39:46.055 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 16:39:46.057 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 16:39:46.185 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 117 ms. Found 0 Redis repository interfaces.
2025-06-21 16:39:46.808 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 16:39:46.815 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 16:39:46.815 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 16:39:46.816 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 16:39:47.042 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 16:39:47.042 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2146 ms
2025-06-21 16:39:47.118 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 16:39:47.162 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 16:39:47.352 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 16:39:47.875 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 16:39:48.204 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 16:39:48.661 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 16:39:48.661 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 16:39:49.820 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 16:39:49.927 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 16:39:49.950 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 16:39:49.955 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 16:39:50.114 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 16:39:50.461 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 16:39:50.469 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 16:39:50.548 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.055 seconds (JVM running for 7.393)
2025-06-21 16:40:00.005 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 16:40:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 16:40:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 16:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 16:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 16:41:20.796 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 16:41:20.796 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 16:41:20.797 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 16:45:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 16:30:00,002 to 2025-06-21 16:45:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 16:45:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 16:39:50,112 to 2025-06-21 16:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 17:00:00.001 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 16:45:00,013 to 2025-06-21 17:00:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 17:00:00.001 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 16:45:00,015 to 2025-06-21 17:00:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 17:00:00.001 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 17:00:00.001 INFO  com.ymiots.campusos.task.SysClearTempFileTask - 开始清除临时文件
2025-06-21 17:00:00.002 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 17:00:00.003 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 17:00:00.004 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 17:00:00.004 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 17:00:00.004 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-06-21 17:00:00.022 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-06-21 17:00:00.028 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-06-21 17:00:00.028 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-06-21 17:00:00.028 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-06-21 17:13:44.807 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:13:44.942 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:13:44.945 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:13:54.446 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 80364 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:13:54.448 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:13:55.834 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:13:55.835 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:13:56.001 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 150 ms. Found 0 Redis repository interfaces.
2025-06-21 17:13:56.639 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:13:56.648 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:13:56.648 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:13:56.648 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:13:56.879 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:13:56.880 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2369 ms
2025-06-21 17:13:56.958 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:13:57.004 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:13:57.186 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:13:57.631 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:13:57.947 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:13:58.293 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:13:58.293 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:13:59.381 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:13:59.495 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:13:59.519 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:13:59.525 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:13:59.690 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:14:00.020 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:14:00.029 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:14:00.115 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.056 seconds (JVM running for 7.316)
2025-06-21 17:14:16.371 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 17:14:16.371 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 17:14:16.372 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 17:15:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 17:00:00,001 to 2025-06-21 17:15:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 17:15:00.018 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 17:13:59,688 to 2025-06-21 17:15:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 17:16:17.917 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:16:18.052 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:16:18.057 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:16:22.273 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 49056 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:16:22.275 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:16:23.502 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:16:23.504 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:16:23.647 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 133 ms. Found 0 Redis repository interfaces.
2025-06-21 17:16:24.284 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:16:24.291 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:16:24.291 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:16:24.292 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:16:24.561 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:16:24.562 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2247 ms
2025-06-21 17:16:24.641 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:16:24.688 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:16:24.872 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:16:25.337 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:16:25.656 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:16:26.015 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:16:26.015 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:16:27.247 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:16:27.363 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:16:27.385 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:16:27.391 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:16:27.568 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:16:27.923 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:16:27.932 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:16:28.014 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.135 seconds (JVM running for 7.786)
2025-06-21 17:16:45.633 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 17:16:45.633 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 17:16:45.634 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 17:17:19.923 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:17:20.070 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:17:20.074 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:17:23.752 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 86840 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:17:23.755 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:17:24.919 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:17:24.921 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:17:25.014 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 83 ms. Found 0 Redis repository interfaces.
2025-06-21 17:17:25.629 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:17:25.636 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:17:25.636 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:17:25.636 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:17:25.872 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:17:25.872 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2079 ms
2025-06-21 17:17:25.948 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:17:25.991 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:17:26.176 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:17:26.636 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:17:26.955 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:17:27.302 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:17:27.302 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:17:28.387 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:17:28.492 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:17:28.514 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:17:28.519 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:17:28.676 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:17:29.021 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:17:29.030 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:17:29.116 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.729 seconds (JVM running for 7.087)
2025-06-21 17:20:00.006 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 17:20:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 17:20:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 17:20:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 17:20:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 17:20:50.544 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:20:50.680 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:20:50.685 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:20:54.478 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 3316 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:20:54.481 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:20:55.663 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:20:55.665 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:20:55.773 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 98 ms. Found 0 Redis repository interfaces.
2025-06-21 17:20:56.380 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:20:56.387 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:20:56.387 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:20:56.387 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:20:56.619 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:20:56.619 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2084 ms
2025-06-21 17:20:56.694 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:20:56.738 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:20:56.917 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:20:57.357 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:20:57.703 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:20:58.064 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:20:58.064 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:20:59.170 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:20:59.269 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:20:59.292 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:20:59.297 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:20:59.469 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:20:59.836 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:20:59.846 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:20:59.935 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.847 seconds (JVM running for 7.361)
2025-06-21 17:21:28.907 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 17:21:28.907 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 17:21:28.908 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-21 17:21:33.318 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK5-OTC1vmLgClSJLnEaqvUwc
2025-06-21 17:21:34.137 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 17:21:34.137 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 17:23:22.167 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:23:22.304 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:23:22.308 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:23:26.233 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 97748 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:23:26.235 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:23:27.376 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:23:27.378 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:23:27.476 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 87 ms. Found 0 Redis repository interfaces.
2025-06-21 17:23:28.091 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:23:28.098 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:23:28.099 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:23:28.099 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:23:28.347 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:23:28.347 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2073 ms
2025-06-21 17:23:28.429 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:23:28.474 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:23:28.657 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:23:29.127 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:23:29.466 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:23:29.901 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:23:29.901 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:23:31.014 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:23:31.117 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:23:31.140 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:23:31.146 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:23:31.307 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:23:31.646 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:23:31.653 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:23:31.733 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.861 seconds (JVM running for 7.174)
2025-06-21 17:23:46.936 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 17:23:46.937 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 17:23:46.938 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 17:24:25.537 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK5-OTC1vmLgClSJLnEaqvUwc
2025-06-21 17:24:26.299 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 17:24:26.299 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 17:25:54.659 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:25:54.903 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:25:54.907 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:25:59.679 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 32312 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:25:59.681 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:26:00.865 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:26:00.867 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:26:01.034 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 158 ms. Found 0 Redis repository interfaces.
2025-06-21 17:26:01.697 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:26:01.704 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:26:01.704 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:26:01.705 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:26:01.942 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:26:01.942 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2221 ms
2025-06-21 17:26:02.022 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:26:02.068 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:26:02.254 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:26:02.898 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:26:03.274 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:26:03.817 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:26:03.817 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:26:05.007 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:26:05.113 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:26:05.136 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:26:05.141 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:26:05.300 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:26:05.631 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:26:05.639 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:26:05.725 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.424 seconds (JVM running for 7.785)
2025-06-21 17:26:14.060 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 17:26:14.062 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 17:26:14.062 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-21 17:26:18.658 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK5-OTC1vmLgClSJLnEaqvUwc
2025-06-21 17:27:08.577 ERROR com.ymiots.campusmp.service.AlleywayService - 548:未配置审核人，请联系管理员！
2025-06-21 17:27:48.851 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:27:48.994 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:27:48.999 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:28:07.334 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 90136 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:28:07.336 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:28:08.482 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:28:08.483 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:28:08.631 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 138 ms. Found 0 Redis repository interfaces.
2025-06-21 17:28:09.306 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:28:09.313 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:28:09.314 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:28:09.314 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:28:09.557 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:28:09.557 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2181 ms
2025-06-21 17:28:09.633 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:28:09.680 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:28:09.865 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:28:10.309 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:28:10.627 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:28:10.968 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:28:10.968 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:28:12.143 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:28:12.249 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:28:12.272 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:28:12.277 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:28:12.440 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:28:12.780 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:28:12.788 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:28:12.872 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.903 seconds (JVM running for 7.175)
2025-06-21 17:28:24.121 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 17:28:24.123 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 17:28:24.123 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-21 17:28:31.809 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 17:28:31.809 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 17:30:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 17:15:00,012 to 2025-06-21 17:30:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 17:30:12.320 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 17:28:12,438 to 2025-06-21 17:30:12,318
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 17:30:13.122 ERROR com.ymiots.campusmp.service.AlleywayService - 548:未配置审核人，请联系管理员！
2025-06-21 17:33:42.292 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:33:42.421 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:33:42.425 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:33:49.959 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 83176 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:33:49.962 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:33:51.285 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:33:51.287 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:33:51.449 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 151 ms. Found 0 Redis repository interfaces.
2025-06-21 17:33:52.146 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:33:52.153 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:33:52.153 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:33:52.153 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:33:52.385 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:33:52.385 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2379 ms
2025-06-21 17:33:52.462 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:33:52.507 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:33:52.685 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:33:53.126 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:33:53.482 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:33:53.858 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:33:53.858 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:33:55.036 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:33:55.138 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:33:55.161 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:33:55.167 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:33:55.321 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:33:55.654 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:33:55.663 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:33:55.748 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.169 seconds (JVM running for 7.496)
2025-06-21 17:37:20.880 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:37:21.023 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:37:21.030 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:37:28.975 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 73712 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:37:28.978 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:37:30.153 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:37:30.154 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:37:30.272 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 109 ms. Found 0 Redis repository interfaces.
2025-06-21 17:37:30.890 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:37:30.897 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:37:30.897 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:37:30.898 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:37:31.125 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:37:31.125 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2107 ms
2025-06-21 17:37:31.201 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:37:31.246 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:37:31.428 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:37:31.951 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:37:32.266 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:37:32.607 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:37:32.607 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:37:33.788 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:37:33.886 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:37:33.910 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:37:33.915 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:37:34.066 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:37:34.347 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:37:34.354 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:37:34.433 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.84 seconds (JVM running for 7.277)
2025-06-21 17:37:40.447 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 17:37:40.448 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 17:37:40.449 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 17:37:56.530 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:o2JXK5-OTC1vmLgClSJLnEaqvUwc
2025-06-21 17:37:57.343 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 17:37:57.343 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 17:38:20.499 ERROR com.ymiots.campusmp.service.AlleywayService - 548:未配置审核人，请联系管理员！
2025-06-21 17:40:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 17:40:00.018 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 17:40:00.018 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 17:40:00.019 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 17:40:00.019 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 17:43:40.659 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:43:40.797 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:43:40.800 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:43:48.700 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 39724 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:43:48.703 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:43:49.905 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:43:49.907 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:43:50.098 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 176 ms. Found 0 Redis repository interfaces.
2025-06-21 17:43:50.772 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:43:50.780 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:43:50.780 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:43:50.780 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:43:51.033 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:43:51.033 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2266 ms
2025-06-21 17:43:51.108 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:43:51.154 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:43:51.337 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:43:51.912 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:43:52.260 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:43:52.610 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:43:52.610 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:43:53.721 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:43:53.824 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:43:53.847 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:43:53.852 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:43:54.004 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:43:54.335 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:43:54.342 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:43:54.423 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.082 seconds (JVM running for 7.398)
2025-06-21 17:45:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 17:30:00,004 to 2025-06-21 17:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 17:45:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 17:43:54,003 to 2025-06-21 17:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 17:46:00.441 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:46:00.689 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:46:00.695 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:46:08.651 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 96324 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:46:08.654 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:46:09.823 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:46:09.824 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:46:09.965 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 130 ms. Found 0 Redis repository interfaces.
2025-06-21 17:46:10.577 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:46:10.583 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:46:10.584 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:46:10.584 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:46:10.816 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:46:10.816 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2125 ms
2025-06-21 17:46:10.890 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:46:10.934 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:46:11.107 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:46:11.536 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:46:11.874 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:46:12.208 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:46:12.208 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:46:13.350 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:46:13.457 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:46:13.481 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:46:13.488 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:46:13.649 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:46:13.969 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:46:13.976 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:46:14.056 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.817 seconds (JVM running for 7.226)
2025-06-21 17:48:13.505 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 17:48:13.505 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 17:48:13.506 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 17:48:33.236 ERROR c.ymiots.campusmp.redis.WeiXinTemplateMsgSubscribe - 用户微信消息推送失败：invalid openid rid: 68567ff1-35f52d7d-33e09e95,微信模版：Atp8_LSXypAjQ0geHAtvVvIFzBgeh-Gea8x7RaBAlko
2025-06-21 17:48:36.966 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:48:36.970 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:48:36.970 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:48:36.975 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:48:50.909 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:48:50.909 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:48:50.911 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:48:50.914 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:53:13.754 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 17:53:13.882 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 17:53:13.884 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 17:53:18.043 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 95604 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 17:53:18.046 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 17:53:19.177 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 17:53:19.178 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 17:53:19.324 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 135 ms. Found 0 Redis repository interfaces.
2025-06-21 17:53:19.937 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 17:53:19.943 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 17:53:19.944 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 17:53:19.944 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 17:53:20.171 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 17:53:20.171 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2087 ms
2025-06-21 17:53:20.245 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 17:53:20.290 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 17:53:20.482 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 17:53:20.931 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 17:53:21.299 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 17:53:21.653 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 17:53:21.653 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 17:53:22.860 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 17:53:22.965 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 17:53:22.989 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 17:53:22.994 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 17:53:23.155 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 17:53:23.491 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 17:53:23.499 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 17:53:23.616 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.932 seconds (JVM running for 7.381)
2025-06-21 17:57:14.251 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 17:57:14.252 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 17:57:14.253 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 17:57:14.907 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:57:14.909 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:57:14.909 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:57:14.920 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:57:19.543 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:57:19.543 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:57:19.552 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:57:19.552 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:58:51.972 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:58:51.972 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:58:51.981 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 17:58:51.981 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:00:00.001 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-06-21 18:00:00.001 INFO  com.ymiots.campusos.task.SysClearTempFileTask - 开始清除临时文件
2025-06-21 18:00:00.002 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-06-21 18:00:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 17:53:23,154 to 2025-06-21 18:00:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 18:00:00.009 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-06-21 18:00:00.010 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-06-21 18:00:00.010 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-06-21 18:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 17:45:00,010 to 2025-06-21 18:00:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 18:00:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-06-21 18:00:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-06-21 18:00:00.032 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-06-21 18:00:00.032 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-06-21 18:00:00.032 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-06-21 18:03:44.487 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:03:44.500 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:03:44.500 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:03:44.505 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:04:07.482 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:04:07.482 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:04:07.493 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:04:07.493 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:07:22.267 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 18:07:22.398 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 18:07:22.400 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 18:07:29.815 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 97248 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-06-21 18:07:29.818 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-06-21 18:07:30.916 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 18:07:30.918 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 18:07:31.063 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 134 ms. Found 0 Redis repository interfaces.
2025-06-21 18:07:31.689 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-06-21 18:07:31.695 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-06-21 18:07:31.695 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 18:07:31.696 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-06-21 18:07:31.922 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 18:07:31.922 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2066 ms
2025-06-21 18:07:31.998 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 18:07:32.043 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-06-21 18:07:32.225 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 18:07:32.655 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-06-21 18:07:32.972 INFO  org.redisson.Version - Redisson 3.12.5
2025-06-21 18:07:33.312 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-06-21 18:07:33.312 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-06-21 18:07:34.518 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-21 18:07:34.630 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-06-21 18:07:34.654 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-06-21 18:07:34.659 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-06-21 18:07:34.849 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-06-21 18:07:35.195 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-06-21 18:07:35.203 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-06-21 18:07:35.296 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.853 seconds (JVM running for 7.19)
2025-06-21 18:07:58.595 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 18:07:58.595 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 18:07:58.597 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-21 18:07:58.871 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:07:58.877 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:07:58.885 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:07:58.891 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:08:01.990 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 18:08:01.996 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 18:08:23.466 ERROR c.ymiots.campusmp.redis.WeiXinTemplateMsgSubscribe - 用户微信消息推送失败：invalid openid rid: 68568497-2f000f17-353f65ec,微信模版：Atp8_LSXypAjQ0geHAtvVvIFzBgeh-Gea8x7RaBAlko
2025-06-21 18:08:42.961 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ffa7153b-86d9-479d-ad85-bc3e165d27be.jpg
2025-06-21 18:08:42.961 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\4a73be69-9ecc-4780-afd4-b97154d05bcf.jpg
2025-06-21 18:08:46.261 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:08:46.261 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:08:46.261 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:08:46.265 ERROR com.ymiots.campusmp.controller.AlleywayController - 657:null
2025-06-21 18:15:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 18:00:00,015 to 2025-06-21 18:15:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 18:15:00.020 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-06-21 18:07:34,847 to 2025-06-21 18:15:00,018
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-06-21 18:19:00.710 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-06-21 18:19:00.870 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-21 18:19:00.873 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
