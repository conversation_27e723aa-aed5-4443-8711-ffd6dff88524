2025-01-03 13:13:21.638 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 59032 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-03 13:13:21.640 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-03 13:13:22.707 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-03 13:13:22.709 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-03 13:13:22.782 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 62 ms. Found 0 Redis repository interfaces.
2025-01-03 13:13:23.481 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-03 13:13:23.490 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-03 13:13:23.490 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-03 13:13:23.490 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-03 13:13:23.662 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-03 13:13:23.662 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1979 ms
2025-01-03 13:13:23.738 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-03 13:13:23.944 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-03 13:13:24.830 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-03 13:13:25.234 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-03 13:13:25.234 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-03 13:13:26.367 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-03 13:13:26.476 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-03 13:13:26.502 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-03 13:13:26.506 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-03 13:13:26.658 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-03 13:13:26.949 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-03 13:13:26.958 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-03 13:13:27.044 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.775 seconds (JVM running for 6.797)
2025-01-03 13:15:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 13:13:26,657 to 2025-01-03 13:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 13:30:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 13:15:00,010 to 2025-01-03 13:30:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 13:35:01.531 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 68208 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-03 13:35:01.533 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-03 13:35:02.683 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-03 13:35:02.686 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-03 13:35:02.773 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 75 ms. Found 0 Redis repository interfaces.
2025-01-03 13:35:03.410 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-03 13:35:03.418 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-03 13:35:03.418 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-03 13:35:03.418 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-03 13:35:03.593 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-03 13:35:03.594 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1988 ms
2025-01-03 13:35:03.675 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-03 13:35:03.873 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-03 13:35:04.963 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-03 13:35:05.348 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-03 13:35:05.348 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-03 13:35:06.496 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-03 13:35:06.598 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-03 13:35:06.619 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-03 13:35:06.625 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-03 13:35:06.763 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-03 13:35:07.011 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-03 13:35:07.019 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-03 13:35:07.094 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.033 seconds (JVM running for 7.175)
2025-01-03 13:38:22.679 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 49412 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-03 13:38:22.681 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-03 13:38:23.586 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-03 13:38:23.587 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-03 13:38:23.663 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 66 ms. Found 0 Redis repository interfaces.
2025-01-03 13:38:24.289 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-03 13:38:24.295 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-03 13:38:24.295 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-03 13:38:24.296 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-03 13:38:24.448 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-03 13:38:24.448 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1726 ms
2025-01-03 13:38:24.523 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-03 13:38:24.707 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-03 13:38:25.442 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-03 13:38:25.805 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-03 13:38:25.805 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-03 13:38:26.883 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-03 13:38:26.992 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-03 13:38:27.015 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-03 13:38:27.020 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-03 13:38:27.158 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-03 13:38:27.410 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-03 13:38:27.418 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-03 13:38:27.496 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.174 seconds (JVM running for 6.174)
2025-01-03 13:45:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 13:38:27,156 to 2025-01-03 13:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 14:00:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 13:45:00,011 to 2025-01-03 14:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 14:00:00.025 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 14:05:56.960 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-03 14:05:56.961 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-03 14:05:56.962 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-03 14:05:57.218 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:57.218 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:57.239 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"B22B31B0B1D8677B165CD4702832E14F","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884357230}
2025-01-03 14:05:57.572 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:57.573 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:57.575 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"BDFDF067C8F7A8ABA76C21645FFABD44","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884357575}
2025-01-03 14:05:57.576 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:57.576 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:57.577 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"53D01895064182B6745F2B7613D01FB8","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884357577}
2025-01-03 14:05:57.743 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:57.744 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:57.746 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"A50219AA0D74E2422828D7440F6D8402","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884357745}
2025-01-03 14:05:57.794 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:57.794 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:57.796 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"FFDF0F38C3EA96001E6853724F90B47B","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884357796}
2025-01-03 14:05:57.950 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:57.950 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:57.951 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"85AAB9F141354427164A6AB9B0BA552F","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884357951}
2025-01-03 14:05:58.062 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:58.063 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:58.064 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"9029C347CE5D98E0B717B3F8345EB39C","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884358064}
2025-01-03 14:05:58.080 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:58.080 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:58.080 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"30518B0E15B33911061361F7F4F85F4A","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884358080}
2025-01-03 14:05:58.234 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:58.234 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:58.235 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"33076CDB8525BF774AE34296829C2EEE","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884358235}
2025-01-03 14:05:58.244 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:58.244 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:58.245 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"ED80393CEC61E2416B48CED2B58C753C","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884358245}
2025-01-03 14:05:58.429 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:58.429 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:58.430 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"D8AAC07C7B08C9029E656E326A4A9012","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884358430}
2025-01-03 14:05:58.905 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:58.906 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:58.907 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"9E082267BAB7086FC796CB0120AC6800","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884358907}
2025-01-03 14:05:59.243 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:05:59.243 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:05:59.244 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"4A0AC85313D10876D5ABB4A5A58DDA90","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884359244}
2025-01-03 14:06:00.368 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:00.369 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:00.370 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"90E104C81B38C5A85387D8E112244EBE","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884360369}
2025-01-03 14:06:00.538 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:00.538 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:00.539 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"902DD62CAA2A2D37A3FF1E539F95B7C6","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884360539}
2025-01-03 14:06:00.721 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:00.722 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:00.723 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"7B177B585B39DA8DC240735A6142F087","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884360722}
2025-01-03 14:06:00.906 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:00.906 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:00.907 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"4F1B374B13F34768AEA9D6F688582407","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884360907}
2025-01-03 14:06:01.048 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:01.048 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:01.049 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"C1D1077BC19A36EB1D694F012E4B2F78","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884361049}
2025-01-03 14:06:01.216 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:01.216 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:01.216 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"5303DA211EFEFF549EC469299126A89B","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884361216}
2025-01-03 14:06:01.385 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:01.385 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:01.386 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"86A76621CB390AE92C94D7897D90857C","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884361386}
2025-01-03 14:06:01.532 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:01.532 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:01.533 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"3AEB20931AC7C45B783E14FA48228266","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884361533}
2025-01-03 14:06:01.711 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:01.711 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:01.711 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"74674BE1771E3BAC13BE21409EFE9C53","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884361711}
2025-01-03 14:06:12.783 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:12.785 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:12.785 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"CE70FDA66BA4B98080D62E9EB12B8A3D","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884372785}
2025-01-03 14:06:12.977 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:12.978 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:12.978 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"526E9E1059CBEE12223C3724085A6EED","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884372978}
2025-01-03 14:06:18.838 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:18.839 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:18.840 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"EC9EEFD0A4DC1BA16D1F7C0CE09F1476","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884378840}
2025-01-03 14:06:19.045 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:19.046 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:19.047 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"5FE98763CE5604230EEDCFF9327644F4","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884379047}
2025-01-03 14:06:19.473 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:19.473 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:19.473 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"A09C5DC6ECB1AC6A3E853545B6883CA3","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884379473}
2025-01-03 14:06:19.614 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:19.614 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:19.615 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"817BB39E930D87E94E7D41002C9D9565","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884379615}
2025-01-03 14:06:19.653 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:19.653 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:19.655 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"B43DD16424BF3558B130565E14F08D9D","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884379654}
2025-01-03 14:06:19.831 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:19.831 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:19.833 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"ACBA63149FFC16912F42620CF3D108CF","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884379833}
2025-01-03 14:06:19.958 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:19.958 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:19.959 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"81C77A4CEC34479C8A32F52DE9189BCA","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884379959}
2025-01-03 14:06:19.970 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:19.970 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:19.972 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"78E012371B3EECF05E9CBC80AC519C41","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884379970}
2025-01-03 14:06:20.116 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:20.117 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:20.117 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"2F1FDEDE5474D598439C70CE5ECA8AB2","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884380117}
2025-01-03 14:06:20.822 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:20.822 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:20.823 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"03BFB8891D0D2D0FC4CBD4A31E726D74","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884380823}
2025-01-03 14:06:21.022 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:21.022 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:06:21.023 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"10014DF86873B49CC4CEB7F17D2DF03B","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735884381023}
2025-01-03 14:06:42.247 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:06:42.247 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:07:57.150 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 8364 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-03 14:07:57.153 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-03 14:07:58.135 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-03 14:07:58.137 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-03 14:07:58.224 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 76 ms. Found 0 Redis repository interfaces.
2025-01-03 14:07:58.837 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-03 14:07:58.845 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-03 14:07:58.845 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-03 14:07:58.845 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-03 14:07:59.041 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-03 14:07:59.042 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1849 ms
2025-01-03 14:07:59.122 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-03 14:07:59.320 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-03 14:08:00.199 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-03 14:08:00.542 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-03 14:08:00.542 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-03 14:08:01.609 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-03 14:08:01.714 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-03 14:08:01.736 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-03 14:08:01.741 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-03 14:08:01.893 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-03 14:08:02.164 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-03 14:08:02.171 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-03 14:08:02.256 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.48 seconds (JVM running for 6.629)
2025-01-03 14:10:02.071 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-03 14:10:02.071 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-03 14:10:02.072 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-03 14:10:24.068 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 14:10:25.126 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 14:10:25.126 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 14:15:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 14:08:01,891 to 2025-01-03 14:15:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 14:30:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 14:15:00,011 to 2025-01-03 14:30:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 14:45:00.041 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 14:30:00,004 to 2025-01-03 14:45:00,027
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 15:00:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 14:45:00,027 to 2025-01-03 15:00:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 15:00:00.096 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 15:04:57.894 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 37392 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-03 15:04:57.896 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-03 15:04:58.917 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-03 15:04:58.919 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-03 15:04:58.986 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 55 ms. Found 0 Redis repository interfaces.
2025-01-03 15:04:59.623 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-03 15:04:59.631 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-03 15:04:59.632 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-03 15:04:59.632 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-03 15:04:59.794 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-03 15:04:59.794 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1858 ms
2025-01-03 15:04:59.871 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-03 15:05:00.090 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-03 15:05:00.880 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-03 15:05:01.242 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-03 15:05:01.242 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-03 15:05:02.304 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-03 15:05:02.411 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-03 15:05:02.437 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-03 15:05:02.443 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-03 15:05:02.597 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-03 15:05:02.861 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-03 15:05:02.870 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-03 15:05:02.950 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.42 seconds (JVM running for 6.511)
2025-01-03 15:05:11.758 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-03 15:05:11.758 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-03 15:05:11.759 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-03 15:05:16.100 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 15:05:16.101 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 15:05:16.468 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 15:05:16.469 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 15:05:53.192 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:05:54.150 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 15:05:54.151 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 15:06:06.408 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:06:06.442 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:06:32.026 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:06:32.028 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:07:04.319 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 55320 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-03 15:07:04.321 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-03 15:07:05.258 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-03 15:07:05.260 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-03 15:07:05.338 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 68 ms. Found 0 Redis repository interfaces.
2025-01-03 15:07:05.925 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-03 15:07:05.932 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-03 15:07:05.932 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-03 15:07:05.933 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-03 15:07:06.093 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-03 15:07:06.093 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1732 ms
2025-01-03 15:07:06.168 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-03 15:07:06.346 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-03 15:07:07.091 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-03 15:07:07.468 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-03 15:07:07.468 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-03 15:07:08.494 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-03 15:07:08.595 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-03 15:07:08.618 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-03 15:07:08.623 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-03 15:07:08.773 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-03 15:07:09.030 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-03 15:07:09.037 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-03 15:07:09.113 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.153 seconds (JVM running for 6.078)
2025-01-03 15:07:17.371 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-03 15:07:17.372 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-03 15:07:17.373 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-03 15:07:18.090 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:07:18.096 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:07:18.123 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:07:18.126 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:07:43.138 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:07:43.139 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:08:37.549 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:08:37.550 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:08:37.723 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:08:37.726 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:15:00.021 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 15:07:08,772 to 2025-01-03 15:15:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 15:17:24.403 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:17:24.403 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:17:24.403 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:17:24.404 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:17:29.022 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:17:29.022 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:17:45.607 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:17:45.607 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:17:51.899 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 36092 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-03 15:17:51.901 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-03 15:17:52.947 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-03 15:17:52.949 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-03 15:17:53.027 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 66 ms. Found 0 Redis repository interfaces.
2025-01-03 15:17:53.643 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-03 15:17:53.651 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-03 15:17:53.651 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-03 15:17:53.652 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-03 15:17:53.844 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-03 15:17:53.844 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1904 ms
2025-01-03 15:17:53.926 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-03 15:17:54.118 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-03 15:17:54.909 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-03 15:17:55.241 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-03 15:17:55.241 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-03 15:17:56.344 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-03 15:17:56.451 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-03 15:17:56.478 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-03 15:17:56.485 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-03 15:17:56.647 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-03 15:17:56.919 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-03 15:17:56.928 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-03 15:17:57.009 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.459 seconds (JVM running for 6.536)
2025-01-03 15:18:19.963 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-03 15:18:19.964 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-03 15:18:19.965 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-03 15:18:31.206 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:18:31.207 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:18:31.207 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:18:31.207 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:18:38.727 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:18:38.727 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:18:56.277 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:18:56.278 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:19:03.225 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:19:03.225 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:19:20.887 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:19:20.888 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:19:27.766 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:19:27.767 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:19:45.653 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:19:45.653 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:20:13.230 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:20:13.230 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:20:16.889 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:api.weixin.qq.com
2025-01-03 15:20:16.889 ERROR com.ymiots.campusmp.utils.weixin.WeiXinOAuth - api.weixin.qq.com
2025-01-03 15:20:21.398 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-01-03 15:20:21.635 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-01-03 15:20:21.640 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-01-03 15:20:26.258 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 42308 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-03 15:20:26.261 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-03 15:20:27.168 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-03 15:20:27.170 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-03 15:20:27.247 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 67 ms. Found 0 Redis repository interfaces.
2025-01-03 15:20:27.839 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-03 15:20:27.846 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-03 15:20:27.847 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-03 15:20:27.847 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-03 15:20:28.003 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-03 15:20:28.003 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1703 ms
2025-01-03 15:20:28.072 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-03 15:20:28.257 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-03 15:20:28.967 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-03 15:20:29.294 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-03 15:20:29.294 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-03 15:20:30.322 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-03 15:20:30.424 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-03 15:20:30.445 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-03 15:20:30.450 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-03 15:20:30.589 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-03 15:20:30.836 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-03 15:20:30.842 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-03 15:20:30.917 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.008 seconds (JVM running for 6.035)
2025-01-03 15:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 15:20:30,588 to 2025-01-03 15:30:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 15:36:10.204 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-03 15:36:10.206 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-03 15:36:10.207 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-03 15:36:16.692 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:36:16.703 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:39:34.211 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 56400 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-03 15:39:34.214 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-03 15:39:35.233 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-03 15:39:35.234 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-03 15:39:35.314 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 68 ms. Found 0 Redis repository interfaces.
2025-01-03 15:39:35.938 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-03 15:39:35.945 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-03 15:39:35.946 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-03 15:39:35.946 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-03 15:39:36.137 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-03 15:39:36.137 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1884 ms
2025-01-03 15:39:36.214 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-03 15:39:36.404 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-03 15:39:37.251 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-03 15:39:37.673 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-03 15:39:37.673 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-03 15:39:38.747 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-03 15:39:38.847 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-03 15:39:38.870 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-03 15:39:38.875 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-03 15:39:39.023 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-03 15:39:39.286 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-03 15:39:39.294 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-03 15:39:39.375 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.518 seconds (JVM running for 6.526)
2025-01-03 15:39:57.304 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-03 15:39:57.324 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-03 15:39:57.324 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-03 15:39:57.328 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-01-03 15:39:59.144 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 15:39:59.146 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 15:40:04.728 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-03 15:40:04.729 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-03 15:40:22.218 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 72208 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-03 15:40:22.220 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-03 15:40:23.208 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-03 15:40:23.209 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-03 15:40:23.287 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 67 ms. Found 0 Redis repository interfaces.
2025-01-03 15:40:23.948 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-03 15:40:23.959 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-03 15:40:23.960 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-03 15:40:23.960 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-03 15:40:24.152 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-03 15:40:24.152 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1892 ms
2025-01-03 15:40:24.232 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-03 15:40:24.461 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-03 15:40:25.302 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-03 15:40:25.749 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-03 15:40:25.749 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-03 15:40:26.870 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-03 15:40:26.971 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-03 15:40:26.994 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-03 15:40:26.999 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-03 15:40:27.167 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-03 15:40:27.427 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-03 15:40:27.436 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-03 15:40:27.526 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.679 seconds (JVM running for 6.702)
2025-01-03 15:43:39.999 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-03 15:43:40.000 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-03 15:43:40.001 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-03 15:43:41.147 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:43:41.151 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:43:41.159 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:43:41.162 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:43:41.163 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:43:41.163 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:43:41.218 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-03 15:43:41.219 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 15:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 15:40:27,165 to 2025-01-03 15:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 16:00:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 15:45:00,010 to 2025-01-03 16:00:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 16:00:00.012 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 16:15:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 16:00:00,004 to 2025-01-03 16:15:00,000
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 16:30:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 16:15:00,000 to 2025-01-03 16:30:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 16:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 16:30:00,008 to 2025-01-03 16:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 17:00:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 16:45:00,010 to 2025-01-03 17:00:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 17:00:00.021 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 17:00:11.221 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:pay.ymiots.com
2025-01-03 17:00:11.224 ERROR com.ymiots.campusmp.utils.weixin.WeiXinAccessToken - 获取微信accesstoken云服务接口错误：pay.ymiots.com
2025-01-03 17:00:11.225 ERROR com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token错误：AccessToken is null
2025-01-03 17:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 17:00:00,007 to 2025-01-03 17:15:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 17:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 17:15:00,006 to 2025-01-03 17:30:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 17:45:01.102 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 17:30:00,013 to 2025-01-03 17:45:01,099
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 18:00:00.010 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 18:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 17:45:01,099 to 2025-01-03 18:00:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 18:00:00.035 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:pay.ymiots.com
2025-01-03 18:00:00.035 ERROR com.ymiots.campusmp.utils.weixin.WeiXinAccessToken - 获取微信accesstoken云服务接口错误：pay.ymiots.com
2025-01-03 18:00:00.036 ERROR com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token错误：AccessToken is null
2025-01-03 18:15:00.028 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 18:00:00,015 to 2025-01-03 18:15:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 18:30:00.028 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 18:15:00,001 to 2025-01-03 18:30:00,028
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 18:45:00.027 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 18:30:00,028 to 2025-01-03 18:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 19:00:00.001 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 19:00:00.027 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 18:45:00,011 to 2025-01-03 19:00:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 19:15:00.027 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 19:00:00,006 to 2025-01-03 19:15:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 19:30:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 19:15:00,001 to 2025-01-03 19:30:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 19:45:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 19:30:00,012 to 2025-01-03 19:45:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 20:00:00.027 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 20:00:00.028 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 19:45:00,005 to 2025-01-03 20:00:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 20:15:00.029 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 20:00:00,013 to 2025-01-03 20:15:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 20:30:00.032 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 20:15:00,002 to 2025-01-03 20:30:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

