2025-07-12 09:23:23.826 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 2848 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 09:23:23.826 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 09:23:23.829 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 09:23:25.852 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 09:23:25.856 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 09:23:26.006 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 134 ms. Found 0 Redis repository interfaces.
2025-07-12 09:23:26.507 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$5fa954d6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:23:26.677 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$deaf9a4e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:23:27.263 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 09:23:27.293 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 09:23:27.294 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 09:23:27.294 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 09:23:27.570 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 09:23:27.570 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3663 ms
2025-07-12 09:23:27.697 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 09:23:27.785 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 09:23:28.108 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 09:23:28.660 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 09:23:31.114 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 09:23:31.270 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 09:23:31.270 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 09:23:32.330 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:23:32.331 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:23:32.333 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:23:32.333 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:23:32.336 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:23:32.508 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:23:32.508 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:23:32.510 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:23:32.510 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:23:32.510 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:23:32.510 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 09:23:33.115 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 09:23:33.350 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 09:23:33.384 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 09:23:33.394 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 09:23:33.529 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 09:23:34.029 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 09:23:34.037 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 09:23:34.213 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$f9438ba3 - 计划任务最多执行：58个
2025-07-12 09:23:34.287 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 09:23:34.292 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 09:23:34.294 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 09:23:34.294 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 09:23:34.297 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 09:23:34.299 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 09:23:34.299 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 09:23:34.302 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 09:23:34.305 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 09:23:34.308 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 09:23:34.309 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 09:23:34.312 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 09:23:34.320 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 10.96 seconds (JVM running for 12.359)
2025-07-12 09:23:34.753 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 09:23:47.626 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 09:23:47.626 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 09:23:47.628 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-12 09:23:50.364 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 09:24:00.040 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:24:04.533 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 721, 月份: 2025-06
2025-07-12 09:24:10.993 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:24:11.085 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 3, 月份: 2025-06
2025-07-12 09:25:02.877 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:25:02.975 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-06
2025-07-12 09:25:13.153 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:25:13.207 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 3, 月份: 2025-05
2025-07-12 09:25:58.574 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:26:31.145 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 3, 月份: 2025-05
2025-07-12 09:26:32.348 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 09:26:32.349 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 09:26:32.349 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 09:26:32.744 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 09:26:32.755 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 09:26:39.518 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 38276 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 09:26:39.517 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 09:26:39.520 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 09:26:40.945 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 09:26:40.948 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 09:26:41.093 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 134 ms. Found 0 Redis repository interfaces.
2025-07-12 09:26:41.512 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$25e2e60c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:26:41.607 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a4e92b84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:26:41.917 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 09:26:41.938 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 09:26:41.938 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 09:26:41.939 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 09:26:42.231 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 09:26:42.233 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2641 ms
2025-07-12 09:26:42.337 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 09:26:42.412 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 09:26:42.699 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 09:26:43.228 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 09:26:45.436 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 09:26:45.578 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 09:26:45.578 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 09:26:46.595 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:26:46.596 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:26:46.598 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:26:46.598 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:26:46.601 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:26:46.773 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:26:46.773 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:26:46.774 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:26:46.774 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:26:46.774 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:26:46.774 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 09:26:47.335 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 09:26:47.556 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 09:26:47.585 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 09:26:47.594 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 09:26:47.728 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 09:26:48.179 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 09:26:48.187 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 09:26:48.353 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$bf7d1cd9 - 计划任务最多执行：58个
2025-07-12 09:26:48.423 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 09:26:48.428 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 09:26:48.430 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 09:26:48.430 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 09:26:48.433 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 09:26:48.435 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 09:26:48.435 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 09:26:48.438 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 09:26:48.440 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 09:26:48.443 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 09:26:48.445 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 09:26:48.446 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 09:26:48.455 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.348 seconds (JVM running for 10.437)
2025-07-12 09:26:48.851 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 09:26:52.562 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 09:26:52.563 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 09:26:52.566 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-12 09:26:54.760 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 09:27:06.701 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:27:40.259 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:27:40.285 WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.ymiots.campusos.common.GlobalExceptionHandler#handlerException(Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:784)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:299)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:273)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:228)
	at com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter.write(FastJsonHttpMessageConverter.java:246)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:290)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1330)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:469)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:593)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:537)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
	at org.apache.coyote.http11.filters.IdentityOutputFilter.doWrite(IdentityOutputFilter.java:73)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
	at org.apache.coyote.Response.doWrite(Response.java:615)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
	... 56 common frames omitted
2025-07-12 09:27:48.084 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:27:48.147 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-05
2025-07-12 09:28:01.390 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:28:01.481 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-06
2025-07-12 09:29:24.853 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 09:29:24.854 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 09:29:24.855 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 09:29:25.007 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 09:29:25.015 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 09:29:32.081 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 30704 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 09:29:32.081 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 09:29:32.083 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 09:29:33.513 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 09:29:33.516 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 09:29:33.660 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 132 ms. Found 0 Redis repository interfaces.
2025-07-12 09:29:34.065 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$c76bca90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:29:34.147 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$46721008] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:29:34.415 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 09:29:34.425 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 09:29:34.425 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 09:29:34.425 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 09:29:34.603 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 09:29:34.603 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2448 ms
2025-07-12 09:29:34.708 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 09:29:34.777 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 09:29:35.053 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 09:29:35.566 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 09:29:37.731 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 09:29:37.870 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 09:29:37.870 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 09:29:38.858 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:29:38.859 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:29:38.860 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:29:38.861 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:29:38.864 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:29:39.034 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:29:39.034 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:29:39.036 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:29:39.036 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:29:39.036 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:29:39.036 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 09:29:39.600 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 09:29:39.823 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 09:29:39.852 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 09:29:39.864 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 09:29:39.986 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 09:29:40.427 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 09:29:40.434 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 09:29:40.596 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$6106015d - 计划任务最多执行：58个
2025-07-12 09:29:40.669 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 09:29:40.674 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 09:29:40.676 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 09:29:40.676 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 09:29:40.679 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 09:29:40.681 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 09:29:40.682 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 09:29:40.684 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 09:29:40.686 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 09:29:40.689 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 09:29:40.691 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 09:29:40.693 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 09:29:40.701 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.036 seconds (JVM running for 10.114)
2025-07-12 09:29:41.107 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 09:29:47.650 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 09:29:47.650 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 09:29:47.653 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-12 09:29:50.420 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 09:30:00.029 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 09:29:39,984 to 2025-07-12 09:30:00,025
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 09:30:25.686 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:30:25.777 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-06
2025-07-12 09:30:32.462 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:30:32.491 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-05
2025-07-12 09:30:42.110 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:30:42.158 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-06
2025-07-12 09:30:47.302 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:30:47.325 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-05
2025-07-12 09:30:55.575 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:30:56.022 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:12.742 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:13.386 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:14.272 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:14.914 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:17.583 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:18.145 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:20.117 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:20.760 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:22.630 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:23.370 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:30.592 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:31.229 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:35.515 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:36.155 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:43.821 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:44.429 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:56.727 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:57.340 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:40:00.002 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 09:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 09:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 09:40:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 09:40:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 09:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 09:30:00,025 to 2025-07-12 09:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 09:52:40.420 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 09:52:40.421 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 09:52:40.421 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 09:52:43.664 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 09:52:43.665 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-01' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 09:52:43.666 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:00:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 10:00:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 09:45:00,011 to 2025-07-12 10:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 10:00:00.015 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:00:00.016 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:00:00.016 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 10:00:00.017 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 10:00:00.028 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-12 10:00:00.029 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-12 10:00:00.029 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-12 10:00:00.029 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-12 10:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 10:00:00,010 to 2025-07-12 10:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 10:20:00.003 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 10:20:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:20:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:20:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 10:20:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 10:25:06.612 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 10:25:06.613 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 10:25:06.614 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 10:25:06.774 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 10:25:06.778 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 10:25:10.445 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 10804 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 10:25:10.444 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 10:25:10.447 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 10:25:12.058 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 10:25:12.060 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 10:25:12.213 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 140 ms. Found 0 Redis repository interfaces.
2025-07-12 10:25:12.670 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f405447a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:25:12.762 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$730b89f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:25:13.055 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 10:25:13.066 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 10:25:13.066 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 10:25:13.066 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 10:25:13.256 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 10:25:13.257 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2729 ms
2025-07-12 10:25:13.381 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 10:25:13.453 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 10:25:13.749 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 10:25:14.316 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 10:25:16.606 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 10:25:16.749 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 10:25:16.749 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 10:25:17.795 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:25:17.795 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:25:17.798 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:25:17.798 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:25:17.801 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:25:17.958 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:25:17.958 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:25:17.959 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:25:17.959 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:25:17.959 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:25:17.959 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 10:25:18.568 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 10:25:18.798 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 10:25:18.828 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 10:25:18.839 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 10:25:18.976 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 10:25:19.477 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 10:25:19.484 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 10:25:19.664 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$8d9f7b47 - 计划任务最多执行：58个
2025-07-12 10:25:19.738 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 10:25:19.743 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 10:25:19.745 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 10:25:19.745 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 10:25:19.748 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 10:25:19.750 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 10:25:19.750 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 10:25:19.753 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 10:25:19.756 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 10:25:19.758 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 10:25:19.761 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 10:25:19.763 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 10:25:19.771 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.755 seconds (JVM running for 10.912)
2025-07-12 10:25:20.199 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 10:25:27.307 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 10:25:27.308 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 10:25:27.309 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-12 10:27:09.535 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 10:27:10.566 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：null
2025-07-12 10:28:07.372 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:28:07.374 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:28:07.375 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:30:00.020 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 10:25:18,973 to 2025-07-12 10:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 10:38:09.602 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 10:38:09.604 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 10:38:09.604 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 10:38:09.765 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 10:38:09.768 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 10:38:13.348 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 33764 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 10:38:13.347 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 10:38:13.349 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 10:38:14.906 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 10:38:14.909 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 10:38:15.070 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 148 ms. Found 0 Redis repository interfaces.
2025-07-12 10:38:15.481 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b03908f0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:38:15.564 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$2f3f4e68] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:38:15.844 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 10:38:15.854 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 10:38:15.855 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 10:38:15.855 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 10:38:16.048 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 10:38:16.048 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2624 ms
2025-07-12 10:38:16.155 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 10:38:16.225 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 10:38:16.518 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 10:38:17.050 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 10:38:19.432 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 10:38:19.585 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 10:38:19.585 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 10:38:20.640 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:38:20.640 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:38:20.642 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:38:20.642 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:38:20.646 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:38:20.817 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:38:20.817 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:38:20.818 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:38:20.818 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:38:20.818 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:38:20.818 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 10:38:21.404 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 10:38:21.637 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 10:38:21.666 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 10:38:21.677 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 10:38:21.806 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 10:38:22.273 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 10:38:22.281 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 10:38:22.487 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$49d33fbd - 计划任务最多执行：58个
2025-07-12 10:38:22.580 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 10:38:22.594 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 10:38:22.596 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 10:38:22.596 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 10:38:22.601 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 10:38:22.603 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 10:38:22.603 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 10:38:22.608 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 10:38:22.616 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 10:38:22.619 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 10:38:22.621 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 10:38:22.623 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 10:38:22.623 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 10:38:22.624 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 10:38:22.625 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-12 10:38:22.635 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.705 seconds (JVM running for 10.868)
2025-07-12 10:38:22.727 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-07-12 10:38:23.063 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 10:38:33.099 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 10:38:47.776 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:38:47.776 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:38:47.776 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:39:15.330 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:39:15.330 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12' and wec.area_code like '001003%']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:39:15.332 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:39:24.458 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：null
2025-07-12 10:39:25.459 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[2]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：null
2025-07-12 10:39:35.897 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:39:35.897 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:39:35.897 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1]; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:39:35.897 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1]; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:39:35.899 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:39:35.899 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:39:38.935 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:39:38.935 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1]; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:39:38.936 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:40:00.004 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 10:40:00.013 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 10:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 10:44:28.213 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 10:44:28.215 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 10:44:28.216 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 10:44:28.388 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 10:44:28.392 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 10:44:32.320 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 10:44:32.320 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 14320 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 10:44:32.322 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 10:44:33.805 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 10:44:33.809 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 10:44:33.959 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 138 ms. Found 0 Redis repository interfaces.
2025-07-12 10:44:34.371 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$c8222c09] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:44:34.453 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$47287181] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:44:34.725 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 10:44:34.735 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 10:44:34.736 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 10:44:34.736 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 10:44:34.926 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 10:44:34.926 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2533 ms
2025-07-12 10:44:35.032 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 10:44:35.099 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 10:44:35.375 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 10:44:35.918 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 10:44:38.111 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 10:44:38.264 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 10:44:38.264 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 10:44:39.285 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:44:39.286 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:44:39.288 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:44:39.288 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:44:39.292 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:44:39.459 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:44:39.459 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:44:39.460 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:44:39.460 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:44:39.460 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:44:39.460 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 10:44:40.034 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 10:44:40.262 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 10:44:40.290 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 10:44:40.300 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 10:44:40.421 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 10:44:40.887 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 10:44:40.895 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 10:44:41.071 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$61bc62d6 - 计划任务最多执行：58个
2025-07-12 10:44:41.149 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 10:44:41.155 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 10:44:41.157 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 10:44:41.157 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 10:44:41.160 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 10:44:41.163 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 10:44:41.163 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 10:44:41.166 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 10:44:41.168 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 10:44:41.171 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 10:44:41.173 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 10:44:41.175 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 10:44:41.183 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.288 seconds (JVM running for 10.508)
2025-07-12 10:44:41.670 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 10:45:00.019 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 10:44:40,419 to 2025-07-12 10:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 10:45:14.697 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 10:45:14.697 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 10:45:14.699 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-12 10:45:18.440 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 10:45:39.506 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:45:39.506 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:45:39.506 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:45:41.294 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:45:41.294 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:45:41.296 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:46:25.520 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 10:46:25.522 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 10:46:25.522 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 10:46:25.680 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 10:46:25.687 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 10:46:29.158 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 29448 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 10:46:29.156 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 10:46:29.159 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 10:46:30.720 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 10:46:30.722 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 10:46:30.879 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 144 ms. Found 0 Redis repository interfaces.
2025-07-12 10:46:31.305 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f405447a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:46:31.388 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$730b89f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:46:31.661 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 10:46:31.670 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 10:46:31.671 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 10:46:31.671 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 10:46:31.861 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 10:46:31.862 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2612 ms
2025-07-12 10:46:31.984 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 10:46:32.049 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 10:46:33.516 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 10:46:34.050 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 10:46:36.246 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 10:46:36.390 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 10:46:36.390 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 10:46:37.473 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 0 个待执行的重试任务
2025-07-12 10:46:37.681 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 0 个待执行的重试任务
2025-07-12 10:46:37.681 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 10:46:38.347 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 10:46:38.635 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 10:46:38.666 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 10:46:38.680 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 10:46:38.820 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 10:46:39.316 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 10:46:39.323 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 10:46:39.502 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$8d9f7b47 - 计划任务最多执行：66个
2025-07-12 10:46:39.570 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 10:46:39.576 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 10:46:39.576 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-12 10:46:39.579 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-12 10:46:39.581 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 10:46:39.581 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 10:46:39.585 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 10:46:39.589 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-12 10:46:39.592 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 10:46:39.594 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-12 10:46:39.597 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-12 10:46:39.599 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 10:46:39.601 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-12 10:46:39.604 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-12 10:46:39.606 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 10:46:39.609 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 10:46:39.611 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 10:46:39.612 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-12 10:46:39.614 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 10:46:39.639 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 10.924 seconds (JVM running for 12.056)
2025-07-12 10:46:40.078 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 10:46:50.288 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 10:46:50.288 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 10:46:50.289 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-12 10:47:12.641 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：null
2025-07-12 10:47:52.017 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[2]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 11:00:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 11:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 10:46:38,818 to 2025-07-12 11:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 11:00:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:00:00.040 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:00:00.050 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 11:00:00.050 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 11:00:00.115 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-12 11:00:00.129 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-12 11:00:00.140 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-12 11:00:00.140 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-12 11:15:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 11:00:00,010 to 2025-07-12 11:15:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 11:20:00.002 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 11:20:00.020 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:20:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:20:00.042 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 11:20:00.042 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 11:30:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 11:15:00,003 to 2025-07-12 11:30:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 11:40:00.006 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 11:40:00.021 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:40:00.032 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:40:00.043 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 11:40:00.043 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 11:43:37.604 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 11:43:37.607 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 11:43:37.607 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 11:43:37.790 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 11:43:37.797 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 11:45:02.537 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 38456 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 11:45:02.536 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 11:45:02.539 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 11:45:04.211 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 11:45:04.214 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 11:45:04.391 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 163 ms. Found 0 Redis repository interfaces.
2025-07-12 11:45:04.838 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$288dd69f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 11:45:04.925 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a7941c17] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 11:45:05.222 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 11:45:05.233 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 11:45:05.234 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 11:45:05.234 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 11:45:05.425 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 11:45:05.425 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2788 ms
2025-07-12 11:45:05.544 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 11:45:05.608 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 11:45:07.980 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 11:45:08.587 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 11:45:10.815 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 11:45:10.963 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 11:45:10.963 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 11:45:12.045 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 0 个待执行的重试任务
2025-07-12 11:45:12.287 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 0 个待执行的重试任务
2025-07-12 11:45:12.287 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 11:45:12.859 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 11:45:13.082 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 11:45:13.110 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 11:45:13.120 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 11:45:13.247 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 11:45:13.710 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 11:45:13.717 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 11:45:13.894 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$c2280d6c - 计划任务最多执行：66个
2025-07-12 11:45:13.957 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 11:45:13.962 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 11:45:13.962 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-12 11:45:13.964 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-12 11:45:13.966 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 11:45:13.967 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 11:45:13.971 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 11:45:13.973 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-12 11:45:13.975 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 11:45:13.977 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-12 11:45:13.979 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-12 11:45:13.982 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 11:45:13.984 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-12 11:45:13.987 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-12 11:45:13.989 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 11:45:13.991 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 11:45:13.994 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 11:45:13.994 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-12 11:45:13.996 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 11:45:14.017 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 12.059 seconds (JVM running for 13.263)
2025-07-12 11:45:14.477 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 11:45:17.671 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 11:45:17.671 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 11:45:17.674 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-12 11:45:22.155 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
