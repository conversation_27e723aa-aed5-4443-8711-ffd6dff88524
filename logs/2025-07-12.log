2025-07-12 09:23:23.826 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 2848 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 09:23:23.826 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 09:23:23.829 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 09:23:25.852 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 09:23:25.856 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 09:23:26.006 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 134 ms. Found 0 Redis repository interfaces.
2025-07-12 09:23:26.507 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$5fa954d6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:23:26.677 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$deaf9a4e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:23:27.263 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 09:23:27.293 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 09:23:27.294 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 09:23:27.294 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 09:23:27.570 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 09:23:27.570 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3663 ms
2025-07-12 09:23:27.697 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 09:23:27.785 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 09:23:28.108 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 09:23:28.660 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 09:23:31.114 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 09:23:31.270 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 09:23:31.270 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 09:23:32.330 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:23:32.331 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:23:32.333 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:23:32.333 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:23:32.336 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:23:32.508 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:23:32.508 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:23:32.510 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:23:32.510 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:23:32.510 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:23:32.510 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 09:23:33.115 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 09:23:33.350 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 09:23:33.384 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 09:23:33.394 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 09:23:33.529 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 09:23:34.029 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 09:23:34.037 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 09:23:34.213 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$f9438ba3 - 计划任务最多执行：58个
2025-07-12 09:23:34.287 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 09:23:34.292 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 09:23:34.294 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 09:23:34.294 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 09:23:34.297 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 09:23:34.299 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 09:23:34.299 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 09:23:34.302 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 09:23:34.305 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 09:23:34.308 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 09:23:34.309 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 09:23:34.312 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 09:23:34.320 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 10.96 seconds (JVM running for 12.359)
2025-07-12 09:23:34.753 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 09:23:47.626 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 09:23:47.626 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 09:23:47.628 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-12 09:23:50.364 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 09:24:00.040 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:24:04.533 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 721, 月份: 2025-06
2025-07-12 09:24:10.993 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:24:11.085 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 3, 月份: 2025-06
2025-07-12 09:25:02.877 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:25:02.975 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-06
2025-07-12 09:25:13.153 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:25:13.207 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 3, 月份: 2025-05
2025-07-12 09:25:58.574 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:26:31.145 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 3, 月份: 2025-05
2025-07-12 09:26:32.348 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 09:26:32.349 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 09:26:32.349 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 09:26:32.744 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 09:26:32.755 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 09:26:39.518 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 38276 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 09:26:39.517 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 09:26:39.520 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 09:26:40.945 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 09:26:40.948 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 09:26:41.093 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 134 ms. Found 0 Redis repository interfaces.
2025-07-12 09:26:41.512 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$25e2e60c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:26:41.607 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a4e92b84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:26:41.917 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 09:26:41.938 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 09:26:41.938 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 09:26:41.939 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 09:26:42.231 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 09:26:42.233 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2641 ms
2025-07-12 09:26:42.337 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 09:26:42.412 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 09:26:42.699 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 09:26:43.228 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 09:26:45.436 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 09:26:45.578 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 09:26:45.578 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 09:26:46.595 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:26:46.596 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:26:46.598 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:26:46.598 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:26:46.601 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:26:46.773 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:26:46.773 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:26:46.774 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:26:46.774 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:26:46.774 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:26:46.774 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 09:26:47.335 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 09:26:47.556 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 09:26:47.585 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 09:26:47.594 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 09:26:47.728 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 09:26:48.179 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 09:26:48.187 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 09:26:48.353 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$bf7d1cd9 - 计划任务最多执行：58个
2025-07-12 09:26:48.423 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 09:26:48.428 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 09:26:48.430 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 09:26:48.430 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 09:26:48.433 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 09:26:48.435 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 09:26:48.435 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 09:26:48.438 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 09:26:48.440 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 09:26:48.443 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 09:26:48.445 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 09:26:48.446 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 09:26:48.455 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.348 seconds (JVM running for 10.437)
2025-07-12 09:26:48.851 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 09:26:52.562 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 09:26:52.563 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 09:26:52.566 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-12 09:26:54.760 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 09:27:06.701 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:27:40.259 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:27:40.285 WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.ymiots.campusos.common.GlobalExceptionHandler#handlerException(Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:784)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:299)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:273)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:228)
	at com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter.write(FastJsonHttpMessageConverter.java:246)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:290)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1330)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:469)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:593)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:537)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
	at org.apache.coyote.http11.filters.IdentityOutputFilter.doWrite(IdentityOutputFilter.java:73)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
	at org.apache.coyote.Response.doWrite(Response.java:615)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
	... 56 common frames omitted
2025-07-12 09:27:48.084 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:27:48.147 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-05
2025-07-12 09:28:01.390 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:28:01.481 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-06
2025-07-12 09:29:24.853 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 09:29:24.854 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 09:29:24.855 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 09:29:25.007 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 09:29:25.015 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 09:29:32.081 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 30704 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 09:29:32.081 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 09:29:32.083 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 09:29:33.513 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 09:29:33.516 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 09:29:33.660 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 132 ms. Found 0 Redis repository interfaces.
2025-07-12 09:29:34.065 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$c76bca90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:29:34.147 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$46721008] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 09:29:34.415 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 09:29:34.425 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 09:29:34.425 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 09:29:34.425 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 09:29:34.603 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 09:29:34.603 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2448 ms
2025-07-12 09:29:34.708 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 09:29:34.777 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 09:29:35.053 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 09:29:35.566 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 09:29:37.731 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 09:29:37.870 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 09:29:37.870 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 09:29:38.858 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:29:38.859 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:29:38.860 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:29:38.861 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:29:38.864 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:29:39.034 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 09:29:39.034 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:29:39.036 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 09:29:39.036 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 09:29:39.036 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 09:29:39.036 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 09:29:39.600 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 09:29:39.823 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 09:29:39.852 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 09:29:39.864 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 09:29:39.986 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 09:29:40.427 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 09:29:40.434 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 09:29:40.596 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$6106015d - 计划任务最多执行：58个
2025-07-12 09:29:40.669 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 09:29:40.674 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 09:29:40.676 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 09:29:40.676 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 09:29:40.679 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 09:29:40.681 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 09:29:40.682 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 09:29:40.684 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 09:29:40.686 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 09:29:40.689 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 09:29:40.691 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 09:29:40.693 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 09:29:40.701 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.036 seconds (JVM running for 10.114)
2025-07-12 09:29:41.107 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 09:29:47.650 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 09:29:47.650 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 09:29:47.653 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-12 09:29:50.420 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 09:30:00.029 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 09:29:39,984 to 2025-07-12 09:30:00,025
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 09:30:25.686 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:30:25.777 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-06
2025-07-12 09:30:32.462 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:30:32.491 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-05
2025-07-12 09:30:42.110 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-07-01 00:00:00 到 2025-07-31 23:59:59, 保存月报: false
2025-07-12 09:30:42.158 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-06
2025-07-12 09:30:47.302 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:30:47.325 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 2, 月份: 2025-05
2025-07-12 09:30:55.575 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:30:56.022 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:12.742 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:13.386 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:14.272 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:14.914 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:17.583 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:18.145 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:20.117 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:20.760 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:22.630 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:23.370 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:30.592 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:31.229 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:35.515 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:36.155 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:43.821 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:44.429 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:31:56.727 INFO  c.y.c.service.consume.TransactionStatService - 开始统计交易数据 - 时间范围: 2025-06-01 00:00:00 到 2025-06-30 23:59:59, 保存月报: false
2025-07-12 09:31:57.340 INFO  c.y.c.service.consume.TransactionStatService - 统计完成 - 记录数: 424, 月份: 2025-05
2025-07-12 09:40:00.002 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 09:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 09:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 09:40:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 09:40:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 09:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 09:30:00,025 to 2025-07-12 09:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 09:52:40.420 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 09:52:40.421 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 09:52:40.421 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 09:52:43.664 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 09:52:43.665 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-01' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 09:52:43.666 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:00:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 10:00:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 09:45:00,011 to 2025-07-12 10:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 10:00:00.015 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:00:00.016 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:00:00.016 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 10:00:00.017 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 10:00:00.028 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-12 10:00:00.029 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-12 10:00:00.029 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-12 10:00:00.029 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-12 10:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 10:00:00,010 to 2025-07-12 10:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 10:20:00.003 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 10:20:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:20:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:20:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 10:20:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 10:25:06.612 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 10:25:06.613 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 10:25:06.614 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 10:25:06.774 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 10:25:06.778 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 10:25:10.445 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 10804 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 10:25:10.444 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 10:25:10.447 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 10:25:12.058 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 10:25:12.060 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 10:25:12.213 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 140 ms. Found 0 Redis repository interfaces.
2025-07-12 10:25:12.670 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f405447a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:25:12.762 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$730b89f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:25:13.055 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 10:25:13.066 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 10:25:13.066 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 10:25:13.066 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 10:25:13.256 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 10:25:13.257 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2729 ms
2025-07-12 10:25:13.381 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 10:25:13.453 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 10:25:13.749 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 10:25:14.316 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 10:25:16.606 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 10:25:16.749 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 10:25:16.749 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 10:25:17.795 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:25:17.795 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:25:17.798 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:25:17.798 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:25:17.801 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:25:17.958 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:25:17.958 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:25:17.959 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:25:17.959 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:25:17.959 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:25:17.959 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 10:25:18.568 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 10:25:18.798 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 10:25:18.828 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 10:25:18.839 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 10:25:18.976 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 10:25:19.477 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 10:25:19.484 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 10:25:19.664 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$8d9f7b47 - 计划任务最多执行：58个
2025-07-12 10:25:19.738 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 10:25:19.743 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 10:25:19.745 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 10:25:19.745 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 10:25:19.748 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 10:25:19.750 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 10:25:19.750 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 10:25:19.753 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 10:25:19.756 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 10:25:19.758 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 10:25:19.761 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 10:25:19.763 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 10:25:19.771 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.755 seconds (JVM running for 10.912)
2025-07-12 10:25:20.199 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 10:25:27.307 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 10:25:27.308 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 10:25:27.309 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-12 10:27:09.535 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 10:27:10.566 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：null
2025-07-12 10:28:07.372 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:28:07.374 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:28:07.375 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:30:00.020 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 10:25:18,973 to 2025-07-12 10:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 10:38:09.602 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 10:38:09.604 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 10:38:09.604 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 10:38:09.765 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 10:38:09.768 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 10:38:13.348 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 33764 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 10:38:13.347 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 10:38:13.349 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 10:38:14.906 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 10:38:14.909 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 10:38:15.070 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 148 ms. Found 0 Redis repository interfaces.
2025-07-12 10:38:15.481 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b03908f0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:38:15.564 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$2f3f4e68] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:38:15.844 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 10:38:15.854 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 10:38:15.855 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 10:38:15.855 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 10:38:16.048 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 10:38:16.048 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2624 ms
2025-07-12 10:38:16.155 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 10:38:16.225 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 10:38:16.518 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 10:38:17.050 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 10:38:19.432 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 10:38:19.585 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 10:38:19.585 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 10:38:20.640 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:38:20.640 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:38:20.642 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:38:20.642 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:38:20.646 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:38:20.817 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:38:20.817 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:38:20.818 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:38:20.818 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:38:20.818 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:38:20.818 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 10:38:21.404 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 10:38:21.637 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 10:38:21.666 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 10:38:21.677 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 10:38:21.806 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 10:38:22.273 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 10:38:22.281 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 10:38:22.487 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$49d33fbd - 计划任务最多执行：58个
2025-07-12 10:38:22.580 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 10:38:22.594 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 10:38:22.596 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 10:38:22.596 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 10:38:22.601 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 10:38:22.603 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 10:38:22.603 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 10:38:22.608 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 10:38:22.616 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 10:38:22.619 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 10:38:22.621 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 10:38:22.623 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 10:38:22.623 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 10:38:22.624 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 10:38:22.625 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-12 10:38:22.635 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.705 seconds (JVM running for 10.868)
2025-07-12 10:38:22.727 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-07-12 10:38:23.063 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 10:38:33.099 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 10:38:47.776 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:38:47.776 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:38:47.776 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:39:15.330 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:39:15.330 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12' and wec.area_code like '001003%']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:39:15.332 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:39:24.458 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：null
2025-07-12 10:39:25.459 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[2]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：null
2025-07-12 10:39:35.897 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:39:35.897 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:39:35.897 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1]; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:39:35.897 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1]; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:39:35.899 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:39:35.899 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:39:38.935 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:39:38.935 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1]; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:39:38.936 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:40:00.004 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 10:40:00.013 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 10:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 10:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 10:44:28.213 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 10:44:28.215 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 10:44:28.216 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 10:44:28.388 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 10:44:28.392 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 10:44:32.320 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 10:44:32.320 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 14320 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 10:44:32.322 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 10:44:33.805 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 10:44:33.809 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 10:44:33.959 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 138 ms. Found 0 Redis repository interfaces.
2025-07-12 10:44:34.371 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$c8222c09] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:44:34.453 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$47287181] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:44:34.725 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 10:44:34.735 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 10:44:34.736 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 10:44:34.736 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 10:44:34.926 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 10:44:34.926 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2533 ms
2025-07-12 10:44:35.032 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 10:44:35.099 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 10:44:35.375 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 10:44:35.918 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 10:44:38.111 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 10:44:38.264 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 10:44:38.264 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 10:44:39.285 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:44:39.286 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:44:39.288 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:44:39.288 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:44:39.292 ERROR c.y.c.service.waterctrl.PersistentRetryTaskService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:44:39.459 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']
2025-07-12 10:44:39.459 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [UPDATE tb_retry_tasks SET status = 'PENDING', modify_date = NOW() WHERE status = 'EXECUTING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:44:39.460 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']
2025-07-12 10:44:39.460 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT COUNT(*) as total FROM tb_retry_tasks WHERE status = 'PENDING']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_retry_tasks' doesn't exist
2025-07-12 10:44:39.460 ERROR c.y.campusos.service.waterctrl.FailureQueueService - 恢复未完成任务失败: Index: 0, Size: 0
2025-07-12 10:44:39.460 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 10:44:40.034 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 10:44:40.262 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 10:44:40.290 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 10:44:40.300 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 10:44:40.421 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 10:44:40.887 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 10:44:40.895 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 10:44:41.071 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$61bc62d6 - 计划任务最多执行：58个
2025-07-12 10:44:41.149 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 10:44:41.155 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 10:44:41.157 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 10:44:41.157 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 10:44:41.160 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 10:44:41.163 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 10:44:41.163 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[个人每日餐次消费分析]成功
2025-07-12 10:44:41.166 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 10:44:41.168 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 10:44:41.171 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 10:44:41.173 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 10:44:41.175 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 10:44:41.183 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.288 seconds (JVM running for 10.508)
2025-07-12 10:44:41.670 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 10:45:00.019 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 10:44:40,419 to 2025-07-12 10:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 10:45:14.697 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 10:45:14.697 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 10:45:14.699 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-12 10:45:18.440 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 10:45:39.506 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:45:39.506 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:45:39.506 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:45:41.294 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-07-12 10:45:41.294 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_equipment_reading_records wec left join tb_dev_areaframework da on da.code = wec.area_code left join tb_dev_accesscontroller tda on tda.devsn = wec.equipment_code  where 1 = 1 and reading_date between '2025-07-12' and '2025-07-12']; nested exception is java.sql.SQLSyntaxErrorException: Table 'ceshi.tb_equipment_reading_records' doesn't exist
2025-07-12 10:45:41.296 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-07-12 10:46:25.520 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 10:46:25.522 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 10:46:25.522 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 10:46:25.680 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 10:46:25.687 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 10:46:29.158 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 29448 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 10:46:29.156 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 10:46:29.159 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 10:46:30.720 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 10:46:30.722 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 10:46:30.879 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 144 ms. Found 0 Redis repository interfaces.
2025-07-12 10:46:31.305 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f405447a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:46:31.388 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$730b89f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 10:46:31.661 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 10:46:31.670 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 10:46:31.671 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 10:46:31.671 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 10:46:31.861 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 10:46:31.862 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2612 ms
2025-07-12 10:46:31.984 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 10:46:32.049 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 10:46:33.516 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 10:46:34.050 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 10:46:36.246 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 10:46:36.390 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 10:46:36.390 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 10:46:37.473 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 0 个待执行的重试任务
2025-07-12 10:46:37.681 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 0 个待执行的重试任务
2025-07-12 10:46:37.681 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 10:46:38.347 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 10:46:38.635 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 10:46:38.666 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 10:46:38.680 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 10:46:38.820 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 10:46:39.316 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 10:46:39.323 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 10:46:39.502 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$8d9f7b47 - 计划任务最多执行：66个
2025-07-12 10:46:39.570 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 10:46:39.576 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 10:46:39.576 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-12 10:46:39.579 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-12 10:46:39.581 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 10:46:39.581 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 10:46:39.585 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 10:46:39.589 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-12 10:46:39.592 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 10:46:39.594 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-12 10:46:39.597 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-12 10:46:39.599 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 10:46:39.601 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-12 10:46:39.604 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-12 10:46:39.606 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 10:46:39.609 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 10:46:39.611 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 10:46:39.612 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-12 10:46:39.614 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 10:46:39.639 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 10.924 seconds (JVM running for 12.056)
2025-07-12 10:46:40.078 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 10:46:50.288 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 10:46:50.288 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 10:46:50.289 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-12 10:47:12.641 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：null
2025-07-12 10:47:52.017 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[2]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 11:00:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 11:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 10:46:38,818 to 2025-07-12 11:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 11:00:00.025 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:00:00.040 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:00:00.050 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 11:00:00.050 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 11:00:00.115 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-12 11:00:00.129 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-12 11:00:00.140 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-12 11:00:00.140 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-12 11:15:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 11:00:00,010 to 2025-07-12 11:15:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 11:20:00.002 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 11:20:00.020 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:20:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:20:00.042 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 11:20:00.042 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 11:30:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 11:15:00,003 to 2025-07-12 11:30:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 11:40:00.006 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 11:40:00.021 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:40:00.032 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 11:40:00.043 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 11:40:00.043 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 11:43:37.604 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 11:43:37.607 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 11:43:37.607 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 11:43:37.790 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 11:43:37.797 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 11:45:02.537 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 38456 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 11:45:02.536 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 11:45:02.539 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 11:45:04.211 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 11:45:04.214 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 11:45:04.391 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 163 ms. Found 0 Redis repository interfaces.
2025-07-12 11:45:04.838 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$288dd69f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 11:45:04.925 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a7941c17] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 11:45:05.222 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 11:45:05.233 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 11:45:05.234 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 11:45:05.234 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 11:45:05.425 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 11:45:05.425 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2788 ms
2025-07-12 11:45:05.544 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 11:45:05.608 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 11:45:07.980 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 11:45:08.587 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 11:45:10.815 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 11:45:10.963 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 11:45:10.963 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 11:45:12.045 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 0 个待执行的重试任务
2025-07-12 11:45:12.287 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 0 个待执行的重试任务
2025-07-12 11:45:12.287 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 11:45:12.859 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 11:45:13.082 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 11:45:13.110 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 11:45:13.120 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 11:45:13.247 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 11:45:13.710 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 11:45:13.717 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 11:45:13.894 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$c2280d6c - 计划任务最多执行：66个
2025-07-12 11:45:13.957 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 11:45:13.962 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 11:45:13.962 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-12 11:45:13.964 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-12 11:45:13.966 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 11:45:13.967 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 11:45:13.971 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 11:45:13.973 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-12 11:45:13.975 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 11:45:13.977 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-12 11:45:13.979 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-12 11:45:13.982 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 11:45:13.984 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-12 11:45:13.987 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-12 11:45:13.989 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 11:45:13.991 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 11:45:13.994 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 11:45:13.994 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-12 11:45:13.996 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 11:45:14.017 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 12.059 seconds (JVM running for 13.263)
2025-07-12 11:45:14.477 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 11:45:17.671 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 11:45:17.671 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 11:45:17.674 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-12 11:45:22.155 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 11:47:33.823 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 开始执行设备费用计算定时任务...
2025-07-12 11:47:40.552 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 开始检查设备异常...
2025-07-12 11:47:40.558 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始异步同步所有设备数据...
2025-07-12 11:47:40.558 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步 203 台设备，使用并发处理
2025-07-12 11:47:40.559 INFO  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 开始自动检查设备异常...
2025-07-12 11:47:40.599 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 020220221001 异常更新: 设备离线 - 设备超过24小时无数据上报 (第4次)
2025-07-12 11:47:40.607 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 energy000003 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.608 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 energy000003 无匹配采集器，已记录异常
2025-07-12 11:47:40.620 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303002347 异常更新: 设备离线 - 设备超过24小时无数据上报 (第2次)
2025-07-12 11:47:40.633 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303000199 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.633 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202303000199 无匹配采集器，已记录异常
2025-07-12 11:47:40.647 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595000018 异常更新: 设备离线 - 设备超过24小时无数据上报 (第2次)
2025-07-12 11:47:40.658 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 电表虚拟钱包-1号楼201 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.658 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 电表虚拟钱包-1号楼201 无匹配采集器，已记录异常
2025-07-12 11:47:40.671 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595000044 异常更新: 设备离线 - 设备超过24小时无数据上报 (第2次)
2025-07-12 11:47:40.679 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304006686 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.679 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304006686 无匹配采集器，已记录异常
2025-07-12 11:47:40.692 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595200173 异常更新: 设备离线 - 设备超过24小时无数据上报 (第2次)
2025-07-12 11:47:40.700 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 ceshirqb2111 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.700 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 ceshirqb2111 无匹配采集器，已记录异常
2025-07-12 11:47:40.716 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595200175 异常更新: 设备离线 - 设备超过24小时无数据上报 (第2次)
2025-07-12 11:47:40.723 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 xu202203030523 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.723 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 xu202203030523 无匹配采集器，已记录异常
2025-07-12 11:47:40.742 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 cs0000000085 异常更新: 设备离线 - 设备超过24小时无数据上报 (第4次)
2025-07-12 11:47:40.749 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00221200002234 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.749 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00221200002234 无匹配采集器，已记录异常
2025-07-12 11:47:40.772 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 cs0000000101 异常更新: 设备离线 - 设备超过24小时无数据上报 (第4次)
2025-07-12 11:47:40.778 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 209901130001 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.778 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 209901130001 无匹配采集器，已记录异常
2025-07-12 11:47:40.784 INFO  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备异常检查完成，发现离线设备: 8个
2025-07-12 11:47:40.784 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 设备异常检查完成
2025-07-12 11:47:40.784 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 设备费用计算定时任务执行完成
2025-07-12 11:47:40.802 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202505130002 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:47:40.802 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202505130002 无匹配采集器，已记录异常
2025-07-12 11:47:40.823 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040204 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.823 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 992308040204 无匹配采集器，已记录异常
2025-07-12 11:47:40.845 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040102 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.845 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 992308040102 无匹配采集器，已记录异常
2025-07-12 11:47:40.865 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 852403159209 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.867 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 852403159209 无匹配采集器，已记录异常
2025-07-12 11:47:40.886 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 82522502270001 异常更新: 无数据返回 - 无匹配采集器 (第16次)
2025-07-12 11:47:40.886 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 82522502270001 无匹配采集器，已记录异常
2025-07-12 11:47:40.907 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076009 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:47:40.907 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 792308076009 无匹配采集器，已记录异常
2025-07-12 11:47:40.927 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792305105021 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.927 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 792305105021 无匹配采集器，已记录异常
2025-07-12 11:47:40.951 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140042 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:47:40.951 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140042 无匹配采集器，已记录异常
2025-07-12 11:47:40.970 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 5903010200107708 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.970 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 5903010200107708 无匹配采集器，已记录异常
2025-07-12 11:47:40.992 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 5903010200107706 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:40.992 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 5903010200107706 无匹配采集器，已记录异常
2025-07-12 11:47:41.013 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 300012121212 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.015 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 300012121212 无匹配采集器，已记录异常
2025-07-12 11:47:41.034 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 2522401120001 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.034 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 2522401120001 无匹配采集器，已记录异常
2025-07-12 11:47:41.055 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 2522310230008 异常更新: 无数据返回 - 无匹配采集器 (第16次)
2025-07-12 11:47:41.055 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 2522310230008 无匹配采集器，已记录异常
2025-07-12 11:47:41.075 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230329550888 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.076 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 230329550888 无匹配采集器，已记录异常
2025-07-12 11:47:41.095 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 223211202104187485 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.095 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 223211202104187485 无匹配采集器，已记录异常
2025-07-12 11:47:41.124 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 222911040989 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.124 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 222911040989 无匹配采集器，已记录异常
2025-07-12 11:47:41.145 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220000003314 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.145 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 220000003314 无匹配采集器，已记录异常
2025-07-12 11:47:41.169 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202505130001 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.169 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202505130001 无匹配采集器，已记录异常
2025-07-12 11:47:41.189 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308090001 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.190 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202308090001 无匹配采集器，已记录异常
2025-07-12 11:47:41.210 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308040001 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.210 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202308040001 无匹配采集器，已记录异常
2025-07-12 11:47:41.233 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001-2 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.233 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001-2 无匹配采集器，已记录异常
2025-07-12 11:47:41.254 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001-1 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.254 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001-1 无匹配采集器，已记录异常
2025-07-12 11:47:41.274 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.274 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001 无匹配采集器，已记录异常
2025-07-12 11:47:41.295 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209230002 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.295 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202209230002 无匹配采集器，已记录异常
2025-07-12 11:47:41.316 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202149 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.316 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202205202149 无匹配采集器，已记录异常
2025-07-12 11:47:41.336 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203034522 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.336 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202203034522 无匹配采集器，已记录异常
2025-07-12 11:47:41.357 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202201210001 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.357 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202201210001 无匹配采集器，已记录异常
2025-07-12 11:47:41.379 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202111290047 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.379 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202111290047 无匹配采集器，已记录异常
2025-07-12 11:47:41.403 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202105080077 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.403 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202105080077 无匹配采集器，已记录异常
2025-07-12 11:47:41.427 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187885 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.427 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187885 无匹配采集器，已记录异常
2025-07-12 11:47:41.450 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187883 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.450 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187883 无匹配采集器，已记录异常
2025-07-12 11:47:41.472 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187880 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.472 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187880 无匹配采集器，已记录异常
2025-07-12 11:47:41.492 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187484 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.493 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187484 无匹配采集器，已记录异常
2025-07-12 11:47:41.514 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 151307080273 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.514 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 151307080273 无匹配采集器，已记录异常
2025-07-12 11:47:41.534 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134043003860 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.534 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 134043003860 无匹配采集器，已记录异常
2025-07-12 11:47:41.556 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 126202104187485 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.556 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 126202104187485 无匹配采集器，已记录异常
2025-07-12 11:47:41.577 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 125202104187485 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.577 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 125202104187485 无匹配采集器，已记录异常
2025-07-12 11:47:41.602 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 124202303150080 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.602 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 124202303150080 无匹配采集器，已记录异常
2025-07-12 11:47:41.621 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 124202104187485 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.621 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 124202104187485 无匹配采集器，已记录异常
2025-07-12 11:47:41.642 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 12306126470003 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.642 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 12306126470003 无匹配采集器，已记录异常
2025-07-12 11:47:41.662 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 112244001089 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.662 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 112244001089 无匹配采集器，已记录异常
2025-07-12 11:47:41.690 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111800135297 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.690 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111800135297 无匹配采集器，已记录异常
2025-07-12 11:47:41.710 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111555447845 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.710 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111555447845 无匹配采集器，已记录异常
2025-07-12 11:47:41.729 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111222333444 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.729 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111222333444 无匹配采集器，已记录异常
2025-07-12 11:47:41.748 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 1111202104187485 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.750 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 1111202104187485 无匹配采集器，已记录异常
2025-07-12 11:47:41.769 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 057701600670 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.769 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 057701600670 无匹配采集器，已记录异常
2025-07-12 11:47:41.795 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 057701600542 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.795 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 057701600542 无匹配采集器，已记录异常
2025-07-12 11:47:41.815 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 0269210625867454 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.815 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 0269210625867454 无匹配采集器，已记录异常
2025-07-12 11:47:41.836 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 020230306006 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.836 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 020230306006 无匹配采集器，已记录异常
2025-07-12 11:47:41.856 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 020220221001 异常更新: 无数据返回 - 无匹配采集器 (第12次)
2025-07-12 11:47:41.857 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 020220221001 无匹配采集器，已记录异常
2025-07-12 11:47:41.876 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 010511000000000001 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.877 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 010511000000000001 无匹配采集器，已记录异常
2025-07-12 11:47:41.896 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 002024054390 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.896 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 002024054390 无匹配采集器，已记录异常
2025-07-12 11:47:41.920 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 002023030450 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.920 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 002023030450 无匹配采集器，已记录异常
2025-07-12 11:47:41.940 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00004300 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.940 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00004300 无匹配采集器，已记录异常
2025-07-12 11:47:41.960 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024060353 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.960 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024060353 无匹配采集器，已记录异常
2025-07-12 11:47:41.980 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024056590 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.980 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024056590 无匹配采集器，已记录异常
2025-07-12 11:47:41.999 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024054390 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:41.999 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024054390 无匹配采集器，已记录异常
2025-07-12 11:47:42.018 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000002090406 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.018 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000002090406 无匹配采集器，已记录异常
2025-07-12 11:47:42.038 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000001310a 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.039 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00000001310a 无匹配采集器，已记录异常
2025-07-12 11:47:42.063 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013109 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.063 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013109 无匹配采集器，已记录异常
2025-07-12 11:47:42.083 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013108 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.083 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013108 无匹配采集器，已记录异常
2025-07-12 11:47:42.102 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013107 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.102 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013107 无匹配采集器，已记录异常
2025-07-12 11:47:42.125 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013106 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.125 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013106 无匹配采集器，已记录异常
2025-07-12 11:47:42.146 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013105 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.146 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013105 无匹配采集器，已记录异常
2025-07-12 11:47:42.168 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013104 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.168 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013104 无匹配采集器，已记录异常
2025-07-12 11:47:42.189 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013103 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.189 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013103 无匹配采集器，已记录异常
2025-07-12 11:47:42.209 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013102 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.209 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013102 无匹配采集器，已记录异常
2025-07-12 11:47:42.230 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013101 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.230 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013101 无匹配采集器，已记录异常
2025-07-12 11:47:42.250 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013100 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.250 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013100 无匹配采集器，已记录异常
2025-07-12 11:47:42.272 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000007480 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.272 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000007480 无匹配采集器，已记录异常
2025-07-12 11:47:42.292 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000004276 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.292 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000004276 无匹配采集器，已记录异常
2025-07-12 11:47:42.311 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000000002185 异常更新: 无数据返回 - 无匹配采集器 (第14次)
2025-07-12 11:47:42.311 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00000000002185 无匹配采集器，已记录异常
2025-07-12 11:47:42.311 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始批量查询采集器状态，共 97 个采集器
2025-07-12 11:47:42.693 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 501107411 状态异常
2025-07-12 11:47:42.716 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202412130001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:42.968 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 938905851 状态异常
2025-07-12 11:47:42.989 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308026601 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:43.350 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 010042970 状态异常
2025-07-12 11:47:43.376 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220722010076 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:43.744 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030501348 状态异常
2025-07-12 11:47:43.769 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403050544 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:43.981 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058615597 查询失败或返回数据为空
2025-07-12 11:47:44.002 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792304250007 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:47:44.264 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202208040019 状态异常
2025-07-12 11:47:44.285 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202208040019 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:44.533 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 106210042 状态异常
2025-07-12 11:47:44.555 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202106210042 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:44.849 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058611356 状态异常
2025-07-12 11:47:44.873 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:45.165 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 896805845 状态异常
2025-07-12 11:47:45.205 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992311211111 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:45.498 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 303150145 状态异常
2025-07-12 11:47:45.536 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303150145 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:45.809 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 120710016 状态异常
2025-07-12 11:47:45.849 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203030518 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:46.140 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 528442169 状态异常
2025-07-12 11:47:46.179 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202305290001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:46.220 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 076052841209 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:46.934 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 837003149 状态异常
2025-07-12 11:47:46.970 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403130098 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:47.163 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140510110 查询失败或返回数据为空
2025-07-12 11:47:47.202 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111800179430 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:47:47.501 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000003 状态异常
2025-07-12 11:47:47.538 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300003 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:47.839 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000004 状态异常
2025-07-12 11:47:47.877 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300004 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:48.230 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141304067677 状态异常
2025-07-12 11:47:48.255 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141304067677 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:48.566 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000001 状态异常
2025-07-12 11:47:48.588 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:48.894 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100410996 状态异常
2025-07-12 11:47:48.927 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203033638 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:49.236 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141304067674 状态异常
2025-07-12 11:47:49.271 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141304067674 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:49.615 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058627469 状态异常
2025-07-12 11:47:49.654 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076002 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:49.977 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 435057026478 状态异常
2025-07-12 11:47:49.999 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202103020884 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:50.275 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307070001 状态异常
2025-07-12 11:47:50.301 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307070001 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:50.460 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 020000002BB5 查询失败或返回数据为空
2025-07-12 11:47:50.481 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187485 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:47:50.501 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 FFFFFFFFFFFF 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:47:50.537 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 905802002779 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:47:50.558 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 882308026005 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:47:50.722 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 533005777 查询失败或返回数据为空
2025-07-12 11:47:50.743 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 200135671002 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:47:51.052 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 821000002 状态异常
2025-07-12 11:47:51.072 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134082100002 异常更新: 无数据返回 - 采集器状态异常 (第9次)
2025-07-12 11:47:51.345 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204140050 状态异常
2025-07-12 11:47:51.367 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204140050 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:51.700 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141230401543 状态异常
2025-07-12 11:47:51.723 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141230401543 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:51.911 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 591501904 查询失败或返回数据为空
2025-07-12 11:47:51.932 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 201120200106 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:47:52.255 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 102004098 状态异常
2025-07-12 11:47:52.291 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202410201002 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:52.591 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140310116 状态异常
2025-07-12 11:47:52.612 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220800001348 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:52.902 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202150 状态异常
2025-07-12 11:47:52.926 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202150 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:53.242 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289C6E307081 状态异常
2025-07-12 11:47:53.263 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 YG000000010765 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:53.570 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 102004097 状态异常
2025-07-12 11:47:53.598 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202410201001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:53.880 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693052804874 状态异常
2025-07-12 11:47:53.905 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230300023684 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:54.299 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693050293377 状态异常
2025-07-12 11:47:54.328 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202141 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:54.643 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 860610052773970 状态异常
2025-07-12 11:47:54.665 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 10304104045111 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:54.938 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100009001 状态异常
2025-07-12 11:47:54.961 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040205 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:54.983 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040202 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:55.004 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308020102 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:55.259 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 020000002BCC 状态异常
2025-07-12 11:47:55.287 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 190331527749 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:55.605 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 047407412 状态异常
2025-07-12 11:47:55.628 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202412130002 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:55.897 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058611935 状态异常
2025-07-12 11:47:55.924 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792305105020 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:56.093 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 834305590 查询失败或返回数据为空
2025-07-12 11:47:56.116 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000018365 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:47:56.394 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204140044 状态异常
2025-07-12 11:47:56.417 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204140044 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:56.696 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 328063829575 状态异常
2025-07-12 11:47:56.721 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:57.031 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 757105587 状态异常
2025-07-12 11:47:57.055 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202312116666 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:57.389 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307316001 状态异常
2025-07-12 11:47:57.451 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307316001 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:57.767 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 378107760 状态异常
2025-07-12 11:47:57.790 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202409290003 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:58.270 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307316002 状态异常
2025-07-12 11:47:58.294 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307316002 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:58.587 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202303170000 状态异常
2025-07-12 11:47:58.617 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303170000 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:58.929 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 627000001 状态异常
2025-07-12 11:47:58.951 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134062700001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:47:59.222 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202146 状态异常
2025-07-12 11:47:59.245 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202146 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:59.513 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202147 状态异常
2025-07-12 11:47:59.537 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202147 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:47:59.710 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 02010000482F 查询失败或返回数据为空
2025-07-12 11:47:59.736 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 011918223316201947 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:48:00.032 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 091641060 状态异常
2025-07-12 11:48:00.054 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202309160100 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:00.328 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 010040200890 状态异常
2025-07-12 11:48:00.359 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000001216 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:00.687 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 328067910801 状态异常
2025-07-12 11:48:00.710 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 121520200076 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:00.987 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202308180001 状态异常
2025-07-12 11:48:01.008 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308180001 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:48:01.365 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 649905588 状态异常
2025-07-12 11:48:01.408 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202312096666 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:01.716 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053294658 状态异常
2025-07-12 11:48:01.751 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220800001308 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:01.784 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203033849 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:01.810 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 20212205005384 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:02.192 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289054758509 状态异常
2025-07-12 11:48:02.236 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202211010771 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:02.283 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130030 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:02.322 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130029 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:02.361 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130026 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:02.728 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 896705587 状态异常
2025-07-12 11:48:02.763 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000020108 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:03.130 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204120218 状态异常
2025-07-12 11:48:03.172 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204120218 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:03.370 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 048071983819 查询失败或返回数据为空
2025-07-12 11:48:03.404 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202502170001 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:48:03.439 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202502240001 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:48:03.805 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 071100612 状态异常
2025-07-12 11:48:03.849 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202307110264 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:04.181 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030506179 状态异常
2025-07-12 11:48:04.221 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403051823 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:04.584 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 240100001 状态异常
2025-07-12 11:48:04.622 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000024010001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:05.057 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058607636 状态异常
2025-07-12 11:48:05.099 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170002 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:05.285 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053330486 查询失败或返回数据为空
2025-07-12 11:48:05.321 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 240010003355 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:48:05.353 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203032689 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:48:05.720 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 24060702830001 状态异常
2025-07-12 11:48:05.744 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 24060702830001 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:48:06.107 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141240410174 状态异常
2025-07-12 11:48:06.131 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141240410174 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:48:06.447 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289054796392 状态异常
2025-07-12 11:48:06.469 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308116666 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:06.492 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308099999 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:06.515 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782306220001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:06.537 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 011509134421 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:06.833 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 071404752 状态异常
2025-07-12 11:48:06.853 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 148307141290 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:07.190 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058606828 状态异常
2025-07-12 11:48:07.217 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076018 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:07.560 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140310121 状态异常
2025-07-12 11:48:07.589 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187886 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:07.609 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202008300467 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:07.887 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 150921012 状态异常
2025-07-12 11:48:07.909 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 energy000001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:08.229 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053321832 状态异常
2025-07-12 11:48:08.252 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000010141802 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:08.271 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000010141801 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:08.614 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 435057022691 状态异常
2025-07-12 11:48:08.655 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000941155 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:09.032 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 966075975598 状态异常
2025-07-12 11:48:09.056 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 244717020004 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:09.365 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058621843 状态异常
2025-07-12 11:48:09.385 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792304250024 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:09.682 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 230732020001 状态异常
2025-07-12 11:48:09.704 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230732020001 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:48:10.070 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 180000336 状态异常
2025-07-12 11:48:10.096 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 250118000150 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:10.122 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000018000150 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:10.464 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058610994 状态异常
2025-07-12 11:48:10.488 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140003 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:10.834 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 865068813782 状态异常
2025-07-12 11:48:10.857 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 123455555555 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:11.152 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058622692 状态异常
2025-07-12 11:48:11.175 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170003 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:11.869 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 210600057 状态异常
2025-07-12 11:48:11.895 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 152000048836 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:12.281 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058615803 状态异常
2025-07-12 11:48:12.306 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792408100001 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:12.636 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 712000002 状态异常
2025-07-12 11:48:12.661 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071200002 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:12.838 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100410103 查询失败或返回数据为空
2025-07-12 11:48:12.860 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000019041782 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:48:12.886 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000010900020 异常更新: 无数据返回 - 采集器不存在或未匹配 (第11次)
2025-07-12 11:48:13.191 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058634622 状态异常
2025-07-12 11:48:13.221 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308192129 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:13.547 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 112060062 状态异常
2025-07-12 11:48:13.584 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202112060062 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:13.879 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 88888888888888 状态异常
2025-07-12 11:48:13.900 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 88888888888888 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:48:14.194 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693052805228 状态异常
2025-07-12 11:48:14.217 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202109171124 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:14.551 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 180000337 状态异常
2025-07-12 11:48:14.576 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000018000151 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:14.889 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 112060025 状态异常
2025-07-12 11:48:14.912 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202112060025 异常更新: 无数据返回 - 采集器状态异常 (第11次)
2025-07-12 11:48:15.195 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 222811190004 状态异常
2025-07-12 11:48:15.225 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 222811190004 异常更新: 无数据返回 - 采集器状态异常 (第14次)
2025-07-12 11:48:15.227 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器状态查询完成 - 总采集器: 97, 有效采集器: 2, 参与同步设备: 8
2025-07-12 11:48:15.227 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组 701073943386: 6 台设备
2025-07-12 11:48:15.227 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组 482067317475: 2 台设备
2025-07-12 11:48:15.227 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 按采集器分组完成，共 2 个采集器组，参与同步设备: 8台，无采集器设备: 195台
2025-07-12 11:48:15.231 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始串行处理采集器组，设备数量: 6
2025-07-12 11:48:15.231 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 697595000018
2025-07-12 11:48:15.231 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始串行处理采集器组，设备数量: 2
2025-07-12 11:48:15.231 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 cs0000000085
2025-07-12 11:48:15.516 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:15.516 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 181:插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:15.519 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 同步失败: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:15.520 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 将在 60 秒后重试，原因: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:15.520 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 同步失败，添加到持久化重试队列，延迟 60 秒后重试
2025-07-12 11:48:15.540 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 立即重试任务已添加 - 设备: cs0000000085, 执行时间: Sat Jul 12 11:48:45 CST 2025
2025-07-12 11:48:15.540 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 已添加到持久化重试队列，重试类型: 立即重试
2025-07-12 11:48:15.754 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 cs0000000101
2025-07-12 11:48:15.971 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:15.971 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 181:插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:15.971 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000101 同步失败: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:15.971 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000101 将在 60 秒后重试，原因: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:15.972 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000101 同步失败，添加到持久化重试队列，延迟 60 秒后重试
2025-07-12 11:48:15.985 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 立即重试任务已添加 - 设备: cs0000000101, 执行时间: Sat Jul 12 11:48:45 CST 2025
2025-07-12 11:48:15.985 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000101 已添加到持久化重试队列，重试类型: 立即重试
2025-07-12 11:48:15.996 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组处理完成，设备数量: 2
2025-07-12 11:48:17.048 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:17.049 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 181:插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:17.049 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595000018 同步失败: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:17.049 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595000018 将在 60 秒后重试，原因: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:17.050 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595000018 同步失败，添加到持久化重试队列，延迟 60 秒后重试
2025-07-12 11:48:17.061 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 立即重试任务已添加 - 设备: 697595000018, 执行时间: Sat Jul 12 11:48:47 CST 2025
2025-07-12 11:48:17.061 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595000018 已添加到持久化重试队列，重试类型: 立即重试
2025-07-12 11:48:17.287 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 697595000044
2025-07-12 11:48:20.546 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:20.547 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 181:插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:20.548 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595000044 同步失败: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:20.548 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595000044 将在 60 秒后重试，原因: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:20.549 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595000044 同步失败，添加到持久化重试队列，延迟 60 秒后重试
2025-07-12 11:48:20.567 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 立即重试任务已添加 - 设备: 697595000044, 执行时间: Sat Jul 12 11:48:50 CST 2025
2025-07-12 11:48:20.567 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595000044 已添加到持久化重试队列，重试类型: 立即重试
2025-07-12 11:48:20.796 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 697595200175
2025-07-12 11:48:23.027 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:23.027 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 181:插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:23.027 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595200175 同步失败: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:23.027 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595200175 将在 60 秒后重试，原因: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:23.027 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595200175 同步失败，添加到持久化重试队列，延迟 60 秒后重试
2025-07-12 11:48:23.036 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 立即重试任务已添加 - 设备: 697595200175, 执行时间: Sat Jul 12 11:48:53 CST 2025
2025-07-12 11:48:23.036 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595200175 已添加到持久化重试队列，重试类型: 立即重试
2025-07-12 11:48:23.259 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 697595200173
2025-07-12 11:48:25.489 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:25.489 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 181:插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:25.489 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595200173 同步失败: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:25.489 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595200173 将在 60 秒后重试，原因: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:25.489 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595200173 同步失败，添加到持久化重试队列，延迟 60 秒后重试
2025-07-12 11:48:25.500 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 立即重试任务已添加 - 设备: 697595200173, 执行时间: Sat Jul 12 11:48:55 CST 2025
2025-07-12 11:48:25.500 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 697595200173 已添加到持久化重试队列，重试类型: 立即重试
2025-07-12 11:48:25.715 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 616408140029
2025-07-12 11:48:55.953 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140029 异常更新: API调用错误 - 同步设备读数失败, 请过几分钟再试 (第87次)
2025-07-12 11:48:55.953 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140029 同步失败: 同步设备读数失败, 请过几分钟再试
2025-07-12 11:48:55.953 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140029 将在 180 秒后重试，原因: 同步设备读数失败, 请过几分钟再试
2025-07-12 11:48:55.953 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140029 同步失败，添加到持久化重试队列，延迟 180 秒后重试
2025-07-12 11:48:55.963 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 延迟重试任务已添加 - 设备: 616408140029, 延迟: 3分钟, 执行时间: Sat Jul 12 11:51:55 CST 2025
2025-07-12 11:48:55.964 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140029 已添加到持久化重试队列，重试类型: 延迟重试
2025-07-12 11:48:56.182 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 202303002347
2025-07-12 11:48:58.918 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:58.919 ERROR c.y.c.service.waterctrl.EquipmentReadingService - 181:插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:58.919 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202303002347 同步失败: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:58.919 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202303002347 将在 60 秒后重试，原因: 插入设备读数记录失败: java.time.LocalDateTime cannot be cast to java.util.Date
2025-07-12 11:48:58.919 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202303002347 同步失败，添加到持久化重试队列，延迟 60 秒后重试
2025-07-12 11:48:58.933 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 立即重试任务已添加 - 设备: 202303002347, 执行时间: Sat Jul 12 11:49:28 CST 2025
2025-07-12 11:48:58.933 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202303002347 已添加到持久化重试队列，重试类型: 立即重试
2025-07-12 11:48:58.948 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组处理完成，设备数量: 6
2025-07-12 11:48:58.965 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备数据异步同步完成 - 总计:203, 成功:0, 失败:203(含无采集器:195), 重试:0
2025-07-12 11:51:56.743 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 11:51:56.745 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 11:51:56.745 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 11:51:56.893 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 11:51:56.899 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 11:52:02.943 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 11:52:02.943 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 26324 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 11:52:02.945 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 11:52:04.404 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 11:52:04.406 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 11:52:04.553 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 135 ms. Found 0 Redis repository interfaces.
2025-07-12 11:52:04.958 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$d3c242e4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 11:52:05.040 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$52c8885c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 11:52:05.307 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 11:52:05.317 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 11:52:05.318 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 11:52:05.318 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 11:52:05.505 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 11:52:05.505 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2487 ms
2025-07-12 11:52:05.612 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 11:52:05.681 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 11:52:07.016 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 11:52:07.533 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 11:52:09.694 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 11:52:09.846 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 11:52:09.846 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 11:52:10.867 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 8 个待执行的重试任务
2025-07-12 11:52:11.070 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 8 个待执行的重试任务
2025-07-12 11:52:11.070 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 11:52:11.672 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 11:52:11.900 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 11:52:11.928 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 11:52:11.939 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 11:52:12.086 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 11:52:12.561 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 11:52:12.569 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 11:52:12.772 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$6d5c79b1 - 计划任务最多执行：66个
2025-07-12 11:52:12.835 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 11:52:12.840 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 11:52:12.840 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-12 11:52:12.842 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-12 11:52:12.845 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 11:52:12.845 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 11:52:12.848 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 11:52:12.851 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-12 11:52:12.853 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 11:52:12.855 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-12 11:52:12.857 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-12 11:52:12.860 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 11:52:12.862 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-12 11:52:12.865 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-12 11:52:12.867 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 11:52:12.869 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 11:52:12.871 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 11:52:12.872 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-12 11:52:12.874 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 11:52:12.902 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 10.369 seconds (JVM running for 11.44)
2025-07-12 11:52:13.333 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 11:52:26.421 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-12 11:52:26.422 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-12 11:52:26.423 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-12 11:52:26.589 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-12 11:52:26.599 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-12 11:52:29.929 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 28644 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-12 11:52:29.929 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-12 11:52:29.931 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-12 11:52:31.401 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-12 11:52:31.404 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-12 11:52:31.557 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 141 ms. Found 0 Redis repository interfaces.
2025-07-12 11:52:31.968 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$5fcdd889] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 11:52:32.048 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$ded41e01] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-12 11:52:32.314 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-12 11:52:32.325 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-12 11:52:32.325 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 11:52:32.325 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-12 11:52:32.510 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 11:52:32.510 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2505 ms
2025-07-12 11:52:32.620 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-12 11:52:32.691 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-12 11:52:33.956 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-12 11:52:34.476 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-12 11:52:36.662 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-12 11:52:36.812 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-12 11:52:36.812 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-12 11:52:37.824 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 8 个待执行的重试任务
2025-07-12 11:52:38.030 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 8 个待执行的重试任务
2025-07-12 11:52:38.030 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-12 11:52:38.596 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 11:52:38.818 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-12 11:52:38.845 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-12 11:52:38.855 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-12 11:52:38.975 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-12 11:52:39.427 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-12 11:52:39.435 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-12 11:52:39.606 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$f9680f56 - 计划任务最多执行：66个
2025-07-12 11:52:39.661 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-12 11:52:39.666 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-12 11:52:39.666 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-12 11:52:39.669 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-12 11:52:39.671 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-12 11:52:39.671 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-12 11:52:39.673 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-12 11:52:39.676 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-12 11:52:39.678 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-12 11:52:39.679 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-12 11:52:39.681 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-12 11:52:39.685 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-12 11:52:39.687 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-12 11:52:39.689 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-12 11:52:39.692 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-12 11:52:39.694 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-12 11:52:39.696 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-12 11:52:39.696 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-12 11:52:39.698 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-12 11:52:39.717 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 10.205 seconds (JVM running for 11.33)
2025-07-12 11:52:40.141 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-12 11:52:59.451 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-12 11:52:59.451 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-12 11:52:59.452 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-12 11:53:01.890 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-12 11:54:04.600 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 开始执行设备费用计算定时任务...
2025-07-12 11:54:12.189 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 开始检查设备异常...
2025-07-12 11:54:12.194 INFO  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 开始自动检查设备异常...
2025-07-12 11:54:12.197 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始异步同步所有设备数据...
2025-07-12 11:54:12.197 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步 203 台设备，使用并发处理
2025-07-12 11:54:12.266 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 020220221001 异常更新: 设备离线 - 设备超过24小时无数据上报 (第5次)
2025-07-12 11:54:12.266 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 energy000003 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.267 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 energy000003 无匹配采集器，已记录异常
2025-07-12 11:54:12.296 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303000199 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.296 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303002347 异常更新: 设备离线 - 设备超过24小时无数据上报 (第3次)
2025-07-12 11:54:12.296 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202303000199 无匹配采集器，已记录异常
2025-07-12 11:54:12.326 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 电表虚拟钱包-1号楼201 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.326 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 电表虚拟钱包-1号楼201 无匹配采集器，已记录异常
2025-07-12 11:54:12.326 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595000018 异常更新: 设备离线 - 设备超过24小时无数据上报 (第3次)
2025-07-12 11:54:12.353 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595000044 异常更新: 设备离线 - 设备超过24小时无数据上报 (第3次)
2025-07-12 11:54:12.354 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304006686 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.354 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304006686 无匹配采集器，已记录异常
2025-07-12 11:54:12.381 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 ceshirqb2111 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.381 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595200173 异常更新: 设备离线 - 设备超过24小时无数据上报 (第3次)
2025-07-12 11:54:12.381 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 ceshirqb2111 无匹配采集器，已记录异常
2025-07-12 11:54:12.404 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 xu202203030523 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.404 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595200175 异常更新: 设备离线 - 设备超过24小时无数据上报 (第3次)
2025-07-12 11:54:12.404 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 xu202203030523 无匹配采集器，已记录异常
2025-07-12 11:54:12.433 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00221200002234 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.433 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 cs0000000085 异常更新: 设备离线 - 设备超过24小时无数据上报 (第5次)
2025-07-12 11:54:12.433 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00221200002234 无匹配采集器，已记录异常
2025-07-12 11:54:12.457 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 209901130001 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.457 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 209901130001 无匹配采集器，已记录异常
2025-07-12 11:54:12.483 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 cs0000000101 异常更新: 设备离线 - 设备超过24小时无数据上报 (第5次)
2025-07-12 11:54:12.483 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202505130002 异常更新: 无数据返回 - 无匹配采集器 (第16次)
2025-07-12 11:54:12.483 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202505130002 无匹配采集器，已记录异常
2025-07-12 11:54:12.498 INFO  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备异常检查完成，发现离线设备: 8个
2025-07-12 11:54:12.498 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 设备异常检查完成
2025-07-12 11:54:12.498 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 设备费用计算定时任务执行完成
2025-07-12 11:54:12.510 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040204 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.510 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 992308040204 无匹配采集器，已记录异常
2025-07-12 11:54:12.533 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040102 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.533 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 992308040102 无匹配采集器，已记录异常
2025-07-12 11:54:12.558 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 852403159209 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.558 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 852403159209 无匹配采集器，已记录异常
2025-07-12 11:54:12.581 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 82522502270001 异常更新: 无数据返回 - 无匹配采集器 (第17次)
2025-07-12 11:54:12.581 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 82522502270001 无匹配采集器，已记录异常
2025-07-12 11:54:12.609 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076009 异常更新: 无数据返回 - 无匹配采集器 (第16次)
2025-07-12 11:54:12.609 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 792308076009 无匹配采集器，已记录异常
2025-07-12 11:54:12.633 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792305105021 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.633 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 792305105021 无匹配采集器，已记录异常
2025-07-12 11:54:12.656 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140042 异常更新: 无数据返回 - 无匹配采集器 (第16次)
2025-07-12 11:54:12.656 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140042 无匹配采集器，已记录异常
2025-07-12 11:54:12.679 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 5903010200107708 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.680 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 5903010200107708 无匹配采集器，已记录异常
2025-07-12 11:54:12.704 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 5903010200107706 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.704 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 5903010200107706 无匹配采集器，已记录异常
2025-07-12 11:54:12.724 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 300012121212 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.724 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 300012121212 无匹配采集器，已记录异常
2025-07-12 11:54:12.748 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 2522401120001 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.748 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 2522401120001 无匹配采集器，已记录异常
2025-07-12 11:54:12.770 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 2522310230008 异常更新: 无数据返回 - 无匹配采集器 (第17次)
2025-07-12 11:54:12.771 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 2522310230008 无匹配采集器，已记录异常
2025-07-12 11:54:12.794 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230329550888 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.794 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 230329550888 无匹配采集器，已记录异常
2025-07-12 11:54:12.814 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 223211202104187485 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.814 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 223211202104187485 无匹配采集器，已记录异常
2025-07-12 11:54:12.838 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 222911040989 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.838 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 222911040989 无匹配采集器，已记录异常
2025-07-12 11:54:12.862 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220000003314 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.862 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 220000003314 无匹配采集器，已记录异常
2025-07-12 11:54:12.888 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202505130001 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.888 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202505130001 无匹配采集器，已记录异常
2025-07-12 11:54:12.908 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308090001 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.908 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202308090001 无匹配采集器，已记录异常
2025-07-12 11:54:12.928 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308040001 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.929 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202308040001 无匹配采集器，已记录异常
2025-07-12 11:54:12.950 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001-2 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.950 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001-2 无匹配采集器，已记录异常
2025-07-12 11:54:12.972 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001-1 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.972 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001-1 无匹配采集器，已记录异常
2025-07-12 11:54:12.998 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:12.998 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001 无匹配采集器，已记录异常
2025-07-12 11:54:13.019 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209230002 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.019 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202209230002 无匹配采集器，已记录异常
2025-07-12 11:54:13.041 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202149 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.041 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202205202149 无匹配采集器，已记录异常
2025-07-12 11:54:13.062 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203034522 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.062 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202203034522 无匹配采集器，已记录异常
2025-07-12 11:54:13.082 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202201210001 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.083 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202201210001 无匹配采集器，已记录异常
2025-07-12 11:54:13.105 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202111290047 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.105 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202111290047 无匹配采集器，已记录异常
2025-07-12 11:54:13.125 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202105080077 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.125 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202105080077 无匹配采集器，已记录异常
2025-07-12 11:54:13.144 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187885 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.144 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187885 无匹配采集器，已记录异常
2025-07-12 11:54:13.165 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187883 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.165 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187883 无匹配采集器，已记录异常
2025-07-12 11:54:13.186 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187880 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.186 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187880 无匹配采集器，已记录异常
2025-07-12 11:54:13.209 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187484 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.209 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187484 无匹配采集器，已记录异常
2025-07-12 11:54:13.230 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 151307080273 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.230 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 151307080273 无匹配采集器，已记录异常
2025-07-12 11:54:13.252 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134043003860 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.252 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 134043003860 无匹配采集器，已记录异常
2025-07-12 11:54:13.275 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 126202104187485 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.275 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 126202104187485 无匹配采集器，已记录异常
2025-07-12 11:54:13.297 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 125202104187485 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.298 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 125202104187485 无匹配采集器，已记录异常
2025-07-12 11:54:13.317 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 124202303150080 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.317 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 124202303150080 无匹配采集器，已记录异常
2025-07-12 11:54:13.339 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 124202104187485 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.339 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 124202104187485 无匹配采集器，已记录异常
2025-07-12 11:54:13.360 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 12306126470003 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.360 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 12306126470003 无匹配采集器，已记录异常
2025-07-12 11:54:13.382 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 112244001089 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.382 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 112244001089 无匹配采集器，已记录异常
2025-07-12 11:54:13.402 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111800135297 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.402 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111800135297 无匹配采集器，已记录异常
2025-07-12 11:54:13.424 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111555447845 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.424 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111555447845 无匹配采集器，已记录异常
2025-07-12 11:54:13.444 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111222333444 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.444 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111222333444 无匹配采集器，已记录异常
2025-07-12 11:54:13.470 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 1111202104187485 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.470 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 1111202104187485 无匹配采集器，已记录异常
2025-07-12 11:54:13.505 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 057701600670 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.505 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 057701600670 无匹配采集器，已记录异常
2025-07-12 11:54:13.526 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 057701600542 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.526 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 057701600542 无匹配采集器，已记录异常
2025-07-12 11:54:13.550 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 0269210625867454 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.550 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 0269210625867454 无匹配采集器，已记录异常
2025-07-12 11:54:13.572 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 020230306006 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.572 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 020230306006 无匹配采集器，已记录异常
2025-07-12 11:54:13.596 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 020220221001 异常更新: 无数据返回 - 无匹配采集器 (第13次)
2025-07-12 11:54:13.596 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 020220221001 无匹配采集器，已记录异常
2025-07-12 11:54:13.623 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 010511000000000001 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.623 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 010511000000000001 无匹配采集器，已记录异常
2025-07-12 11:54:13.644 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 002024054390 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.644 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 002024054390 无匹配采集器，已记录异常
2025-07-12 11:54:13.667 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 002023030450 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.667 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 002023030450 无匹配采集器，已记录异常
2025-07-12 11:54:13.688 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00004300 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.688 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00004300 无匹配采集器，已记录异常
2025-07-12 11:54:13.709 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024060353 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.709 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024060353 无匹配采集器，已记录异常
2025-07-12 11:54:13.732 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024056590 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.733 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024056590 无匹配采集器，已记录异常
2025-07-12 11:54:13.753 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024054390 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.753 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024054390 无匹配采集器，已记录异常
2025-07-12 11:54:13.775 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000002090406 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.775 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000002090406 无匹配采集器，已记录异常
2025-07-12 11:54:13.796 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000001310a 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.796 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00000001310a 无匹配采集器，已记录异常
2025-07-12 11:54:13.822 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013109 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.824 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013109 无匹配采集器，已记录异常
2025-07-12 11:54:13.845 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013108 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.845 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013108 无匹配采集器，已记录异常
2025-07-12 11:54:13.866 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013107 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.866 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013107 无匹配采集器，已记录异常
2025-07-12 11:54:13.889 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013106 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.889 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013106 无匹配采集器，已记录异常
2025-07-12 11:54:13.911 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013105 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.912 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013105 无匹配采集器，已记录异常
2025-07-12 11:54:13.935 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013104 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.935 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013104 无匹配采集器，已记录异常
2025-07-12 11:54:13.958 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013103 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.958 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013103 无匹配采集器，已记录异常
2025-07-12 11:54:13.980 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013102 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:13.980 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013102 无匹配采集器，已记录异常
2025-07-12 11:54:14.008 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013101 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:14.008 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013101 无匹配采集器，已记录异常
2025-07-12 11:54:14.031 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013100 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:14.031 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013100 无匹配采集器，已记录异常
2025-07-12 11:54:14.054 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000007480 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:14.054 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000007480 无匹配采集器，已记录异常
2025-07-12 11:54:14.074 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000004276 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:14.074 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000004276 无匹配采集器，已记录异常
2025-07-12 11:54:14.095 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000000002185 异常更新: 无数据返回 - 无匹配采集器 (第15次)
2025-07-12 11:54:14.096 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00000000002185 无匹配采集器，已记录异常
2025-07-12 11:54:14.096 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始批量查询采集器状态，共 97 个采集器
2025-07-12 11:54:14.466 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 501107411 状态异常
2025-07-12 11:54:14.487 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202412130001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:14.958 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 938905851 状态异常
2025-07-12 11:54:14.981 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308026601 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:15.296 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 010042970 状态异常
2025-07-12 11:54:15.318 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220722010076 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:15.616 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030501348 状态异常
2025-07-12 11:54:15.638 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403050544 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:15.801 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058615597 查询失败或返回数据为空
2025-07-12 11:54:15.825 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792304250007 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:16.069 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202208040019 状态异常
2025-07-12 11:54:16.092 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202208040019 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:16.396 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 106210042 状态异常
2025-07-12 11:54:16.421 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202106210042 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:16.702 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058611356 状态异常
2025-07-12 11:54:16.724 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:16.998 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 896805845 状态异常
2025-07-12 11:54:17.022 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992311211111 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:17.327 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 303150145 状态异常
2025-07-12 11:54:17.349 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303150145 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:17.607 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 120710016 状态异常
2025-07-12 11:54:17.631 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203030518 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:18.003 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 528442169 状态异常
2025-07-12 11:54:18.027 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202305290001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:18.048 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 076052841209 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:18.795 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 837003149 状态异常
2025-07-12 11:54:18.823 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403130098 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:18.997 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140510110 查询失败或返回数据为空
2025-07-12 11:54:19.020 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111800179430 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:19.305 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000003 状态异常
2025-07-12 11:54:19.330 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300003 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:19.830 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000004 状态异常
2025-07-12 11:54:19.940 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300004 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:20.243 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141304067677 状态异常
2025-07-12 11:54:20.280 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141304067677 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:20.584 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000001 状态异常
2025-07-12 11:54:20.620 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:20.878 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100410996 状态异常
2025-07-12 11:54:20.914 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203033638 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:21.206 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141304067674 状态异常
2025-07-12 11:54:21.229 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141304067674 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:21.518 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058627469 状态异常
2025-07-12 11:54:21.547 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076002 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:21.806 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 435057026478 状态异常
2025-07-12 11:54:21.830 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202103020884 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:22.088 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307070001 状态异常
2025-07-12 11:54:22.122 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307070001 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:22.301 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 020000002BB5 查询失败或返回数据为空
2025-07-12 11:54:22.336 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187485 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:22.377 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 FFFFFFFFFFFF 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:22.414 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 905802002779 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:22.443 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 882308026005 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:22.619 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 533005777 查询失败或返回数据为空
2025-07-12 11:54:22.655 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 200135671002 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:22.996 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 821000002 状态异常
2025-07-12 11:54:23.033 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134082100002 异常更新: 无数据返回 - 采集器状态异常 (第10次)
2025-07-12 11:54:23.320 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204140050 状态异常
2025-07-12 11:54:23.357 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204140050 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:23.668 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141230401543 状态异常
2025-07-12 11:54:23.706 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141230401543 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:23.894 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 591501904 查询失败或返回数据为空
2025-07-12 11:54:23.930 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 201120200106 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:24.276 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 102004098 状态异常
2025-07-12 11:54:24.312 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202410201002 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:24.596 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140310116 状态异常
2025-07-12 11:54:24.631 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220800001348 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:24.904 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202150 状态异常
2025-07-12 11:54:24.943 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202150 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:25.267 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289C6E307081 状态异常
2025-07-12 11:54:25.306 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 YG000000010765 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:25.610 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 102004097 状态异常
2025-07-12 11:54:25.644 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202410201001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:25.931 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693052804874 状态异常
2025-07-12 11:54:25.968 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230300023684 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:26.256 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693050293377 状态异常
2025-07-12 11:54:26.293 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202141 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:26.585 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 860610052773970 状态异常
2025-07-12 11:54:26.621 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 10304104045111 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:26.898 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100009001 状态异常
2025-07-12 11:54:26.934 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040205 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:26.970 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040202 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:27.006 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308020102 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:27.283 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 020000002BCC 状态异常
2025-07-12 11:54:27.320 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 190331527749 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:27.635 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 047407412 状态异常
2025-07-12 11:54:27.670 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202412130002 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:27.943 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058611935 状态异常
2025-07-12 11:54:27.982 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792305105020 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:28.168 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 834305590 查询失败或返回数据为空
2025-07-12 11:54:28.205 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000018365 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:28.517 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204140044 状态异常
2025-07-12 11:54:28.553 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204140044 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:28.982 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 328063829575 状态异常
2025-07-12 11:54:29.020 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:29.330 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 757105587 状态异常
2025-07-12 11:54:29.366 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202312116666 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:29.681 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307316001 状态异常
2025-07-12 11:54:29.707 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307316001 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:30.004 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 378107760 状态异常
2025-07-12 11:54:30.030 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202409290003 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:30.305 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307316002 状态异常
2025-07-12 11:54:30.333 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307316002 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:30.616 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202303170000 状态异常
2025-07-12 11:54:30.644 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303170000 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:30.925 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 627000001 状态异常
2025-07-12 11:54:30.948 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134062700001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:31.210 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202146 状态异常
2025-07-12 11:54:31.236 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202146 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:31.517 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202147 状态异常
2025-07-12 11:54:31.546 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202147 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:31.730 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 02010000482F 查询失败或返回数据为空
2025-07-12 11:54:31.752 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 011918223316201947 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:32.050 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 091641060 状态异常
2025-07-12 11:54:32.076 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202309160100 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:32.356 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 010040200890 状态异常
2025-07-12 11:54:32.388 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000001216 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:32.703 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 328067910801 状态异常
2025-07-12 11:54:32.728 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 121520200076 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:32.986 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202308180001 状态异常
2025-07-12 11:54:33.017 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308180001 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:33.315 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 649905588 状态异常
2025-07-12 11:54:33.340 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202312096666 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:33.683 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053294658 状态异常
2025-07-12 11:54:33.706 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220800001308 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:33.731 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203033849 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:33.761 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 20212205005384 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:34.112 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289054758509 状态异常
2025-07-12 11:54:34.136 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202211010771 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:34.158 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130030 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:34.184 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130029 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:34.206 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130026 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:34.565 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 896705587 状态异常
2025-07-12 11:54:34.590 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000020108 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:34.917 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204120218 状态异常
2025-07-12 11:54:34.941 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204120218 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:35.130 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 048071983819 查询失败或返回数据为空
2025-07-12 11:54:35.152 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202502170001 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:35.173 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202502240001 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:35.481 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 071100612 状态异常
2025-07-12 11:54:35.503 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202307110264 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:35.852 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030506179 状态异常
2025-07-12 11:54:35.873 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403051823 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:36.170 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 240100001 状态异常
2025-07-12 11:54:36.191 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000024010001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:36.511 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058607636 状态异常
2025-07-12 11:54:36.533 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170002 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:36.715 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053330486 查询失败或返回数据为空
2025-07-12 11:54:36.748 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 240010003355 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:36.776 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203032689 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:37.081 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 24060702830001 状态异常
2025-07-12 11:54:37.117 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 24060702830001 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:37.429 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141240410174 状态异常
2025-07-12 11:54:37.471 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141240410174 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:37.773 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289054796392 状态异常
2025-07-12 11:54:37.810 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308116666 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:37.844 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308099999 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:37.879 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782306220001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:37.913 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 011509134421 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:38.217 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 071404752 状态异常
2025-07-12 11:54:38.253 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 148307141290 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:38.559 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058606828 状态异常
2025-07-12 11:54:38.595 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076018 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:38.896 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140310121 状态异常
2025-07-12 11:54:38.932 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187886 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:38.966 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202008300467 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:39.350 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 150921012 状态异常
2025-07-12 11:54:39.386 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 energy000001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:39.682 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053321832 状态异常
2025-07-12 11:54:39.719 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000010141802 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:39.754 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000010141801 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:40.036 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 435057022691 状态异常
2025-07-12 11:54:40.071 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000941155 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:40.395 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 966075975598 状态异常
2025-07-12 11:54:40.431 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 244717020004 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:40.728 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058621843 状态异常
2025-07-12 11:54:40.763 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792304250024 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:41.029 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 230732020001 状态异常
2025-07-12 11:54:41.066 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230732020001 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:41.405 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 180000336 状态异常
2025-07-12 11:54:41.430 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 250118000150 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:41.454 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000018000150 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:41.740 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058610994 状态异常
2025-07-12 11:54:41.776 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140003 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:42.071 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 865068813782 状态异常
2025-07-12 11:54:42.108 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 123455555555 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:42.540 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058622692 状态异常
2025-07-12 11:54:42.575 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170003 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:43.358 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 210600057 状态异常
2025-07-12 11:54:43.382 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 152000048836 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:43.655 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058615803 状态异常
2025-07-12 11:54:43.675 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792408100001 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:43.988 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 712000002 状态异常
2025-07-12 11:54:44.017 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071200002 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:44.196 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100410103 查询失败或返回数据为空
2025-07-12 11:54:44.234 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000019041782 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:44.269 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000010900020 异常更新: 无数据返回 - 采集器不存在或未匹配 (第12次)
2025-07-12 11:54:44.544 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058634622 状态异常
2025-07-12 11:54:44.580 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308192129 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:44.875 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 112060062 状态异常
2025-07-12 11:54:44.914 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202112060062 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:45.201 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 88888888888888 状态异常
2025-07-12 11:54:45.238 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 88888888888888 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:45.507 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693052805228 状态异常
2025-07-12 11:54:45.543 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202109171124 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:45.883 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 180000337 状态异常
2025-07-12 11:54:45.922 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000018000151 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:46.217 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 112060025 状态异常
2025-07-12 11:54:46.240 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202112060025 异常更新: 无数据返回 - 采集器状态异常 (第12次)
2025-07-12 11:54:46.584 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 222811190004 状态异常
2025-07-12 11:54:46.607 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 222811190004 异常更新: 无数据返回 - 采集器状态异常 (第15次)
2025-07-12 11:54:46.609 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器状态查询完成 - 总采集器: 97, 有效采集器: 2, 参与同步设备: 8
2025-07-12 11:54:46.609 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组 701073943386: 6 台设备
2025-07-12 11:54:46.609 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组 482067317475: 2 台设备
2025-07-12 11:54:46.609 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 按采集器分组完成，共 2 个采集器组，参与同步设备: 8台，无采集器设备: 195台
2025-07-12 11:54:46.610 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始串行处理采集器组，设备数量: 2
2025-07-12 11:54:46.610 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始串行处理采集器组，设备数量: 6
2025-07-12 11:54:46.610 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 cs0000000085
2025-07-12 11:54:46.610 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 697595000018
2025-07-12 11:54:46.934 INFO  c.y.c.service.waterctrl.EquipmentReadingService - 设备读数记录保存成功: cs0000000085, 基础用量: 0.0000, 尖峰平谷用量: 0.0000/0.0000/0.0000/0.0000
2025-07-12 11:54:46.941 INFO  c.y.c.service.waterctrl.EquipmentReadingService - 设备读数记录保存成功: 697595000018, 基础用量: 0.0000, 尖峰平谷用量: 0.0000/0.0000/0.0000/0.0000
2025-07-12 11:54:46.964 ERROR c.y.c.service.waterctrl.EquipmentCostService - 44:用量为空或为零，无需计算费用
2025-07-12 11:54:46.964 ERROR c.y.c.service.waterctrl.EquipmentCostService - 44:用量为空或为零，无需计算费用
2025-07-12 11:54:47.162 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 cs0000000101
2025-07-12 11:54:47.178 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 697595000044
2025-07-12 11:54:47.421 INFO  c.y.c.service.waterctrl.EquipmentReadingService - 设备读数记录保存成功: cs0000000101, 基础用量: 0.0000, 尖峰平谷用量: 0.0000/0.0000/0.0000/0.0000
2025-07-12 11:54:47.435 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组处理完成，设备数量: 2
2025-07-12 11:54:47.439 ERROR c.y.c.service.waterctrl.EquipmentCostService - 44:用量为空或为零，无需计算费用
2025-07-12 11:54:47.470 INFO  c.y.c.service.waterctrl.EquipmentReadingService - 设备读数记录保存成功: 697595000044, 基础用量: 0.0000, 尖峰平谷用量: 0.0000/0.0000/0.0000/0.0000
2025-07-12 11:54:47.488 ERROR c.y.c.service.waterctrl.EquipmentCostService - 44:用量为空或为零，无需计算费用
2025-07-12 11:54:47.687 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 697595200175
2025-07-12 11:54:47.945 INFO  c.y.c.service.waterctrl.EquipmentReadingService - 设备读数记录保存成功: 697595200175, 基础用量: 0.3300, 尖峰平谷用量: 0.0000/0.0000/0.3300/0.0000
2025-07-12 11:54:47.995 INFO  c.y.campusos.service.waterctrl.SystemConfigService - 配置缓存刷新完成，共加载 58 个配置项
2025-07-12 11:54:48.056 INFO  c.y.c.service.waterctrl.EquipmentCostService - 费用计算记录保存成功: 697595200175
2025-07-12 11:54:48.166 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 697595200173
2025-07-12 11:54:48.427 INFO  c.y.c.service.waterctrl.EquipmentReadingService - 设备读数记录保存成功: 697595200173, 基础用量: 0.1900, 尖峰平谷用量: 0.0400/0.0300/0.0300/0.0900
2025-07-12 11:54:48.512 INFO  c.y.c.service.waterctrl.EquipmentCostService - 费用计算记录保存成功: 697595200173
2025-07-12 11:54:48.642 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 616408140029
2025-07-12 11:55:18.892 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140029 异常更新: API调用错误 - 同步设备读数失败, 请过几分钟再试 (第88次)
2025-07-12 11:55:18.893 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140029 同步失败: 同步设备读数失败, 请过几分钟再试
2025-07-12 11:55:18.893 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140029 将在 180 秒后重试，原因: 同步设备读数失败, 请过几分钟再试
2025-07-12 11:55:18.893 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140029 同步失败，添加到持久化重试队列，延迟 180 秒后重试
2025-07-12 11:55:18.920 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 延迟重试任务已添加 - 设备: 616408140029, 延迟: 3分钟, 执行时间: Sat Jul 12 11:58:18 CST 2025
2025-07-12 11:55:18.920 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140029 已添加到持久化重试队列，重试类型: 延迟重试
2025-07-12 11:55:19.141 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 202303002347
2025-07-12 11:55:19.421 INFO  c.y.c.service.waterctrl.EquipmentReadingService - 设备读数记录保存成功: 202303002347, 基础用量: 0.0000, 尖峰平谷用量: 0.0000/0.0000/0.0000/0.0000
2025-07-12 11:55:19.434 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组处理完成，设备数量: 6
2025-07-12 11:55:19.434 ERROR c.y.c.service.waterctrl.EquipmentCostService - 44:用量为空或为零，无需计算费用
2025-07-12 11:55:19.447 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备数据异步同步完成 - 总计:203, 成功:7, 失败:196(含无采集器:195), 重试:0
2025-07-12 12:00:00.013 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 12:00:00.024 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 11:52:38,973 to 2025-07-12 12:00:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 12:00:00.027 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 12:00:00.039 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 12:00:00.051 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 12:00:00.051 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 12:00:00.117 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-12 12:00:00.133 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-12 12:00:00.148 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-12 12:00:00.148 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-12 12:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 12:00:00,016 to 2025-07-12 12:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 12:20:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 12:20:00.033 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 12:20:00.050 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 12:20:00.068 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 12:20:00.068 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 13:39:04.235 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 12:15:00,010 to 2025-07-12 13:39:04,235
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 13:39:04.236 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 13:39:04,235 to 2025-07-12 13:39:04,236
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 13:39:04.236 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 13:39:04,236 to 2025-07-12 13:39:04,236
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 13:39:04.236 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 13:39:04,236 to 2025-07-12 13:39:04,236
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 13:39:04.237 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 13:39:04,236 to 2025-07-12 13:39:04,237
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 13:45:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 13:39:04,237 to 2025-07-12 13:45:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 13:49:04.233 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 13:49:04.254 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 13:49:04.272 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 13:49:04.290 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 13:49:04.290 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-12 13:49:04.393 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-12 13:49:04.412 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-12 13:49:04.433 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-12 13:49:04.434 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-12 14:00:00.002 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-12 14:00:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-12 13:45:00,017 to 2025-07-12 14:00:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-12 14:00:00.014 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-12 14:00:00.025 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-12 14:00:00.025 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-12 14:00:00.079 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-12 14:00:00.090 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-12 14:00:00.101 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-12 14:00:00.111 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-12 14:00:00.111 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
