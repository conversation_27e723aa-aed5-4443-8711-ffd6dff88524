2025-01-16 08:52:28.550 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 46716 (D:\yunmai\campusos5.0\campusos\target\classes started by <PERSON><PERSON> in D:\yunmai\campusos5.0)
2025-01-16 08:52:28.550 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 08:52:28.552 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 08:52:30.203 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 08:52:30.205 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 08:52:30.347 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 131 ms. Found 0 Redis repository interfaces.
2025-01-16 08:52:30.767 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$8b4c8be0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 08:52:30.816 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a52d158] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 08:52:31.027 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 08:52:31.036 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 08:52:31.037 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 08:52:31.037 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 08:52:31.188 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 08:52:31.188 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2584 ms
2025-01-16 08:52:31.266 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 08:52:31.521 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 08:52:32.861 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 08:52:33.440 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 08:52:33.440 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 08:52:34.182 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 08:52:34.337 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 08:52:34.363 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 08:52:34.369 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 08:52:34.541 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 08:52:34.980 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 08:52:34.995 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 08:52:35.213 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$24e6c2ad - 计划任务最多执行：0个
2025-01-16 08:52:35.350 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.232 seconds (JVM running for 8.471)
2025-01-16 08:52:36.580 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 08:52:39.413 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 08:52:39.431 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 08:52:39.431 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 08:52:39.435 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-01-16 08:52:46.812 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-01-16 08:52:46.815 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-01-16 08:53:07.536 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 09:00:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 08:52:34,540 to 2025-01-16 09:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 09:15:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 09:00:00,008 to 2025-01-16 09:15:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 09:30:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 09:15:00,012 to 2025-01-16 09:30:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 09:45:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 09:30:00,016 to 2025-01-16 09:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 10:00:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 09:45:00,010 to 2025-01-16 10:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 10:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 10:00:00,008 to 2025-01-16 10:15:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 10:30:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 10:15:00,014 to 2025-01-16 10:30:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 10:37:20.437 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 48164 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 10:37:20.437 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 10:37:20.439 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 10:37:21.894 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 10:37:21.896 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 10:37:22.022 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 116 ms. Found 0 Redis repository interfaces.
2025-01-16 10:37:22.383 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$7f4ea01d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 10:37:22.424 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$fe54e595] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 10:37:22.616 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 10:37:22.623 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 10:37:22.623 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 10:37:22.623 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 10:37:22.748 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 10:37:22.748 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2267 ms
2025-01-16 10:37:22.814 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 10:37:23.041 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 10:37:24.193 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 10:37:24.643 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 10:37:24.645 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 10:37:25.292 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 10:37:25.441 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 10:37:25.473 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 10:37:25.478 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 10:37:25.651 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 10:37:25.949 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 10:37:25.955 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 10:37:26.016 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$18e8d6ea - 计划任务最多执行：0个
2025-01-16 10:37:26.034 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 5.94 seconds (JVM running for 7.099)
2025-01-16 10:37:26.412 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 10:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 10:37:25,649 to 2025-01-16 10:45:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 10:48:20.567 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 10:48:20.574 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 10:48:20.574 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 10:48:20.575 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-16 10:48:25.816 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-01-16 10:48:25.818 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-01-16 10:48:28.809 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 10:49:00.842 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 22996 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 10:49:00.841 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 10:49:00.844 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 10:49:02.409 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 10:49:02.411 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 10:49:02.557 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 135 ms. Found 0 Redis repository interfaces.
2025-01-16 10:49:02.922 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b389b9c6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 10:49:02.966 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$328fff3e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 10:49:03.163 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 10:49:03.170 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 10:49:03.171 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 10:49:03.171 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 10:49:03.300 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 10:49:03.300 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2407 ms
2025-01-16 10:49:03.371 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 10:49:03.600 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 10:49:04.850 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 10:49:05.386 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 10:49:05.386 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 10:49:06.012 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 10:49:06.154 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 10:49:06.178 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 10:49:06.183 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 10:49:06.347 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 10:49:06.665 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 10:49:06.668 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 10:49:06.721 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$4d23f093 - 计划任务最多执行：0个
2025-01-16 10:49:06.740 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.252 seconds (JVM running for 7.201)
2025-01-16 10:49:07.117 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 10:49:13.692 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 10:49:13.702 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 10:49:13.702 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 10:49:13.704 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-01-16 10:49:17.002 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 10:50:17.487 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 28696 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 10:50:17.486 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 10:50:17.687 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 10:50:18.950 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 10:50:18.952 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 10:50:19.095 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 132 ms. Found 0 Redis repository interfaces.
2025-01-16 10:50:19.475 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$9492124e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 10:50:19.515 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$139857c6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 10:50:19.695 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 10:50:19.701 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 10:50:19.701 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 10:50:19.701 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 10:50:19.832 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 10:50:19.832 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2100 ms
2025-01-16 10:50:19.907 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 10:50:20.283 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 10:50:21.558 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 10:50:22.066 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 10:50:22.066 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 10:50:22.696 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 10:50:22.844 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 10:50:22.865 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 10:50:22.870 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 10:50:23.014 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 10:50:23.304 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 10:50:23.307 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 10:50:23.363 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$2e2c491b - 计划任务最多执行：0个
2025-01-16 10:50:23.381 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.213 seconds (JVM running for 7.058)
2025-01-16 10:50:23.572 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 10:50:23.579 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 10:50:23.579 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 10:50:23.581 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-01-16 10:50:23.771 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 10:50:30.500 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 10:55:50.376 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 28164 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 10:55:50.376 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 10:55:50.378 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 10:55:51.822 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 10:55:51.824 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 10:55:51.963 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 129 ms. Found 0 Redis repository interfaces.
2025-01-16 10:55:52.344 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$5d57adc5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 10:55:52.387 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$dc5df33d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 10:55:52.580 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 10:55:52.588 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 10:55:52.588 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 10:55:52.588 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 10:55:52.723 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 10:55:52.723 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2302 ms
2025-01-16 10:55:52.799 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 10:55:53.036 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 10:55:54.205 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 10:55:54.688 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 10:55:54.688 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 10:55:55.309 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 10:55:55.452 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 10:55:55.474 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 10:55:55.480 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 10:55:55.630 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 10:55:55.959 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 10:55:55.964 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 10:55:56.022 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$f6f1e492 - 计划任务最多执行：0个
2025-01-16 10:55:56.041 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.005 seconds (JVM running for 6.872)
2025-01-16 10:55:56.465 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 10:56:08.223 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 10:56:08.229 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 10:56:08.229 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 10:56:08.230 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-16 11:00:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 10:55:55,629 to 2025-01-16 11:00:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 11:00:32.197 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 4552 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 11:00:32.197 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 11:00:32.200 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 11:00:33.515 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 11:00:33.516 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 11:00:33.651 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 125 ms. Found 0 Redis repository interfaces.
2025-01-16 11:00:34.106 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$689ad5da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 11:00:34.165 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e7a11b52] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 11:00:34.367 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 11:00:34.374 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 11:00:34.374 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 11:00:34.374 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 11:00:34.534 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 11:00:34.535 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2286 ms
2025-01-16 11:00:34.618 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 11:00:34.845 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 11:00:36.104 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 11:00:36.653 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 11:00:36.653 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 11:00:37.274 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 11:00:37.411 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 11:00:37.433 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 11:00:37.439 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 11:00:37.587 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 11:00:37.897 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 11:00:37.901 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 11:00:37.954 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$2350ca7 - 计划任务最多执行：0个
2025-01-16 11:00:37.971 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.108 seconds (JVM running for 7.056)
2025-01-16 11:00:38.332 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 11:00:45.224 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 11:00:45.231 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 11:00:45.231 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 11:00:45.233 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-01-16 11:09:56.406 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select status from tb_card_cardinfo where cardno ='001757323232' and status != 0]
2025-01-16 11:09:56.407 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-16 11:09:56.425 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select status from tb_card_cardinfo where cardno ='001757323232' and status != 0]
2025-01-16 11:09:56.426 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-16 11:12:03.826 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 11:12:03.833 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 37428 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 11:12:03.834 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 11:12:05.307 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 11:12:05.308 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 11:12:05.442 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 122 ms. Found 0 Redis repository interfaces.
2025-01-16 11:12:05.819 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$66939b00] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 11:12:05.862 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e599e078] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 11:12:06.055 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 11:12:06.062 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 11:12:06.063 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 11:12:06.063 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 11:12:06.187 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 11:12:06.188 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2306 ms
2025-01-16 11:12:06.262 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 11:12:06.492 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 11:12:07.642 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 11:12:08.108 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 11:12:08.108 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 11:12:08.724 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 11:12:08.858 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 11:12:08.878 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 11:12:08.883 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 11:12:09.037 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 11:12:09.334 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 11:12:09.339 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 11:12:09.391 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$2dd1cd - 计划任务最多执行：0个
2025-01-16 11:12:09.410 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 5.995 seconds (JVM running for 6.898)
2025-01-16 11:12:09.817 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 11:12:11.046 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 11:12:11.060 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 11:12:11.061 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 11:12:11.065 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-01-16 11:15:00.019 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 11:12:09,036 to 2025-01-16 11:15:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 11:30:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 11:15:00,017 to 2025-01-16 11:30:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 11:45:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 11:30:00,002 to 2025-01-16 11:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 11:49:38.585 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 26508 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 11:49:38.585 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 11:49:38.588 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 11:49:39.781 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 11:49:39.783 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 11:49:39.902 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 109 ms. Found 0 Redis repository interfaces.
2025-01-16 11:49:40.265 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$69c1ee71] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 11:49:40.308 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e8c833e9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 11:49:40.490 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 11:49:40.497 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 11:49:40.498 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 11:49:40.498 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 11:49:40.626 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 11:49:40.626 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1994 ms
2025-01-16 11:49:40.696 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 11:49:40.750 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-01-16 11:49:40.791 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 11:49:42.012 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 11:49:42.456 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 11:49:42.456 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 11:49:43.075 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 11:49:43.210 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 11:49:43.233 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 11:49:43.239 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 11:49:43.383 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 11:49:43.686 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 11:49:43.690 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 11:49:43.768 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$35c253e - 计划任务最多执行：48个
2025-01-16 11:49:43.823 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-16 11:49:43.825 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-16 11:49:43.825 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-16 11:49:43.825 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-01-16 11:49:43.825 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-16 11:49:43.827 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-16 11:49:43.829 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-16 11:49:43.830 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-16 11:49:43.831 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-16 11:49:43.832 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-16 11:49:43.833 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-16 11:49:43.833 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-16 11:49:43.862 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 5.606 seconds (JVM running for 6.463)
2025-01-16 11:49:44.268 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 11:49:49.410 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 11:49:49.416 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 11:49:49.417 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 11:49:49.419 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-01-16 11:49:52.401 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 11:50:05.005 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select o.uid,o.orgcode,o.code,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname,o.years from tb_card_orgframework o  where o.status=1  and  o.orgtype in('1','3','4')  order by o.code asc ]
2025-01-16 11:50:05.006 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [select o.uid,o.orgcode,o.code,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname,o.years from tb_card_orgframework o  where o.status=1  and  o.orgtype in('1','3','4')  order by o.code asc ]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:50:05.176 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.focus,ct.status,ct.infolabel,ct.property,d.name AS propertyname , ct.linkwork1,ct.linkwork2,ct.major,ct.nowteacher,ct.teachername,ct.major1,ct.oldteacher,ct.major2,ct.recruitername,ct.diplomanumber,ct.graduatego from tb_card_teachstudinfo ct join tb_card_orgframework co on co.code = ct.orgcode and co.status = 1 LEFT JOIN tb_sys_dictionary d on d.code=ct.property and d.groupcode='SYS0000055'   where ct.infotype=2 and ct.status=1  order by ct.createdate desc limit 0,50]
2025-01-16 11:50:05.176 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.focus,ct.status,ct.infolabel,ct.property,d.name AS propertyname , ct.linkwork1,ct.linkwork2,ct.major,ct.nowteacher,ct.teachername,ct.major1,ct.oldteacher,ct.major2,ct.recruitername,ct.diplomanumber,ct.graduatego from tb_card_teachstudinfo ct join tb_card_orgframework co on co.code = ct.orgcode and co.status = 1 LEFT JOIN tb_sys_dictionary d on d.code=ct.property and d.groupcode='SYS0000055'   where ct.infotype=2 and ct.status=1  order by ct.createdate desc limit 0,50]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:50:05.176 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 234:SQL执行错误，详情请查询日志
2025-01-16 11:50:05.844 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select o.uid,o.code,o.orgcode,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o   where o.orgtype in('1','2') and o.status=1  order by o.code asc ]
2025-01-16 11:50:05.844 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [select o.uid,o.code,o.orgcode,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o   where o.orgtype in('1','2') and o.status=1  order by o.code asc ]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:50:06.103 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.position,ct.posrank,d.name as positionname,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.email,ct.focus,ct.plateno,ct.yunfei,ct.volume,ct.status,ct.infoindex,ct.passpassword,ct.infolabel,d1.name as propertyname,ct.infolabel,ct.property from tb_card_teachstudinfo ct LEFT JOIN tb_sys_dictionary d on d.code=ct.position and d.groupcode='SYS0000045' LEFT JOIN tb_sys_dictionary d1 on d1.code=ct.property and d1.groupcode='SYS0000055'  where ct.infotype=1 and ct.status=1  order by ct.createdate desc limit 0,50]
2025-01-16 11:50:06.105 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.position,ct.posrank,d.name as positionname,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.email,ct.focus,ct.plateno,ct.yunfei,ct.volume,ct.status,ct.infoindex,ct.passpassword,ct.infolabel,d1.name as propertyname,ct.infolabel,ct.property from tb_card_teachstudinfo ct LEFT JOIN tb_sys_dictionary d on d.code=ct.position and d.groupcode='SYS0000045' LEFT JOIN tb_sys_dictionary d1 on d1.code=ct.property and d1.groupcode='SYS0000055'  where ct.infotype=1 and ct.status=1  order by ct.createdate desc limit 0,50]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:50:06.105 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 234:SQL执行错误，详情请查询日志
2025-01-16 11:50:07.689 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select o.uid,o.code,o.orgcode,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o   where o.orgtype in('1','2') and o.status=1  order by o.code asc ]
2025-01-16 11:50:07.689 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [select o.uid,o.code,o.orgcode,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o   where o.orgtype in('1','2') and o.status=1  order by o.code asc ]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:50:52.970 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select o.uid,o.orgcode,o.code,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname,o.years from tb_card_orgframework o  where o.status=1  and  o.orgtype in('1','3','4')  order by o.code asc ]
2025-01-16 11:50:52.971 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [select o.uid,o.orgcode,o.code,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname,o.years from tb_card_orgframework o  where o.status=1  and  o.orgtype in('1','3','4')  order by o.code asc ]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:50:53.201 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.focus,ct.status,ct.infolabel,ct.property,d.name AS propertyname , ct.linkwork1,ct.linkwork2,ct.major,ct.nowteacher,ct.teachername,ct.major1,ct.oldteacher,ct.major2,ct.recruitername,ct.diplomanumber,ct.graduatego from tb_card_teachstudinfo ct join tb_card_orgframework co on co.code = ct.orgcode and co.status = 1 LEFT JOIN tb_sys_dictionary d on d.code=ct.property and d.groupcode='SYS0000055'   where ct.infotype=2 and ct.status=1  order by ct.createdate desc limit 0,50]
2025-01-16 11:50:53.201 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.focus,ct.status,ct.infolabel,ct.property,d.name AS propertyname , ct.linkwork1,ct.linkwork2,ct.major,ct.nowteacher,ct.teachername,ct.major1,ct.oldteacher,ct.major2,ct.recruitername,ct.diplomanumber,ct.graduatego from tb_card_teachstudinfo ct join tb_card_orgframework co on co.code = ct.orgcode and co.status = 1 LEFT JOIN tb_sys_dictionary d on d.code=ct.property and d.groupcode='SYS0000055'   where ct.infotype=2 and ct.status=1  order by ct.createdate desc limit 0,50]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:50:53.201 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 234:SQL执行错误，详情请查询日志
2025-01-16 11:50:53.689 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select o.uid,o.code,o.orgcode,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o   where o.orgtype in('1','2') and o.status=1  order by o.code asc ]
2025-01-16 11:50:53.689 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [select o.uid,o.code,o.orgcode,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o   where o.orgtype in('1','2') and o.status=1  order by o.code asc ]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:50:53.938 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.position,ct.posrank,d.name as positionname,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.email,ct.focus,ct.plateno,ct.yunfei,ct.volume,ct.status,ct.infoindex,ct.passpassword,ct.infolabel,d1.name as propertyname,ct.infolabel,ct.property from tb_card_teachstudinfo ct LEFT JOIN tb_sys_dictionary d on d.code=ct.position and d.groupcode='SYS0000045' LEFT JOIN tb_sys_dictionary d1 on d1.code=ct.property and d1.groupcode='SYS0000055'  where ct.infotype=1 and ct.status=1  order by ct.createdate desc limit 0,50]
2025-01-16 11:50:53.938 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.position,ct.posrank,d.name as positionname,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.email,ct.focus,ct.plateno,ct.yunfei,ct.volume,ct.status,ct.infoindex,ct.passpassword,ct.infolabel,d1.name as propertyname,ct.infolabel,ct.property from tb_card_teachstudinfo ct LEFT JOIN tb_sys_dictionary d on d.code=ct.position and d.groupcode='SYS0000045' LEFT JOIN tb_sys_dictionary d1 on d1.code=ct.property and d1.groupcode='SYS0000055'  where ct.infotype=1 and ct.status=1  order by ct.createdate desc limit 0,50]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:50:53.940 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 234:SQL执行错误，详情请查询日志
2025-01-16 11:52:41.628 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 44868 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 11:52:41.628 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 11:52:41.630 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 11:52:42.822 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 11:52:42.823 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 11:52:42.938 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 104 ms. Found 0 Redis repository interfaces.
2025-01-16 11:52:43.329 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$21e96b62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 11:52:43.370 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a0efb0da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 11:52:43.557 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 11:52:43.564 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 11:52:43.564 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 11:52:43.564 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 11:52:43.695 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 11:52:43.695 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2025 ms
2025-01-16 11:52:43.767 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 11:52:43.819 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-01-16 11:52:43.857 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 11:52:45.056 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 11:52:45.561 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 11:52:45.561 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 11:52:46.169 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 11:52:46.300 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 11:52:46.324 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 11:52:46.330 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 11:52:46.478 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 11:52:46.765 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 11:52:46.770 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 11:52:46.854 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$bb83a22f - 计划任务最多执行：48个
2025-01-16 11:52:46.912 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-16 11:52:46.916 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-16 11:52:46.916 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-16 11:52:46.916 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-01-16 11:52:46.916 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-16 11:52:46.918 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-16 11:52:46.919 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-16 11:52:46.920 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-16 11:52:46.921 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-16 11:52:46.923 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-16 11:52:46.925 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-16 11:52:46.925 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-16 11:52:46.957 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 5.674 seconds (JVM running for 6.662)
2025-01-16 11:52:47.353 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 11:52:54.650 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 11:52:54.668 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 11:52:54.668 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 11:52:54.671 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-01-16 11:53:07.208 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[a81adcff-5611-11ea-9e31-4cedfb0db910]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 11:53:15.316 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[a81adcff-5611-11ea-9e31-4cedfb0db910]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 11:53:21.027 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select o.uid,o.orgcode,o.code,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname,o.years from tb_card_orgframework o  where o.status=1  and  o.orgtype in('1','3','4')  order by o.code asc ]
2025-01-16 11:53:21.027 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [select o.uid,o.orgcode,o.code,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname,o.years from tb_card_orgframework o  where o.status=1  and  o.orgtype in('1','3','4')  order by o.code asc ]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:53:21.277 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select a.uid,a.code,a.name,a.sex,a.card,a.cardsn,a.orgcode,a.infotype,a.intoyear,a.focus,a.status,b.imgpath, getorgname(a.orgcode) as orgname,photo_quality,(SELECT COUNT(1) FROM tb_face_infoface b WHERE b.infoid = a.uid ) as total,(SELECT COUNT(1) FROM tb_face_finger_infofinger b WHERE b.infoid = a.uid ) as fingertotal from tb_card_teachstudinfo a LEFT JOIN tb_face_infoface b ON a.uid = b.infoid where  a.status=1  order by a.createdate desc limit 0,50]
2025-01-16 11:53:21.277 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select a.uid,a.code,a.name,a.sex,a.card,a.cardsn,a.orgcode,a.infotype,a.intoyear,a.focus,a.status,b.imgpath, getorgname(a.orgcode) as orgname,photo_quality,(SELECT COUNT(1) FROM tb_face_infoface b WHERE b.infoid = a.uid ) as total,(SELECT COUNT(1) FROM tb_face_finger_infofinger b WHERE b.infoid = a.uid ) as fingertotal from tb_card_teachstudinfo a LEFT JOIN tb_face_infoface b ON a.uid = b.infoid where  a.status=1  order by a.createdate desc limit 0,50]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:53:21.278 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 234:SQL执行错误，详情请查询日志
2025-01-16 11:53:21.295 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select a.uid,a.code,a.name,a.sex,a.card,a.cardsn,a.orgcode,a.infotype,a.intoyear,a.focus,a.status,b.imgpath, getorgname(a.orgcode) as orgname,photo_quality,(SELECT COUNT(1) FROM tb_face_infoface b WHERE b.infoid = a.uid ) as total,(SELECT COUNT(1) FROM tb_face_finger_infofinger b WHERE b.infoid = a.uid ) as fingertotal from tb_card_teachstudinfo a LEFT JOIN tb_face_infoface b ON a.uid = b.infoid where  a.status=1  order by a.createdate desc limit 0,50]
2025-01-16 11:53:21.295 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select a.uid,a.code,a.name,a.sex,a.card,a.cardsn,a.orgcode,a.infotype,a.intoyear,a.focus,a.status,b.imgpath, getorgname(a.orgcode) as orgname,photo_quality,(SELECT COUNT(1) FROM tb_face_infoface b WHERE b.infoid = a.uid ) as total,(SELECT COUNT(1) FROM tb_face_finger_infofinger b WHERE b.infoid = a.uid ) as fingertotal from tb_card_teachstudinfo a LEFT JOIN tb_face_infoface b ON a.uid = b.infoid where  a.status=1  order by a.createdate desc limit 0,50]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:53:21.296 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 234:SQL执行错误，详情请查询日志
2025-01-16 11:53:21.334 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select a.uid,a.code,a.name,a.sex,a.card,a.cardsn,a.orgcode,a.infotype,a.intoyear,a.focus,a.status,b.imgpath, getorgname(a.orgcode) as orgname,photo_quality,(SELECT COUNT(1) FROM tb_face_infoface b WHERE b.infoid = a.uid ) as total,(SELECT COUNT(1) FROM tb_face_finger_infofinger b WHERE b.infoid = a.uid ) as fingertotal from tb_card_teachstudinfo a LEFT JOIN tb_face_infoface b ON a.uid = b.infoid where  a.status=1  order by a.createdate desc limit 0,50]
2025-01-16 11:53:21.335 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select a.uid,a.code,a.name,a.sex,a.card,a.cardsn,a.orgcode,a.infotype,a.intoyear,a.focus,a.status,b.imgpath, getorgname(a.orgcode) as orgname,photo_quality,(SELECT COUNT(1) FROM tb_face_infoface b WHERE b.infoid = a.uid ) as total,(SELECT COUNT(1) FROM tb_face_finger_infofinger b WHERE b.infoid = a.uid ) as fingertotal from tb_card_teachstudinfo a LEFT JOIN tb_face_infoface b ON a.uid = b.infoid where  a.status=1  order by a.createdate desc limit 0,50]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:53:21.335 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 234:SQL执行错误，详情请查询日志
2025-01-16 11:53:23.008 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select o.uid,o.code,o.orgcode,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o   where o.orgtype in('1','2') and o.status=1  order by o.code asc ]
2025-01-16 11:53:23.010 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [select o.uid,o.code,o.orgcode,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o   where o.orgtype in('1','2') and o.status=1  order by o.code asc ]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:53:23.236 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.position,ct.posrank,d.name as positionname,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.email,ct.focus,ct.plateno,ct.yunfei,ct.volume,ct.status,ct.infoindex,ct.passpassword,ct.infolabel,d1.name as propertyname,ct.infolabel,ct.property from tb_card_teachstudinfo ct LEFT JOIN tb_sys_dictionary d on d.code=ct.position and d.groupcode='SYS0000045' LEFT JOIN tb_sys_dictionary d1 on d1.code=ct.property and d1.groupcode='SYS0000055'  where ct.infotype=1 and ct.status=1  order by ct.createdate desc limit 0,50]
2025-01-16 11:53:23.237 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.position,ct.posrank,d.name as positionname,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.email,ct.focus,ct.plateno,ct.yunfei,ct.volume,ct.status,ct.infoindex,ct.passpassword,ct.infolabel,d1.name as propertyname,ct.infolabel,ct.property from tb_card_teachstudinfo ct LEFT JOIN tb_sys_dictionary d on d.code=ct.position and d.groupcode='SYS0000045' LEFT JOIN tb_sys_dictionary d1 on d1.code=ct.property and d1.groupcode='SYS0000055'  where ct.infotype=1 and ct.status=1  order by ct.createdate desc limit 0,50]; nested exception is java.sql.SQLSyntaxErrorException: FUNCTION xtcampusos.getorgname does not exist
2025-01-16 11:53:23.237 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 234:SQL执行错误，详情请查询日志
2025-01-16 11:57:55.739 INFO  com.ymiots.campusos.service.face.IntoFaceService - 导入人脸人员编号： find_in_set(code,'00648,00003,03514,03679,00045,00122,03879,00722,05335,00004,05415,05139,05777,05292,11343,00317,05768,00034,00116,00358,00753,00038,00115,00070,10900,00151,05282,00309,00629,00069,00541,00585,05115,05678,00064,04062,05275,05396,20001,00332,00012,00853,00775,03522,05305,05547,05700,05741,05784,00097,00373,05383,00010,11057')>0 
2025-01-16 11:57:55.801 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-01-16 12:00:00.087 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 11:52:46,477 to 2025-01-16 12:00:00,084
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 12:01:00.005 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-01-16 12:01:00.017 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-01-16 12:01:00.043 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-01-16 12:01:00.043 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-01-16 12:01:00.043 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-01-16计划任务[TASK000056]错误
2025-01-16 12:15:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 12:00:00,084 to 2025-01-16 12:15:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 12:30:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 12:15:00,011 to 2025-01-16 12:30:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 12:45:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 12:30:00,008 to 2025-01-16 12:45:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 13:00:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 12:45:00,002 to 2025-01-16 13:00:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 13:01:00.005 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-01-16 13:01:00.008 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-01-16 13:01:00.046 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-01-16 13:01:00.046 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-01-16 13:01:00.046 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-01-16计划任务[TASK000056]错误
2025-01-16 13:09:18.921 INFO  com.ymiots.campusos.service.face.IntoFaceService - 导入人脸人员编号： find_in_set(code,'00648,00003,03514,03679,00045,00122,03879,00722,05335,00004,05415,05139,05777,05292,11343,00317,05768,00034,00116,00358,00753,00038,00115,00070,10900,00151,05282,00309,00629,00069,00541,00585,05115,05678,00064,04062,05275,05396,20001,00332,00012,00853,00775,03522,05305,05547,05700,05741,05784,00097,00373,05383,00010,11057')>0 
2025-01-16 13:09:18.962 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-01-16 13:09:54.994 INFO  com.ymiots.campusos.service.face.IntoFaceService - 导入人脸人员编号： find_in_set(code,'00648,00003,03514,03679,00045,00122,03879,00722,05335,00004,05415,05139,05777,05292,11343,00317,05768,00034,00116,00358,00753,00038,00115,00070,10900,00151,05282,00309,00629,00069,00541,00585,05115,05678,00064,04062,05275,05396,20001,00332,00012,00853,00775,03522,05305,05547,05700,05741,05784,00097,00373,05383,00010,11057')>0 
2025-01-16 13:09:55.034 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-01-16 13:10:23.076 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 13:10:23.079 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 17596 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 13:10:23.080 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 13:10:24.624 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 13:10:24.626 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 13:10:24.748 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 111 ms. Found 0 Redis repository interfaces.
2025-01-16 13:10:25.114 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1e63073d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 13:10:25.158 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$9d694cb5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 13:10:25.350 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 13:10:25.357 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 13:10:25.357 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 13:10:25.357 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 13:10:25.489 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 13:10:25.489 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2362 ms
2025-01-16 13:10:25.557 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 13:10:25.606 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-01-16 13:10:25.646 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 13:10:26.773 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 13:10:27.298 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 13:10:27.298 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 13:10:27.898 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 13:10:28.030 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 13:10:28.052 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 13:10:28.058 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 13:10:28.207 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 13:10:28.484 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 13:10:28.519 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 13:10:28.600 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$b7fd3e0a - 计划任务最多执行：48个
2025-01-16 13:10:28.660 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-16 13:10:28.662 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-16 13:10:28.662 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-16 13:10:28.662 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-01-16 13:10:28.663 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-16 13:10:28.664 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-16 13:10:28.666 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-16 13:10:28.667 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-16 13:10:28.668 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-16 13:10:28.669 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-16 13:10:28.670 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-16 13:10:28.670 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-16 13:10:28.702 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 5.971 seconds (JVM running for 6.946)
2025-01-16 13:10:29.086 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 13:10:37.987 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 13:10:37.994 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 13:10:37.994 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 13:10:37.997 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-01-16 13:10:48.015 INFO  com.ymiots.campusos.service.face.IntoFaceService - 导入人脸人员编号： find_in_set(code,'00648,00003,03514,03679,00045,00122,03879,00722,05335,00004,05415,05139,05777,05292,11343,00317,05768,00034,00116,00358,00753,00038,00115,00070,10900,00151,05282,00309,00629,00069,00541,00585,05115,05678,00064,04062,05275,05396,20001,00332,00012,00853,00775,03522,05305,05547,05700,05741,05784,00097,00373,05383,00010,11057')>0 
2025-01-16 13:10:48.055 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-01-16 13:13:24.002 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 30884 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 13:13:24.002 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 13:13:24.005 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 13:13:25.224 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 13:13:25.225 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 13:13:25.332 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 98 ms. Found 0 Redis repository interfaces.
2025-01-16 13:13:25.684 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$9147e6e2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 13:13:25.726 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$104e2c5a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 13:13:25.902 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 13:13:25.908 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 13:13:25.908 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 13:13:25.908 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 13:13:26.033 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 13:13:26.034 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1986 ms
2025-01-16 13:13:26.104 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 13:13:26.152 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-01-16 13:13:26.187 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 13:13:27.876 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 13:13:28.361 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 13:13:28.361 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 13:13:28.998 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 13:13:29.132 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 13:13:29.154 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 13:13:29.158 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 13:13:29.308 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 13:13:29.601 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 13:13:29.606 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 13:13:29.688 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$2ae21daf - 计划任务最多执行：48个
2025-01-16 13:13:29.759 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-16 13:13:29.762 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-16 13:13:29.762 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-16 13:13:29.762 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-01-16 13:13:29.762 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-16 13:13:29.764 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-16 13:13:29.765 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-16 13:13:29.767 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-16 13:13:29.768 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-16 13:13:29.769 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-16 13:13:29.770 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-16 13:13:29.770 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-16 13:13:29.799 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.13 seconds (JVM running for 7.053)
2025-01-16 13:13:30.268 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 13:13:44.233 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 13:13:44.240 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 13:13:44.240 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 13:13:44.241 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-16 13:15:00.544 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 13:13:29,307 to 2025-01-16 13:15:00,541
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 13:16:16.837 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 43500 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 13:16:16.835 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 13:16:16.839 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 13:16:18.220 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 13:16:18.222 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 13:16:18.337 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 105 ms. Found 0 Redis repository interfaces.
2025-01-16 13:16:18.719 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$d1675e26] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 13:16:18.760 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$506da39e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 13:16:18.960 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 13:16:18.967 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 13:16:18.968 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 13:16:18.968 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 13:16:19.114 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 13:16:19.114 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2231 ms
2025-01-16 13:16:19.188 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 13:16:19.246 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-01-16 13:16:19.288 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 13:16:20.461 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 13:16:20.997 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 13:16:20.997 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 13:16:21.626 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 13:16:21.756 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 13:16:21.780 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 13:16:21.786 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 13:16:21.933 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 13:16:22.232 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 13:16:22.237 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 13:16:22.326 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$6b0194f3 - 计划任务最多执行：48个
2025-01-16 13:16:22.393 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-16 13:16:22.396 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-16 13:16:22.396 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-16 13:16:22.396 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-01-16 13:16:22.396 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-16 13:16:22.398 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-16 13:16:22.399 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-16 13:16:22.401 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-16 13:16:22.402 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-16 13:16:22.403 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-16 13:16:22.405 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-16 13:16:22.405 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-16 13:16:22.434 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 5.989 seconds (JVM running for 6.91)
2025-01-16 13:16:22.915 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 13:18:36.719 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 13:18:36.737 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 13:18:36.738 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 13:18:36.742 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-01-16 13:18:41.850 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[a81adcff-5611-11ea-9e31-4cedfb0db910]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 13:19:13.041 INFO  com.ymiots.campusos.service.face.IntoFaceService - 导入人脸人员编号： find_in_set(code,'00648,00003,03514,03679,00045,00122,03879,00722,05335,00004,05415,05139,05777,05292,11343,00317,05768,00034,00116,00358,00753,00038,00115,00070,10900,00151,05282,00309,00629,00069,00541,00585,05115,05678,00064,04062,05275,05396,20001,00332,00012,00853,00775,03522,05305,05547,05700,05741,05784,00097,00373,05383,00010,11057')>0 
2025-01-16 13:19:13.114 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-01-16 13:19:59.468 INFO  com.ymiots.campusos.service.face.IntoFaceService - 导入人脸人员编号： find_in_set(code,'00648,00003,03514,03679,00045,00122,03879,00722,05335,00004,05415,05139,05777,05292,11343,00317,05768,00034,00116,00358,00753,00038,00115,00070,10900,00151,05282,00309,00629,00069,00541,00585,05115,05678,00064,04062,05275,05396,20001,00332,00012,00853,00775,03522,05305,05547,05700,05741,05784,00097,00373,05383,00010,11057')>0 
2025-01-16 13:19:59.542 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-01-16 13:20:25.009 INFO  com.ymiots.campusos.service.face.IntoFaceService - 导入人脸人员编号： find_in_set(code,'00648,00003,03514,03679,00045,00122,03879,00722,05335,00004,05415,05139,05777,05292,11343,00317,05768,00034,00116,00358,00753,00038,00115,00070,10900,00151,05282,00309,00629,00069,00541,00585,05115,05678,00064,04062,05275,05396,20001,00332,00012,00853,00775,03522,05305,05547,05700,05741,05784,00097,00373,05383,00010,11057')>0 
2025-01-16 13:20:25.083 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-01-16 13:24:06.201 INFO  com.ymiots.campusos.service.face.IntoFaceService - 导入人脸人员编号： find_in_set(code,'00648,00003,03514,03679,00045,00122,03879,00722,05335,00004,05415,05139,05777,05292,11343,00317,05768,00034,00116,00358,00753,00038,00115,00070,10900,00151,05282,00309,00629,00069,00541,00585,05115,05678,00064,04062,05275,05396,20001,00332,00012,00853,00775,03522,05305,05547,05700,05741,05784,00097,00373,05383,00010,11057')>0 
2025-01-16 13:24:06.340 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-01-16 13:24:24.728 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 13:24:24.728 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 30408 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 13:24:24.731 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 13:24:25.994 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 13:24:25.996 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 13:24:26.111 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 104 ms. Found 0 Redis repository interfaces.
2025-01-16 13:24:26.492 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$c76bca90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 13:24:26.534 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$46721008] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 13:24:26.713 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 13:24:26.718 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 13:24:26.718 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 13:24:26.720 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 13:24:26.847 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 13:24:26.847 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2074 ms
2025-01-16 13:24:26.917 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 13:24:26.963 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-01-16 13:24:26.998 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 13:24:28.112 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 13:24:28.810 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 13:24:28.810 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 13:24:29.412 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 13:24:29.550 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 13:24:29.572 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 13:24:29.578 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 13:24:29.724 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 13:24:30.016 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 13:24:30.019 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 13:24:30.107 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$6106015d - 计划任务最多执行：48个
2025-01-16 13:24:30.166 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-16 13:24:30.168 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-16 13:24:30.168 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-16 13:24:30.168 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-01-16 13:24:30.168 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-16 13:24:30.170 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-16 13:24:30.172 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-16 13:24:30.173 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-16 13:24:30.174 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-16 13:24:30.175 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-16 13:24:30.176 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-16 13:24:30.176 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-16 13:24:30.210 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 5.808 seconds (JVM running for 6.803)
2025-01-16 13:24:30.593 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 13:24:36.212 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 13:24:36.217 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 13:24:36.217 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 13:24:36.219 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-01-16 13:24:38.102 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[a81adcff-5611-11ea-9e31-4cedfb0db910]主题[batchUpload]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 13:25:45.644 INFO  com.ymiots.campusos.service.face.IntoFaceService - 导入人脸人员编号： find_in_set(code,'00648,00003,03514,03679,00045,00122,03879,00722,05335,00004,05415,05139,05777,05292,11343,00317,05768,00034,00116,00358,00753,00038,00115,00070,10900,00151,05282,00309,00629,00069,00541,00585,05115,05678,00064,04062,05275,05396,20001,00332,00012,00853,00775,03522,05305,05547,05700,05741,05784,00097,00373,05383,00010,11057')>0 
2025-01-16 13:33:55.433 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 13:24:29,722 to 2025-01-16 13:33:55,426
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 13:33:55.433 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-01-16 13:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 13:33:55,426 to 2025-01-16 13:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 14:00:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 13:45:00,016 to 2025-01-16 14:00:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 14:01:00.011 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-01-16 14:01:00.017 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-01-16 14:01:00.221 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-01-16 14:01:00.223 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-01-16 14:01:00.223 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-01-16计划任务[TASK000056]错误
2025-01-16 14:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 14:00:00,015 to 2025-01-16 14:15:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 15:23:15.498 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 15260 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 15:23:15.498 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 15:23:15.500 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 15:23:16.846 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 15:23:16.848 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 15:23:16.986 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 125 ms. Found 0 Redis repository interfaces.
2025-01-16 15:23:17.391 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$6351aec1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 15:23:17.434 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e257f439] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 15:23:17.626 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 15:23:17.633 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 15:23:17.634 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 15:23:17.634 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 15:23:17.767 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 15:23:17.767 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2222 ms
2025-01-16 15:23:17.840 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 15:23:20.390 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 15:23:21.637 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 15:23:22.110 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 15:23:22.110 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 15:23:22.834 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 15:23:22.979 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 15:23:23.004 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 15:23:23.010 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 15:23:23.170 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 15:23:23.508 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 15:23:23.511 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 15:23:23.604 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$fcebe58e - 计划任务最多执行：58个
2025-01-16 15:23:23.675 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-16 15:23:23.678 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-16 15:23:23.678 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-16 15:23:23.679 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[自动生成卡号]成功
2025-01-16 15:23:23.681 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-01-16 15:23:23.682 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-01-16 15:23:23.682 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-16 15:23:23.684 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-16 15:23:23.685 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-16 15:23:23.686 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-01-16 15:23:23.688 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[订餐定时删除人脸授权]成功
2025-01-16 15:23:23.690 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[大华人脸机时段下发]成功
2025-01-16 15:23:23.690 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[处理订餐人脸照片下发]成功
2025-01-16 15:23:23.693 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-16 15:23:23.695 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-16 15:23:23.696 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-16 15:23:23.698 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-01-16 15:23:23.699 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-16 15:23:23.700 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-01-16 15:23:23.701 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-16 15:23:23.702 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-01-16 15:23:23.733 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 8.603 seconds (JVM running for 9.72)
2025-01-16 15:23:24.144 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 15:30:00.024 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 15:23:23,169 to 2025-01-16 15:30:00,020
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 15:36:46.507 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 15:36:46.520 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 15:36:46.520 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 15:36:46.538 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 17 ms
2025-01-16 15:36:57.196 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[2]用户[a81adcff-5611-11ea-9e31-4cedfb0db910]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 15:40:00.008 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-16 15:40:00.047 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-16 15:40:00.072 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-16 15:40:00.097 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-16 15:40:00.097 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-16 15:41:23.054 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-16 15:41:23.054 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 25368 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-16 15:41:23.056 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-16 15:41:24.637 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 15:41:24.638 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 15:41:24.782 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 132 ms. Found 0 Redis repository interfaces.
2025-01-16 15:41:25.193 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$ed5013b2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 15:41:25.237 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$6c56592a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-16 15:41:25.426 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-16 15:41:25.433 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-16 15:41:25.434 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 15:41:25.434 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-16 15:41:25.568 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-16 15:41:25.569 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2470 ms
2025-01-16 15:41:25.640 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 15:41:28.178 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 15:41:29.347 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-16 15:41:29.880 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-16 15:41:29.880 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-16 15:41:30.556 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-16 15:41:30.685 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-16 15:41:30.706 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-16 15:41:30.712 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-16 15:41:30.862 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-16 15:41:31.172 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-16 15:41:31.176 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-16 15:41:31.259 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$86ea4a7f - 计划任务最多执行：58个
2025-01-16 15:41:31.335 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-01-16 15:41:31.338 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-01-16 15:41:31.339 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-01-16 15:41:31.340 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[自动生成卡号]成功
2025-01-16 15:41:31.341 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-01-16 15:41:31.342 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-01-16 15:41:31.343 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-01-16 15:41:31.344 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-01-16 15:41:31.345 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-01-16 15:41:31.347 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-01-16 15:41:31.349 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[订餐定时删除人脸授权]成功
2025-01-16 15:41:31.350 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[大华人脸机时段下发]成功
2025-01-16 15:41:31.350 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[处理订餐人脸照片下发]成功
2025-01-16 15:41:31.352 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-01-16 15:41:31.354 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-01-16 15:41:31.355 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-01-16 15:41:31.357 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-01-16 15:41:31.358 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-01-16 15:41:31.360 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-01-16 15:41:31.360 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-01-16 15:41:31.361 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-01-16 15:41:31.394 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 8.677 seconds (JVM running for 9.626)
2025-01-16 15:41:31.870 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-16 15:41:34.211 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-16 15:41:34.217 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 15:41:34.217 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 15:41:34.218 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-16 15:42:37.582 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-01-16 15:42:37.601 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-01-16 15:42:38.783 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-01-16 15:42:38.788 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-01-16 15:42:42.376 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-16 15:45:00.019 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 15:41:30,861 to 2025-01-16 15:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 16:00:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 15:45:00,015 to 2025-01-16 16:00:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 16:00:00.025 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-16 16:00:00.080 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-16 16:00:00.127 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-16 16:00:00.127 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-16 16:00:00.326 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-16 16:00:00.401 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-16 16:00:00.554 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-16 16:00:00.588 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-16 16:00:00.588 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-16 16:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 16:00:00,015 to 2025-01-16 16:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 16:20:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-16 16:20:00.053 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-16 16:20:00.100 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-16 16:20:00.131 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-16 16:20:00.131 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-16 16:30:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 16:15:00,007 to 2025-01-16 16:30:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 16:40:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-16 16:40:00.049 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-16 16:40:00.089 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-16 16:40:00.117 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-16 16:40:00.117 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-16 16:59:32.000 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 16:30:00,010 to 2025-01-16 16:59:31,995
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 16:59:32.561 WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.ymiots.campusos.common.GlobalExceptionHandler#handlerException(Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:784)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:299)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:273)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:228)
	at com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter.write(FastJsonHttpMessageConverter.java:246)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:290)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1330)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1141)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:469)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:593)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:537)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
	at org.apache.coyote.http11.filters.IdentityOutputFilter.doWrite(IdentityOutputFilter.java:73)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
	at org.apache.coyote.Response.doWrite(Response.java:615)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
	... 56 common frames omitted
2025-01-16 17:00:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 16:59:31,995 to 2025-01-16 17:00:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 17:00:00.020 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-01-16 17:00:00.049 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-01-16 17:00:00.076 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-01-16 17:00:00.076 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-01-16 17:00:00.213 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-16 17:00:00.252 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-16 17:00:00.289 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-16 17:00:00.322 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-16 17:00:00.322 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-16 17:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 17:00:00,012 to 2025-01-16 17:15:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-16 17:20:00.001 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-01-16 17:20:00.057 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-01-16 17:20:00.095 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-01-16 17:20:00.124 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-01-16 17:20:00.126 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-01-16 17:30:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-16 17:15:00,013 to 2025-01-16 17:30:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

