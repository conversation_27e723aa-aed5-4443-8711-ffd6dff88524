2025-04-15 10:49:58.246 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 29196 (D:\yunmai\campusos5.0\campusos\target\classes started by Tan<PERSON> in D:\yunmai\campusos5.0)
2025-04-15 10:49:58.244 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-04-15 10:49:58.247 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-04-15 10:49:59.972 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-15 10:49:59.981 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-15 10:50:00.167 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 176 ms. Found 0 Redis repository interfaces.
2025-04-15 10:50:00.580 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$52f452ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:50:00.625 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d1fa9842] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:50:00.813 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-04-15 10:50:00.820 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-04-15 10:50:00.820 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-15 10:50:00.820 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-15 10:50:00.978 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-15 10:50:00.978 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2682 ms
2025-04-15 10:50:01.052 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-15 10:50:01.099 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-15 10:50:01.621 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-15 10:50:03.320 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-15 10:50:03.399 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-15 10:50:03.399 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-15 10:50:04.050 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-15 10:50:04.182 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-15 10:50:04.203 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-15 10:50:04.207 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-15 10:50:04.271 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-15 10:50:04.604 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-04-15 10:50:04.608 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-04-15 10:50:04.658 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$ec8e8997 - 计划任务最多执行：50个
2025-04-15 10:50:04.692 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-04-15 10:50:04.694 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-04-15 10:50:04.694 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-04-15 10:50:04.694 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步消费考勤1]成功
2025-04-15 10:50:04.694 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-04-15 10:50:04.695 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-04-15 10:50:04.697 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-04-15 10:50:04.698 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-04-15 10:50:04.698 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步消费考勤2]成功
2025-04-15 10:50:04.699 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-04-15 10:50:04.701 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-04-15 10:50:04.702 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-04-15 10:50:04.703 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-04-15 10:50:04.703 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-04-15 10:50:04.712 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.823 seconds (JVM running for 8.645)
2025-04-15 10:50:05.136 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-04-15 10:54:43.920 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 35208 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-15 10:54:43.923 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-04-15 10:54:44.848 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-15 10:54:44.850 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-15 10:54:44.926 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 66 ms. Found 0 Redis repository interfaces.
2025-04-15 10:54:45.570 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-04-15 10:54:45.578 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-04-15 10:54:45.578 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-15 10:54:45.578 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-15 10:54:45.727 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-15 10:54:45.727 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1762 ms
2025-04-15 10:54:45.799 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-15 10:54:45.844 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-15 10:54:46.028 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-15 10:54:46.883 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-15 10:54:47.203 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-15 10:54:47.203 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-15 10:54:48.301 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-04-15 10:54:48.401 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-15 10:54:48.424 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-15 10:54:48.428 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-15 10:54:48.580 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-15 10:54:48.841 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-04-15 10:54:48.849 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-04-15 10:54:48.945 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.382 seconds (JVM running for 6.304)
2025-04-15 10:55:42.459 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 29848 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-15 10:55:42.459 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-04-15 10:55:42.462 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-04-15 10:55:43.888 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-15 10:55:43.889 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-15 10:55:44.066 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 164 ms. Found 0 Redis repository interfaces.
2025-04-15 10:55:44.470 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$847b580d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:55:44.512 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$3819d85] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 10:55:44.694 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-04-15 10:55:44.700 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-04-15 10:55:44.702 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-15 10:55:44.702 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-15 10:55:44.855 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-15 10:55:44.855 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2348 ms
2025-04-15 10:55:44.929 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-15 10:55:44.974 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-15 10:55:45.158 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-15 10:55:46.603 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-15 10:55:46.678 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-15 10:55:46.678 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-15 10:55:47.305 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-15 10:55:47.431 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-15 10:55:47.449 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-15 10:55:47.454 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-15 10:55:47.518 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-15 10:55:47.828 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-04-15 10:55:47.832 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-04-15 10:55:47.879 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$1e158eda - 计划任务最多执行：0个
2025-04-15 10:55:47.898 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 5.781 seconds (JVM running for 6.702)
2025-04-15 10:55:48.230 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-04-15 10:56:00.317 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-04-15 10:56:00.326 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-15 10:56:00.327 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-15 10:56:00.329 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-15 10:56:01.897 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-04-15 10:56:05.357 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-04-15 10:56:05.370 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-04-15 10:56:08.015 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-04-15 10:56:08.065 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-04-15 10:56:08.079 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-04-15 10:56:15.437 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-04-15 10:59:15.128 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-04-15 10:59:15.129 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-04-15 10:59:21.702 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-04-15 10:59:21.704 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-04-15 10:59:25.177 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-04-15 10:59:25.179 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-04-15 11:00:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 10:55:47,516 to 2025-04-15 11:00:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 11:00:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 10:54:48,578 to 2025-04-15 11:00:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 11:00:00.021 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-04-15 11:01:14.736 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-04-15 11:01:20.460 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 35580 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-15 11:01:20.462 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-04-15 11:01:21.278 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-15 11:01:21.279 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-15 11:01:21.345 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 54 ms. Found 0 Redis repository interfaces.
2025-04-15 11:01:21.928 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-04-15 11:01:21.934 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-04-15 11:01:21.935 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-15 11:01:21.935 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-15 11:01:22.081 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-15 11:01:22.081 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1581 ms
2025-04-15 11:01:22.152 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-15 11:01:22.197 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-15 11:01:22.361 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-15 11:01:23.075 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-15 11:01:23.383 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-15 11:01:23.383 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-15 11:01:24.417 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-04-15 11:01:24.518 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-15 11:01:24.542 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-15 11:01:24.548 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-15 11:01:24.696 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-15 11:01:24.941 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-04-15 11:01:24.948 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-04-15 11:01:25.030 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 4.921 seconds (JVM running for 5.889)
2025-04-15 11:01:36.178 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-04-15 11:01:41.130 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 38212 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-15 11:01:41.128 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-04-15 11:01:41.131 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-04-15 11:01:42.631 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-15 11:01:42.633 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-15 11:01:42.822 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 177 ms. Found 0 Redis repository interfaces.
2025-04-15 11:01:43.251 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$3bce6041] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 11:01:43.301 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$bad4a5b9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-15 11:01:43.557 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-04-15 11:01:43.567 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-04-15 11:01:43.568 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-15 11:01:43.568 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-15 11:01:43.753 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-15 11:01:43.753 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2573 ms
2025-04-15 11:01:43.836 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-15 11:01:43.885 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-15 11:01:44.143 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-15 11:01:45.643 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-15 11:01:45.730 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-15 11:01:45.730 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-15 11:01:46.355 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-15 11:01:46.479 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-15 11:01:46.499 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-15 11:01:46.503 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-15 11:01:46.568 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-15 11:01:46.896 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-04-15 11:01:46.901 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-04-15 11:01:46.958 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$d568970e - 计划任务最多执行：0个
2025-04-15 11:01:46.978 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.224 seconds (JVM running for 7.174)
2025-04-15 11:01:47.494 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-04-15 11:01:59.543 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-04-15 11:01:59.549 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-15 11:01:59.549 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-15 11:01:59.550 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-15 11:02:00.251 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-04-15 11:02:04.595 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-04-15 11:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 11:01:46,565 to 2025-04-15 11:15:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 11:15:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 11:01:24,695 to 2025-04-15 11:15:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 11:22:58.079 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-15 11:22:58.080 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-15 11:22:58.083 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-04-15 11:23:28.642 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 12692 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-15 11:23:28.644 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-04-15 11:23:29.544 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-15 11:23:29.545 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-15 11:23:29.614 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 59 ms. Found 0 Redis repository interfaces.
2025-04-15 11:23:30.209 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-04-15 11:23:30.216 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-04-15 11:23:30.216 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-15 11:23:30.216 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-15 11:23:30.368 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-15 11:23:30.369 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1684 ms
2025-04-15 11:23:30.440 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-15 11:23:30.484 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-15 11:23:30.660 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-15 11:23:31.355 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-15 11:23:31.662 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-15 11:23:31.662 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-15 11:23:32.706 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-04-15 11:23:32.805 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-15 11:23:32.828 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-15 11:23:32.834 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-15 11:23:32.989 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-15 11:23:33.260 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-04-15 11:23:33.267 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-04-15 11:23:33.347 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.067 seconds (JVM running for 6.051)
2025-04-15 11:30:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 11:15:00,011 to 2025-04-15 11:30:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 11:30:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 11:23:32,988 to 2025-04-15 11:30:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.651 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 11:30:00,003 to 2025-04-15 13:30:43,651
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.651 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,651 to 2025-04-15 13:30:43,651
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.651 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,651 to 2025-04-15 13:30:43,651
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.653 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,651 to 2025-04-15 13:30:43,653
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.653 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,653 to 2025-04-15 13:30:43,653
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.653 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,653 to 2025-04-15 13:30:43,653
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.653 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,653 to 2025-04-15 13:30:43,653
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.653 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,653 to 2025-04-15 13:30:43,653
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.657 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-04-15 13:30:43.667 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 11:30:00,007 to 2025-04-15 13:30:43,667
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.667 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,667 to 2025-04-15 13:30:43,667
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.667 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,667 to 2025-04-15 13:30:43,667
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.667 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,667 to 2025-04-15 13:30:43,667
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.668 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,667 to 2025-04-15 13:30:43,667
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.668 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,667 to 2025-04-15 13:30:43,668
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.668 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,668 to 2025-04-15 13:30:43,668
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:30:43.668 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,668 to 2025-04-15 13:30:43,668
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:45:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,668 to 2025-04-15 13:45:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 13:45:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:30:43,653 to 2025-04-15 13:45:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 14:00:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:45:00,014 to 2025-04-15 14:00:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 14:00:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 13:45:00,014 to 2025-04-15 14:00:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 14:00:00.004 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-04-15 14:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 14:00:00,002 to 2025-04-15 14:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 14:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 14:00:00,002 to 2025-04-15 14:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 14:30:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 14:15:00,007 to 2025-04-15 14:30:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 14:30:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 14:15:00,007 to 2025-04-15 14:30:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 14:45:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 14:30:00,009 to 2025-04-15 14:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 14:45:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 14:30:00,009 to 2025-04-15 14:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 15:00:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 14:45:00,010 to 2025-04-15 15:00:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 15:00:00.005 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-04-15 15:00:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 14:45:00,010 to 2025-04-15 15:00:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 15:15:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 15:00:00,005 to 2025-04-15 15:15:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 15:15:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 15:00:00,005 to 2025-04-15 15:15:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 15:30:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 15:15:00,017 to 2025-04-15 15:30:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 15:30:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 15:15:00,017 to 2025-04-15 15:30:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 15:45:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 15:30:00,011 to 2025-04-15 15:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 15:45:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 15:30:00,011 to 2025-04-15 15:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 16:00:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 15:45:00,015 to 2025-04-15 16:00:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 16:00:00.009 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-04-15 16:00:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 15:45:00,015 to 2025-04-15 16:00:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 16:15:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 16:00:00,009 to 2025-04-15 16:15:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 16:15:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 16:00:00,009 to 2025-04-15 16:15:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 16:30:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 16:15:00,017 to 2025-04-15 16:30:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 16:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 16:15:00,017 to 2025-04-15 16:30:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 16:45:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 16:30:00,012 to 2025-04-15 16:45:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 16:45:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 16:30:00,012 to 2025-04-15 16:45:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 16:58:15.223 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-15 16:58:15.224 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-15 16:58:15.234 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 10 ms
2025-04-15 16:58:17.178 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"2071C0B23C802B495A8D2CD384EA0A53","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1744707497174}
2025-04-15 16:58:17.903 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-04-15 16:58:18.938 INFO  com.ymiots.campusmp.controller.FileController - 文件未找到: D:\upload\face\ae7f7a8f-9c86-454b-992c-c4912c3aa2d1.jpg
2025-04-15 17:00:00.001 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-04-15 17:00:00.003 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 16:45:00,007 to 2025-04-15 17:00:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 17:00:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-15 16:45:00,007 to 2025-04-15 17:00:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-15 17:00:27.639 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-04-15 17:00:27.659 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-04-15 17:00:27.808 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-04-15 17:00:27.809 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-04-15 17:00:27.812 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-04-15 17:00:27.812 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-04-15 21:29:31.083 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 37844 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-15 21:29:31.086 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-04-15 21:29:32.178 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-15 21:29:32.180 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-15 21:29:32.267 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 74 ms. Found 0 Redis repository interfaces.
2025-04-15 21:29:33.012 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-04-15 21:29:33.021 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-04-15 21:29:33.021 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-15 21:29:33.021 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-15 21:29:33.230 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-15 21:29:33.230 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2104 ms
2025-04-15 21:29:33.318 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-15 21:29:33.372 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-15 21:29:39.535 ERROR com.alibaba.druid.pool.DruidDataSource - init datasource error, url: ****************************************************************************************************************
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1643)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1709)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:901)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusmp.CampusMP.main(CampusMP.java:15)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 87 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 93 common frames omitted
2025-04-15 21:29:39.541 ERROR com.alibaba.druid.pool.DruidDataSource - {dataSource-1} init error
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1643)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1709)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:901)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusmp.CampusMP.main(CampusMP.java:15)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 87 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 93 common frames omitted
2025-04-15 21:29:39.542 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-15 21:29:39.546 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'permissionAspect': Unsatisfied dependency expressed through field 'userredis'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userRedis': Unsatisfied dependency expressed through field 'dbService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
2025-04-15 21:29:39.547 INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-04-15 21:29:39.556 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-04-15 21:29:39.597 ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'permissionAspect': Unsatisfied dependency expressed through field 'userredis'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userRedis': Unsatisfied dependency expressed through field 'dbService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusmp.CampusMP.main(CampusMP.java:15)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userRedis': Unsatisfied dependency expressed through field 'dbService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 34 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 63 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1643)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1709)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:901)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 74 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 87 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 93 common frames omitted
