2025-07-19 08:57:25.979 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 29396 (D:\yunmai\campusos5.0\campusos\target\classes started by <PERSON><PERSON> in D:\yunmai\campusos5.0)
2025-07-19 08:57:25.982 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 08:57:25.982 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 08:57:28.204 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 08:57:28.207 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 08:57:28.367 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 145 ms. Found 0 Redis repository interfaces.
2025-07-19 08:57:28.807 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$fd05e1d4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 08:57:28.891 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7c0c274c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 08:57:29.173 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 08:57:29.184 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 08:57:29.185 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 08:57:29.185 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 08:57:29.397 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 08:57:29.397 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3315 ms
2025-07-19 08:57:29.512 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 08:57:29.577 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 08:57:31.429 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 08:57:31.890 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 08:57:34.287 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 08:57:34.444 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 08:57:34.444 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 08:57:35.596 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 29 个待执行的重试任务
2025-07-19 08:57:35.861 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 29 个待执行的重试任务
2025-07-19 08:57:35.861 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 08:57:36.517 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 08:57:36.763 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 08:57:36.798 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 08:57:36.811 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 08:57:36.949 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 08:57:37.552 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 08:57:37.561 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 08:57:37.795 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$96a018a1 - 计划任务最多执行：66个
2025-07-19 08:57:37.878 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 08:57:37.883 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 08:57:37.883 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 08:57:37.886 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 08:57:37.888 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 08:57:37.888 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 08:57:37.892 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 08:57:37.895 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 08:57:37.897 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 08:57:37.899 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 08:57:37.902 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 08:57:37.905 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 08:57:37.908 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 08:57:37.911 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 08:57:37.913 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 08:57:37.916 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 08:57:37.919 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 08:57:37.919 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 08:57:37.922 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 08:57:37.964 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 12.768 seconds (JVM running for 14.519)
2025-07-19 08:57:38.387 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 09:00:00.010 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-19 09:00:00.025 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 08:57:36,947 to 2025-07-19 09:00:00,018
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 09:00:00.045 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-19 09:00:00.067 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-19 09:00:00.067 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-19 09:00:00.160 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 09:00:00.179 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 09:00:00.198 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 09:00:00.219 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 09:00:00.219 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 09:03:55.619 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 09:03:55.619 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 09:03:55.621 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-19 09:03:57.006 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 09:04:23.629 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 开始执行设备费用计算定时任务...
2025-07-19 09:04:30.868 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 开始检查设备异常...
2025-07-19 09:04:30.875 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始异步同步所有设备数据...
2025-07-19 09:04:30.876 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步 200 台设备，使用并发处理
2025-07-19 09:04:30.876 INFO  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 开始自动检查设备异常...
2025-07-19 09:04:30.937 ERROR c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202106074723 新异常: 设备离线 - 设备超过24小时无数据上报
2025-07-19 09:04:30.941 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 energy000003 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:30.941 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 energy000003 无匹配采集器，已记录异常
2025-07-19 09:04:30.976 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303002347 异常更新: 设备离线 - 设备超过24小时无数据上报 (第27次)
2025-07-19 09:04:30.979 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303000199 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:30.979 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202303000199 无匹配采集器，已记录异常
2025-07-19 09:04:31.012 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595000018 异常更新: 设备离线 - 设备超过24小时无数据上报 (第27次)
2025-07-19 09:04:31.017 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 电表虚拟钱包-1号楼201 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.017 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 电表虚拟钱包-1号楼201 无匹配采集器，已记录异常
2025-07-19 09:04:31.050 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595000044 异常更新: 设备离线 - 设备超过24小时无数据上报 (第27次)
2025-07-19 09:04:31.054 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304006686 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.054 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304006686 无匹配采集器，已记录异常
2025-07-19 09:04:31.085 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595200173 异常更新: 设备离线 - 设备超过24小时无数据上报 (第27次)
2025-07-19 09:04:31.092 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 ceshirqb2111 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.092 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 ceshirqb2111 无匹配采集器，已记录异常
2025-07-19 09:04:31.124 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 ************ 异常更新: 设备离线 - 设备超过24小时无数据上报 (第27次)
2025-07-19 09:04:31.127 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 xu202203030523 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.127 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 xu202203030523 无匹配采集器，已记录异常
2025-07-19 09:04:31.160 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 cs0000000085 异常更新: 设备离线 - 设备超过24小时无数据上报 (第7次)
2025-07-19 09:04:31.165 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00221200002234 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.165 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00221200002234 无匹配采集器，已记录异常
2025-07-19 09:04:31.178 INFO  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备异常检查完成，发现离线设备: 7个
2025-07-19 09:04:31.178 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 设备异常检查完成
2025-07-19 09:04:31.178 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 设备费用计算定时任务执行完成
2025-07-19 09:04:31.198 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 209901130001 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.198 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 209901130001 无匹配采集器，已记录异常
2025-07-19 09:04:31.228 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202505130002 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 09:04:31.230 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202505130002 无匹配采集器，已记录异常
2025-07-19 09:04:31.268 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040204 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.268 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 992308040204 无匹配采集器，已记录异常
2025-07-19 09:04:31.301 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040102 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.301 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 992308040102 无匹配采集器，已记录异常
2025-07-19 09:04:31.334 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 852403159209 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.334 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 852403159209 无匹配采集器，已记录异常
2025-07-19 09:04:31.364 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 82522502270001 异常更新: 无数据返回 - 无匹配采集器 (第47次)
2025-07-19 09:04:31.364 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 82522502270001 无匹配采集器，已记录异常
2025-07-19 09:04:31.396 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076009 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 09:04:31.396 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 792308076009 无匹配采集器，已记录异常
2025-07-19 09:04:31.428 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792305105021 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.428 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 792305105021 无匹配采集器，已记录异常
2025-07-19 09:04:31.460 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140042 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 09:04:31.460 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140042 无匹配采集器，已记录异常
2025-07-19 09:04:31.491 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140029 异常更新: 无数据返回 - 无匹配采集器 (第25次)
2025-07-19 09:04:31.491 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140029 无匹配采集器，已记录异常
2025-07-19 09:04:31.523 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 5903010200107708 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.523 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 5903010200107708 无匹配采集器，已记录异常
2025-07-19 09:04:31.553 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 5903010200107706 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.553 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 5903010200107706 无匹配采集器，已记录异常
2025-07-19 09:04:31.585 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 300012121212 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.585 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 300012121212 无匹配采集器，已记录异常
2025-07-19 09:04:31.618 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 2522401120001 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.618 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 2522401120001 无匹配采集器，已记录异常
2025-07-19 09:04:31.649 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 2522310230008 异常更新: 无数据返回 - 无匹配采集器 (第47次)
2025-07-19 09:04:31.649 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 2522310230008 无匹配采集器，已记录异常
2025-07-19 09:04:31.679 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230329550888 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.680 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 230329550888 无匹配采集器，已记录异常
2025-07-19 09:04:31.711 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 223211202104187485 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.711 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 223211202104187485 无匹配采集器，已记录异常
2025-07-19 09:04:31.741 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 222911040989 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.741 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 222911040989 无匹配采集器，已记录异常
2025-07-19 09:04:31.775 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220000003314 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.775 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 220000003314 无匹配采集器，已记录异常
2025-07-19 09:04:31.805 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202505130001 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.805 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202505130001 无匹配采集器，已记录异常
2025-07-19 09:04:31.835 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308090001 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.835 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202308090001 无匹配采集器，已记录异常
2025-07-19 09:04:31.870 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308040001 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.870 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202308040001 无匹配采集器，已记录异常
2025-07-19 09:04:31.901 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001-2 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.901 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001-2 无匹配采集器，已记录异常
2025-07-19 09:04:31.931 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001-1 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.931 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001-1 无匹配采集器，已记录异常
2025-07-19 09:04:31.963 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.964 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001 无匹配采集器，已记录异常
2025-07-19 09:04:31.995 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209230002 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:31.995 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202209230002 无匹配采集器，已记录异常
2025-07-19 09:04:32.026 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202149 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.026 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202205202149 无匹配采集器，已记录异常
2025-07-19 09:04:32.055 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203034522 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.056 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202203034522 无匹配采集器，已记录异常
2025-07-19 09:04:32.086 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202201210001 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.086 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202201210001 无匹配采集器，已记录异常
2025-07-19 09:04:32.117 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202111290047 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.117 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202111290047 无匹配采集器，已记录异常
2025-07-19 09:04:32.149 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202105080077 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.149 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202105080077 无匹配采集器，已记录异常
2025-07-19 09:04:32.180 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187885 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.181 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187885 无匹配采集器，已记录异常
2025-07-19 09:04:32.212 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187883 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.212 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187883 无匹配采集器，已记录异常
2025-07-19 09:04:32.242 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187880 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.242 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187880 无匹配采集器，已记录异常
2025-07-19 09:04:32.274 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187484 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.275 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187484 无匹配采集器，已记录异常
2025-07-19 09:04:32.309 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 151307080273 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.309 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 151307080273 无匹配采集器，已记录异常
2025-07-19 09:04:32.340 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134043003860 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.340 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 134043003860 无匹配采集器，已记录异常
2025-07-19 09:04:32.373 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 126202104187485 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.373 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 126202104187485 无匹配采集器，已记录异常
2025-07-19 09:04:32.418 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 125202104187485 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.418 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 125202104187485 无匹配采集器，已记录异常
2025-07-19 09:04:32.449 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 124202303150080 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.449 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 124202303150080 无匹配采集器，已记录异常
2025-07-19 09:04:32.479 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 124202104187485 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.479 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 124202104187485 无匹配采集器，已记录异常
2025-07-19 09:04:32.510 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 12306126470003 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.510 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 12306126470003 无匹配采集器，已记录异常
2025-07-19 09:04:32.541 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 112244001089 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.541 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 112244001089 无匹配采集器，已记录异常
2025-07-19 09:04:32.572 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111800135297 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.572 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111800135297 无匹配采集器，已记录异常
2025-07-19 09:04:32.603 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111555447845 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.603 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111555447845 无匹配采集器，已记录异常
2025-07-19 09:04:32.633 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111222333444 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.633 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111222333444 无匹配采集器，已记录异常
2025-07-19 09:04:32.664 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 1111202104187485 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.664 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 1111202104187485 无匹配采集器，已记录异常
2025-07-19 09:04:32.695 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 057701600670 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.695 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 057701600670 无匹配采集器，已记录异常
2025-07-19 09:04:32.727 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 057701600542 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.727 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 057701600542 无匹配采集器，已记录异常
2025-07-19 09:04:32.758 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 0269210625867454 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.758 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 0269210625867454 无匹配采集器，已记录异常
2025-07-19 09:04:32.787 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 020230306006 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.787 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 020230306006 无匹配采集器，已记录异常
2025-07-19 09:04:32.817 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 020220221001 异常更新: 无数据返回 - 无匹配采集器 (第43次)
2025-07-19 09:04:32.817 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 020220221001 无匹配采集器，已记录异常
2025-07-19 09:04:32.847 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 010511000000000001 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.847 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 010511000000000001 无匹配采集器，已记录异常
2025-07-19 09:04:32.877 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 002024054390 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.877 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 002024054390 无匹配采集器，已记录异常
2025-07-19 09:04:32.906 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 002023030450 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.906 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 002023030450 无匹配采集器，已记录异常
2025-07-19 09:04:32.935 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00004300 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.935 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00004300 无匹配采集器，已记录异常
2025-07-19 09:04:32.963 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024060353 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.963 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024060353 无匹配采集器，已记录异常
2025-07-19 09:04:32.990 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024056590 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:32.990 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024056590 无匹配采集器，已记录异常
2025-07-19 09:04:33.017 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024054390 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.017 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024054390 无匹配采集器，已记录异常
2025-07-19 09:04:33.045 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000002090406 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.045 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000002090406 无匹配采集器，已记录异常
2025-07-19 09:04:33.074 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000001310a 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.074 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00000001310a 无匹配采集器，已记录异常
2025-07-19 09:04:33.102 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013109 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.102 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013109 无匹配采集器，已记录异常
2025-07-19 09:04:33.129 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013108 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.131 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013108 无匹配采集器，已记录异常
2025-07-19 09:04:33.160 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013107 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.160 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013107 无匹配采集器，已记录异常
2025-07-19 09:04:33.190 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013106 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.190 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013106 无匹配采集器，已记录异常
2025-07-19 09:04:33.218 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013105 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.219 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013105 无匹配采集器，已记录异常
2025-07-19 09:04:33.246 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013104 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.246 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013104 无匹配采集器，已记录异常
2025-07-19 09:04:33.274 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013103 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.274 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013103 无匹配采集器，已记录异常
2025-07-19 09:04:33.306 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013102 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.306 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013102 无匹配采集器，已记录异常
2025-07-19 09:04:33.336 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013101 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.336 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013101 无匹配采集器，已记录异常
2025-07-19 09:04:33.364 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013100 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.364 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013100 无匹配采集器，已记录异常
2025-07-19 09:04:33.392 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000007480 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.392 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000007480 无匹配采集器，已记录异常
2025-07-19 09:04:33.420 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000004276 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.420 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000004276 无匹配采集器，已记录异常
2025-07-19 09:04:33.447 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000000002185 异常更新: 无数据返回 - 无匹配采集器 (第45次)
2025-07-19 09:04:33.447 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00000000002185 无匹配采集器，已记录异常
2025-07-19 09:04:33.447 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始批量查询采集器状态，共 99 个采集器
2025-07-19 09:04:33.802 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 501107411 状态异常
2025-07-19 09:04:33.849 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202412130001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:34.233 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030501348 状态异常
2025-07-19 09:04:34.285 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403050544 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:34.599 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030501349 状态异常
2025-07-19 09:04:34.639 ERROR c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403050545 新异常: 无数据返回 - 采集器状态异常
2025-07-19 09:04:35.036 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 106210042 状态异常
2025-07-19 09:04:35.073 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202106210042 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:35.353 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058611356 状态异常
2025-07-19 09:04:35.386 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:35.752 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 896805845 状态异常
2025-07-19 09:04:35.785 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992311211111 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:36.055 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 303150145 状态异常
2025-07-19 09:04:36.087 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303150145 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:36.407 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 120710016 状态异常
2025-07-19 09:04:36.439 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203030518 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:36.792 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 837003149 状态异常
2025-07-19 09:04:36.825 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403130098 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:37.030 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140510110 查询失败或返回数据为空
2025-07-19 09:04:37.061 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111800179430 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:37.356 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100410996 状态异常
2025-07-19 09:04:37.388 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203033638 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:37.686 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 435057026478 状态异常
2025-07-19 09:04:37.718 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202103020884 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:37.894 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 533005777 查询失败或返回数据为空
2025-07-19 09:04:37.925 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 200135671002 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:38.212 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141230401543 状态异常
2025-07-19 09:04:38.245 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141230401543 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:38.437 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 591501904 查询失败或返回数据为空
2025-07-19 09:04:38.470 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 201120200106 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:38.764 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140310116 状态异常
2025-07-19 09:04:38.797 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220800001348 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:39.147 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693050293377 状态异常
2025-07-19 09:04:39.182 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202141 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:39.486 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 047407412 状态异常
2025-07-19 09:04:39.524 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202412130002 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:39.716 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 834305590 查询失败或返回数据为空
2025-07-19 09:04:39.748 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000018365 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:40.109 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 328063829575 状态异常
2025-07-19 09:04:40.146 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:40.445 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307316001 状态异常
2025-07-19 09:04:40.479 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307316001 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:40.784 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 378107760 状态异常
2025-07-19 09:04:40.829 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202409290003 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:41.120 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307316002 状态异常
2025-07-19 09:04:41.153 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307316002 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:41.462 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 627000001 状态异常
2025-07-19 09:04:41.497 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134062700001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:41.799 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 091641060 状态异常
2025-07-19 09:04:41.833 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202309160100 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:42.143 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 010040200890 状态异常
2025-07-19 09:04:42.194 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000001216 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:42.518 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 328067910801 状态异常
2025-07-19 09:04:42.570 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 121520200076 异常更新: 无数据返回 - 采集器状态异常 (第40次)
2025-07-19 09:04:42.865 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202308180001 状态异常
2025-07-19 09:04:42.915 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308180001 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:43.207 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 649905588 状态异常
2025-07-19 09:04:43.239 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202312096666 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:43.505 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053294658 状态异常
2025-07-19 09:04:43.539 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220800001308 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:43.571 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203033849 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:43.603 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 20212205005384 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:44.062 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204120218 状态异常
2025-07-19 09:04:44.095 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204120218 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:44.281 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053330486 查询失败或返回数据为空
2025-07-19 09:04:44.316 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 240010003355 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:44.623 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 24060702830001 状态异常
2025-07-19 09:04:44.661 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 24060702830001 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:44.949 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 150921012 状态异常
2025-07-19 09:04:44.981 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 energy000001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:45.306 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 435057022691 状态异常
2025-07-19 09:04:45.340 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000941155 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:45.655 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 966075975598 状态异常
2025-07-19 09:04:45.688 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 244717020004 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:46.071 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058621843 状态异常
2025-07-19 09:04:46.103 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792304250024 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:46.410 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 230732020001 状态异常
2025-07-19 09:04:46.443 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230732020001 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:46.738 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 865068813782 状态异常
2025-07-19 09:04:46.780 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 123455555555 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:47.058 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058634622 状态异常
2025-07-19 09:04:47.090 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308192129 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:47.369 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 112060062 状态异常
2025-07-19 09:04:47.402 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202112060062 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:47.686 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 88888888888888 状态异常
2025-07-19 09:04:47.718 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 88888888888888 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:47.993 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 222811190004 状态异常
2025-07-19 09:04:48.029 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 222811190004 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:48.361 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 938905851 状态异常
2025-07-19 09:04:48.394 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308026601 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:48.704 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 010042970 状态异常
2025-07-19 09:04:48.741 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220722010076 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:49.010 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058615597 查询失败或返回数据为空
2025-07-19 09:04:49.048 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792304250007 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:49.328 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202208040019 状态异常
2025-07-19 09:04:49.363 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202208040019 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:49.659 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 528442169 状态异常
2025-07-19 09:04:49.703 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202305290001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:49.737 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 076052841209 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:50.435 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000003 状态异常
2025-07-19 09:04:50.469 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300003 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:50.817 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000004 状态异常
2025-07-19 09:04:50.861 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300004 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:51.158 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141304067677 状态异常
2025-07-19 09:04:51.193 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141304067677 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:51.538 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000001 状态异常
2025-07-19 09:04:51.593 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:51.940 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141304067674 状态异常
2025-07-19 09:04:51.990 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141304067674 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:52.292 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058627469 状态异常
2025-07-19 09:04:52.323 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076002 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:52.576 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307070001 状态异常
2025-07-19 09:04:52.610 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307070001 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:52.795 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 020000002BB5 查询失败或返回数据为空
2025-07-19 09:04:52.826 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187485 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:52.856 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 FFFFFFFFFFFF 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:52.887 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 905802002779 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:52.921 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 882308026005 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:53.250 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 821000002 状态异常
2025-07-19 09:04:53.283 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134082100002 异常更新: 无数据返回 - 采集器状态异常 (第40次)
2025-07-19 09:04:53.559 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204140050 状态异常
2025-07-19 09:04:53.592 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204140050 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:53.901 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 102004098 状态异常
2025-07-19 09:04:53.938 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202410201002 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:54.251 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202150 状态异常
2025-07-19 09:04:54.283 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202150 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:54.589 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289C6E307081 状态异常
2025-07-19 09:04:54.622 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 YG000000010765 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:54.960 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 102004097 状态异常
2025-07-19 09:04:54.992 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202410201001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:55.294 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693052804874 状态异常
2025-07-19 09:04:55.326 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230300023684 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:55.598 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 860610052773970 状态异常
2025-07-19 09:04:55.627 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 10304104045111 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:55.886 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100009001 状态异常
2025-07-19 09:04:55.918 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040205 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:55.979 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040202 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:56.016 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308020102 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:56.298 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 020000002BCC 状态异常
2025-07-19 09:04:56.331 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 190331527749 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:56.628 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058611935 状态异常
2025-07-19 09:04:56.660 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792305105020 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:56.942 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204140044 状态异常
2025-07-19 09:04:56.973 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204140044 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:57.261 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 757105587 状态异常
2025-07-19 09:04:57.300 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202312116666 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:57.723 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202303170000 状态异常
2025-07-19 09:04:57.755 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303170000 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:58.023 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202146 状态异常
2025-07-19 09:04:58.055 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202146 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:58.364 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202147 状态异常
2025-07-19 09:04:58.397 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202147 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:04:58.572 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 02010000482F 查询失败或返回数据为空
2025-07-19 09:04:58.603 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 011918223316201947 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:04:58.945 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 040900103 状态异常
2025-07-19 09:04:58.978 ERROR c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616504090067 新异常: 无数据返回 - 采集器状态异常
2025-07-19 09:04:59.333 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289054758509 状态异常
2025-07-19 09:04:59.380 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202211010771 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:59.430 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130030 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:59.474 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130029 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:59.519 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130026 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:04:59.918 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 896705587 状态异常
2025-07-19 09:04:59.950 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000020108 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:00.142 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 048071983819 查询失败或返回数据为空
2025-07-19 09:05:00.175 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202502170001 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:05:00.206 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202502240001 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:05:00.539 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030506179 状态异常
2025-07-19 09:05:00.571 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403051823 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:00.905 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 240100001 状态异常
2025-07-19 09:05:00.940 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000024010001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:01.347 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058607636 状态异常
2025-07-19 09:05:01.379 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170002 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:01.722 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141240410174 状态异常
2025-07-19 09:05:01.756 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141240410174 异常更新: 无数据返回 - 采集器状态异常 (第45次)
2025-07-19 09:05:02.175 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289054796392 状态异常
2025-07-19 09:05:02.208 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308116666 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:02.241 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308099999 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:02.275 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782306220001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:02.316 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 011509134421 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:02.644 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 071404752 状态异常
2025-07-19 09:05:02.676 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 148307141290 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:02.976 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058606828 状态异常
2025-07-19 09:05:03.008 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076018 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:03.390 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140310121 状态异常
2025-07-19 09:05:03.434 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187886 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:03.469 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202008300467 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:03.778 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053321832 状态异常
2025-07-19 09:05:03.812 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000010141802 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:03.846 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000010141801 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:04.022 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 180000336 查询失败或返回数据为空
2025-07-19 09:05:04.058 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 250118000150 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:05:04.090 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000018000150 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:05:04.409 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058610994 状态异常
2025-07-19 09:05:04.447 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140003 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:04.750 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058622692 状态异常
2025-07-19 09:05:04.782 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170003 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:05.091 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 210600057 状态异常
2025-07-19 09:05:05.124 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 152000048836 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:05.483 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030800003 状态异常
2025-07-19 09:05:05.521 ERROR c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616503080003 新异常: 无数据返回 - 采集器状态异常
2025-07-19 09:05:05.834 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058615803 状态异常
2025-07-19 09:05:05.868 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792408100001 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:06.210 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 712000002 状态异常
2025-07-19 09:05:06.244 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071200002 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:06.427 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100410103 查询失败或返回数据为空
2025-07-19 09:05:06.461 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000019041782 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:05:06.490 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000010900020 异常更新: 无数据返回 - 采集器不存在或未匹配 (第42次)
2025-07-19 09:05:06.891 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 060718211 状态异常
2025-07-19 09:05:06.940 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202106074723 异常更新: 无数据返回 - 采集器状态异常 (第8次)
2025-07-19 09:05:07.233 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693052805228 状态异常
2025-07-19 09:05:07.282 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202109171124 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:07.798 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 180000337 状态异常
2025-07-19 09:05:07.849 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000018000151 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:08.188 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 112060025 状态异常
2025-07-19 09:05:08.263 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202112060025 异常更新: 无数据返回 - 采集器状态异常 (第42次)
2025-07-19 09:05:08.268 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器状态查询完成 - 总采集器: 99, 有效采集器: 1, 参与同步设备: 2
2025-07-19 09:05:08.268 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组 482067317475: 2 台设备
2025-07-19 09:05:08.269 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 按采集器分组完成，共 1 个采集器组，参与同步设备: 2台，无采集器设备: 198台
2025-07-19 09:05:08.272 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始串行处理采集器组，设备数量: 2
2025-07-19 09:05:08.273 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 cs0000000085
2025-07-19 09:05:08.541 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 cs0000000085 异常更新: API调用错误 - 同步设备读数失败, 请过几分钟再试 (第15次)
2025-07-19 09:05:08.549 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 同步失败: 同步设备读数失败, 请过几分钟再试
2025-07-19 09:05:08.549 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 将在 180 秒后重试，原因: 同步设备读数失败, 请过几分钟再试
2025-07-19 09:05:08.549 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 同步失败，添加到持久化重试队列，延迟 180 秒后重试
2025-07-19 09:05:08.604 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 延迟重试任务已添加 - 设备: cs0000000085, 延迟: 3分钟, 执行时间: Sat Jul 19 09:08:08 CST 2025
2025-07-19 09:05:08.605 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 已添加到持久化重试队列，重试类型: 延迟重试
2025-07-19 09:05:08.844 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 cs0000000101
2025-07-19 09:05:09.148 INFO  c.y.c.service.waterctrl.EquipmentReadingService - 设备读数记录保存成功: cs0000000101
2025-07-19 09:05:09.168 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组处理完成，设备数量: 2
2025-07-19 09:05:09.185 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备数据异步同步完成 - 总计:200, 成功:1, 失败:199(含无采集器:198), 重试:0
2025-07-19 09:05:09.263 INFO  c.y.campusos.service.waterctrl.SystemConfigService - 配置缓存刷新完成，共加载 58 个配置项
2025-07-19 09:05:09.264 INFO  c.y.c.service.waterctrl.EquipmentCostService - 从缓存获取分时电价 - 设备: cs0000000101, 尖: 1, 峰: 1, 平: 1, 谷: 1
2025-07-19 09:05:09.280 INFO  c.y.c.service.waterctrl.EquipmentCostService - 分时费用计算完成 - 设备: cs0000000101, 尖: 0×1=￥0.00, 峰: 0×1=￥0.00, 平: 0×1=￥0.00, 谷: 0×1=￥0.00, 总计: ￥0.00
2025-07-19 09:05:19.201 INFO  c.y.c.service.waterctrl.EquipmentCostService - 费用计算记录保存成功: cs0000000101
2025-07-19 09:07:03.936 INFO  c.y.campusos.controller.hotel.WECostController - 生成打印内容 - 日期: 2025-07-19至2025-07-19, 区域: 
2025-07-19 09:07:03.939 INFO  c.y.campusos.service.waterctrl.WECostExportService - 生成打印内容 - 日期: 2025-07-19至2025-07-19, 区域: 
2025-07-19 09:07:03.939 INFO  c.y.campusos.service.waterctrl.WECostExportService - 执行查询SQL: SELECT * FROM tb_equipment_reading_records r join tb_equipment_cost_calculation c on r.uid = c.reading_record_id WHERE r.status = 1 AND DATE(r.reading_date) >= '2025-07-19' AND DATE(r.reading_date) <= '2025-07-19' ORDER BY r.area_code, r.equipment_code, r.reading_date
2025-07-19 09:07:22.082 INFO  c.y.campusos.controller.hotel.WECostController - 开始导出Excel账单 - 日期: 2025-07-19至2025-07-19, 区域: 
2025-07-19 09:07:22.082 INFO  c.y.campusos.service.waterctrl.WECostExportService - 开始导出Excel账单 - 日期: 2025-07-19至2025-07-19, 区域: 
2025-07-19 09:07:22.084 INFO  c.y.campusos.service.waterctrl.WECostExportService - 执行查询SQL: SELECT * FROM tb_equipment_reading_records r join tb_equipment_cost_calculation c on r.uid = c.reading_record_id WHERE r.status = 1 AND DATE(r.reading_date) >= '2025-07-19' AND DATE(r.reading_date) <= '2025-07-19' ORDER BY r.area_code, r.equipment_code, r.reading_date
2025-07-19 09:07:23.605 INFO  c.y.campusos.service.waterctrl.WECostExportService - Excel账单导出成功 - 文件: 水电费账单20250719090722.xlsx, 记录数: 1
2025-07-19 09:07:23.606 INFO  c.y.campusos.controller.hotel.WECostController - Excel账单导出成功
2025-07-19 09:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 09:00:00,018 to 2025-07-19 09:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 09:20:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 09:20:00.038 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 09:20:00.065 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 09:20:00.092 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 09:20:00.092 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 09:30:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 09:15:00,015 to 2025-07-19 09:30:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 09:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 09:40:00.032 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 09:40:00.049 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 09:40:00.065 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 09:40:00.066 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 09:45:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 09:30:00,005 to 2025-07-19 09:45:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 10:00:00.003 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 09:45:00,013 to 2025-07-19 10:00:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 10:00:00.007 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-19 10:00:00.028 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-19 10:00:00.046 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-19 10:00:00.046 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-19 10:00:00.110 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 10:00:00.128 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 10:00:00.145 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 10:00:00.162 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 10:00:00.162 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 10:15:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 10:00:00,003 to 2025-07-19 10:15:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 10:20:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 10:20:00.042 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 10:20:00.069 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 10:20:00.087 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 10:20:00.087 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 10:23:06.846 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 开始执行设备费用计算定时任务...
2025-07-19 10:23:13.685 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 开始检查设备异常...
2025-07-19 10:23:13.686 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始异步同步所有设备数据...
2025-07-19 10:23:13.686 INFO  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 开始自动检查设备异常...
2025-07-19 10:23:13.686 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步 200 台设备，使用并发处理
2025-07-19 10:23:13.782 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 energy000003 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:13.782 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202106074723 异常更新: 设备离线 - 设备超过24小时无数据上报 (第2次)
2025-07-19 10:23:13.782 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 energy000003 无匹配采集器，已记录异常
2025-07-19 10:23:13.822 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303002347 异常更新: 设备离线 - 设备超过24小时无数据上报 (第28次)
2025-07-19 10:23:13.822 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303000199 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:13.823 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202303000199 无匹配采集器，已记录异常
2025-07-19 10:23:13.860 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595000018 异常更新: 设备离线 - 设备超过24小时无数据上报 (第28次)
2025-07-19 10:23:13.862 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 电表虚拟钱包-1号楼201 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:13.862 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 电表虚拟钱包-1号楼201 无匹配采集器，已记录异常
2025-07-19 10:23:13.897 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595000044 异常更新: 设备离线 - 设备超过24小时无数据上报 (第28次)
2025-07-19 10:23:13.903 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304006686 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:13.903 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304006686 无匹配采集器，已记录异常
2025-07-19 10:23:13.934 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 697595200173 异常更新: 设备离线 - 设备超过24小时无数据上报 (第28次)
2025-07-19 10:23:13.945 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 ceshirqb2111 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:13.945 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 ceshirqb2111 无匹配采集器，已记录异常
2025-07-19 10:23:13.981 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 ************ 异常更新: 设备离线 - 设备超过24小时无数据上报 (第28次)
2025-07-19 10:23:13.995 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 xu202203030523 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:13.995 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 xu202203030523 无匹配采集器，已记录异常
2025-07-19 10:23:14.031 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 cs0000000085 异常更新: 设备离线 - 设备超过24小时无数据上报 (第8次)
2025-07-19 10:23:14.037 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00221200002234 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.037 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00221200002234 无匹配采集器，已记录异常
2025-07-19 10:23:14.049 INFO  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备异常检查完成，发现离线设备: 7个
2025-07-19 10:23:14.049 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 设备异常检查完成
2025-07-19 10:23:14.049 INFO  c.y.campusos.task.EquipmentCostCalculationTask - 设备费用计算定时任务执行完成
2025-07-19 10:23:14.067 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 209901130001 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.067 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 209901130001 无匹配采集器，已记录异常
2025-07-19 10:23:14.096 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202505130002 异常更新: 无数据返回 - 无匹配采集器 (第47次)
2025-07-19 10:23:14.096 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202505130002 无匹配采集器，已记录异常
2025-07-19 10:23:14.126 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040204 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.126 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 992308040204 无匹配采集器，已记录异常
2025-07-19 10:23:14.154 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040102 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.154 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 992308040102 无匹配采集器，已记录异常
2025-07-19 10:23:14.182 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 852403159209 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.182 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 852403159209 无匹配采集器，已记录异常
2025-07-19 10:23:14.210 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 82522502270001 异常更新: 无数据返回 - 无匹配采集器 (第48次)
2025-07-19 10:23:14.211 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 82522502270001 无匹配采集器，已记录异常
2025-07-19 10:23:14.239 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076009 异常更新: 无数据返回 - 无匹配采集器 (第47次)
2025-07-19 10:23:14.239 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 792308076009 无匹配采集器，已记录异常
2025-07-19 10:23:14.267 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792305105021 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.267 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 792305105021 无匹配采集器，已记录异常
2025-07-19 10:23:14.294 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140042 异常更新: 无数据返回 - 无匹配采集器 (第47次)
2025-07-19 10:23:14.294 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140042 无匹配采集器，已记录异常
2025-07-19 10:23:14.322 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140029 异常更新: 无数据返回 - 无匹配采集器 (第26次)
2025-07-19 10:23:14.322 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 616408140029 无匹配采集器，已记录异常
2025-07-19 10:23:14.349 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 5903010200107708 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.349 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 5903010200107708 无匹配采集器，已记录异常
2025-07-19 10:23:14.376 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 5903010200107706 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.376 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 5903010200107706 无匹配采集器，已记录异常
2025-07-19 10:23:14.403 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 300012121212 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.404 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 300012121212 无匹配采集器，已记录异常
2025-07-19 10:23:14.434 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 2522401120001 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.434 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 2522401120001 无匹配采集器，已记录异常
2025-07-19 10:23:14.466 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 2522310230008 异常更新: 无数据返回 - 无匹配采集器 (第48次)
2025-07-19 10:23:14.466 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 2522310230008 无匹配采集器，已记录异常
2025-07-19 10:23:14.494 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230329550888 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.494 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 230329550888 无匹配采集器，已记录异常
2025-07-19 10:23:14.524 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 223211202104187485 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.524 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 223211202104187485 无匹配采集器，已记录异常
2025-07-19 10:23:14.558 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 222911040989 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.558 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 222911040989 无匹配采集器，已记录异常
2025-07-19 10:23:14.594 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220000003314 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.594 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 220000003314 无匹配采集器，已记录异常
2025-07-19 10:23:14.628 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202505130001 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.629 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202505130001 无匹配采集器，已记录异常
2025-07-19 10:23:14.661 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308090001 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.661 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202308090001 无匹配采集器，已记录异常
2025-07-19 10:23:14.696 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308040001 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.696 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202308040001 无匹配采集器，已记录异常
2025-07-19 10:23:14.727 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001-2 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.728 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001-2 无匹配采集器，已记录异常
2025-07-19 10:23:14.760 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001-1 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.760 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001-1 无匹配采集器，已记录异常
2025-07-19 10:23:14.793 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202304060001 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.793 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202304060001 无匹配采集器，已记录异常
2025-07-19 10:23:14.825 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209230002 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.825 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202209230002 无匹配采集器，已记录异常
2025-07-19 10:23:14.856 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202149 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.857 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202205202149 无匹配采集器，已记录异常
2025-07-19 10:23:14.892 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203034522 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.892 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202203034522 无匹配采集器，已记录异常
2025-07-19 10:23:14.924 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202201210001 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.924 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202201210001 无匹配采集器，已记录异常
2025-07-19 10:23:14.961 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202111290047 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.962 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202111290047 无匹配采集器，已记录异常
2025-07-19 10:23:14.994 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202105080077 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:14.994 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202105080077 无匹配采集器，已记录异常
2025-07-19 10:23:15.027 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187885 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.027 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187885 无匹配采集器，已记录异常
2025-07-19 10:23:15.060 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187883 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.060 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187883 无匹配采集器，已记录异常
2025-07-19 10:23:15.092 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187880 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.092 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187880 无匹配采集器，已记录异常
2025-07-19 10:23:15.124 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187484 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.124 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 202104187484 无匹配采集器，已记录异常
2025-07-19 10:23:15.157 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 151307080273 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.157 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 151307080273 无匹配采集器，已记录异常
2025-07-19 10:23:15.189 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134043003860 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.189 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 134043003860 无匹配采集器，已记录异常
2025-07-19 10:23:15.222 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 126202104187485 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.223 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 126202104187485 无匹配采集器，已记录异常
2025-07-19 10:23:15.255 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 125202104187485 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.255 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 125202104187485 无匹配采集器，已记录异常
2025-07-19 10:23:15.289 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 124202303150080 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.289 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 124202303150080 无匹配采集器，已记录异常
2025-07-19 10:23:15.320 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 124202104187485 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.320 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 124202104187485 无匹配采集器，已记录异常
2025-07-19 10:23:15.352 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 12306126470003 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.352 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 12306126470003 无匹配采集器，已记录异常
2025-07-19 10:23:15.386 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 112244001089 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.386 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 112244001089 无匹配采集器，已记录异常
2025-07-19 10:23:15.418 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111800135297 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.418 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111800135297 无匹配采集器，已记录异常
2025-07-19 10:23:15.454 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111555447845 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.454 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111555447845 无匹配采集器，已记录异常
2025-07-19 10:23:15.485 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111222333444 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.485 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 111222333444 无匹配采集器，已记录异常
2025-07-19 10:23:15.521 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 1111202104187485 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.521 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 1111202104187485 无匹配采集器，已记录异常
2025-07-19 10:23:15.552 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 057701600670 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.553 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 057701600670 无匹配采集器，已记录异常
2025-07-19 10:23:15.586 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 057701600542 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.586 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 057701600542 无匹配采集器，已记录异常
2025-07-19 10:23:15.621 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 0269210625867454 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.621 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 0269210625867454 无匹配采集器，已记录异常
2025-07-19 10:23:15.654 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 020230306006 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.654 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 020230306006 无匹配采集器，已记录异常
2025-07-19 10:23:15.685 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 020220221001 异常更新: 无数据返回 - 无匹配采集器 (第44次)
2025-07-19 10:23:15.685 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 020220221001 无匹配采集器，已记录异常
2025-07-19 10:23:15.718 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 010511000000000001 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.718 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 010511000000000001 无匹配采集器，已记录异常
2025-07-19 10:23:15.750 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 002024054390 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.750 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 002024054390 无匹配采集器，已记录异常
2025-07-19 10:23:15.781 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 002023030450 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.782 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 002023030450 无匹配采集器，已记录异常
2025-07-19 10:23:15.817 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00004300 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.817 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00004300 无匹配采集器，已记录异常
2025-07-19 10:23:15.850 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024060353 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.850 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024060353 无匹配采集器，已记录异常
2025-07-19 10:23:15.880 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024056590 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.880 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024056590 无匹配采集器，已记录异常
2025-07-19 10:23:15.922 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00002024054390 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.922 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00002024054390 无匹配采集器，已记录异常
2025-07-19 10:23:15.956 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000002090406 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.956 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000002090406 无匹配采集器，已记录异常
2025-07-19 10:23:15.987 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000001310a 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:15.987 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00000001310a 无匹配采集器，已记录异常
2025-07-19 10:23:16.020 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013109 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.020 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013109 无匹配采集器，已记录异常
2025-07-19 10:23:16.057 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013108 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.057 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013108 无匹配采集器，已记录异常
2025-07-19 10:23:16.091 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013107 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.092 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013107 无匹配采集器，已记录异常
2025-07-19 10:23:16.129 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013106 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.129 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013106 无匹配采集器，已记录异常
2025-07-19 10:23:16.163 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013105 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.163 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013105 无匹配采集器，已记录异常
2025-07-19 10:23:16.193 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013104 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.193 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013104 无匹配采集器，已记录异常
2025-07-19 10:23:16.222 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013103 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.222 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013103 无匹配采集器，已记录异常
2025-07-19 10:23:16.252 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013102 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.252 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013102 无匹配采集器，已记录异常
2025-07-19 10:23:16.289 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013101 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.289 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013101 无匹配采集器，已记录异常
2025-07-19 10:23:16.318 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000013100 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.318 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000013100 无匹配采集器，已记录异常
2025-07-19 10:23:16.350 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000007480 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.351 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000007480 无匹配采集器，已记录异常
2025-07-19 10:23:16.385 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000004276 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.385 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 000000004276 无匹配采集器，已记录异常
2025-07-19 10:23:16.416 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000000002185 异常更新: 无数据返回 - 无匹配采集器 (第46次)
2025-07-19 10:23:16.416 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 00000000002185 无匹配采集器，已记录异常
2025-07-19 10:23:16.416 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始批量查询采集器状态，共 99 个采集器
2025-07-19 10:23:16.815 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 501107411 状态异常
2025-07-19 10:23:16.845 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202412130001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:17.298 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030501348 状态异常
2025-07-19 10:23:17.335 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403050544 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:17.671 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030501349 状态异常
2025-07-19 10:23:17.706 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403050545 异常更新: 无数据返回 - 采集器状态异常 (第2次)
2025-07-19 10:23:18.016 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 106210042 状态异常
2025-07-19 10:23:18.047 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202106210042 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:18.446 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058611356 状态异常
2025-07-19 10:23:18.479 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:18.792 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 896805845 状态异常
2025-07-19 10:23:18.823 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992311211111 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:19.113 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 303150145 状态异常
2025-07-19 10:23:19.144 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303150145 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:19.416 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 120710016 状态异常
2025-07-19 10:23:19.450 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203030518 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:19.871 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 837003149 状态异常
2025-07-19 10:23:19.907 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403130098 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:20.085 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140510110 查询失败或返回数据为空
2025-07-19 10:23:20.118 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 111800179430 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:20.501 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100410996 状态异常
2025-07-19 10:23:20.534 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203033638 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:20.859 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 435057026478 状态异常
2025-07-19 10:23:20.893 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202103020884 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:21.063 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 533005777 查询失败或返回数据为空
2025-07-19 10:23:21.100 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 200135671002 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:21.419 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141230401543 状态异常
2025-07-19 10:23:21.474 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141230401543 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:21.662 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 591501904 查询失败或返回数据为空
2025-07-19 10:23:21.700 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 201120200106 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:21.988 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140310116 状态异常
2025-07-19 10:23:22.023 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220800001348 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:22.442 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693050293377 状态异常
2025-07-19 10:23:22.479 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202141 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:22.796 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 047407412 状态异常
2025-07-19 10:23:22.829 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202412130002 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:23.008 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 834305590 查询失败或返回数据为空
2025-07-19 10:23:23.041 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000018365 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:23.343 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 328063829575 状态异常
2025-07-19 10:23:23.376 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:23.640 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307316001 状态异常
2025-07-19 10:23:23.686 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307316001 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:23.986 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 378107760 状态异常
2025-07-19 10:23:24.027 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202409290003 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:24.307 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307316002 状态异常
2025-07-19 10:23:24.339 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307316002 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:24.715 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 627000001 状态异常
2025-07-19 10:23:24.752 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134062700001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:25.076 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 091641060 状态异常
2025-07-19 10:23:25.111 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202309160100 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:25.448 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 010040200890 状态异常
2025-07-19 10:23:25.482 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000001216 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:25.797 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 328067910801 状态异常
2025-07-19 10:23:25.830 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 121520200076 异常更新: 无数据返回 - 采集器状态异常 (第41次)
2025-07-19 10:23:26.130 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202308180001 状态异常
2025-07-19 10:23:26.164 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202308180001 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:26.503 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 649905588 状态异常
2025-07-19 10:23:26.535 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202312096666 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:26.928 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053294658 状态异常
2025-07-19 10:23:26.959 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220800001308 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:26.990 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202203033849 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:27.042 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 20212205005384 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:27.309 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204120218 状态异常
2025-07-19 10:23:27.342 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204120218 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:27.512 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053330486 查询失败或返回数据为空
2025-07-19 10:23:27.542 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 240010003355 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:27.881 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 24060702830001 状态异常
2025-07-19 10:23:27.916 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 24060702830001 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:28.219 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 150921012 状态异常
2025-07-19 10:23:28.270 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 energy000001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:28.571 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 435057022691 状态异常
2025-07-19 10:23:28.624 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000941155 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:28.936 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 966075975598 状态异常
2025-07-19 10:23:28.978 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 244717020004 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:29.280 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058621843 状态异常
2025-07-19 10:23:29.328 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792304250024 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:29.653 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 230732020001 状态异常
2025-07-19 10:23:29.707 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230732020001 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:30.059 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 865068813782 状态异常
2025-07-19 10:23:30.120 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 123455555555 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:30.406 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058634622 状态异常
2025-07-19 10:23:30.458 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308192129 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:30.750 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 112060062 状态异常
2025-07-19 10:23:30.806 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202112060062 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:31.129 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 88888888888888 状态异常
2025-07-19 10:23:31.170 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 88888888888888 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:31.473 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 222811190004 状态异常
2025-07-19 10:23:31.508 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 222811190004 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:31.758 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 938905851 状态异常
2025-07-19 10:23:31.804 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308026601 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:32.112 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 010042970 状态异常
2025-07-19 10:23:32.166 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 220722010076 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:32.356 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058615597 查询失败或返回数据为空
2025-07-19 10:23:32.405 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792304250007 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:32.723 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202208040019 状态异常
2025-07-19 10:23:32.773 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202208040019 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:33.073 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 528442169 状态异常
2025-07-19 10:23:33.124 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202305290001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:33.172 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 076052841209 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:33.909 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000003 状态异常
2025-07-19 10:23:33.942 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300003 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:34.319 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000004 状态异常
2025-07-19 10:23:34.347 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300004 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:34.761 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141304067677 状态异常
2025-07-19 10:23:34.810 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141304067677 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:35.104 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 713000001 状态异常
2025-07-19 10:23:35.130 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071300001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:35.417 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141304067674 状态异常
2025-07-19 10:23:35.444 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141304067674 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:35.732 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058627469 状态异常
2025-07-19 10:23:35.761 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076002 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:36.027 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 992307070001 状态异常
2025-07-19 10:23:36.055 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992307070001 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:36.217 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 020000002BB5 查询失败或返回数据为空
2025-07-19 10:23:36.245 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187485 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:36.271 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 FFFFFFFFFFFF 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:36.296 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 905802002779 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:36.322 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 882308026005 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:36.689 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 821000002 状态异常
2025-07-19 10:23:36.714 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134082100002 异常更新: 无数据返回 - 采集器状态异常 (第41次)
2025-07-19 10:23:37.019 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204140050 状态异常
2025-07-19 10:23:37.046 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204140050 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:37.408 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 102004098 状态异常
2025-07-19 10:23:37.434 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202410201002 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:37.728 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202150 状态异常
2025-07-19 10:23:37.792 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202150 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:38.116 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289C6E307081 状态异常
2025-07-19 10:23:38.149 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 YG000000010765 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:38.491 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 102004097 状态异常
2025-07-19 10:23:38.518 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202410201001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:38.841 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693052804874 状态异常
2025-07-19 10:23:38.868 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 230300023684 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:39.209 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 860610052773970 状态异常
2025-07-19 10:23:39.241 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 10304104045111 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:39.541 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100009001 状态异常
2025-07-19 10:23:39.582 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040205 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:39.611 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308040202 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:39.640 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 992308020102 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:39.900 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 020000002BCC 状态异常
2025-07-19 10:23:39.929 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 190331527749 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:40.194 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058611935 状态异常
2025-07-19 10:23:40.222 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792305105020 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:40.489 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 204140044 状态异常
2025-07-19 10:23:40.528 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202204140044 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:40.885 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 757105587 状态异常
2025-07-19 10:23:40.916 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202312116666 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:41.268 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202303170000 状态异常
2025-07-19 10:23:41.323 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202303170000 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:41.597 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202146 状态异常
2025-07-19 10:23:41.624 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202146 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:42.037 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 202205202147 状态异常
2025-07-19 10:23:42.065 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202205202147 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:42.226 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 02010000482F 查询失败或返回数据为空
2025-07-19 10:23:42.252 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 011918223316201947 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:42.402 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 040900103 查询失败或返回数据为空
2025-07-19 10:23:42.427 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616504090067 异常更新: 无数据返回 - 采集器不存在或未匹配 (第2次)
2025-07-19 10:23:42.733 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289054758509 状态异常
2025-07-19 10:23:42.760 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202211010771 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:42.784 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130030 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:42.812 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130029 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:42.838 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202209130026 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:43.204 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 896705587 状态异常
2025-07-19 10:23:43.230 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000000020108 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:43.418 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 048071983819 查询失败或返回数据为空
2025-07-19 10:23:43.444 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202502170001 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:43.470 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202502240001 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:43.825 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030506179 状态异常
2025-07-19 10:23:43.857 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202403051823 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:44.202 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 240100001 状态异常
2025-07-19 10:23:44.229 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000024010001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:44.535 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058607636 状态异常
2025-07-19 10:23:44.562 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170002 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:44.908 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 141240410174 状态异常
2025-07-19 10:23:44.953 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 141240410174 异常更新: 无数据返回 - 采集器状态异常 (第46次)
2025-07-19 10:23:45.374 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 289054796392 状态异常
2025-07-19 10:23:45.453 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308116666 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:45.504 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782308099999 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:45.541 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 782306220001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:45.576 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 011509134421 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:45.880 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 071404752 状态异常
2025-07-19 10:23:45.923 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 148307141290 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:46.244 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058606828 状态异常
2025-07-19 10:23:46.287 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792308076018 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:46.601 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 140310121 状态异常
2025-07-19 10:23:46.629 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202104187886 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:46.652 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202008300467 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:46.950 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 551053321832 状态异常
2025-07-19 10:23:46.979 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000010141802 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:47.004 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000010141801 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:47.376 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 180000336 状态异常
2025-07-19 10:23:47.425 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 250118000150 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:47.461 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000018000150 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:47.823 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058610994 状态异常
2025-07-19 10:23:47.871 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616408140003 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:48.170 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058622692 状态异常
2025-07-19 10:23:48.217 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792407170003 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:48.526 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 210600057 状态异常
2025-07-19 10:23:48.575 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 152000048836 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:48.855 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 030800003 状态异常
2025-07-19 10:23:48.903 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 616503080003 异常更新: 无数据返回 - 采集器状态异常 (第2次)
2025-07-19 10:23:49.176 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693058615803 状态异常
2025-07-19 10:23:49.224 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 792408100001 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:49.534 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 712000002 状态异常
2025-07-19 10:23:49.599 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 134071200002 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:49.782 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 100410103 查询失败或返回数据为空
2025-07-19 10:23:49.828 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000019041782 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:49.863 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 00000010900020 异常更新: 无数据返回 - 采集器不存在或未匹配 (第43次)
2025-07-19 10:23:50.214 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 060718211 状态异常
2025-07-19 10:23:50.268 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202106074723 异常更新: 无数据返回 - 采集器状态异常 (第9次)
2025-07-19 10:23:50.580 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 866693052805228 状态异常
2025-07-19 10:23:50.624 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202109171124 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:50.939 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 180000337 状态异常
2025-07-19 10:23:50.966 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 000018000151 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:51.261 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器 112060025 状态异常
2025-07-19 10:23:51.288 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 202112060025 异常更新: 无数据返回 - 采集器状态异常 (第43次)
2025-07-19 10:23:51.289 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器状态查询完成 - 总采集器: 99, 有效采集器: 1, 参与同步设备: 2
2025-07-19 10:23:51.289 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组 482067317475: 2 台设备
2025-07-19 10:23:51.289 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 按采集器分组完成，共 1 个采集器组，参与同步设备: 2台，无采集器设备: 198台
2025-07-19 10:23:51.290 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始串行处理采集器组，设备数量: 2
2025-07-19 10:23:51.290 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 cs0000000085
2025-07-19 10:23:51.571 WARN  c.y.c.s.waterctrl.EquipmentExceptionMonitorService - 设备 cs0000000085 异常更新: API调用错误 - 同步设备读数失败, 请过几分钟再试 (第16次)
2025-07-19 10:23:51.571 WARN  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 同步失败: 同步设备读数失败, 请过几分钟再试
2025-07-19 10:23:51.571 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 将在 180 秒后重试，原因: 同步设备读数失败, 请过几分钟再试
2025-07-19 10:23:51.571 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 同步失败，添加到持久化重试队列，延迟 180 秒后重试
2025-07-19 10:23:51.585 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 延迟重试任务已添加 - 设备: cs0000000085, 延迟: 3分钟, 执行时间: Sat Jul 19 10:26:51 CST 2025
2025-07-19 10:23:51.585 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备 cs0000000085 已添加到持久化重试队列，重试类型: 延迟重试
2025-07-19 10:23:51.811 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 开始同步设备 cs0000000101
2025-07-19 10:23:52.057 INFO  c.y.c.service.waterctrl.EquipmentReadingService - 设备读数记录更新成功: cs0000000101
2025-07-19 10:23:52.076 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 采集器组处理完成，设备数量: 2
2025-07-19 10:23:52.099 INFO  c.y.c.s.waterctrl.AsyncEquipmentDataSyncService - 设备数据异步同步完成 - 总计:200, 成功:1, 失败:199(含无采集器:198), 重试:0
2025-07-19 10:23:52.125 INFO  c.y.campusos.service.waterctrl.SystemConfigService - 配置缓存刷新完成，共加载 58 个配置项
2025-07-19 10:23:52.125 INFO  c.y.c.service.waterctrl.EquipmentCostService - 从缓存获取分时电价 - 设备: cs0000000101, 尖: 1, 峰: 1, 平: 1, 谷: 1
2025-07-19 10:23:52.141 INFO  c.y.c.service.waterctrl.EquipmentCostService - 分时费用计算完成 - 设备: cs0000000101, 尖: 0×1=￥0.00, 峰: 0×1=￥0.00, 平: 0×1=￥0.00, 谷: 0×1=￥0.00, 总计: ￥0.00
2025-07-19 10:24:16.142 INFO  c.y.c.service.waterctrl.EquipmentCostService - 费用计算记录更新成功: cs0000000101, 总费用: 0.00, 分时费用: 尖0.00/峰0.00/平0.00/谷0.00
2025-07-19 10:30:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 10:15:00,014 to 2025-07-19 10:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 10:40:00.005 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 10:40:00.019 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 10:40:00.033 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 10:40:00.047 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 10:40:00.047 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 10:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 10:30:00,015 to 2025-07-19 10:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 11:00:00.011 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-19 11:00:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 10:45:00,011 to 2025-07-19 11:00:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 11:00:00.028 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-19 11:00:00.051 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-19 11:00:00.051 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-19 11:00:00.131 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 11:00:00.150 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 11:00:00.170 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 11:00:00.187 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 11:00:00.187 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 11:15:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 11:00:00,011 to 2025-07-19 11:15:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 11:20:00.011 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 11:20:00.027 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 11:20:00.043 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 11:20:00.058 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 11:20:00.058 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 11:30:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 11:15:00,011 to 2025-07-19 11:30:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 11:40:00.016 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 11:40:00.042 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 11:40:00.064 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 11:40:00.085 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 11:40:00.086 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 11:45:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 11:30:00,016 to 2025-07-19 11:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 11:57:33.231 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 11:57:33.233 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 11:57:33.234 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 11:57:33.413 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 11:57:33.423 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 12:04:06.517 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 41640 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 12:04:06.517 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 12:04:06.520 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 12:04:08.388 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 12:04:08.391 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 12:04:08.564 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 161 ms. Found 0 Redis repository interfaces.
2025-07-19 12:04:09.007 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$d1fbf0f0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 12:04:09.099 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$51023668] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 12:04:09.408 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 12:04:09.419 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 12:04:09.420 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 12:04:09.420 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 12:04:09.648 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 12:04:09.648 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3051 ms
2025-07-19 12:04:09.769 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 12:04:09.849 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 12:04:11.669 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 12:04:12.149 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 12:04:14.418 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 12:04:14.565 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 12:04:14.565 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 12:04:15.639 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 12:04:15.840 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 12:04:15.840 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 12:04:16.424 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 12:04:16.654 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 12:04:16.690 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 12:04:16.700 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 12:04:16.852 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 12:04:17.380 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 12:04:17.388 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 12:04:17.572 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$6b9627bd - 计划任务最多执行：66个
2025-07-19 12:04:17.633 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 12:04:17.637 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 12:04:17.637 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 12:04:17.640 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 12:04:17.643 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 12:04:17.643 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 12:04:17.646 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 12:04:17.648 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 12:04:17.650 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 12:04:17.652 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 12:04:17.655 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 12:04:17.657 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 12:04:17.660 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 12:04:17.662 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 12:04:17.665 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 12:04:17.667 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 12:04:17.669 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 12:04:17.669 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 12:04:17.673 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 12:04:17.697 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.61 seconds (JVM running for 12.768)
2025-07-19 12:04:18.092 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 12:15:00.024 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 12:04:16,850 to 2025-07-19 12:15:00,018
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 13:47:07.315 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 13:47:07.337 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 13:47:07.354 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 13:47:07.373 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 13:47:07.373 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 13:47:07.505 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-19 13:47:07.526 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-19 13:47:07.546 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-19 13:47:07.546 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-19 13:57:07.313 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 12:15:00,018 to 2025-07-19 13:57:07,313
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 13:57:07.313 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 13:57:07,313 to 2025-07-19 13:57:07,313
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 13:57:07.313 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 13:57:07,313 to 2025-07-19 13:57:07,313
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 13:57:07.313 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 13:57:07,313 to 2025-07-19 13:57:07,313
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 13:57:07.314 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 13:57:07,313 to 2025-07-19 13:57:07,313
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 13:57:07.314 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 13:57:07,313 to 2025-07-19 13:57:07,314
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 14:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 13:57:07,314 to 2025-07-19 14:00:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 14:00:00.015 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 14:00:00.030 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 14:00:00.044 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 14:00:00.057 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 14:00:00.057 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 14:00:00.131 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-19 14:00:00.149 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-19 14:00:00.167 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-19 14:00:00.168 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-19 14:04:56.874 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 14:04:56.874 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 14:04:56.877 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-19 14:04:58.767 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 14:06:30.982 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 14:06:30.984 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 14:06:30.984 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 14:06:31.139 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 14:06:31.144 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 14:06:34.758 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 51072 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 14:06:34.758 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 14:06:34.761 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 14:06:36.488 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 14:06:36.491 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 14:06:36.649 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 144 ms. Found 0 Redis repository interfaces.
2025-07-19 14:06:37.088 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$5fcdd889] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 14:06:37.172 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$ded41e01] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 14:06:37.449 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 14:06:37.460 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 14:06:37.460 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 14:06:37.460 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 14:06:37.647 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 14:06:37.647 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2798 ms
2025-07-19 14:06:37.766 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 14:06:37.829 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 14:06:39.586 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 14:06:40.289 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 14:06:42.565 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 14:06:42.724 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 14:06:42.724 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 14:06:43.941 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 14:06:44.212 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 14:06:44.213 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 14:06:44.806 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 14:06:45.039 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 14:06:45.068 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 14:06:45.078 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 14:06:45.214 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 14:06:45.749 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 14:06:45.757 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 14:06:45.962 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$f9680f56 - 计划任务最多执行：66个
2025-07-19 14:06:46.036 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 14:06:46.042 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 14:06:46.042 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 14:06:46.060 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 14:06:46.065 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 14:06:46.065 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 14:06:46.070 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 14:06:46.074 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 14:06:46.078 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 14:06:46.082 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 14:06:46.085 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 14:06:46.089 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 14:06:46.092 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 14:06:46.096 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 14:06:46.101 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 14:06:46.104 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 14:06:46.106 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 14:06:46.107 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 14:06:46.110 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 14:06:46.147 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.814 seconds (JVM running for 13.07)
2025-07-19 14:06:46.598 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 14:07:27.955 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 14:07:27.955 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 14:07:27.957 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-19 14:07:33.331 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 14:07:34.795 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 14:15:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 14:06:45,212 to 2025-07-19 14:15:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 14:20:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 14:20:00.032 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 14:20:00.047 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 14:20:00.065 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 14:20:00.065 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 14:22:22.469 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 14:22:22.470 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 14:22:22.471 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 14:22:22.737 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 14:22:22.743 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 14:24:55.889 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 4032 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 14:24:55.889 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 14:24:55.891 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 14:24:57.809 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 14:24:57.811 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 14:24:57.999 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 175 ms. Found 0 Redis repository interfaces.
2025-07-19 14:24:58.451 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$d696b429] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 14:24:58.533 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$559cf9a1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 14:24:58.830 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 14:24:58.840 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 14:24:58.841 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 14:24:58.841 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 14:24:59.046 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 14:24:59.047 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3078 ms
2025-07-19 14:24:59.155 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 14:24:59.227 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 14:25:00.876 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 14:25:01.396 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 14:25:03.915 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 14:25:04.071 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 14:25:04.071 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 14:25:05.420 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 14:25:05.716 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 14:25:05.716 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 14:25:06.465 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 14:25:06.723 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 14:25:06.754 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 14:25:06.764 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 14:25:06.907 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 14:25:07.464 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 14:25:07.472 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 14:25:07.683 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$7030eaf6 - 计划任务最多执行：66个
2025-07-19 14:25:07.754 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 14:25:07.760 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 14:25:07.760 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 14:25:07.763 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 14:25:07.765 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 14:25:07.765 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 14:25:07.770 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 14:25:07.773 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 14:25:07.777 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 14:25:07.780 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 14:25:07.783 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 14:25:07.787 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 14:25:07.789 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 14:25:07.792 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 14:25:07.795 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 14:25:07.797 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 14:25:07.800 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 14:25:07.800 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 14:25:07.805 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 14:25:07.845 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 12.425 seconds (JVM running for 13.665)
2025-07-19 14:25:08.361 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 14:27:32.831 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 14:27:32.831 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 14:27:32.833 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-19 14:27:46.519 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 14:30:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 14:25:06,904 to 2025-07-19 14:30:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 14:33:15.480 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 14:33:15.481 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 14:33:15.482 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 14:33:15.634 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 14:33:15.640 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 14:33:20.319 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 42928 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 14:33:20.317 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 14:33:20.320 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 14:33:22.075 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 14:33:22.078 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 14:33:22.284 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 192 ms. Found 0 Redis repository interfaces.
2025-07-19 14:33:22.770 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$69d44630] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 14:33:22.865 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e8da8ba8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 14:33:23.240 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 14:33:23.257 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 14:33:23.258 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 14:33:23.258 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 14:33:23.804 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 14:33:23.805 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3396 ms
2025-07-19 14:33:23.970 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 14:33:24.047 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 14:33:25.789 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 14:33:26.481 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 14:33:28.895 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 14:33:29.042 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 14:33:29.042 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 14:33:30.245 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 14:33:30.501 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 14:33:30.501 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 14:33:31.132 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 14:33:31.368 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 14:33:31.398 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 14:33:31.409 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 14:33:31.541 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 14:33:32.068 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 14:33:32.077 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 14:33:32.326 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$36e7cfd - 计划任务最多执行：66个
2025-07-19 14:33:32.392 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 14:33:32.392 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 14:33:32.394 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-19 14:33:32.411 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 14:33:32.420 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 14:33:32.420 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 14:33:32.425 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 14:33:32.427 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 14:33:32.428 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 14:33:32.433 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 14:33:32.437 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 14:33:32.441 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 14:33:32.443 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 14:33:32.447 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 14:33:32.452 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 14:33:32.455 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 14:33:32.460 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 14:33:32.463 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 14:33:32.466 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 14:33:32.469 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 14:33:32.469 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 14:33:32.475 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 14:33:32.505 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 12.634 seconds (JVM running for 14.043)
2025-07-19 14:33:32.507 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-07-19 14:33:32.924 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 14:33:46.842 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 14:34:09.559 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: 1, 类型: 1, 区域: 001001002001
2025-07-19 14:34:09.578 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 14:34:09.719 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT config_value FROM tb_sys_config WHERE config_key = 'electric_unit_price' AND status = 1]
2025-07-19 14:34:09.719 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT config_value FROM tb_sys_config WHERE config_key = 'electric_unit_price' AND status = 1]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'config_value' in 'field list'
2025-07-19 14:34:09.719 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 0.6000
2025-07-19 14:34:09.719 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 14:34:09.720 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量12.00 * 单价0.6000 = 总费用7.20
2025-07-19 14:34:09.720 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (equipment_code, equipment_type, area_code, reading_date, current_reading, previous_reading, usage_amount, unit_price, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, total_cost, is_time_sharing, current_balance, remark, status, create_time) VALUES ('1', '1', '001001002001', '2025-07-19 14:34:00', 12.0, 0.0, 12.0, 0.6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.199999999999999, 0, 0.0, '12', 1, NOW())
2025-07-19 14:34:09.736 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[INSERT INTO tb_equipment_reading_records (equipment_code, equipment_type, area_code, reading_date, current_reading, previous_reading, usage_amount, unit_price, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, total_cost, is_time_sharing, current_balance, remark, status, create_time) VALUES ('1', '1', '001001002001', '2025-07-19 14:34:00', 12.0, 0.0, 12.0, 0.6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.199999999999999, 0, 0.0, '12', 1, NOW())]
2025-07-19 14:34:09.736 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [INSERT INTO tb_equipment_reading_records (equipment_code, equipment_type, area_code, reading_date, current_reading, previous_reading, usage_amount, unit_price, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, total_cost, is_time_sharing, current_balance, remark, status, create_time) VALUES ('1', '1', '001001002001', '2025-07-19 14:34:00', 12.0, 0.0, 12.0, 0.6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.199999999999999, 0, 0.0, '12', 1, NOW())]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'previous_tip_reading' in 'field list'
2025-07-19 14:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 14:40:00.045 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 14:40:00.064 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 14:40:00.083 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 14:40:00.083 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 14:45:00.021 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 14:33:31,539 to 2025-07-19 14:45:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 14:48:28.915 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 14:48:28.916 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 14:48:28.916 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 14:48:29.066 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 14:48:29.070 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 14:48:35.530 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 49420 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 14:48:35.529 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 14:48:35.532 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 14:48:37.191 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 14:48:37.193 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 14:48:37.378 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 173 ms. Found 0 Redis repository interfaces.
2025-07-19 14:48:37.870 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$ed557984] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 14:48:37.962 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$6c5bbefc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 14:48:38.246 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 14:48:38.259 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 14:48:38.259 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 14:48:38.259 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 14:48:38.498 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 14:48:38.498 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2887 ms
2025-07-19 14:48:38.640 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 14:48:38.725 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 14:48:40.486 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 14:48:41.137 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 14:48:43.458 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 14:48:43.620 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 14:48:43.620 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 14:48:44.777 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 14:48:45.039 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 14:48:45.040 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 14:48:45.630 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 14:48:45.881 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 14:48:45.911 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 14:48:45.922 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 14:48:46.057 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 14:48:46.617 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 14:48:46.626 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 14:48:46.823 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$86efb051 - 计划任务最多执行：66个
2025-07-19 14:48:46.886 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 14:48:46.891 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 14:48:46.891 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 14:48:46.895 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 14:48:46.898 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 14:48:46.898 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 14:48:46.901 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 14:48:46.903 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 14:48:46.906 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 14:48:46.908 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 14:48:46.910 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 14:48:46.913 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 14:48:46.915 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 14:48:46.918 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 14:48:46.920 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 14:48:46.923 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 14:48:46.925 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 14:48:46.925 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 14:48:46.928 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 14:48:46.953 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.852 seconds (JVM running for 13.018)
2025-07-19 14:48:47.408 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 15:00:00.006 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-19 15:00:00.026 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 14:48:46,055 to 2025-07-19 15:00:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 15:00:00.038 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-19 15:00:00.056 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-19 15:00:00.056 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-19 15:00:00.170 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 15:00:00.189 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 15:00:00.207 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 15:00:00.224 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 15:00:00.224 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 15:15:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 15:00:00,011 to 2025-07-19 15:15:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 15:20:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 15:20:00.033 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 15:20:00.055 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 15:20:00.073 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 15:20:00.074 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 15:20:03.938 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 15:20:03.938 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 15:20:03.944 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-19 15:20:09.303 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 15:20:09.308 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 15:20:09.310 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 15:20:09.567 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 15:20:09.589 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 15:20:13.688 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 20600 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 15:20:13.688 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 15:20:13.690 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 15:20:15.339 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 15:20:15.341 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 15:20:15.524 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 171 ms. Found 0 Redis repository interfaces.
2025-07-19 15:20:15.973 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$726722d5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:20:16.054 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$f16d684d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:20:16.322 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 15:20:16.333 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 15:20:16.334 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 15:20:16.334 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 15:20:16.544 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 15:20:16.544 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2775 ms
2025-07-19 15:20:16.648 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 15:20:16.709 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 15:20:18.770 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 15:20:19.279 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 15:20:21.556 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 15:20:21.700 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 15:20:21.700 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 15:20:22.812 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 15:20:23.038 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 15:20:23.038 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 15:20:23.684 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 15:20:23.944 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 15:20:23.976 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 15:20:23.987 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 15:20:24.126 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 15:20:24.685 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 15:20:24.694 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 15:20:24.883 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$c0159a2 - 计划任务最多执行：66个
2025-07-19 15:20:24.949 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 15:20:24.953 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 15:20:24.955 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 15:20:24.957 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 15:20:24.960 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 15:20:24.960 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 15:20:24.962 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 15:20:24.965 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 15:20:24.967 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 15:20:24.970 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 15:20:24.972 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 15:20:24.976 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 15:20:24.979 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 15:20:24.983 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 15:20:25.007 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 15:20:25.012 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 15:20:25.014 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 15:20:25.014 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 15:20:25.018 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 15:20:25.046 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.798 seconds (JVM running for 12.997)
2025-07-19 15:20:25.475 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 15:21:19.778 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 15:21:19.778 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 15:21:19.780 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-19 15:21:26.484 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 15:21:53.321 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: 1, 类型: 1, 区域: 001001002001
2025-07-19 15:21:53.345 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 15:21:53.661 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT config_value FROM tb_sys_config WHERE config_key = 'electric_unit_price' AND status = 1]
2025-07-19 15:21:53.662 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT config_value FROM tb_sys_config WHERE config_key = 'electric_unit_price' AND status = 1]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'config_value' in 'field list'
2025-07-19 15:21:53.662 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 0.6000
2025-07-19 15:21:53.662 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 15:21:53.662 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量1.00 * 单价0.6000 = 总费用0.60
2025-07-19 15:21:53.663 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('81d7c408-7f45-424a-a18b-73ff5eb7dfa2', '1', '1', '001001002001', '2025-07-19', '2025-07-19 15:21:46', 1.0, 0.0, 1.0, 0.0, 0.6, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 15:21:53.681 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数保存成功，开始保存到费用计算表
2025-07-19 15:21:53.681 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 15:21:53.681 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('027e3583-bc76-4cf0-a503-6c6e7dc4a202', '1', '1', '2025-07-19', 'b339fbdb-614d-41a6-b232-d1b48dbaa287', 1.0, 0.0, 1.0, 0.6, 0.6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002001', 1, NOW(), 'manual_input', '')
2025-07-19 15:21:53.700 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 15:21:53.700 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 15:21:53.741 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[INSERT INTO tb_equipment_cost_summary (uid, area_code, summary_month, electric_total_usage, electric_total_cost, total_cost, water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, status, create_date, creator_id) VALUES ('343bf5d5-7ef0-413a-9ac2-15525db412c5', '001001002001', '2025-07', 0, 0, 1.0, 0.6, 0, 0, 0, 0, 0.6, 1, NOW(), 'manual_input')]
2025-07-19 15:21:53.741 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [INSERT INTO tb_equipment_cost_summary (uid, area_code, summary_month, electric_total_usage, electric_total_cost, total_cost, water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, status, create_date, creator_id) VALUES ('343bf5d5-7ef0-413a-9ac2-15525db412c5', '001001002001', '2025-07', 0, 0, 1.0, 0.6, 0, 0, 0, 0, 0.6, 1, NOW(), 'manual_input')]; nested exception is java.sql.SQLException: Column count doesn't match value count at row 1
2025-07-19 15:21:53.742 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表插入成功
2025-07-19 15:21:53.742 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据保存成功
2025-07-19 15:30:00.019 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 15:20:24,123 to 2025-07-19 15:30:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 15:31:20.126 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 15:31:20.127 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 15:31:20.128 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 15:31:20.279 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 15:31:20.283 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 15:31:26.531 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 15:31:26.531 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 21776 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 15:31:26.534 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 15:31:28.180 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 15:31:28.183 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 15:31:28.364 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 168 ms. Found 0 Redis repository interfaces.
2025-07-19 15:31:28.841 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$bdafb291] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:31:28.928 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$3cb5f809] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:31:29.202 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 15:31:29.213 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 15:31:29.213 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 15:31:29.213 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 15:31:29.408 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 15:31:29.409 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2786 ms
2025-07-19 15:31:29.523 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 15:31:29.586 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 15:31:31.450 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 15:31:31.911 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 15:31:34.189 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 15:31:34.335 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 15:31:34.335 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 15:31:35.563 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 15:31:35.782 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 15:31:35.782 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 15:31:36.459 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 15:31:36.702 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 15:31:36.732 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 15:31:36.742 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 15:31:36.871 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 15:31:37.370 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 15:31:37.378 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 15:31:37.652 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$5749e95e - 计划任务最多执行：66个
2025-07-19 15:31:37.719 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 15:31:37.719 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 15:31:37.722 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-19 15:31:37.737 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 15:31:37.746 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 15:31:37.746 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 15:31:37.750 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 15:31:37.756 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 15:31:37.756 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 15:31:37.760 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 15:31:37.763 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 15:31:37.769 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 15:31:37.773 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 15:31:37.777 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 15:31:37.782 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 15:31:37.790 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 15:31:37.796 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 15:31:37.800 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 15:31:37.822 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 15:31:37.830 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 15:31:37.831 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 15:31:37.837 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 15:31:37.873 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.776 seconds (JVM running for 12.897)
2025-07-19 15:31:37.879 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-07-19 15:31:38.285 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 15:31:53.575 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 15:32:25.549 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: 1, 类型: 1, 区域: 001001002001
2025-07-19 15:32:25.566 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 15:32:25.690 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT config_value FROM tb_sys_config WHERE config_key = 'ele_unit_price' AND status = 1]
2025-07-19 15:32:25.690 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT config_value FROM tb_sys_config WHERE config_key = 'ele_unit_price' AND status = 1]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'config_value' in 'field list'
2025-07-19 15:32:25.690 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 0.6000
2025-07-19 15:32:25.692 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 15:32:25.692 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量1.00 * 单价0.6000 = 总费用0.60
2025-07-19 15:32:25.692 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('3b6bb049-ad27-4f97-bb2f-6bd752fffbe5', '1', '1', '001001002001', '2025-07-19', '2025-07-19 15:32:16', 1.0, 0.0, 1.0, 0.0, 0.6, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 15:32:25.708 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数保存成功，开始保存到费用计算表
2025-07-19 15:32:25.708 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 15:32:25.708 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('ff89e35d-fea9-4041-a88c-5dfb1b3033a0', '1', '1', '2025-07-19', '54d11c91-a9dd-48cc-a806-476fbad5fa81', 1.0, 0.0, 1.0, 0.6, 0.6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002001', 1, NOW(), 'manual_input', '')
2025-07-19 15:32:25.724 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 15:32:25.725 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 15:32:25.766 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[INSERT INTO tb_equipment_cost_summary (uid, area_code, summary_month, electric_total_usage, electric_total_cost, total_cost, water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, status, create_date, creator_id) VALUES ('c3498d97-7f4f-4f09-a9e8-9a73a9afda52', '001001002001', '2025-07', 0, 0, 1.0, 0.6, 0, 0, 0, 0, 0.6, 1, NOW(), 'manual_input')]
2025-07-19 15:32:25.767 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [INSERT INTO tb_equipment_cost_summary (uid, area_code, summary_month, electric_total_usage, electric_total_cost, total_cost, water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, status, create_date, creator_id) VALUES ('c3498d97-7f4f-4f09-a9e8-9a73a9afda52', '001001002001', '2025-07', 0, 0, 1.0, 0.6, 0, 0, 0, 0, 0.6, 1, NOW(), 'manual_input')]; nested exception is java.sql.SQLException: Column count doesn't match value count at row 1
2025-07-19 15:32:25.767 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表插入成功
2025-07-19 15:32:25.767 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据保存成功
2025-07-19 15:32:58.437 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: cs0000000101, 类型: 1, 区域: 001001002001
2025-07-19 15:32:58.457 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 22.11
2025-07-19 15:32:58.473 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT config_value FROM tb_sys_config WHERE config_key = 'ele_unit_price' AND status = 1]
2025-07-19 15:32:58.474 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT config_value FROM tb_sys_config WHERE config_key = 'ele_unit_price' AND status = 1]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'config_value' in 'field list'
2025-07-19 15:32:58.474 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 0.6000
2025-07-19 15:32:58.475 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 15:32:58.475 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量977.89 * 单价0.6000 = 总费用586.73
2025-07-19 15:32:58.475 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('9fba0751-cfdd-4111-a16f-f7b60fa4a3b7', 'cs0000000101', '1', '001001002001', '2025-07-19', '2025-07-19 15:32:49', 1000.0, 22.11, 977.89, 0.0, 0.6, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 15:32:58.496 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('9fba0751-cfdd-4111-a16f-f7b60fa4a3b7', 'cs0000000101', '1', '001001002001', '2025-07-19', '2025-07-19 15:32:49', 1000.0, 22.11, 977.89, 0.0, 0.6, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')]
2025-07-19 15:32:58.496 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; SQL [INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('9fba0751-cfdd-4111-a16f-f7b60fa4a3b7', 'cs0000000101', '1', '001001002001', '2025-07-19', '2025-07-19 15:32:49', 1000.0, 22.11, 977.89, 0.0, 0.6, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')]; Duplicate entry 'cs0000000101-2025-07-19' for key 'uk_equipment_date'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'cs0000000101-2025-07-19' for key 'uk_equipment_date'
2025-07-19 15:33:26.666 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: cs0000000085, 类型: 1, 区域: 001001002001
2025-07-19 15:33:26.693 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 37.00
2025-07-19 15:33:26.722 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT config_value FROM tb_sys_config WHERE config_key = 'ele_unit_price' AND status = 1]
2025-07-19 15:33:26.723 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT config_value FROM tb_sys_config WHERE config_key = 'ele_unit_price' AND status = 1]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'config_value' in 'field list'
2025-07-19 15:33:26.723 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 0.6000
2025-07-19 15:33:26.723 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 15:33:26.723 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量963.00 * 单价0.6000 = 总费用577.80
2025-07-19 15:33:26.724 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('fc03141b-2e07-4b32-9ae2-509bb80fa910', 'cs0000000085', '1', '001001002001', '2025-07-19', '2025-07-19 15:32:49', 1000.0, 37.0, 963.0, 0.0, 0.6, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 15:33:26.752 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数保存成功，开始保存到费用计算表
2025-07-19 15:33:26.752 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 15:33:26.752 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('e2f8aa34-151f-408c-b10d-7c3ff9537f43', 'cs0000000085', '1', '2025-07-19', 'f1f853db-4051-4711-9ff4-f5581d52f628', 1000.0, 37.0, 963.0, 0.6, 577.8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002001', 1, NOW(), 'manual_input', '')
2025-07-19 15:33:26.780 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 15:33:26.780 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 15:33:26.839 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[INSERT INTO tb_equipment_cost_summary (uid, area_code, summary_month, electric_total_usage, electric_total_cost, total_cost, water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, status, create_date, creator_id) VALUES ('01e477c0-9d72-4dba-b6b5-181d55330d8d', '001001002001', '2025-07', 0, 0, 963.0, 577.8, 0, 0, 0, 0, 577.8, 1, NOW(), 'manual_input')]
2025-07-19 15:33:26.840 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [INSERT INTO tb_equipment_cost_summary (uid, area_code, summary_month, electric_total_usage, electric_total_cost, total_cost, water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, status, create_date, creator_id) VALUES ('01e477c0-9d72-4dba-b6b5-181d55330d8d', '001001002001', '2025-07', 0, 0, 963.0, 577.8, 0, 0, 0, 0, 577.8, 1, NOW(), 'manual_input')]; nested exception is java.sql.SQLException: Column count doesn't match value count at row 1
2025-07-19 15:33:26.840 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表插入成功
2025-07-19 15:33:26.840 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据保存成功
2025-07-19 15:34:50.381 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 15:34:50.382 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 15:34:50.383 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 15:34:50.536 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 15:34:50.547 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 15:35:07.734 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 50720 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 15:35:07.734 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 15:35:07.737 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 15:35:09.459 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 15:35:09.461 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 15:35:09.665 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 191 ms. Found 0 Redis repository interfaces.
2025-07-19 15:35:10.128 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$9c46dacd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:35:10.213 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$1b4d2045] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:35:10.491 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 15:35:10.502 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 15:35:10.503 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 15:35:10.503 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 15:35:10.693 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 15:35:10.694 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2870 ms
2025-07-19 15:35:10.803 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 15:35:10.877 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 15:35:12.525 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 15:35:13.057 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 15:35:15.439 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 15:35:15.595 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 15:35:15.595 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 15:35:16.829 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 15:35:17.058 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 15:35:17.058 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 15:35:17.684 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 15:35:17.925 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 15:35:17.955 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 15:35:17.966 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 15:35:18.101 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 15:35:18.630 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 15:35:18.640 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 15:35:18.847 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$35e1119a - 计划任务最多执行：66个
2025-07-19 15:35:18.914 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 15:35:18.920 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 15:35:18.920 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 15:35:18.923 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 15:35:18.926 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 15:35:18.926 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 15:35:18.928 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 15:35:18.932 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 15:35:18.935 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 15:35:18.937 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 15:35:18.942 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 15:35:18.946 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 15:35:18.950 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 15:35:18.953 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 15:35:18.955 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 15:35:18.957 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 15:35:18.961 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 15:35:18.961 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 15:35:18.963 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 15:35:18.993 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.72 seconds (JVM running for 13.225)
2025-07-19 15:35:19.409 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 15:39:36.226 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 15:39:36.227 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 15:39:36.229 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-19 15:39:40.219 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 15:40:00.004 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 15:40:00.027 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 15:40:00.044 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 15:40:00.063 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 15:40:00.063 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 15:40:43.024 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: cs0000000085, 类型: 1, 区域: 001001002002
2025-07-19 15:40:43.054 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 37.00
2025-07-19 15:40:47.915 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT config_value FROM tb_sys_config WHERE config_key = 'ele_unit_price']
2025-07-19 15:40:47.915 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT config_value FROM tb_sys_config WHERE config_key = 'ele_unit_price']; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'config_value' in 'field list'
2025-07-19 15:40:47.915 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 0.6000
2025-07-19 15:40:47.916 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 15:40:47.916 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量1175.00 * 单价0.6000 = 总费用705.00
2025-07-19 15:40:47.916 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('a63b6233-3eed-41a4-b0d0-5e93a09b79a7', 'cs0000000085', '1', '001001002002', '2025-07-19', '2025-07-19 15:40:30', 1212.0, 37.0, 1175.0, 0.0, 0.6, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 15:40:47.933 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数保存成功，开始保存到费用计算表
2025-07-19 15:40:47.933 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 15:40:47.933 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('6acd9603-bac5-4653-ac60-a36d0b068f7c', 'cs0000000085', '1', '2025-07-19', '03563f5b-637b-4f95-842a-8652f78a9969', 1212.0, 37.0, 1175.0, 0.6, 705.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002002', 1, NOW(), 'manual_input', '')
2025-07-19 15:40:47.949 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 15:40:47.949 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 15:40:47.982 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[INSERT INTO tb_equipment_cost_summary (uid, area_code, summary_month, electric_total_usage, electric_total_cost, total_cost, water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, status, create_date, creator_id) VALUES ('d8c36728-2801-49e2-b61b-d0d2f4ebed16', '001001002002', '2025-07', 0, 0, 1175.0, 705.0, 0, 0, 0, 0, 705.0, 1, NOW(), 'manual_input')]
2025-07-19 15:40:47.982 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [INSERT INTO tb_equipment_cost_summary (uid, area_code, summary_month, electric_total_usage, electric_total_cost, total_cost, water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, status, create_date, creator_id) VALUES ('d8c36728-2801-49e2-b61b-d0d2f4ebed16', '001001002002', '2025-07', 0, 0, 1175.0, 705.0, 0, 0, 0, 0, 705.0, 1, NOW(), 'manual_input')]; nested exception is java.sql.SQLException: Column count doesn't match value count at row 1
2025-07-19 15:40:47.982 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表插入成功
2025-07-19 15:40:47.982 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据保存成功
2025-07-19 15:41:20.977 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 15:41:20.978 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 15:41:20.979 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 15:41:21.136 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 15:41:21.142 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 15:41:24.377 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 54216 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 15:41:24.377 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 15:41:24.380 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 15:41:26.022 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 15:41:26.024 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 15:41:26.208 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 172 ms. Found 0 Redis repository interfaces.
2025-07-19 15:41:26.663 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1118817e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:41:26.754 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$901ec6f6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:41:27.023 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 15:41:27.034 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 15:41:27.034 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 15:41:27.034 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 15:41:27.230 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 15:41:27.230 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2769 ms
2025-07-19 15:41:27.339 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 15:41:27.409 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 15:41:29.138 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 15:41:29.638 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 15:41:31.936 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 15:41:32.087 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 15:41:32.088 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 15:41:33.210 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 15:41:33.445 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 15:41:33.445 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 15:41:34.034 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 15:41:34.265 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 15:41:34.294 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 15:41:34.305 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 15:41:34.430 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 15:41:34.942 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 15:41:34.949 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 15:41:35.142 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$aab2b84b - 计划任务最多执行：66个
2025-07-19 15:41:35.210 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 15:41:35.215 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 15:41:35.215 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 15:41:35.218 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 15:41:35.220 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 15:41:35.220 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 15:41:35.223 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 15:41:35.225 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 15:41:35.227 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 15:41:35.229 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 15:41:35.232 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 15:41:35.234 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 15:41:35.237 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 15:41:35.239 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 15:41:35.242 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 15:41:35.244 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 15:41:35.246 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 15:41:35.246 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 15:41:35.249 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 15:41:35.286 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.367 seconds (JVM running for 12.686)
2025-07-19 15:41:35.680 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 15:41:39.235 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 15:41:39.236 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 15:41:39.238 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-19 15:41:55.997 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: cs0000000085, 类型: 1, 区域: 001001002002
2025-07-19 15:41:56.030 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 1212.00
2025-07-19 15:42:03.963 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT config_value FROM tb_sys_config WHERE config_key = 'ele_unit_price']
2025-07-19 15:42:03.964 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT config_value FROM tb_sys_config WHERE config_key = 'ele_unit_price']; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'config_value' in 'field list'
2025-07-19 15:42:58.881 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 0.6000
2025-07-19 15:42:58.884 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 15:42:58.884 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量10909.00 * 单价0.6000 = 总费用6545.40
2025-07-19 15:42:58.884 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('30505049-82db-4c7d-a50f-ee7d893ece9c', 'cs0000000085', '1', '001001002002', '2025-07-19', '2025-07-19 15:41:47', 12121.0, 1212.0, 10909.0, 0.0, 0.6, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 15:42:58.941 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('30505049-82db-4c7d-a50f-ee7d893ece9c', 'cs0000000085', '1', '001001002002', '2025-07-19', '2025-07-19 15:41:47', 12121.0, 1212.0, 10909.0, 0.0, 0.6, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')]
2025-07-19 15:42:58.941 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; SQL [INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('30505049-82db-4c7d-a50f-ee7d893ece9c', 'cs0000000085', '1', '001001002002', '2025-07-19', '2025-07-19 15:41:47', 12121.0, 1212.0, 10909.0, 0.0, 0.6, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')]; Duplicate entry 'cs0000000085-2025-07-19' for key 'uk_equipment_date'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'cs0000000085-2025-07-19' for key 'uk_equipment_date'
2025-07-19 15:42:59.620 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 15:42:59.622 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 15:42:59.623 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 15:42:59.789 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 15:42:59.799 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 15:43:06.222 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 44080 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 15:43:06.222 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 15:43:06.224 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 15:43:07.872 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 15:43:07.874 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 15:43:08.068 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 181 ms. Found 0 Redis repository interfaces.
2025-07-19 15:43:08.506 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$47848371] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:43:08.596 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$c68ac8e9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:43:08.881 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 15:43:08.892 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 15:43:08.892 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 15:43:08.892 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 15:43:09.085 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 15:43:09.086 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2789 ms
2025-07-19 15:43:09.192 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 15:43:09.265 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 15:43:10.970 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 15:43:11.436 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 15:43:13.680 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 15:43:13.844 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 15:43:13.844 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 15:43:15.014 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 15:43:15.387 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 15:43:15.387 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 15:43:16.018 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 15:43:16.280 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 15:43:16.307 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 15:43:16.318 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 15:43:16.446 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 15:43:17.044 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 15:43:17.052 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 15:43:17.264 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$e11eba3e - 计划任务最多执行：66个
2025-07-19 15:43:17.338 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 15:43:17.343 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 15:43:17.343 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 15:43:17.346 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 15:43:17.349 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 15:43:17.349 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 15:43:17.351 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 15:43:17.354 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 15:43:17.357 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 15:43:17.359 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 15:43:17.361 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 15:43:17.364 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 15:43:17.366 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 15:43:17.369 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 15:43:17.372 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 15:43:17.374 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 15:43:17.377 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 15:43:17.377 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 15:43:17.380 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 15:43:17.409 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.619 seconds (JVM running for 12.869)
2025-07-19 15:43:17.813 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 15:43:29.214 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 15:43:29.214 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 15:43:29.216 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-19 15:43:55.489 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 15:44:44.770 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: ************, 类型: 1, 区域: 001001002001
2025-07-19 15:44:44.790 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 4.07
2025-07-19 15:44:51.026 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 15:44:53.512 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 15:44:53.513 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量12116.93 * 单价1.0000 = 总费用12116.93
2025-07-19 15:44:53.513 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('b25bd306-24b3-4394-a9f5-49d1c6e9fb35', '************', '1', '001001002001', '2025-07-19', '2025-07-19 15:44:32', 12121.0, 4.07, 12116.93, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 15:44:53.534 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数保存成功，开始保存到费用计算表
2025-07-19 15:44:53.535 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 15:44:53.535 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('3044f591-fb42-4adc-af28-fae12545f314', '************', '1', '2025-07-19', 'e24f93e7-e967-4d73-9309-8b58fc246bc9', 12121.0, 4.07, 12116.93, 1.0, 12116.93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002001', 1, NOW(), 'manual_input', '')
2025-07-19 15:44:53.558 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 15:44:53.558 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 15:44:53.700 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[INSERT INTO tb_equipment_cost_summary (uid, area_code, summary_month, electric_total_usage, electric_total_cost, total_cost, water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, status, create_date, creator_id) VALUES ('0219a85f-876b-44ba-8851-7267ffc7ab0d', '001001002001', '2025-07', 0, 0, 12116.93, 12116.93, 0, 0, 0, 0, 12116.93, 1, NOW(), 'manual_input')]
2025-07-19 15:44:53.700 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [INSERT INTO tb_equipment_cost_summary (uid, area_code, summary_month, electric_total_usage, electric_total_cost, total_cost, water_total_usage, water_total_cost, electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, gas_total_usage, gas_total_cost, status, create_date, creator_id) VALUES ('0219a85f-876b-44ba-8851-7267ffc7ab0d', '001001002001', '2025-07', 0, 0, 12116.93, 12116.93, 0, 0, 0, 0, 12116.93, 1, NOW(), 'manual_input')]; nested exception is java.sql.SQLException: Column count doesn't match value count at row 1
2025-07-19 15:44:53.700 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表插入成功
2025-07-19 15:44:53.700 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据保存成功
2025-07-19 15:45:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 15:43:16,444 to 2025-07-19 15:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 15:49:27.100 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 15:49:27.101 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 15:49:27.101 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 15:49:27.262 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 15:49:27.267 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 15:50:33.072 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 52384 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 15:50:33.072 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 15:50:33.075 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 15:50:34.696 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 15:50:34.699 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 15:50:34.878 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 168 ms. Found 0 Redis repository interfaces.
2025-07-19 15:50:35.320 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1a8e5f21] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:50:35.402 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$9994a499] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:50:35.671 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 15:50:35.681 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 15:50:35.682 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 15:50:35.682 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 15:50:35.877 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 15:50:35.877 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2726 ms
2025-07-19 15:50:35.981 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 15:50:36.053 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 15:50:37.883 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 15:50:38.352 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 15:50:40.593 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 15:50:40.735 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 15:50:40.735 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 15:50:41.849 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 15:50:42.097 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 15:50:42.097 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 15:50:42.674 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 15:50:42.912 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 15:50:42.940 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 15:50:42.950 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 15:50:43.077 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 15:50:43.577 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 15:50:43.585 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 15:50:43.778 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$b42895ee - 计划任务最多执行：66个
2025-07-19 15:50:43.846 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 15:50:43.851 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 15:50:43.851 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 15:50:43.853 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 15:50:43.856 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 15:50:43.856 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 15:50:43.858 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 15:50:43.861 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 15:50:43.863 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 15:50:43.865 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 15:50:43.867 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 15:50:43.870 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 15:50:43.872 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 15:50:43.875 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 15:50:43.877 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 15:50:43.881 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 15:50:43.883 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 15:50:43.883 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 15:50:43.886 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 15:50:43.921 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.274 seconds (JVM running for 12.434)
2025-07-19 15:50:44.335 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 15:50:51.323 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 15:50:51.324 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 15:50:51.325 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-19 15:50:58.318 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 15:51:30.020 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: ************, 类型: 1, 区域: 001001002005
2025-07-19 15:51:30.049 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 12121.00
2025-07-19 15:51:37.939 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 15:51:37.940 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 15:51:37.940 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量-9800.00 * 单价1.0000 = 总费用-9800.00
2025-07-19 15:51:37.940 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('a62cd4e1-a7ab-4a36-a702-38daf2c83a44', '************', '1', '001001002005', '2025-07-19', '2025-07-19 15:51:23', 2321.0, 12121.0, -9800.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 15:51:38.098 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('a62cd4e1-a7ab-4a36-a702-38daf2c83a44', '************', '1', '001001002005', '2025-07-19', '2025-07-19 15:51:23', 2321.0, 12121.0, -9800.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')]
2025-07-19 15:51:38.099 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; SQL [INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('a62cd4e1-a7ab-4a36-a702-38daf2c83a44', '************', '1', '001001002005', '2025-07-19', '2025-07-19 15:51:23', 2321.0, 12121.0, -9800.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')]; Duplicate entry '************-2025-07-19' for key 'uk_equipment_date'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-2025-07-19' for key 'uk_equipment_date'
2025-07-19 15:51:52.465 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: ************, 类型: 1, 区域: 001001002005
2025-07-19 15:51:52.492 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 15:51:52.518 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 15:51:52.519 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 15:51:52.520 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量2321.00 * 单价1.0000 = 总费用2321.00
2025-07-19 15:51:52.520 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('4988e34e-9ea8-4b70-a564-727c85cb559b', '************', '1', '001001002005', '2025-07-19', '2025-07-19 15:51:23', 2321.0, 0.0, 2321.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 15:51:52.547 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数保存成功，开始保存到费用计算表
2025-07-19 15:51:52.548 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 15:51:52.548 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('f2fdea7e-3506-44dc-ba3d-63303875a286', '************', '1', '2025-07-19', 'b589dabe-7164-4874-b6bf-1ae399fb4ad8', 2321.0, 0.0, 2321.0, 1.0, 2321.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002005', 1, NOW(), 'manual_input', '')
2025-07-19 15:51:52.572 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 15:51:52.572 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 15:51:52.623 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表插入成功
2025-07-19 15:51:52.624 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据保存成功
2025-07-19 15:59:19.323 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 15:59:19.325 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 15:59:19.325 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 15:59:19.484 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 15:59:19.488 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 15:59:26.234 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 15:59:26.234 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 47368 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 15:59:26.236 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 15:59:27.878 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 15:59:27.881 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 15:59:28.094 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 200 ms. Found 0 Redis repository interfaces.
2025-07-19 15:59:28.529 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$a9e70c1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:59:28.614 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$28ed5197] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 15:59:28.891 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 15:59:28.902 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 15:59:28.902 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 15:59:28.903 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 15:59:29.094 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 15:59:29.094 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2784 ms
2025-07-19 15:59:29.204 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 15:59:29.276 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 15:59:31.101 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 15:59:31.587 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 15:59:33.866 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 15:59:34.015 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 15:59:34.015 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 15:59:35.141 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 15:59:35.403 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 15:59:35.403 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 15:59:36.044 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 15:59:36.319 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 15:59:36.352 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 15:59:36.363 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 15:59:36.522 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 15:59:37.124 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 15:59:37.131 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 15:59:37.344 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$438142ec - 计划任务最多执行：66个
2025-07-19 15:59:37.418 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 15:59:37.425 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 15:59:37.425 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 15:59:37.430 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 15:59:37.433 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 15:59:37.433 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 15:59:37.435 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 15:59:37.439 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 15:59:37.441 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 15:59:37.443 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 15:59:37.446 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 15:59:37.450 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 15:59:37.453 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 15:59:37.456 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 15:59:37.458 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 15:59:37.461 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 15:59:37.463 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 15:59:37.463 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 15:59:37.466 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 15:59:37.504 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.694 seconds (JVM running for 12.844)
2025-07-19 15:59:37.928 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 15:59:41.236 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 15:59:41.237 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 15:59:41.238 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-19 16:00:00.012 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 16:00:00.020 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 15:59:36,520 to 2025-07-19 16:00:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 16:00:00.034 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 16:00:00.051 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 16:00:00.068 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 16:00:00.068 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 16:00:00.162 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-19 16:00:00.178 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-19 16:00:00.195 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-19 16:00:00.195 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-19 16:00:29.464 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:01:23.234 INFO  c.y.campusos.controller.hotel.WECostController - 新增设备读数 - ID: 0, 设备: ************, 类型: 1, 区域: 001001002001
2025-07-19 16:01:23.285 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 12121.00
2025-07-19 16:01:23.306 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 16:01:23.307 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 16:01:23.308 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量2879.00 * 单价1.0000 = 总费用2879.00
2025-07-19 16:01:23.308 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('a1d32b5d-3f61-40fd-b748-b3d7230994da', '************', '1', '001001002001', '2025-07-19', '2025-07-19 08:00:00', 15000.0, 12121.0, 2879.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 16:01:23.665 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('a1d32b5d-3f61-40fd-b748-b3d7230994da', '************', '1', '001001002001', '2025-07-19', '2025-07-19 08:00:00', 15000.0, 12121.0, 2879.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')]
2025-07-19 16:01:23.665 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; SQL [INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('a1d32b5d-3f61-40fd-b748-b3d7230994da', '************', '1', '001001002001', '2025-07-19', '2025-07-19 08:00:00', 15000.0, 12121.0, 2879.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')]; Duplicate entry '************-2025-07-19' for key 'uk_equipment_date'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '************-2025-07-19' for key 'uk_equipment_date'
2025-07-19 16:08:53.833 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:08:53.834 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:08:53.835 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:08:53.993 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:08:53.998 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:09:00.445 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 46560 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:09:00.445 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:09:00.447 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:09:02.061 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:09:02.064 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:09:02.251 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 176 ms. Found 0 Redis repository interfaces.
2025-07-19 16:09:02.696 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1c43722a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:09:02.778 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$9b49b7a2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:09:03.050 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:09:03.061 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:09:03.061 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:09:03.061 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:09:03.253 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:09:03.253 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2729 ms
2025-07-19 16:09:03.356 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:09:03.429 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:09:05.238 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:09:05.755 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:09:08.077 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:09:08.222 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:09:08.222 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:09:09.497 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:09:09.855 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:09:09.855 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:09:10.516 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:09:10.757 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:09:10.787 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:09:10.799 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:09:10.943 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:09:11.487 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:09:11.495 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:09:11.705 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$b5dda8f7 - 计划任务最多执行：66个
2025-07-19 16:09:11.772 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:09:11.777 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:09:11.777 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:09:11.780 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:09:11.783 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:09:11.783 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:09:11.786 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:09:11.788 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:09:11.791 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:09:11.793 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:09:11.796 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:09:11.799 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:09:11.802 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:09:11.804 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:09:11.807 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:09:11.810 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:09:11.813 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:09:11.813 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:09:11.816 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:09:11.843 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.818 seconds (JVM running for 12.962)
2025-07-19 16:09:12.343 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:09:15.269 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:09:15.269 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:09:15.271 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-19 16:09:34.543 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:11:28.618 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:11:28.619 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:11:28.619 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:11:28.771 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:11:28.781 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:11:32.837 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 26908 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:11:32.836 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:11:32.839 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:11:34.538 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:11:34.540 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:11:34.717 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 165 ms. Found 0 Redis repository interfaces.
2025-07-19 16:11:35.159 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$585b6c2d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:11:35.242 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d761b1a5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:11:35.530 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:11:35.542 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:11:35.542 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:11:35.542 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:11:35.745 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:11:35.746 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2818 ms
2025-07-19 16:11:35.862 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:11:35.925 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:11:37.746 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:11:38.216 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:11:43.519 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:11:44.000 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:11:44.000 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:11:47.922 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:11:48.548 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:11:48.549 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:11:50.382 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:11:51.160 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:11:51.260 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:11:51.296 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:11:51.749 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:11:53.396 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:11:53.417 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:11:54.017 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$f1f5a2fa - 计划任务最多执行：66个
2025-07-19 16:11:54.181 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:11:54.195 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:11:54.197 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:11:54.205 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:11:54.213 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:11:54.221 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:11:54.230 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:11:54.238 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:11:54.246 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:11:54.255 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:11:54.264 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:11:54.273 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:11:54.282 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:11:54.290 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:11:54.298 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:11:54.308 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:11:54.315 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:11:54.315 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:11:54.323 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:11:54.375 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 21.982 seconds (JVM running for 23.141)
2025-07-19 16:11:55.692 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:12:39.598 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:12:39.599 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:12:39.604 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-19 16:12:46.453 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:15:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 16:11:51,742 to 2025-07-19 16:15:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 16:16:13.855 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:16:13.856 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:16:13.857 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:16:14.015 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:16:14.027 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:16:17.901 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 49252 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:16:17.900 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:16:17.903 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:16:19.560 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:16:19.562 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:16:19.804 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 174 ms. Found 0 Redis repository interfaces.
2025-07-19 16:16:20.255 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$4dce600] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:16:20.338 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$83e32b78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:16:20.613 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:16:20.623 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:16:20.624 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:16:20.624 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:16:20.818 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:16:20.818 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2854 ms
2025-07-19 16:16:20.925 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:16:20.989 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:16:22.704 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:16:23.162 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:16:25.619 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:16:25.773 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:16:25.773 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:16:26.880 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:16:27.102 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:16:27.102 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:16:27.696 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:16:27.924 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:16:27.953 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:16:27.963 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:16:28.091 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:16:28.617 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:16:28.625 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:16:28.819 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$9e771ccd - 计划任务最多执行：66个
2025-07-19 16:16:28.880 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:16:28.886 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:16:28.886 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:16:28.888 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:16:28.891 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:16:28.891 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:16:28.894 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:16:28.896 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:16:28.898 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:16:28.900 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:16:28.903 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:16:28.905 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:16:28.908 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:16:28.910 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:16:28.913 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:16:28.915 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:16:28.918 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:16:28.918 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:16:28.921 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:16:28.948 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.491 seconds (JVM running for 12.668)
2025-07-19 16:16:29.444 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:16:46.655 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:16:46.655 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:16:46.659 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-19 16:16:55.468 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:18:02.266 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:18:02.268 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:18:02.268 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:18:02.440 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:18:02.450 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:18:05.796 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:18:05.798 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 35584 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:18:05.798 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:18:07.485 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:18:07.487 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:18:07.687 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 187 ms. Found 0 Redis repository interfaces.
2025-07-19 16:18:08.142 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$585b6c2d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:18:08.222 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d761b1a5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:18:08.497 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:18:08.508 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:18:08.509 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:18:08.509 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:18:08.702 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:18:08.702 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2815 ms
2025-07-19 16:18:08.819 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:18:08.889 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:18:10.721 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:18:11.205 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:18:13.518 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:18:13.670 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:18:13.670 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:18:15.231 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:18:15.484 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:18:15.485 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:18:16.070 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:18:16.301 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:18:16.327 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:18:16.336 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:18:16.464 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:18:16.987 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:18:16.994 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:18:17.189 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$f1f5a2fa - 计划任务最多执行：66个
2025-07-19 16:18:17.257 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:18:17.263 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:18:17.263 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:18:17.266 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:18:17.268 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:18:17.269 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:18:17.271 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:18:17.274 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:18:17.276 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:18:17.278 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:18:17.280 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:18:17.283 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:18:17.286 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:18:17.288 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:18:17.291 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:18:17.294 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:18:17.296 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:18:17.296 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:18:17.299 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:18:17.335 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.966 seconds (JVM running for 13.247)
2025-07-19 16:18:17.863 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:18:22.267 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:18:22.268 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:18:22.269 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-19 16:18:28.545 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:19:15.055 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:19:15.056 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:19:15.057 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:19:15.206 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:19:15.216 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:19:18.636 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 51832 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:19:18.634 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:19:18.637 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:19:20.418 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:19:20.420 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:19:20.619 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 186 ms. Found 0 Redis repository interfaces.
2025-07-19 16:19:21.088 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$ed557984] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:19:21.170 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$6c5bbefc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:19:21.445 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:19:21.455 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:19:21.456 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:19:21.456 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:19:21.655 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:19:21.655 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2943 ms
2025-07-19 16:19:21.762 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:19:21.835 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:19:23.650 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:19:24.108 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:19:26.425 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:19:26.577 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:19:26.577 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:19:27.736 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:19:27.954 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:19:27.954 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:19:28.606 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:19:28.850 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:19:28.879 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:19:28.889 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:19:29.022 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:19:29.571 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:19:29.578 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:19:29.833 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$86efb051 - 计划任务最多执行：66个
2025-07-19 16:19:29.859 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:19:29.859 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:19:29.861 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-19 16:19:29.923 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:19:29.930 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:19:29.931 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:19:29.936 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:19:29.938 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:19:29.939 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:19:29.942 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:19:29.946 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:19:29.949 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:19:29.952 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:19:29.955 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:19:29.960 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:19:29.965 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:19:29.967 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:19:29.970 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:19:29.972 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:19:29.974 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:19:29.975 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:19:29.980 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:19:29.985 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-07-19 16:19:30.023 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.83 seconds (JVM running for 13.204)
2025-07-19 16:19:30.419 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:19:45.405 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:20:00.010 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 16:20:00.041 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 16:20:00.064 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 16:20:00.091 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 16:20:00.091 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 16:20:05.816 INFO  c.y.campusos.controller.hotel.WECostController - 保存设备读数 - 设备: 1, 类型: 1, 区域: 001001002006
2025-07-19 16:20:05.833 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 16:20:05.851 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 16:20:05.851 INFO  c.y.campusos.controller.hotel.WECostController - 处理普通设备数据
2025-07-19 16:20:05.852 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用计算: 用量1212.00 * 单价1.0000 = 总费用1212.00
2025-07-19 16:20:05.852 INFO  c.y.campusos.controller.hotel.WECostController - 执行SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('335d3b1b-6325-4e1b-88c7-094c3f434e4d', '1', '1', '001001002006', '2025-07-19', '2025-07-19 16:20:00', 1212.0, 0.0, 1212.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 16:20:05.868 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数保存成功，开始保存到费用计算表
2025-07-19 16:20:05.869 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 16:20:05.869 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('ead779fe-d23e-4d91-924e-774addb4cda6', '1', '1', '2025-07-19', 'abee48b4-29b7-4058-8270-d1fd214ec544', 1212.0, 0.0, 1212.0, 1.0, 1212.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002006', 1, NOW(), 'manual_input', '')
2025-07-19 16:20:05.886 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 16:20:05.886 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 16:20:05.918 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表插入成功
2025-07-19 16:20:05.918 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据保存成功
2025-07-19 16:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 16:19:29,019 to 2025-07-19 16:30:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 16:34:20.086 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:34:20.087 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:34:20.088 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:34:20.244 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:34:20.250 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:36:19.623 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 10504 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:36:19.623 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:36:19.626 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:36:21.316 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:36:21.319 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:36:21.493 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 162 ms. Found 0 Redis repository interfaces.
2025-07-19 16:36:21.951 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$a9b894af] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:36:22.034 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$28beda27] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:36:22.317 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:36:22.328 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:36:22.328 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:36:22.329 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:36:22.526 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:36:22.527 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2814 ms
2025-07-19 16:36:22.642 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:36:22.704 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:36:24.606 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:36:25.086 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:36:27.413 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:36:27.564 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:36:27.564 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:36:28.637 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:36:28.881 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:36:28.881 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:36:29.743 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:36:30.140 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:36:30.183 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:36:30.197 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:36:30.357 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:36:30.935 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:36:30.943 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:36:31.142 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$4352cb7c - 计划任务最多执行：66个
2025-07-19 16:36:31.204 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:36:31.209 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:36:31.210 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:36:31.212 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:36:31.215 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:36:31.215 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:36:31.218 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:36:31.220 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:36:31.222 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:36:31.224 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:36:31.227 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:36:31.229 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:36:31.232 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:36:31.234 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:36:31.237 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:36:31.240 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:36:31.242 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:36:31.242 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:36:31.245 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:36:31.273 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 12.08 seconds (JVM running for 13.266)
2025-07-19 16:36:31.761 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:37:16.501 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:37:16.502 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:37:16.503 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-19 16:37:26.440 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:37:28.920 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：null
2025-07-19 16:37:52.572 INFO  c.y.campusos.controller.hotel.WECostController - 新增设备读数 - UID: , 设备: 123, 类型: 1, 区域: 001001002008
2025-07-19 16:37:52.600 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 16:37:52.629 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 16:37:52.629 INFO  c.y.campusos.controller.hotel.WECostController - 计算普通设备费用
2025-07-19 16:37:52.629 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用: 213213.00
2025-07-19 16:37:52.629 INFO  c.y.campusos.controller.hotel.WECostController - 执行INSERT SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('7b188b5d-e710-4b58-9bc7-5bfa6948c52c', '123', '1', '001001002008', '2025-07-19', '2025-07-19 16:37:47', 213213.0, 0.0, 213213.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 16:37:52.655 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数新增成功，开始保存到费用计算表
2025-07-19 16:37:52.655 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 16:37:52.655 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('f1029948-57ff-4b82-b6c7-04f1c49fde24', '123', '1', '2025-07-19', 'ca308655-0e64-4751-8b08-ffb7e4885b64', 213213.0, 0.0, 213213.0, 1.0, 213213.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002008', 1, NOW(), 'manual_input', '')
2025-07-19 16:37:52.685 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 16:37:52.685 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 16:37:52.726 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表插入成功
2025-07-19 16:37:52.727 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据新增成功
2025-07-19 16:37:58.723 INFO  c.y.campusos.controller.hotel.WECostController - 修改设备读数 - UID: 7b188b5d-e710-4b58-9bc7-5bfa6948c52c, 设备: 123, 类型: 1, 区域: 001001002008
2025-07-19 16:37:58.741 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 213213.00
2025-07-19 16:37:58.759 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 16:37:58.759 INFO  c.y.campusos.controller.hotel.WECostController - 计算普通设备费用
2025-07-19 16:37:58.759 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用: -213201.00
2025-07-19 16:37:58.759 INFO  c.y.campusos.controller.hotel.WECostController - 执行UPDATE SQL: UPDATE tb_equipment_reading_records SET equipment_code = '123', equipment_type = '1', area_code = '001001002008', reading_date = '2025-07-19', reading_time = '2025-07-19 08:00:00', current_reading = 12.0, previous_reading = 213213.0, usage_amount = -213201.0, current_balance = 0.0, unit_price = 1.0, tip_reading = 0, peak_reading = 0, flat_reading = 0, valley_reading = 0, is_time_sharing = 0, status = 1, modify_date = NOW(), modifier_id = 'manual_input', remark = '' WHERE uid = '7b188b5d-e710-4b58-9bc7-5bfa6948c52c'
2025-07-19 16:37:58.776 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数修改成功，开始保存到费用计算表
2025-07-19 16:37:58.776 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 16:37:58.777 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('44c04e74-db35-4b3a-af90-bac863405098', '123', '1', '2025-07-19', '72a4e66a-796d-41c1-8d36-c8380f7d15e2', 12.0, 213213.0, -213201.0, 1.0, -213201.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002008', 1, NOW(), 'manual_input', '')
2025-07-19 16:37:58.796 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 16:37:58.796 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 16:37:58.841 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表更新成功
2025-07-19 16:37:58.841 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据修改成功
2025-07-19 16:40:00.014 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 16:40:00.036 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 16:40:00.052 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 16:40:00.067 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 16:40:00.067 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 16:41:10.744 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:41:10.745 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:41:10.746 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:41:10.892 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:41:10.902 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:41:17.214 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 46368 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:41:17.212 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:41:17.215 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:41:18.870 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:41:18.872 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:41:19.048 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 162 ms. Found 0 Redis repository interfaces.
2025-07-19 16:41:19.483 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f405447a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:41:19.569 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$730b89f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:41:19.845 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:41:19.856 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:41:19.856 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:41:19.856 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:41:20.070 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:41:20.070 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2768 ms
2025-07-19 16:41:20.171 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:41:20.245 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:41:21.959 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:41:22.438 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:41:24.746 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:41:24.890 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:41:24.890 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:41:25.973 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:41:26.189 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:41:26.189 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:41:26.787 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:41:27.020 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:41:27.046 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:41:27.056 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:41:27.182 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:41:27.683 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:41:27.691 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:41:27.875 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$8d9f7b47 - 计划任务最多执行：66个
2025-07-19 16:41:27.943 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:41:27.948 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:41:27.948 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:41:27.951 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:41:27.953 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:41:27.953 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:41:27.956 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:41:27.958 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:41:27.960 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:41:27.962 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:41:27.966 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:41:27.968 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:41:27.971 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:41:27.974 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:41:27.977 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:41:27.979 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:41:27.982 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:41:27.982 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:41:27.984 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:41:28.009 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.273 seconds (JVM running for 12.451)
2025-07-19 16:41:28.401 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:41:33.267 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:41:33.267 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:41:33.269 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-19 16:44:03.939 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:44:03.940 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:44:03.941 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:44:04.098 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:44:04.110 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:44:10.588 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 47572 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:44:10.587 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:44:10.590 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:44:12.290 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:44:12.292 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:44:12.481 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 176 ms. Found 0 Redis repository interfaces.
2025-07-19 16:44:12.937 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$d1675e26] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:44:13.027 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$506da39e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:44:13.295 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:44:13.305 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:44:13.305 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:44:13.306 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:44:13.502 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:44:13.502 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2824 ms
2025-07-19 16:44:13.619 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:44:13.682 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:44:15.415 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:44:16.024 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:44:18.404 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:44:18.547 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:44:18.547 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:44:19.645 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:44:19.903 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:44:19.903 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:44:20.528 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:44:20.762 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:44:20.792 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:44:20.802 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:44:20.932 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:44:21.497 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:44:21.506 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:44:21.725 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$6b0194f3 - 计划任务最多执行：66个
2025-07-19 16:44:21.802 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:44:21.807 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:44:21.808 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:44:21.811 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:44:21.823 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:44:21.827 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:44:21.837 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:44:21.845 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:44:21.852 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:44:21.858 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:44:21.869 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:44:21.876 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:44:21.889 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:44:21.897 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:44:21.905 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:44:21.912 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:44:21.918 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:44:21.919 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:44:21.926 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:44:21.960 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.799 seconds (JVM running for 13.007)
2025-07-19 16:44:22.465 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:44:39.875 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:44:39.875 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:44:39.877 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-19 16:44:46.111 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:45:00.034 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 16:44:20,929 to 2025-07-19 16:45:00,020
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 16:45:06.476 INFO  c.y.campusos.controller.hotel.WECostController - 新增设备读数 - UID: , 设备: 1212, 类型: 1, 区域: 001001002004
2025-07-19 16:45:06.503 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 16:45:06.529 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 16:45:06.529 INFO  c.y.campusos.controller.hotel.WECostController - 计算普通设备费用
2025-07-19 16:45:06.529 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用: 123.00
2025-07-19 16:45:06.530 INFO  c.y.campusos.controller.hotel.WECostController - 执行INSERT SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('3b9c431d-a6bf-4f06-b96d-4e794c9f4225', '1212', '1', '001001002004', '2025-07-19', '2025-07-19 16:45:00', 123.0, 0.0, 123.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 16:45:06.548 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数新增成功，开始保存到费用计算表
2025-07-19 16:45:06.548 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 16:45:06.548 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('7d4c26e4-992a-4b58-8df1-8af3a147939f', '1212', '1', '2025-07-19', '697deade-c056-42c2-999a-8e2989f1b76d', 123.0, 0.0, 123.0, 1.0, 123.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002004', 1, NOW(), 'manual_input', '')
2025-07-19 16:45:06.565 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 16:45:06.565 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 16:45:06.598 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表插入成功
2025-07-19 16:45:06.598 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据新增成功
2025-07-19 16:45:11.106 INFO  c.y.campusos.controller.hotel.WECostController - 修改设备读数 - UID: 3b9c431d-a6bf-4f06-b96d-4e794c9f4225, 设备: 1212, 类型: 1, 区域: 001001002004
2025-07-19 16:45:11.106 INFO  c.y.campusos.controller.hotel.WECostController - 删除设备读数 - UID: 3b9c431d-a6bf-4f06-b96d-4e794c9f4225
2025-07-19 16:45:11.150 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 16:45:11.167 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 16:45:11.168 INFO  c.y.campusos.controller.hotel.WECostController - 计算普通设备费用
2025-07-19 16:45:11.168 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用: 1.00
2025-07-19 16:45:11.168 INFO  c.y.campusos.controller.hotel.WECostController - 执行UPDATE SQL: UPDATE tb_equipment_reading_records SET equipment_code = '1212', equipment_type = '1', area_code = '001001002004', reading_date = '2025-07-19', reading_time = '2025-07-19 08:00:00', current_reading = 1.0, previous_reading = 0.0, usage_amount = 1.0, current_balance = 0.0, unit_price = 1.0, tip_reading = 0, peak_reading = 0, flat_reading = 0, valley_reading = 0, is_time_sharing = 0, status = 1, modify_date = NOW(), modifier_id = 'manual_input', remark = '' WHERE uid = '3b9c431d-a6bf-4f06-b96d-4e794c9f4225'
2025-07-19 16:45:49.193 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:45:49.194 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:45:49.196 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:45:49.362 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:45:49.372 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:45:55.536 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 51792 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:45:55.536 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:45:55.538 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:45:57.336 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:45:57.340 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:45:57.543 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 190 ms. Found 0 Redis repository interfaces.
2025-07-19 16:45:58.050 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$a1740087] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:45:58.132 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$207a45ff] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:45:58.423 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:45:58.436 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:45:58.436 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:45:58.436 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:45:58.654 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:45:58.654 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3030 ms
2025-07-19 16:45:58.775 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:45:58.859 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:46:00.682 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:46:01.317 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:46:03.566 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:46:03.715 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:46:03.715 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:46:04.868 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:46:05.107 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:46:05.107 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:46:05.757 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:46:05.998 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:46:06.031 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:46:06.041 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:46:06.171 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:46:06.705 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:46:06.714 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:46:06.919 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$3b0e3754 - 计划任务最多执行：66个
2025-07-19 16:46:06.997 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:46:07.004 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:46:07.004 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:46:07.007 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:46:07.010 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:46:07.010 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:46:07.014 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:46:07.017 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:46:07.019 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:46:07.022 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:46:07.024 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:46:07.028 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:46:07.031 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:46:07.034 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:46:07.036 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:46:07.040 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:46:07.042 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:46:07.042 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:46:07.045 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:46:07.075 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.977 seconds (JVM running for 13.137)
2025-07-19 16:46:07.504 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:46:18.254 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:46:18.254 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:46:18.256 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-19 16:46:28.698 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:46:44.099 INFO  c.y.campusos.controller.hotel.WECostController - 修改设备读数 - UID: 335d3b1b-6325-4e1b-88c7-094c3f434e4d, 设备: 2, 类型: 1, 区域: 001001002006
2025-07-19 16:46:44.100 INFO  c.y.campusos.controller.hotel.WECostController - 删除设备读数 - UID: 335d3b1b-6325-4e1b-88c7-094c3f434e4d
2025-07-19 16:46:44.172 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 16:46:44.196 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 16:46:44.196 INFO  c.y.campusos.controller.hotel.WECostController - 计算普通设备费用
2025-07-19 16:46:44.196 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用: 1212.00
2025-07-19 16:46:44.197 INFO  c.y.campusos.controller.hotel.WECostController - 执行INSERT SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('e8d62412-352a-453c-90b7-3d4c1ec81695', '2', '1', '001001002006', '2025-07-19', '2025-07-19 08:00:00', 1212.0, 0.0, 1212.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 16:46:44.216 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数修改成功，开始保存到费用计算表
2025-07-19 16:46:44.216 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 16:46:44.216 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('22658da2-5836-46ee-9fcd-5ebe37d3e080', '2', '1', '2025-07-19', '797dde6b-6177-42ef-ba0f-6d78a02291fb', 1212.0, 0.0, 1212.0, 1.0, 1212.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002006', 1, NOW(), 'manual_input', '')
2025-07-19 16:46:44.232 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 16:46:44.232 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 16:46:44.266 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表更新成功
2025-07-19 16:46:44.266 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据修改成功
2025-07-19 16:46:54.286 INFO  c.y.campusos.controller.hotel.WECostController - 修改设备读数 - UID: e8d62412-352a-453c-90b7-3d4c1ec81695, 设备: 2, 类型: 1, 区域: 001001002006
2025-07-19 16:46:54.287 INFO  c.y.campusos.controller.hotel.WECostController - 删除设备读数 - UID: e8d62412-352a-453c-90b7-3d4c1ec81695
2025-07-19 16:46:54.323 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 16:46:54.341 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 16:46:54.341 INFO  c.y.campusos.controller.hotel.WECostController - 计算普通设备费用
2025-07-19 16:46:54.341 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用: 2.00
2025-07-19 16:46:54.342 INFO  c.y.campusos.controller.hotel.WECostController - 执行INSERT SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('6321c03f-1c71-4d0f-bb86-e3a06314a733', '2', '1', '001001002006', '2025-07-19', '2025-07-19 08:00:00', 2.0, 0.0, 2.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 16:46:54.358 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数修改成功，开始保存到费用计算表
2025-07-19 16:46:54.358 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 16:46:54.359 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('d09db88a-0d27-49a2-8d77-49ab4dd9021a', '2', '1', '2025-07-19', 'f0481490-d1f3-43fa-911e-937d812b2dec', 2.0, 0.0, 2.0, 1.0, 2.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002006', 1, NOW(), 'manual_input', '')
2025-07-19 16:46:54.378 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 16:46:54.378 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 16:46:54.415 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表更新成功
2025-07-19 16:46:54.417 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据修改成功
2025-07-19 16:46:59.556 INFO  c.y.campusos.controller.hotel.WECostController - 修改设备读数 - UID: 7b188b5d-e710-4b58-9bc7-5bfa6948c52c, 设备: 123, 类型: 1, 区域: 001001002008
2025-07-19 16:46:59.557 INFO  c.y.campusos.controller.hotel.WECostController - 删除设备读数 - UID: 7b188b5d-e710-4b58-9bc7-5bfa6948c52c
2025-07-19 16:46:59.591 INFO  c.y.campusos.controller.hotel.WECostController - 获取上次读数: 0.00
2025-07-19 16:46:59.608 INFO  c.y.campusos.controller.hotel.WECostController - 获取设备单价: 1.0000
2025-07-19 16:46:59.608 INFO  c.y.campusos.controller.hotel.WECostController - 计算普通设备费用
2025-07-19 16:46:59.608 INFO  c.y.campusos.controller.hotel.WECostController - 普通设备费用: 2.00
2025-07-19 16:46:59.609 INFO  c.y.campusos.controller.hotel.WECostController - 执行INSERT SQL: INSERT INTO tb_equipment_reading_records (uid, equipment_code, equipment_type, area_code, reading_date, reading_time, current_reading, previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, status, create_date, creator_id, remark) VALUES ('d37315e7-7b49-4ff9-9454-971bbab01dcc', '123', '1', '001001002008', '2025-07-19', '2025-07-19 08:00:00', 2.0, 0.0, 2.0, 0.0, 1.0, 0, 0, 0, 0, 0, 1, NOW(), 'manual_input', '')
2025-07-19 16:46:59.624 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数修改成功，开始保存到费用计算表
2025-07-19 16:46:59.624 INFO  c.y.campusos.controller.hotel.WECostController - 开始保存到费用计算表
2025-07-19 16:46:59.624 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表SQL: INSERT INTO tb_equipment_cost_calculation (uid, equipment_code, equipment_type, calculation_date, reading_record_id, current_reading, previous_reading, usage_amount, unit_price, total_cost, tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, is_time_sharing, area_code, status, create_date, creator_id, remark) VALUES ('563d9a8c-f851-4d33-a7b7-************', '123', '1', '2025-07-19', 'd7ffcd52-c606-4fb9-a841-24826964c989', 2.0, 0.0, 2.0, 1.0, 2.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '001001002008', 1, NOW(), 'manual_input', '')
2025-07-19 16:46:59.641 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算表保存成功
2025-07-19 16:46:59.642 INFO  c.y.campusos.controller.hotel.WECostController - 开始更新费用汇总表
2025-07-19 16:46:59.674 INFO  c.y.campusos.controller.hotel.WECostController - 费用汇总表更新成功
2025-07-19 16:46:59.674 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数和费用计算数据修改成功
2025-07-19 16:51:49.327 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:51:49.328 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:51:49.337 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:51:49.495 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:51:49.500 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:51:56.683 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 45116 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:51:56.683 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:51:56.686 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:51:58.547 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:51:58.550 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:51:58.795 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 230 ms. Found 0 Redis repository interfaces.
2025-07-19 16:51:59.291 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b0b3934c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:51:59.382 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$2fb9d8c4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:51:59.677 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:51:59.688 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:51:59.688 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:51:59.688 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:51:59.903 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:51:59.904 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3135 ms
2025-07-19 16:52:00.017 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:52:00.100 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:52:02.042 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:52:02.565 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:52:05.038 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:52:05.199 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:52:05.199 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:52:06.438 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:52:06.732 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:52:06.733 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:52:07.358 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:52:07.610 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:52:07.644 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:52:07.654 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:52:07.795 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:52:08.317 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:52:08.325 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:52:08.568 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$4a4dca19 - 计划任务最多执行：66个
2025-07-19 16:52:08.637 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:52:08.637 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:52:08.640 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-19 16:52:08.643 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:52:08.656 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:52:08.656 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:52:08.659 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:52:08.662 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:52:08.663 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:52:08.669 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:52:08.673 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:52:08.675 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:52:08.680 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:52:08.684 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:52:08.688 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:52:08.692 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:52:08.697 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:52:08.702 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:52:08.707 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:52:08.710 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:52:08.710 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:52:08.715 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:52:08.743 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 12.522 seconds (JVM running for 13.789)
2025-07-19 16:52:08.757 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-07-19 16:52:09.160 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:52:42.989 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:53:00.324 INFO  c.y.campusos.controller.hotel.WECostController - 删除设备读数 - UID: b25bd306-24b3-4394-a9f5-49d1c6e9fb35
2025-07-19 16:53:00.665 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT equipment_code, equipment_type, area_code, reading_date, usage_amount, total_cost FROM tb_equipment_reading_records WHERE uid = 'b25bd306-24b3-4394-a9f5-49d1c6e9fb35' AND status = 1]
2025-07-19 16:53:00.666 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT equipment_code, equipment_type, area_code, reading_date, usage_amount, total_cost FROM tb_equipment_reading_records WHERE uid = 'b25bd306-24b3-4394-a9f5-49d1c6e9fb35' AND status = 1]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total_cost' in 'field list'
2025-07-19 16:53:06.176 INFO  c.y.campusos.controller.hotel.WECostController - 删除设备读数 - UID: d37315e7-7b49-4ff9-9454-971bbab01dcc
2025-07-19 16:53:06.197 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT equipment_code, equipment_type, area_code, reading_date, usage_amount, total_cost FROM tb_equipment_reading_records WHERE uid = 'd37315e7-7b49-4ff9-9454-971bbab01dcc' AND status = 1]
2025-07-19 16:53:06.197 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT equipment_code, equipment_type, area_code, reading_date, usage_amount, total_cost FROM tb_equipment_reading_records WHERE uid = 'd37315e7-7b49-4ff9-9454-971bbab01dcc' AND status = 1]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total_cost' in 'field list'
2025-07-19 16:54:09.571 INFO  c.y.campusos.controller.hotel.WECostController - 删除设备读数 - UID: d37315e7-7b49-4ff9-9454-971bbab01dcc
2025-07-19 16:54:09.589 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT equipment_code, equipment_type, area_code, reading_date, usage_amount, total_cost FROM tb_equipment_reading_records WHERE uid = 'd37315e7-7b49-4ff9-9454-971bbab01dcc' AND status = 1]
2025-07-19 16:54:09.590 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [SELECT equipment_code, equipment_type, area_code, reading_date, usage_amount, total_cost FROM tb_equipment_reading_records WHERE uid = 'd37315e7-7b49-4ff9-9454-971bbab01dcc' AND status = 1]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total_cost' in 'field list'
2025-07-19 16:57:31.854 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-19 16:57:31.856 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 任务扫描器已关闭
2025-07-19 16:57:31.856 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 持久化重试任务服务关闭 - 总处理: 0, 成功: 0, 失败: 0
2025-07-19 16:57:32.016 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-19 16:57:32.022 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-19 16:57:38.223 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 17056 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-07-19 16:57:38.222 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-07-19 16:57:38.225 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-07-19 16:57:39.912 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-19 16:57:39.914 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 16:57:40.090 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 164 ms. Found 0 Redis repository interfaces.
2025-07-19 16:57:40.543 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$331a7399] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:57:40.630 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$b220b911] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 16:57:40.910 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-07-19 16:57:40.922 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-07-19 16:57:40.922 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:57:40.922 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-19 16:57:41.129 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:57:41.129 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2830 ms
2025-07-19 16:57:41.236 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-19 16:57:41.308 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-07-19 16:57:43.205 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-19 16:57:43.720 WARN  i.n.r.dns.DefaultDnsServerAddressStreamProvider - Default DNS servers: [/*******:53, /*******:53] (Google Public DNS as a fallback)
2025-07-19 16:57:46.152 INFO  org.redisson.Version - Redisson 3.12.5
2025-07-19 16:57:46.298 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-19 16:57:46.298 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-19 16:57:47.410 INFO  c.y.c.service.waterctrl.PersistentRetryTaskService - 当前有 31 个待执行的重试任务
2025-07-19 16:57:47.624 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 当前有 31 个待执行的重试任务
2025-07-19 16:57:47.624 INFO  c.y.campusos.service.waterctrl.FailureQueueService - 持久化重试任务扫描器已启动
2025-07-19 16:57:48.217 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:57:48.454 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-07-19 16:57:48.486 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-07-19 16:57:48.498 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-07-19 16:57:48.689 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-19 16:57:49.230 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-07-19 16:57:49.237 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-07-19 16:57:49.425 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$ccb4aa66 - 计划任务最多执行：66个
2025-07-19 16:57:49.487 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-07-19 16:57:49.492 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-07-19 16:57:49.492 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-07-19 16:57:49.495 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-07-19 16:57:49.497 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-07-19 16:57:49.498 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-07-19 16:57:49.500 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-07-19 16:57:49.503 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-07-19 16:57:49.505 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-07-19 16:57:49.507 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[远程抄表水电控]成功
2025-07-19 16:57:49.510 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-07-19 16:57:49.512 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-07-19 16:57:49.516 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-07-19 16:57:49.518 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸权限组定时部门授权]成功
2025-07-19 16:57:49.521 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-07-19 16:57:49.524 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-07-19 16:57:49.526 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-07-19 16:57:49.526 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-07-19 16:57:49.529 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-07-19 16:57:49.559 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.755 seconds (JVM running for 12.893)
2025-07-19 16:57:49.985 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-07-19 16:57:52.252 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:57:52.253 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 16:57:52.254 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-19 16:58:02.396 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-07-19 16:58:13.657 INFO  c.y.campusos.controller.hotel.WECostController - 删除设备读数 - UID: b25bd306-24b3-4394-a9f5-49d1c6e9fb35
2025-07-19 16:58:13.676 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数记录删除成功
2025-07-19 16:58:13.698 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算记录删除成功
2025-07-19 16:58:17.076 INFO  c.y.campusos.controller.hotel.WECostController - 删除设备读数 - UID: 4988e34e-9ea8-4b70-a564-727c85cb559b
2025-07-19 16:58:17.093 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数记录删除成功
2025-07-19 16:58:17.112 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算记录删除成功
2025-07-19 16:58:21.065 INFO  c.y.campusos.controller.hotel.WECostController - 删除设备读数 - UID: 6321c03f-1c71-4d0f-bb86-e3a06314a733
2025-07-19 16:58:21.082 INFO  c.y.campusos.controller.hotel.WECostController - 设备读数记录删除成功
2025-07-19 16:58:21.099 INFO  c.y.campusos.controller.hotel.WECostController - 费用计算记录删除成功
2025-07-19 17:00:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 16:57:48,686 to 2025-07-19 17:00:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 17:00:00.016 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-07-19 17:00:00.046 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-07-19 17:00:00.070 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-07-19 17:00:00.070 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-07-19 17:00:00.168 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 17:00:00.185 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 17:00:00.208 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 17:00:00.226 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 17:00:00.226 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-07-19 17:15:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-19 17:00:00,004 to 2025-07-19 17:15:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-19 17:20:00.006 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-07-19 17:20:00.031 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-07-19 17:20:00.053 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-07-19 17:20:00.071 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-07-19 17:20:00.072 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
