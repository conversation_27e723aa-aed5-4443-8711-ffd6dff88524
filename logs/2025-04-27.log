2025-04-27 10:06:21.963 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 32820 (D:\yunmai\campusos5.0\campusos\target\classes started by <PERSON><PERSON> in D:\yunmai\campusos5.0)
2025-04-27 10:06:21.963 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-04-27 10:06:21.965 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-04-27 10:06:23.535 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-27 10:06:23.537 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-27 10:06:23.774 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 193 ms. Found 0 Redis repository interfaces.
2025-04-27 10:06:24.170 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$483c9c31] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-27 10:06:24.216 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$c742e1a9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-27 10:06:24.422 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-04-27 10:06:24.431 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-04-27 10:06:24.431 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-27 10:06:24.431 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-27 10:06:24.587 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-27 10:06:24.587 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2557 ms
2025-04-27 10:06:24.671 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-27 10:06:24.723 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-27 10:06:25.644 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-27 10:06:27.314 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-27 10:06:27.400 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-27 10:06:27.400 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-27 10:06:28.075 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-27 10:06:28.212 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-27 10:06:28.238 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-27 10:06:28.243 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-27 10:06:28.320 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-27 10:06:28.704 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-04-27 10:06:28.708 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-04-27 10:06:28.767 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$e1d6d2fe - 计划任务最多执行：48个
2025-04-27 10:06:28.810 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-04-27 10:06:28.812 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-04-27 10:06:28.812 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-04-27 10:06:28.813 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-04-27 10:06:28.813 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-04-27 10:06:28.814 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-04-27 10:06:28.816 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-04-27 10:06:28.818 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-04-27 10:06:28.819 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-04-27 10:06:28.820 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-04-27 10:06:28.822 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-04-27 10:06:28.822 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-04-27 10:06:28.840 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.253 seconds (JVM running for 8.418)
2025-04-27 10:06:29.187 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-04-27 10:11:59.527 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-04-27 10:11:59.535 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-27 10:11:59.535 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-27 10:11:59.537 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-27 10:11:59.592 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-04-27 10:12:00.531 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-04-27 10:12:00.545 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-04-27 10:12:01.880 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:12:01.881 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:12:06.325 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-04-27 10:12:06.328 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-04-27 10:12:09.584 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:12:09.585 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 10:06:28,319 to 2025-04-27 10:15:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 10:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 10:15:00,006 to 2025-04-27 10:30:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 10:34:45.047 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-04-27 10:34:53.703 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 49156 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-27 10:34:53.703 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-04-27 10:34:53.705 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-04-27 10:34:55.283 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-27 10:34:55.285 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-27 10:34:55.514 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 176 ms. Found 0 Redis repository interfaces.
2025-04-27 10:34:55.894 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$743b4e3f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-27 10:34:55.935 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$f34193b7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-27 10:34:56.117 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-04-27 10:34:56.125 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-04-27 10:34:56.125 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-27 10:34:56.125 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-27 10:34:56.291 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-27 10:34:56.291 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2522 ms
2025-04-27 10:34:56.378 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-27 10:34:56.426 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-27 10:34:57.323 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-27 10:34:59.008 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-27 10:34:59.090 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-27 10:34:59.090 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-27 10:34:59.774 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-27 10:34:59.902 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-27 10:34:59.924 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-27 10:34:59.928 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-27 10:34:59.996 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-27 10:35:00.332 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-04-27 10:35:00.337 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-04-27 10:35:00.400 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$dd5850c - 计划任务最多执行：48个
2025-04-27 10:35:00.438 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-04-27 10:35:00.440 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-04-27 10:35:00.441 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-04-27 10:35:00.441 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-04-27 10:35:00.441 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-04-27 10:35:00.442 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-04-27 10:35:00.444 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-04-27 10:35:00.445 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-04-27 10:35:00.447 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-04-27 10:35:00.448 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-04-27 10:35:00.449 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-04-27 10:35:00.450 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-04-27 10:35:00.462 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.111 seconds (JVM running for 8.254)
2025-04-27 10:35:00.801 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-04-27 10:35:06.235 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-04-27 10:35:06.248 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-27 10:35:06.248 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-27 10:35:06.250 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-27 10:35:07.152 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:35:07.152 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:35:07.242 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[a818cd62-5611-11ea-9e31-4cedfb0db910]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-04-27 10:35:24.646 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:35:24.647 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:35:29.223 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:35:29.223 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:35:29.244 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[a818cd62-5611-11ea-9e31-4cedfb0db910]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-04-27 10:36:15.700 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:36:15.701 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:36:20.189 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:36:20.190 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:36:33.593 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:36:33.594 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:36:37.075 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:36:37.075 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:38:06.901 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:38:06.901 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:41:32.773 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-04-27 10:41:32.913 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-04-27 10:41:32.916 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-04-27 10:41:36.247 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 17472 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-27 10:41:36.247 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-04-27 10:41:36.249 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-04-27 10:41:37.912 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-27 10:41:37.914 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-27 10:41:38.122 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 154 ms. Found 0 Redis repository interfaces.
2025-04-27 10:41:38.498 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$135d1711] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-27 10:41:38.549 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$92635c89] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-27 10:41:38.735 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-04-27 10:41:38.742 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-04-27 10:41:38.742 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-27 10:41:38.744 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-27 10:41:38.900 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-27 10:41:38.900 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2576 ms
2025-04-27 10:41:38.975 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-27 10:41:39.028 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-27 10:41:39.879 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-27 10:41:41.430 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-27 10:41:41.512 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-27 10:41:41.512 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-27 10:41:42.151 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-27 10:41:42.276 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-27 10:41:42.296 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-27 10:41:42.301 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-27 10:41:42.367 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-27 10:41:42.683 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-04-27 10:41:42.687 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-04-27 10:41:42.737 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$acf74dde - 计划任务最多执行：48个
2025-04-27 10:41:42.775 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-04-27 10:41:42.777 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-04-27 10:41:42.777 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-04-27 10:41:42.777 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-04-27 10:41:42.778 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-04-27 10:41:42.779 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-04-27 10:41:42.780 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-04-27 10:41:42.782 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-04-27 10:41:42.783 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-04-27 10:41:42.784 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-04-27 10:41:42.787 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-04-27 10:41:42.787 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-04-27 10:41:42.795 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-04-27 10:41:42.800 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.914 seconds (JVM running for 8.541)
2025-04-27 10:41:42.800 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-27 10:41:42.800 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-27 10:41:42.801 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-27 10:41:42.841 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-04-27 10:41:43.149 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-04-27 10:42:09.879 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:42:09.879 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:42:29.843 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:42:29.843 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:42:29.932 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[a818cd62-5611-11ea-9e31-4cedfb0db910]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-04-27 10:42:38.379 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:42:38.379 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:43:32.278 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-04-27 10:43:32.419 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-04-27 10:43:32.426 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-04-27 10:43:37.746 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-04-27 10:43:37.746 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 36212 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-27 10:43:37.748 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-04-27 10:43:39.283 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-27 10:43:39.284 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-27 10:43:39.497 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 160 ms. Found 0 Redis repository interfaces.
2025-04-27 10:43:39.872 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1aeb8394] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-27 10:43:39.913 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$99f1c90c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-27 10:43:40.096 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-04-27 10:43:40.104 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-04-27 10:43:40.105 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-27 10:43:40.105 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-27 10:43:40.263 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-27 10:43:40.263 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2442 ms
2025-04-27 10:43:40.341 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-27 10:43:40.398 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-27 10:43:41.290 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-27 10:43:42.867 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-27 10:43:42.969 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-27 10:43:42.967 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-27 10:43:43.591 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-27 10:43:43.715 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-27 10:43:43.736 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-27 10:43:43.740 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-27 10:43:43.806 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-27 10:43:44.146 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-04-27 10:43:44.151 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-04-27 10:43:44.199 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$b485ba61 - 计划任务最多执行：48个
2025-04-27 10:43:44.234 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-04-27 10:43:44.236 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-04-27 10:43:44.236 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-04-27 10:43:44.237 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-04-27 10:43:44.237 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-04-27 10:43:44.238 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-04-27 10:43:44.240 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-04-27 10:43:44.242 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-04-27 10:43:44.243 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-04-27 10:43:44.244 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-04-27 10:43:44.246 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-04-27 10:43:44.246 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-04-27 10:43:44.258 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.864 seconds (JVM running for 7.984)
2025-04-27 10:43:44.659 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-04-27 10:43:53.017 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-04-27 10:43:53.023 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-27 10:43:53.023 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-27 10:43:53.025 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-27 10:44:18.151 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:44:18.151 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:44:23.808 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:44:23.809 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:44:36.081 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:44:36.081 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:45:03.279 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 10:43:43,805 to 2025-04-27 10:45:03,276
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 10:45:03.475 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-04-27 10:45:03.626 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-04-27 10:45:03.637 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-04-27 10:45:08.693 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 5068 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-27 10:45:08.693 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-04-27 10:45:08.695 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-04-27 10:45:10.187 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-27 10:45:10.188 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-27 10:45:10.404 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 169 ms. Found 0 Redis repository interfaces.
2025-04-27 10:45:10.801 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$65870628] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-27 10:45:10.846 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e48d4ba0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-04-27 10:45:11.030 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-04-27 10:45:11.037 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-04-27 10:45:11.038 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-27 10:45:11.038 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-27 10:45:11.197 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-27 10:45:11.197 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2425 ms
2025-04-27 10:45:11.274 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-27 10:45:11.330 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-27 10:45:12.242 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-27 10:45:13.974 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-27 10:45:14.050 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-27 10:45:14.050 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-27 10:45:14.694 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-04-27 10:45:14.818 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-27 10:45:14.837 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-27 10:45:14.843 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-27 10:45:14.915 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-27 10:45:15.277 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-04-27 10:45:15.281 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-04-27 10:45:15.339 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$ff213cf5 - 计划任务最多执行：48个
2025-04-27 10:45:15.382 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-04-27 10:45:15.385 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-04-27 10:45:15.385 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-04-27 10:45:15.385 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[同步考勤人员部门数据]成功
2025-04-27 10:45:15.385 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-04-27 10:45:15.387 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-04-27 10:45:15.389 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-04-27 10:45:15.390 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-04-27 10:45:15.391 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-04-27 10:45:15.392 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-04-27 10:45:15.394 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-04-27 10:45:15.394 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-04-27 10:45:15.413 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.1 seconds (JVM running for 8.19)
2025-04-27 10:45:15.893 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-04-27 10:45:19.442 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-04-27 10:45:19.449 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-27 10:45:19.449 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-27 10:45:19.451 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-27 10:45:19.493 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:45:19.493 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:45:25.175 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 10:45:25.176 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 10:45:25.255 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[a818cd62-5611-11ea-9e31-4cedfb0db910]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-04-27 11:00:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 10:45:14,913 to 2025-04-27 11:00:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 11:01:00.004 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 11:01:00.006 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 11:01:00.075 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-04-27 11:01:00.075 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-04-27 11:01:00.075 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-04-27计划任务[TASK000056]错误
2025-04-27 11:15:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 11:00:00,006 to 2025-04-27 11:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 11:30:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 11:15:00,015 to 2025-04-27 11:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 11:45:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 11:30:00,015 to 2025-04-27 11:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 12:00:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 11:45:00,012 to 2025-04-27 12:00:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 12:01:00.001 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 12:01:00.002 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 12:01:00.017 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-04-27 12:01:00.017 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-04-27 12:01:00.017 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-04-27计划任务[TASK000056]错误
2025-04-27 12:20:00.469 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 12:00:00,005 to 2025-04-27 12:20:00,469
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 12:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 12:20:00,469 to 2025-04-27 12:30:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 12:45:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 12:30:00,017 to 2025-04-27 12:45:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 13:36:56.646 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 12:45:00,013 to 2025-04-27 13:36:56,646
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 13:36:56.646 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 13:36:56,646 to 2025-04-27 13:36:56,646
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 13:36:56.646 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 13:36:56,646 to 2025-04-27 13:36:56,646
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 13:42:57.108 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 13:42:57.108 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 13:42:57.117 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-04-27 13:42:57.117 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-04-27 13:42:57.117 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-04-27计划任务[TASK000056]错误
2025-04-27 13:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 13:36:56,646 to 2025-04-27 13:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 14:00:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 13:45:00,016 to 2025-04-27 14:00:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 14:01:00.007 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 14:01:00.008 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 14:01:00.018 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-04-27 14:01:00.018 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-04-27 14:01:00.018 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-04-27计划任务[TASK000056]错误
2025-04-27 14:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 14:00:00,007 to 2025-04-27 14:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 14:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 14:15:00,010 to 2025-04-27 14:30:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 14:45:00.003 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 14:30:00,017 to 2025-04-27 14:45:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 15:00:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 14:45:00,003 to 2025-04-27 15:00:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 15:01:00.014 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 15:01:00.015 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 15:01:00.029 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-04-27 15:01:00.029 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-04-27 15:01:00.030 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-04-27计划任务[TASK000056]错误
2025-04-27 15:15:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 15:00:00,017 to 2025-04-27 15:15:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 15:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 15:15:00,013 to 2025-04-27 15:30:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 15:37:04.824 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 36512 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-04-27 15:37:04.826 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-04-27 15:37:05.892 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-27 15:37:05.894 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-04-27 15:37:05.983 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 79 ms. Found 0 Redis repository interfaces.
2025-04-27 15:37:06.631 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-04-27 15:37:06.639 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-04-27 15:37:06.639 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-04-27 15:37:06.640 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-04-27 15:37:06.861 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-27 15:37:06.862 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1995 ms
2025-04-27 15:37:06.947 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-27 15:37:07.003 WARN  com.alibaba.druid.pool.DruidAbstractDataSource - keepAliveBetweenTimeMillis should be greater than timeBetweenEvictionRunsMillis
2025-04-27 15:37:07.889 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-27 15:37:08.772 INFO  org.redisson.Version - Redisson 3.12.5
2025-04-27 15:37:09.108 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-04-27 15:37:09.108 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-04-27 15:37:10.317 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-04-27 15:37:10.420 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-04-27 15:37:10.449 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-04-27 15:37:10.455 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-04-27 15:37:10.620 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-04-27 15:37:10.932 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-04-27 15:37:10.940 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-04-27 15:37:11.042 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 6.601 seconds (JVM running for 7.795)
2025-04-27 15:37:14.252 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 15:37:14.253 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 15:37:18.845 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 15:37:18.845 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 15:37:20.007 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-04-27 15:37:21.820 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-04-27 15:37:21.820 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 15:37:21.839 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[2]用户[a81adcff-5611-11ea-9e31-4cedfb0db910]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-04-27 15:38:20.272 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-27 15:38:20.273 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-27 15:38:20.274 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-27 15:38:27.618 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"935D65066D14C61D11CC39C53F57E52A","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1745739507615}
2025-04-27 15:38:28.583 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-04-27 15:38:29.779 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-04-27 15:38:29.779 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 15:40:02.181 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-04-27 15:40:02.181 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 15:40:31.415 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-04-27 15:40:31.415 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 15:40:42.058 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-04-27 15:40:42.059 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-04-27 15:45:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 15:30:00,013 to 2025-04-27 15:45:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 15:45:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 15:37:10,619 to 2025-04-27 15:45:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 16:00:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 15:45:00,006 to 2025-04-27 16:00:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 16:00:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 15:45:00,005 to 2025-04-27 16:00:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 16:00:00.004 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-04-27 16:01:00.008 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 16:01:00.018 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 16:01:00.032 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-04-27 16:01:00.032 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-04-27 16:01:00.032 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-04-27计划任务[TASK000056]错误
2025-04-27 16:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 16:00:00,002 to 2025-04-27 16:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 16:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 16:00:00,002 to 2025-04-27 16:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 16:30:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 16:15:00,007 to 2025-04-27 16:30:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 16:30:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 16:15:00,007 to 2025-04-27 16:30:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 16:45:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 16:30:00,014 to 2025-04-27 16:45:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 16:45:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 16:30:00,014 to 2025-04-27 16:45:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 17:00:00.001 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-04-27 17:00:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 16:45:00,005 to 2025-04-27 17:00:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 17:00:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 16:45:00,005 to 2025-04-27 17:00:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 17:01:00.006 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 17:01:00.022 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 17:01:00.035 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-04-27 17:01:00.035 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-04-27 17:01:00.036 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-04-27计划任务[TASK000056]错误
2025-04-27 17:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 17:00:00,001 to 2025-04-27 17:15:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 17:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 17:00:00,017 to 2025-04-27 17:15:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 17:30:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 17:15:00,008 to 2025-04-27 17:30:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 17:30:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 17:15:00,008 to 2025-04-27 17:30:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 17:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 17:30:00,009 to 2025-04-27 17:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 17:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 17:30:00,009 to 2025-04-27 17:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 18:00:00.001 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-04-27 18:00:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 17:45:00,016 to 2025-04-27 18:00:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 18:00:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 17:45:00,016 to 2025-04-27 18:00:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 18:01:00.008 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 准备执行计划任务[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 18:01:00.018 INFO  com.ymiots.campusos.task.SystemDynamicTasks - 计划任务线程已启动[TASK000056]call sync_teachstudinfo_dept_tongbu()
2025-04-27 18:01:00.027 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[call sync_teachstudinfo_dept_tongbu()]
2025-04-27 18:01:00.027 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; bad SQL grammar [call sync_teachstudinfo_dept_tongbu()]; nested exception is java.sql.SQLSyntaxErrorException: PROCEDURE xtcampusos.sync_teachstudinfo_dept_tongbu does not exist
2025-04-27 18:01:00.027 ERROR com.ymiots.campusos.task.SystemDynamicTasks - 分析2025-04-27计划任务[TASK000056]错误
2025-04-27 18:01:59.499 INFO  org.apache.coyote.http11.Http11Processor - Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in method name [0x160x030x010x00{0x010x000x00w0x030x03W0xb20xc2d0xa60xfb0xd1o30xf9#0xd50x04h0xee0xb50x82E0x1950x870xdf-0x860xd5Av0x0cZ@F0xce0x000x000x1a0xc0/0xc0+0xc00x110xc00x070xc00x130xc00x090xc00x140xc00x0a0x000x050x00/0x0050xc00x120x000x0a0x010x000x0040x000x050x000x050x010x000x000x000x000x000x0a0x000x080x000x060x000x170x000x180x000x190x000x0b0x000x020x010x000x000x0d0x000x100x000x0e0x040x010x040x030x020x010x020x030x040x010x050x010x060x010xff0x010x000x010x00...]. HTTP method names must be tokens
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:419)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:271)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-04-27 18:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 18:00:00,006 to 2025-04-27 18:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-04-27 18:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-04-27 18:00:00,006 to 2025-04-27 18:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

