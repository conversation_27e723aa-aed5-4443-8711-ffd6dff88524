2025-01-08 12:02:49.444 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-08 12:02:49.446 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 39692 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-08 12:02:49.447 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-08 12:02:51.083 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-08 12:02:51.086 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-08 12:02:51.223 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 124 ms. Found 0 Redis repository interfaces.
2025-01-08 12:02:51.620 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$8c3e2b3b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-08 12:02:51.661 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$b4470b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-08 12:02:51.856 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-08 12:02:51.865 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-08 12:02:51.865 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-08 12:02:51.865 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-08 12:02:52.015 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-08 12:02:52.015 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2521 ms
2025-01-08 12:02:52.091 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-08 12:02:52.352 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-08 12:02:53.579 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-08 12:02:54.029 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-08 12:02:54.029 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-08 12:02:54.691 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-08 12:02:54.833 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-08 12:02:54.857 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-08 12:02:54.863 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-08 12:02:55.028 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-08 12:02:55.354 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-08 12:02:55.358 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-08 12:02:55.416 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$25d86208 - 计划任务最多执行：1个
2025-01-08 12:02:55.439 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.NoSuchBeanDefinitionException: No bean named 'AutoGenerateCardNumberAndFaceDownTask' available
2025-01-08 12:02:55.442 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-01-08 12:02:55.514 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-08 12:02:55.519 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-08 12:02:55.520 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-08 12:02:55.523 ERROR org.springframework.web.servlet.DispatcherServlet - Context initialization failed
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'localeResolver': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1160)
	at org.springframework.web.servlet.DispatcherServlet.initLocaleResolver(DispatcherServlet.java:539)
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:498)
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:489)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1164)
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:804)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-01-08 12:02:55.523 ERROR o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Servlet.init() for servlet [dispatcherServlet] threw exception
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'localeResolver': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1160)
	at org.springframework.web.servlet.DispatcherServlet.initLocaleResolver(DispatcherServlet.java:539)
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:498)
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:489)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1164)
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:804)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-01-08 12:02:55.524 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Allocate exception for servlet [dispatcherServlet]
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'localeResolver': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1160)
	at org.springframework.web.servlet.DispatcherServlet.initLocaleResolver(DispatcherServlet.java:539)
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:498)
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:489)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1164)
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:804)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-01-08 12:02:55.529 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-08 12:02:55.529 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-08 12:02:55.529 ERROR org.springframework.web.servlet.DispatcherServlet - Context initialization failed
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'localeResolver': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1160)
	at org.springframework.web.servlet.DispatcherServlet.initLocaleResolver(DispatcherServlet.java:539)
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:498)
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:489)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1164)
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:804)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:687)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:461)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:403)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:249)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:344)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:169)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-01-08 12:02:55.531 ERROR o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Servlet.init() for servlet [dispatcherServlet] threw exception
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'localeResolver': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1160)
	at org.springframework.web.servlet.DispatcherServlet.initLocaleResolver(DispatcherServlet.java:539)
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:498)
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:489)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1164)
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:804)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:687)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:461)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:403)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:249)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:344)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:169)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-01-08 12:02:55.531 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] - Allocate exception for servlet [dispatcherServlet]
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'localeResolver': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1160)
	at org.springframework.web.servlet.DispatcherServlet.initLocaleResolver(DispatcherServlet.java:539)
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:498)
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:489)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1164)
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:804)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:687)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:461)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:403)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:249)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:344)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:169)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-01-08 12:02:55.531 ERROR o.a.c.core.ContainerBase.[Tomcat].[localhost] - Exception Processing ErrorPage[errorCode=0, location=/error]
javax.servlet.ServletException: Servlet.init() for servlet [dispatcherServlet] threw exception
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1181)
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:804)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:687)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:461)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at org.apache.catalina.core.StandardHostValve.custom(StandardHostValve.java:403)
	at org.apache.catalina.core.StandardHostValve.status(StandardHostValve.java:249)
	at org.apache.catalina.core.StandardHostValve.throwable(StandardHostValve.java:344)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:169)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'localeResolver': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1160)
	at org.springframework.web.servlet.DispatcherServlet.initLocaleResolver(DispatcherServlet.java:539)
	at org.springframework.web.servlet.DispatcherServlet.initStrategies(DispatcherServlet.java:498)
	at org.springframework.web.servlet.DispatcherServlet.onRefresh(DispatcherServlet.java:489)
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:599)
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530)
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170)
	at javax.servlet.GenericServlet.init(GenericServlet.java:158)
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1164)
	... 21 common frames omitted
2025-01-08 12:02:55.619 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-01-08 12:02:55.626 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-01-08 12:02:55.863 INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-2018"]
2025-01-08 12:02:55.863 INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-08 12:02:55.902 INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-2018"]
2025-01-08 12:02:55.905 INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-2018"]
2025-01-08 12:02:55.907 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-01-08 12:02:55.949 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean named 'AutoGenerateCardNumberAndFaceDownTask' that could not be found.


Action:

Consider defining a bean named 'AutoGenerateCardNumberAndFaceDownTask' in your configuration.

2025-01-08 12:03:32.683 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 40732 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-08 12:03:32.682 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-08 12:03:32.685 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-08 12:03:34.252 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-08 12:03:34.255 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-08 12:03:34.405 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 138 ms. Found 0 Redis repository interfaces.
2025-01-08 12:03:34.807 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$7fda39ff] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-08 12:03:34.853 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$fee07f77] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-08 12:03:35.062 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-08 12:03:35.070 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-08 12:03:35.070 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-08 12:03:35.071 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-08 12:03:35.234 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-08 12:03:35.234 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2506 ms
2025-01-08 12:03:35.311 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-08 12:03:35.572 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-08 12:03:36.781 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-08 12:03:37.226 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-08 12:03:37.226 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-08 12:03:37.863 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-08 12:03:38.010 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-08 12:03:38.034 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-08 12:03:38.039 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-08 12:03:38.203 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-08 12:03:38.535 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-08 12:03:38.539 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-08 12:03:38.593 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$197470cc - 计划任务最多执行：0个
2025-01-08 12:03:38.613 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 6.286 seconds (JVM running for 7.223)
2025-01-08 12:03:39.132 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-08 12:03:42.992 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-08 12:03:42.997 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-08 12:03:42.997 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-08 12:03:42.999 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-01-08 12:04:03.930 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-08 12:15:00.020 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 12:03:38,202 to 2025-01-08 12:15:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 12:27:08.685 ERROR c.y.campusos.service.card.TeachStudCardService - 617:
2025-01-08 12:30:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 12:15:00,017 to 2025-01-08 12:30:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 12:45:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 12:30:00,002 to 2025-01-08 12:45:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 13:13:32.747 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 12:45:00,005 to 2025-01-08 13:13:32,747
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 13:15:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 13:13:32,747 to 2025-01-08 13:15:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 13:30:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 13:15:00,014 to 2025-01-08 13:30:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 13:45:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 13:30:00,010 to 2025-01-08 13:45:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 14:00:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 13:45:00,006 to 2025-01-08 14:00:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 14:15:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 14:00:00,012 to 2025-01-08 14:15:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 14:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 14:15:00,008 to 2025-01-08 14:30:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 14:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 14:30:00,013 to 2025-01-08 14:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 15:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 14:45:00,016 to 2025-01-08 15:00:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 15:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 15:00:00,015 to 2025-01-08 15:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 15:30:00.001 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 15:15:00,010 to 2025-01-08 15:30:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 15:45:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 15:30:00,001 to 2025-01-08 15:45:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 16:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 15:45:00,017 to 2025-01-08 16:00:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 16:15:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 16:00:00,007 to 2025-01-08 16:15:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 16:25:26.676 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[]
2025-01-08 16:25:26.677 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select count(1) as total from tb_card_teachstudinfo_money a left join tb_card_teachstudinfo b on a.infoid = b.uid where  1=1 ]; nested exception is java.sql.SQLSyntaxErrorException: Table 'campusos.tb_card_teachstudinfo_money' doesn't exist
2025-01-08 16:25:26.679 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-01-08 16:25:51.710 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select ea.permissions,ea.uid, ea.infoid, ea.cardno, ea.infoname, ea.infoindex, ea.isvisitor, ea.passpassword as pass, ea.idcard, ea.devid, ea.machineid, ea.weekuid, ea.storeys, date_format(ea.starttime, '%Y-%m-%d %H:%i:%s') as starttime,date_format(ea.endtime, '%Y-%m-%d %H:%i:%s') as endtime, ea.downmsg, ea.downnum, ea.status,d.name as statusname ,ea.numberlimit as number  from tb_elevator_s_authrize ea LEFT JOIN tb_sys_dictionary d on d.code=ea.status and d.groupcode='SYS0000025'  where 1=1  order by createdate DESC limit 0,50]
2025-01-08 16:25:51.710 ERROR com.ymiots.framework.datafactory.DataBaseFactory - StatementCallback; bad SQL grammar [select ea.permissions,ea.uid, ea.infoid, ea.cardno, ea.infoname, ea.infoindex, ea.isvisitor, ea.passpassword as pass, ea.idcard, ea.devid, ea.machineid, ea.weekuid, ea.storeys, date_format(ea.starttime, '%Y-%m-%d %H:%i:%s') as starttime,date_format(ea.endtime, '%Y-%m-%d %H:%i:%s') as endtime, ea.downmsg, ea.downnum, ea.status,d.name as statusname ,ea.numberlimit as number  from tb_elevator_s_authrize ea LEFT JOIN tb_sys_dictionary d on d.code=ea.status and d.groupcode='SYS0000025'  where 1=1  order by createdate DESC limit 0,50]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'ea.numberlimit' in 'field list'
2025-01-08 16:25:51.710 ERROR com.ymiots.framework.datafactory.DataBaseFactory - 263:SQL执行错误，详情请查询日志
2025-01-08 16:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 16:15:00,003 to 2025-01-08 16:30:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 16:45:00.002 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 16:30:00,017 to 2025-01-08 16:45:00,002
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 17:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 16:45:00,002 to 2025-01-08 17:00:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 17:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 17:00:00,015 to 2025-01-08 17:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-08 17:30:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-08 17:15:00,010 to 2025-01-08 17:30:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

