2025-02-20 16:38:30.579 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 45172 (D:\yunmai\campusos5.0\campusos\target\classes started by <PERSON><PERSON> in D:\yunmai\campusos5.0)
2025-02-20 16:38:30.579 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-20 16:38:30.583 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-20 16:38:32.361 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-20 16:38:32.363 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-20 16:38:32.519 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 146 ms. Found 0 Redis repository interfaces.
2025-02-20 16:38:32.937 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$d99b4626] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-20 16:38:32.981 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$58a18b9e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-20 16:38:33.173 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-20 16:38:33.180 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-20 16:38:33.181 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-20 16:38:33.181 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-20 16:38:33.320 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-20 16:38:33.320 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2688 ms
2025-02-20 16:38:33.400 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-20 16:38:33.677 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-20 16:38:35.672 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-20 16:38:36.325 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-20 16:38:36.325 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-20 16:38:37.034 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-20 16:38:37.197 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-20 16:38:37.223 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-20 16:38:37.230 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-20 16:38:37.404 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-20 16:38:37.772 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-20 16:38:37.777 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-20 16:38:37.849 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$73357cf3 - 计划任务最多执行：0个
2025-02-20 16:38:37.874 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.673 seconds (JVM running for 8.883)
2025-02-20 16:38:38.409 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-20 16:45:00.018 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-20 16:38:37,402 to 2025-02-20 16:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-20 16:45:56.768 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-20 16:45:56.774 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-20 16:45:56.774 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-20 16:45:56.777 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-02-20 16:46:01.620 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-02-20 17:00:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-20 16:45:00,016 to 2025-02-20 17:00:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-20 17:15:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-20 17:00:00,008 to 2025-02-20 17:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-20 17:30:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-20 17:15:00,007 to 2025-02-20 17:30:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

