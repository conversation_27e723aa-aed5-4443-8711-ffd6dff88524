2025-02-19 17:28:38.958 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 48156 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tan<PERSON> in D:\yunmai\campusos5.0)
2025-02-19 17:28:38.962 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-02-19 17:28:54.048 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 51452 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-19 17:28:54.051 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-19 17:28:54.053 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-19 17:28:56.175 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-19 17:28:56.178 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-19 17:28:56.378 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 183 ms. Found 0 Redis repository interfaces.
2025-02-19 17:28:56.923 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$55b959eb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 17:28:56.979 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d4bf9f63] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 17:28:57.242 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-19 17:28:57.254 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-19 17:28:57.255 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-19 17:28:57.255 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-19 17:28:57.489 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-19 17:28:57.489 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3386 ms
2025-02-19 17:28:57.590 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-19 17:28:57.954 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-19 17:29:00.530 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-19 17:29:01.337 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-19 17:29:01.337 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-19 17:29:02.547 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-19 17:29:02.787 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-19 17:29:02.834 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-19 17:29:02.840 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-19 17:29:03.077 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-19 17:29:03.569 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-19 17:29:03.577 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-19 17:29:03.668 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$ef5390b8 - 计划任务最多执行：0个
2025-02-19 17:29:03.721 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 10.107 seconds (JVM running for 11.439)
2025-02-19 17:29:04.230 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-19 17:29:14.700 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-19 17:29:14.713 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-19 17:29:14.713 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-19 17:29:14.716 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-02-19 17:29:16.530 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-02-19 17:29:20.338 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-02-19 17:29:20.357 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-02-19 17:29:32.121 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-02-19 17:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-19 17:29:03,074 to 2025-02-19 17:30:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

