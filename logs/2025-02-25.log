2025-02-25 08:45:00.794 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-25 08:45:00.797 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 29424 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-25 08:45:00.797 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-25 08:45:03.940 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-25 08:45:03.943 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-25 08:45:04.237 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 292 ms. Found 0 Redis repository interfaces.
2025-02-25 08:45:04.869 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$11a26459] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 08:45:04.911 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$90a8a9d1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 08:45:05.117 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-25 08:45:05.136 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-25 08:45:05.136 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-25 08:45:05.136 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-25 08:45:05.359 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-25 08:45:05.359 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4505 ms
2025-02-25 08:45:05.438 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-25 08:45:05.711 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-25 08:45:08.172 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-25 08:45:08.977 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-25 08:45:08.979 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-25 08:45:09.932 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-25 08:45:10.092 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-25 08:45:10.130 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-25 08:45:10.132 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-25 08:45:10.344 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-25 08:45:10.948 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-25 08:45:10.964 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-25 08:45:11.046 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$ab3c9b26 - 计划任务最多执行：0个
2025-02-25 08:45:11.112 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 10.832 seconds (JVM running for 12.629)
2025-02-25 08:45:11.116 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-25 08:45:11.116 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-25 08:45:11.116 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-25 08:45:11.124 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2025-02-25 08:45:11.211 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统授权许可证校验失败
2025-02-25 08:45:11.859 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-25 08:45:23.487 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-02-25 08:45:34.467 ERROR com.alibaba.druid.pool.DruidPooledStatement - CommunicationsException, druid version 1.2.17, jdbcUrl : ****************************************************************************************************************, testWhileIdle true, idle millis 10021, minIdle 10, poolingCount 19, timeBetweenEvictionRunsMillis 60000, lastValidIdleMillis 10021, driver com.mysql.cj.jdbc.Driver, exceptionSorter com.alibaba.druid.pool.vendor.MySqlExceptionSorter
2025-02-25 08:45:34.467 ERROR com.alibaba.druid.pool.DruidDataSource - {conn-10020} discard
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 10,019 milliseconds ago. The last packet sent successfully to the server was 10,019 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:972)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeQuery(DruidPooledPreparedStatement.java:213)
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:722)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:651)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:713)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:744)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:757)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:810)
	at org.springframework.jdbc.core.JdbcTemplate.queryForList(JdbcTemplate.java:942)
	at com.ymiots.framework.datafactory.DataBaseFactory.QueryList(DataBaseFactory.java:118)
	at com.ymiots.framework.datafactory.DataBaseFactory.QueryObject(DataBaseFactory.java:148)
	at com.ymiots.framework.datafactory.DataBaseFactory$$FastClassBySpringCGLIB$$d1c4ff69.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ymiots.framework.datafactory.DataBaseFactory$$EnhancerBySpringCGLIB$$9ebc6d05.QueryObject(<generated>)
	at com.ymiots.campusos.common.DBService.QueryObject(DBService.java:74)
	at com.ymiots.campusos.common.DBService$$FastClassBySpringCGLIB$$d2b1dff7.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ymiots.campusos.common.DBService$$EnhancerBySpringCGLIB$$8cd4d76f.QueryObject(<generated>)
	at com.ymiots.campusos.service.card.LeaveRecordService.getHomePage2(LeaveRecordService.java:1182)
	at com.ymiots.campusos.service.card.LeaveRecordService$$FastClassBySpringCGLIB$$b70960ef.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ymiots.campusos.service.card.LeaveRecordService$$EnhancerBySpringCGLIB$$eb37b557.getHomePage2(<generated>)
	at com.ymiots.campusos.controller.card.LeaveRecordController.getHomePage2(LeaveRecordController.java:142)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1070)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 10,019 milliseconds ago. The last packet sent successfully to the server was 10,019 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:761)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:700)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:1051)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:665)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:893)
	... 94 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 99 common frames omitted
2025-02-25 08:45:34.514 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[SELECT SUM(money) as sum FROM tb_consume_payrecords_sleep WHERE DATE_FORMAT(recordtime, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m')]
2025-02-25 08:45:34.514 ERROR com.ymiots.framework.datafactory.DataBaseFactory - PreparedStatementCallback; SQL [SELECT SUM(money) as sum FROM tb_consume_payrecords_sleep WHERE DATE_FORMAT(recordtime, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m')]; Communications link failure

The last packet successfully received from the server was 10,019 milliseconds ago. The last packet sent successfully to the server was 10,019 milliseconds ago.; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 10,019 milliseconds ago. The last packet sent successfully to the server was 10,019 milliseconds ago.
2025-02-25 08:46:23.659 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 64856 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-25 08:46:23.657 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-25 08:46:23.659 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-25 08:46:26.047 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-25 08:46:26.047 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-25 08:46:26.420 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 363 ms. Found 0 Redis repository interfaces.
2025-02-25 08:46:27.007 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$9e244a37] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 08:46:27.056 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$1d2a8faf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 08:46:27.269 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-25 08:46:27.278 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-25 08:46:27.278 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-25 08:46:27.278 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-25 08:46:27.501 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-25 08:46:27.501 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3795 ms
2025-02-25 08:46:27.582 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-25 08:46:27.835 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-25 08:46:29.889 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-25 08:46:30.424 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-25 08:46:30.424 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-25 08:46:31.107 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-25 08:46:31.259 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-25 08:46:31.274 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-25 08:46:31.290 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-25 08:46:31.530 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-25 08:46:32.171 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-25 08:46:32.174 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-25 08:46:32.263 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$37be8104 - 计划任务最多执行：0个
2025-02-25 08:46:32.285 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.002 seconds (JVM running for 10.635)
2025-02-25 08:46:32.796 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-25 08:49:37.050 INFO  com.ymiots.campusos.redis.CardRecordMsgSubscribe - 订阅主题【/service/recordlist】接收到消息：{"Data":{"Recordtime":"2025-02-25 08:48:45","Machineid":"15","Uid":"4c07fda0-005f-4ad4-a9a3-1045bfb1a77b","Infoid":"a657d10f-a302-45b3-baa0-fada336412dd","Persontype":2,"Readhead":0,"usedType":22}}
2025-02-25 08:49:37.050 ERROR com.ymiots.campusos.redis.CardRecordMsgSubscribe - UsedType===0
2025-02-25 08:54:06.348 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-25 08:54:06.364 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-25 08:54:06.365 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-25 08:54:06.395 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 30 ms
2025-02-25 08:54:12.849 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-02-25 08:55:19.425 INFO  com.ymiots.campusos.redis.CardRecordMsgSubscribe - 订阅主题【/service/recordlist】接收到消息：{"Data":{"Recordtime":"2025-02-25 08:54:59","Machineid":"15","Uid":"6a710fbe-ef6a-4787-a281-d3eead723638","Infoid":"a657d10f-a302-45b3-baa0-fada336412dd","Persontype":2,"Readhead":0,"usedType":22}}
2025-02-25 08:55:19.425 ERROR com.ymiots.campusos.redis.CardRecordMsgSubscribe - UsedType===0
2025-02-25 08:59:44.335 INFO  com.ymiots.campusos.redis.CardRecordMsgSubscribe - 订阅主题【/service/recordlist】接收到消息：{"Data":{"Recordtime":"2025-02-25 08:59:23","Machineid":"15","Uid":"1dc2cc0a-e42e-4af8-9711-0a0e1d12ce17","Infoid":"a657d10f-a302-45b3-baa0-fada336412dd","Persontype":2,"Readhead":0,"usedType":22}}
2025-02-25 08:59:44.335 ERROR com.ymiots.campusos.redis.CardRecordMsgSubscribe - UsedType===0
2025-02-25 09:00:00.020 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 08:46:31,527 to 2025-02-25 09:00:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 09:00:38.245 INFO  com.ymiots.campusos.redis.CardRecordMsgSubscribe - 订阅主题【/service/recordlist】接收到消息：{"Data":{"Recordtime":"2025-02-25 09:00:19","Machineid":"15","Uid":"74bd6edd-f2fb-4b7b-b019-fa5bc43c9bf7","Infoid":"a657d10f-a302-45b3-baa0-fada336412dd","Persontype":2,"Readhead":0,"usedType":22}}
2025-02-25 09:00:38.245 ERROR com.ymiots.campusos.redis.CardRecordMsgSubscribe - UsedType===0
2025-02-25 09:01:13.605 INFO  com.ymiots.campusos.redis.CardRecordMsgSubscribe - 订阅主题【/service/recordlist】接收到消息：{"Data":{"Recordtime":"2025-02-25 09:01:02","Machineid":"15","Uid":"c42a67d9-fd63-472c-9864-4581b8007a02","Infoid":"a657d10f-a302-45b3-baa0-fada336412dd","Persontype":2,"Readhead":0,"usedType":22}}
2025-02-25 09:01:13.605 ERROR com.ymiots.campusos.redis.CardRecordMsgSubscribe - UsedType===0
2025-02-25 09:01:52.337 INFO  com.ymiots.campusos.redis.CardRecordMsgSubscribe - 订阅主题【/service/recordlist】接收到消息：{"Data":{"Recordtime":"2025-02-25 09:01:40","Machineid":"15","Uid":"e262ec9a-cb0d-4604-8d01-04a77a7514e8","Infoid":"a657d10f-a302-45b3-baa0-fada336412dd","Persontype":2,"Readhead":0,"usedType":22}}
2025-02-25 09:01:52.337 ERROR com.ymiots.campusos.redis.CardRecordMsgSubscribe - UsedType===0
2025-02-25 09:03:50.259 INFO  com.ymiots.campusos.redis.CardRecordMsgSubscribe - 订阅主题【/service/recordlist】接收到消息：{"Data":{"Recordtime":"2025-02-25 09:03:38","Machineid":"15","Uid":"42c1483c-93cb-4368-8e90-bbd033be8634","Infoid":"a657d10f-a302-45b3-baa0-fada336412dd","Persontype":2,"Readhead":0,"usedType":22}}
2025-02-25 09:03:50.259 ERROR com.ymiots.campusos.redis.CardRecordMsgSubscribe - UsedType===0
2025-02-25 09:09:15.548 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select status from tb_card_cardinfo where cardno ='001757323232' and status != 0]
2025-02-25 09:09:15.548 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-02-25 09:09:17.558 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select status from tb_card_cardinfo where cardno ='001757323232' and status != 0]
2025-02-25 09:09:17.558 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-02-25 09:15:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 09:00:00,017 to 2025-02-25 09:15:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 09:30:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 09:15:00,005 to 2025-02-25 09:30:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 09:42:09.969 INFO  com.ymiots.campusos.redis.CardRecordMsgSubscribe - 订阅主题【/service/recordlist】接收到消息：{"Data":{"Recordtime":"2025-02-25 09:41:58","Machineid":"15","Uid":"d072cbe4-2c09-413c-b993-525d17d6b2c3","Infoid":"a657d10f-a302-45b3-baa0-fada336412dd","Persontype":2,"Readhead":0,"usedType":22}}
2025-02-25 09:42:09.969 ERROR com.ymiots.campusos.redis.CardRecordMsgSubscribe - UsedType===0
2025-02-25 09:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 09:30:00,005 to 2025-02-25 09:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 10:00:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 09:45:00,016 to 2025-02-25 10:00:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 10:15:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 10:00:00,004 to 2025-02-25 10:15:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 10:30:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 10:15:00,009 to 2025-02-25 10:30:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 10:45:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 10:30:00,009 to 2025-02-25 10:45:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 11:00:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 10:45:00,004 to 2025-02-25 11:00:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 11:15:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 11:00:00,016 to 2025-02-25 11:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 11:30:00.032 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 11:15:00,007 to 2025-02-25 11:30:00,022
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 11:45:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 11:30:00,022 to 2025-02-25 11:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 12:00:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 11:45:00,010 to 2025-02-25 12:00:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 12:15:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 12:00:00,004 to 2025-02-25 12:15:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 12:30:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 12:15:00,016 to 2025-02-25 12:30:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 12:45:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 12:30:00,007 to 2025-02-25 12:45:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 13:00:00.003 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 12:45:00,014 to 2025-02-25 13:00:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 13:12:41.773 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-02-25 13:12:42.069 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-02-25 13:12:42.085 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-02-25 17:03:43.201 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 20576 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-25 17:03:43.201 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-25 17:03:43.204 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-25 17:03:45.969 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-25 17:03:45.971 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-25 17:03:46.267 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 284 ms. Found 0 Redis repository interfaces.
2025-02-25 17:03:46.955 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$6b6872e4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 17:03:47.001 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$ea6eb85c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 17:03:47.232 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-25 17:03:47.240 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-25 17:03:47.241 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-25 17:03:47.241 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-25 17:03:47.497 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-25 17:03:47.498 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4238 ms
2025-02-25 17:03:47.589 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-25 17:03:47.908 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-25 17:03:50.277 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-25 17:03:50.856 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-02-25 17:03:50.856 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-02-25 17:03:51.620 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-25 17:03:51.772 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-25 17:03:51.798 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-25 17:03:51.804 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-25 17:03:51.979 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-25 17:03:52.511 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-25 17:03:52.516 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-25 17:03:52.602 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$502a9b1 - 计划任务最多执行：0个
2025-02-25 17:03:52.625 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 9.84 seconds (JVM running for 11.403)
2025-02-25 17:03:53.100 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-02-25 17:03:58.510 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-25 17:03:58.518 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-25 17:03:58.518 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-25 17:03:58.520 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-02-25 17:04:02.610 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-02-25 17:04:02.613 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-02-25 17:04:30.902 INFO  com.ymiots.campusos.redis.CardRecordMsgSubscribe - 订阅主题【/service/recordlist】接收到消息：{"Data":{"Recordtime":"2025-02-25 17:04:20","Machineid":"15","Uid":"1b54b980-ff80-4f3e-8cb5-9c21f42ec6e4","Infoid":"a657d10f-a302-45b3-baa0-fada336412dd","Persontype":2,"Readhead":0,"usedType":22}}
2025-02-25 17:04:30.903 ERROR com.ymiots.campusos.redis.CardRecordMsgSubscribe - UsedType===0
2025-02-25 17:15:00.054 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 17:03:51,978 to 2025-02-25 17:15:00,038
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 17:30:00.007 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-02-25 17:15:00,038 to 2025-02-25 17:30:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-02-25 17:43:13.077 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 79720 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-02-25 17:43:13.077 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-02-25 17:43:13.080 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-02-25 17:43:15.571 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-02-25 17:43:15.573 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-02-25 17:43:15.907 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 320 ms. Found 0 Redis repository interfaces.
2025-02-25 17:43:16.493 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$31b92b15] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 17:43:16.541 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$b0bf708d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-25 17:43:16.760 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-02-25 17:43:16.769 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-02-25 17:43:16.769 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-02-25 17:43:16.770 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-02-25 17:43:17.003 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-02-25 17:43:17.003 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3874 ms
2025-02-25 17:43:17.090 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-02-25 17:43:19.269 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-02-25 17:43:21.337 INFO  org.redisson.Version - Redisson 3.12.5
2025-02-25 17:43:21.934 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /*************:6379
2025-02-25 17:43:21.936 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /*************:6379
2025-02-25 17:43:22.656 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-02-25 17:43:22.802 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-02-25 17:43:22.827 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-02-25 17:43:22.833 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-02-25 17:43:23.096 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-02-25 17:43:23.599 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-02-25 17:43:23.635 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-02-25 17:43:23.750 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-25 17:43:23.757 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-25 17:43:23.757 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-02-25 17:43:23.760 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-02-25 17:43:23.795 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$cb5361e2 - 计划任务最多执行：39个
2025-02-25 17:43:23.808 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-02-25 17:43:23.842 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-02-25 17:43:23.844 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-02-25 17:43:23.870 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 11.185 seconds (JVM running for 12.498)
2025-02-25 17:43:24.372 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
