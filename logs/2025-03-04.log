2025-03-04 15:56:32.513 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 99908 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 15:56:32.516 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-04 15:56:34.632 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 15:56:34.635 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 15:56:34.880 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 233 ms. Found 0 Redis repository interfaces.
2025-03-04 15:56:35.632 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-04 15:56:35.646 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-04 15:56:35.646 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 15:56:35.647 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 15:56:36.111 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 15:56:36.111 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3544 ms
2025-03-04 15:56:36.252 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 15:56:37.371 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 15:56:38.420 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 15:56:38.946 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 15:56:38.946 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 15:56:40.242 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-04 15:56:40.370 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 15:56:40.400 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 15:56:40.407 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 15:56:40.577 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 15:56:41.015 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-04 15:56:41.025 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-04 15:56:41.144 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 9.11 seconds (JVM running for 11.565)
2025-03-04 16:00:00.140 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-04 15:56:40,575 to 2025-03-04 16:00:00,089
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-04 16:00:00.152 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-04 16:15:00.033 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-04 16:00:00,089 to 2025-03-04 16:15:00,023
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-04 16:22:05.648 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 93020 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 16:22:05.650 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-04 16:22:07.741 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 16:22:07.744 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 16:22:08.012 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 253 ms. Found 0 Redis repository interfaces.
2025-03-04 16:22:08.766 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-04 16:22:08.775 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-04 16:22:08.776 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 16:22:08.776 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 16:22:09.169 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 16:22:09.170 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3475 ms
2025-03-04 16:22:09.258 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 16:22:10.314 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 16:22:11.975 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 16:22:12.922 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 16:22:12.921 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 16:22:15.178 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-04 16:22:15.395 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 16:22:15.445 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 16:22:15.458 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 16:22:15.749 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 16:22:16.472 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-04 16:22:16.486 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-04 16:22:16.654 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 11.441 seconds (JVM running for 13.706)
2025-03-04 16:30:00.080 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-04 16:22:15,746 to 2025-03-04 16:30:00,063
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-04 16:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-04 16:30:00,063 to 2025-03-04 16:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-04 16:45:15.321 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 16:45:15.324 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 16:45:15.505 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 179 ms
2025-03-04 16:45:18.787 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"F3F419FD0B6DC3C9590FEEC328138BF7","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1741077918759}
2025-03-04 16:45:20.140 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-03-04 16:45:31.455 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://*************:2024/config/picture/pf2pMatch]
2025-03-04 16:46:15.719 ERROR com.ymiots.campusmp.service.TimeService - 665:请选择更新的图片
2025-03-04 16:46:30.968 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://*************:2024/config/picture/pf2pMatch]
2025-03-04 16:47:23.888 ERROR com.ymiots.campusmp.service.TimeService - 665:请选择更新的图片
2025-03-04 16:53:26.239 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 118892 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 16:53:26.243 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-04 16:53:28.303 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 16:53:28.303 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 16:53:28.569 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 240 ms. Found 0 Redis repository interfaces.
2025-03-04 16:53:29.582 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-04 16:53:29.590 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-04 16:53:29.590 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 16:53:29.590 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 16:53:30.315 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 16:53:30.315 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4032 ms
2025-03-04 16:53:30.484 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 16:53:31.649 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 16:53:32.999 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 16:53:33.631 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 16:53:33.631 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 16:53:35.530 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-04 16:53:35.699 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 16:53:35.744 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 16:53:35.755 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 16:53:35.967 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 16:53:36.648 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-04 16:53:36.660 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-04 16:53:36.797 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 11.158 seconds (JVM running for 14.711)
2025-03-04 16:54:16.083 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 19652 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 16:54:16.087 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-03-04 16:54:16.092 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-03-04 16:54:20.305 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 16:54:20.307 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 16:54:21.005 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 687 ms. Found 0 Redis repository interfaces.
2025-03-04 16:54:21.897 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$88dda545] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-04 16:54:21.950 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7e3eabd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-04 16:54:22.259 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-03-04 16:54:22.271 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-03-04 16:54:22.272 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 16:54:22.272 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 16:54:22.740 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 16:54:22.741 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6605 ms
2025-03-04 16:54:22.842 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 16:54:24.974 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 16:54:26.676 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 16:54:27.277 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /*************:6379
2025-03-04 16:54:27.277 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /*************:6379
2025-03-04 16:54:28.128 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-03-04 16:54:28.291 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 16:54:28.314 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 16:54:28.320 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 16:54:28.635 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 16:54:29.733 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-03-04 16:54:29.742 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-03-04 16:54:29.921 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$2277dc12 - 计划任务最多执行：39个
2025-03-04 16:54:29.984 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-03-04 16:54:29.987 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-03-04 16:54:30.012 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 14.36 seconds (JVM running for 16.077)
2025-03-04 16:54:30.568 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-03-04 16:54:56.772 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-03-04 16:54:56.784 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 16:54:56.784 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 16:54:56.787 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-03-04 16:55:04.236 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-03-04 16:55:04.236 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-03-04 16:55:04.368 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-03-04 16:55:22.467 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-03-04 16:55:22.520 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-03-04 16:55:33.863 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-03-04 16:55:33.976 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-03-04 16:55:33.980 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-03-04 16:55:33.980 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-03-04 16:55:34.293 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-03-04 16:55:35.563 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-03-04 16:55:35.564 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-03-04 16:55:41.024 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-03-04 16:55:41.033 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-03-04 16:55:48.004 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-03-04 16:55:55.307 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname='ostitle' ]
2025-03-04 16:55:55.308 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-03-04 16:55:55.347 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[2]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：null
2025-03-04 16:55:58.865 ERROR com.ymiots.campusos.service.SysUserService - 90:账号密码错误
2025-03-04 16:56:37.881 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select cvalue from tb_sys_config where cname = ?]，参数=,FaceModelingVerify
2025-03-04 16:56:37.881 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-03-04 16:56:38.872 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/visitorfacedel】消息：{"Cmd":"visitorfacedel","Data":{"uid":""},"Devtype":4,"Server_uid":""}
2025-03-04 16:56:40.946 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-03-04 16:56:48.339 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 16:56:48.339 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 16:56:48.420 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 81 ms
2025-03-04 16:56:59.672 ERROR com.ymiots.campusmp.service.TimeService - 664:请先注册照片信息
2025-03-04 16:58:06.251 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 123600 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 16:58:06.251 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-04 16:58:08.473 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 16:58:08.473 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 16:58:08.787 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 291 ms. Found 0 Redis repository interfaces.
2025-03-04 16:58:09.634 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-04 16:58:09.650 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-04 16:58:09.650 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 16:58:09.650 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 16:58:10.090 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 16:58:10.090 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3788 ms
2025-03-04 16:58:10.186 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 16:58:11.282 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 16:58:12.339 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 16:58:12.975 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 16:58:12.975 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 16:58:14.357 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-04 16:58:14.488 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 16:58:14.523 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 16:58:14.523 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 16:58:14.723 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 16:58:15.185 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-04 16:58:15.189 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-04 16:58:15.324 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 9.593 seconds (JVM running for 11.769)
2025-03-04 16:58:19.388 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 16:58:19.388 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 16:58:19.399 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 11 ms
2025-03-04 16:59:13.139 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-03-04 16:59:13.276 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-03-04 16:59:13.283 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-03-04 16:59:19.793 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 111660 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 16:59:19.805 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-04 16:59:21.571 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 16:59:21.571 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 16:59:21.836 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 259 ms. Found 0 Redis repository interfaces.
2025-03-04 16:59:22.536 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-04 16:59:22.552 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-04 16:59:22.552 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 16:59:22.552 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 16:59:22.930 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 16:59:22.930 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3090 ms
2025-03-04 16:59:23.025 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 16:59:24.190 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 16:59:25.442 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 16:59:25.873 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 16:59:25.873 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 16:59:26.724 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-03-04 16:59:27.343 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-04 16:59:27.459 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 16:59:27.484 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 16:59:27.490 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 16:59:27.677 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 16:59:28.252 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-04 16:59:28.258 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-04 16:59:28.385 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 9.009 seconds (JVM running for 11.254)
2025-03-04 16:59:28.776 ERROR i.n.u.concurrent.DefaultPromise.rejectedExecution - Failed to submit a listener notification task. Event loop shut down?
java.util.concurrent.RejectedExecutionException: event executor terminated
	at io.netty.util.concurrent.SingleThreadEventExecutor.reject(SingleThreadEventExecutor.java:934)
	at io.netty.util.concurrent.SingleThreadEventExecutor.offerTask(SingleThreadEventExecutor.java:351)
	at io.netty.util.concurrent.SingleThreadEventExecutor.addTask(SingleThreadEventExecutor.java:344)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:836)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute0(SingleThreadEventExecutor.java:827)
	at io.netty.util.concurrent.SingleThreadEventExecutor.execute(SingleThreadEventExecutor.java:817)
	at io.netty.util.concurrent.DefaultPromise.safeExecute(DefaultPromise.java:841)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:499)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:605)
	at io.netty.util.concurrent.DefaultPromise.setSuccess(DefaultPromise.java:96)
	at io.netty.channel.group.DefaultChannelGroupFuture.setSuccess0(DefaultChannelGroupFuture.java:200)
	at io.netty.channel.group.DefaultChannelGroupFuture.access$400(DefaultChannelGroupFuture.java:41)
	at io.netty.channel.group.DefaultChannelGroupFuture$1.operationComplete(DefaultChannelGroupFuture.java:75)
	at io.netty.channel.group.DefaultChannelGroupFuture$1.operationComplete(DefaultChannelGroupFuture.java:48)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:578)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:571)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:550)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:491)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:616)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:605)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$CloseFuture.setClosed(AbstractChannel.java:1164)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.doClose0(AbstractChannel.java:755)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:731)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:620)
	at io.netty.channel.nio.NioEventLoop.closeAll(NioEventLoop.java:772)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:529)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-03-04 16:59:28.950 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-03-04 16:59:28.950 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-03-04 16:59:34.649 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 114284 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 16:59:34.653 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-03-04 16:59:34.653 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-03-04 16:59:39.038 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 16:59:39.040 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 16:59:39.998 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 943 ms. Found 0 Redis repository interfaces.
2025-03-04 16:59:41.084 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$c64645d9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-04 16:59:41.133 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$454c8b51] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-04 16:59:41.452 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-03-04 16:59:41.460 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-03-04 16:59:41.461 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 16:59:41.461 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 16:59:42.229 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 16:59:42.231 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7523 ms
2025-03-04 16:59:42.386 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 16:59:43.921 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 16:59:46.122 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 16:59:46.908 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 16:59:46.908 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 16:59:48.267 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-03-04 16:59:48.565 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 16:59:48.604 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 16:59:48.617 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 16:59:48.915 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 16:59:50.339 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-03-04 16:59:50.347 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-03-04 16:59:50.482 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$5fe07ca6 - 计划任务最多执行：58个
2025-03-04 16:59:50.554 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-03-04 16:59:50.563 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-03-04 16:59:50.563 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-03-04 16:59:50.592 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[自动生成卡号]成功
2025-03-04 16:59:50.596 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-03-04 16:59:50.599 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-03-04 16:59:50.599 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-03-04 16:59:50.603 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-03-04 16:59:50.606 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-03-04 16:59:50.609 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-03-04 16:59:50.614 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[订餐定时删除人脸授权]成功
2025-03-04 16:59:50.618 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[大华人脸机时段下发]成功
2025-03-04 16:59:50.618 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[处理订餐人脸照片下发]成功
2025-03-04 16:59:50.623 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-03-04 16:59:50.626 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-03-04 16:59:50.630 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-03-04 16:59:50.632 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-03-04 16:59:50.636 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-03-04 16:59:50.637 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-03-04 16:59:50.638 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-03-04 16:59:50.641 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-03-04 16:59:50.657 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 16.392 seconds (JVM running for 18.423)
2025-03-04 16:59:51.308 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-03-04 16:59:51.320 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 16:59:51.320 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 16:59:51.322 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-03-04 16:59:51.610 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-03-04 16:59:59.557 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-03-04 17:00:00.007 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-04 17:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-04 16:59:48,914 to 2025-03-04 17:00:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-04 17:00:00.050 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-04 17:00:00.052 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-04 16:59:27,677 to 2025-03-04 17:00:00,035
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-04 17:00:00.085 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-04 17:00:00.098 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-04 17:00:00.122 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-04 17:00:00.123 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-04 17:00:00.276 INFO  c.y.campusos.task.CardSettlementTransactionTask - 开始结算交易流水
2025-03-04 17:00:00.290 INFO  c.y.campusos.task.CardSettlementTransactionTask - 主钱包暂无交易流水可结算，等待下一次结算
2025-03-04 17:00:00.313 INFO  c.y.campusos.task.CardSettlementTransactionTask - 副钱包暂无交易流水可结算，等待下一次结算
2025-03-04 17:00:00.318 INFO  c.y.campusos.task.CardSettlementTransactionTask - 完成交易流水结算
2025-03-04 17:00:47.609 INFO  com.ymiots.campusos.service.face.IntoFaceService - 检测到人脸数量：1
2025-03-04 17:00:47.635 INFO  com.ymiots.campusos.service.face.IntoFaceService - 图像清晰度检测，方差值：213.56885112866118，是否清晰：true
2025-03-04 17:00:47.635 INFO  com.ymiots.campusos.service.face.IntoFaceService - 人脸包含检测：顶部位置：144，底部位置：367，是否完全包含：true
2025-03-04 17:00:47.636 INFO  com.ymiots.campusos.service.face.IntoFaceService - 人脸宽高比检测：宽高比：1.0，是否正常：true
2025-03-04 17:00:47.650 INFO  com.ymiots.campusos.service.face.IntoFaceService - 眼睛检测：检测到眼睛数量：2，是否检测到两个眼睛：true
2025-03-04 17:00:47.654 INFO  com.ymiots.campusos.service.face.IntoFaceService - 检测到合格的人脸，位置：(290, 255)
2025-03-04 17:00:47.654 INFO  com.ymiots.campusos.service.face.IntoFaceService - 检测到人脸，且符合所有条件。
2025-03-04 17:00:47.715 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/visitorfacedel】消息：{"Cmd":"visitorfacedel","Data":{"uid":""},"Devtype":4,"Server_uid":""}
2025-03-04 17:00:49.720 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-03-04 17:00:55.080 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 17:00:55.082 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 17:00:55.190 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 108 ms
2025-03-04 17:01:50.380 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://*************:2024/config/picture/pf2pMatch]
2025-03-04 17:03:23.504 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 133304 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 17:03:23.504 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-03-04 17:03:23.506 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-03-04 17:03:30.171 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 17:03:30.177 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 17:03:30.478 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 117532 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 17:03:30.478 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-04 17:03:31.077 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 888 ms. Found 0 Redis repository interfaces.
2025-03-04 17:03:32.462 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$95a32053] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-04 17:03:32.527 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$14a965cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-04 17:03:32.871 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-03-04 17:03:32.880 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-03-04 17:03:32.881 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 17:03:32.881 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 17:03:32.998 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 17:03:33.001 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 17:03:33.389 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 370 ms. Found 0 Redis repository interfaces.
2025-03-04 17:03:33.447 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 17:03:33.447 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9875 ms
2025-03-04 17:03:33.566 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 17:03:34.556 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-04 17:03:34.566 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-04 17:03:34.566 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 17:03:34.566 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 17:03:35.050 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 17:03:35.181 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 17:03:35.181 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4648 ms
2025-03-04 17:03:35.387 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 17:03:36.298 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 17:03:37.086 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 17:03:37.330 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 17:03:37.914 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 17:03:37.914 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 17:03:37.930 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 17:03:37.930 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 17:03:39.308 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-03-04 17:03:39.593 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 17:03:39.640 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 17:03:39.652 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 17:03:39.962 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 17:03:40.111 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-04 17:03:40.304 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 17:03:40.350 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 17:03:40.361 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 17:03:40.678 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 17:03:41.398 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-03-04 17:03:41.409 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-03-04 17:03:41.506 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-04 17:03:41.520 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-04 17:03:41.527 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$2f3d5720 - 计划任务最多执行：58个
2025-03-04 17:03:41.636 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费休眠转历史休眠表]成功
2025-03-04 17:03:41.642 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录休眠表数据删除]成功
2025-03-04 17:03:41.642 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤原始记录转为休眠数据]成功
2025-03-04 17:03:41.647 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[自动生成卡号]成功
2025-03-04 17:03:41.647 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[交易记录转为休眠数据]成功
2025-03-04 17:03:41.652 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算消费流水 ]成功
2025-03-04 17:03:41.653 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别记录转为休眠数据]成功
2025-03-04 17:03:41.654 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别休眠表数据删除]成功
2025-03-04 17:03:41.659 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[通行记录休眠表数据删除]成功
2025-03-04 17:03:41.659 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[结算交易流水]成功
2025-03-04 17:03:41.667 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[订餐定时删除人脸授权]成功
2025-03-04 17:03:41.689 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[大华人脸机时段下发]成功
2025-03-04 17:03:41.689 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[处理订餐人脸照片下发]成功
2025-03-04 17:03:41.699 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[考勤记录休眠表数据删除]成功
2025-03-04 17:03:41.703 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除系统日志]成功
2025-03-04 17:03:41.707 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[宿舍归寝情况分析]成功
2025-03-04 17:03:41.712 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[消费记录转为休眠数据]成功
2025-03-04 17:03:41.712 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[清除临时文件]成功
2025-03-04 17:03:41.718 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[数据库数据备份]成功
2025-03-04 17:03:41.718 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[出入口通行记录转休眠数据]成功
2025-03-04 17:03:41.718 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 11.707 seconds (JVM running for 14.369)
2025-03-04 17:03:41.718 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸识别照片删除]成功
2025-03-04 17:03:41.737 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 18.71 seconds (JVM running for 20.758)
2025-03-04 17:03:42.896 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-03-04 17:04:17.391 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 17:04:17.391 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 17:04:17.400 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 9 ms
2025-03-04 17:04:39.391 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://*************:2024/config/picture/pf2pMatch]
2025-03-04 17:15:00.112 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-04 17:03:39,958 to 2025-03-04 17:15:00,077
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-04 17:20:00.009 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-04 17:20:00.071 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-04 17:20:00.080 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-04 17:20:00.089 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-04 17:20:00.089 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-04 17:26:48.224 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-04 17:03:40,677 to 2025-03-04 17:26:48,201
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-04 17:26:57.268 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 60852 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 17:26:57.271 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-04 17:26:59.111 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 17:26:59.111 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 17:26:59.354 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 224 ms. Found 0 Redis repository interfaces.
2025-03-04 17:27:00.084 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-04 17:27:00.091 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-04 17:27:00.091 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 17:27:00.095 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 17:27:00.527 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 17:27:00.527 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3212 ms
2025-03-04 17:27:00.618 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 17:27:01.862 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 17:27:02.775 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 17:27:03.201 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 17:27:03.201 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 17:27:04.431 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-04 17:27:04.551 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 17:27:04.574 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 17:27:04.581 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 17:27:04.751 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 17:27:05.097 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-04 17:27:05.103 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-04 17:27:05.211 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 8.419 seconds (JVM running for 10.264)
2025-03-04 17:27:20.055 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 17:27:20.055 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 17:27:20.055 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-03-04 17:27:34.941 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:27:36.099 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload//face/c5a2950c40e04c52bb49d23dc72e6b62.jpg，照片2D:/upload//face/c5a2950c40e04c52bb49d23dc72e6b62.jpg，识别阀值：人脸检测失败：0.0
2025-03-04 17:27:41.061 ERROR com.ymiots.campusmp.service.TimeService - 689:人脸比对失败
2025-03-04 17:30:00.023 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-04 17:27:04,751 to 2025-03-04 17:30:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-04 17:30:00.023 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-04 17:15:00,077 to 2025-03-04 17:30:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-04 17:30:49.709 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 126588 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 17:30:49.709 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-04 17:30:51.399 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 17:30:51.399 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 17:30:51.630 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 223 ms. Found 0 Redis repository interfaces.
2025-03-04 17:30:52.349 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-04 17:30:52.354 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-04 17:30:52.358 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 17:30:52.358 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 17:30:52.709 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 17:30:52.709 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2950 ms
2025-03-04 17:30:52.789 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 17:30:53.639 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 17:30:54.499 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 17:30:54.929 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 17:30:54.929 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 17:30:56.124 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-04 17:30:56.229 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 17:30:56.251 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 17:30:56.257 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 17:30:56.413 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 17:30:56.809 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-04 17:30:56.819 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-04 17:30:56.929 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 7.608 seconds (JVM running for 9.472)
2025-03-04 17:30:58.537 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 17:30:58.537 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 17:30:58.539 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-03-04 17:31:06.283 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:31:06.927 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/27a2386a0d5543aca21ecf6ce86d4eb8.jpg，照片2D:/upload/face/27a2386a0d5543aca21ecf6ce86d4eb8.jpg，识别阀值：人脸检测失败：0.0
2025-03-04 17:31:06.929 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:31:40.463 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:31:40.921 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/6e57b6bea0664a20a4e3ec85dfc44dc1.jpg，照片2D:/upload/face/6e57b6bea0664a20a4e3ec85dfc44dc1.jpg，识别阀值：人脸检测失败：1.0
2025-03-04 17:31:40.922 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:32:12.565 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:32:33.139 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/dd35604904a643bea3b590c69c50991c.jpg，照片2D:/upload/face/dd35604904a643bea3b590c69c50991c.jpg，识别阀值：人脸检测失败：1.0
2025-03-04 17:32:33.139 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:33:15.182 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:33:15.505 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/b834bc0f2823403a8c5bd72ac5a2acd5.jpg，照片2D:/upload/face/b834bc0f2823403a8c5bd72ac5a2acd5.jpg，识别阀值：人脸检测失败：0.0
2025-03-04 17:33:15.508 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:33:25.151 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:34:53.092 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 115724 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 17:34:53.095 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-04 17:34:54.804 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 17:34:54.805 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 17:34:55.118 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 296 ms. Found 0 Redis repository interfaces.
2025-03-04 17:34:55.874 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-04 17:34:55.884 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-04 17:34:55.884 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 17:34:55.884 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 17:34:56.314 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 17:34:56.319 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3185 ms
2025-03-04 17:34:56.404 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 17:34:57.413 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 17:34:58.415 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 17:34:58.864 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 17:34:58.864 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 17:35:00.120 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-04 17:35:00.231 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 17:35:00.254 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 17:35:00.262 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 17:35:00.427 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 17:35:00.854 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-04 17:35:00.864 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-04 17:35:00.984 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 8.387 seconds (JVM running for 10.141)
2025-03-04 17:35:13.331 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 17:35:13.331 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 17:35:13.334 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-03-04 17:35:35.979 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569,1735981007;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-03-04 17:35:36.004 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 17:35:36.004 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 17:35:36.084 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 80 ms
2025-03-04 17:35:42.957 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71be4e80-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-03-04 17:35:54.184 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1D33eg3gkKQUIY7yba7K6d4
2025-03-04 17:36:03.558 ERROR com.ymiots.campusmp.service.TimeService - 664:请先注册照片信息
2025-03-04 17:36:11.757 INFO  com.ymiots.campusos.service.face.IntoFaceService - 检测到人脸数量：1
2025-03-04 17:36:11.784 INFO  com.ymiots.campusos.service.face.IntoFaceService - 图像清晰度检测，方差值：305.79777519656284，是否清晰：true
2025-03-04 17:36:11.784 INFO  com.ymiots.campusos.service.face.IntoFaceService - 人脸包含检测：顶部位置：146，底部位置：532，是否完全包含：true
2025-03-04 17:36:11.784 INFO  com.ymiots.campusos.service.face.IntoFaceService - 人脸宽高比检测：宽高比：1.0，是否正常：true
2025-03-04 17:36:11.817 INFO  com.ymiots.campusos.service.face.IntoFaceService - 眼睛检测：检测到眼睛数量：3，是否检测到两个眼睛：true
2025-03-04 17:36:11.822 INFO  com.ymiots.campusos.service.face.IntoFaceService - 检测到合格的人脸，位置：(339, 339)
2025-03-04 17:36:11.823 INFO  com.ymiots.campusos.service.face.IntoFaceService - 检测到人脸，且符合所有条件。
2025-03-04 17:36:11.934 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/visitorfacedel】消息：{"Cmd":"visitorfacedel","Data":{"uid":""},"Devtype":4,"Server_uid":""}
2025-03-04 17:36:13.934 INFO  com.ymiots.campusos.redis.RedisMessagePublish - 发布主题【/campus/personfaceauth】消息：{"Cmd":"personfaceauth","Data":{},"Devtype":3,"Server_uid":""}
2025-03-04 17:36:22.850 INFO  com.ymiots.campusmp.service.FaceService - 照片方向：0
2025-03-04 17:36:22.904 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - 尝试加载的DLL路径是：D:\yunmai\opencv\opencv_java490.dll
2025-03-04 17:36:23.004 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - DLL 文件加载成功: D:\yunmai\opencv\opencv_java490.dll
2025-03-04 17:36:23.034 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - 人脸分类器加载成功：D:\yunmai\opencv\haarcascade_frontalface_alt.xml
2025-03-04 17:36:23.045 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - 分类器加载成功：D:\yunmai\opencv\haarcascade_frontalface_alt.xml 和 D:\yunmai\opencv\haarcascade_eye.xml
2025-03-04 17:36:23.376 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - 检测到人脸数量：1
2025-03-04 17:36:23.386 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - 图像清晰度检测，方差值：340.5269935230293，是否清晰：true
2025-03-04 17:36:23.387 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - 人脸包含检测：顶部位置：92，底部位置：366，是否完全包含：true
2025-03-04 17:36:23.387 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - 人脸宽高比检测：宽高比：1.0，是否正常：true
2025-03-04 17:36:23.406 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - 眼睛检测：检测到眼睛数量：3，是否检测到两个眼睛：true
2025-03-04 17:36:23.408 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - 检测到合格的人脸，位置：(177, 229)
2025-03-04 17:36:23.409 INFO  c.y.c.controller.weixinwork.WeiXinWorkCallBack - 检测到人脸，且符合所有条件。
2025-03-04 17:36:23.409 INFO  com.ymiots.campusmp.service.FaceService - 照片路径：D:/upload//face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg
2025-03-04 17:36:44.146 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:36:58.704 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:37:28.564 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:37:54.404 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:38:49.705 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 127356 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-04 17:38:49.705 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-04 17:38:51.527 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-04 17:38:51.534 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-04 17:38:51.784 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 244 ms. Found 0 Redis repository interfaces.
2025-03-04 17:38:52.487 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-04 17:38:52.494 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-04 17:38:52.494 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-04 17:38:52.494 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-04 17:38:52.846 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-04 17:38:52.847 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3094 ms
2025-03-04 17:38:52.934 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-04 17:38:54.028 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-04 17:38:54.994 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-04 17:38:55.664 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-04 17:38:55.664 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-04 17:38:56.878 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-04 17:38:56.994 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-04 17:38:57.024 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-04 17:38:57.034 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-04 17:38:57.209 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-04 17:38:57.684 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-04 17:38:57.694 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-04 17:38:57.796 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 8.563 seconds (JVM running for 10.491)
2025-03-04 17:38:58.569 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-03-04 17:38:58.569 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-03-04 17:38:58.569 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-03-04 17:40:00.019 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 开始逐笔结算消费流水
2025-03-04 17:40:00.052 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 主钱包暂无消费流水可结算，等待下一次结算
2025-03-04 17:40:00.060 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 副钱包暂无消费流水可结算，等待下一次结算
2025-03-04 17:40:00.079 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 计次消费暂无流水可结算，等待下一次结算
2025-03-04 17:40:00.079 INFO  c.y.campusos.task.CardSettlementPayRecordsTask - 完成消费流水结算
2025-03-04 17:40:03.198 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:04.173 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/aa2a92f799814cacaeb269e12a0394f5.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:04.180 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:40:24.138 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:24.633 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/98125a832fb8445a9d9276fef7e59cf9.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:24.638 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:40:24.702 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:25.053 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:25.063 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/ebb2ceb0490d4cea9980fc3e0d258269.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:25.065 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:40:25.308 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:25.538 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/4039ce59fd024f1d9d5bd11dbb45077f.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:25.540 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:40:25.689 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/08c361af877f418aaf455590c7d482a9.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:25.689 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:40:25.884 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:26.158 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/dc99a9b14d4d4a75b88031dd41507eeb.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:26.158 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:40:35.228 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:35.384 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/0277a28974914b218f19b66cd29e0e40.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:35.384 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:40:37.238 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:37.388 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/dd9b176617cc42caa004d94c65b79974.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:37.388 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:40:38.898 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:39.053 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/76321b09f88446879228ca19f4cd83fd.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:39.053 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:40:40.718 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:40.918 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/0acbfa6bd18344f5a6dbdd4e75533450.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:40.918 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:40:41.366 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:40:41.509 INFO  c.y.campusmp.service.face.FaceDetectionService - 照片1D:/upload/face/6941e47c5a5b40f2aa572c03c9cdba58.jpg，照片2D:/upload/face/edaa0e0e-052a-477c-80ea-1943ffe7e1be.jpg，识别阀值：人脸检测失败：0.05193924158811569
2025-03-04 17:40:41.509 ERROR com.ymiots.campusmp.service.TimeService - 694:人脸比对失败
2025-03-04 17:41:07.248 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:41:48.818 INFO  com.ymiots.campusmp.utils.OkHttpUtil - do post request and url[http://127.0.0.1:2024/config/picture/pf2pMatch]
2025-03-04 17:43:22.398 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-03-04 17:43:22.766 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-03-04 17:43:22.914 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-03-04 17:43:22.938 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-03-04 17:43:23.018 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-03-04 17:43:23.027 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
