2025-03-05 09:29:27.180 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 103328 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-05 09:29:27.180 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-05 09:29:30.093 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-05 09:29:30.095 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-05 09:29:30.307 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 206 ms. Found 0 Redis repository interfaces.
2025-03-05 09:29:31.050 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tom<PERSON> initialized with port(s): 2022 (http)
2025-03-05 09:29:31.050 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-05 09:29:31.050 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-05 09:29:31.050 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-05 09:29:31.422 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-05 09:29:31.422 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4194 ms
2025-03-05 09:29:31.515 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-05 09:29:41.653 ERROR com.alibaba.druid.pool.DruidDataSource - init datasource error, url: jdbc:mysql://************:3306/campusos?useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=GMT%2b8
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1675)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1771)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:925)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusmp.CampusMP.main(CampusMP.java:15)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 87 common frames omitted
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 90 common frames omitted
2025-03-05 09:29:41.659 ERROR com.alibaba.druid.pool.DruidDataSource - {dataSource-1} init error
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1675)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1771)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:925)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusmp.CampusMP.main(CampusMP.java:15)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 87 common frames omitted
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 90 common frames omitted
2025-03-05 09:29:41.659 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-05 09:29:41.661 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'permissionAspect': Unsatisfied dependency expressed through field 'userredis'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userRedis': Unsatisfied dependency expressed through field 'dbService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
2025-03-05 09:29:41.661 INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-03-05 09:29:41.674 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-03-05 09:29:41.705 ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'permissionAspect': Unsatisfied dependency expressed through field 'userredis'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userRedis': Unsatisfied dependency expressed through field 'dbService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusmp.CampusMP.main(CampusMP.java:15)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userRedis': Unsatisfied dependency expressed through field 'dbService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 34 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 63 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1675)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1771)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:925)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeCustomInitMethod(AbstractAutowireCapableBeanFactory.java:1930)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1872)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 74 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:120)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 87 common frames omitted
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 90 common frames omitted
2025-03-05 09:30:15.547 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 131052 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-03-05 09:30:15.550 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-03-05 09:30:18.106 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-03-05 09:30:18.109 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-03-05 09:30:18.572 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 450 ms. Found 0 Redis repository interfaces.
2025-03-05 09:30:19.521 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-03-05 09:30:19.537 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-03-05 09:30:19.537 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-03-05 09:30:19.538 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-03-05 09:30:20.198 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-03-05 09:30:20.198 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4591 ms
2025-03-05 09:30:20.357 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-03-05 09:30:21.687 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-03-05 09:30:23.404 INFO  org.redisson.Version - Redisson 3.12.5
2025-03-05 09:30:24.317 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-03-05 09:30:24.317 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-03-05 09:30:26.229 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-03-05 09:30:26.449 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-03-05 09:30:26.502 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-03-05 09:30:26.513 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-03-05 09:30:26.849 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-03-05 09:30:27.664 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-03-05 09:30:27.681 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-03-05 09:30:27.870 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 12.848 seconds (JVM running for 15.709)
2025-03-05 09:45:00.019 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 09:30:26,845 to 2025-03-05 09:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 10:11:57.562 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-05 10:21:57.531 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 09:45:00,015 to 2025-03-05 10:21:57,530
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 10:21:57.531 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 10:21:57,530 to 2025-03-05 10:21:57,531
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 10:30:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 10:21:57,531 to 2025-03-05 10:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 10:45:00.025 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 10:30:00,015 to 2025-03-05 10:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 11:00:00.024 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 10:45:00,011 to 2025-03-05 11:00:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 11:00:00.024 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-05 11:15:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 11:00:00,017 to 2025-03-05 11:15:00,007
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 11:30:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 11:15:00,007 to 2025-03-05 11:30:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 11:45:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 11:30:00,005 to 2025-03-05 11:45:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 12:00:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 11:45:00,005 to 2025-03-05 12:00:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 12:00:00.029 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-05 12:15:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 12:00:00,013 to 2025-03-05 12:15:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 12:30:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 12:15:00,016 to 2025-03-05 12:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 12:45:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 12:30:00,015 to 2025-03-05 12:45:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 13:00:00.003 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 12:45:00,003 to 2025-03-05 13:00:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 13:00:00.013 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-05 13:15:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 13:00:00,003 to 2025-03-05 13:15:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 13:30:00.018 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 13:15:00,008 to 2025-03-05 13:30:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 13:45:00.021 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 13:30:00,009 to 2025-03-05 13:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 14:00:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 13:45:00,015 to 2025-03-05 14:00:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 14:00:00.022 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-05 14:15:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 14:00:00,013 to 2025-03-05 14:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 14:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 14:15:00,010 to 2025-03-05 14:30:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 14:45:00.044 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 14:30:00,016 to 2025-03-05 14:45:00,018
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 15:00:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 14:45:00,018 to 2025-03-05 15:00:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 15:00:00.013 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-05 15:15:00.030 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 15:00:00,003 to 2025-03-05 15:15:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 15:30:00.027 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 15:15:00,013 to 2025-03-05 15:30:00,008
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 15:45:00.027 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 15:30:00,008 to 2025-03-05 15:45:00,019
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 16:00:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 15:45:00,019 to 2025-03-05 16:00:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 16:00:00.025 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-05 16:15:00.033 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 16:00:00,012 to 2025-03-05 16:15:00,021
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 16:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 16:15:00,021 to 2025-03-05 16:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 16:45:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 16:30:00,015 to 2025-03-05 16:45:00,005
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 17:00:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 16:45:00,005 to 2025-03-05 17:00:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 17:00:00.027 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-05 17:15:00.008 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 17:00:00,014 to 2025-03-05 17:15:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 17:30:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 17:15:00,003 to 2025-03-05 17:30:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-05 17:45:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 17:30:00,010 to 2025-03-05 17:45:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:37:51.614 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-06 08:42:51.628 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-05 17:45:00,003 to 2025-03-06 08:42:51,613
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.628 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,613 to 2025-03-06 08:42:51,628
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.643 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,628 to 2025-03-06 08:42:51,628
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.645 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,628 to 2025-03-06 08:42:51,643
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.646 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,643 to 2025-03-06 08:42:51,646
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.646 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,646 to 2025-03-06 08:42:51,646
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.647 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,646 to 2025-03-06 08:42:51,647
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.648 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,647 to 2025-03-06 08:42:51,648
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.648 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,648 to 2025-03-06 08:42:51,648
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,648 to 2025-03-06 08:42:51,648
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,648 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.649 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,649
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,649 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:42:51.665 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:42:51,665
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:44:58.366 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:42:51,665 to 2025-03-06 08:44:58,366
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 08:59:58.366 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:44:58,366 to 2025-03-06 08:59:58,366
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 09:00:00.003 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-06 09:14:58.359 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 08:59:58,366 to 2025-03-06 09:14:58,359
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 09:29:58.359 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 09:14:58,359 to 2025-03-06 09:29:58,359
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 09:44:58.356 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 09:29:58,359 to 2025-03-06 09:44:58,355
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 09:59:58.364 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 09:44:58,355 to 2025-03-06 09:59:58,364
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 10:00:00.034 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-06 10:14:58.355 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 09:59:58,364 to 2025-03-06 10:14:58,355
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 10:29:58.366 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 10:14:58,355 to 2025-03-06 10:29:58,366
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 10:44:58.360 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 10:29:58,366 to 2025-03-06 10:44:58,360
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 10:59:58.362 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 10:44:58,360 to 2025-03-06 10:59:58,362
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 11:00:00.016 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-06 11:14:58.368 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 10:59:58,362 to 2025-03-06 11:14:58,368
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 11:29:58.360 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 11:14:58,368 to 2025-03-06 11:29:58,360
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 11:44:58.366 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 11:29:58,360 to 2025-03-06 11:44:58,366
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 11:59:58.371 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 11:44:58,366 to 2025-03-06 11:59:58,371
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 12:00:00.022 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-06 12:14:58.366 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 11:59:58,371 to 2025-03-06 12:14:58,366
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 13:02:52.873 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-06 13:07:51.219 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 12:14:58,366 to 2025-03-06 13:07:51,219
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 13:07:51.227 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 13:07:51,219 to 2025-03-06 13:07:51,227
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 13:07:51.229 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 13:07:51,227 to 2025-03-06 13:07:51,229
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 13:14:58.363 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 13:07:51,229 to 2025-03-06 13:14:58,360
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 13:29:58.365 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 13:14:58,360 to 2025-03-06 13:29:58,363
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 13:44:58.443 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 13:29:58,363 to 2025-03-06 13:44:58,391
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 13:59:58.393 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 13:44:58,391 to 2025-03-06 13:59:58,378
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 14:00:00.028 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-06 14:14:58.403 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 13:59:58,378 to 2025-03-06 14:14:58,371
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 14:29:58.389 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 14:14:58,371 to 2025-03-06 14:29:58,375
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 14:44:58.398 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 14:29:58,375 to 2025-03-06 14:44:58,382
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 14:59:58.382 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 14:44:58,382 to 2025-03-06 14:59:58,366
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 15:00:00.126 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-06 15:14:58.369 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 14:59:58,366 to 2025-03-06 15:14:58,363
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 15:34:37.695 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 15:14:58,363 to 2025-03-06 15:34:37,678
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 15:44:58.382 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 15:34:37,678 to 2025-03-06 15:44:58,368
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 15:59:58.370 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 15:44:58,368 to 2025-03-06 15:59:58,369
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 16:00:00.031 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-06 16:14:58.364 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 15:59:58,369 to 2025-03-06 16:14:58,363
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 16:29:58.383 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 16:14:58,363 to 2025-03-06 16:29:58,368
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 16:44:58.674 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 16:29:58,368 to 2025-03-06 16:44:58,635
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 16:59:58.402 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 16:44:58,635 to 2025-03-06 16:59:58,385
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 17:00:00.030 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-03-06 17:14:58.372 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 16:59:58,385 to 2025-03-06 17:14:58,367
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 17:29:58.378 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 17:14:58,367 to 2025-03-06 17:29:58,361
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-03-06 17:44:58.386 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-03-06 17:29:58,361 to 2025-03-06 17:44:58,366
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

