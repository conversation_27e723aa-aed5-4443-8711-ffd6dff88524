2025-01-02 12:00:11.383 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 15972 (D:\yunmai\campusos5.0\campusos\target\classes started by <PERSON>ph in D:\yunmai\campusos5.0)
2025-01-02 12:00:11.383 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-02 12:00:11.383 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-02 12:00:13.472 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-02 12:00:13.472 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-02 12:00:13.652 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 168 ms. Found 0 Redis repository interfaces.
2025-01-02 12:00:14.103 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$6c6b1b73] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 12:00:14.142 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$eb7160eb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 12:00:14.353 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-02 12:00:14.358 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-02 12:00:14.358 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-02 12:00:14.358 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-02 12:00:14.513 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-02 12:00:14.513 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3081 ms
2025-01-02 12:00:14.598 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-02 12:00:24.877 ERROR com.alibaba.druid.pool.DruidDataSource - init datasource error, url: ****************************************************************************************************************
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1682)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:924)
	at com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper.afterPropertiesSet(DruidDataSourceWrapper.java:51)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusos.CamPusOS.main(CamPusOS.java:29)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 83 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 89 common frames omitted
2025-01-02 12:00:24.886 ERROR com.alibaba.druid.pool.DruidDataSource - {dataSource-1} init error
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1682)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:924)
	at com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper.afterPropertiesSet(DruidDataSourceWrapper.java:51)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusos.CamPusOS.main(CamPusOS.java:29)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 83 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 89 common frames omitted
2025-01-02 12:00:24.888 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-02 12:00:24.892 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'applicationStartup': Unsatisfied dependency expressed through field 'license'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLicenseService': Unsatisfied dependency expressed through field 'dbService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
2025-01-02 12:00:24.892 INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-02 12:00:24.905 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-01-02 12:00:24.932 ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'applicationStartup': Unsatisfied dependency expressed through field 'license'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLicenseService': Unsatisfied dependency expressed through field 'dbService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.ymiots.campusos.CamPusOS.main(CamPusOS.java:29)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysLicenseService': Unsatisfied dependency expressed through field 'dbService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'DBService': Unsatisfied dependency expressed through field 'jdbcTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 34 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSourceScriptDatabaseInitializer' defined in class path resource [org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class]: Unsatisfied dependency expressed through method 'dataSourceScriptDatabaseInitializer' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:541)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 63 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1682)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1803)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:924)
	at com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper.afterPropertiesSet(DruidDataSourceWrapper.java:51)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 74 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:581)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:536)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:423)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1431)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818)
	... 83 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:575)
	... 89 common frames omitted
2025-01-02 13:07:27.999 INFO  com.ymiots.campusos.CamPusOS - Starting CamPusOS using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 8564 (D:\yunmai\campusos5.0\campusos\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-02 13:07:27.999 INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.4.Final
2025-01-02 13:07:27.999 INFO  com.ymiots.campusos.CamPusOS - The following 1 profile is active: "framework"
2025-01-02 13:07:29.933 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-02 13:07:29.933 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-02 13:07:30.122 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 180 ms. Found 0 Redis repository interfaces.
2025-01-02 13:07:30.549 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f4c840a3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 13:07:30.596 INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$73ce861b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-01-02 13:07:30.816 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2018 (http)
2025-01-02 13:07:30.816 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2018"]
2025-01-02 13:07:30.816 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-02 13:07:30.816 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-02 13:07:30.986 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-02 13:07:30.986 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2939 ms
2025-01-02 13:07:31.055 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-02 13:07:31.294 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-02 13:07:32.772 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-02 13:07:33.369 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-02 13:07:33.369 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-02 13:07:34.136 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-01-02 13:07:34.290 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-02 13:07:34.313 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-02 13:07:34.329 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-02 13:07:34.498 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-02 13:07:34.848 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2018"]
2025-01-02 13:07:34.851 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2018 (http) with context path ''
2025-01-02 13:07:34.915 INFO  c.y.c.t.ScheduleConfig$$EnhancerBySpringCGLIB$$8e627770 - 计划任务最多执行：1个
2025-01-02 13:07:34.963 INFO  com.ymiots.campusos.task.ScheduleConfig - 启动任务[人脸建模的照片自动下发到人脸授权]成功
2025-01-02 13:07:34.963 INFO  com.ymiots.campusos.CamPusOS - Started CamPusOS in 7.387 seconds (JVM running for 8.779)
2025-01-02 13:07:35.333 INFO  com.ymiots.campusos.ApplicationStartup - 软件许可证初始化完成
2025-01-02 13:15:00.036 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 13:07:34,498 to 2025-01-02 13:15:00,024
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 13:30:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 13:15:00,024 to 2025-01-02 13:30:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 13:44:38.832 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-02 13:44:38.874 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-02 13:44:38.874 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-02 13:44:39.115 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 241 ms
2025-01-02 13:44:40.614 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-01-02 13:44:45.909 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-01-02 13:44:45.924 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-01-02 13:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 13:30:00,009 to 2025-01-02 13:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 13:46:51.813 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 45100 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-02 13:46:51.829 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-02 13:46:52.901 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-02 13:46:52.901 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-02 13:46:52.995 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 74 ms. Found 0 Redis repository interfaces.
2025-01-02 13:46:53.650 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-02 13:46:53.650 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-02 13:46:53.650 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-02 13:46:53.650 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-02 13:46:53.811 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-02 13:46:53.811 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1950 ms
2025-01-02 13:46:53.878 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-02 13:46:54.098 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-02 13:46:54.915 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-02 13:46:55.285 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-02 13:46:55.285 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-02 13:46:56.366 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-02 13:46:56.478 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-02 13:46:56.501 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-02 13:46:56.501 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-02 13:46:56.652 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-02 13:46:56.916 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-02 13:46:56.916 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-02 13:46:57.017 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.561 seconds (JVM running for 6.564)
2025-01-02 13:49:01.764 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-02 13:49:01.764 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-02 13:49:01.764 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-01-02 13:49:09.956 INFO  com.ymiots.campusmp.utils.OkHttpUtil - url=http://*************:2019/weChat/getServicePaid 
 param={"signature":"5611763655722FC83E649019869D62CD","mpAppId":"wx5ecc4dbd1e75f81f","appId":"fbd0057b18bd457a8cbc25383e6e54a3","timestamp":1735796949945}
2025-01-02 13:49:11.662 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-02 13:49:11.694 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-02 13:50:13.112 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-01-02 13:50:13.194 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-01-02 13:50:13.209 ERROR c.ymiots.framework.filter.PermissionFilterHandler - 100:系统用户未在线
2025-01-02 13:50:18.797 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-01-02 13:50:18.797 ERROR com.ymiots.campusos.service.SysUserService - 89:账号密码错误
2025-01-02 13:50:37.086 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[0]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-02 13:50:55.478 ERROR com.ymiots.campusmp.service.OAuthService - 513:验证码错误，请重新输入
2025-01-02 13:51:03.838 ERROR com.ymiots.campusmp.service.OAuthService - 513:验证码错误，请重新输入
2025-01-02 13:51:16.487 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select uid from tb_weixin_user where docid= ? and openid= ? ]，参数=,432b5c2d-5dfa-4faf-a50c-e6385abed0c1,oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-02 13:51:16.488 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 13:51:22.238 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 13:51:22.238 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 14:00:00.003 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-02 14:00:00.018 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 13:45:00,015 to 2025-01-02 14:00:00,018
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 14:00:00.018 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 13:46:56,652 to 2025-01-02 14:00:00,018
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 14:15:00.019 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 14:00:00,018 to 2025-01-02 14:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 14:15:00.019 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 14:00:00,018 to 2025-01-02 14:15:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 14:30:00.022 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 14:15:00,015 to 2025-01-02 14:30:00,022
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 14:30:00.022 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 14:15:00,015 to 2025-01-02 14:30:00,022
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 14:45:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 14:30:00,022 to 2025-01-02 14:45:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 14:45:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 14:30:00,022 to 2025-01-02 14:45:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 15:00:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 14:45:00,014 to 2025-01-02 15:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 15:00:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 14:45:00,014 to 2025-01-02 15:00:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 15:00:00.015 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-02 15:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 15:00:00,010 to 2025-01-02 15:15:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 15:15:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 15:00:00,010 to 2025-01-02 15:15:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 15:30:00.015 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 15:15:00,011 to 2025-01-02 15:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 15:30:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 15:15:00,010 to 2025-01-02 15:30:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 15:45:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 15:30:00,015 to 2025-01-02 15:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 15:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 15:30:00,015 to 2025-01-02 15:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 16:00:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 15:45:00,010 to 2025-01-02 16:00:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 16:00:00.016 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-02 16:00:00.372 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 15:45:00,010 to 2025-01-02 16:00:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 16:01:23.302 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 60984 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-02 16:01:23.304 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-02 16:01:24.416 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-02 16:01:24.417 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-02 16:01:24.504 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 75 ms. Found 0 Redis repository interfaces.
2025-01-02 16:01:25.193 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-02 16:01:25.202 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-02 16:01:25.202 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-02 16:01:25.202 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-02 16:01:25.394 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-02 16:01:25.394 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2044 ms
2025-01-02 16:01:25.474 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-02 16:01:25.683 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-02 16:01:26.483 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-02 16:01:26.917 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-02 16:01:26.917 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-02 16:01:28.035 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-02 16:01:28.149 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-02 16:01:28.173 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-02 16:01:28.178 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-02 16:01:28.334 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-02 16:01:28.593 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-02 16:01:28.600 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-02 16:01:28.680 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.776 seconds (JVM running for 7.062)
2025-01-02 16:06:07.454 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-02 16:06:07.455 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-02 16:06:07.456 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-02 16:06:10.038 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-02 16:06:10.728 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:06:10.728 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:06:21.247 INFO  com.ymiots.campusmp.service.FaceService - 照片方向：0
2025-01-02 16:06:21.258 INFO  com.ymiots.campusmp.service.FaceService - 照片路径：D:/upload//face/70b89633-8d2c-4744-9c90-7608550d989d.jpg
2025-01-02 16:06:46.747 INFO  com.ymiots.campusmp.service.FaceService - 照片方向：0
2025-01-02 16:06:46.762 INFO  com.ymiots.campusmp.service.FaceService - 照片路径：D:/upload//face/4ecaaa9a-f579-4ded-8807-fbbfe4c847a6.jpg
2025-01-02 16:09:51.510 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 71124 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-02 16:09:51.513 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-02 16:09:52.415 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-02 16:09:52.416 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-02 16:09:52.492 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 65 ms. Found 0 Redis repository interfaces.
2025-01-02 16:09:53.075 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-02 16:09:53.081 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-02 16:09:53.081 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-02 16:09:53.082 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-02 16:09:53.227 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-02 16:09:53.227 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1677 ms
2025-01-02 16:09:53.300 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-02 16:09:53.492 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-02 16:09:54.317 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-02 16:09:54.658 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-02 16:09:54.658 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-02 16:09:55.798 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-02 16:09:55.900 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-02 16:09:55.923 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-02 16:09:55.928 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-02 16:09:56.133 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-02 16:09:56.395 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-02 16:09:56.401 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-02 16:09:56.487 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.318 seconds (JVM running for 6.24)
2025-01-02 16:10:01.615 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-02 16:10:01.617 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-02 16:10:01.618 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-02 16:10:24.381 INFO  com.ymiots.campusmp.service.FaceService - 照片方向：0
2025-01-02 16:10:24.399 INFO  com.ymiots.campusmp.service.FaceService - 照片路径：D:/upload//face/dcaf8531-6712-4013-8a92-9faa4a062bd4.jpg
2025-01-02 16:13:00.621 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 62432 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-02 16:13:00.623 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-02 16:13:01.494 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-02 16:13:01.495 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-02 16:13:01.558 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 53 ms. Found 0 Redis repository interfaces.
2025-01-02 16:13:02.153 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-02 16:13:02.159 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-02 16:13:02.159 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-02 16:13:02.160 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-02 16:13:02.306 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-02 16:13:02.306 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1644 ms
2025-01-02 16:13:02.381 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-02 16:13:02.562 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-02 16:13:03.390 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-02 16:13:03.740 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-02 16:13:03.740 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-02 16:13:04.787 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-02 16:13:04.891 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-02 16:13:04.913 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-02 16:13:04.919 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-02 16:13:05.068 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-02 16:13:05.326 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-02 16:13:05.334 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-02 16:13:05.419 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.149 seconds (JVM running for 6.11)
2025-01-02 16:13:12.630 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-02 16:13:12.631 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-02 16:13:12.632 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-02 16:13:12.733 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:13:12.733 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:13:38.557 INFO  com.ymiots.campusmp.service.FaceService - 照片方向：0
2025-01-02 16:13:38.584 INFO  com.ymiots.campusmp.service.FaceService - 照片路径：D:/upload//face/7c602eef-cf98-44a0-be6b-0231fd851e21.jpg
2025-01-02 16:14:50.208 INFO  com.ymiots.campusmp.service.FaceService - 照片方向：0
2025-01-02 16:15:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 16:00:00,014 to 2025-01-02 16:15:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 16:15:06.060 INFO  com.ymiots.campusmp.service.FaceService - 照片路径：D:/upload//face/9d29541d-9f89-4b76-b96f-2136b7f9c9f7.jpg
2025-01-02 16:15:10.443 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 16:13:05,067 to 2025-01-02 16:15:06,060
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 16:28:48.106 INFO  com.ymiots.campusmp.CampusMP - Starting CampusMP using Java 1.8.0_341 on LAPTOP-I3RJCDS4 with PID 68096 (D:\yunmai\campusos5.0\campusmp\target\classes started by Tanph in D:\yunmai\campusos5.0)
2025-01-02 16:28:48.109 INFO  com.ymiots.campusmp.CampusMP - The following 1 profile is active: "framework"
2025-01-02 16:28:49.104 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-02 16:28:49.105 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-02 16:28:49.190 INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 73 ms. Found 0 Redis repository interfaces.
2025-01-02 16:28:49.840 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 2022 (http)
2025-01-02 16:28:49.848 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-2022"]
2025-01-02 16:28:49.848 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-01-02 16:28:49.849 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-01-02 16:28:50.034 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-01-02 16:28:50.034 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1886 ms
2025-01-02 16:28:50.125 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-02 16:28:50.316 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-02 16:28:51.276 INFO  org.redisson.Version - Redisson 3.12.5
2025-01-02 16:28:51.693 INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-01-02 16:28:51.693 INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-01-02 16:28:52.801 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-02 16:28:52.911 WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-01-02 16:28:52.934 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= linkedhashmap
2025-01-02 16:28:52.940 INFO  c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redis.lettuce
2025-01-02 16:28:53.088 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-01-02 16:28:53.358 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-2022"]
2025-01-02 16:28:53.366 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 2022 (http) with context path ''
2025-01-02 16:28:53.447 INFO  com.ymiots.campusmp.CampusMP - Started CampusMP in 5.69 seconds (JVM running for 6.725)
2025-01-02 16:30:00.012 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 16:28:53,087 to 2025-01-02 16:30:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 16:30:00.022 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 16:15:00,012 to 2025-01-02 16:30:00,022
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 16:30:16.137 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-02 16:30:16.137 INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-02 16:30:16.138 INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-02 16:30:25.711 INFO  com.ymiots.campusmp.service.FaceService - 照片方向：0
2025-01-02 16:30:29.170 INFO  com.ymiots.campusmp.service.FaceService - 照片路径：D:/upload//face/4246f51c-2ec9-4f66-aeda-d6db71dffebf.jpg
2025-01-02 16:31:16.436 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:31:16.436 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:31:42.861 INFO  com.ymiots.campusmp.service.FaceService - 照片方向：0
2025-01-02 16:31:42.871 INFO  com.ymiots.campusmp.service.FaceService - 照片路径：D:/upload//face/b40658a4-fb91-480d-a14e-38f0d00ebc5d.jpg
2025-01-02 16:31:52.463 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:31:52.463 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:34:21.986 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[1]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[sysmsg]通讯异常：你的主机中的软件中止了一个已建立的连接。
2025-01-02 16:35:50.341 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:35:50.342 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:36:50.549 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:36:50.549 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:39:46.641 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:39:46.641 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:39:46.880 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:39:46.880 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:40:01.583 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1JNe838mmJ2m5FQsHE4ei1w
2025-01-02 16:40:02.709 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:40:02.711 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:45:00.016 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 16:30:00,009 to 2025-01-02 16:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 16:45:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 16:30:00,022 to 2025-01-02 16:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 16:50:16.049 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:50:16.049 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:52:00.814 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:52:00.814 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:52:09.746 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:52:09.746 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:54:39.527 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:54:39.527 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:54:51.118 INFO  com.ymiots.campusmp.service.FaceService - 照片方向：0
2025-01-02 16:54:51.121 INFO  com.ymiots.campusmp.service.FaceService - 照片路径：D:/upload//face/27855ae4-5702-4b7a-9140-a778b3dc6e61.jpg
2025-01-02 16:55:02.885 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:55:02.886 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:55:06.049 ERROR com.ymiots.framework.datafactory.DataBaseFactory - SQL语句执行出错：[select schoolName ,fileUrl schoolLogo from tb_photo_schoolfiles limit 1]
2025-01-02 16:55:06.050 ERROR com.ymiots.framework.datafactory.DataBaseFactory - Incorrect result size: expected 1, actual 0
2025-01-02 16:55:17.595 INFO  com.ymiots.campusmp.service.FaceService - 照片方向：0
2025-01-02 16:55:17.598 INFO  com.ymiots.campusmp.service.FaceService - 照片路径：D:/upload//face/02166e7d-b91b-4e65-94b4-ae2993e0b7e6.jpg
2025-01-02 17:00:00.003 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 16:45:00,016 to 2025-01-02 17:00:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 17:00:00.009 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-02 17:00:00.020 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 16:45:00,016 to 2025-01-02 17:00:00,019
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 17:15:00.022 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 17:00:00,019 to 2025-01-02 17:15:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 17:15:00.023 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 17:00:00,003 to 2025-01-02 17:15:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 17:16:44.100 INFO  org.apache.coyote.http11.Http11Processor - Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in method name [0x050x010x00...]. HTTP method names must be tokens
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:419)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:271)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-01-02 17:16:44.100 INFO  org.apache.coyote.http11.Http11Processor - Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in method name [0x040x010x010xbb0x000x000x000x01proxychecker0x00api.ip.pn0x00...]. HTTP method names must be tokens
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:419)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:271)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-01-02 17:30:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 17:15:00,016 to 2025-01-02 17:30:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 17:30:00.014 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 17:15:00,016 to 2025-01-02 17:30:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 17:45:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 17:30:00,011 to 2025-01-02 17:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 17:45:00.040 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 17:30:00,011 to 2025-01-02 17:45:00,012
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 18:00:00.038 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 17:45:00,012 to 2025-01-02 18:00:00,038
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 18:00:00.038 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 17:45:00,012 to 2025-01-02 18:00:00,038
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 18:00:00.042 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-02 18:00:00.093 ERROR com.ymiots.campusmp.utils.weixin.WeiXinRequest - 101:pay.ymiots.com
2025-01-02 18:00:00.093 ERROR com.ymiots.campusmp.utils.weixin.WeiXinAccessToken - 获取微信accesstoken云服务接口错误：pay.ymiots.com
2025-01-02 18:00:00.094 ERROR com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token错误：AccessToken is null
2025-01-02 18:15:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 18:00:00,038 to 2025-01-02 18:15:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 18:15:00.028 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 18:00:00,038 to 2025-01-02 18:15:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 18:30:00.005 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 18:15:00,013 to 2025-01-02 18:30:00,003
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 18:30:00.019 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 18:15:00,013 to 2025-01-02 18:30:00,018
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 18:45:00.020 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 18:30:00,018 to 2025-01-02 18:45:00,020
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 18:45:00.004 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 18:30:00,003 to 2025-01-02 18:45:00,004
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 19:00:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 18:45:00,020 to 2025-01-02 19:00:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 19:00:00.013 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 18:45:00,004 to 2025-01-02 19:00:00,013
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 19:00:00.027 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-02 19:15:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 19:00:00,013 to 2025-01-02 19:15:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 19:15:00.010 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 19:00:00,013 to 2025-01-02 19:15:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 19:30:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 19:15:00,009 to 2025-01-02 19:30:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 19:30:00.009 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 19:15:00,009 to 2025-01-02 19:30:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 19:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 19:30:00,006 to 2025-01-02 19:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 19:45:00.011 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 19:30:00,009 to 2025-01-02 19:45:00,010
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 20:00:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 19:45:00,010 to 2025-01-02 20:00:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 20:00:00.001 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-02 20:00:00.001 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 19:45:00,010 to 2025-01-02 20:00:00,001
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 20:15:00.006 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 20:00:00,001 to 2025-01-02 20:15:00,006
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 20:15:00.021 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 20:00:00,017 to 2025-01-02 20:15:00,021
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 20:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 20:15:00,006 to 2025-01-02 20:30:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 20:30:00.017 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 20:15:00,021 to 2025-01-02 20:30:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 20:45:00.018 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 20:30:00,017 to 2025-01-02 20:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-02 20:45:00.045 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 20:30:00,017 to 2025-01-02 20:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:30:44.535 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 08:35:45.869 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 20:45:00,016 to 2025-01-03 08:35:45,869
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.869 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-02 20:45:00,016 to 2025-01-03 08:35:45,869
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.874 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,869 to 2025-01-03 08:35:45,874
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.874 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,874 to 2025-01-03 08:35:45,874
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.874 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,869 to 2025-01-03 08:35:45,874
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.874 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,874 to 2025-01-03 08:35:45,874
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.874 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,874 to 2025-01-03 08:35:45,874
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.874 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,874 to 2025-01-03 08:35:45,874
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.874 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,874 to 2025-01-03 08:35:45,874
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.874 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,874 to 2025-01-03 08:35:45,874
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.874 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,874 to 2025-01-03 08:35:45,874
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.881 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,874 to 2025-01-03 08:35:45,881
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.881 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,874 to 2025-01-03 08:35:45,881
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.881 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,881 to 2025-01-03 08:35:45,881
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.881 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,881 to 2025-01-03 08:35:45,881
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.881 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,881 to 2025-01-03 08:35:45,881
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.881 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,881 to 2025-01-03 08:35:45,881
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.883 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,881 to 2025-01-03 08:35:45,881
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.883 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,881 to 2025-01-03 08:35:45,883
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.883 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,883 to 2025-01-03 08:35:45,883
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.883 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,883 to 2025-01-03 08:35:45,883
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.883 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,883 to 2025-01-03 08:35:45,883
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.881 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,881 to 2025-01-03 08:35:45,881
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.883 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,881 to 2025-01-03 08:35:45,883
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.883 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,883 to 2025-01-03 08:35:45,883
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.883 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,883 to 2025-01-03 08:35:45,883
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.883 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,883 to 2025-01-03 08:35:45,883
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,883 to 2025-01-03 08:35:45,883
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,883 to 2025-01-03 08:35:45,883
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,883 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,883 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.885 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.886 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.886 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,885
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.886 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,886
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.886 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,886 to 2025-01-03 08:35:45,886
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.887 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,886 to 2025-01-03 08:35:45,887
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.887 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,887 to 2025-01-03 08:35:45,887
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.887 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,887 to 2025-01-03 08:35:45,887
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.887 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,885 to 2025-01-03 08:35:45,886
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.887 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,887 to 2025-01-03 08:35:45,887
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.887 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,886 to 2025-01-03 08:35:45,887
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.887 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,887 to 2025-01-03 08:35:45,887
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.887 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,887 to 2025-01-03 08:35:45,887
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.887 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,887 to 2025-01-03 08:35:45,887
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.887 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,887 to 2025-01-03 08:35:45,887
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,887 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,887 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.888 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,888
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,888 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.889 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,889
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:35:45.893 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:35:45,893
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:44:57.333 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,889 to 2025-01-03 08:44:57,333
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:44:57.340 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:35:45,893 to 2025-01-03 08:44:57,333
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:59:57.332 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:44:57,333 to 2025-01-03 08:59:57,332
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 08:59:57.332 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:44:57,333 to 2025-01-03 08:59:57,332
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 09:00:00.015 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 09:14:57.336 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:59:57,332 to 2025-01-03 09:14:57,335
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 09:14:57.338 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 08:59:57,332 to 2025-01-03 09:14:57,335
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 09:41:08.047 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 09:14:57,335 to 2025-01-03 09:41:08,046
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 09:41:08.047 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 09:14:57,335 to 2025-01-03 09:41:08,046
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 09:44:57.323 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 09:41:08,046 to 2025-01-03 09:44:57,323
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 09:44:57.334 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 09:41:08,046 to 2025-01-03 09:44:57,334
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 09:59:57.325 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 09:44:57,323 to 2025-01-03 09:59:57,324
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 09:59:57.331 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 09:44:57,334 to 2025-01-03 09:59:57,331
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 10:00:00.013 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 10:14:57.335 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 09:59:57,331 to 2025-01-03 10:14:57,335
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 10:14:57.335 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 09:59:57,324 to 2025-01-03 10:14:57,335
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 10:29:57.337 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 10:14:57,335 to 2025-01-03 10:29:57,337
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 10:29:57.342 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 10:14:57,335 to 2025-01-03 10:29:57,337
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 10:44:57.332 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 10:29:57,337 to 2025-01-03 10:44:57,332
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 10:44:57.333 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 10:29:57,337 to 2025-01-03 10:44:57,332
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 10:59:57.336 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 10:44:57,332 to 2025-01-03 10:59:57,335
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 10:59:57.336 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 10:44:57,332 to 2025-01-03 10:59:57,336
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 11:00:00.013 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 11:14:57.330 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 10:59:57,335 to 2025-01-03 11:14:57,330
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 11:14:57.338 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 10:59:57,336 to 2025-01-03 11:14:57,329
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 11:29:57.328 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 11:14:57,329 to 2025-01-03 11:29:57,326
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 11:29:57.330 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 11:14:57,330 to 2025-01-03 11:29:57,330
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 11:41:15.923 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1D33eg3gkKQUIY7yba7K6d4
2025-01-03 11:41:15.937 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 11:44:57.337 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 11:29:57,326 to 2025-01-03 11:44:57,336
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 11:44:57.336 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 11:29:57,330 to 2025-01-03 11:44:57,336
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 11:58:56.855 INFO  c.y.c.controller.weixin.WeiXinOAuthController - 微信用户openid:oYPku1D33eg3gkKQUIY7yba7K6d4
2025-01-03 11:58:56.859 ERROR com.ymiots.campusmp.service.OAuthService - 微信用户信息未绑定
2025-01-03 11:59:57.328 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 11:44:57,336 to 2025-01-03 11:59:57,328
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 11:59:57.343 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 11:44:57,336 to 2025-01-03 11:59:57,343
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 12:00:00.013 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 12:14:57.332 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 11:59:57,343 to 2025-01-03 12:14:57,332
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 12:14:57.332 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 11:59:57,328 to 2025-01-03 12:14:57,332
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 12:29:57.330 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 12:14:57,332 to 2025-01-03 12:29:57,329
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 12:29:57.344 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 12:14:57,332 to 2025-01-03 12:29:57,344
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 12:44:57.323 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 12:29:57,329 to 2025-01-03 12:44:57,323
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 12:44:57.341 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 12:29:57,344 to 2025-01-03 12:44:57,339
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 12:59:57.334 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 12:44:57,323 to 2025-01-03 12:59:57,334
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 12:59:57.334 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 12:44:57,339 to 2025-01-03 12:59:57,334
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 13:00:00.020 INFO  com.ymiots.campusmp.task.WeiXinAccessTokenManager - 刷新access_token
2025-01-03 13:14:57.329 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 12:59:57,334 to 2025-01-03 13:14:57,329
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 13:18:00.728 ERROR com.ymiots.framework.websocket.WebsocketManager - WSS客户端[5]用户[71d4d757-7563-11e9-9c41-b083fe682d68]主题[serviceosinfo]通讯异常：null
2025-01-03 13:18:24.369 ERROR com.ymiots.campusos.service.card.CardManageService - 305:卡片未登记
2025-01-03 13:19:03.222 ERROR com.ymiots.campusos.service.card.CardManageService - 305:卡片未登记
2025-01-03 13:19:32.742 ERROR c.y.campusos.service.card.TeachStudCardService - 434:
2025-01-03 13:29:57.340 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 13:14:57,329 to 2025-01-03 13:29:57,340
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 13:44:57.330 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 13:29:57,340 to 2025-01-03 13:44:57,329
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 13:44:58.859 INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1733556318,1733727569;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-01-03 13:59:57.339 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 13:44:57,329 to 2025-01-03 13:59:57,339
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 14:14:57.348 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 13:59:57,339 to 2025-01-03 14:14:57,341
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 14:29:57.342 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 14:14:57,341 to 2025-01-03 14:29:57,342
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 14:44:57.347 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 14:29:57,342 to 2025-01-03 14:44:57,338
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 14:59:57.341 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 14:44:57,338 to 2025-01-03 14:59:57,341
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 15:14:57.342 INFO  com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-01-03 14:59:57,341 to 2025-01-03 15:14:57,338
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-01-03 15:20:21.652 INFO  com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-01-03 15:20:21.858 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-01-03 15:20:21.861 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
