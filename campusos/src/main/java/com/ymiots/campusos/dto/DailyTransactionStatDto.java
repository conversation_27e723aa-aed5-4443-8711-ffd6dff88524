package com.ymiots.campusos.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 日交易消费统计
 *
 * <AUTHOR>
 * @date 2023/07/23
 */
@Data
@Accessors(chain = true)
public class DailyTransactionStatDto {

    /**
     * 天日期
     */
    private Date time;

    /**
     * 人员id
     */
    private String infoId;

    /**
     * 人员姓名
     */
    private String infoName;

    /**
     * 人员工号
     */
    private String code;

    /**
     * 充值统计
     */
    private Double chargeSum;

    /**
     * 部门
     */
    private String departName;

    /**
     * 绩效补贴统计
     */
    private Double performanceRewardSum;

    /**
     * 公司补贴统计
     */
    private Double companyAllowanceSum;

    /**
     * 主钱包消费
     */
    private Double consumeMainWalletSum;

    /**
     * 副钱包消费
     */
    private Double consumeSubWalletSum;

    /**
     * 消费金额
     */
    private Double consumeSum;

    /**
     * 退款
     */
    private Double refundSum;

    /**
     * 充值退款
     */
    private Double refundChargeSum;

    /**
     * 合计
     * 钱包剩余金额
     */
    private Double sum;

    public Double getChargeSum() {
        return round2Decimals(chargeSum);
    }

    public Double getConsumeMainWalletSum() {
        return round2Decimals(consumeMainWalletSum);
    }

    public Double getConsumeSubWalletSum() {
        return round2Decimals(consumeSubWalletSum);
    }

    public Double getConsumeSum() {
        return round2Decimals(consumeSum);
    }

    public Double getRefundSum() {
        return round2Decimals(refundSum);
    }

    public Double getPerformanceRewardSum() {
        return round2Decimals(performanceRewardSum);
    }

    public Double getCompanyAllowanceSum() {
        return round2Decimals(companyAllowanceSum);
    }

    public Double getRefundChargeSum() {
        return round2Decimals(refundChargeSum);
    }

    public Double getSum() {
        return round2Decimals(sum);
    }

    private Double round2Decimals(Double num) {
        return num != null ? (Math.round(num * 100) / 100.0) : null;
    }
}

