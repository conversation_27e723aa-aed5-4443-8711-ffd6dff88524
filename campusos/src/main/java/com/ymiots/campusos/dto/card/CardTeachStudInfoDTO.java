package com.ymiots.campusos.dto.card;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.ymiots.framework.utils.excel.Row;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class CardTeachStudInfoDTO extends Row {
    @ExcelProperty("姓名")
    @ColumnWidth(30)
    private String name;

    @ExcelProperty("学工号")
    @ColumnWidth(20)
    private String code;

    @ExcelProperty("卡号")
    @ColumnWidth(20)
    private String card;

    @ExcelIgnore
    private String cardId;

    @ExcelIgnore
    private String uid;

    @ExcelIgnore
    private Boolean isMain;

    @ExcelProperty("主钱包")
    @ColumnWidth(10)
    private BigDecimal balance;

    @ExcelProperty("副钱包")
    @ColumnWidth(10)
    private BigDecimal viceWallet;

    @ExcelProperty("水控钱包")
    @ColumnWidth(10)
    private BigDecimal waterWallet;

    @ExcelProperty("合计")
    @ColumnWidth(10)
    private BigDecimal sum;

    @ExcelProperty("有效期")
    @ColumnWidth(20)
    private String endDt;

    @ExcelProperty("发卡时间")
    @ColumnWidth(20)
    private String createDt;

    @ExcelIgnore
    private Integer status;

    @ExcelProperty("备注")
    @ColumnWidth(30)
    private String des;
}

