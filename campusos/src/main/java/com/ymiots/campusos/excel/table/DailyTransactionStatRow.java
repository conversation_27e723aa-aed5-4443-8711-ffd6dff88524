package com.ymiots.campusos.excel.table;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ymiots.framework.utils.excel.Row;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7/27 16:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DailyTransactionStatRow extends Row {
    /**
     * 天日期
     */
    @ExcelIgnore
    private Date time;

    /**
     * 人员id
     */
    @ExcelIgnore
    private String infoId;

    /**
     * 人员姓名
     */
    @ExcelProperty("姓名")
    private String infoName;

    /**
     * 人员工号
     */
    @ExcelProperty("工号")
    private String code;

    /**
     * 部门名称
     */
    @ExcelProperty("部门")
    private String departName;

    /**
     * 充值统计
     */
    @ExcelProperty("充值合计")
    private Double chargeSum;

    /**
     * 绩效补贴统计
     */
    @ExcelProperty("绩效补贴合计")
    private Double performanceRewardSum;

    /**
     * 公司补贴统计
     */
    @ExcelProperty("补贴合计")
    private Double companyAllowanceSum;

    /**
     * 主钱包消费
     */
    @ExcelProperty("主钱包消费")
    private Double consumeMainWalletSum;

    /**
     * 副钱包消费
     */
    @ExcelProperty("副钱包消费")
    private Double consumeSubWalletSum;


    /**
     * 退款
     */
    @ExcelProperty("退款")
    private Double refundSum;

    /**
     * 充值退款
     */
    @ExcelProperty("充值退款")
    private Double refundChargeSum;

    /**
     * 消费金额
     */
    @ExcelProperty("消费金额")
    private Double consumeSum;
    /**
     * 卡余额
     */
    @ExcelProperty("卡余额")
    private Double sum;

    public DailyTransactionStatRow(Date time, String infoId, String infoName, String code,String departName, Double chargeSum,  Double performanceRewardSum, Double companyAllowanceSum, Double consumeMainWalletSum, Double consumeSubWalletSum, Double consumeSum, Double refundSum, Double refundChargeSum, Double sum) {
        this.time = time;
        this.infoId = infoId;
        this.infoName = infoName;
        this.code = code;
        this.departName = departName;
        this.chargeSum = chargeSum;
        this.performanceRewardSum = performanceRewardSum;
        this.companyAllowanceSum = companyAllowanceSum;
        this.consumeMainWalletSum = consumeMainWalletSum;
        this.consumeSubWalletSum = consumeSubWalletSum;
        this.consumeSum = consumeSum;
        this.refundSum = refundSum;
        this.refundChargeSum = refundChargeSum;
        this.sum = sum;
    }
}

