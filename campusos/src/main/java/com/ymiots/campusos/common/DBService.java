package com.ymiots.campusos.common;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.framework.common.JSONObjectPlus;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.datafactory.BaseFactory;
import com.ymiots.framework.datafactory.CallCallableStatement;
import com.ymiots.framework.datafactory.CallbackTransaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


@Repository
public class DBService extends BaseFactory {

    @Autowired
    @Qualifier("CamPusOSJdbcTemplate")
    protected JdbcTemplate jdbcTemplate;


    public int excuteSql(String sql, Object... args) {
        try {
            return dbFactory.excuteSql(jdbcTemplate, sql, args);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            return -1;
        }
    }

    public int[] excuteBatchSql(String... sql) {
        return dbFactory.excuteBatchSql(jdbcTemplate, sql);
    }

    public void excuteTransaction(CallbackTransaction callback,Integer isolationLevel) throws Exception {
        dbFactory.excuteTransaction(jdbcTemplate, callback, isolationLevel);
    }

    public void excuteTransaction(CallbackTransaction callback) throws Exception {
        dbFactory.excuteTransaction(jdbcTemplate, callback);
    }


    public JSONArray QueryList(String sql) {
        return dbFactory.QueryList(jdbcTemplate, sql);
    }

    public JSONArray QueryList(String sql, Object... args) {
        return dbFactory.QueryList(jdbcTemplate, sql, args);
    }

    public JSONObject QueryJSONObject(String sql) {
        return dbFactory.QueryJSONObject(jdbcTemplate, sql);
    }

    public JSONObject QueryJSONObject(String sql, Object... args) {
        return dbFactory.QueryJSONObject(jdbcTemplate, sql, args);
    }

    public JSONObjectPlus QueryJSONObjectPlus(String sql, Object... args) {
        JSONObject json = dbFactory.QueryJSONObject(jdbcTemplate, sql, args);
        return JSONObjectPlus.ToPlus(json);
    }

    public <T> T QueryObject(String sql, Class<T> requiredType) {
        return dbFactory.QueryObject(jdbcTemplate, sql, requiredType, new Object[]{});
    }

    public <T> T QueryObject(String sql, Class<T> requiredType, Object... args) {
        return dbFactory.QueryObject(jdbcTemplate, sql, requiredType, args);
    }


    public <T> List<T> QueryObjectList(String sql, Class<T> requiredType) {
        return dbFactory.QueryObjectList(jdbcTemplate, sql, requiredType);
    }

    public <T> List<T> QueryObjectList(String sql, Class<T> requiredType, Object... args) {
        return dbFactory.QueryObjectList(jdbcTemplate, sql, requiredType, args);
    }


    public JsonData QueryJsonData(String sql) {
        return dbFactory.QueryJsonData(jdbcTemplate, sql);
    }

    public JsonData QueryJsonData(String fields, String table, String where, String orderby) {
        return dbFactory.QueryJsonData(jdbcTemplate, fields, table, where, orderby);
    }

    public JSONArray QueryJSONArray(String fields, String table, String where, String orderby, int start, int limit) {
        return dbFactory.QueryJSONArray(jdbcTemplate, fields, table, where, orderby, start, limit);
    }

    public JsonData QueryJsonData(String fields, String table, String where, String orderby, int start, int limit) {
        return dbFactory.QueryJsonData(jdbcTemplate, fields, table, where, orderby, start, limit);
    }

    public int getCount(String table, String where) {
        return dbFactory.getCount(jdbcTemplate, table, where);
    }

    public int getCountBySQL(String sql, String totalcolumen) {
        return dbFactory.getCountBySQL(jdbcTemplate, sql, totalcolumen);
    }

    public JsonData QueryStoreList(String stored, CallCallableStatement callback) {
        return dbFactory.QueryStoreList(jdbcTemplate, stored, callback);
    }

    public <T> List<T> queryList(String sql, Class<T> requireClass) {
        try {
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(requireClass));
        } catch (DataAccessException e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    public <T> T queryForOne(String sql, Class<T> requireClass) {
        List<T> list = queryList(sql, requireClass);
        T obj = null;
        if (!CollectionUtils.isEmpty(list)) {
            obj = list.get(0);
        }
        return obj;
    }

    public <T> List<T> queryFields(String sql, Class<T> requireClass) {
        return jdbcTemplate.queryForList(sql, requireClass);
    }

    public <T> T queryOneField(String sql, Class<T> requireClass) {
        try {
            return this.jdbcTemplate.queryForObject(sql, requireClass);
        } catch (DataAccessException e) {
            return null;
        }
    }
}
