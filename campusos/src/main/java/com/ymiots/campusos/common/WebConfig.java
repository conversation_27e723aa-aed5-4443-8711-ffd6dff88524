package com.ymiots.campusos.common;
import com.ymiots.framework.common.CampusLicense;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import static com.ymiots.framework.config.SystemEnv.currentRunPath;
//import com.ymiots.framework.common.SystemConfig;

@Service
@Component
@ConfigurationProperties(prefix = "webconfig")
public class WebConfig {
    private static String uploaddir;
    private static String appversion;
    private static String processtask;
    private static String domain;
    private static String resourceDomain;
    private static String weixinDomain;
    private static String serverid;
    private static String parkDomain;
    private static String nodeid;
    private static String campusCPDomain;
    private static String campusCPAppId;
    private static String campusCPAppSecret;
    private static boolean startNew;
    private static String thirdParty;
    private static String wxAppId;
    private static String payType;
    private static String mchntCode;

    private static boolean seaoAuthrizeHandleing;
    private static boolean subscribeUseMinuteHandleing;
    private static final String[] RUN_PATH_ARRAY = new String[] {currentRunPath};

    public static boolean isStartNew() {
        return startNew;
    }

    public void setStartNew(boolean startNew) {
        WebConfig.startNew = startNew;
    }

    public static String getUploaddir()  {

        //return "/usr/campus/upload/";
        //return RUN_PATH_ARRAY[0].toString()+"/upload/";
        return uploaddir;
    }

    public static String getThirdParty() {
        return thirdParty;
    }

    public void setThirdParty(String thirdParty) {
        WebConfig.thirdParty = thirdParty;
    }

    public  void setUploaddir(String uploaddir) {
        WebConfig.uploaddir = uploaddir;
    }

    public static String getApptypename() {
        if (CampusLicense.getApptype() == 1) {
            return "智慧校园";
        } else if (CampusLicense.getApptype() == 2) {
            return "智慧企业";
        } else {
            return "智慧校园";
        }
    }

    public static String getWxAppId() {
        return wxAppId;
    }

    public void setWxAppId(String wxAppId) {
        WebConfig.wxAppId = wxAppId;
    }

    public static String getAppversion() {
        return appversion;
    }

    public  void setAppversion(String appversion) {
        WebConfig.appversion = appversion;
    }

    public static String getApptype() {
        if (CampusLicense.getApptype() == 0) {
            return "1";
        } else {
            return String.valueOf(CampusLicense.getApptype());
        }
    }

    public static String getProcesstask() {
        return processtask;
    }

    public  void setProcesstask(String processtask) {
        WebConfig.processtask = processtask;
    }

    public static String getResourceDomain() {
        return resourceDomain;
    }

    public  void setResourceDomain(String resourceDomain) {
        WebConfig.resourceDomain = resourceDomain;
    }

    public static String getDomain() {
        return domain;
    }

    public  void setDomain(String domain) {
        WebConfig.domain = domain;
    }


    public static String getWeixinDomain() {
        return weixinDomain;
    }

    public  void setWeixinDomain(String weixinDomain) {
        WebConfig.weixinDomain = weixinDomain;
    }

    public static String getServerid() {
        return serverid;
    }

    public  void setServerid(String serverid) {
        WebConfig.serverid = serverid;
    }


    public static String getParkDomain() {
        return parkDomain;
    }

    public  void setParkDomain(String parkDomain) {
        WebConfig.parkDomain = parkDomain;
    }


    public static String getNodeid() {
        return nodeid;
    }

    public  void setNodeid(String nodeid) {
        WebConfig.nodeid = nodeid;
    }


    public static String getCampusCPDomain() {
        return campusCPDomain;
    }

    public  void setCampusCPDomain(String campusCPDomain) {
        WebConfig.campusCPDomain = campusCPDomain;
    }

    public static String getCampusCPAppId() {
        return campusCPAppId;
    }

    public  void setCampusCPAppId(String campusCPAppId) {
        WebConfig.campusCPAppId = campusCPAppId;
    }

    public static String getCampusCPAppSecret() {
        return campusCPAppSecret;
    }

    public  void setCampusCPAppSecret(String campusCPAppSecret) {
        WebConfig.campusCPAppSecret = campusCPAppSecret;
    }

    public static boolean getStartNew() {
        return startNew;
    }

    public static String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        WebConfig.payType = payType;
    }
//    /**
//     * 当前应用启动端口
//     *
//     * @return
//     */
//    public static String getApplicationPort() {
//        return "2018";
//    }
//
//    /**
//     * 获取当前系统访问的地址及端口,例如http://www.xxx.com:8080/
//     *
//     * @param request
//     * @return
//     */
//    public static String getServerUrl(HttpServletRequest request) {
//        return String.format("http://%s:%s/", request.getServerName(), request.getServerPort());
//    }

    /**
     * 获取许可证文件存放文件夹
     *
     * @return
     */
    public static String getLicenseFolder() {
        return String.format("%slicense/%s/", WebConfig.getUploaddir(), CampusLicense.getLicenseSN());
    }

    public static boolean isSeaoAuthrizeHandleing() {
        return seaoAuthrizeHandleing;
    }

    public static void setSeaoAuthrizeHandleing(boolean seaoAuthrizeHandleing) {
        WebConfig.seaoAuthrizeHandleing = seaoAuthrizeHandleing;
    }

    public static boolean isSubscribeUseMinuteHandleing() {
        return subscribeUseMinuteHandleing;
    }

    public static void setSubscribeUseMinuteHandleing(boolean subscribeUseMinuteHandleing) {
        WebConfig.subscribeUseMinuteHandleing = subscribeUseMinuteHandleing;
    }

    public static boolean EnableCampusCPService() {
        if (!StringUtils.isBlank(WebConfig.getNodeid()) && !StringUtils.isBlank(WebConfig.getCampusCPDomain()) && !StringUtils.isBlank(WebConfig.getCampusCPAppId()) && !StringUtils.isBlank(WebConfig.getCampusCPAppSecret())) {
            return true;
        }
        return false;
    }

    public static String getMchntCode() {
        return mchntCode;
    }

    public void setMchntCode(String mchntCode) {
        WebConfig.mchntCode = mchntCode;
    }
//
//    @Configuration
//    public static class FrontEnd implements WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> {
//        @Override
//        public void customize(ConfigurableServletWebServerFactory factory) {
//            factory.setPort(2018);
//        }
//    }
}
