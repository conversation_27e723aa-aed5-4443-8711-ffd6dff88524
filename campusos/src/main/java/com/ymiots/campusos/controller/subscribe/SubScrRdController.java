package com.ymiots.campusos.controller.subscribe;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ymiots.campusos.service.subscribe.SubScrRdService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;

@RestController
@RequestMapping("/Subscribe/SubScrRd")
public class SubScrRdController {


    final String menuid="11b7d556-3ee1-11e9-8dfa-9c5c8e6dafdd";

    @Autowired
    private SubScrRdService service;
    
    @RequestMapping(value="/getSubScrRdList",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JsonData getSubScrRdList(HttpServletRequest request, HttpServletResponse response,String areacode, String datetype, String startdate, String enddate,String auditstatus,String status, boolean viewchild,String key,int start,int limit) {
        try {
            return service.getSubScrRdList(request, response, areacode,datetype, startdate, enddate, auditstatus, status, viewchild, key, start,limit);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }
    
    @RequestMapping(value="/DelSubScrBlack",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="del")
    public JsonResult DelSubScrBlack(HttpServletRequest request, HttpServletResponse response,String uids) {
        try {
            return service.DelSubScrBlack(request, response, uids);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value="/AuditSubscribe",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="del")
    public JsonResult AuditSubscribe(HttpServletRequest request, HttpServletResponse response,
    		String uid, String auditstatus, String emperr, String empblack, String auditremark) {
        try {
            return service.AuditSubscribe(request, response, uid, auditstatus, emperr, empblack, auditremark);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }
    
    @RequestMapping(value="/BatchAuditSubscribe",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="batchaudit")
    public JsonResult BatchAuditSubscribe(HttpServletRequest request, HttpServletResponse response,
    		String uids, String auditstatus, String emperr, String empblack, String auditremark) {
        try {
            return service.BatchAuditSubscribe(request, response, uids, auditstatus, emperr, empblack, auditremark);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }
    
}
