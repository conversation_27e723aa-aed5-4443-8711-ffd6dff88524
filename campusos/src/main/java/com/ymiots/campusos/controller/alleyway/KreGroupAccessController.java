package com.ymiots.campusos.controller.alleyway;

import com.ymiots.campusos.service.alleyway.KreGroupAccessService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022-02-04 23:55
 * @since 2022-02-04 23:55
 */
@RestController
@RequestMapping("/alleyway/KreGroupAccess")
public class KreGroupAccessController {

    final String menuid = "dd1a4592-154c-4084-8434-7478b09cb8fc";

    @Autowired
    KreGroupAccessService kreGroupAccessService;

    @RequestMapping(value = "/getGroup", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getGroup(HttpServletRequest request, HttpServletResponse response, String key,int start, int limit) {
        try {
            return kreGroupAccessService.getGroup(request, response, key,start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getNewGroup", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getNewGroup(HttpServletRequest request, HttpServletResponse response, String key, String areacode,int start, int limit) {
        try {
            return kreGroupAccessService.getNewGroup(request, response, key, areacode,start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getGroupAccess", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getGroupAccess(HttpServletRequest request, HttpServletResponse response, String groupid, String key, int start, int limit) {
        try {
            return kreGroupAccessService.getGroupAccess(request, response, groupid, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getInfoList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response, String groupid,
                                String orgcode, String key, String infotype, boolean viewchild, String intoyear, String infolabel, String selecteduid, int start, int limit) {
        try {
            return kreGroupAccessService.getInfoList(request, response, groupid, orgcode, key, infotype, viewchild, intoyear, infolabel, selecteduid, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/saveGroupAccess", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "add")
    public JsonResult saveGroupAccess(HttpServletRequest request, HttpServletResponse response, String groupid, String infoid) {
        try {
            return kreGroupAccessService.saveGroupAccess(request, response, groupid, infoid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/delGroupAccess", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "del")
    public JsonResult delGroupAccess(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return kreGroupAccessService.delGroupAccess(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }
}
