package com.ymiots.campusos.controller.visitor;

import com.ymiots.campusos.service.visitor.VisitorMachineService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/Visitor/VisitorMachine")
public class VisitorMachineController {

    final String menuid="838399ac-5d95-11e9-b584-b42e9902e98b";

    @Autowired
    private VisitorMachineService visitorMachineService;

    @RequestMapping(value="/getVistiorMachineList",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JsonData getVistiorMachineList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        try {
            return visitorMachineService.getVistiorMachineList(request, response, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }

    @RequestMapping(value="/addMachine",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="addmachine,editmachine")
    public JsonResult addMachine(HttpServletRequest request, HttpServletResponse response, String uid, String position, String name, String remark, MultipartRequest filepart) {
        try {
            return visitorMachineService.addMachine(request, response, uid, position, name, remark,filepart);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value="/delMachine",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="delmachine")
    public JsonResult delMachine(HttpServletRequest request, HttpServletResponse response, String uid, String position) {
        try {
            return visitorMachineService.delMachine(request, response, uid, position);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value="/getAllDeviceList",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JsonData getAllDeviceList(HttpServletRequest request, HttpServletResponse response,String position, String selecteduid, String key, int start, int limit) {
        try {
            return visitorMachineService.getAllDeviceList(request, response, position, selecteduid, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }

    @RequestMapping(value="/setDevice",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="setdevice")
    public JsonResult setDevice(HttpServletRequest request, HttpServletResponse response,String position,String devid,String doorids) {
        try {
            return visitorMachineService.setDevice(request, response, position, devid,doorids);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value="/addFloor",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="setdevice")
    public JsonResult addFloor(HttpServletRequest request, HttpServletResponse response,String position,String devid,String floor) {
        try {
            return visitorMachineService.addFloor(request, response, position, devid,floor);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value="/delDevice",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="deldevice")
    public JsonResult delDevice(HttpServletRequest request, HttpServletResponse response, String uid,String position ) {
        try {
            return visitorMachineService.delDevice(request, response, uid, position);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value="/getVMDeviceList",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JsonData getVMDeviceList(HttpServletRequest request, HttpServletResponse response, String position, String key, int start, int limit) {
        try {
            return visitorMachineService.getVMDeviceList(request, response,position, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }
}
