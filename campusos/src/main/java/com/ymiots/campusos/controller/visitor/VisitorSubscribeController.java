package com.ymiots.campusos.controller.visitor;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymiots.campusos.common.r.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ymiots.campusos.service.visitor.VisitorSubscribeService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;

@RestController
@RequestMapping("/Visitor/VisitorSubscribe")
public class VisitorSubscribeController {

    final String menuid = "0ffb05aa-b82c-49e0-bed3-76979b77888d";

    @Autowired
    private VisitorSubscribeService visitorSubscribeService;

    @RequestMapping(value = "/getSubscribeList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getSubscribeList(HttpServletRequest request, HttpServletResponse response,
                                     int auditstatus, String key, String starttime, String endtime, int start, int limit) {
        try {
            return visitorSubscribeService.getSubscribeList(request, response, auditstatus, key, starttime, endtime, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getVisitorList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getVisitorList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        try {
            return visitorSubscribeService.getVisitorList(request, response, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/DelSubscribe", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "del")
    public JsonResult DelSubscribe(HttpServletRequest request, HttpServletResponse response, String uids) {
        try {
            return visitorSubscribeService.DelSubscribe(request, response, uids);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 访客预约功能
     */
    @RequestMapping(value = "/SaveVisitorSubscribe", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "add")
    public R<?> SaveStudent(HttpServletRequest request, HttpServletResponse response,
                            String visitorid, String name, String sex, String idtype,
                            String idcard, String mobile, String cardsn, String qrcode,
                            String company, String department, String position,
                            String byvisitorid, String byvisitor, String othervisitorval,
                            String plateno, String reason, String things, String visittime, String leavetime,String areaPosition,String byvisitorcode) {
        try {
            return visitorSubscribeService.SaveVisitorSubscribe(request, response, visitorid, name, sex, idtype, idcard, mobile, cardsn, qrcode, company, department, position, byvisitorid, byvisitor, othervisitorval, plateno, things, reason, visittime, leavetime,areaPosition,byvisitorcode);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    @RequestMapping(value = "/isCanAudit", method = RequestMethod.POST)
//    @Permission(menu = menuid)
//    public JsonResult isCanAudit(HttpServletRequest request, HttpServletResponse response,String uid,String byvisitorid,String areaposition){
//        return visitorSubscribeService.isCanAudit(request, response, uid, byvisitorid, areaposition);
//    }

//    @RequestMapping(value = "/AuditSubscribe", method = RequestMethod.POST)
//    @Permission(menu = menuid, opcode = "audit")
//    public JsonResult AuditSubscribe(HttpServletRequest request, HttpServletResponse response, String uid, String visitorid, String cardsn, String qrcode, int auditstatusint, String remark, String visittimestr, String leavetimestr,String areaposition,String things,String reason) {
//        try {
//            return visitorSubscribeService.AuditSubscribe(request, response, uid, visitorid, cardsn, qrcode, auditstatusint, remark, visittimestr, leavetimestr, areaposition,things, reason);
//        } catch (Exception e) {
//            return Json.getJsonResult(false, e.getMessage());
//        }
//    }

    @RequestMapping(value = "/AuditSubscribe", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "audit")
    public JsonResult auditSubscribe(HttpServletRequest request, HttpServletResponse response, String uid, String visitorid, String cardsn, String qrcode, int auditstatusint, String remark, String visittimestr, String leavetimestr,String areaposition,String things,String reason) {
        try {
            return visitorSubscribeService.auditSubscribe(request, response, uid, visitorid, cardsn, qrcode, auditstatusint, remark, visittimestr, leavetimestr, areaposition,things, reason);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/CreateQRcode", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "audit")
    public JsonResult CreateQRcode(HttpServletRequest request, HttpServletResponse response) {
        try {
            return visitorSubscribeService.CreateQRcode(request, response);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }


    @RequestMapping(value = "/ShowByVisitor", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData ShowByVisitor(HttpServletRequest request, HttpServletResponse response, boolean viewchild, String orgcode, String key, int start, int limit) {
        try {
            return visitorSubscribeService.ShowByVisitor(request, response, viewchild, orgcode, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getSubscribeVisitorSuccess", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getSubscribeVisitorSuccess(String formId) {
        try {
            return visitorSubscribeService.getSubscribeVisitorSuccess(formId);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }
}
