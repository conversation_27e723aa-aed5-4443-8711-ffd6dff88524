package com.ymiots.campusos.controller.workflow;

import com.ymiots.campusos.service.workflow.ApproverService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: 邱文彬
 * @Date: 2022/2/2511:48
 * @Version 1.0
 */

@RestController
@RequestMapping("/Workflow/Approver")
public class ApproverController {

    final String menuid = "9a998880-9794-4600-acfd-941c9cb1d033";

    /**
     * 审核人模块service层
     */
    @Autowired
    private ApproverService approverService;

    /**
     * 根据节点ID查询审核人 (硬性)
     * @param request
     * @param response
     * @param nodeid
     * @param start
     * @param limit
     * @return
     */
    @RequestMapping(value = "/getApproverList" , method = RequestMethod.POST)
    @Permission(menu=menuid,opcode = "selapprover")
    public JsonData getApproverList(HttpServletRequest request, HttpServletResponse response, String key,String nodeid, int start, int limit){
        try {
            return approverService.getApproverList(request, response, key, nodeid,start,limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getInfoList",method = RequestMethod.POST)
    public JsonData getInfoList(HttpServletRequest request, String orgcode, String key, String infotype, boolean viewchild,
                                String property, String infolabel, String selecteduid, int start, int limit){
        return approverService.getInfoList(request, orgcode, key, infotype, viewchild, property, infolabel, selecteduid, start, limit);
    }

    /**
     * 根据审核人员表uid删除数据
     * @param request
     * @param response
     * @param uid
     * @return
     */
    @RequestMapping(value = "/delApprover", method = RequestMethod.POST)
    @Permission(menu=menuid,opcode = "selapprover")
    public JsonResult delApprover(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return approverService.delNameList(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 根据审核人员表uid设置默认审核人
     * @param request
     * @param response
     * @param uid
     * @return
     */
    @RequestMapping(value = "/setDefaultApprover", method = RequestMethod.POST)
    @Permission(menu=menuid,opcode = "selapprover")
    public JsonResult setDefaultApprover(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return approverService.setDefaultApprover(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 增加审核人 (必须带有节点id,审核人id)
     * @param request
     * @param nodeid
     * @param listInfoid
     * @return
     */

    @RequestMapping(value = "/saveApprover", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "addapprover")
    public JsonResult saveApprover(HttpServletRequest request, String nodeid, String listInfoid){
        try {
            return approverService.saveApprover(request, nodeid,listInfoid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }
}
