package com.ymiots.campusos.controller.time;

import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.entity.time.TimeRecordRow;
import com.ymiots.campusos.service.time.TimeRecordsService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Null;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/time/TimeRecords")
public class TimeRecordsController {

    final String menuid="de8ebf85-13e8-11e9-be1d-9c5c8e6dafdd";

    @Autowired
    TimeRecordsService timeRecordsService;

    //@RequestMapping(value = "/getTimeRecordsList",method = RequestMethod.POST)
    //@Permission(menu = menuid)
    //public JsonData getTimeRecordsList(HttpServletRequest request, HttpServletResponse response, String key, String starttime,String endtime,int start, int limit){
    //    try {
    //        return timeRecordsService.getTimeRecordsList(request, response, key, starttime, endtime, start, limit);
    //    } catch (Exception e) {
    //        return Json.getJsonData(false,e.getMessage());
    //    }
    //}

    @RequestMapping(value = "/getTimeRecordsList",method = RequestMethod.POST)
    @Permission(menu = menuid)
    public R<List<TimeRecordRow>> getTimeRecordsList(HttpServletRequest request, HttpServletResponse response, String key,
                                                     String orgcode,String starttime, String endtime, int start, int limit){
        try {
            return timeRecordsService.getNewTimeRecordsList(request, response, key,orgcode, starttime, endtime, start, limit);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(new ArrayList<>());
        }
    }

    @RequestMapping(value = "/getTimeHistoryRecordsList",method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getTimeHistoryRecordsList(HttpServletRequest request, HttpServletResponse response, String key, String starttime,String endtime,int start, int limit){
        try {
            return timeRecordsService.getTimeHistoryRecordsList(request, response, key, starttime, endtime, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }

    @RequestMapping(value = "/ExportERPTimeRecords",method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "hrexport")
    public JsonResult ExportERPTimeRecords(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime ){
        try {
            String filepath=timeRecordsService.ExportERPTimeRecords(request, response, key, starttime, endtime);
            if(!StringUtils.isBlank(filepath)){
                return Json.getJsonResult(true,filepath);
            }else{
                return Json.getJsonResult(false, "无导出数据");
            }
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }

    //@RequestMapping(value = "/ExportTimeRecords",method = RequestMethod.POST)
    //@Permission(menu = menuid,opcode = "export")
    //public JsonResult ExportTimeRecords(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime ){
    //    try {
    //        String filepath=timeRecordsService.ExportTimeRecords(request, response, key, starttime, endtime);
    //        if(!StringUtils.isBlank(filepath)){
    //            return Json.getJsonResult(true,filepath);
    //        }else{
    //            return Json.getJsonResult(false, "无导出数据");
    //        }
    //    } catch (Exception e) {
    //        return Json.getJsonResult(e.getMessage());
    //    }
    //}

    @RequestMapping(value = "/ExportTimeRecords",method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "export")
    public R<Null> ExportTimeRecords(HttpServletRequest request, HttpServletResponse response, String key,String orgcode, String starttime, String endtime ){
        try {
            return timeRecordsService.exportTimeRecord(request, response, key,orgcode, starttime, endtime);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @RequestMapping(value = "/ExportTimeRecordsTxt",method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "exporttxt")
    public JsonResult ExportTimeRecordsTxt(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime){
        try {
            String filepath=timeRecordsService.ExportTimeRecordsTxt(request, response, key, starttime, endtime);
            if(!StringUtils.isBlank(filepath)){
                return Json.getJsonResult(true,filepath);
            }else{
                return Json.getJsonResult(false, "无导出数据");
            }
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }
    @RequestMapping(value = "/ExportHistoryERPTimeRecords",method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "hrexport")
    public JsonResult ExportHistoryERPTimeRecords(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime ){
        try {
            String filepath=timeRecordsService.ExportHistoryERPTimeRecords(request, response, key, starttime, endtime);
            if(!StringUtils.isBlank(filepath)){
                return Json.getJsonResult(true,filepath);
            }else{
                return Json.getJsonResult(false, "无导出数据");
            }
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }

    @RequestMapping(value = "/ExportHistoryTimeRecords",method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "export")
    public JsonResult ExportHistoryTimeRecords(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime ){
        try {
            String filepath=timeRecordsService.ExportHistoryTimeRecords(request, response, key, starttime, endtime);
            if(!StringUtils.isBlank(filepath)){
                return Json.getJsonResult(true,filepath);
            }else{
                return Json.getJsonResult(false, "无导出数据");
            }
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }

    @RequestMapping(value = "/ExportHistoryTimeRecordsTxt",method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "exporttxt")
    public JsonResult ExportHistoryTimeRecordsTxt(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime){
        try {
            String filepath=timeRecordsService.ExportHistoryTimeRecordsTxt(request, response, key, starttime, endtime);
            if(!StringUtils.isBlank(filepath)){
                return Json.getJsonResult(true,filepath);
            }else{
                return Json.getJsonResult(false, "无导出数据");
            }
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }
}
