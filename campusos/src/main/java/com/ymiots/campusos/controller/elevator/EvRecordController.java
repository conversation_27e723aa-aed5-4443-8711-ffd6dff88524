package com.ymiots.campusos.controller.elevator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ymiots.campusos.service.elevator.EvRecordService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.filter.Permission;

@RestController
@RequestMapping("/Elevator/EvRecord")
public class EvRecordController {

    final String menuid = "6c885f7f-e8a6-463d-91ff-441e4a64e1a1";

    @Autowired
    private EvRecordService erecordService;
    
    @RequestMapping(value = "/getEvRecordsList", method = RequestMethod.POST)
    @Permission(menu = menuid)
	public JsonData getEvRecordsList(HttpServletRequest request, HttpServletResponse response,String starttime, String endtime, String key,int start,int limit){
		try {
            return erecordService.getEvRecordsList(request, response, starttime, endtime, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
	}
	
	
	
}
