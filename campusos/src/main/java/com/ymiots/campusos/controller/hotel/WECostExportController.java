package com.ymiots.campusos.controller.hotel;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.service.waterctrl.WECostExportServiceSimple;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 水电费账单导出控制器
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Controller
@RequestMapping("/Hotel/WECost")
public class WECostExportController {

    @Autowired
    private WECostExportServiceSimple exportService;

    /**
     * 导出水电费账单
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param areaCode 区域编码
     * @param exportType 导出类型 (excel/pdf)
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 导出结果
     */
    @RequestMapping("/exportBill")
    @ResponseBody
    public JsonResult exportBill(@RequestParam String startDate,
                                @RequestParam String endDate,
                                @RequestParam(required = false) String areaCode,
                                @RequestParam String exportType,
                                HttpServletRequest request,
                                HttpServletResponse response) {
        try {
            Log.info(WECostExportController.class, 
                String.format("开始导出水电费账单 - 日期: %s至%s, 区域: %s, 类型: %s", 
                    startDate, endDate, areaCode, exportType));

            JsonResult result;
            
            switch (exportType.toLowerCase()) {
                case "excel":
                    result = exportService.exportToExcel(startDate, endDate, areaCode, response);
                    break;
                case "pdf":
                    // PDF功能暂未实现，返回错误
                    JsonResult pdfResult = new JsonResult();
                    pdfResult.setSuccess(false);
                    pdfResult.setMsg("PDF导出功能暂未实现，请使用Excel导出");
                    return pdfResult;
                default:
                    JsonResult errorResult = new JsonResult();
                    errorResult.setSuccess(false);
                    errorResult.setMsg("不支持的导出类型: " + exportType);
                    return errorResult;
            }

            if (result.isSuccess()) {
                Log.info(WECostExportController.class, 
                    String.format("水电费账单导出成功 - 类型: %s", exportType));
            }

            return result;

        } catch (Exception e) {
            Log.error(WECostExportController.class,
                String.format("导出水电费账单失败: %s", e.getMessage()));

            JsonResult errorResult = new JsonResult();
            errorResult.setSuccess(false);
            errorResult.setMsg("导出失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 生成打印内容
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param areaCode 区域编码
     * @return 打印内容
     */
    @RequestMapping("/generatePrintContent")
    @ResponseBody
    public JsonResult generatePrintContent(@RequestParam String startDate,
                                         @RequestParam String endDate,
                                         @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostExportController.class, 
                String.format("生成打印内容 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);

            // 创建JSONObject返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("生成成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportController.class,
                String.format("生成打印内容失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("生成失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 预览账单
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param areaCode 区域编码
     * @return 账单预览数据
     */
    @RequestMapping("/previewBill")
    @ResponseBody
    public JsonResult previewBill(@RequestParam String startDate,
                                 @RequestParam String endDate,
                                 @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostExportController.class, 
                String.format("预览账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            // 直接生成HTML内容用于预览
            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);

            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("预览成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportController.class,
                String.format("预览账单失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("预览失败: " + e.getMessage());
            return result;
        }
    }

//    /**
//     * 批量导出账单
//     * @param startDate 开始日期
//     * @param endDate 结束日期
//     * @param areaCodes 区域编码列表（逗号分隔）
//     * @param exportType 导出类型
//     * @param response HTTP响应
//     * @return 导出结果
//     */
//    @RequestMapping("/batchExportBill")
//    @ResponseBody
//    public JsonResult batchExportBill(@RequestParam String startDate,
//                                     @RequestParam String endDate,
//                                     @RequestParam String areaCodes,
//                                     @RequestParam String exportType,
//                                     HttpServletResponse response) {
//        try {
//            Log.info(WECostExportController.class,
//                String.format("批量导出账单 - 日期: %s至%s, 区域: %s, 类型: %s",
//                    startDate, endDate, areaCodes, exportType));
//
//            String[] areaCodeArray = areaCodes.split(",");
//            JsonResult result = exportService.batchExport(startDate, endDate, areaCodeArray, exportType, response);
//
//            if (result.isSuccess()) {
//                Log.info(WECostExportController.class,
//                    String.format("批量导出账单成功 - 区域数量: %d", areaCodeArray.length));
//            }
//
//            return result;
//
//        } catch (Exception e) {
//            Log.error(WECostExportController.class,
//                String.format("批量导出账单失败: %s", e.getMessage()));
//
//            JsonResult result = new JsonResult();
//            result.setSuccess(false);
//            result.setMsg("批量导出失败: " + e.getMessage());
//            return result;
//        }
//    }
}
