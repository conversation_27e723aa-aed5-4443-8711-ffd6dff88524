package com.ymiots.campusos.controller.hotel;

import com.ymiots.campusos.service.waterctrl.WECostExportService;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 水电费账单导出控制器
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Controller
@RequestMapping("/Hotel/WECost")
public class WECostExportController {

    @Autowired
    private WECostExportService exportService;

    /**
     * 导出Excel账单
     */
    @RequestMapping("/exportExcel")
    @ResponseBody
    public JsonResult exportExcel(@RequestParam String startDate,
                                 @RequestParam String endDate,
                                 @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostExportController.class, 
                String.format("开始导出Excel账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            JsonResult result = exportService.exportToExcel(startDate, endDate, areaCode);
            
            if (result.isSuccess()) {
                Log.info(WECostExportController.class, "Excel账单导出成功");
            }

            return result;

        } catch (Exception e) {
            Log.error(WECostExportController.class, 
                String.format("导出Excel账单失败: %s", e.getMessage()), e);
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("导出失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 生成打印内容
     */
    @RequestMapping("/generatePrintContent")
    @ResponseBody
    public JsonResult generatePrintContent(@RequestParam String startDate,
                                         @RequestParam String endDate,
                                         @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostExportController.class, 
                String.format("生成打印内容 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);
            
            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("生成成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportController.class, 
                String.format("生成打印内容失败: %s", e.getMessage()), e);
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("生成失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 检查导出状态
     */
    @RequestMapping("/checkExportStatus")
    @ResponseBody
    public JsonResult checkExportStatus(@RequestParam String startDate,
                                       @RequestParam String endDate,
                                       @RequestParam(required = false) String areaCode) {
        try {
            // 检查是否有数据
            com.alibaba.fastjson.JSONArray records = exportService.getCostRecords(startDate, endDate, areaCode);
            
            JSONObject data = new JSONObject();
            data.put("hasData", records.size() > 0);
            data.put("recordCount", records.size());
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            
            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("检查完成");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportController.class, 
                String.format("检查导出状态失败: %s", e.getMessage()), e);
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("检查失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 预览账单
     */
    @RequestMapping("/previewBill")
    @ResponseBody
    public JsonResult previewBill(@RequestParam String startDate,
                                 @RequestParam String endDate,
                                 @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostExportController.class, 
                String.format("预览账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            // 生成HTML内容用于预览
            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);
            
            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));
            
            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("预览成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportController.class, 
                String.format("预览账单失败: %s", e.getMessage()), e);
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("预览失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 下载导出的文件
     */
    @RequestMapping("/downloadFile")
    @ResponseBody
    public JsonResult downloadFile(@RequestParam String filePath) {
        try {
            Log.info(WECostExportController.class, 
                String.format("下载文件 - 路径: %s", filePath));

            // 这里可以添加文件下载逻辑
            // 或者直接返回文件路径供前端下载
            
            JSONObject data = new JSONObject();
            data.put("downloadUrl", filePath);
            data.put("downloadTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));
            
            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("文件准备完成");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportController.class, 
                String.format("下载文件失败: %s", e.getMessage()), e);
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("下载失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取导出模板信息
     */
    @RequestMapping("/getExportTemplate")
    @ResponseBody
    public JsonResult getExportTemplate() {
        try {
            JSONObject data = new JSONObject();
            
            // 普通账单字段
            JSONObject normalFields = new JSONObject();
            normalFields.put("equipment_code", "设备编号");
            normalFields.put("equipment_type_name", "设备类型");
            normalFields.put("area_code", "区域编码");
            normalFields.put("reading_date", "读数日期");
            normalFields.put("current_reading", "当前读数");
            normalFields.put("previous_reading", "上次读数");
            normalFields.put("usage_amount", "用量");
            normalFields.put("unit_price", "单价");
            normalFields.put("total_cost", "费用");
            normalFields.put("current_balance", "余额");
            normalFields.put("time_sharing_info", "分时信息");
            
            data.put("normalFields", normalFields);
            data.put("supportedFormats", new String[]{"xlsx"});
            data.put("maxRecords", 10000);
            
            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("获取成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportController.class, 
                String.format("获取导出模板失败: %s", e.getMessage()), e);
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("获取失败: " + e.getMessage());
            return result;
        }
    }
}
