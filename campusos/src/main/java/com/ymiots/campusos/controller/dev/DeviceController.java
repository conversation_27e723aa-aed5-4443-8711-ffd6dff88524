package com.ymiots.campusos.controller.dev;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.service.dev.DeviceService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;

@RestController
@RequestMapping("/Dev/Device")
public class DeviceController {
	
	 final String menuid="518fa2f0-07c2-11e8-9075-9c5c8e6dafdd";
	 
	 @Autowired
	 DeviceService devservice;
	 
	@RequestMapping(value="/PingDevice",method = RequestMethod.POST)
	@Permission(menu=menuid)
	public JsonResult PingDevice(HttpServletRequest request, HttpServletResponse response,String ip,String topic){
		try {
			return devservice.PingDevice(request, response,ip,topic);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}
	
	//设备、门禁、道闸、出入管控按人授权、按门或通道授权、床位规划、预约系统预约管理模块使用
	@RequestMapping(value="/getAreaTree",method = RequestMethod.POST)
	@Permission()
	public JSONArray getAreaTree(HttpServletRequest request, HttpServletResponse response,String key){
		try {
			return devservice.getAreaTree(request, response,key,true);
		} catch (Exception e) {
			return new JSONArray();
		}
	}

	@RequestMapping(value="/getDeviceList",method = RequestMethod.POST)
	@Permission(menu=menuid)
	public JsonData getDeviceList(HttpServletRequest request, HttpServletResponse response,String areacode,String devclass, String server_uid, boolean viewchild,String key,int start,int limit){
		try {
			return devservice.getDeviceList(request, response,areacode,devclass,server_uid,viewchild, key, start, limit);
		} catch (Exception e) {
			return  Json.getJsonData(false,e.getMessage());
		}
	}
	@RequestMapping(value="/getDoorList",method = RequestMethod.POST)
	@Permission(menu=menuid)
	public JsonData getDoorList(HttpServletRequest request, HttpServletResponse response,String controllerid){
		try {
			return devservice.getDoorList(request, response,controllerid);
		} catch (Exception e) {
			return  Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/updateDoorRead",method = RequestMethod.POST)
	@Permission(menu=menuid)
	public JsonResult updateDoorRead(HttpServletRequest request, HttpServletResponse response){
		try {
			return devservice.updateDoorRead(request, response);
		} catch (Exception e) {
			return  Json.getJsonResult(false,e.getMessage());
		}
	}

	@RequestMapping(value="/SaveOrUpdateDoor",method = RequestMethod.POST)
	@Permission(menu=menuid)
	public JsonResult SaveOrUpdateDoor(HttpServletRequest request, HttpServletResponse response,String uid,String controllerid,String name,String relays){
		try {
			return devservice.SaveOrUpdateDoor(request, response,uid,controllerid,name,relays);
		} catch (Exception e) {
			return  Json.getJsonResult(false,e.getMessage());
		}
	}
	
	
	@RequestMapping(value="/getDeviceReadList",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode="edit")
	public JsonData getDeviceReadList(HttpServletRequest request, HttpServletResponse response,String controllerid,String relays){
		try {
			return devservice.getDeviceReadList(request, response,controllerid,relays);
		} catch (Exception e) {
			return  Json.getJsonData(false,e.getMessage());
		}
	}
	
	
	@RequestMapping(value="/SaveDevice",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode="add,edit")
	public JsonResult SaveDevice(HttpServletRequest request, HttpServletResponse response,String uid,String devsn,String machineid,
			String devname,String devip,String devport,String useclass,String server_uid,String devclass,String areacode,String door,String used, String devmodel){
		try {
			return devservice.SaveDevice(request, response,uid,devsn,machineid,devname,devip,devport,useclass,server_uid,devclass,areacode,door,used,devmodel);
		} catch (Exception e) {
			return  Json.getJsonResult(e.getMessage());
		}
	}
	
	@RequestMapping(value="/DelDevice",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode="del")
	public JsonResult DelDevice(HttpServletRequest request, HttpServletResponse response,String devid){
		try {
			return devservice.DelDevice(request, response,devid);
		} catch (Exception e) {
			return  Json.getJsonResult(e.getMessage());
		}
	}
	@RequestMapping(value="/DelDoorByUid",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode="del")
	public JsonResult DelDoorByUid(HttpServletRequest request, HttpServletResponse response,String uid,String controllerid){
		try {
			return devservice.DelDoorByUid(request, response,uid,controllerid);
		} catch (Exception e) {
			return  Json.getJsonResult(e.getMessage());
		}
	}
	
	@RequestMapping(value="/MoveDevice",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode="edit")
	public JsonResult MoveDevice(HttpServletRequest request, HttpServletResponse response,String devid,String areacode){
		try {
			return devservice.MoveDevice(request, response,devid,areacode);
		} catch (Exception e) {
			return  Json.getJsonResult(e.getMessage());
		}
	}
	
	
	@RequestMapping(value="/SaveDeviceRead",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode="edit")
	public JsonResult SaveDeviceRead(HttpServletRequest request, HttpServletResponse response,String uid, String controllerid,String devclass,
			String name, String inouttype, String readhead, String relays, String voice, String readdevid,String areacode){
		try {
			return devservice.SaveDeviceRead(request, response, uid,controllerid, devclass,name, inouttype, readhead, relays, voice, readdevid,areacode);
		} catch (Exception e) {
			return  Json.getJsonResult(e.getMessage());
		}
	}
	
	@RequestMapping(value="/DelDeviceRead",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode="edit")
	public JsonResult DelDeviceRead(HttpServletRequest request, HttpServletResponse response,String uid){
		try {
			return devservice.DelDeviceRead(request, response,uid);
		} catch (Exception e) {
			return  Json.getJsonResult(e.getMessage());
		}
	}
	

	@RequestMapping(value = "/notifyService", method = RequestMethod.POST)
	@Permission(menu = menuid, opcode = "edit")
	public JsonResult NotifyService(HttpServletRequest request, HttpServletResponse response,String serveruid, String machineid) {
		try {
			return devservice.NotifyService(request, response, serveruid, machineid);
		} catch (Exception e) {
			return Json.getJsonResult(false, e.getMessage());
		}
	}

    @RequestMapping(value="/getInfoList",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response,
                                String orgcode,String key,String infotype,boolean viewchild, String intoyear, String selecteduid,int start,int limit){
        try {
            return devservice.getInfoList(request, response,orgcode,key,infotype,viewchild,intoyear, selecteduid, start,limit);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }

    @RequestMapping(value="/getDevAdminList",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JsonData getDevAdminList(HttpServletRequest request, HttpServletResponse response, String devid, String key, int start, int limit){
        try {
            return devservice.getDevAdminList(request, response, devid, key, start, limit);
        } catch (Exception e) {
            return  Json.getJsonData(false,e.getMessage());
        }
    }

    @RequestMapping(value="/setDevAdmin",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="setadmin")
    public JsonResult setDevAdmin(HttpServletRequest request, HttpServletResponse response,String devid,String infoid){
        try {
            return devservice.setDevAdmin(request, response, devid, infoid);
        } catch (Exception e) {
            return  Json.getJsonResult(e.getMessage());
        }
    }

    @RequestMapping(value="/delAdmin",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JsonResult delAdmin(HttpServletRequest request, HttpServletResponse response,String uid){
        try {
            return devservice.delAdmin(request, response, uid);
        } catch (Exception e) {
            return  Json.getJsonResult(e.getMessage());
        }
    }
    
    
    @RequestMapping(value="/SyncCampusOSDevice",method = RequestMethod.POST)
    public JsonResult SyncCampusOSDevice(HttpServletRequest request, HttpServletResponse response, String nodeid, String appid){
        try {
            return devservice.SyncCampusOSDevice(request, response, nodeid, appid);
        } catch (Exception e) {
            return  Json.getJsonResult(e.getMessage());
        }
    }
}
