package com.ymiots.campusos.controller.teach;

import com.ymiots.campusos.service.teach.ViopconfigService;
import com.ymiots.campusos.service.time.RuleconfigService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/teach/Viopconfig")
public class ViopconfigController {



    @Autowired
    private ViopconfigService viopconfigService;

    @RequestMapping(value = "/getRule", method = RequestMethod.POST)
    public JsonData getRule(HttpServletRequest request, HttpServletResponse response) {
        try {
            return viopconfigService.getRule(request, response);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/saveRule", method = RequestMethod.POST)
    public JsonResult saveRule(HttpServletRequest request, HttpServletResponse response,String heartbeatFrequency,String callTime,
                               String timeOnOff, String forbidPhone, String phoneType, String messageFlag, String checkSosPhone, String achievementFlag,
                               String separateBillingFlag, String costFlag, String verifyFlag){
        try {
            return viopconfigService.saveRule(request, response, heartbeatFrequency, callTime, timeOnOff, forbidPhone, phoneType, messageFlag,
                    checkSosPhone, achievementFlag,separateBillingFlag,costFlag,verifyFlag);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }
}
