package com.ymiots.campusos.controller.alleyway;

import com.ymiots.campusos.service.alleyway.CarTransportReportService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/AlleyWay/CarTransportRecords")
public class CarTransportReportController {

    final String daymenuid = "e2c64ed0-164e-497b-8af8-f3215867b069";
    final String monthmenuid = "75593ac0-773f-4302-b5bb-4697e3f25388";

    @Autowired
    private CarTransportReportService service;

    @RequestMapping(value = "/getCarNOList", method = RequestMethod.POST)
    public JsonData getCarNOList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        try {
            return service.getCarNOList(request, response, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getCarDayReport", method = RequestMethod.POST)
    @Permission(menu = daymenuid)
    public JsonData getCarDayReport(HttpServletRequest request, HttpServletResponse response, String startdate,String enddate, String plateno, int start, int limit) {
        try {
            return service.getCarDayReport(request, response, startdate, enddate, plateno, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getCarMonthReport", method = RequestMethod.POST)
    @Permission(menu = monthmenuid)
    public JsonData getCarMonthReport(HttpServletRequest request, HttpServletResponse response,String startdate,String enddate,String plateno,int start,int limit){
        try {
            return service.getCarMonthReport(request, response, startdate, enddate, plateno, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value="/ExportDaily",method = RequestMethod.POST)
    @Permission(menu=daymenuid,opcode="exportdaily")
    public JsonResult ExportDaily(HttpServletRequest request, HttpServletResponse response, String startdate, String enddate, String plateno){
        try {
            String filepath= service.ExportDaily(request, response, startdate, enddate, plateno);
            return Json.getJsonResult(true,filepath);
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }

    @RequestMapping(value="/ExportMonth",method = RequestMethod.POST)
    @Permission(menu=monthmenuid,opcode="exportmonth")
    public JsonResult ExportMonth(HttpServletRequest request, HttpServletResponse response, String startdate, String enddate, String plateno){
        try {
            String filepath= service.ExportMonth(request, response, startdate, enddate, plateno);
            return Json.getJsonResult(true,filepath);
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }

    @RequestMapping(value="/getAnalysisDailyLits",method = RequestMethod.POST)
    @Permission(menu=daymenuid)
    public JsonData getAnalysisDailyLits(HttpServletRequest request, HttpServletResponse response, String start, String end){
        try {
            return service.getAnalysisDailyLits(request, response, start,end);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }

    @RequestMapping(value="/AnalysisDaily",method = RequestMethod.POST)
    @Permission(menu=daymenuid)
    public JsonResult AnalysisDaily(HttpServletRequest request, HttpServletResponse response, String daily){
        try {
            return service.AnalysisDaily(request, response, daily);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }
}
