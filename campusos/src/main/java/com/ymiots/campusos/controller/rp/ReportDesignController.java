package com.ymiots.campusos.controller.rp;

import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.service.rp.ReportDesignService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.entity.GridTable;
import com.ymiots.framework.filter.Permission;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/rp/reportDesign")
public class ReportDesignController {
    final String menuid = "18751a3a-f7e2-4c04-889e-07b76c452d2f";

    @Autowired
    ReportDesignService reportDesignService;

    @RequestMapping(value = "/getReportDesignList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getReportDesignList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        try {
            return reportDesignService.getReportDesignList(request, response, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getReportList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getReportList(HttpServletRequest request, HttpServletResponse response, String key, String typeuid, int start, int limit) {
        try {

            return reportDesignService.getReportList(request, response, key, typeuid, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

	/*@RequestMapping(value="/SaveInsert",method = RequestMethod.POST)
	@Permission(menu=menuid)
	public JsonResult SaveInsert(HttpServletRequest request, HttpServletResponse response,String uid,String typeuid,String name,String status) {
		try {
			return reportDesignService.SaveInsert(request, response, uid, typeuid, name, status);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}*/

    @RequestMapping(value = "/saveReportDesignInsert", method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "add,edit")
    public JsonResult saveReportDesignInsert(HttpServletRequest request, HttpServletResponse response, String uid, String reporttype, String name, String sqltype, String sqltext, String reportfile, String status, MultipartRequest filepart, String tablem) {
        try {
            MultipartFile file = filepart.getFile("tablenameinput");
            if (!StringUtils.isBlank(tablem)) {
                if (tablem.equals("1")) {

                    if (file == null) {
                        return Json.getJsonResult(false, "请选择需要上传的报表grf模板");
                    }
                    if (file.getSize() == 0) {
                        return Json.getJsonResult(false, "上传文件为空文件");
                    }
                    String filename = file.getOriginalFilename();
                    String ext = filename.substring(filename.lastIndexOf(".")).toLowerCase();
                    if (!ext.equals(".grf")) {
                        return Json.getJsonResult(false, "请选择上传grf模板文档，扩展名必须为.grf");
                    }
                    return reportDesignService.saveReportDesignInsert(request, response, uid, reporttype, name, sqltype, sqltext, reportfile, status, file, tablem);
                }
            }
            return reportDesignService.saveReportDesignInsert(request, response, uid, reporttype, name, sqltype, sqltext, reportfile, status, file, tablem);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/copytable", method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "copetable")
    public JsonResult copytable(HttpServletRequest request, HttpServletResponse response, String tableuid) {
        try {
            return reportDesignService.copytable(request, response, tableuid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/delReport", method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "del")
    public JsonResult delReport(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return reportDesignService.delReport(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getReportParamete", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getReportParamete(HttpServletRequest request, HttpServletResponse response, String key, String tuid, int start, int limit) {
        try {
            return reportDesignService.getReportParamete(request, response, key, tuid, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/SaveReportParamete", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "addparams,editparams")
    public JsonResult SaveReportParamete(HttpServletRequest request, HttpServletResponse response, String uid, String tid, String code, String name, String controltype, String controlconfig, String controlsort, String status) {
        try {
            return reportDesignService.SaveReportParamete(request, response, uid, tid, code, name, controltype, controlconfig, controlsort, status);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/delReportParamete", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "delparams")
    public JsonResult delReportParamete(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return reportDesignService.delReportParamete(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /*
    @RequestMapping(value = "/getsqltext", method = RequestMethod.POST)
	@Permission(menu = menuid)
	public JsonData getsqltext(HttpServletRequest request, HttpServletResponse response, String sqltype,String sqltext) {
		try {
			return reportDesignService.getsqltext(request, response, sqltype, sqltext);
		} catch (Exception e) {
			return Json.getJsonData(false, e.getMessage());
		}
	}*/

    @RequestMapping(value = "/SaveReportTemplate", method = RequestMethod.POST)
    public JsonResult SaveReportTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            int DataLen = request.getContentLength();
            if (DataLen > 0) {
                String filename = String.format("%s.grf", request.getParameter("filename"));
                String filecontent = HttpRequest.GetNoKeyData(request);
                byte[] file = filecontent.getBytes("utf-8");
                FileHelper.SaveByte(file, WebConfig.getUploaddir(), "report", filename);
            }
            return Json.getJsonResult(true);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/ReportDataSource", method = RequestMethod.POST)
    public GridTable ReportDataSource(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return reportDesignService.ReportDataSource(request, response, uid);
        } catch (Exception e) {
            Log.error(this.getClass(), "报表[" + uid + "]数据源错误：" + e.getMessage());
            return new GridTable();
        }
    }

    @RequestMapping(value = "/CheckReportDataSource", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData CheckReportDataSource(HttpServletRequest request, HttpServletResponse response, String reportid) {
        try {
            return reportDesignService.CheckReportDataSource(request, response, reportid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

}

