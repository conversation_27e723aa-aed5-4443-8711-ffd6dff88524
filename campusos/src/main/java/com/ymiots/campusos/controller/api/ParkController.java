package com.ymiots.campusos.controller.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.WebConfig;

import com.ymiots.campusos.service.api.ParkService;
import com.ymiots.framework.common.HttpRequest;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.MD5;
import com.ymiots.framework.filter.Permission;
import org.opencv.core.*;
import org.opencv.highgui.HighGui;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import org.opencv.objdetect.CascadeClassifier;
import org.opencv.videoio.VideoCapture;
import org.opencv.videoio.VideoWriter;
import org.opencv.videoio.Videoio;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.net.URLEncoder;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/API/Park/")
public class ParkController {

    @Autowired
    ParkService park;

    //@Autowired
    //private FaceRecognitionService faceRecognitionService;

    // 初始化人脸探测器
    static CascadeClassifier faceDetector;

    static CascadeClassifier eyeDetector;// 加载眼睛检测器
    static int i = 0;

//    static {
//        // 判断系统
//        String os = System.getProperty("os.name");
//        // 加载动态库
//        String currentDir = System.getProperty("user.dir");
//        // 获取当前目录的父目录
//        String parentDir = new File(currentDir).getParent();
//        // 加载动态库
//        if (os != null && os.toLowerCase().startsWith("windows")) {
//            // Windows操作系统
//            // todo windows 系统部署加载 .dll 文件 - 路径跟据自己存放位置更改【这里需要使用绝对路径】
//            // 构建DLL文件的完整路径
//            //String dllPath = Paths.get(parentDir, "opencv/opencv_java490.dll").toString();
//            //// 加载 DLL 文件
//            //System.load(dllPath);
//        } else if (os != null && os.toLowerCase().startsWith("linux")) {
//            // Linux操作系统
//            // todo Linux 服务器部署加载 .so 文件 - 路径跟据自己存放位置更改【是否需要绝对路径有待验证，目前只在windows 系统实践过】
////            System.load("/opt/face/libopencv_java440.so");
//        }
//
//        // 引入 特征分类器配置 文件：haarcascade_frontalface_alt.xml 文件路径
//        // 此文件在opencv的安装目录build\etc\haarcascades下可以找到
//        // 加载人脸分类器
//        //String property = Paths.get(parentDir, "opencv/haarcascade_frontalface_alt.xml").toString();
//        //faceDetector = new CascadeClassifier(property);
//        //// 获取分类器文件路径
//        //String eyeXmlFilePath = Paths.get(parentDir, "opencv/haarcascade_eye.xml").toString();// 眼睛检测分类器路径
//        //eyeDetector = new CascadeClassifier(eyeXmlFilePath); // 加载眼睛检测器
//    }

    @RequestMapping(value = "/StartParkOS", method = RequestMethod.GET)
    @Permission(authorization = true)
    public String StartParkOS(HttpServletRequest request, HttpServletResponse response, Model model) {
        JSONArray list = park.GetParkApplicationInfo();
        if (list.size() == 0) {
            model.addAttribute("error", "第三方应用无授权信息，serviceid=3");
            return "error";
        } else {
            JSONObject item = list.getJSONObject(0);
            long timestamp = System.currentTimeMillis();
            String appId = item.getString("appid");
            String appsecret = item.getString("appsecret");
            String data = String.format("appId=%s&timestamp=%s&key=%s", appId, timestamp, appsecret);
            String signature = MD5.MD5Encode(data);
            JSONObject params = new JSONObject();
            params.put("appId", appId);
            params.put("signature", signature);
            params.put("timestamp", String.valueOf(timestamp));
            String url = String.format("%s/api/login_in_app", WebConfig.getParkDomain());
            JsonResult result = HttpRequest.HttpPostJsonData(url, params.toJSONString());
            if (result.isSuccess()) {
                JSONObject jsondata = result.getData();
                if (jsondata.getString("code").equals("10000")) {
                    try {
                        JSONObject user_info = jsondata.getJSONObject("user_info");
                        String access_token = jsondata.getString("access_token");
                        String parkurl = String.format("%s/#/login", WebConfig.getParkDomain());
                        String userinfo = URLEncoder.encode(user_info.toJSONString(), "utf-8");
                        parkurl = String.format("%s?access_token=%s&user_info=%s", parkurl, access_token, userinfo);
                        model.addAttribute("url", parkurl);
                        return "StartParkOS";
                    } catch (Exception ex) {
                        model.addAttribute("error", ex.getMessage());
                        return "error";
                    }
                } else {
                    model.addAttribute("error", jsondata.getString("msg"));
                    return "error";
                }
            } else {
                model.addAttribute("error", result.getMsg());
                return "error";
            }
        }
    }



    @RequestMapping(value = "/StartNewParkOS", method = RequestMethod.GET)
    @Permission(authorization = true)
    public String StartNewParkOS(HttpServletRequest request, HttpServletResponse response, Model model) {
        try {
//            JSONObject user_info=jsondata.getJSONObject("user_info");
//            String access_token=jsondata.getString("access_token");
            String appSecret = "c3e0d0d7c348b899ab2301117f317754";
            int parkId = 3;
            int type = 1;

            // 获取当前时间戳（毫秒）
            long timestamp = System.currentTimeMillis();

            // 生成 nonStr
            String nonStr = String.valueOf(timestamp).substring(0, 4) + appSecret.substring(0, 4);

            // 拼接字符串
            String strToSign = "type=" + type + "&timestamp=" + timestamp + "&parkId=" + parkId + "&appSecret=" + appSecret + "&nonStr=" + nonStr;

            // 进行 MD5 加密并转换为大写
            String sign = md5(strToSign).toUpperCase();
            String parkurl = "http://beta.qtck.net/park/#/checkauth?";
            sign=URLEncoder.encode(sign, "utf-8");
            String newtimestamp=URLEncoder.encode(String.valueOf(timestamp), "utf-8");
            String canshu="parkId="+parkId+"&type=1&timestamp="+newtimestamp+"&sign="+sign;
            //canshu= URLEncoder.encode(canshu, "utf-8");
            //parkurl=String.format("%s?access_token=%s&user_info=%s", parkurl,access_token,userinfo);
            model.addAttribute("url", parkurl+canshu);
            return "StartNewParkOS";
        } catch (Exception ex) {
            model.addAttribute("error", ex.getMessage());
            return "error";
        }
    }






    public static String md5(String input) {
        try {
            // 获取 MD5 实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 计算 MD5 哈希值
            byte[] messageDigest = md.digest(input.getBytes());

            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

//    //    @RequestMapping(value = "/StartVisitorOS", method = RequestMethod.GET)
//    @Permission(authorization = true)
//    public String StartVisitorOS(HttpServletRequest request, HttpServletResponse response, Model model) {
//        try {
////            JSONObject user_info=jsondata.getJSONObject("user_info");
////            String access_token=jsondata.getString("access_token");
//            String parkurl = String.format("%s", WebConfig.getThirdParty());
////            String userinfo= URLEncoder.encode(user_info.toJSONString(), "utf-8");
////            parkurl=String.format("%s?access_token=%s&user_info=%s", parkurl,access_token,userinfo);
//            model.addAttribute("url", parkurl);
//            return "StartThirdPartyOS";
//        } catch (Exception ex) {
//            model.addAttribute("error", ex.getMessage());
//            return "error";
//        }
//    }
//
//
//    /**
//     * 视频流进行人证比对
//     *
//     * @param request
//     * @param response
//     * @param uid
//     * @return
//     */
//    @RequestMapping(value = "/StartVisitorOS", method = RequestMethod.GET)
//    public String videoDetectFace(HttpServletRequest request, HttpServletResponse response, String uid) {
//        // 1- 从摄像头实时人脸识别，识别成功保存图片到本地
////        getVideoFromCamera();
//
//        // 2- 从本地视频文件中识别人脸
//// getVideoFromFile();
//
//        // 3- 本地图片人脸识别，识别成功并保存人脸图片到本地
////        face();
//
//        // 4- 比对本地2张图的人脸相似度 （越接近1越相似）
////        String basePicPath = "D:\\Documents\\Pictures\\";
////        double compareHist = compare_image(basePicPath + "fc.jpg", basePicPath + "fc_1.jpg");
////        System.out.println(compareHist);
////        if (compareHist > 0.72) {
////            System.out.println("人脸匹配");
////            return Json.getJsonResult(true);
////        } else {
////
////            System.out.println("人脸不匹配");
////            return Json.getJsonResult(false);
////        }
//        return "StartThirdPartyOS";
//    }
//
//    @PostMapping("/videoDetectFace")
//    public ResponseEntity<Map<String, Object>> detectFace(@RequestBody Map<String, String> request) {
//        Map<String, Object> response = new HashMap<>();
//        try {
//            // 获取 Base64 图像数据
//            String imageBase64 = request.get("image");
//            if (imageBase64 == null || imageBase64.isEmpty()) {
//                response.put("success", false);
//                response.put("message", "图像数据为空！");
//                return ResponseEntity.badRequest().body(response);
//            }
//
//            // 解码并处理图像
//            byte[] imageBytes = Base64.getDecoder().decode(imageBase64.split(",")[1]);
//            boolean isFaceDetected = faceRecognitionService.detectAndSaveFace(imageBytes);
//
//            if (isFaceDetected) {
//                response.put("success", true);
////                response.put("image", faceRecognitionService.getProcessedFaceBase64()); // 返回处理后的图片
//            } else {
//                response.put("success", false);
//                response.put("message", "未检测到人脸！");
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            response.put("success", false);
//            response.put("message", "处理失败：" + e.getMessage());
//        }
//        return ResponseEntity.ok(response);
//    }
//    @ResponseBody
//    @PostMapping("/featureExtraction1")
//    public String featureExtraction1(String img,String img1) throws Exception {
//        return faceRecognitionService.detectAndSaveFace(img, img1);
//    }
//    /**
//     * OpenCV-4.1.1 从摄像头实时读取 * @return: void * @date: 2019年8月19日 17:20:13
//     */
//    public static void getVideoFromCamera() {
//
//        // 初始化视频捕捉对象，打开摄像头
//        VideoCapture capture = new VideoCapture(0);
//        Mat video = new Mat();
//
//        if (capture.isOpened()) {
//            while (i < 3) {  // 执行3次人脸识别并保存图像
//                capture.read(video);  // 读取摄像头帧
//                if (!video.empty()) {
//                    Mat faceImage = getFace(video);  // 进行人脸识别处理
////                    HighGui.imshow("实时人脸识别", faceImage);  // 显示图像
//
////                    int index = HighGui.waitKey(1);  // 等待1ms，刷新图像
////                    if (index == 27) {  // 按Esc键退出
////                        break;
////                    }
//                }
//            }
//
//            capture.release();  // 释放摄像头资源
//            HighGui.destroyAllWindows();  // 关闭所有窗口
//        } else {
//            System.out.println("摄像头未开启");
//        }
//    }
//
//    /**
//     * OpenCV-4.1.1 从视频文件中读取 * @return: void * @date: 2019年8月19日 17:20:20
//     */
//    public static void getVideoFromFile() {
//
//        VideoCapture capture = new VideoCapture();
//        capture.open("C:\\Users\\<USER>\\Desktop\\1.avi");//1 读取视频文件的路径
//
//        if (!capture.isOpened()) {
//
//            System.out.println("读取视频文件失败！");
//            return;
//        }
//        Mat video = new Mat();
//        int index = 0;
//        while (capture.isOpened()) {
//
//            capture.read(video);//2 视频文件的视频写入 Mat video 中
//            HighGui.imshow("本地视频识别人脸", getFace(video));//3 显示图像
//            index = HighGui.waitKey(100);//4 获取键盘输入
//            if (index == 27) {
//                //5 如果是 Esc 则退出
//                capture.release();
//                return;
//            }
//        }
//    }
//
//    /**
//     * OpenCV-4.1.1 人脸识别 * @date: 2019年8月19日 17:19:36 * @param image 待处理Mat图片(视频中的某一帧) * @return 处理后的图片
//     */
//    public static Mat getFace(Mat image) {
//        // 进行人脸检测
//        MatOfRect face = new MatOfRect();
//        faceDetector.detectMultiScale(image, face);
//        Rect[] rects = face.toArray();
//
//        System.out.println("匹配到 " + rects.length + " 个人脸");
//        if (rects.length > 0) {
//            for (Rect rect : rects) {
//                // 在图像上绘制矩形框标识人脸
//                Imgproc.rectangle(image, new Point(rect.x, rect.y), new Point(rect.x + rect.width, rect.y + rect.height), new Scalar(0, 255, 0));
//                Imgproc.putText(image, "Human", new Point(rect.x, rect.y), Imgproc.FONT_HERSHEY_SIMPLEX, 1.0, new Scalar(0, 255, 0), 1, Imgproc.LINE_AA, false);
//            }
//
//            // 每检测到3次，保存图像
//            i++;
//            if (i == 3) {
//                i = 0;
//                Imgcodecs.imwrite("D:\\upload\\face_" + i + ".png", image);
//                System.out.println("保存成功，第 " + i + " 次人脸识别图片");
//            }
//        }
//        return image;
//    }
//
//
//    /**
//     * OpenCV-4.1.1 图片人脸识别 * @return: void * @date: 2019年5月7日12:16:55
//     */
//    public static void face() {
//
//        // 1 读取OpenCV自带的人脸识别特征XML文件
//        //OpenCV 图像识别库一般位于 opencv\sources\data 下面
//// CascadeClassifier facebook=new CascadeClassifier("D:\\Sofeware\\opencv\\sources\\data\\haarcascades\\haarcascade_frontalface_alt.xml");
//        // 2 读取测试图片
//        String imgPath = "D:\\Documents\\Pictures\\he.png";
//        Mat image = Imgcodecs.imread(imgPath);
//        if (image.empty()) {
//
//            System.out.println("image 内容不存在！");
//            return;
//        }
//        // 3 特征匹配
//        MatOfRect face = new MatOfRect();
//        faceDetector.detectMultiScale(image, face);
//        // 4 匹配 Rect 矩阵 数组
//        Rect[] rects = face.toArray();
//        System.out.println("匹配到 " + rects.length + " 个人脸");
//        // 5 为每张识别到的人脸画一个圈
//        int i = 1;
//        for (Rect rect : face.toArray()) {
//
//            Imgproc.rectangle(image, new Point(rect.x, rect.y), new Point(rect.x + rect.width, rect.y + rect.height),
//                    new Scalar(0, 255, 0), 3);
//            imageCut(imgPath, "D:\\Documents\\Pictures\\" + i + ".jpg", rect.x, rect.y, rect.width, rect.height);// 进行图片裁剪
//            i++;
//        }
//        // 6 展示图片
//        HighGui.imshow("人脸识别", image);
//        HighGui.waitKey(0);
//    }
//
//    /**
//     * 裁剪人脸 * @param imagePath * @param outFile * @param posX * @param posY * @param width * @param height
//     */
//    public static void imageCut(String imagePath, String outFile, int posX, int posY, int width, int height) {
//
//        // 原始图像
//        Mat image = Imgcodecs.imread(imagePath);
//        // 截取的区域：参数,坐标X,坐标Y,截图宽度,截图长度
//        Rect rect = new Rect(posX, posY, width, height);
//        // 两句效果一样
//        Mat sub = image.submat(rect); // Mat sub = new Mat(image,rect);
//        Mat mat = new Mat();
//        Size size = new Size(width, height);
//        Imgproc.resize(sub, mat, size);// 将人脸进行截图并保存
//        Imgcodecs.imwrite(outFile, mat);
//        System.out.println(String.format("图片裁切成功，裁切后图片文件为： %s", outFile));
//
//    }
//
//    /**
//     * 人脸比对 * @param img_1 * @param img_2 * @return
//     */
//    public static double compare_image(String img_1, String img_2) {
//
//        Mat mat_1 = conv_Mat(img_1);
//        Mat mat_2 = conv_Mat(img_2);
//        Mat hist_1 = new Mat();
//        Mat hist_2 = new Mat();
//
//        //颜色范围
//        MatOfFloat ranges = new MatOfFloat(0f, 256f);
//        //直方图大小， 越大匹配越精确 (越慢)
//        MatOfInt histSize = new MatOfInt(1000);
//
//        Imgproc.calcHist(Arrays.asList(mat_1), new MatOfInt(0), new Mat(), hist_1, histSize, ranges);
//        Imgproc.calcHist(Arrays.asList(mat_2), new MatOfInt(0), new Mat(), hist_2, histSize, ranges);
//
//        // CORREL 相关系数
//        double res = Imgproc.compareHist(hist_1, hist_2, Imgproc.CV_COMP_CORREL);
//        return res;
//    }
//
//    /**
//     * 灰度化人脸 * @param img * @return
//     */
//    public static Mat conv_Mat(String img) {
//
//        Mat image0 = Imgcodecs.imread(img);
//
//        Mat image1 = new Mat();
//        // 灰度化
//        Imgproc.cvtColor(image0, image1, Imgproc.COLOR_BGR2GRAY);
//        // 探测人脸
//        MatOfRect faceDetections = new MatOfRect();
//        faceDetector.detectMultiScale(image1, faceDetections);
//        // rect中人脸图片的范围
//        for (Rect rect : faceDetections.toArray()) {
//
//            Mat face = new Mat(image1, rect);
//            return face;
//        }
//        return null;
//    }
//
//    /**
//     * OpenCV-4.1.1 将摄像头拍摄的视频写入本地 * @return: void * @date: 2019年8月19日 17:20:48
//     */
//    public static void writeVideo() {
//
//        //1 如果要从摄像头获取视频 则要在 VideoCapture 的构造方法写 0
//        VideoCapture capture = new VideoCapture(0);
//        Mat video = new Mat();
//        int index = 0;
//        Size size = new Size(capture.get(Videoio.CAP_PROP_FRAME_WIDTH), capture.get(Videoio.CAP_PROP_FRAME_HEIGHT));
//        VideoWriter writer = new VideoWriter("D:/a.mp4", VideoWriter.fourcc('D', 'I', 'V', 'X'), 15.0, size, true);
//        while (capture.isOpened()) {
//
//            capture.read(video);//2 将摄像头的视频写入 Mat video 中
//            writer.write(video);
//            HighGui.imshow("像头获取视频", video);//3 显示图像
//            index = HighGui.waitKey(100);//4 获取键盘输入
//            if (index == 27) {
//                //5 如果是 Esc 则退出
//                capture.release();
//                writer.release();
//                return;
//            }
//        }
//    }
}
