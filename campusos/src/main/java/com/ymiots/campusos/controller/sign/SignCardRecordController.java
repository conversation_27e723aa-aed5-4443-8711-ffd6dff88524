package com.ymiots.campusos.controller.sign;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import cn.hutool.core.date.DateTime;
import com.ymiots.campusos.entity.ApiResponse;
import com.ymiots.campusos.entity.DeviceData;
import com.ymiots.framework.common.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ymiots.campusos.service.sign.SignCardRecordService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;

@RestController
@RequestMapping("/Sign/SignCardRecord")
public class SignCardRecordController {

	final String menuid = "6926ddb6-654a-4152-9167-664eddcb16df";

	@Autowired
	SignCardRecordService signCardRecordService;

	@RequestMapping(value = "/getSignCardRecordList", method = RequestMethod.POST)
	@Permission(menu = menuid)
	public JsonData getSignCardRecordList(HttpServletRequest request, HttpServletResponse response, String key,
			String starttime, String endtime, int start, int limit) {
		try {
			return signCardRecordService.getSignCardRecordList(request, response, key, starttime, endtime, start,limit);
		} catch (Exception e) {
			return Json.getJsonData(false, e.getMessage());
		}
	}
	@RequestMapping(value = "/getPCWGSignCardRecordList1", method = RequestMethod.POST)
	@Permission(menu = menuid)
	public JsonData getPCWGSignCardRecordList1(HttpServletRequest request, HttpServletResponse response, String areacode) {
		try {
			return signCardRecordService.getPCWGSignCardRecordList1(request, response, areacode);
		} catch (Exception e) {
			return Json.getJsonData(true);
		}
	}
	@RequestMapping(value = "/getPCWGSignCardRecordList2", method = RequestMethod.POST)
	@Permission(menu = menuid)
	public JsonData getPCWGSignCardRecordList2(HttpServletRequest request, HttpServletResponse response, String areacode) {
		try {
			return signCardRecordService.getPCWGSignCardRecordList(request, response, areacode);
		} catch (Exception e) {
			return Json.getJsonData(true);
		}
	}

	@RequestMapping(value = "/ExportSignCardRecordData", method = RequestMethod.POST)
	@Permission(menu = menuid)
	public JsonResult ExportSignCardRecordData(HttpServletRequest request, HttpServletResponse response, String key,
			String starttime, String endtime) {
		try {
			String filepath = signCardRecordService.ExportSignCardRecordData(request, response, key, starttime,endtime);
			return Json.getJsonResult(true, filepath);
		} catch (IOException e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value = "/WGSignCardRecordData", method = RequestMethod.POST)
	@ResponseBody
	public ApiResponse notifyCardRecord(HttpServletRequest request, HttpServletResponse response,
										@RequestBody DeviceData deviceData) {
		try {
			System.out.println(deviceData);
			String s = JSON.toJSONString(deviceData);
			System.out.println(s);
			return signCardRecordService.notifyCardRecord(deviceData);
		} catch (Exception e) {
			// 封装返回值
			ApiResponse.Status status = new ApiResponse.Status(400, "请求失败！");
			ApiResponse.WaringInfo warn = new ApiResponse.WaringInfo("0", "5");
			Log.info(this.getClass(),"==4===========WaringInfo(0, 0)===================");
			ApiResponse.Custom custom = new ApiResponse.Custom(warn);
			return new ApiResponse(status, custom);
		}

	}

	@RequestMapping(value = "/getWGSignCardRecordList", method = RequestMethod.POST)
	@Permission(menu = menuid)
	public JsonData getWGSignCardRecordList(HttpServletRequest request, HttpServletResponse response, String key,
										  String starttime, String endtime, int start, int limit) {
		try {
			return signCardRecordService.getWGSignCardRecordList(request, response, key, starttime, endtime, start,limit);
		} catch (Exception e) {
			return Json.getJsonData(false, e.getMessage());
		}
	}

	@RequestMapping(value = "/getPCWGSignCardRecordList", method = RequestMethod.POST)
	public JsonData getPCWGSignCardRecordList(HttpServletRequest request, HttpServletResponse response, String machineids) {
		try {
			String starttime = DateTime.now().toString("yyyy-MM-dd") + " 00:00:00";
			String endtime = DateTime.now().toString("yyyy-MM-dd") + " 23:59:59";
			return signCardRecordService.getWGDateViewSignCardRecordList(request, response, machineids, starttime, endtime, 0,1);
		} catch (Exception e) {
			return Json.getJsonData(false, e.getMessage());
		}
	}
}
