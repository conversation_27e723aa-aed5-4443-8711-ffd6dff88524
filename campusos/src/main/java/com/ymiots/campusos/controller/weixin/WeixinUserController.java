package com.ymiots.campusos.controller.weixin;

import com.ymiots.campusos.service.weixin.WeixinUserService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/Weixin/WeixinUser")
public class WeixinUserController {

    final String menuid = "8a42c27e-cc37-11e8-a11c-9c5c8e6dafdd";

    @Autowired
    private WeixinUserService weixinUserService;

    @RequestMapping(value = "/getWeixinUserList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getWeixinUserList(HttpServletRequest request, HttpServletResponse response, String usertype, String key, int start, int limit) {
        try {
            return weixinUserService.getWeixinUserList(request, response, key, usertype, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonResult export(HttpServletRequest request, HttpServletResponse response, String usertype, String key) {
        try {
            String s = weixinUserService.export(request, response, key, usertype);
            return Json.getJsonResult(true, s);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/updateWeixin", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "updateweixin")
    public JsonResult UpdateWeixin(HttpServletRequest request, HttpServletResponse response, String status, String uid) {
        try {
            return weixinUserService.updateWeixinStatus(uid, status);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/deleteWeixinUser", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "del")
    public JsonResult DeleteWeixinUser(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return weixinUserService.DeleteWeixinUser(uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/delWeiXinUserFromCP", method = RequestMethod.POST)
    public JsonResult delWeiXinUserFromCP(HttpServletRequest request, HttpServletResponse response, String uid, String nodeid, String appid, String timestamp, String signature) {
        try {
            return weixinUserService.delWeiXinUserFromCP(request, response, uid, nodeid, appid, timestamp, signature);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/updateWeixinStatusFromCP", method = RequestMethod.POST)
    public JsonResult updateWeixinStatusFromCP(HttpServletRequest request, HttpServletResponse response,String uid,String status,String nodeid,String appid,String timestamp,String signature){
        try {
            return weixinUserService.updateWeixinStatusFromCP(request, response, uid, status, nodeid, appid, timestamp, signature);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }
}
