package com.ymiots.campusos.controller.alleyway;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.service.alleyway.KreGroupService;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022-01-27 10:29
 * @since 2022-01-27 10:29
 */
@RestController
@RequestMapping("/alleyway/KreGroupCfg")
public class KreGroupCfgController {

    final String menuid = "c0a2575d-aee5-4e9d-9c18-b3fd60937eb0";

    @Autowired
    KreGroupService kreGroupService;

    @RequestMapping(value = "/getGroup", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getGroup(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        try {
            return kreGroupService.getGroup(request, response, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/saveGroup", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "addgroup,editgroup")
    public JsonResult saveGroup(HttpServletRequest request, HttpServletResponse response, String uid, String name) {
        try {
            return kreGroupService.saveGroup(request, response, uid, name);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getNEWGroup", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getNEWGroup(HttpServletRequest request, HttpServletResponse response, String key,String areacode, int start, int limit) {
        try {
            return kreGroupService.getNEWGroup(request, response, key,areacode, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/saveNEWGroup", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "addgroup,editgroup")
    public JsonResult saveNEWGroup(HttpServletRequest request, HttpServletResponse response, String uid, String name,String areacode) {
        try {
            return kreGroupService.saveNEWGroup(request, response, uid, name,areacode);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/delGroup", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "delgroup")
    public JsonResult delGroup(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return kreGroupService.delGroup(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getGroupCfg", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getGroupCfg(HttpServletRequest request, HttpServletResponse response, String groupid, String key, int start, int limit) {
        try {
            return kreGroupService.getGroupCfg(request, response, groupid, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getDoorList",method = RequestMethod.POST)
    public JsonData getDoorList(HttpServletRequest request, String key, String areacode, String groupid, boolean viewchild, int start, int limit) {
        try {
            return kreGroupService.getDoorList(request, key, areacode, groupid, viewchild, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/saveGroupCfg", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "adddoor,editdoor")
    public JsonResult saveGroupCfg(HttpServletRequest request, HttpServletResponse response, String uid, String groupid,
                                   String devdata, String weekplanid, String limits, String endtime, String effectivetimes) {
        try {
            return kreGroupService.saveGroupCfg(request, response, uid, groupid, devdata, weekplanid, limits, endtime, effectivetimes);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/queryweek",method = RequestMethod.GET)
    public JsonData queryweek(HttpServletRequest request, String devdata,String uid){
        return kreGroupService.queryweek(request, devdata,uid);
    }

    @RequestMapping(value = "/delGroupCfg", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "deldoor")
    public JsonResult delGroupCfg(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return kreGroupService.delGroupCfg(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }
}
