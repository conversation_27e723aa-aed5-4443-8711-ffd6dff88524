package com.ymiots.campusos.controller.card;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.service.card.TeachStudInfoService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/Card/TeachStudInfo")
public class TeachStudInfoController {

	 /**
	  *  教职工
	  */
	 final String teachmenuid="76b735fe-07be-11e8-9075-9c5c8e6dafdd";
	 /**
	  *  学生
	  */
	 final String studentmenuid="552a932d-07be-11e8-9075-9c5c8e6dafdd";

	 @Autowired
	 TeachStudInfoService infoservice;

	@RequestMapping(value="/getTeacherList",method = RequestMethod.POST)
	@Permission(menu=teachmenuid)
	public JsonData getTeacherList(HttpServletRequest request, HttpServletResponse response,String orgcode,String key,boolean viewchild, String viewleave, String infolabel, String property, int start,int limit){
		try {
			return infoservice.getTeacherList(request, response,orgcode,key,viewchild, viewleave, infolabel, property, start,limit);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/getStudentList",method = RequestMethod.POST)
	@Permission(menu=studentmenuid)
	public JsonData getStudentList(HttpServletRequest request, HttpServletResponse response,String orgcode, String key,
								   boolean viewchild,String viewleave, String infolabel, String property, int start,int limit){
		try {
			return infoservice.getStudentList(request, response, orgcode, key, viewchild, viewleave, infolabel, property, start, limit);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/getPerfectStudentList",method = RequestMethod.POST)
	@Permission(menu=studentmenuid)
	public JsonData getPerfectStudentList(HttpServletRequest request, HttpServletResponse response,String orgcode, String key,
								   boolean viewchild,String viewleave, String infolabel, String property, int start,int limit){
		try {
			return infoservice.getPerfectStudentList(request, response, orgcode, key, viewchild, viewleave, infolabel, property, start, limit);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/getReplenishStudentList",method = RequestMethod.POST)
	@Permission(menu=studentmenuid)
	public JsonData getReplenishStudentList(HttpServletRequest request, HttpServletResponse response, String key,String orgcode, int start,int limit){
		try {
			return infoservice.getReplenishStudentList(request, response, key,orgcode, start, limit);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/getParentsList",method = RequestMethod.POST)
	@Permission(menu=studentmenuid)
	public JsonData getParentsList(HttpServletRequest request, HttpServletResponse response,String orgcode, String key,
								   boolean viewchild,String viewleave, String infolabel, String property, int start,int limit){
		try {
			return infoservice.getParentsList(request, response, orgcode, key, viewchild, viewleave, infolabel, property, start, limit);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

    @RequestMapping(value = "/getInfoLabel", method = RequestMethod.POST)
    public JSONArray getInfoLabel(HttpServletRequest request, HttpServletResponse response, String infotype){
        try {
            return infoservice.getInfoLabel(request, response,infotype);
        } catch (Exception e) {
            return new JSONArray();
        }
    }


	@RequestMapping(value="/SaveTeacher",method = RequestMethod.POST)
	@Permission(menu=teachmenuid,opcode="add,edit")
	public JsonResult SaveTeacher(HttpServletRequest request, HttpServletResponse response,String uid,String code,String name,String sex,String card,String position,String posrank,
			String mobile,String orgcode,String nation, String birthday, String address,String idcard,String email, String linkman1,String linkmobile1,String linkdes1,
								  String linkman2,String linkmobile2,String linkdes2,String startdate,String enddate,String plateno,String yunfei,String volume,
								  String passpassword, String infolabel ,String property,String groupname,String userId){
		try {
			return infoservice.SaveTeacher(request, response, uid, code, name, sex, card, position, posrank, mobile, orgcode, nation, birthday, address, idcard, email, linkman1, linkmobile1, linkdes1, linkman2, linkmobile2, linkdes2, startdate, enddate, plateno, yunfei, volume, passpassword, infolabel, property, groupname, userId);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/addParentInfo",method = RequestMethod.POST)
	@Permission(menu=teachmenuid,opcode="add,edit")
	public JsonResult saveDateView(HttpServletRequest request, HttpServletResponse response,String name,String pid,String linkman,String linkmobile,String linkdes) {
		try {
			return infoservice.addParentInfo(request, response, name, pid, linkman, linkmobile, linkdes);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}

	@RequestMapping(value="/editParentInfo",method = RequestMethod.POST)
	@Permission(menu=teachmenuid,opcode="add,edit")
	public JsonResult editParentInfo(HttpServletRequest request, HttpServletResponse response,String pname,String pid,String mobile,String linkdes1) {
		try {
			return infoservice.editParentInfo(request, response, pname, pid, mobile, linkdes1);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}


	@RequestMapping(value="/SaveStudent",method = RequestMethod.POST)
	@Permission(menu=studentmenuid,opcode="add,edit")
	public JsonResult SaveStudent(HttpServletRequest request, HttpServletResponse response,String uid,String code,String name,String sex,String card,
			String mobile,String orgcode,String nation, String birthday, String address, String idcard, String intoyear,String linkman1,String linkmobile1,String linkdes1,String linkman2,String linkmobile2,String linkdes2,String startdate,String enddate,String infolabel,String property,String groupname,String userId){
		try {
			return infoservice.SaveStudent(request, response, uid, code, name, sex, card, mobile, orgcode, nation, birthday, address, idcard, intoyear, linkman1, linkmobile1, linkdes1, linkman2, linkmobile2, linkdes2, startdate, enddate, infolabel, property, groupname, userId);
		} catch (Exception e) {
			e.printStackTrace();
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/MoveTeacherOrg",method = RequestMethod.POST)
	@Permission(menu=teachmenuid,opcode="edit")
	public JsonResult MoveTeacherOrg(HttpServletRequest request, String orgcode, String infoid, String fromOrgs){
		try {
			return infoservice.MoveTeachStudInfo(request, orgcode, infoid, fromOrgs,"教职工信息");
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/MoveStudentCls",method = RequestMethod.POST)
	@Permission(menu=studentmenuid,opcode="edit")
	public JsonResult MoveStudentCls(HttpServletRequest request, String orgcode, String infoid, String fromOrgs) {
		try {
			return infoservice.MoveTeachStudInfo(request, orgcode, infoid, fromOrgs, "学生信息");
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/DelTeacher",method = RequestMethod.POST)
	@Permission(menu=teachmenuid,opcode="del")
	public JsonResult DelTeacher(HttpServletRequest request, HttpServletResponse response, String infoids){
		try {
			return infoservice.delBatchTeachStudInfo(request, response, infoids);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/DelParent",method = RequestMethod.POST)
	@Permission(menu=studentmenuid,opcode="del")
	public JsonResult DelParent(HttpServletRequest request, HttpServletResponse response, String infoids){
		try {
			return infoservice.DelParent(request, response, infoids);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/DelStudent",method = RequestMethod.POST)
	@Permission(menu=studentmenuid,opcode="del")
	public JsonResult DelStudent(HttpServletRequest request, HttpServletResponse response, String infoids){
		try {
			return infoservice.delBatchTeachStudInfo(request, response, infoids);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/getInfoList",method = RequestMethod.POST)
	@Permission()
	public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response,String key,int start,int limit){
		try {
			return infoservice.getInfoList(request, response,key,start,limit);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/DisabledStudent",method = RequestMethod.POST)
	@Permission(menu=studentmenuid,opcode="disable")
	public JsonResult DisabledStudent(HttpServletRequest request, HttpServletResponse response,String uids){
		try {
			return infoservice.SetInfoStatus(request, response,uids,0);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/AbledStudent",method = RequestMethod.POST)
	@Permission(menu=studentmenuid,opcode="able")
	public JsonResult AbledStudent(HttpServletRequest request, HttpServletResponse response,String uids){
		try {
			return infoservice.SetInfoStatus(request, response,uids,1);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/DisabledTeacher",method = RequestMethod.POST)
	@Permission(menu=teachmenuid,opcode="disable")
	public JsonResult DisabledTeacher(HttpServletRequest request, HttpServletResponse response,String uids){
		try {
			return infoservice.SetInfoStatus(request, response,uids,0);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/AbledTeacher",method = RequestMethod.POST)
	@Permission(menu=teachmenuid,opcode="able")
	public JsonResult AbledTeacher(HttpServletRequest request, HttpServletResponse response,String uids){
		try {
			return infoservice.SetInfoStatus(request, response,uids,1);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/GetStudentIdCardInfo",method = RequestMethod.POST)
	@Permission(menu=studentmenuid,opcode="viewidcard")
	public JsonResult GetStudentIdCardInfo(HttpServletRequest request, HttpServletResponse response,String uid){
		try {
			return infoservice.GetStudentIdCardInfo(request, response,uid);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/UploadStudentFile",method = RequestMethod.POST)
	@Permission(menu=studentmenuid,opcode="import")
	public JsonData UploadStudentFile(HttpServletRequest request, HttpServletResponse response, MultipartRequest filepart){
		try {
			MultipartFile file=  filepart.getFile("excelfile");

			if(file==null){
				return Json.getJsonData(false,"请选择上传招生名单Excel文档");
			}
			if(file.getSize()==0){
				return Json.getJsonData(false,"上传文件为空文档");
			}
			String filename=file.getOriginalFilename();
			String ext=filename.substring(filename.lastIndexOf(".")).toLowerCase();
			if(!ext.equals(".xls") && !ext.equals(".xlsx")){
				return Json.getJsonData(false,"请选择上传Excel文档，扩展名必须为.xls或.xlsx");
			}
			return infoservice.UploadStudentFile(request, response, file);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/UploadImportMoneyFile",method = RequestMethod.POST)
	@Permission(menu=studentmenuid,opcode="import")
	public JsonData UploadImportMoneyFile(HttpServletRequest request, HttpServletResponse response, MultipartRequest filepart){
		try {
			MultipartFile file=  filepart.getFile("excelfile");

			if(file==null){
				return Json.getJsonData(false,"请选择上传充值金额Excel文档");
			}
			if(file.getSize()==0){
				return Json.getJsonData(false,"上传文件为空文档");
			}
			String filename=file.getOriginalFilename();
			String ext=filename.substring(filename.lastIndexOf(".")).toLowerCase();
			if(!ext.equals(".xls") && !ext.equals(".xlsx")){
				return Json.getJsonData(false,"请选择上传Excel文档，扩展名必须为.xls或.xlsx");
			}
			return infoservice.UploadStudentFile(request, response, file);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/UploadTeachFile",method = RequestMethod.POST)
	@Permission(menu=teachmenuid,opcode="import")
	public JsonData UploadTeachFile(HttpServletRequest request, HttpServletResponse response, MultipartRequest filepart){
		try {
			MultipartFile file=  filepart.getFile("excelfile");

			if(file==null){
				return Json.getJsonData(false,"请选择上传招生名单Excel文档");
			}
			if(file.getSize()==0){
				return Json.getJsonData(false,"上传文件为空文档");
			}
			String filename=file.getOriginalFilename();
			String ext=filename.substring(filename.lastIndexOf(".")).toLowerCase();
			if(!ext.equals(".xls") && !ext.equals(".xlsx")){
				return Json.getJsonData(false,"请选择上传Excel文档，扩展名必须为.xls或.xlsx");
			}
			return infoservice.UploadStudentFile(request, response, file);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/ImportStudentFile",method = RequestMethod.POST)
	@Permission(menu = studentmenuid, opcode="import")
	public JsonResult ImportStudentFile(HttpServletRequest request, HttpServletResponse response, String excelpath,String columncfg){
		try {
			return infoservice.ImportStudentFile(request, response, excelpath, columncfg);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/ImportMoneyFile",method = RequestMethod.POST)
	@Permission(menu = studentmenuid, opcode="import")
	public JsonResult ImportMoneyFile(HttpServletRequest request, HttpServletResponse response, String excelpath,String columncfg){
		try {
			return infoservice.ImportMoneyFile(request, response, excelpath, columncfg);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/ImportTeachMoneyFile",method = RequestMethod.POST)
	@Permission(menu = studentmenuid, opcode="import")
	public JsonResult ImportTeachMoneyFile(HttpServletRequest request, HttpServletResponse response, String excelpath,String columncfg){
		try {
			return infoservice.ImportTeachMoneyFile(request, response, excelpath, columncfg);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/ImportTeacherFile",method = RequestMethod.POST)
	@Permission(menu = teachmenuid, opcode="import")
	public JsonResult ImportTeacherFile(HttpServletRequest request, HttpServletResponse response, String excelpath,String columncfg){
		try {
			return infoservice.ImportTeacherFile(request, response, excelpath, columncfg);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/UploadLeavelTeachFile",method = RequestMethod.POST)
	@Permission(menu=teachmenuid,opcode="importleave")
	public JsonData UploadLeavelTeachFile(HttpServletRequest request, HttpServletResponse response, MultipartRequest filepart){
		try {
			MultipartFile file=  filepart.getFile("excelfile");

			if(file==null){
				return Json.getJsonData(false,"请选择上传招生名单Excel文档");
			}
			if(file.getSize()==0){
				return Json.getJsonData(false,"上传文件为空文档");
			}
			String filename=file.getOriginalFilename();
			String ext=filename.substring(filename.lastIndexOf(".")).toLowerCase();
			if(!ext.equals(".xls") && !ext.equals(".xlsx")){
				return Json.getJsonData(false,"请选择上传Excel文档，扩展名必须为.xls或.xlsx");
			}
			return infoservice.UploadStudentFile(request, response, file);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}


	@RequestMapping(value="/ImportLeavelTeacherFile",method = RequestMethod.POST)
	@Permission(menu = teachmenuid, opcode="importleave")
	public JsonResult ImportLeavelTeacherFile(HttpServletRequest request, HttpServletResponse response, String excelpath,String columncfg){
		try {
			return infoservice.ImportLeavelTeacherFile(request, response, excelpath, columncfg);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/uploadUpdateExcel",method = RequestMethod.POST)
	@Permission(menu = teachmenuid, opcode="importleave")
	public JsonData uploadUpdateExcel(HttpServletRequest request, HttpServletResponse response, MultipartRequest filepart){
		try {
			MultipartFile file=  filepart.getFile("excelfile");

			if(file==null){
				return Json.getJsonData(false,"请选择上传Excel文档");
			}
			if(file.getSize()==0){
				return Json.getJsonData(false,"上传文件为空文档");
			}
			String filename=file.getOriginalFilename();
			String ext=filename.substring(filename.lastIndexOf(".")).toLowerCase();
			if(!ext.equals(".xls") && !ext.equals(".xlsx")){
				return Json.getJsonData(false,"请选择上传Excel文档，扩展名必须为.xls或.xlsx");
			}
			return infoservice.uploadUpdateFile(file);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/updatePersonInfoByExcel",method = RequestMethod.POST)
	@Permission(menu = teachmenuid, opcode="importleave")
	public JsonResult updatePersonInfoByExcel(HttpServletRequest request, HttpServletResponse response, String excelpath,String columncfg){
		try {
			return infoservice.updatePersonInfoByExcel(excelpath, columncfg);
		} catch (Exception e) {
			log.error(ExceptionUtil.stacktraceToString(e));
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/updateTeamInfoByExcel",method = RequestMethod.POST)
	@Permission(menu = teachmenuid, opcode="importleave")
	public JsonResult updateTeamInfoByExcel(HttpServletRequest request, HttpServletResponse response, String excelpath,String columncfg){
		try {
			return infoservice.updatePersonMobileInfoByExcel(excelpath, columncfg);
		} catch (Exception e) {
			log.error(ExceptionUtil.stacktraceToString(e));
			return Json.getJsonResult(e.getMessage());
		}
	}

	@RequestMapping(value="/importParentsByExcel",method = RequestMethod.POST)
	@Permission(menu = teachmenuid, opcode="importleave")
	public JsonResult importParentsByExcel(HttpServletRequest request, HttpServletResponse response, String excelpath,String columncfg){
		try {
			return infoservice.importParentsByExcel(request,response,excelpath, columncfg);
		} catch (Exception e) {
			log.error(ExceptionUtil.stacktraceToString(e));
			return Json.getJsonResult(e.getMessage());
		}
	}

    @RequestMapping(value = "/ExportTeacher",method = RequestMethod.POST)
    @Permission(menu = teachmenuid, opcode="export")
    public JsonResult ExportTeacher(HttpServletRequest request, HttpServletResponse response, String viewleave,String property,String orgcode,boolean viewchild) throws IOException {
        try {
            String filepath= infoservice.ExportTeacher(request, response, viewleave,property, orgcode, viewchild);
            return Json.getJsonResult(true,filepath);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value = "/ExportStudent",method = RequestMethod.POST)
    @Permission(menu = studentmenuid, opcode="export")
    public JsonResult ExportStudent(HttpServletRequest request, HttpServletResponse response, String orgcode,boolean viewchild) throws IOException {
        try {
            String filepath= infoservice.ExportStudent(request, response, orgcode, viewchild);
            return Json.getJsonResult(true,filepath);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

	@RequestMapping(value = "/ExportTemplate",method = RequestMethod.POST)
	@Permission(menu = studentmenuid, opcode="export")
	public JsonResult ExportTemplate(HttpServletRequest request, HttpServletResponse response, String name) throws IOException {
		try {
			String filepath= infoservice.ExportTemplate(request, response, name);
			return Json.getJsonResult(true,filepath);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}

	@RequestMapping(value = "/ExportTeachTemplate",method = RequestMethod.POST)
	@Permission(menu = studentmenuid, opcode="export")
	public JsonResult ExportTeachTemplate(HttpServletRequest request, HttpServletResponse response, String name) throws IOException {
		try {
			String filepath= infoservice.ExportTemplate(request, response, name);
			return Json.getJsonResult(true,filepath);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}

	@RequestMapping(value = "/ExportPerfectStudent",method = RequestMethod.POST)
	@Permission(menu = studentmenuid, opcode="export")
	public JsonResult ExportPerfectStudent(HttpServletRequest request, HttpServletResponse response, String orgcode,boolean viewchild) throws IOException {
		try {
			String filepath= infoservice.ExportPerfectStudent(request, response, orgcode, viewchild);
			return Json.getJsonResult(true,filepath);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}

	@RequestMapping(value = "/ExportReplenishStudent",method = RequestMethod.POST)
	@Permission(menu = studentmenuid, opcode="export")
	public JsonResult ExportReplenishStudent(HttpServletRequest request, HttpServletResponse response,String orgcode) throws IOException {
		try {
			String filepath= infoservice.ExportReplenishStudent(request, response,orgcode);
			return Json.getJsonResult(true,filepath);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}

    @RequestMapping(value = "/getSInfoLabel",method = RequestMethod.POST)
    @Permission(menu=studentmenuid)
    public JSONArray getSInfoLabel(HttpServletRequest request, HttpServletResponse response){
        try {
            return infoservice.getInfoLabel(request, response,2);
        } catch (Exception e) {
            return new JSONArray();
        }
    }
    /**
     * 获取学生标签信息
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/getTInfoLabel",method = RequestMethod.POST)
    @Permission(menu=teachmenuid)
    public JSONArray getTInfoLabel(HttpServletRequest request, HttpServletResponse response){
        try {
            return infoservice.getInfoLabel(request, response,1);
        } catch (Exception e) {
            return new JSONArray();
        }
    }

    /**
     *  验证CP平台用户档案身份
     * @param request
     * @param response
     * @param usertype
     * @param name
     * @param idcard
     * @param mobile
     * @param nodeid
     * @param appid
     * @param signature
     * @param timestamp
     * @return
     */
    @RequestMapping(value = "/VerificationCPUserDocment",method = RequestMethod.POST)
    public JsonResult VerificationCPuserDocment(HttpServletRequest request, HttpServletResponse response,String idtype,String infocode,
			String usertype, String name, String idcard, String mobile, String nodeid, String appid, String signature,
			String timestamp) {
		try {
			return infoservice.VerificationCPUserDocment(request, response, idtype, infocode, usertype, name, idcard, mobile, nodeid, appid, signature, timestamp);
		} catch (Exception e) {
			return Json.getJsonResult(e.getMessage());
		}
	}

    @RequestMapping(value = "/exportQRCode",method = RequestMethod.POST)
    @Permission(opcode = "exportqrcode")
    public JsonResult exportQRCode(HttpServletRequest request, HttpServletResponse response,String infoid){
        return infoservice.exportQRCode(request, response, infoid);
    }

	@RequestMapping(value = "/ResetPassword",method = RequestMethod.POST)
    @Permission(opcode = "resetPassword")
    public JsonResult ResetPassword(HttpServletRequest request, HttpServletResponse response,String infoid){
        return infoservice.resetPassword(request, response, infoid);
    }

	@RequestMapping(value = "/exportStudentInfo",method = RequestMethod.POST)
	@Permission(opcode = "exportStudentInfo")
	public R<?> exportStudentInfo(String uid) {
		return infoservice.exportStudentInfo(uid);
	}

	@RequestMapping(value="/getTeacherUid",method = RequestMethod.POST)
	@Permission(menu=teachmenuid)
	public JsonData getTeacherUid(HttpServletRequest request, HttpServletResponse response,String orgcode){
		try {
			return infoservice.getTeacherUid(request, response,orgcode);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value = "/getGroupStore", method = RequestMethod.POST)
	@Permission(menu = teachmenuid)
	public JSONArray getGroupStore(HttpServletRequest request, HttpServletResponse response, String infoId) {
		try {
			return infoservice.getGroupStore(request, response, infoId);
		} catch (Exception e) {
			return new JSONArray();
		}
	}
}
