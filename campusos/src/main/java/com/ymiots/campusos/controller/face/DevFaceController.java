package com.ymiots.campusos.controller.face;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.entity.face.FaceFingerDto;
import com.ymiots.campusos.service.face.DevFaceService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/Face/DevFace")
public class DevFaceController {

    final String menuid = "cc7c3618-aa71-11e8-a2f5-9c5c8e6dafdd";

	@Autowired
	DevFaceService devface;

	@RequestMapping(value = "/renameThePhoto", method = RequestMethod.POST)
	public JsonResult renameThePhoto(HttpServletRequest request, HttpServletResponse response){
		try {
			return devface.renameThePhoto(request, response);
		} catch (Exception e) {
			e.printStackTrace();
			return Json.getJsonResult(false,e.getMessage());
		}
	}

    @RequestMapping(value = "/getFaceDeviceList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getFaceDeviceList(HttpServletRequest request, HttpServletResponse response,String serveruid, String key, int start, int limit){
        try {
            return devface.getFaceDeviceList(request, response, serveruid, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }

    @RequestMapping(value = "/getInfoFaceList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getInfoFaceList(HttpServletRequest request, HttpServletResponse response,String devids,String orgcode,boolean viewchild,String timeid,String key, String infodownstatus, int start, int limit){
        try {
            return devface.getInfoFaceList(request, response, devids, orgcode,viewchild, timeid, key, infodownstatus, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }

	@RequestMapping(value="/getInfoList",method = RequestMethod.POST)
	@Permission(menu=menuid)
	public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response,
			String orgcode,String key,String infotype,boolean viewchild, String devids,String property,String infolabel, String selecteduid,String ishasmodel,String ismanwoman, int start,int limit){
		try {
			return devface.getInfoList(request, response, orgcode, key, infotype, viewchild, devids, property,infolabel, selecteduid,ishasmodel,ismanwoman, start,limit);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/SaveAuthorizeFace",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode="add")
	public JsonResult SaveAuthorizeFace(HttpServletRequest request, HttpServletResponse response, String devids,String timeid,String infoids, String allowcard){
		try {
			return devface.SaveAuthorizeFace(request, response,devids, timeid, infoids, allowcard);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}

	@RequestMapping(value="/getTimeSchemeList",method = RequestMethod.POST)
	@Permission(menu=menuid)
	public JsonData getTimeSchemeList(HttpServletRequest request, HttpServletResponse response, String key, int start,int limit){
		try {
			return devface.getTimeSchemeList(request, response, key, start,limit);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/UpdateTimeScheme",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode="edittimezone")
	public JsonResult UpdateTimeScheme(HttpServletRequest request, HttpServletResponse response, String timeid,String uids){
		try {
			return devface.UpdateTimeScheme(request, response,timeid, uids);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}


	@RequestMapping(value="/DelDevface",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode="del")
	public JsonResult DelDevface(HttpServletRequest request, HttpServletResponse response, String uids){
		try {
			return devface.DelDevface(request, response, uids);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}

    @RequestMapping(value="/redown",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="redown")
    public JsonResult redown(HttpServletRequest request, HttpServletResponse response, String infoids,String devids){
        try {
            return devface.redown(request, response, infoids, devids);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value="/changeAllowCard",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="allowcard,disallowcard")
    public JsonResult changeAllowCard(HttpServletRequest request, HttpServletResponse response,String uid,String allowcard){
        try {
            return devface.changeAllowCard(request, response, uid, allowcard);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value = "/getInfoLabel", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JSONArray getInfoLabel(HttpServletRequest request, HttpServletResponse response, String infotype){
        try {
            return devface.getInfoLabel(request, response,infotype);
        } catch (Exception e) {
            return new JSONArray();
        }
    }

	@RequestMapping(value = "/getAuthorizedUsers", method = RequestMethod.POST)
	public R<List<FaceFingerDto>> getAuthorizedUsers(HttpServletRequest request, String devid, String key,Integer page,Integer limit) {
		try {
			return devface.getAuthorizedUsers(request, devid, key, page, limit);
		} catch (Exception e) {
			return R.fail(e.getMessage(), null);
		}
	}

	@RequestMapping(value = "/getFingerDev", method = RequestMethod.POST)
	public JsonData getFingerDev(HttpServletRequest request, HttpServletResponse response,String key, int start,int limit){
		try {
			return devface.getFingerDev(request, response, key, start, limit);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/getFingerCollectionList",method = RequestMethod.POST)
	@Permission(menu=menuid)
	public JsonData getFingerCollectionList(HttpServletRequest request, HttpServletResponse response,String infoid,String status,String key,int start,int limit){
		try {
			return devface.getFingerCollectionList(request, response, infoid, status, key, start, limit);
		} catch (Exception e) {
			return Json.getJsonData(false,e.getMessage());
		}
	}

	@RequestMapping(value="/fingerCollection",method = RequestMethod.POST)
	@Permission(menu=menuid,opcode = "collectfinger")
	public JsonResult fingerCollection(HttpServletRequest request, HttpServletResponse response,String devid,String infoid){
		try {
			return devface.fingerCollection(request, response, devid,infoid);
		} catch (Exception e) {
			return Json.getJsonResult(false,e.getMessage());
		}
	}

}
