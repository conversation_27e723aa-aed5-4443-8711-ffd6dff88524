package com.ymiots.campusos.controller.consume;

import com.ymiots.campusos.service.consume.AnalysisNotWorkConsumeService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/Consume/AnalysisNotWorkConsume")
public class AnalysisNotWorkConsumeController {

    final String menuid = "494f2890-e4fb-49da-87b6-e02a0055f5b2";

    @Autowired
    private AnalysisNotWorkConsumeService service;
    /**
	 * 消费管理--未考勤消费--查询
	 * @param request
	 * @param response
	 * @param viewchild
	 * @param orgcode
	 * @param startdate
	 * @param enddate
	 * @param key
	 * @param start
	 * @param limit
	 * @return JsonData
	 */
    @RequestMapping(value = "/getNotWorkConsumeList",method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getNotWorkConsumeList(HttpServletRequest request, HttpServletResponse response, boolean viewchild, String orgcode, String startdate, String enddate, String key, int start, int limit) {
        try {
            return service.getNotWorkConsumeList(request, response, viewchild, orgcode, startdate, enddate, key, start, limit);
        }catch (Exception e){
            return Json.getJsonData(false,e.getMessage());
        }

    }
    
    @RequestMapping(value="/analysisConsume",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="analysis")
    public JsonResult analysisConsume(HttpServletRequest request, HttpServletResponse response, String startdate, String enddate){
        try {
            return service.analysisConsume(request, response, startdate,enddate);
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }

    @RequestMapping(value="/exportNotWorkConsume",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="export")
    public JsonResult ExportNotWorkConsume(HttpServletRequest request, HttpServletResponse response, boolean viewchild, String orgcode, String startdate, String enddate, String key){
        try {
            String filepath= service.ExportNotWorkConsume(request, response, viewchild, orgcode, startdate, enddate, key);
            return Json.getJsonResult(true,filepath);
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }


}
