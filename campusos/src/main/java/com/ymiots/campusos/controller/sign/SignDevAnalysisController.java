package com.ymiots.campusos.controller.sign;

import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.sign.SignDevAnalysisService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2021/11/11 9:10
 * @Version 1.0
 */
@RestController
@RequestMapping("/sign/signDevAnalysis")
public class SignDevAnalysisController {

    @Autowired
    SignDevAnalysisService analysisService;

    @Autowired
    UserRedis userRedis;

    @RequestMapping(value = "/getSignReport",method = RequestMethod.GET)
    public JsonData getSignReport(String status, String startdate, String enddate, String key, int start, int limit){
        return analysisService.getSignReport(status, startdate, enddate, key, start, limit);
    }


    @RequestMapping(value = "/analysisSignDev",method = RequestMethod.POST)
    public JsonResult analysisSignDev(HttpServletRequest request, String startdate, String enddate){
        String userId = userRedis.getUserId(request);
        analysisService.analysisSignDev(startdate,enddate,userId);
        return Json.getJsonResult(true,"分析成功");
    }

    @RequestMapping(value = "/ExportTimeRecords",method = RequestMethod.POST)
    public JsonResult ExportTimeRecords(String status, String startdate, String enddate, String key){
        try {
            String filepath=analysisService.ExportTimeRecords(status, startdate, enddate, key);
            if(!StringUtils.isBlank(filepath)){
                return Json.getJsonResult(true,filepath);
            }else{
                return Json.getJsonResult(false, "无导出数据");
            }
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }
}
