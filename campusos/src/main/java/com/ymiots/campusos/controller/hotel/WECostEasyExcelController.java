package com.ymiots.campusos.controller.hotel;

import com.ymiots.campusos.service.waterctrl.WECostEasyExcelService;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 水电费账单导出控制器（EasyExcel版本）
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Controller
@RequestMapping("/Hotel/WECost")
public class WECostEasyExcelController {

    @Autowired
    private WECostEasyExcelService exportService;

    /**
     * 导出普通水电费账单（EasyExcel）
     */
    @RequestMapping("/exportExcel")
    public void exportExcel(@RequestParam String startDate,
                           @RequestParam String endDate,
                           @RequestParam(required = false) String areaCode,
                           HttpServletResponse response) {
        try {
            Log.info(WECostEasyExcelController.class, 
                String.format("开始导出Excel账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            exportService.exportToExcel(startDate, endDate, areaCode, response);
            
            Log.info(WECostEasyExcelController.class, "Excel账单导出成功");

        } catch (Exception e) {
            Log.error(WECostEasyExcelController.class, 
                String.format("导出Excel账单失败: %s", e.getMessage()), e);
            
            try {
                response.setContentType("text/plain; charset=UTF-8");
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                Log.error(WECostEasyExcelController.class, "写入错误响应失败", ioException);
            }
        }
    }

    /**
     * 导出分时电表详细账单（EasyExcel）
     */
    @RequestMapping("/exportTimeSharingExcel")
    public void exportTimeSharingExcel(@RequestParam String startDate,
                                      @RequestParam String endDate,
                                      @RequestParam(required = false) String areaCode,
                                      HttpServletResponse response) {
        try {
            Log.info(WECostEasyExcelController.class, 
                String.format("开始导出分时电表账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            exportService.exportTimeSharingExcel(startDate, endDate, areaCode, response);
            
            Log.info(WECostEasyExcelController.class, "分时电表账单导出成功");

        } catch (Exception e) {
            Log.error(WECostEasyExcelController.class, 
                String.format("导出分时电表账单失败: %s", e.getMessage()), e);
            
            try {
                response.setContentType("text/plain; charset=UTF-8");
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                Log.error(WECostEasyExcelController.class, "写入错误响应失败", ioException);
            }
        }
    }

    /**
     * 生成打印内容
     */
    @RequestMapping("/generatePrintContent")
    @ResponseBody
    public JsonResult generatePrintContent(@RequestParam String startDate,
                                         @RequestParam String endDate,
                                         @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostEasyExcelController.class, 
                String.format("生成打印内容 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);
            
            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("生成成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostEasyExcelController.class, 
                String.format("生成打印内容失败: %s", e.getMessage()), e);
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("生成失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 检查导出状态
     */
    @RequestMapping("/checkExportStatus")
    @ResponseBody
    public JsonResult checkExportStatus(@RequestParam String startDate,
                                       @RequestParam String endDate,
                                       @RequestParam(required = false) String areaCode) {
        try {
            // 检查是否有数据
            net.sf.json.JSONArray records = exportService.getCostRecords(startDate, endDate, areaCode);
            
            JSONObject data = new JSONObject();
            data.put("hasData", records.size() > 0);
            data.put("recordCount", records.size());
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            
            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("检查完成");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostEasyExcelController.class, 
                String.format("检查导出状态失败: %s", e.getMessage()), e);
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("检查失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 预览账单
     */
    @RequestMapping("/previewBill")
    @ResponseBody
    public JsonResult previewBill(@RequestParam String startDate,
                                 @RequestParam String endDate,
                                 @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostEasyExcelController.class, 
                String.format("预览账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            // 生成HTML内容用于预览
            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);
            
            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));
            
            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("预览成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostEasyExcelController.class, 
                String.format("预览账单失败: %s", e.getMessage()), e);
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("预览失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取导出模板（用于前端显示可导出的字段）
     */
    @RequestMapping("/getExportTemplate")
    @ResponseBody
    public JsonResult getExportTemplate() {
        try {
            JSONObject data = new JSONObject();
            
            // 普通账单字段
            JSONObject normalFields = new JSONObject();
            normalFields.put("equipmentCode", "设备编号");
            normalFields.put("equipmentType", "设备类型");
            normalFields.put("areaCode", "区域编码");
            normalFields.put("readingDate", "读数日期");
            normalFields.put("currentReading", "当前读数");
            normalFields.put("previousReading", "上次读数");
            normalFields.put("usageAmount", "用量");
            normalFields.put("unitPrice", "单价");
            normalFields.put("totalCost", "费用");
            normalFields.put("currentBalance", "余额");
            normalFields.put("timeSharingInfo", "分时信息");
            
            // 分时电表详细字段
            JSONObject timeSharingFields = new JSONObject();
            timeSharingFields.put("equipmentCode", "设备编号");
            timeSharingFields.put("areaCode", "区域编码");
            timeSharingFields.put("readingDate", "读数日期");
            timeSharingFields.put("tipReading", "尖时读数");
            timeSharingFields.put("tipUsage", "尖时用量");
            timeSharingFields.put("tipPrice", "尖时单价");
            timeSharingFields.put("tipCost", "尖时费用");
            timeSharingFields.put("peakReading", "峰时读数");
            timeSharingFields.put("peakUsage", "峰时用量");
            timeSharingFields.put("peakPrice", "峰时单价");
            timeSharingFields.put("peakCost", "峰时费用");
            timeSharingFields.put("flatReading", "平时读数");
            timeSharingFields.put("flatUsage", "平时用量");
            timeSharingFields.put("flatPrice", "平时单价");
            timeSharingFields.put("flatCost", "平时费用");
            timeSharingFields.put("valleyReading", "谷时读数");
            timeSharingFields.put("valleyUsage", "谷时用量");
            timeSharingFields.put("valleyPrice", "谷时单价");
            timeSharingFields.put("valleyCost", "谷时费用");
            timeSharingFields.put("totalCost", "总费用");
            timeSharingFields.put("currentBalance", "余额");
            
            data.put("normalFields", normalFields);
            data.put("timeSharingFields", timeSharingFields);
            
            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("获取成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostEasyExcelController.class, 
                String.format("获取导出模板失败: %s", e.getMessage()), e);
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("获取失败: " + e.getMessage());
            return result;
        }
    }
}
