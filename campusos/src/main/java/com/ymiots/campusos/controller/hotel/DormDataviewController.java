package com.ymiots.campusos.controller.hotel;


import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dto.OrgframeworkDto;
import com.ymiots.campusos.dto.SignStudentDtos;
import com.ymiots.campusos.service.hotel.DormDataviewService;
import com.ymiots.campusos.vo.hotel.BuildingVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/Dorm/dataview")
public class DormDataviewController {
    @Autowired
    DormDataviewService dormDataview;

    @RequestMapping(value="/getDormTotal",method = RequestMethod.POST)
    public HashMap<String, String> getDormTotal(HttpServletRequest request, HttpServletResponse response, String id){
        try {
            return dormDataview.getDormTotal(request, response,id);
        } catch (Exception e) {
            return new HashMap<String, String>();
        }
    }
    //嘉禾中学的大屏
    @RequestMapping(value="/getDormTotal2",method = RequestMethod.POST)
    public HashMap<String, String> getDormTotal2(HttpServletRequest request, HttpServletResponse response, String id){
        try {
            return dormDataview.getDormTotal2(request, response,id);
        } catch (Exception e) {
            return new HashMap<String, String>();
        }
    }

    //区域进出大屏
    @RequestMapping(value="/getQuYuTotal",method = RequestMethod.POST)
    public HashMap<String, Integer> getQuYuTotal(HttpServletRequest request, HttpServletResponse response, String id){
        try {
            return dormDataview.getQuYuTotal(request, response);
        } catch (Exception e) {
            return new HashMap<String, Integer>();
        }
    }

    @RequestMapping(value="/getDormTotal3",method = RequestMethod.POST)
    public HashMap<String, String> getDormTotal3(HttpServletRequest request, HttpServletResponse response, String id){
        try {
            return dormDataview.getDormTotal3(request, response,id);
        } catch (Exception e) {
            return new HashMap<String, String>();
        }
    }

    @RequestMapping(value="/getDormBuilding",method = RequestMethod.POST)
    public HashMap<String, List<BuildingVo>> getDormBuilding(HttpServletRequest request, HttpServletResponse response, String id){
        try {
            return dormDataview.getDormBuilding(request, response,id);
        } catch (Exception e) {
            return new HashMap<String, List<BuildingVo>>();
        }
    }
    @RequestMapping(value="/getPhoto",method = RequestMethod.POST)
    public List<Map<String,String>> getPhoto(HttpServletRequest request, HttpServletResponse response, String id){
        try {
            return dormDataview.getPhoto(request, response,id);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
    @RequestMapping(value="/getdromlist",method = RequestMethod.POST)
    public List<Map<String,String>> getdromlist(HttpServletRequest request, HttpServletResponse response){
        try {
            return dormDataview.getdromlist(request, response);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @RequestMapping(value="/getClasslist",method = RequestMethod.GET)
    public List<OrgframeworkDto> getClasslist(HttpServletRequest request, HttpServletResponse response){
        try {
            return dormDataview.getClasslist(request, response);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @RequestMapping(value="/getClassStudentlist",method = RequestMethod.GET)
    public R<?> getClassStudentlist(HttpServletRequest request, HttpServletResponse response, String orgcode){
        try {
            SignStudentDtos classStudentlist = dormDataview.getClassStudentlist(request, response, orgcode);
            return R.ok(classStudentlist);
        } catch (Exception e) {
            return R.fail(e);
        }
    }

    @RequestMapping(value="/getAllClassStudentlist",method = RequestMethod.GET)
    @CrossOrigin(origins = "http://localhost:63342")
    public R<?> getAllClassStudentlist(HttpServletRequest request, HttpServletResponse response){
        try {
            SignStudentDtos classStudentlist = dormDataview.getAllClassStudentlist(request, response);
            return R.ok(classStudentlist);
        } catch (Exception e) {
            return R.fail(e);
        }
    }

    @RequestMapping(value="/getmachinelist",method = RequestMethod.POST)
    public List<Map<String,String>> getmachinelist(HttpServletRequest request, HttpServletResponse response,String areacode){
        try {
            return dormDataview.getmachinelist(request, response,areacode);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @RequestMapping(value="/getarealist",method = RequestMethod.POST)
    public List<Map<String,String>> getarealist(HttpServletRequest request, HttpServletResponse response){
        try {
            return dormDataview.getarealist(request, response);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
    @RequestMapping(value="/getsignarealist",method = RequestMethod.POST)
    public List<Map<String,String>> getsignarealist(HttpServletRequest request, HttpServletResponse response){
        try {
            return dormDataview.getsignarealist(request, response);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
    //维瀚区域 接口
    @RequestMapping(value="/getareaweihanlist",method = RequestMethod.POST)
    public List<Map<String,String>> getareaweihanlist(HttpServletRequest request, HttpServletResponse response){
        try {
            return dormDataview.getareaweihanlist(request, response);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    //最近30条通行记录
    @RequestMapping(value="/getTrafficRecord",method = RequestMethod.POST)
    public List<JSONObject> getTrafficRecord(HttpServletRequest request, HttpServletResponse response,String sex,String machineid){
        try {
            return dormDataview.getTrafficRecord(request, response,sex,machineid);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    //最近30条通行记录
    @RequestMapping(value="/getTrafficAreaRecord",method = RequestMethod.GET)
    public List<JSONObject> getTrafficAreaRecord(HttpServletRequest request, HttpServletResponse response, @RequestParam String areacode, @RequestParam String machineids){
        try {
            return dormDataview.getTrafficAreaRecord(request, response,areacode,machineids);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    //最近30条通行记录
    @RequestMapping(value="/getAreaRecord",method = RequestMethod.GET)
    public List<JSONObject> getAreaRecord(HttpServletRequest request, HttpServletResponse response){
        try {
            return dormDataview.getAreaRecord(request, response);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    //当天没有通行记录晚归和未归的
    @RequestMapping(value="/getComeBackLateRecord",method = RequestMethod.POST)
    public List<JSONObject> getComeBackLateRecord(HttpServletRequest request, HttpServletResponse response,String id){
        try {
            return dormDataview.getComeBackLateRecord(request, response,id);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
    //晚归 嘉禾中学 10点前未归的
    @RequestMapping(value="/getLateRecord",method = RequestMethod.POST)
    public List<JSONObject> getLateRecord(HttpServletRequest request, HttpServletResponse response,String id){
        try {
            return dormDataview.getLateRecord(request, response,id);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    //未归 嘉禾中学 10点后还有数据的
    @RequestMapping(value="/getNoHomeRecord",method = RequestMethod.POST)
    public List<JSONObject> getNoHomeRecord(HttpServletRequest request, HttpServletResponse response,String id){
        try {
            return dormDataview.getNoHomeRecord(request, response,id);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    //不在宿舍的 3天的通行数据分析
    @RequestMapping(value="/getNoDormStu",method = RequestMethod.POST)
    public List<JSONObject> getNoDormStu(HttpServletRequest request, HttpServletResponse response,String sex){
        try {
            return dormDataview.getNoDormStu(request, response,sex);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    //区域人数统计
    @RequestMapping(value="/getQuYuRecord",method = RequestMethod.POST)
    public List<JSONObject>  getQuYuRecord(HttpServletRequest request, HttpServletResponse response){
        try {
            return dormDataview.getQuYuRecord(request, response);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

}
