package com.ymiots.campusos.controller.consume;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dto.DailyTransactionStatDto;
import com.ymiots.campusos.dto.DailyTransactionStatDto;
import com.ymiots.campusos.service.consume.TransactionStatService;
import com.ymiots.framework.common.JsonData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Null;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/7/23 13:44
 */
@RestController
@RequestMapping("/TransactionStat")
public class TransactionStatController {

    @Autowired
    TransactionStatService TransactionStatService;


    @PostMapping("/getStatResult")
    public JsonData getStatResult(HttpServletRequest request,String startdate, String enddate, String infoid, String orgcode, boolean viewchild,int start,int limit) {
        List<DailyTransactionStatDto> result = TransactionStatService.getStatResult(request,startdate, enddate,infoid,orgcode,viewchild);
        List<DailyTransactionStatDto> dailyTransactionStatDtos = new ArrayList<>();
        if (result.size()<=start+limit){
            dailyTransactionStatDtos = result.subList(start, result.size());
        }else {
            dailyTransactionStatDtos = result.subList(start, start + limit);
        }
        JSONArray array = new JSONArray();
        for (DailyTransactionStatDto transactionDetailDto : dailyTransactionStatDtos) {
            array.add(transactionDetailDto);
        }
        JsonData data = new JsonData();
        data.setData(array);
        data.setTotal(result.size());
        data.setSuccess(true);
        return data;
    }

    @PostMapping("/ExportData")
    public R<Null> ExportData(HttpServletRequest request,String startdate, String enddate,String infoid,String orgcode,boolean viewchild) {
        return TransactionStatService.ExportData(request,startdate, enddate,infoid,orgcode,viewchild);
    }
}