package com.ymiots.campusos.controller.hotel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.service.waterctrl.WECostExportServiceSimple;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 水电费账单导出控制器（修正版）
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Controller
@RequestMapping("/Hotel/WECost")
public class WECostExportControllerFixed {

    @Autowired
    private WECostExportServiceSimple exportService;

    /**
     * 导出Excel账单（直接下载文件）
     */
    @RequestMapping("/exportExcel")
    public void exportExcel(@RequestParam String startDate,
                           @RequestParam String endDate,
                           @RequestParam(required = false) String areaCode,
                           HttpServletResponse response) {
        try {
            Log.info(WECostExportControllerFixed.class, 
                String.format("开始导出Excel账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            exportService.exportToExcel(startDate, endDate, areaCode, response);
            
            Log.info(WECostExportControllerFixed.class, "Excel账单导出成功");

        } catch (Exception e) {
            Log.error(WECostExportControllerFixed.class, 
                String.format("导出Excel账单失败: %s", e.getMessage()));
            
            try {
                response.setContentType("text/plain; charset=UTF-8");
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                Log.error(WECostExportControllerFixed.class, "写入错误响应失败");
            }
        }
    }

    /**
     * 生成打印内容
     */
    @RequestMapping("/generatePrintContent")
    @ResponseBody
    public JsonResult generatePrintContent(@RequestParam String startDate,
                                         @RequestParam String endDate,
                                         @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostExportControllerFixed.class, 
                String.format("生成打印内容 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);
            
            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("生成成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportControllerFixed.class, 
                String.format("生成打印内容失败: %s", e.getMessage()));
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("生成失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 预览账单
     */
    @RequestMapping("/previewBill")
    @ResponseBody
    public JsonResult previewBill(@RequestParam String startDate,
                                 @RequestParam String endDate,
                                 @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostExportControllerFixed.class, 
                String.format("预览账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            // 直接生成HTML内容用于预览
            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);
            
            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));
            
            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("预览成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportControllerFixed.class, 
                String.format("预览账单失败: %s", e.getMessage()));
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("预览失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 检查导出状态（用于前端轮询检查）
     */
    @RequestMapping("/checkExportStatus")
    @ResponseBody
    public JsonResult checkExportStatus(@RequestParam String startDate,
                                       @RequestParam String endDate,
                                       @RequestParam(required = false) String areaCode) {
        try {
            // 简单检查是否有数据
            JSONArray records = exportService.getCostRecords(startDate, endDate, areaCode);
            
            JSONObject data = new JSONObject();
            data.put("hasData", records.size() > 0);
            data.put("recordCount", records.size());
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            
            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("检查完成");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportControllerFixed.class, 
                String.format("检查导出状态失败: %s", e.getMessage()));
            
            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("检查失败: " + e.getMessage());
            return result;
        }
    }
}
