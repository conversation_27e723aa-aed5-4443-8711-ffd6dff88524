package com.ymiots.campusos.controller.card;

import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.service.card.CardBalanceService;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/CardBalance")
public class CardBalanceController {

    @Autowired
    private CardBalanceService cardBalanceService;

    /**
     *  卡操作
     */
    final String menuid="c7534a47-07be-11e8-9075-9c5c8e6dafdd";

    @PostMapping("/getUserBalance")
    @Permission(menu=menuid)
    public R<?> getUserBalance(HttpServletRequest request,String orgCode,boolean viewChild,String key,int start, int limit) {
        try {
            return cardBalanceService.getUserBalance(request, orgCode,viewChild,key, start, limit);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(e.getMessage());
        }
    }

    @PostMapping("/ExportUserBalance")
    @Permission(menu=menuid)
    public R<?> exportUserBalance(String orgCode,boolean viewChild,String key) {
        try {
            return cardBalanceService.exportUserBalance(orgCode, viewChild, key);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(e.getMessage());
        }
    }
}
