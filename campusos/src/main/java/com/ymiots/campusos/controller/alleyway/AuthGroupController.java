package com.ymiots.campusos.controller.alleyway;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.service.alleyway.AuthGroupService;
import com.ymiots.campusos.service.dev.DeviceService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/AlleyWay/AuthGroup")
public class AuthGroupController {

    final String menuid = "843391f9-a02c-11e8-a7c3-9c5c8e6dafdd";

    @Autowired
    private AuthGroupService authGroupService;

    @Autowired
    private DeviceService devservice;


    /**
     * 获取权限组列表
     * @param authtype
     * @param key
     * @return
     */
    @RequestMapping(value = "/getAuthGroupList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getAuthGroupList(HttpServletRequest request, HttpServletResponse response, String authtype, String key, int start, int limit) {
        try {
            return authGroupService.getAuthGroupList(request, response, authtype, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 获取权限组门禁列表
     * @param key
     * @param groupid
     * @return
     */
    @RequestMapping(value = "/getAuthGroupDoorList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getAuthGroupDoorList(HttpServletRequest request, HttpServletResponse response, String key, String groupid, int start, int limit) {
        try {
            return authGroupService.getAuthGroupDoorList(request, response, key, groupid, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 获取区域列表
     * @param request
     * @param response
     * @param key
     * @return
     */
    @RequestMapping(value="/getAreaTree",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JSONArray getAreaTree(HttpServletRequest request, HttpServletResponse response, String key){
        try {
            return devservice.getAreaTree(request, response,key,true);
        } catch (Exception e) {
            return new JSONArray();
        }
    }

    /**
     * 获取设备列表
     * @param areacode
     * @param key
     * @return
     */
    @RequestMapping(value = "/getDevList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JSONArray getDevList(HttpServletRequest request, HttpServletResponse response, String areacode,
                                String selecteduid, boolean viewchild,String key,String groupid) {
        try {
            return authGroupService.getDevList(request, response, areacode, selecteduid, viewchild, key,groupid);
        } catch (Exception e) {
            return new JSONArray();
        }
    }


    /**
     * 获取已授权的门（读头）
     * @param request
     * @param response
     * @param groupid
     * @return
     */
    @RequestMapping(value = "/getHadAudDoor", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getHadAudDoor(HttpServletRequest request, HttpServletResponse response,String groupid){
        try {
            return authGroupService.getHadAudDoor(request, response, groupid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 添加/修改权限组
     * @param uid
     * @param name
     * @param authvalue
     * @param authtype
     * @param enddate
     * @return
     */
    @RequestMapping(value = "/saveAuthGroup", method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "addrolegroup,editrolegroup")
    public JsonResult saveAuthGroup(HttpServletRequest request, HttpServletResponse response, String uid, String name, String authvalue, String authtype, String enddate) {
        try {
            return authGroupService.saveAuthGroup(request, uid, name, authvalue, authtype, enddate);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 删除权限组
     * @param groupid
     * @return
     */
    @RequestMapping(value = "/delAuthGroup", method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "delrolegroup")
    public JsonResult delAuthGroup(HttpServletRequest request, HttpServletResponse response,String groupid) {
        try {
            return authGroupService.delAuthGroup(request, response, groupid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    /**
     * 添加门禁权限
     * @param doorid
     * @param groupid
     * @return
     */
    @RequestMapping(value = "/setDoor", method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "setdoor")
    public JsonResult setDoor(HttpServletRequest request, HttpServletResponse response,String doorid,String groupid) {
        try {
            if(WebConfig.isSeaoAuthrizeHandleing()) {
                return Json.getJsonResult(false,"授权正在合并，请耐心等待系统通知");
            }
            return authGroupService.setDoor(request, doorid,groupid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }


    @RequestMapping(value = "/delDoor", method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "deldoor")
    public JsonResult delDoor(HttpServletRequest request, HttpServletResponse response,String uid,String groupId){
        try {
            if(WebConfig.isSeaoAuthrizeHandleing()) {
                return Json.getJsonResult(false,"授权正在合并，请耐心等待系统通知");
            }
            return authGroupService.delDoor(request,  uid,groupId);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getDoorList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getDoorList(HttpServletRequest request, HttpServletResponse response,String controllerid){
        try {
            return authGroupService.getDoorList(request, response, controllerid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/editReadhead", method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "editdoor")
    public JsonResult editReadhead(HttpServletRequest request, HttpServletResponse response,String readhead,String controllerid,String uid,String groupid){
        try {
            if(WebConfig.isSeaoAuthrizeHandleing()) {
                return Json.getJsonResult(false,"授权正在合并，请耐心等待系统通知");
            }
            return authGroupService.editReadhead(request, readhead, controllerid, uid, groupid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/copygroup", method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "copygroup")
    public JsonResult copyGroup(HttpServletRequest request, HttpServletResponse response,String groupid,String name){
        try {
            if(WebConfig.isSeaoAuthrizeHandleing()) {
                return Json.getJsonResult(false,"授权正在合并，请耐心等待系统通知");
            }
            return authGroupService.copyGroup(request, response, groupid, name);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }
}
