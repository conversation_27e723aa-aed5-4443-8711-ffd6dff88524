package com.ymiots.campusos.controller.order;

import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.service.order.OrderApplyRecordService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @date 2022-12-21 8:56
 * @since 2022-12-21 8:56
 */
@RestController
@RequestMapping("/order/apply")
public class OrderApplyRecordController {

    final String menuid = "0151bcfc-c8b0-4ffd-89fd-d98602d90ecf";

    @Autowired
    private OrderApplyRecordService orderApplyRecordService;

    @RequestMapping(value = "/getOrderApplyRecordList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getOrderApplyRecordList(HttpServletRequest request, HttpServletResponse response,String starttime,String endtime, String status, String key, int start,int limit){
        try {
            return orderApplyRecordService.getOrderApplyRecordList(request, response, starttime, endtime, status, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getOrderApplyAuditRecordList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getOrderApplyRecord(HttpServletRequest request, HttpServletResponse response,String starttime,String endtime, Integer mouldType,String type, String status, String key, int start,int limit){
        try {
            return orderApplyRecordService.getOrderApplyRecord(request, response, key,status,starttime, endtime,mouldType,type, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/OrderAuditRecord", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData OrderAuditRecord(HttpServletRequest request, HttpServletResponse response,String uid){
        try {
            return orderApplyRecordService.OrderAuditRecord(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/OrderAuditJCanRecord", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData OrderAuditJCanRecord(HttpServletRequest request, HttpServletResponse response,String uid,Integer type,String reason){
        try {
            return orderApplyRecordService.OrderAuditJCanRecord(request, response, uid,type,reason);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/OrderTurnDownRecord", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData OrderTurnDownRecord(HttpServletRequest request, HttpServletResponse response,String uid){
        try {
            return orderApplyRecordService.OrderTurnDownRecord(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     *  导出订餐记录
     */
    @RequestMapping(value = "/exportRecord",method = RequestMethod.POST)
    @Permission(menu = menuid)
    public R<Null> exportRecord(HttpServletRequest request, HttpServletResponse response, String starttime, String endtime, String status, String key, Integer start, Integer limit){
        try {
            return orderApplyRecordService.exportRecord(request, response, starttime, endtime, status,key,start,limit);
        }catch (Exception e){
            e.printStackTrace();
            return R.fail("导出失败");
        }
    }
}
