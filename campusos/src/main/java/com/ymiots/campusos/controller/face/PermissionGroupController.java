package com.ymiots.campusos.controller.face;


import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.service.face.PermissionGroupService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/Face/PermissionGroup")
public class PermissionGroupController {

	final String menuid="60e778f0-1dca-47db-9aa5-14969f993239";

	@Autowired
	PermissionGroupService service;
	/**
	 * 获取授权组
	 * @param request
	 * @param response
	 * @param key
	 * @param start
	 * @param limit
	 * @return
	 */
	@RequestMapping(value = "/getPermissionGroup", method = RequestMethod.POST)
	@Permission(menu = menuid)
	public JsonData getPermissionGroup(HttpServletRequest request, HttpServletResponse response,String key,int start,int limit){
        try {
            return service.getPermissionGroup(request, response,key,start,limit);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }

	/**
	 * 获取tb_face_authorize_group_access数据
	 * @param request
	 * @param response
	 * @param downstatus
	 * @param key
	 * @param start
	 * @param limit
	 * @return
	 */
	@RequestMapping(value = "/getPermissionPeople", method = RequestMethod.POST)
	@Permission(menu = menuid)
	public JsonData getPermissionPeople(HttpServletRequest request, HttpServletResponse response,String downstatus,String facedownstatus,String infolabel,String property,String groupid,String infotype,String key,int start,int limit){
        try {
        	 return service.getPermissionPeople(request, response,downstatus,facedownstatus,infolabel,property,groupid,infotype,key,start,limit);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
    }

	/**
	 * 删除人员授权
	 * @param request
	 * @param response
	 * @param uids
	 * @return
	 */
	@RequestMapping(value = "/deletePermissionGroup",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode = "del")
    public JsonResult deletePermissionGroup(HttpServletRequest request, HttpServletResponse response,String uids){
        try {
            return service.deletePermissionGroup(request, response, uids);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

	/**
	 * 新增人员授权
	 * @param request
	 * @param response
	 * @param infoid
	 * @param groupuid
	 * @return
	 */
	@RequestMapping(value = "/addPermissionGroup", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "add")
    public JsonResult addPermissionGroup(HttpServletRequest request, HttpServletResponse response, String infoid,String groupuid){
        try {
            return service.addPermissionGroup(request, response, infoid, groupuid);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

	/**
	 * 重新下载
	 * @param request
	 * @param response
	 * @param uid
	 * @return
	 */
	@RequestMapping(value = "/redown",method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "redown")
    public JsonResult redown(HttpServletRequest request, HttpServletResponse response, String uids){
        try {
            return service.redown(request, response,uids);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

	/**
	 * 新增授权中间获取人员
	 * @param request
	 * @param response
	 * @param orgcode
	 * @param key
	 * @param infotype
	 * @param viewchild
	 * @param infolabel
	 * @param selecteduid
	 * @param start
	 * @param limit
	 * @return
	 */
	@RequestMapping(value = "/getPeopleList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getPeopleList(HttpServletRequest request, HttpServletResponse response,String groupid, String orgcode, String key, String infotype,String codes,String infolabel, boolean viewchild, String property, String selecteduid,String ishasmodel,String ismanwoman, int start, int limit){
	        try {
	            return service.getPeopleList(request, response, groupid, orgcode, key, infotype,codes, viewchild,property,infolabel, selecteduid, ishasmodel,ismanwoman,start, limit);
	        } catch (Exception e) {
	            return Json.getJsonData(false,e.getMessage());
	        }
	    }

	/**
	 * 获取infotype返回JSONArray数据类型的name字段值
	 * @param request
	 * @param response
	 * @param infotype
	 * @return
	 */
	@RequestMapping(value = "/getInfoLabel", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JSONArray getInfoLabel(HttpServletRequest request, HttpServletResponse response, String infotype){
        try {
            return service.getInfoLabel(request, response,infotype);
        } catch (Exception e) {
            return new JSONArray();
        }
    }
}
