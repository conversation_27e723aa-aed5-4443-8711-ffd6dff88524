package com.ymiots.campusos.controller.hotel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.DBService;
import com.ymiots.campusos.entity.WECost;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.dev.DeviceService;
import com.ymiots.campusos.service.hotel.WECostService;
import com.ymiots.campusos.service.waterctrl.WECostExportService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/Hotel/WECost")
public class WECostController {

    final String menuid = "81ef86b3-12f9-419f-82e8-d9077ca5375c";

    @Autowired
    WECostService weCostService;

    @Autowired
    DeviceService devservice;

    @Autowired
    UserRedis userRedis;

    @Autowired
    WECostExportService exportService;

    @Autowired
    DBService dbService;

    @RequestMapping("/getWECostList")
    @Permission(menu = menuid )
    public JsonData getWECostList(HttpServletRequest request, HttpServletResponse response, String areaCode,boolean viewChild, String daily, int start, int limit){
        return weCostService.getWECostList(request,response,areaCode,viewChild,daily,start,limit);
    }

    @RequestMapping(value="/getAreaTree",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JSONArray getAreaTree(HttpServletRequest request, HttpServletResponse response, String key){
        try {
            return devservice.getAreaTree(request, response,key,true);
        } catch (Exception e) {
            return new JSONArray();
        }
    }

    @RequestMapping("/saveWECost")
    @Permission(menu = menuid )
    public JsonResult saveWECost(HttpServletRequest request, HttpServletResponse response,WECost weCost){
        try {
            String creatorId=userRedis.getUserId(request);
            weCost.setCreatorId(creatorId);
            return weCostService.saveWECost(weCost);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping("/delWECost")
    @Permission(menu = menuid )
    public JsonResult delWECost(HttpServletRequest request, HttpServletResponse response,Integer id){
        return weCostService.delWECost(id);
    }

    @RequestMapping("/export")
    @Permission(menu = menuid )
    public JsonResult export(HttpServletRequest request, HttpServletResponse response, String areaCode,boolean viewChild, String daily){
        try {
            String filePath = weCostService.export(request,response,areaCode,viewChild,daily);
            return Json.getJsonResult(true,filePath);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value="/ImportExcel",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JsonResult ImportExcel(HttpServletRequest request, HttpServletResponse response, MultipartRequest filepart){
        try {
            MultipartFile file=  filepart.getFile("excelfile");

            if(file==null){
                return Json.getJsonResult(false,"请选择Excel文档");
            }
            if(file.getSize()==0){
                return Json.getJsonResult(false,"上传文件为空文档");
            }
            String filename=file.getOriginalFilename();
            String ext=filename.substring(filename.lastIndexOf(".")).toLowerCase();
            if(!ext.equals(".xls") && !ext.equals(".xlsx")){
                return Json.getJsonResult(false,"请选择上传Excel文档，扩展名必须为.xls或.xlsx");
            }
            return weCostService.ImportExcel(request, response, file);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping("/getWEDayCostList")
    @Permission(menu = menuid )
    public JsonData getWEDayCostList(HttpServletRequest request, HttpServletResponse response, String areaCode,boolean viewChild, String starttime,String endtime, int start, int limit){
        return weCostService.getWEDayCostList(request,response,areaCode,viewChild,starttime,endtime,start,limit);
    }

    /**
     * 导出Excel账单（直接下载文件）
     */
    @RequestMapping("/exportExcel")
    @Permission(menu = menuid )
    public JsonResult exportExcel(@RequestParam String startDate,
                            @RequestParam String endDate,
                            @RequestParam(required = false) String areaCode,
                            HttpServletResponse response) {
        try {
            Log.info(WECostController.class,
                    String.format("开始导出Excel账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            String filePath = exportService.exportToExcel(startDate, endDate, areaCode);
            Log.info(WECostController.class, "Excel账单导出成功");
            return Json.getJsonResult(true,filePath);
        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("导出Excel账单失败: %s", e.getMessage()));

            try {
                response.setContentType("text/plain; charset=UTF-8");
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                Log.error(WECostController.class, "写入错误响应失败");
            }
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    /**
     * 生成打印内容
     */
    @RequestMapping("/generatePrintContent")
    @ResponseBody
    public JsonResult generatePrintContent(@RequestParam String startDate,
                                           @RequestParam String endDate,
                                           @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostController.class,
                    String.format("生成打印内容 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);

            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("生成成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("生成打印内容失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("生成失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 预览账单
     */
    @RequestMapping("/previewBill")
    @ResponseBody
    public JsonResult previewBill(@RequestParam String startDate,
                                  @RequestParam String endDate,
                                  @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostController.class,
                    String.format("预览账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            // 直接生成HTML内容用于预览
            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);

            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("预览成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("预览账单失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("预览失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 检查导出状态（用于前端轮询检查）
     */
    @RequestMapping("/checkExportStatus")
    @ResponseBody
    public JsonResult checkExportStatus(@RequestParam String startDate,
                                        @RequestParam String endDate,
                                        @RequestParam(required = false) String areaCode) {
        try {
            // 简单检查是否有数据
            JSONArray records = exportService.getCostRecords(startDate, endDate, areaCode);

            JSONObject data = new JSONObject();
            data.put("hasData", records.size() > 0);
            data.put("recordCount", records.size());
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("检查完成");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("检查导出状态失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("检查失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 保存设备读数记录
     */
    @RequestMapping("/saveEquipmentReading")
    @Permission(menu = menuid)
    public JsonResult saveEquipmentReading(HttpServletRequest request,
                                         @RequestParam(required = false, defaultValue = "0") Integer id,
                                         @RequestParam String equipment_code,
                                         @RequestParam String equipment_type,
                                         @RequestParam String area_code,
                                         @RequestParam String reading_date,
                                         @RequestParam Double current_reading,
                                         @RequestParam(required = false, defaultValue = "0") Double current_balance,
                                         @RequestParam(required = false, defaultValue = "0") Integer is_time_sharing,
                                         @RequestParam(required = false) Double tip_reading,
                                         @RequestParam(required = false) Double peak_reading,
                                         @RequestParam(required = false) Double flat_reading,
                                         @RequestParam(required = false) Double valley_reading,
                                         @RequestParam(required = false) String remark,
                                         @RequestParam(required = false, defaultValue = "1") Integer status) {
        try {
            // 判断是新增还是修改
            boolean isUpdate = (id != null && id > 0);
            String operation = isUpdate ? "修改" : "新增";

            Log.info(WECostController.class,
                String.format("%s设备读数 - ID: %s, 设备: %s, 类型: %s, 区域: %s",
                    operation, id, equipment_code, equipment_type, area_code));

            // 1. 获取上次读数（查询该设备最近一次的读数记录）
            Double previous_reading = getPreviousReading(equipment_code, equipment_type);
            Log.info(WECostController.class, String.format("获取上次读数: %.2f", previous_reading));

            // 2. 获取设备单价（从系统配置表获取）
            Double unit_price = getEquipmentUnitPrice(equipment_type);
            Log.info(WECostController.class, String.format("获取设备单价: %.4f", unit_price));

            // 3. 获取分时电表的上次读数和单价
            Double previous_tip_reading = 0.0, previous_peak_reading = 0.0,
                   previous_flat_reading = 0.0, previous_valley_reading = 0.0;
            Double tip_price = 0.0, peak_price = 0.0, flat_price = 0.0, valley_price = 0.0;

            if (is_time_sharing == 1 && "1".equals(equipment_type)) {
                // 获取分时电表的上次读数
                previous_tip_reading = getPreviousTimeSharingReading(equipment_code, "tip");
                previous_peak_reading = getPreviousTimeSharingReading(equipment_code, "peak");
                previous_flat_reading = getPreviousTimeSharingReading(equipment_code, "flat");
                previous_valley_reading = getPreviousTimeSharingReading(equipment_code, "valley");

                // 获取分时电表的单价
                tip_price = getTimeSharingPrice("tip");
                peak_price = getTimeSharingPrice("peak");
                flat_price = getTimeSharingPrice("flat");
                valley_price = getTimeSharingPrice("valley");

                Log.info(WECostController.class, String.format(
                    "分时电表上次读数 - 尖:%.2f 峰:%.2f 平:%.2f 谷:%.2f",
                    previous_tip_reading, previous_peak_reading, previous_flat_reading, previous_valley_reading));
                Log.info(WECostController.class, String.format(
                    "分时电表单价 - 尖:%.4f 峰:%.4f 平:%.4f 谷:%.4f",
                    tip_price, peak_price, flat_price, valley_price));
            }

            // 构建插入SQL
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO tb_equipment_reading_records (");
            sql.append("uid, equipment_code, equipment_type, area_code, reading_date, reading_time, ");
            sql.append("current_reading, previous_reading, usage_amount, current_balance, unit_price, ");
            sql.append("tip_reading, peak_reading, flat_reading, valley_reading, ");
            sql.append("is_time_sharing, status, create_date, creator_id, remark");
            sql.append(") VALUES (");
            sql.append("'").append(java.util.UUID.randomUUID().toString()).append("', ");
            sql.append("'").append(equipment_code).append("', ");
            sql.append("'").append(equipment_type).append("', ");
            sql.append("'").append(area_code).append("', ");
            sql.append("'").append(reading_date.substring(0, 10)).append("', ");  // reading_date (日期部分)
            sql.append("'").append(reading_date).append("', ");  // reading_time (完整时间)
            sql.append(current_reading).append(", ");
            sql.append(previous_reading).append(", ");

            // 计算用量
            double usage_amount = current_reading - previous_reading;
            sql.append(usage_amount).append(", ");
            sql.append(current_balance).append(", ");
            sql.append(unit_price).append(", ");

            // 初始化费用变量
            double totalCost = 0.0;
            double tipCost = 0.0, peakCost = 0.0, flatCost = 0.0, valleyCost = 0.0;

            // 分时电表数据
            if (is_time_sharing == 1 && "1".equals(equipment_type)) {
                Log.info(WECostController.class, "处理分时电表数据");

                // 尖时计算
                double tipReading = tip_reading != null ? tip_reading : 0;
                double previousTipReading = previous_tip_reading != null ? previous_tip_reading : 0;
                double tipPrice = tip_price != null ? tip_price : 0;
                double tipUsage = tipReading - previousTipReading;
                tipCost = tipUsage * tipPrice;

                sql.append(tipReading).append(", ");

                Log.info(WECostController.class, String.format("尖时: 读数%.2f, 上次%.2f, 用量%.2f, 单价%.4f, 费用%.2f",
                    tipReading, previousTipReading, tipUsage, tipPrice, tipCost));

                // 峰时计算
                double peakReading = peak_reading != null ? peak_reading : 0;
                double previousPeakReading = previous_peak_reading != null ? previous_peak_reading : 0;
                double peakPrice = peak_price != null ? peak_price : 0;
                double peakUsage = peakReading - previousPeakReading;
                peakCost = peakUsage * peakPrice;

                sql.append(peakReading).append(", ");

                Log.info(WECostController.class, String.format("峰时: 读数%.2f, 上次%.2f, 用量%.2f, 单价%.4f, 费用%.2f",
                    peakReading, previousPeakReading, peakUsage, peakPrice, peakCost));

                // 平时计算
                double flatReading = flat_reading != null ? flat_reading : 0;
                double previousFlatReading = previous_flat_reading != null ? previous_flat_reading : 0;
                double flatPrice = flat_price != null ? flat_price : 0;
                double flatUsage = flatReading - previousFlatReading;
                flatCost = flatUsage * flatPrice;

                sql.append(flatReading).append(", ");

                Log.info(WECostController.class, String.format("平时: 读数%.2f, 上次%.2f, 用量%.2f, 单价%.4f, 费用%.2f",
                    flatReading, previousFlatReading, flatUsage, flatPrice, flatCost));

                // 谷时计算
                double valleyReading = valley_reading != null ? valley_reading : 0;
                double previousValleyReading = previous_valley_reading != null ? previous_valley_reading : 0;
                double valleyPrice = valley_price != null ? valley_price : 0;
                double valleyUsage = valleyReading - previousValleyReading;
                valleyCost = valleyUsage * valleyPrice;

                sql.append(valleyReading).append(", ");

                Log.info(WECostController.class, String.format("谷时: 读数%.2f, 上次%.2f, 用量%.2f, 单价%.4f, 费用%.2f",
                    valleyReading, previousValleyReading, valleyUsage, valleyPrice, valleyCost));

                // 分时电表总费用 = 各时段费用之和
                totalCost = tipCost + peakCost + flatCost + valleyCost;

                Log.info(WECostController.class, String.format("分时电表总费用: %.2f (尖%.2f + 峰%.2f + 平%.2f + 谷%.2f)",
                    totalCost, tipCost, peakCost, flatCost, valleyCost));

            } else {
                Log.info(WECostController.class, "处理普通设备数据");

                // 非分时电表，分时读数字段设为0
                sql.append("0, 0, 0, 0, ");  // tip, peak, flat, valley

                // 普通设备总费用 = 用量 * 单价
                totalCost = usage_amount * unit_price;

                Log.info(WECostController.class, String.format("普通设备费用计算: 用量%.2f * 单价%.4f = 总费用%.2f",
                    usage_amount, unit_price, totalCost));
            }

            sql.append(is_time_sharing).append(", ");
            sql.append(status).append(", ");
            sql.append("NOW(), ");
            sql.append("'manual_input', ");
            sql.append("'").append(remark != null ? remark : "手动录入").append("'");
            sql.append(")");

            Log.info(WECostController.class, "执行SQL: " + sql.toString());

            // 执行插入读数记录
            int readingResult = dbService.excuteSql(sql.toString());

            if (readingResult > 0) {
                Log.info(WECostController.class, "设备读数保存成功，开始保存到费用计算表");

                // 同时保存到费用计算表
                boolean costSaved = saveToCostCalculationTable(equipment_code, equipment_type, area_code,
                    reading_date, current_reading, previous_reading, usage_amount, unit_price, totalCost,
                    current_balance, is_time_sharing,
                    tip_reading, previous_tip_reading, tip_price, tipCost,
                    peak_reading, previous_peak_reading, peak_price, peakCost,
                    flat_reading, previous_flat_reading, flat_price, flatCost,
                    valley_reading, previous_valley_reading, valley_price, valleyCost,
                    remark, status);

                if (costSaved) {
                    Log.info(WECostController.class, "设备读数和费用计算数据保存成功");

                    JsonResult jsonResult = new JsonResult();
                    jsonResult.setSuccess(true);
                    jsonResult.setMsg("设备读数和费用计算数据保存成功");
                    return jsonResult;
                } else {
                    Log.warn(WECostController.class, "读数保存成功，但费用计算数据保存失败");

                    JsonResult jsonResult = new JsonResult();
                    jsonResult.setSuccess(true);
                    jsonResult.setMsg("读数保存成功，但费用计算数据保存失败，请检查");
                    return jsonResult;
                }
            } else {
                JsonResult jsonResult = new JsonResult();
                jsonResult.setSuccess(false);
                jsonResult.setMsg("设备读数保存失败");
                return jsonResult;
            }

        } catch (Exception e) {
            Log.error(WECostController.class,
                String.format("保存设备读数失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("保存失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 保存到费用计算表
     */
    private boolean saveToCostCalculationTable(String equipment_code, String equipment_type, String area_code,
                                             String reading_date, Double current_reading, Double previous_reading,
                                             double usage_amount, Double unit_price, double totalCost,
                                             Double current_balance, Integer is_time_sharing,
                                             Double tip_reading, Double previous_tip_reading, Double tip_price, double tipCost,
                                             Double peak_reading, Double previous_peak_reading, Double peak_price, double peakCost,
                                             Double flat_reading, Double previous_flat_reading, Double flat_price, double flatCost,
                                             Double valley_reading, Double previous_valley_reading, Double valley_price, double valleyCost,
                                             String remark, Integer status) {
        try {
            Log.info(WECostController.class, "开始保存到费用计算表");

            // 生成UUID作为主键
            String uid = java.util.UUID.randomUUID().toString();
            String readingRecordId = java.util.UUID.randomUUID().toString(); // 关联读数记录ID

            // 提取日期部分
            String calculationDate = reading_date.substring(0, 10); // 取 YYYY-MM-DD 部分

            // 构建费用计算表插入SQL
            StringBuilder costSql = new StringBuilder();
            costSql.append("INSERT INTO tb_equipment_cost_calculation (");
            costSql.append("uid, equipment_code, equipment_type, calculation_date, reading_record_id, ");
            costSql.append("current_reading, previous_reading, usage_amount, unit_price, total_cost, ");
            costSql.append("tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, ");
            costSql.append("peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, ");
            costSql.append("flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, ");
            costSql.append("valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, ");
            costSql.append("is_time_sharing, area_code, status, create_date, creator_id, remark");
            costSql.append(") VALUES (");
            costSql.append("'").append(uid).append("', ");
            costSql.append("'").append(equipment_code).append("', ");
            costSql.append("'").append(equipment_type).append("', ");
            costSql.append("'").append(calculationDate).append("', ");
            costSql.append("'").append(readingRecordId).append("', ");

            // 基础读数信息
            costSql.append(current_reading).append(", ");
            costSql.append(previous_reading).append(", ");
            costSql.append(usage_amount).append(", ");
            costSql.append(unit_price).append(", ");
            costSql.append(totalCost).append(", ");

            // 分时电表数据
            if (is_time_sharing == 1 && "1".equals(equipment_type)) {
                // 尖时
                double tipUsage = (tip_reading != null ? tip_reading : 0) - (previous_tip_reading != null ? previous_tip_reading : 0);
                costSql.append(tip_reading != null ? tip_reading : 0).append(", ");
                costSql.append(previous_tip_reading != null ? previous_tip_reading : 0).append(", ");
                costSql.append(tipUsage).append(", ");
                costSql.append(tip_price != null ? tip_price : 0).append(", ");
                costSql.append(tipCost).append(", ");

                // 峰时
                double peakUsage = (peak_reading != null ? peak_reading : 0) - (previous_peak_reading != null ? previous_peak_reading : 0);
                costSql.append(peak_reading != null ? peak_reading : 0).append(", ");
                costSql.append(previous_peak_reading != null ? previous_peak_reading : 0).append(", ");
                costSql.append(peakUsage).append(", ");
                costSql.append(peak_price != null ? peak_price : 0).append(", ");
                costSql.append(peakCost).append(", ");

                // 平时
                double flatUsage = (flat_reading != null ? flat_reading : 0) - (previous_flat_reading != null ? previous_flat_reading : 0);
                costSql.append(flat_reading != null ? flat_reading : 0).append(", ");
                costSql.append(previous_flat_reading != null ? previous_flat_reading : 0).append(", ");
                costSql.append(flatUsage).append(", ");
                costSql.append(flat_price != null ? flat_price : 0).append(", ");
                costSql.append(flatCost).append(", ");

                // 谷时
                double valleyUsage = (valley_reading != null ? valley_reading : 0) - (previous_valley_reading != null ? previous_valley_reading : 0);
                costSql.append(valley_reading != null ? valley_reading : 0).append(", ");
                costSql.append(previous_valley_reading != null ? previous_valley_reading : 0).append(", ");
                costSql.append(valleyUsage).append(", ");
                costSql.append(valley_price != null ? valley_price : 0).append(", ");
                costSql.append(valleyCost).append(", ");
            } else {
                // 非分时电表，分时字段设为0
                costSql.append("0, 0, 0, 0, 0, ");  // tip
                costSql.append("0, 0, 0, 0, 0, ");  // peak
                costSql.append("0, 0, 0, 0, 0, ");  // flat
                costSql.append("0, 0, 0, 0, 0, ");  // valley
            }

            costSql.append(is_time_sharing).append(", ");
            costSql.append("'").append(area_code).append("', ");
            costSql.append(status).append(", ");
            costSql.append("NOW(), ");
            costSql.append("'manual_input', ");
            costSql.append("'").append(remark != null ? remark : "手动录入").append("'");
            costSql.append(")");

            Log.info(WECostController.class, "费用计算表SQL: " + costSql.toString());

            // 执行费用计算表插入
            int costResult = dbService.excuteSql(costSql.toString());

            if (costResult > 0) {
                Log.info(WECostController.class, "费用计算表保存成功");

                // 同时更新费用汇总表
                updateCostSummaryTable(area_code, calculationDate, equipment_type, usage_amount, totalCost);

                return true;
            } else {
                Log.error(WECostController.class, "费用计算表保存失败");
                return false;
            }

        } catch (Exception e) {
            Log.error(WECostController.class,
                String.format("保存费用计算表失败: %s", e.getMessage()));
            return false;
        }
    }

    /**
     * 更新费用汇总表
     */
    private void updateCostSummaryTable(String area_code, String calculation_date, String equipment_type,
                                       double usage_amount, double total_cost) {
        try {
            Log.info(WECostController.class, "开始更新费用汇总表");

            // 提取年月 (YYYY-MM)
            String summaryMonth = calculation_date.substring(0, 7);

            // 根据设备类型确定字段
            String usageField = "";
            String costField = "";

            switch (equipment_type) {
                case "1": // 电表
                    usageField = "electric_total_usage";
                    costField = "electric_total_cost";
                    break;
                case "2": // 水表
                    usageField = "water_total_usage";
                    costField = "water_total_cost";
                    break;
                case "4": // 热水表
                    usageField = "hot_water_total_usage";
                    costField = "hot_water_total_cost";
                    break;
                case "221": // 气表
                    usageField = "gas_total_usage";
                    costField = "gas_total_cost";
                    break;
                default:
                    Log.warn(WECostController.class, "未知设备类型: " + equipment_type);
                    return;
            }

            // 检查是否已存在汇总记录
            String checkSql = "SELECT COUNT(*) FROM tb_equipment_cost_summary " +
                             "WHERE area_code = '" + area_code + "' AND summary_month = '" + summaryMonth + "'";

            Object countObj = dbService.queryObject(checkSql);
            int existCount = countObj != null ? Integer.parseInt(countObj.toString()) : 0;

            if (existCount > 0) {
                // 更新现有记录
                String updateSql = "UPDATE tb_equipment_cost_summary SET " +
                                  usageField + " = " + usageField + " + " + usage_amount + ", " +
                                  costField + " = " + costField + " + " + total_cost + ", " +
                                  "total_cost = water_total_cost + electric_total_cost + hot_water_total_cost + gas_total_cost, " +
                                  "modify_date = NOW() " +
                                  "WHERE area_code = '" + area_code + "' AND summary_month = '" + summaryMonth + "'";

                dbService.excuteSql(updateSql);
                Log.info(WECostController.class, "费用汇总表更新成功");
            } else {
                // 插入新记录 - 按实际表结构
                String insertSql = "INSERT INTO tb_equipment_cost_summary " +
                                  "(uid, area_code, summary_month, " +
                                  "water_total_usage, water_total_cost, " +
                                  "electric_total_usage, electric_total_cost, " +
                                  "hot_water_total_usage, hot_water_total_cost, " +
                                  "gas_total_usage, gas_total_cost, " +
                                  "total_cost, status, create_date, creator_id, " +
                                  "modify_date, modifier_id, remark, " +
                                  "paid_amount, unpaid_amount, payment_status, due_date) VALUES (" +
                                  "'" + java.util.UUID.randomUUID().toString() + "', " +
                                  "'" + area_code + "', '" + summaryMonth + "', ";

                // 根据设备类型设置对应字段值，其他字段为0
                if ("1".equals(equipment_type)) { // 电表
                    insertSql += "0.0000, 0.00, " + usage_amount + ", " + total_cost + ", 0.0000, 0.00, 0.0000, 0.00, ";
                } else if ("2".equals(equipment_type)) { // 水表
                    insertSql += usage_amount + ", " + total_cost + ", 0.0000, 0.00, 0.0000, 0.00, 0.0000, 0.00, ";
                } else if ("4".equals(equipment_type)) { // 热水表
                    insertSql += "0.0000, 0.00, 0.0000, 0.00, " + usage_amount + ", " + total_cost + ", 0.0000, 0.00, ";
                } else if ("221".equals(equipment_type)) { // 气表
                    insertSql += "0.0000, 0.00, 0.0000, 0.00, 0.0000, 0.00, " + usage_amount + ", " + total_cost + ", ";
                }

                // 添加其他必需字段
                insertSql += total_cost + ", 1, NOW(), 'manual_input', " +
                           "NOW(), 'manual_input', '手动录入', " +
                           "0.00, " + total_cost + ", 0, NULL)";

                dbService.excuteSql(insertSql);
                Log.info(WECostController.class, "费用汇总表插入成功");
            }

        } catch (Exception e) {
            Log.error(WECostController.class,
                String.format("更新费用汇总表失败: %s", e.getMessage()));
        }
    }

    /**
     * 获取设备上次读数
     */
    private Double getPreviousReading(String equipment_code, String equipment_type) {
        try {
            String sql = "SELECT current_reading FROM tb_equipment_reading_records " +
                        "WHERE equipment_code = '" + equipment_code + "' " +
                        "AND equipment_type = '" + equipment_type + "' " +
                        "AND status = 1 " +
                        "ORDER BY reading_date DESC LIMIT 1";

            Object result = dbService.queryObject(sql);
            if (result != null) {
                return Double.parseDouble(result.toString());
            }
            return 0.0;
        } catch (Exception e) {
            Log.error(WECostController.class,
                String.format("获取设备上次读数失败: %s", e.getMessage()));
            return 0.0;
        }
    }

    /**
     * 获取设备单价
     */
    private Double getEquipmentUnitPrice(String equipment_type) {
        try {
            String configKey = "";
            switch (equipment_type) {
                case "1": // 电表
                    configKey = "ele_unit_price";
                    break;
                case "2": // 水表
                    configKey = "water_unit_price";
                    break;
                case "4": // 热水表
                    configKey = "hot_water_unit_price";
                    break;
                case "221": // 气表
                    configKey = "gas_unit_price";
                    break;
                default:
                    return 0.0;
            }

            String sql = "SELECT cvalue FROM tb_sys_config " +
                        "WHERE cname = '" + configKey + "'";

            Object result = dbService.queryObject(sql);
            if (result != null) {
                return Double.parseDouble(result.toString());
            }

            // 如果配置表中没有，返回默认值
            switch (equipment_type) {
                case "1": return 0.6; // 电表默认单价
                case "2": return 3.5; // 水表默认单价
                case "4": return 5.0; // 热水表默认单价
                case "221": return 2.8; // 气表默认单价
                default: return 0.0;
            }
        } catch (Exception e) {
            Log.error(WECostController.class,
                String.format("获取设备单价失败: %s", e.getMessage()));
            return 0.0;
        }
    }

    /**
     * 获取分时电表上次读数
     */
    private Double getPreviousTimeSharingReading(String equipment_code, String timeType) {
        try {
            String fieldName = "";
            switch (timeType) {
                case "tip": fieldName = "tip_reading"; break;
                case "peak": fieldName = "peak_reading"; break;
                case "flat": fieldName = "flat_reading"; break;
                case "valley": fieldName = "valley_reading"; break;
                default: return 0.0;
            }

            String sql = "SELECT " + fieldName + " FROM tb_equipment_reading_records " +
                        "WHERE equipment_code = '" + equipment_code + "' " +
                        "AND equipment_type = '1' AND is_time_sharing = 1 " +
                        "AND status = 1 " +
                        "ORDER BY reading_date DESC LIMIT 1";

            Object result = dbService.queryObject(sql);
            if (result != null) {
                return Double.parseDouble(result.toString());
            }
            return 0.0;
        } catch (Exception e) {
            Log.error(WECostController.class,
                String.format("获取分时电表上次读数失败: %s", e.getMessage()));
            return 0.0;
        }
    }

    /**
     * 获取分时电表单价
     */
    private Double getTimeSharingPrice(String timeType) {
        try {
            String configKey = "";
            switch (timeType) {
                case "tip": configKey = "electric_tip_price"; break;
                case "peak": configKey = "electric_peak_price"; break;
                case "flat": configKey = "electric_flat_price"; break;
                case "valley": configKey = "electric_valley_price"; break;
                default: return 0.0;
            }

            String sql = "SELECT config_value FROM tb_sys_config " +
                        "WHERE config_key = '" + configKey + "' AND status = 1";

            Object result = dbService.queryObject(sql);
            if (result != null) {
                return Double.parseDouble(result.toString());
            }

            // 如果配置表中没有，返回默认值
            switch (timeType) {
                case "tip": return 1.2;   // 尖时默认单价
                case "peak": return 0.8;  // 峰时默认单价
                case "flat": return 0.6;  // 平时默认单价
                case "valley": return 0.3; // 谷时默认单价
                default: return 0.0;
            }
        } catch (Exception e) {
            Log.error(WECostController.class,
                String.format("获取分时电表单价失败: %s", e.getMessage()));
            return 0.0;
        }
    }
}
