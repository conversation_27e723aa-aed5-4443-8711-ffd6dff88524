package com.ymiots.campusos.controller.hotel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.entity.WECost;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.dev.DeviceService;
import com.ymiots.campusos.service.hotel.WECostService;
import com.ymiots.campusos.service.waterctrl.WECostExportService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/Hotel/WECost")
public class WECostController {

    final String menuid = "81ef86b3-12f9-419f-82e8-d9077ca5375c";

    @Autowired
    WECostService weCostService;

    @Autowired
    DeviceService devservice;

    @Autowired
    UserRedis userRedis;

    @Autowired
    WECostExportService exportService;

    @RequestMapping("/getWECostList")
    @Permission(menu = menuid )
    public JsonData getWECostList(HttpServletRequest request, HttpServletResponse response, String areaCode,boolean viewChild, String daily, int start, int limit){
        return weCostService.getWECostList(request,response,areaCode,viewChild,daily,start,limit);
    }

    @RequestMapping(value="/getAreaTree",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JSONArray getAreaTree(HttpServletRequest request, HttpServletResponse response, String key){
        try {
            return devservice.getAreaTree(request, response,key,true);
        } catch (Exception e) {
            return new JSONArray();
        }
    }

    @RequestMapping("/saveWECost")
    @Permission(menu = menuid )
    public JsonResult saveWECost(HttpServletRequest request, HttpServletResponse response,WECost weCost){
        try {
            String creatorId=userRedis.getUserId(request);
            weCost.setCreatorId(creatorId);
            return weCostService.saveWECost(weCost);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping("/delWECost")
    @Permission(menu = menuid )
    public JsonResult delWECost(HttpServletRequest request, HttpServletResponse response,Integer id){
        return weCostService.delWECost(id);
    }

    @RequestMapping("/export")
    @Permission(menu = menuid )
    public JsonResult export(HttpServletRequest request, HttpServletResponse response, String areaCode,boolean viewChild, String daily){
        try {
            String filePath = weCostService.export(request,response,areaCode,viewChild,daily);
            return Json.getJsonResult(true,filePath);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value="/ImportExcel",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JsonResult ImportExcel(HttpServletRequest request, HttpServletResponse response, MultipartRequest filepart){
        try {
            MultipartFile file=  filepart.getFile("excelfile");

            if(file==null){
                return Json.getJsonResult(false,"请选择Excel文档");
            }
            if(file.getSize()==0){
                return Json.getJsonResult(false,"上传文件为空文档");
            }
            String filename=file.getOriginalFilename();
            String ext=filename.substring(filename.lastIndexOf(".")).toLowerCase();
            if(!ext.equals(".xls") && !ext.equals(".xlsx")){
                return Json.getJsonResult(false,"请选择上传Excel文档，扩展名必须为.xls或.xlsx");
            }
            return weCostService.ImportExcel(request, response, file);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping("/getWEDayCostList")
    @Permission(menu = menuid )
    public JsonData getWEDayCostList(HttpServletRequest request, HttpServletResponse response, String areaCode,boolean viewChild, String starttime,String endtime, int start, int limit){
        return weCostService.getWEDayCostList(request,response,areaCode,viewChild,starttime,endtime,start,limit);
    }

    /**
     * 导出Excel账单（直接下载文件）
     */
    @RequestMapping("/exportExcel")
    @Permission(menu = menuid )
    public JsonResult exportExcel(@RequestParam String startDate,
                            @RequestParam String endDate,
                            @RequestParam(required = false) String areaCode,
                            HttpServletResponse response) {
        try {
            Log.info(WECostController.class,
                    String.format("开始导出Excel账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            String filePath = exportService.exportToExcel(startDate, endDate, areaCode, response);
            return Json.getJsonResult(true,filePath);
            Log.info(WECostController.class, "Excel账单导出成功");

        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("导出Excel账单失败: %s", e.getMessage()));

            try {
                response.setContentType("text/plain; charset=UTF-8");
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                Log.error(WECostController.class, "写入错误响应失败");
            }
        }
    }

    /**
     * 生成打印内容
     */
    @RequestMapping("/generatePrintContent")
    @ResponseBody
    public JsonResult generatePrintContent(@RequestParam String startDate,
                                           @RequestParam String endDate,
                                           @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostController.class,
                    String.format("生成打印内容 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);

            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("生成成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("生成打印内容失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("生成失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 预览账单
     */
    @RequestMapping("/previewBill")
    @ResponseBody
    public JsonResult previewBill(@RequestParam String startDate,
                                  @RequestParam String endDate,
                                  @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostController.class,
                    String.format("预览账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            // 直接生成HTML内容用于预览
            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);

            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("预览成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("预览账单失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("预览失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 检查导出状态（用于前端轮询检查）
     */
    @RequestMapping("/checkExportStatus")
    @ResponseBody
    public JsonResult checkExportStatus(@RequestParam String startDate,
                                        @RequestParam String endDate,
                                        @RequestParam(required = false) String areaCode) {
        try {
            // 简单检查是否有数据
            JSONArray records = exportService.getCostRecords(startDate, endDate, areaCode);

            JSONObject data = new JSONObject();
            data.put("hasData", records.size() > 0);
            data.put("recordCount", records.size());
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("检查完成");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("检查导出状态失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("检查失败: " + e.getMessage());
            return result;
        }
    }
}
