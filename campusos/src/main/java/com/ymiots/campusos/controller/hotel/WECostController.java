package com.ymiots.campusos.controller.hotel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.entity.WECost;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.dev.DeviceService;
import com.ymiots.campusos.service.hotel.WECostService;
import com.ymiots.campusos.service.waterctrl.WECostExportService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/Hotel/WECost")
public class WECostController {

    final String menuid = "81ef86b3-12f9-419f-82e8-d9077ca5375c";

    @Autowired
    WECostService weCostService;

    @Autowired
    DeviceService devservice;

    @Autowired
    UserRedis userRedis;

    @Autowired
    WECostExportService exportService;

    @RequestMapping("/getWECostList")
    @Permission(menu = menuid )
    public JsonData getWECostList(HttpServletRequest request, HttpServletResponse response, String areaCode,boolean viewChild, String daily, int start, int limit){
        return weCostService.getWECostList(request,response,areaCode,viewChild,daily,start,limit);
    }

    @RequestMapping(value="/getAreaTree",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JSONArray getAreaTree(HttpServletRequest request, HttpServletResponse response, String key){
        try {
            return devservice.getAreaTree(request, response,key,true);
        } catch (Exception e) {
            return new JSONArray();
        }
    }

    @RequestMapping("/saveWECost")
    @Permission(menu = menuid )
    public JsonResult saveWECost(HttpServletRequest request, HttpServletResponse response,WECost weCost){
        try {
            String creatorId=userRedis.getUserId(request);
            weCost.setCreatorId(creatorId);
            return weCostService.saveWECost(weCost);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping("/delWECost")
    @Permission(menu = menuid )
    public JsonResult delWECost(HttpServletRequest request, HttpServletResponse response,Integer id){
        return weCostService.delWECost(id);
    }

    @RequestMapping("/export")
    @Permission(menu = menuid )
    public JsonResult export(HttpServletRequest request, HttpServletResponse response, String areaCode,boolean viewChild, String daily){
        try {
            String filePath = weCostService.export(request,response,areaCode,viewChild,daily);
            return Json.getJsonResult(true,filePath);
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping(value="/ImportExcel",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JsonResult ImportExcel(HttpServletRequest request, HttpServletResponse response, MultipartRequest filepart){
        try {
            MultipartFile file=  filepart.getFile("excelfile");

            if(file==null){
                return Json.getJsonResult(false,"请选择Excel文档");
            }
            if(file.getSize()==0){
                return Json.getJsonResult(false,"上传文件为空文档");
            }
            String filename=file.getOriginalFilename();
            String ext=filename.substring(filename.lastIndexOf(".")).toLowerCase();
            if(!ext.equals(".xls") && !ext.equals(".xlsx")){
                return Json.getJsonResult(false,"请选择上传Excel文档，扩展名必须为.xls或.xlsx");
            }
            return weCostService.ImportExcel(request, response, file);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    @RequestMapping("/getWEDayCostList")
    @Permission(menu = menuid )
    public JsonData getWEDayCostList(HttpServletRequest request, HttpServletResponse response, String areaCode,boolean viewChild, String starttime,String endtime, int start, int limit){
        return weCostService.getWEDayCostList(request,response,areaCode,viewChild,starttime,endtime,start,limit);
    }

    /**
     * 导出Excel账单（直接下载文件）
     */
    @RequestMapping("/exportExcel")
    @Permission(menu = menuid )
    public JsonResult exportExcel(@RequestParam String startDate,
                            @RequestParam String endDate,
                            @RequestParam(required = false) String areaCode,
                            HttpServletResponse response) {
        try {
            Log.info(WECostController.class,
                    String.format("开始导出Excel账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            String filePath = exportService.exportToExcel(startDate, endDate, areaCode);
            Log.info(WECostController.class, "Excel账单导出成功");
            return Json.getJsonResult(true,filePath);
        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("导出Excel账单失败: %s", e.getMessage()));

            try {
                response.setContentType("text/plain; charset=UTF-8");
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                Log.error(WECostController.class, "写入错误响应失败");
            }
            return Json.getJsonResult(false,e.getMessage());
        }
    }

    /**
     * 生成打印内容
     */
    @RequestMapping("/generatePrintContent")
    @ResponseBody
    public JsonResult generatePrintContent(@RequestParam String startDate,
                                           @RequestParam String endDate,
                                           @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostController.class,
                    String.format("生成打印内容 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);

            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("生成成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("生成打印内容失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("生成失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 预览账单
     */
    @RequestMapping("/previewBill")
    @ResponseBody
    public JsonResult previewBill(@RequestParam String startDate,
                                  @RequestParam String endDate,
                                  @RequestParam(required = false) String areaCode) {
        try {
            Log.info(WECostController.class,
                    String.format("预览账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            // 直接生成HTML内容用于预览
            String htmlContent = exportService.generatePrintContent(startDate, endDate, areaCode);

            // 创建返回数据
            JSONObject data = new JSONObject();
            data.put("htmlContent", htmlContent);
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);
            data.put("generateTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("预览成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("预览账单失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("预览失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 检查导出状态（用于前端轮询检查）
     */
    @RequestMapping("/checkExportStatus")
    @ResponseBody
    public JsonResult checkExportStatus(@RequestParam String startDate,
                                        @RequestParam String endDate,
                                        @RequestParam(required = false) String areaCode) {
        try {
            // 简单检查是否有数据
            JSONArray records = exportService.getCostRecords(startDate, endDate, areaCode);

            JSONObject data = new JSONObject();
            data.put("hasData", records.size() > 0);
            data.put("recordCount", records.size());
            data.put("startDate", startDate);
            data.put("endDate", endDate);
            data.put("areaCode", areaCode);

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("检查完成");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostController.class,
                    String.format("检查导出状态失败: %s", e.getMessage()));

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("检查失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 保存设备读数记录
     */
    @RequestMapping("/saveEquipmentReading")
    @Permission(menu = menuid)
    public JsonResult saveEquipmentReading(HttpServletRequest request,
                                         @RequestParam String equipment_code,
                                         @RequestParam String equipment_type,
                                         @RequestParam String area_code,
                                         @RequestParam String reading_date,
                                         @RequestParam Double current_reading,
                                         @RequestParam(required = false, defaultValue = "0") Double previous_reading,
                                         @RequestParam Double unit_price,
                                         @RequestParam(required = false, defaultValue = "0") Double current_balance,
                                         @RequestParam(required = false, defaultValue = "0") Integer is_time_sharing,
                                         @RequestParam(required = false) Double tip_reading,
                                         @RequestParam(required = false, defaultValue = "0") Double previous_tip_reading,
                                         @RequestParam(required = false) Double tip_price,
                                         @RequestParam(required = false) Double peak_reading,
                                         @RequestParam(required = false, defaultValue = "0") Double previous_peak_reading,
                                         @RequestParam(required = false) Double peak_price,
                                         @RequestParam(required = false) Double flat_reading,
                                         @RequestParam(required = false, defaultValue = "0") Double previous_flat_reading,
                                         @RequestParam(required = false) Double flat_price,
                                         @RequestParam(required = false) Double valley_reading,
                                         @RequestParam(required = false, defaultValue = "0") Double previous_valley_reading,
                                         @RequestParam(required = false) Double valley_price,
                                         @RequestParam(required = false) String remark,
                                         @RequestParam(required = false, defaultValue = "1") Integer status) {
        try {
            Log.info(WECostController.class,
                String.format("保存设备读数 - 设备: %s, 类型: %s, 区域: %s", equipment_code, equipment_type, area_code));

            // 构建插入SQL
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO tb_equipment_reading_records (");
            sql.append("equipment_code, equipment_type, area_code, reading_date, ");
            sql.append("current_reading, previous_reading, usage_amount, unit_price, ");
            sql.append("tip_reading, previous_tip_reading, tip_usage, tip_price, tip_cost, ");
            sql.append("peak_reading, previous_peak_reading, peak_usage, peak_price, peak_cost, ");
            sql.append("flat_reading, previous_flat_reading, flat_usage, flat_price, flat_cost, ");
            sql.append("valley_reading, previous_valley_reading, valley_usage, valley_price, valley_cost, ");
            sql.append("total_cost, is_time_sharing, current_balance, remark, status, create_time");
            sql.append(") VALUES (");
            sql.append("'").append(equipment_code).append("', ");
            sql.append("'").append(equipment_type).append("', ");
            sql.append("'").append(area_code).append("', ");
            sql.append("'").append(reading_date).append("', ");
            sql.append(current_reading).append(", ");
            sql.append(previous_reading).append(", ");

            // 计算用量
            double usage_amount = current_reading - previous_reading;
            sql.append(usage_amount).append(", ");
            sql.append(unit_price).append(", ");

            // 分时电表数据
            if (is_time_sharing == 1 && "1".equals(equipment_type)) {
                // 尖时
                double tipUsage = (tip_reading != null ? tip_reading : 0) - (previous_tip_reading != null ? previous_tip_reading : 0);
                double tipCost = tipUsage * (tip_price != null ? tip_price : 0);
                sql.append(tip_reading != null ? tip_reading : 0).append(", ");
                sql.append(previous_tip_reading != null ? previous_tip_reading : 0).append(", ");
                sql.append(tipUsage).append(", ");
                sql.append(tip_price != null ? tip_price : 0).append(", ");
                sql.append(tipCost).append(", ");

                // 峰时
                double peakUsage = (peak_reading != null ? peak_reading : 0) - (previous_peak_reading != null ? previous_peak_reading : 0);
                double peakCost = peakUsage * (peak_price != null ? peak_price : 0);
                sql.append(peak_reading != null ? peak_reading : 0).append(", ");
                sql.append(previous_peak_reading != null ? previous_peak_reading : 0).append(", ");
                sql.append(peakUsage).append(", ");
                sql.append(peak_price != null ? peak_price : 0).append(", ");
                sql.append(peakCost).append(", ");

                // 平时
                double flatUsage = (flat_reading != null ? flat_reading : 0) - (previous_flat_reading != null ? previous_flat_reading : 0);
                double flatCost = flatUsage * (flat_price != null ? flat_price : 0);
                sql.append(flat_reading != null ? flat_reading : 0).append(", ");
                sql.append(previous_flat_reading != null ? previous_flat_reading : 0).append(", ");
                sql.append(flatUsage).append(", ");
                sql.append(flat_price != null ? flat_price : 0).append(", ");
                sql.append(flatCost).append(", ");

                // 谷时
                double valleyUsage = (valley_reading != null ? valley_reading : 0) - (previous_valley_reading != null ? previous_valley_reading : 0);
                double valleyCost = valleyUsage * (valley_price != null ? valley_price : 0);
                sql.append(valley_reading != null ? valley_reading : 0).append(", ");
                sql.append(previous_valley_reading != null ? previous_valley_reading : 0).append(", ");
                sql.append(valleyUsage).append(", ");
                sql.append(valley_price != null ? valley_price : 0).append(", ");
                sql.append(valleyCost).append(", ");

                // 总费用
                double totalCost = tipCost + peakCost + flatCost + valleyCost;
                sql.append(totalCost).append(", ");
            } else {
                // 非分时电表，分时字段设为0
                sql.append("0, 0, 0, 0, 0, ");  // tip
                sql.append("0, 0, 0, 0, 0, ");  // peak
                sql.append("0, 0, 0, 0, 0, ");  // flat
                sql.append("0, 0, 0, 0, 0, ");  // valley

                // 总费用 = 用量 * 单价
                double totalCost = usage_amount * unit_price;
                sql.append(totalCost).append(", ");
            }

            sql.append(is_time_sharing).append(", ");
            sql.append(current_balance).append(", ");
            sql.append("'").append(remark != null ? remark : "").append("', ");
            sql.append(status).append(", ");
            sql.append("NOW()");
            sql.append(")");

            Log.info(WECostController.class, "执行SQL: " + sql.toString());

            // 执行插入
            int result = weCostService.dbService.ExecuteUpdate(sql.toString());

            if (result > 0) {
                Log.info(WECostController.class, "设备读数保存成功");

                JsonResult jsonResult = new JsonResult();
                jsonResult.setSuccess(true);
                jsonResult.setMsg("设备读数保存成功");
                return jsonResult;
            } else {
                JsonResult jsonResult = new JsonResult();
                jsonResult.setSuccess(false);
                jsonResult.setMsg("设备读数保存失败");
                return jsonResult;
            }

        } catch (Exception e) {
            Log.error(WECostController.class,
                String.format("保存设备读数失败: %s", e.getMessage()), e);

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("保存失败: " + e.getMessage());
            return result;
        }
    }
}
