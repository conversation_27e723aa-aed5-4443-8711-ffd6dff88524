package com.ymiots.campusos.controller.alleyway;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.entity.alleyway.AlleywayRecordRow;
import com.ymiots.campusos.service.alleyway.CardRecordsService;
import com.ymiots.campusos.service.dev.DeviceService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.filter.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Null;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/AlleyWay/CardRecords")
public class CardRecordsController {

    final String menuid = "e0a1934d-3181-11e8-b7aa-9c5c8e6dafdd";

    @Autowired
    CardRecordsService cardrecords;

    @Autowired
    private DeviceService devservice;

    @RequestMapping(value = "/getAlleyway", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getRecordList(HttpServletRequest request, HttpServletResponse response, String areacode,String machineid, boolean viewchild,
    		String startdate, String enddate, String starttime, String endtime, String datetimetype, String orgcode, String readheader, String infoid, String key, String status, int start, int limit,String infoType) {
        try {
            return cardrecords.getRecordList(request, response, areacode, machineid, viewchild, startdate, enddate, starttime, endtime, datetimetype, orgcode, readheader, infoid, key, status, start, limit,infoType);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getDoorApplyRecordList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getDoorApplyRecordList(HttpServletRequest request, HttpServletResponse response,String starttime,String endtime, String status, String key, int start,int limit){
        try {
            return cardrecords.getDoorApplyRecordList(request, response, starttime, endtime, status, key, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }


    /**
     * 获取门禁下载状态详情
     */
    @ResponseBody
    @RequestMapping(value = "/getDoorDownload", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData getDoorDownload(HttpServletRequest request, HttpServletResponse response, String formid) {
        try {
            return cardrecords.getDoorDownload(request, response, formid);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonData(false, e.getMessage());
        }
    }

    /**
     * 获取门禁下载状态详情
     */
    @ResponseBody
    @RequestMapping(value = "/DoorReturnDownload", method = RequestMethod.POST)
    @Permission(authorization = true, usertype = -1, allowpc = false)
    public JsonData DoorReturnDownload(HttpServletRequest request, HttpServletResponse response, String recordUids) {
        try {
            return cardrecords.DoorReturnDownload(request, response, recordUids);
        } catch (Exception e) {
            e.printStackTrace();
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getAlleywayList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public R<List<AlleywayRecordRow>> getAlleywayList(HttpServletRequest request, HttpServletResponse response, String areacode,String machineid, boolean viewchild,
                                                      String startdate, String enddate, String starttime, String endtime, String datetimetype, String orgcode, String readheader, String infoid, String key, String status, int start, int limit,String infoType) {

        try {
            return cardrecords.getAlleywayList(request, response, areacode, machineid, viewchild, startdate, enddate, starttime, endtime, datetimetype, orgcode, readheader, infoid, key, status, start, limit, infoType);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(new ArrayList<>());
        }
    }

    @RequestMapping(value = "/getHistoryRecordList", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getHistoryRecordList(HttpServletRequest request, HttpServletResponse response, String areacode,String machineid, boolean viewchild,
    		String startdate, String enddate, String starttime, String endtime, String datetimetype, String orgcode, String readheader, String infoid, String key, String status, int start, int limit) {
        try {
            return cardrecords.getHistoryRecordList(request, response, areacode, machineid, viewchild, startdate, enddate, starttime, endtime, datetimetype, orgcode, readheader, infoid, key, status, start, limit);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/AddBlackName", method = RequestMethod.POST)
    @Permission(menu = menuid, opcode = "addblackname")
    public JsonResult AddBlackName(HttpServletRequest request, HttpServletResponse response, String cardnos) {
        try {
            return cardrecords.AddBlackName(request, response, cardnos);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/getRecordimg", method = RequestMethod.POST)
    @Permission(menu = menuid)
    public JsonData getRecordimg(HttpServletRequest request, HttpServletResponse response, String uid) {
        try {
            return cardrecords.getRecordimg(request, response, uid);
        } catch (Exception e) {
            return Json.getJsonData(false, e.getMessage());
        }
    }

    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    @Permission(menu = menuid,opcode = "updatestatus")
    public JsonResult updateStatus(HttpServletRequest request, HttpServletResponse response,String uid,String status){
        try {
            return cardrecords.updateStatus(request, response, uid, status);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }
    
	@RequestMapping(value="/getDeviceList",method = RequestMethod.POST)
	@Permission(menu=menuid)
	public JsonData getDeviceList(HttpServletRequest request, HttpServletResponse response,String areacode,boolean viewchild, String key,int start,int limit){
		try {
			return cardrecords.getDeviceList(request, response,areacode,viewchild, key, start, limit);
		} catch (Exception e) {
			return  Json.getJsonData(false,e.getMessage());
		}
	}

    //@RequestMapping(value="/ExportRecords",method = RequestMethod.POST)
    //@Permission(menu=menuid,opcode="export")
    //public JsonResult ExportRecords(HttpServletRequest request, HttpServletResponse response, String areacode,String machineid, boolean viewchild,
    //                                String startdate, String enddate, String starttime, String endtime, String datetimetype, String orgcode, String readheader, String infoid, String key, String status){
    //    try {
    //        String filepath= cardrecords.ExportRecords(request, response, areacode, machineid, viewchild, startdate, enddate, starttime, endtime, datetimetype, orgcode, readheader, infoid, key, status);
    //        return Json.getJsonResult(true,filepath);
    //    } catch (Exception e) {
    //        return Json.getJsonResult(e.getMessage());
    //    }
    //}

    @RequestMapping(value = "/ExportRecords", method = RequestMethod.POST)
    public R<Null> ExportFaceNote(HttpServletRequest request, HttpServletResponse response, String key, String machineid, String startdate, String enddate) {
        try {
            return cardrecords.exportAlleywayRecord(request, response, key, machineid, startdate, enddate);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @RequestMapping(value="/ExportHistoryRecords",method = RequestMethod.POST)
    @Permission(menu=menuid,opcode="export")
    public JsonResult ExportHistoryRecords(HttpServletRequest request, HttpServletResponse response, String areacode,String machineid, boolean viewchild,
                                    String startdate, String enddate, String starttime, String endtime, String datetimetype, String orgcode, String readheader, String infoid, String key, String status){
        try {
            String filepath= cardrecords.ExportHistoryRecords(request, response, areacode, machineid, viewchild, startdate, enddate, starttime, endtime, datetimetype, orgcode, readheader, infoid, key, status);
            return Json.getJsonResult(true,filepath);
        } catch (Exception e) {
            return Json.getJsonResult(e.getMessage());
        }
    }

    /**
     * 获取区域列表
     * @param request
     * @param response
     * @param key
     * @return
     */
    @RequestMapping(value="/getAreaTree",method = RequestMethod.POST)
    @Permission(menu=menuid)
    public JSONArray getAreaTree(HttpServletRequest request, HttpServletResponse response, String key){
        try {
            return devservice.getAreaTree(request, response,key,true);
        } catch (Exception e) {
            return new JSONArray();
        }
    }
	
}
