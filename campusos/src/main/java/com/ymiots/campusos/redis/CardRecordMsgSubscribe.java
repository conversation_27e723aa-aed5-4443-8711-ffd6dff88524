package com.ymiots.campusos.redis;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.service.SysConfigService;
import com.ymiots.campusos.util.weixin.entity.TemplateMessageData;
import com.ymiots.framework.common.*;
import com.ymiots.framework.websocket.WebSocketClient;
import com.ymiots.framework.websocket.WebSocketSets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

/**
 * 刷卡记录上传（用于实时监控）
 * <AUTHOR>
 *
 */
@Component
public class CardRecordMsgSubscribe extends BaseService implements RedisMessageHandler {

    @Autowired
    RedisPublish redispublish;

	@Autowired
	SysConfigService sysconfig;

	@Override
	public String getChannel() {
		return "/service/recordlist";
	}

	@Override
	public void handleMessage(String message) {
		// 如果消息被转义，可以先去除转义字符再解析
		if (message.startsWith("\"") && message.endsWith("\"")) {
			message = message.substring(1, message.length() - 1);
		}// 去除反斜杠转义
		// 去掉转义字符
		message = message.replace("\\", "");
		JSONObject msgobj=JSONObject.parseObject(message);
		Log.info(CardRecordMsgSubscribe.class, "订阅主题【"+getChannel()+"】接收到消息："+msgobj.toJSONString());

		if (WebConfig.getProcesstask().equals("1")) {
			JSONObject data=msgobj.getJSONObject("Data");
			if(data!=null) {
				SendCardRecord(data);
			}else {
				Log.error(CardRecordMsgSubscribe.class, "消息data is null，无法推送消息");
			}
		}else {
			Log.info(CardRecordMsgSubscribe.class, "本节点未启用任务处理，不做任何处理");
		}
	}

	/**
	 * 发送刷卡记录动态
	 *
	 * @return
	 * @throws IOException
	 */
	private void SendCardRecord(JSONObject msg){
		try {
			int UsedType = msg.getIntValue("UsedType");
			String recordid  = msg.getString("Uid");
			String machineid = msg.getString("Machineid");
			String readhead = msg.getString("Readhead");
			String recordtime = msg.getString("Recordtime");
			String cardsn = msg.getString("Cardsn");
			int personType = msg.getIntValue("Persontype");
			Log.error(this.getClass(), "UsedType==="+String.valueOf(UsedType));
			if(UsedType==21) {
				//机器人身份核验
				String infoid=msg.getString("Infoid");
				Log.error(this.getClass(), "infoid==="+infoid);
				if(!StringUtils.isBlank(infoid)) {
					List<WebSocketClient> screenwsslist = WebSocketSets.getInstance().get("screen_cardrecord");
					 if(screenwsslist.size()>0) {
						if (personType != 3) {
					      //内部人员
						  SendPensonHeYanRecord(screenwsslist, infoid, machineid, recordtime);
					   }else{
					      //访客
					      SendVisitorHeYanRecord(screenwsslist, infoid, machineid, recordtime);
					   }
				   }else {
					   Log.error(this.getClass(), "没有监控大屏订阅");
				   }
				}else {
					Log.error(this.getClass(), "infoid为空");
				}
			}else {
				List<WebSocketClient> wsslist = WebSocketSets.getInstance().get("serviceosinfo");
				String sql = "SELECT ac.uid as controllerid,area.name as areaname,ac.devname,ac.areacode,ac.useclass,ac.used"
						+ " FROM tb_dev_accesscontroller ac inner join tb_dev_areaframework area on ac.areacode=area.code where ac.machineid=?; ";
				JSONArray pslist = dbService.QueryList(sql, machineid);
				if (pslist.size() == 0) {
					Log.error(CardRecordMsgSubscribe.class, String.format("机号%s设备不存在，无法推送消息", machineid));
					return;
				}
				final JSONObject acitem = pslist.getJSONObject(0);
				String useclass=acitem.getString("useclass");
				if(useclass.equals("1")) {
					//出入口
	                if (personType != 3) {
                        String infoid = msg.getString("Infoid");
	                    ProcessAllWayRecord(wsslist, recordid, acitem, cardsn, recordtime, readhead,infoid);
	                }else{
	                	//访客
	                    visitorProcessAllWayRecord(wsslist, recordid, acitem, cardsn, recordtime, readhead);
	                }
				}
				if(useclass.equals("3")) {
					//考勤
					if (personType != 3) {
						String infoid = msg.getString("Infoid");
						ProcessAttendanceAllWayRecord(wsslist, recordid, acitem, cardsn, recordtime, readhead,infoid);
					}else{
						//访客
						visitorProcessAllWayRecord(wsslist, recordid, acitem, cardsn, recordtime, readhead);
					}
				}
				if(useclass.equals("2")) {
					String money  = msg.getString("money");
					//消费
					if (personType != 3) {
						String infoid = msg.getString("Infoid");
						ProcessAttendanceConsumeRecord(wsslist, recordid, acitem, cardsn, recordtime, readhead,infoid,money);
					}else{
						//访客
						visitorProcessAllWayRecord(wsslist, recordid, acitem, cardsn, recordtime, readhead);
					}
				}
			}
		}catch(Exception ex) {
		    ex.printStackTrace();
			Log.error(CardRecordMsgSubscribe.class, ex.getMessage());
		}
	}

	//处理出入口记录消息推送
	private void ProcessAllWayRecord(List<WebSocketClient> wsslist, String recordid ,final JSONObject acitem, String cardsn, String recordtime, String readhead,String infoid) {
		try {
			//更新刷卡次数
			dbService.excuteSql("update tb_alleyway_authorize_infoconfig infocfg inner join tb_card_cardinfo card on card.infoid=infocfg.uid set hasusetime=hasusetime+1 where card.cardsn=? ", cardsn);
			//删除权限使用次数已达上线的人员授权
			dbService.excuteSql("delete az from tb_alleyway_authorize az inner join tb_alleyway_authorize_infoconfig infocfg on infocfg.uid=az.infouid where infocfg.hasusetime>=infocfg.maxusetime;");

			wsslist = CollectionUtils.where(wsslist, new CallBackFilter<WebSocketClient>() {
				@Override
				public Boolean filter(WebSocketClient model) {
					boolean isfilter=false;
					String authority = model.getAuthority();
					if (!StringUtils.isBlank(authority)) {
						JSONArray authorityjson = JSONArray.parseArray(authority);
						for(int i=0;i<authorityjson.size();i++) {
							JSONObject item=authorityjson.getJSONObject(i);
							//infotype：1、2系统服务，3:系统桌面刷卡记录，4:归寝情况监控
							if (item.getJSONArray("infotype").contains(3)) {
								if (String.format(",%s,", item.getString("areacode")).indexOf(String.format(",%s,", acitem.getString("areacode"))) != -1) {
									isfilter=true;
									break;
								}
							}
						}
					}
					return isfilter;
				}
			});

			 List<WebSocketClient> wssbackhomelist = CollectionUtils.where(wsslist, new CallBackFilter<WebSocketClient>() {
				@Override
				public Boolean filter(WebSocketClient model) {
					boolean isfilter=false;
					String authority = model.getAuthority();
					if (!StringUtils.isBlank(authority)) {
						JSONArray authorityjson = JSONArray.parseArray(authority);
						for(int i=0;i<authorityjson.size();i++) {
							JSONObject item=authorityjson.getJSONObject(i);
							//infotype：1、2系统服务，3:刷卡记录，4:归寝情况监控
							if (item.getJSONArray("infotype").contains(4)) {
								if (String.format(",%s,", item.getString("areacode")).indexOf(String.format(",%s,", acitem.getString("areacode"))) != -1) {
									isfilter=true;
									break;
								}
							}
						}
					}
					return isfilter;
				}
			});

			List<WebSocketClient> screenwsslist =WebSocketSets.getInstance().get("screen_cardrecord");

			if(wsslist.size()>0 || screenwsslist.size()>0){
				JSONObject info = GetCardRecordInfo(cardsn, acitem, recordid, readhead, recordtime,infoid);
				if (info != null) {
					JSONObject doorinfo= GetDoorInfo(acitem, readhead);
					if(null!=doorinfo) {
						info.putAll(doorinfo);
						if (wsslist.size() > 0) {
							//推送桌面刷卡记录
							WebSocketSets.getInstance().send(info.toJSONString(), wsslist);
						}
						if (screenwsslist.size() > 0) {
							//发送大屏幕监控刷卡记录
							for(WebSocketClient client:screenwsslist){
								Log.info(this.getClass(), String.format("消息所属区域:%s,订阅客户端要求:%s",acitem.getString("areacode"),client.getAuthority()));
								if(client.getSession().isOpen()) {
									if(!StringUtils.isBlank(client.getAuthority())) {
										//根据区域权限推送
										JSONObject item=JSONObject.parseObject(client.getAuthority());
										if (String.format(",%s,", item.getString("areacode")).indexOf(String.format(",%s,", acitem.getString("areacode"))) != -1) {
											client.getSession().getAsyncRemote().sendText(info.toJSONString());
										}
									}else {
										client.getSession().getAsyncRemote().sendText(info.toJSONString());
									}
								}else {
									Log.info(this.getClass(), "客户端Session.isOpen=false");
								}
							}
						}
					}else {
						Log.error(CardRecordMsgSubscribe.class, "通道门禁为null,消息无法推送");
					}
				}else {
					Log.error(CardRecordMsgSubscribe.class, "卡片信息、档案信息为null,消息无法推送");
				}
			}

			// 刷卡记录上传时，如果有监听归寝的，使用刷卡记录实时更新归寝人数
			if (wssbackhomelist.size() > 0) {
				JSONObject msginfo = new JSONObject();
				msginfo.put("msgtype", 4);
				WebSocketSets.getInstance().send(msginfo.toJSONString(), wsslist);
			}

			//应用为校园版，且设备用途=出入校门口
			if(WebConfig.getApptype().equals("1")) {

                JSONObject msgcfg = sysconfig.get("sysmsg_push_timerang", "sysmsg_push_weekrang");
                if(msgcfg==null) {
                    Log.error(CardRecordMsgSubscribe.class, "消息推送时间和星期配置不存在");
                    return;
                }
                if (DateHelper.ExistsTimeRang(msgcfg.getString("sysmsg_push_timerang")).size() == 0) {
                    Log.info(this.getClass(), "微信消息推送：不在允许推送时间范围");
                    return;
                }
                if (!DateHelper.ExistsWeekRang(msgcfg.getString("sysmsg_push_weekrang"))) {
                    Log.info(this.getClass(), "微信消息推送：不在允许推送星期范围");
                    return;
                }

				//used-2: 出入宿舍 used-3: 出入校门口 used-26 出入口考勤
				if(acitem.getString("used").equals("2") || acitem.getString("used").equals("3") || acitem.getString("used").equals("26")) {

					//出入校提醒
					int msgcount=dbService.getCount("tb_weixin_msgcfg", " code='OPENTM417728550' and ifnull(templateid,'')<>'' ");
					if (msgcount> 0) {
                        String wxsql2="select wx.openid,info.name,wx.uid as wxuserid from tb_weixin_user wx inner join tb_card_teachstudinfo info on wx.usertype=2 and info.infotype=2 and info.uid=wx.docid WHERE info.uid='"+infoid+"' AND wx.status=1;";
						JSONArray wxlist= dbService.QueryList(wxsql2);
						if(wxlist.size()>0) {
                            for (int i = 0; i < wxlist.size(); i++) {
                                JSONObject doorinfo= GetDoorInfo(acitem, readhead);
                                JSONObject wxitem=wxlist.getJSONObject(i);
                                String openid=wxitem.getString("openid");
                                String name=wxitem.getString("name");
                                String wxuserid=wxitem.getString("wxuserid");
                                //审核成功以后推送微信消息给访客
                                String topic="/campus/weixintemplatemsg";
                                RedisMessageEntity message=new RedisMessageEntity();
                                JSONObject data = new JSONObject();
                                data.put("msgid",UUID.randomUUID().toString());
                                data.put("openid",openid);
                                data.put("cfgcode","OPENTM417728550");
								//通行记录链接
//                                data.put("url", String.format("%s/alleyway/detailedrecord?uid=%s", WebConfig.getWeixinDomain(),recordid));
								// 人脸识别记录链接
                                data.put("url", String.format("%s/face/record?", WebConfig.getWeixinDomain()));
                                JSONObject wxdata=new JSONObject();
                                String inoutname="";
                                if(StringUtils.isBlank(doorinfo.getString("inouttype"))){
                                    Log.error(CardRecordMsgSubscribe.class, "未设置门禁进出方向");
                                    return;
                                }
                                if(doorinfo.getString("inouttype").equals("1")) {
                                    inoutname="进入";
                                }
                                if(doorinfo.getString("inouttype").equals("2")) {
                                    inoutname="离开";
                                }
								// 去掉毫秒部分
								recordtime = recordtime.split("\\.")[0];
								// 将字符串解析为 LocalDateTime
								LocalDateTime dateTime = LocalDateTime.parse(recordtime);
								// 定义格式化器
								DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
								recordtime = dateTime.format(formatter);
								if (WebConfig.getStartNew()) {
									wxdata.put("thing15", new TemplateMessageData(name).toJSONString());
									wxdata.put("time3", new TemplateMessageData(recordtime).toJSONString());
									wxdata.put("const14", new TemplateMessageData(inoutname,"#4caf50").toJSONString());
								}else {
									if(acitem.getString("used").equals("2")){
                                    	wxdata.put("first", new TemplateMessageData("家长您好，您孩子刚刚"+inoutname+"宿舍，请知晓！").toJSONString());
                                	} else if (acitem.getString("used").equals("3")){
                                    	wxdata.put("first", new TemplateMessageData("家长您好，您孩子刚刚"+inoutname+"学校，请知晓！").toJSONString());
                                	}
                                	wxdata.put("keyword1", new TemplateMessageData(name).toJSONString());
                                	wxdata.put("keyword2", new TemplateMessageData(recordtime).toJSONString());
                                	wxdata.put("keyword3", new TemplateMessageData(inoutname,"#4caf50").toJSONString());
                                	wxdata.put("remark", new TemplateMessageData("请关注学生安全，点击查看详情").toJSONString());
                                }
								data.put("wxtempdata",wxdata);
								data.put("wxuserid",wxuserid);
                                message.setCmd("weixintemplatemsg");
                                message.setData(data);
                                message.setDevtype(0);
                                redispublish.Publish(topic, message);
                                Log.info(this.getClass(), "wxdata数据为：" + wxdata);
                                Log.info(CardRecordMsgSubscribe.class,"被推送人员："+name+"==="+infoid);
                            }
						}else {
							Log.info(CardRecordMsgSubscribe.class, "校门出入口找不到对应学生档案及微信绑定信息");
						}
					}else {
						Log.error(CardRecordMsgSubscribe.class, "微信消息OPENTM417728550模板配置不存在");
					}
				}

				//上下车消息推送
                if(acitem.getString("used").equals("25")){
                    int msgcount=dbService.getCount("tb_weixin_msgcfg", " code='OPENTM417728550' and ifnull(templateid,'')<>'' ");
                    if (msgcount> 0) {
                        String wxsql2="select wx.openid,info.name,wx.uid as wxuserid from tb_weixin_user wx inner join tb_card_teachstudinfo info on wx.usertype=2 and info.infotype=2 and info.uid=wx.docid WHERE info.uid='"+infoid+"' AND wx.status=1;";
                        JSONArray wxlist= dbService.QueryList(wxsql2);
                        if(wxlist.size()>0) {
                            JSONObject wxitem=wxlist.getJSONObject(0);
                            String openid=wxitem.getString("openid");
                            String name=wxitem.getString("name");
                            String wxuserid=wxitem.getString("wxuserid");
                            String devname = acitem.getString("devname");

                            String topic="/campus/weixintemplatemsg";
                            RedisMessageEntity message=new RedisMessageEntity();
                            JSONObject data = new JSONObject();
                            data.put("msgid",UUID.randomUUID().toString());
                            data.put("openid",openid);
                            data.put("cfgcode","OPENTM415173392");
                            JSONObject wxdata=new JSONObject();
                            wxdata.put("first", new TemplateMessageData("家长您好，您孩子已在校车刷卡，请注意！").toJSONString());
                            wxdata.put("keyword1", new TemplateMessageData(name).toJSONString());
                            wxdata.put("keyword2", new TemplateMessageData(devname).toJSONString());
                            wxdata.put("keyword3", new TemplateMessageData("已刷卡上下车").toJSONString());
                            wxdata.put("keyword4", new TemplateMessageData(recordtime).toJSONString());
                            wxdata.put("remark", new TemplateMessageData("祝您生活愉快。").toJSONString());
                            data.put("wxtempdata",wxdata);
                            data.put("wxuserid",wxuserid);
                            message.setCmd("weixintemplatemsg");
                            message.setData(data);
                            message.setDevtype(0);
                            redispublish.Publish(topic, message);
                        }else {
                            Log.info(CardRecordMsgSubscribe.class, "上下校车找不到对应学生档案及微信绑定信息");
                        }
                    }else {
                        Log.error(CardRecordMsgSubscribe.class, "微信消息OPENTM415173392模板配置不存在");
                    }
                }
			} else {
				//考勤打卡消息推送
				if(acitem.getString("used").equals("26")){
					int msgcount=dbService.getCount("tb_weixin_msgcfg", " code='OPENTM20240316' and ifnull(templateid,'')<>'' ");
					if (msgcount> 0) {
						String wxsql2="select wx.openid,info.name,wx.uid as wxuserid,info.code from tb_weixin_user wx inner join tb_card_teachstudinfo info on wx.usertype=2 and info.infotype=2 and info.uid=wx.docid WHERE info.uid='"+infoid+"' AND wx.status=1;";
						JSONArray wxlist= dbService.QueryList(wxsql2);
						if(wxlist.size()>0) {
							JSONObject wxitem=wxlist.getJSONObject(0);
							String openid=wxitem.getString("openid");
							String name=wxitem.getString("name");
							String code=wxitem.getString("code");
							String wxUserid=wxitem.getString("wxuserid");
							String devName = acitem.getString("devname");

							String topic="/campus/weixintemplatemsg";
							RedisMessageEntity message=new RedisMessageEntity();
							JSONObject data = new JSONObject();
							data.put("msgid",UUID.randomUUID().toString());
							data.put("openid",openid);
							data.put("cfgcode","OPENTM20240316");
							data.put("url", String.format("%s/alleyway/detailedrecord?uid=%s", WebConfig.getWeixinDomain(),recordid));
							JSONObject wxdata=new JSONObject();
							//姓名 工号 考勤地点 考勤时间
							wxdata.put("thing", new TemplateMessageData(name).toJSONString());
							wxdata.put("character_string2", new TemplateMessageData(code).toJSONString());
							wxdata.put("thing3", new TemplateMessageData(devName).toJSONString());
							wxdata.put("time4", new TemplateMessageData(recordtime).toJSONString());
							data.put("wxtempdata",wxdata);
							data.put("wxuserid",wxUserid);
							message.setCmd("weixintemplatemsg");
							message.setData(data);
							message.setDevtype(0);
							redispublish.Publish(topic, message);
							Log.info(this.getClass(), "wxdata数据为：" + wxdata);
							Log.info(CardRecordMsgSubscribe.class,"被推送人员："+name+"==="+infoid);
						}else {
							Log.info(CardRecordMsgSubscribe.class, "考勤打卡找不到对应档案及微信绑定信息");
						}
					}else {
						Log.error(CardRecordMsgSubscribe.class, "微信消息OPENTM20240316模板配置不存在");
					}
				}
			}

		}catch(Exception ex) {
            ex.printStackTrace();
			Log.error(CardRecordMsgSubscribe.class, ex.getMessage());
		}
	}


	//处理考勤消息推送
	private void ProcessAttendanceAllWayRecord(List<WebSocketClient> wsslist, String recordid ,final JSONObject acitem, String cardsn, String recordtime, String readhead,String infoid) {
		try {
			//更新刷卡次数
			dbService.excuteSql("update tb_alleyway_authorize_infoconfig infocfg inner join tb_card_cardinfo card on card.infoid=infocfg.uid set hasusetime=hasusetime+1 where card.cardsn=? ", cardsn);
			//删除权限使用次数已达上线的人员授权
			dbService.excuteSql("delete az from tb_alleyway_authorize az inner join tb_alleyway_authorize_infoconfig infocfg on infocfg.uid=az.infouid where infocfg.hasusetime>=infocfg.maxusetime;");
			//考勤打卡消息推送
			if(acitem.getString("used").equals("11")){
					int msgcount=dbService.getCount("tb_weixin_msgcfg", " code='OPENTM20240316' and ifnull(templateid,'')<>'' ");
					if (msgcount> 0) {
						String wxsql2="select wx.openid,info.name,wx.uid as wxuserid,info.code from tb_weixin_user wx inner join tb_card_teachstudinfo info on wx.usertype=3 and info.infotype=1 and info.uid=wx.docid WHERE info.uid='"+infoid+"' AND wx.status=1";
						JSONArray wxlist= dbService.QueryList(wxsql2);
						if(wxlist.size()>0) {
							JSONObject wxitem=wxlist.getJSONObject(0);
							String openid=wxitem.getString("openid");
							String name=wxitem.getString("name");
							String code=wxitem.getString("code");
							String wxUserid=wxitem.getString("wxuserid");
							String devName = acitem.getString("devname");

							String topic="/campus/weixintemplatemsg";
							RedisMessageEntity message=new RedisMessageEntity();
							JSONObject data = new JSONObject();
							data.put("msgid",UUID.randomUUID().toString());
							data.put("openid",openid);
							data.put("cfgcode","OPENTM20240316");
							data.put("url", String.format("%s/face/record?uid=%s", WebConfig.getWeixinDomain(),recordid));
							JSONObject wxdata=new JSONObject();
							//姓名 工号 考勤地点 考勤时间
							wxdata.put("thing1", new TemplateMessageData(name).toJSONString());
							wxdata.put("character_string2", new TemplateMessageData(code).toJSONString());
							wxdata.put("thing3", new TemplateMessageData(devName).toJSONString());
							wxdata.put("time4", new TemplateMessageData(recordtime).toJSONString());
							data.put("wxtempdata",wxdata);
							data.put("wxuserid",wxUserid);
							message.setCmd("weixintemplatemsg");
							message.setData(data);
							message.setDevtype(0);
							redispublish.Publish(topic, message);
							Log.info(this.getClass(), "wxdata数据为：" + wxdata);
							Log.info(CardRecordMsgSubscribe.class,"被推送人员："+name+"==="+infoid);
						}else {
							Log.info(CardRecordMsgSubscribe.class, "考勤打卡找不到对应档案及微信绑定信息");
						}
					}else {
						Log.error(CardRecordMsgSubscribe.class, "微信消息OPENTM20240316模板配置不存在");
					}
				}

		}catch(Exception ex) {
			ex.printStackTrace();
			Log.error(CardRecordMsgSubscribe.class, ex.getMessage());
		}
	}
	//处理消费消息推送
	private void ProcessAttendanceConsumeRecord(List<WebSocketClient> wsslist, String recordid ,final JSONObject acitem, String cardsn, String recordtime, String readhead,String infoid,String money) {
		try {
					//消费详情消息推送
					int msgcount=dbService.getCount("tb_weixin_msgcfg", " code='OPENTM20240516' and ifnull(templateid,'')<>'' ");
					if (msgcount> 0) {
						String wxsql2="select wx.openid,info.name,wx.uid as wxuserid,info.code from tb_weixin_user wx inner join tb_card_teachstudinfo info on info.uid=wx.docid WHERE info.uid='"+infoid+"' AND wx.status=1";
						JSONArray wxlist= dbService.QueryList(wxsql2);
						Log.info(this.getClass(), "人员信息：" + wxlist);
						if(wxlist.size()>0) {
							for (Object o : wxlist) {
								JSONObject wxitem= (JSONObject) o;
								String openid=wxitem.getString("openid");
								String name=wxitem.getString("name");
								String wxUserid=wxitem.getString("wxuserid");

								String topic="/campus/weixintemplatemsg";
								RedisMessageEntity message=new RedisMessageEntity();
								JSONObject data = new JSONObject();
								data.put("msgid",UUID.randomUUID().toString());
								data.put("openid",openid);
								data.put("cfgcode","OPENTM20240516");
								data.put("url", String.format("%s/consume/getpaydetail?uid=%s", WebConfig.getWeixinDomain(),recordid));
								JSONObject wxdata=new JSONObject();
								//姓名 金额 消费时间
								wxdata.put("thing16", new TemplateMessageData(name).toJSONString());
								wxdata.put("amount10", new TemplateMessageData(money).toJSONString());
								wxdata.put("time9", new TemplateMessageData(recordtime).toJSONString());
								data.put("wxtempdata",wxdata);
								data.put("wxuserid",wxUserid);
								message.setCmd("weixintemplatemsg");
								message.setData(data);
								message.setDevtype(0);
								redispublish.Publish(topic, message);
								Log.info(this.getClass(), "wxdata数据为：" + wxdata);
								Log.info(CardRecordMsgSubscribe.class,"被推送人员："+name+"==="+infoid);
							}
						}else {
							Log.info(CardRecordMsgSubscribe.class, "消费信息找不到对应档案及微信绑定信息");
						}
					}else {
						Log.error(CardRecordMsgSubscribe.class, "微信消息OPENTM20240516模板配置不存在");
					}
		}catch(Exception ex) {
			ex.printStackTrace();
			Log.error(CardRecordMsgSubscribe.class, ex.getMessage());
		}
	}


	private JSONObject GetDoorInfo(JSONObject aclist, String readhead){
		JSONObject msginfo=new JSONObject();
		String sql="SELECT door.name as doorname,door.inouttype,dic.name as inoutname FROM tb_dev_door door left join tb_sys_dictionary dic on dic.code=door.inouttype and dic.groupcode='SYS0000018' where controllerid=? and readhead=?;";
		JSONArray doorlist= dbService.QueryList(sql,aclist.getString("controllerid"),readhead);
		if(doorlist.size()>0){
			JSONObject door=doorlist.getJSONObject(0);
			msginfo.put("inouttype", door.getIntValue("inouttype"));
			msginfo.put("inoutname", door.getString("inoutname"));
			msginfo.put("doorname",door.getString("doorname"));
			msginfo.put("msgtype", 3);
			return msginfo;
		}else {
			return null;
		}
	}

	//获取考勤设备信息
	public void getDevInfo(String readHead,JSONObject acitem) {

	}


	private JSONObject GetCardRecordInfo(String cardsn, JSONObject acitem,String recordid, String readhead, String recordtime,String infoid){
		String sql="SELECT t.uid as infoid,t.code as infocode,t.name as infoname,t.mobile,t.orgcode,org.name as orgname," +
                "c.usedes,t.nation,t.birthday,t.address,t.intoyear,t.infotype,linkman1,linkmobile1,linkman2,linkmobile2,t.infolabel,t.property,dic.name as propertyname " +
                "FROM tb_card_teachstudinfo t " +
                "left join tb_card_cardinfo c on t.uid=c.infoid " +
                "left join tb_sys_dictionary dic on dic.code=t.property and dic.groupcode='SYS0000055'" +
                "inner join tb_card_orgframework org on org.code=t.orgcode ";
        JSONArray infolist = new JSONArray();
        if (StringUtils.isNotBlank(infoid)) {
            sql += "where t.uid=? ;";
            infolist= dbService.QueryList(sql,infoid);
        } else if(StringUtils.isNotBlank(cardsn)){
            sql += "where c.cardsn=? and c.status=1;";
            infolist= dbService.QueryList(sql,cardsn);
        }

		if(infolist.size()>0){
			JSONObject info=infolist.getJSONObject(0);
			String facepath="";
            if (StringUtils.isBlank(infoid)) {
                infoid=info.getString("infoid");
            }

			sql="SELECT imgpath FROM tb_face_infoface where infoid='"+infoid+"' limit 1;";
			JSONArray facelist= dbService.QueryList(sql);
			if(facelist.size()>0){
				facepath=facelist.getJSONObject(0).getString("imgpath");
				facepath= ComHelper.getResourceUrl(WebConfig.getResourceDomain(), facepath);
			}
			String selrecordimg = "SELECT recordimg FROM tb_alleyway_records WHERE uid=?";
            JSONArray recordimgja = dbService.QueryList(selrecordimg, recordid);
            String recordimg = "";
            if (!recordimgja.isEmpty()) {
                recordimg = recordimgja.getJSONObject(0).getString("recordimg");
            }
            JSONObject msginfo=new JSONObject();
			msginfo.put("infoid", info.getString("infoid"));
			msginfo.put("recordtime", recordtime);
			msginfo.put("recordimg",recordimg);
			msginfo.put("cardsn", cardsn);
			msginfo.put("areaname",acitem.getString("areaname"));
			msginfo.put("devname",acitem.getString("devname"));
			msginfo.put("useclass",acitem.getString("useclass"));
			msginfo.put("used",acitem.getString("used"));
			msginfo.put("infocode", info.getString("infocode"));
			msginfo.put("infoname", info.getString("infoname"));
			msginfo.put("mobile", info.getString("mobile"));
			msginfo.put("infotype", info.getString("infotype"));
			msginfo.put("linkman1", info.getString("linkman1"));
			msginfo.put("linkmobile1", info.getString("linkmobile1"));
			msginfo.put("linkman2", info.getString("linkman2"));
			msginfo.put("linkmobile2", info.getString("linkmobile2"));
			msginfo.put("orgname", info.getString("orgname"));
			msginfo.put("usedes", info.getString("usedes"));
			msginfo.put("nation", info.getString("nation"));
			msginfo.put("birthday", info.getString("birthday"));
			msginfo.put("address", info.getString("address"));
			msginfo.put("intoyear", info.getString("intoyear"));
			msginfo.put("orgcode", info.getString("orgcode"));
			msginfo.put("infolabel", info.getString("infolabel"));
			msginfo.put("propertyname", info.getString("propertyname"));
			msginfo.put("facepath", facepath);
			return msginfo;
		}else{
			return null;
		}
	}

    private JSONObject GetRecordVisitorInfo(String cardsn, JSONObject acitem,String recordid, String readhead, String recordtime) {
        String sql = "SELECT vp.facepath as recordimg,vp.recordtime,vv.uid,vv.name,vv.cardsn,vv.idtype,vv.idcard,vv.sex,vv.mobile,vv.nation,vv.photo as facepath,vv.company,vv.position,vv.department" +
                " FROM tb_visitor_passrecord vp " +
                "INNER JOIN tb_visitor_visitorinfo vv ON vp.visitorid=vv.uid " +
                "WHERE vp.uid=?";
        JSONArray infolist = dbService.QueryList(sql, recordid);
        if (!infolist.isEmpty()) {
            JSONObject jo=infolist.getJSONObject(0);
            JSONObject msginfo=new JSONObject();
            msginfo.put("visitorid", jo.getString("uid"));
            msginfo.put("recordtime", recordtime);
            msginfo.put("recordimg",jo.getString("recordimg"));
            msginfo.put("cardsn", cardsn);
            msginfo.put("areaname",acitem.getString("areaname"));
            msginfo.put("devname",acitem.getString("devname"));
            msginfo.put("useclass",acitem.getString("useclass"));
            msginfo.put("used",acitem.getString("used"));
            msginfo.put("infoname", jo.getString("name"));
            msginfo.put("mobile", jo.getString("mobile"));
            msginfo.put("idcard", jo.getString("idcard"));
            msginfo.put("sex", jo.getString("sex"));
            msginfo.put("nation", jo.getString("nation"));
            msginfo.put("company", jo.getString("company"));
            msginfo.put("position", jo.getString("position"));
            msginfo.put("department", jo.getString("department"));
            msginfo.put("facepath", jo.getString("facepath"));
            msginfo.put("infotype", 3);

            JSONArray list=dbService.QueryList("select byvisitor,byvisitorid from tb_visitor_record where visitorid='"+jo.getString("uid")+"' and isleave=0 order by createdate desc limit 1 ");
            if(list.size()>0) {
            	JSONObject item=list.getJSONObject(0);
            	msginfo.put("byvisitor", item.getString("byvisitor"));
            	JSONArray orglist=dbService.QueryList("SELECT getminiorgname(orgcode) as byvisitororg FROM tb_card_teachstudinfo where uid='"+item.getString("byvisitorid")+"'");
            	if(orglist.size()>0) {
            		msginfo.put("byvisitororg", orglist.getJSONObject(0).getString("byvisitororg"));
            	}
            }
            return msginfo;
        }else{
            return null;
        }
    }

    //访客出入口记录消息推送
    private void visitorProcessAllWayRecord(List<WebSocketClient> wsslist, String recordid ,final JSONObject acitem, String cardsn, String recordtime, String readhead) {
        try {
            List<WebSocketClient> screenwsslist = WebSocketSets.getInstance().get("screen_cardrecord");
            if(wsslist.size()>0 || screenwsslist.size()>0){
                JSONObject info = GetRecordVisitorInfo(cardsn, acitem, recordid, readhead, recordtime);
                if (info != null) {
                    JSONObject doorinfo= GetDoorInfo(acitem, readhead);
                    if(null!=doorinfo) {
                        info.putAll(doorinfo);
                        if (screenwsslist.size() > 0) {
                          //发送大屏幕监控刷卡记录
							for(WebSocketClient client:screenwsslist){
								if(client.getSession().isOpen()) {
									if(!StringUtils.isBlank(client.getAuthority())) {
										//根据区域权限推送
										JSONObject item=JSONObject.parseObject(client.getAuthority());
										if (String.format(",%s,", item.getString("areacode")).indexOf(String.format(",%s,", acitem.getString("areacode"))) != -1) {
											client.getSession().getAsyncRemote().sendText(info.toJSONString());
										}
									}else {
										client.getSession().getAsyncRemote().sendText(info.toJSONString());
									}
								}
							}
                        }
                    }else {
						Log.error(CardRecordMsgSubscribe.class, "通道门禁为null,访客消息无法推送");
					}
				}else {
					Log.error(CardRecordMsgSubscribe.class, "访客卡片信息、身份信息为null,访客消息无法推送");
				}
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.error(CardRecordMsgSubscribe.class, e.getMessage());
        }
    }


   /**
    *  内部人员身份核验
    * @param infoid
    * @param machineid
    * @param recordtime
    */
   private void SendPensonHeYanRecord(List<WebSocketClient> screenwsslist, String infoid, String machineid, String recordtime) {
	   String sql="SELECT uid as infoid,code as infocode,name as infoname,mobile,getminiorgname(orgcode) as orgname,cardsn FROM tb_card_teachstudinfo where uid='"+infoid+"';";
		JSONArray infolist= dbService.QueryList(sql);
		if(infolist.size()>0){
			sql="SELECT uid,devname,useclass,used,areacode,getminiareaname(areacode) as areaname FROM tb_dev_accesscontroller where machineid='"+machineid+"'";
	    	   JSONArray devlist=dbService.QueryList(sql);
	    	   if (!devlist.isEmpty()) {
	    		   JSONObject acitem=devlist.getJSONObject(0);
	    		   JSONObject info=infolist.getJSONObject(0);
	    		   String facepath="";
		   		   sql="SELECT imgpath FROM tb_face_infoface where infoid='"+infoid+"' limit 1;";
		   		   JSONArray facelist= dbService.QueryList(sql);
		   		   if(facelist.size()>0){
		   				facepath=facelist.getJSONObject(0).getString("imgpath");
		   				facepath= ComHelper.getResourceUrl(WebConfig.getResourceDomain(), facepath);
		   		   }
			   	   JSONObject msginfo=new JSONObject();
				   msginfo.put("infoid", info.getString("infoid"));
				   msginfo.put("recordtime", recordtime);
				   msginfo.put("recordimg",facepath);
				   msginfo.put("cardsn", info.getString("cardsn"));
				   msginfo.put("areaname",acitem.getString("areaname"));
				   msginfo.put("devname",acitem.getString("devname"));
				   msginfo.put("useclass",acitem.getString("useclass"));
				   msginfo.put("used",acitem.getString("used"));
				   msginfo.put("infocode", info.getString("infocode"));
				   msginfo.put("infoname", info.getString("infoname"));
				   msginfo.put("mobile", info.getString("mobile"));
				   msginfo.put("orgname", info.getString("orgname"));
				   msginfo.put("facepath", facepath);
				   msginfo.put("infotype", 1);

				   Log.info(this.getClass(), "screenwsslist.size=="+String.valueOf(screenwsslist.size()));

				   for(WebSocketClient client:screenwsslist){
						if(client.getSession().isOpen()) {
							if(!StringUtils.isBlank(client.getAuthority())) {
								//根据区域权限推送
								JSONObject item=JSONObject.parseObject(client.getAuthority());
								if (String.format(",%s,", item.getString("areacode")).indexOf(String.format(",%s,", acitem.getString("areacode"))) != -1) {
									Log.info(this.getClass(),"wss推送消息:"+msginfo.toJSONString());
									client.getSession().getAsyncRemote().sendText(msginfo.toJSONString());
								}else {
									Log.info(this.getClass(), "设备["+acitem.getString("devname")+"]所属区域代码不符");
								}
							}else {
								Log.info(this.getClass(),"wss推送消息:"+msginfo.toJSONString());
								client.getSession().getAsyncRemote().sendText(msginfo.toJSONString());
							}
						}else {
							Log.info(this.getClass(), "客户端Session.isOpen=false");
						}
					}
	    	   }else {
	   	    	Log.error(this.getClass(), "人员身份核验设备信息不存在");
	  	     }
		 }else {
	    	Log.error(this.getClass(), "人员信息不存在");
	     }
   }

   /**
    *  访客身份核验
    * @param infoid
    * @param machineid
    * @param recordtime
    */
   private void SendVisitorHeYanRecord(List<WebSocketClient> screenwsslist, String infoid, String machineid, String recordtime) {
	   String sql = "SELECT uid,name,cardsn,idtype,idcard,sex,mobile,nation,photo,company,position,department " +
               " FROM tb_visitor_visitorinfo WHERE uid=?";
       JSONArray infolist = dbService.QueryList(sql, infoid);
       if (infolist.size()>0) {
    	   sql="SELECT uid,devname,useclass,used,areacode,getminiareaname(areacode) as areaname FROM tb_dev_accesscontroller where machineid='"+machineid+"'";
    	   JSONArray devlist=dbService.QueryList(sql);
    	   if (devlist.size()>0) {
    		   JSONObject acitem=devlist.getJSONObject(0);
    		   JSONObject jo=infolist.getJSONObject(0);
               JSONObject msginfo=new JSONObject();
               msginfo.put("visitorid", jo.getString("uid"));
               msginfo.put("recordtime", recordtime);
               msginfo.put("recordimg",jo.getString("photo"));
               msginfo.put("cardsn", jo.getString("cardsn"));
               msginfo.put("areaname",acitem.getString("areaname"));
               msginfo.put("devname",acitem.getString("devname"));
               msginfo.put("useclass",acitem.getString("useclass"));
               msginfo.put("used",acitem.getString("used"));
               msginfo.put("infoname", jo.getString("name"));
               msginfo.put("mobile", jo.getString("mobile"));
               msginfo.put("idcard", jo.getString("idcard"));
               msginfo.put("sex", jo.getString("sex"));
               msginfo.put("nation", jo.getString("nation"));
               msginfo.put("company", jo.getString("company"));
               msginfo.put("position", jo.getString("position"));
               msginfo.put("department", jo.getString("department"));
               msginfo.put("facepath", jo.getString("photo"));
               msginfo.put("infotype", 3);

               sql="select byvisitor,byvisitorid from tb_visitor_record where visitorid='"+infoid+"' and isleave=0 and status=1 order by createdate desc limit 1 ";
               JSONArray vislist=dbService.QueryList(sql);
               if(vislist.size()>0) {
               	JSONObject item=vislist.getJSONObject(0);
               	msginfo.put("byvisitor", item.getString("byvisitor"));
               	JSONArray orglist=dbService.QueryList("SELECT getminiorgname(orgcode) as byvisitororg FROM tb_card_teachstudinfo where uid='"+item.getString("byvisitorid")+"'");
               	if(orglist.size()>0) {
               		msginfo.put("byvisitororg", orglist.getJSONObject(0).getString("byvisitororg"));
               	 }
              }
              msginfo.put("inouttype", 0);
   			  msginfo.put("inoutname", "核验");
   			  msginfo.put("doorname", acitem.getString("devname"));
   			  msginfo.put("msgtype", 3);

   			  Log.info(this.getClass(), "screenwsslist.size=="+String.valueOf(screenwsslist.size()));

   			  for(WebSocketClient client:screenwsslist){
				if(client.getSession().isOpen()) {
					if(!StringUtils.isBlank(client.getAuthority())) {
						//根据区域权限推送
						JSONObject item=JSONObject.parseObject(client.getAuthority());
						if (String.format(",%s,", item.getString("areacode")).indexOf(String.format(",%s,", acitem.getString("areacode"))) != -1) {
							Log.info(this.getClass(),"wss推送消息:"+msginfo.toJSONString());
							client.getSession().getAsyncRemote().sendText(msginfo.toJSONString());
						}else {
							Log.info(this.getClass(), "设备["+acitem.getString("devname")+"]所属区域代码不符");
						}
					}else {
						Log.info(this.getClass(),"wss推送消息:"+msginfo.toJSONString());
						client.getSession().getAsyncRemote().sendText(msginfo.toJSONString());
					}
				}else {
					Log.info(this.getClass(), "客户端Session.isOpen=false");
				}
			 }
    	   }else {
    		   Log.error(this.getClass(), "访客身份核验设备信息不存在");
    	   }
       }else {
    	   Log.error(this.getClass(), "访客信息不存在");
       }
   }
}
