package com.ymiots.campusos.dahua.event.device;

import com.ymiots.campusos.dahua.bean.Device;
import com.ymiots.campusos.dahua.common.DaHuaUrl;
import com.ymiots.campusos.dahua.conf.DynamicTask;
import com.ymiots.campusos.dahua.service.DaHuaFaceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 管理zlm流媒体节点的状态
 */
@Component
public class ServerStatusManger {

//    private final static Logger logger = LoggerFactory.getLogger(ServerStatusManger.class);
//
//    private final Map<Object, Device> offlineZlmPrimaryMap = new ConcurrentHashMap<>();
//    private final Map<Object, Device> offlineZlmsecondaryMap = new ConcurrentHashMap<>();
//    private final Map<Object, Long> offlineZlmTimeMap = new ConcurrentHashMap<>();
//
//
//    @Autowired
//    private DaHuaFaceService daHuaFaceService;
//
//    @Autowired
//    private DeviceConnectionManager deviceConnectionManager;
//
//    @Autowired
//    private DynamicTask dynamicTask;
//
//    private final String type = "ym";
//
//    @Async("taskExecutor")
//    @EventListener
//    public void onApplicationEvent(ServerChangeEvent event) {
//        if (event.getMediaServerItemList() == null
//                || event.getMediaServerItemList().isEmpty()) {
//            return;
//        }
//        for (Device mediaServerItem : event.getMediaServerItemList()) {
////            if (!type.equals(mediaServerItem.getType())) {
////                continue;
////            }
//            logger.info("[DH-添加待上线节点] ID：" + mediaServerItem.getDevSn());
//            offlineZlmPrimaryMap.put(mediaServerItem.getDevSn(), mediaServerItem);
//            offlineZlmTimeMap.put(mediaServerItem.getDevSn(), System.currentTimeMillis());
////            execute();
//        }
//    }
//
//    @Async("taskExecutor")
//    @EventListener
//    public void onApplicationEvent(ServerDeleteEvent event) {
//        if (event.getMediaServerId() == null) {
//            return;
//        }
//        logger.info("[DH-节点被移除] ID：" + event.getMediaServerId());
//        offlineZlmPrimaryMap.remove(event.getMediaServerId());
//        offlineZlmsecondaryMap.remove(event.getMediaServerId());
//        offlineZlmTimeMap.remove(event.getMediaServerId());
//    }
//
//    @Scheduled(fixedDelay = 10*1000)   //每隔10秒检查一次
//    public void execute(){
//        // 初次加入的离线节点会在30分钟内，每间隔十秒尝试一次，30分钟后如果仍然没有上线，则每隔30分钟尝试一次连接
//        if (offlineZlmPrimaryMap.isEmpty() && offlineZlmsecondaryMap.isEmpty()) {
//            return;
//        }
//        if (!offlineZlmPrimaryMap.isEmpty()) {
//            for (Device mediaServerItem : offlineZlmPrimaryMap.values()) {
////                if (offlineZlmTimeMap.get(mediaServerItem.getDevSn()) <  System.currentTimeMillis() - 30*60*1000) {
////                    offlineZlmsecondaryMap.put(mediaServerItem.getDevSn(), mediaServerItem);
////                    offlineZlmPrimaryMap.remove(mediaServerItem.getDevSn());
////                    continue;
////                }
//                logger.info("[DH-尝试连接] ID：{}, 地址： {}:{}", mediaServerItem.getDevSn(), mediaServerItem.getDevIp(), mediaServerItem.getDevPort());
////                deviceConnectionManager.sendGetMultipart(mediaServerItem.getDevSn(), DaHuaUrl.EVENT_URL.getUrl(mediaServerItem.getDevIp()), null, "admin", "admin123", null);
//                deviceConnectionManager.sendGetMultipart("AD0A425PAJ6A3AA", DaHuaUrl.EVENT_URL.getUrl("************"), null, "admin", "admin123", null);
//
////                JSONObject responseJson = zlmresTfulUtils.getMediaServerConfig(mediaServerItem);
////                ZLMServerConfig zlmServerConfig = null;
////                if (responseJson == null) {
////                    logger.info("[ZLM-尝试连接]失败, ID：{}, 地址： {}:{}", mediaServerItem.getDevSn(), mediaServerItem.getDevIp(), mediaServerItem.getDevPort());
////                    continue;
////                }
////                JSONArray data = responseJson.getJSONArray("data");
////                if (data == null || data.isEmpty()) {
////                    logger.info("[ZLM-尝试连接]失败, ID：{}, 地址： {}:{}", mediaServerItem.getDevSn(), mediaServerItem.getDevIp(), mediaServerItem.getDevPort());
////                }else {
////                    zlmServerConfig = JSON.parseObject(JSON.toJSONString(data.get(0)), ZLMServerConfig.class);
////                    initPort(mediaServerItem, zlmServerConfig);
////                    online(mediaServerItem, zlmServerConfig);
//                }
//            }
//        }
////        if (!offlineZlmsecondaryMap.isEmpty()) {
////            for (Device mediaServerItem : offlineZlmsecondaryMap.values()) {
////                if (offlineZlmTimeMap.get(mediaServerItem.getDevSn()) <  System.currentTimeMillis() - 30*60*1000) {
////                    continue;
////                }
////                logger.info("[ZLM-尝试连接] ID：{}, 地址： {}:{}", mediaServerItem.getDevSn(), mediaServerItem.getDevIp(), mediaServerItem.getDevPort());
////                JSONObject responseJson = zlmresTfulUtils.getMediaServerConfig(mediaServerItem);
////                ZLMServerConfig zlmServerConfig = null;
////                if (responseJson == null) {
////                    logger.info("[ZLM-尝试连接]失败, ID：{}, 地址： {}:{}", mediaServerItem.getDevSn(), mediaServerItem.getDevIp(), mediaServerItem.getDevPort());
////                    offlineZlmTimeMap.put(mediaServerItem.getId(), System.currentTimeMillis());
////                    continue;
////                }
////                JSONArray data = responseJson.getJSONArray("data");
////                if (data == null || data.isEmpty()) {
////                    logger.info("[ZLM-尝试连接]失败, ID：{}, 地址： {}:{}", mediaServerItem.getDevSn(), mediaServerItem.getDevIp(), mediaServerItem.getDevPort());
////                    offlineZlmTimeMap.put(mediaServerItem.getId(), System.currentTimeMillis());
////                }else {
////                    zlmServerConfig = JSON.parseObject(JSON.toJSONString(data.get(0)), ZLMServerConfig.class);
////                    initPort(mediaServerItem, zlmServerConfig);
////                    online(mediaServerItem, zlmServerConfig);
////                }
////            }
////        }
////    }
//
//    private void online(Device mediaServerItem) {
//        offlineZlmPrimaryMap.remove(mediaServerItem.getDevSn());
//        offlineZlmsecondaryMap.remove(mediaServerItem.getDevSn());
//        offlineZlmTimeMap.remove(mediaServerItem.getDevSn());
//        if (mediaServerItem.getStatus() == 0) {
//            logger.info("[DH-连接成功] ID：{}, 地址： {}:{}", mediaServerItem.getDevSn(), mediaServerItem.getDevIp(), mediaServerItem.getDevPort());
//            mediaServerItem.setStatus(1);
////            mediaServerService.update(mediaServerItem);
////            if(mediaServerItem.isAutoConfig()) {
////                if (config == null) {
////                    JSONObject responseJSON = zlmresTfulUtils.getMediaServerConfig(mediaServerItem);
////                    JSONArray data = responseJSON.getJSONArray("data");
////                    if (data != null && !data.isEmpty()) {
////                        config = JSON.parseObject(JSON.toJSONString(data.get(0)), ZLMServerConfig.class);
////                    }
////                }
////                if (config != null) {
////                    initPort(mediaServerItem, config);
////                    setZLMConfig(mediaServerItem, "0".equals(config.getHookEnable())
////                            || !Objects.equals(mediaServerItem.getHookAliveInterval(), config.getHookAliveInterval()));
////                }
////            }
////            mediaServerService.update(mediaServerItem);
//        }
//        // 设置两次心跳未收到则认为zlm离线
//        String key = "DH-keepalive-" + mediaServerItem.getDevSn();
//        dynamicTask.startDelay(key, ()->{
//            logger.warn("[DH-心跳超时] ID：{}", mediaServerItem.getDevSn());
//            mediaServerItem.setStatus(0);
//            offlineZlmPrimaryMap.put(mediaServerItem.getDevSn(), mediaServerItem);
//            offlineZlmTimeMap.put(mediaServerItem.getDevSn(), System.currentTimeMillis());
//            // TODO 发送离线通知
//            daHuaFaceService.update(mediaServerItem);
//        }, 10 * 2 * 1000);
//    }
//
////    public void setZLMConfig(MediaServer mediaServerItem, boolean restart) {
////        logger.info("[媒体服务节点] 正在设置 ：{} -> {}:{}",
////                mediaServerItem.getId(), mediaServerItem.getIp(), mediaServerItem.getHttpPort());
////        String protocol = sslEnabled ? "https" : "http";
////        String hookPrefix = String.format("%s://%s:%s%s/index/hook", protocol, mediaServerItem.getHookIp(), serverPort, (serverServletContextPath == null || "/".equals(serverServletContextPath)) ? "" : serverServletContextPath);
////
////        Map<String, Object> param = new HashMap<>();
////        param.put("api.secret",mediaServerItem.getSecret()); // -profile:v Baseline
////        if (mediaServerItem.getRtspPort() != 0) {
////            param.put("ffmpeg.snap", "%s -rtsp_transport tcp -i %s -y -f mjpeg -frames:v 1 %s");
////        }
////        param.put("hook.enable","1");
////        param.put("hook.on_flow_report","");
////        param.put("hook.on_play",String.format("%s/on_play", hookPrefix));
////        param.put("hook.on_http_access","");
////        param.put("hook.on_publish", String.format("%s/on_publish", hookPrefix));
////        param.put("hook.on_record_ts","");
////        param.put("hook.on_rtsp_auth","");
////        param.put("hook.on_rtsp_realm","");
////        param.put("hook.on_server_started",String.format("%s/on_server_started", hookPrefix));
////        param.put("hook.on_shell_login","");
////        param.put("hook.on_stream_changed",String.format("%s/on_stream_changed", hookPrefix));
////        param.put("hook.on_stream_none_reader",String.format("%s/on_stream_none_reader", hookPrefix));
////        param.put("hook.on_stream_not_found",String.format("%s/on_stream_not_found", hookPrefix));
////        param.put("hook.on_server_keepalive",String.format("%s/on_server_keepalive", hookPrefix));
////        param.put("hook.on_send_rtp_stopped",String.format("%s/on_send_rtp_stopped", hookPrefix));
////        param.put("hook.on_rtp_server_timeout",String.format("%s/on_rtp_server_timeout", hookPrefix));
////        param.put("hook.on_record_mp4",String.format("%s/on_record_mp4", hookPrefix));
////        param.put("hook.timeoutSec","30");
////        param.put("hook.alive_interval", mediaServerItem.getHookAliveInterval());
////        // 推流断开后可以在超时时间内重新连接上继续推流，这样播放器会接着播放。
////        // 置0关闭此特性(推流断开会导致立即断开播放器)
////        // 此参数不应大于播放器超时时间
////        // 优化此消息以更快的收到流注销事件
////        param.put("protocol.continue_push_ms", "3000" );
////        // 最多等待未初始化的Track时间，单位毫秒，超时之后会忽略未初始化的Track, 设置此选项优化那些音频错误的不规范流，
////        // 等zlm支持给每个rtpServer设置关闭音频的时候可以不设置此选项
////        if (mediaServerItem.isRtpEnable() && !ObjectUtils.isEmpty(mediaServerItem.getRtpPortRange())) {
////            param.put("rtp_proxy.port_range", mediaServerItem.getRtpPortRange().replace(",", "-"));
////        }
////
////        if (!ObjectUtils.isEmpty(mediaServerItem.getRecordPath())) {
////            File recordPathFile = new File(mediaServerItem.getRecordPath());
////            param.put("protocol.mp4_save_path", recordPathFile.getParentFile().getPath());
////            param.put("protocol.downloadRoot", recordPathFile.getParentFile().getPath());
////            param.put("record.appName", recordPathFile.getName());
////        }
////
////        JSONObject responseJSON = zlmresTfulUtils.setServerConfig(mediaServerItem, param);
////
////        if (responseJSON != null && responseJSON.getInteger("code") == 0) {
////            if (restart) {
////                logger.info("[媒体服务节点] 设置成功,开始重启以保证配置生效 {} -> {}:{}",
////                        mediaServerItem.getId(), mediaServerItem.getIp(), mediaServerItem.getHttpPort());
////                zlmresTfulUtils.restartServer(mediaServerItem);
////            }else {
////                logger.info("[媒体服务节点] 设置成功 {} -> {}:{}",
////                        mediaServerItem.getId(), mediaServerItem.getIp(), mediaServerItem.getHttpPort());
////            }
////        }else {
////            logger.info("[媒体服务节点] 设置媒体服务节点失败 {} -> {}:{}",
////                    mediaServerItem.getId(), mediaServerItem.getIp(), mediaServerItem.getHttpPort());
////        }
////    }

}
