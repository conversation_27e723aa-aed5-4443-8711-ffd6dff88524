package com.ymiots.campusos.vo.consume;

import com.ymiots.campusos.common.OsSign;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PayRefundVo extends OsSign {
    @NotBlank
    private String orderNo;

    @NotBlank
    private String refundOrderNo;

    private String payType;

    private String payMethods;

    @NotNull(message = "退款金额不能为空")
    @Min(value = 0, message = "退款金额不能为负数")
    private Integer refund;

    @NotNull(message = "订单金额不能为空")
    @Min(value = 0, message = "订单金额不能为负数")
    private Integer orderAmount;
}
