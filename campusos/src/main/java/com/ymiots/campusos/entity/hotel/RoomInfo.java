package com.ymiots.campusos.entity.hotel;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 房间信息实体类
 * 对应 tb_room_info 表
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@Accessors(chain = true)
public class RoomInfo {

    /**
     * 主键ID
     */
    private String uid;

    /**
     * 房间编号
     */
    private String roomCode;

    /**
     * 房间名称
     */
    private String roomName;

    /**
     * 房间类型(单人间/双人间/套房等)
     */
    private String roomType;

    /**
     * 楼栋编号
     */
    private String buildingCode;

    /**
     * 楼栋名称
     */
    private String buildingName;

    /**
     * 楼层
     */
    private Integer floorNumber;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 房间状态(1正常 2停用 3维修)
     */
    private Integer roomStatus;

    /**
     * 入住人数
     */
    private Integer occupantCount;

    /**
     * 最大入住人数
     */
    private Integer maxOccupant;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 记录状态(1正常 0删除)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人ID
     */
    private String creatorId;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 修改人ID
     */
    private String modifierId;

    /**
     * 获取房间状态描述
     */
    public String getRoomStatusDesc() {
        if (roomStatus == null) return "未知";
        switch (roomStatus) {
            case 1: return "正常";
            case 2: return "停用";
            case 3: return "维修";
            default: return "未知";
        }
    }

    /**
     * 获取完整房间信息
     */
    public String getFullRoomInfo() {
        return String.format("%s - %s (%s)", buildingName, roomName, roomType);
    }

    /**
     * 判断是否可以入住
     */
    public boolean isAvailableForOccupancy() {
        return roomStatus == 1 && (occupantCount == null || occupantCount < maxOccupant);
    }

    /**
     * 获取入住率
     */
    public double getOccupancyRate() {
        if (maxOccupant == null || maxOccupant == 0) return 0.0;
        if (occupantCount == null) return 0.0;
        return (double) occupantCount / maxOccupant * 100;
    }
}
