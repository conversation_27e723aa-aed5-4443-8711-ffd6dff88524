package com.ymiots.campusos.entity.hotel;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 月结房间账单实体类
 * 对应 tb_monthly_room_bill 表
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@Accessors(chain = true)
public class MonthlyRoomBill {

    /**
     * 主键ID
     */
    private String uid;

    /**
     * 账单月份(YYYY-MM)
     */
    private String billMonth;

    /**
     * 房间编号
     */
    private String roomCode;

    /**
     * 房间名称
     */
    private String roomName;

    /**
     * 楼栋名称
     */
    private String buildingName;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    // ==================== 电费信息 ====================
    /**
     * 电量用量(kWh)
     */
    private BigDecimal electricUsage;

    /**
     * 电费金额
     */
    private BigDecimal electricAmount;

    /**
     * 尖时用量
     */
    private BigDecimal electricTipUsage;

    /**
     * 尖时费用
     */
    private BigDecimal electricTipAmount;

    /**
     * 峰时用量
     */
    private BigDecimal electricPeakUsage;

    /**
     * 峰时费用
     */
    private BigDecimal electricPeakAmount;

    /**
     * 平时用量
     */
    private BigDecimal electricFlatUsage;

    /**
     * 平时费用
     */
    private BigDecimal electricFlatAmount;

    /**
     * 谷时用量
     */
    private BigDecimal electricValleyUsage;

    /**
     * 谷时费用
     */
    private BigDecimal electricValleyAmount;

    // ==================== 水费信息 ====================
    /**
     * 水量用量(m³)
     */
    private BigDecimal waterUsage;

    /**
     * 水费金额
     */
    private BigDecimal waterAmount;

    // ==================== 热水费信息 ====================
    /**
     * 热水用量(m³)
     */
    private BigDecimal hotWaterUsage;

    /**
     * 热水费金额
     */
    private BigDecimal hotWaterAmount;

    // ==================== 燃气费信息 ====================
    /**
     * 燃气用量(m³)
     */
    private BigDecimal gasUsage;

    /**
     * 燃气费金额
     */
    private BigDecimal gasAmount;

    // ==================== 费用汇总 ====================
    /**
     * 总费用
     */
    private BigDecimal totalAmount;

    /**
     * 已缴费用
     */
    private BigDecimal paidAmount;

    /**
     * 未缴费用
     */
    private BigDecimal unpaidAmount;

    // ==================== 账单状态 ====================
    /**
     * 账单状态(1待缴费 2部分缴费 3已缴费 4已逾期)
     */
    private Integer billStatus;

    /**
     * 缴费状态(0未缴费 1已缴费)
     */
    private Integer paymentStatus;

    /**
     * 缴费截止日期
     */
    private Date dueDate;

    /**
     * 账单生成日期
     */
    private Date billDate;

    /**
     * 缴费时间
     */
    private Date paymentDate;

    // ==================== 其他信息 ====================
    /**
     * 备注
     */
    private String remark;

    /**
     * 记录状态(1正常 0删除)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人ID
     */
    private String creatorId;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 修改人ID
     */
    private String modifierId;

    // ==================== 业务方法 ====================

    /**
     * 获取账单状态描述
     */
    public String getBillStatusDesc() {
        if (billStatus == null) return "未知";
        switch (billStatus) {
            case 1: return "待缴费";
            case 2: return "部分缴费";
            case 3: return "已缴费";
            case 4: return "已逾期";
            default: return "未知";
        }
    }

    /**
     * 获取缴费状态描述
     */
    public String getPaymentStatusDesc() {
        if (paymentStatus == null) return "未知";
        switch (paymentStatus) {
            case 0: return "未缴费";
            case 1: return "已缴费";
            default: return "未知";
        }
    }

    /**
     * 判断是否逾期
     */
    public boolean isOverdue() {
        if (dueDate == null || paymentStatus == 1) return false;
        return new Date().after(dueDate);
    }

    /**
     * 获取逾期天数
     */
    public int getOverdueDays() {
        if (!isOverdue()) return 0;
        long diffTime = new Date().getTime() - dueDate.getTime();
        return (int) (diffTime / (24 * 60 * 60 * 1000));
    }

    /**
     * 获取缴费进度百分比
     */
    public double getPaymentProgress() {
        if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) == 0) return 0.0;
        if (paidAmount == null) return 0.0;
        return paidAmount.divide(totalAmount, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
    }

    /**
     * 判断是否为分时电表
     */
    public boolean isTimeSharingElectric() {
        return electricTipUsage != null || electricPeakUsage != null || 
               electricFlatUsage != null || electricValleyUsage != null;
    }

    /**
     * 获取费用明细描述
     */
    public String getBillDetailDesc() {
        StringBuilder sb = new StringBuilder();
        
        if (electricAmount != null && electricAmount.compareTo(BigDecimal.ZERO) > 0) {
            sb.append(String.format("电费: ￥%.2f", electricAmount));
        }
        
        if (waterAmount != null && waterAmount.compareTo(BigDecimal.ZERO) > 0) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(String.format("水费: ￥%.2f", waterAmount));
        }
        
        if (hotWaterAmount != null && hotWaterAmount.compareTo(BigDecimal.ZERO) > 0) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(String.format("热水费: ￥%.2f", hotWaterAmount));
        }
        
        if (gasAmount != null && gasAmount.compareTo(BigDecimal.ZERO) > 0) {
            if (sb.length() > 0) sb.append(", ");
            sb.append(String.format("燃气费: ￥%.2f", gasAmount));
        }
        
        return sb.toString();
    }
}
