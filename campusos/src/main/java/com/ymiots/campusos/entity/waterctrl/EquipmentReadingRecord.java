package com.ymiots.campusos.entity.waterctrl;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备读数记录实体
 * tb_equipment_reading_records
 * <AUTHOR>
 * @date 2025-06-28
 */
@Data
@Accessors(chain = true)
public class EquipmentReadingRecord {

    /**
     * 主键ID
     */
    private String uid;

    /**
     * 设备编号
     */
    private String equipmentCode;

    /**
     * 设备类型(1电表 2水表 4热水表 221气表)
     */
    private Integer equipmentType;

    /**
     * 读数日期
     */
    private Date readingDate;

    /**
     * 读数时间
     */
    private Date readingTime;

    /**
     * 当前读数
     */
    private BigDecimal currentReading;

    /**
     * 上次读数
     */
    private BigDecimal previousReading;

    /**
     * 用量(当前读数-上次读数)
     */
    private BigDecimal usageAmount;

    /**
     * 当前余额
     */
    private BigDecimal currentBalance;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 尖时读数(分时表)
     */
    private BigDecimal tipReading;

    /**
     * 峰时读数(分时表)
     */
    private BigDecimal peakReading;

    /**
     * 平时读数(分时表)
     */
    private BigDecimal flatReading;

    /**
     * 谷时读数(分时表)
     */
    private BigDecimal valleyReading;

    /**
     * 上次尖时读数(分时表)
     */
    private BigDecimal previousTipReading;

    /**
     * 上次峰时读数(分时表)
     */
    private BigDecimal previousPeakReading;

    /**
     * 上次平时读数(分时表)
     */
    private BigDecimal previousFlatReading;

    /**
     * 上次谷时读数(分时表)
     */
    private BigDecimal previousValleyReading;

    /**
     * 尖时用量
     */
    private BigDecimal tipUsage;

    /**
     * 峰时用量
     */
    private BigDecimal peakUsage;

    /**
     * 平时用量
     */
    private BigDecimal flatUsage;

    /**
     * 谷时用量
     */
    private BigDecimal valleyUsage;

    /**
     * 上次读表时间
     */
    private Date previousReadingTime;

    /**
     * 尖时电价
     */
    private BigDecimal tipPrice;

    /**
     * 峰时电价
     */
    private BigDecimal peakPrice;

    /**
     * 平时电价
     */
    private BigDecimal flatPrice;

    /**
     * 谷时电价
     */
    private BigDecimal valleyPrice;

    /**
     * 尖时费用
     */
    private BigDecimal tipCost;

    /**
     * 峰时费用
     */
    private BigDecimal peakCost;

    /**
     * 平时费用
     */
    private BigDecimal flatCost;

    /**
     * 谷时费用
     */
    private BigDecimal valleyCost;

    /**
     * 分时总费用
     */
    private BigDecimal totalTimeSharingCost;

    /**
     * 是否为分时表(0否 1是)
     */
    private Integer isTimeSharing;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 状态(1正常 0删除)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人ID
     */
    private String creatorId;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 修改人ID
     */
    private String modifierId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 获取设备类型描述
     */
    public String getEquipmentTypeDesc() {
        if (equipmentType == null) return "未知";
        switch (equipmentType) {
            case 1: return "电表";
            case 2: return "水表";
            case 4: return "热水表";
            case 221: return "气表";
            default: return "未知";
        }
    }

    /**
     * 判断是否为分时表
     */
    public boolean isTimeSharingMeter() {
        return tipReading != null || peakReading != null || 
               flatReading != null || valleyReading != null;
    }
}
