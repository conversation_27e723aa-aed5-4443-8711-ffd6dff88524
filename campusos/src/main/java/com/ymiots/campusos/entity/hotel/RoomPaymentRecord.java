package com.ymiots.campusos.entity.hotel;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 房间缴费记录实体类
 * 对应 tb_room_payment_record 表
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@Accessors(chain = true)
public class RoomPaymentRecord {

    /**
     * 主键ID
     */
    private String uid;

    /**
     * 缴费单号
     */
    private String paymentNo;

    /**
     * 账单ID
     */
    private String billId;

    /**
     * 房间编号
     */
    private String roomCode;

    /**
     * 账单月份
     */
    private String billMonth;

    // ==================== 缴费信息 ====================
    /**
     * 缴费金额
     */
    private BigDecimal paymentAmount;

    /**
     * 缴费方式(现金/银行卡/微信/支付宝/转账)
     */
    private String paymentMethod;

    /**
     * 缴费时间
     */
    private Date paymentDate;

    /**
     * 缴费人
     */
    private String paymentPerson;

    /**
     * 缴费人电话
     */
    private String paymentPhone;

    // ==================== 收费信息 ====================
    /**
     * 收费员ID
     */
    private String cashierId;

    /**
     * 收费员姓名
     */
    private String cashierName;

    /**
     * 收据号
     */
    private String receiptNo;

    // ==================== 第三方支付信息 ====================
    /**
     * 第三方交易号
     */
    private String transactionId;

    /**
     * 支付渠道
     */
    private String paymentChannel;

    /**
     * 支付状态(1成功 2失败 3退款)
     */
    private Integer paymentStatus;

    // ==================== 其他信息 ====================
    /**
     * 备注
     */
    private String remark;

    /**
     * 记录状态(1正常 0删除)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人ID
     */
    private String creatorId;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 修改人ID
     */
    private String modifierId;

    // ==================== 业务方法 ====================

    /**
     * 获取缴费方式描述
     */
    public String getPaymentMethodDesc() {
        if (paymentMethod == null) return "未知";
        switch (paymentMethod.toLowerCase()) {
            case "cash": return "现金";
            case "card": return "银行卡";
            case "wechat": return "微信支付";
            case "alipay": return "支付宝";
            case "transfer": return "银行转账";
            case "online": return "在线支付";
            default: return paymentMethod;
        }
    }

    /**
     * 获取支付状态描述
     */
    public String getPaymentStatusDesc() {
        if (paymentStatus == null) return "未知";
        switch (paymentStatus) {
            case 1: return "支付成功";
            case 2: return "支付失败";
            case 3: return "已退款";
            default: return "未知";
        }
    }

    /**
     * 判断是否为在线支付
     */
    public boolean isOnlinePayment() {
        if (paymentMethod == null) return false;
        String method = paymentMethod.toLowerCase();
        return method.equals("wechat") || method.equals("alipay") || 
               method.equals("online") || method.equals("card");
    }

    /**
     * 判断是否需要收据
     */
    public boolean needsReceipt() {
        return paymentStatus == 1 && (receiptNo == null || receiptNo.trim().isEmpty());
    }

    /**
     * 获取缴费摘要
     */
    public String getPaymentSummary() {
        return String.format("%s - ￥%.2f (%s)", 
            billMonth, paymentAmount, getPaymentMethodDesc());
    }

    /**
     * 判断是否可以退款
     */
    public boolean canRefund() {
        return paymentStatus == 1 && 
               (paymentMethod.equals("wechat") || paymentMethod.equals("alipay") || 
                paymentMethod.equals("online"));
    }

    /**
     * 生成缴费单号
     */
    public static String generatePaymentNo() {
        return "PAY" + System.currentTimeMillis();
    }

    /**
     * 生成收据号
     */
    public static String generateReceiptNo() {
        return "REC" + System.currentTimeMillis();
    }
}
