package com.ymiots.campusos.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

/**
 * 分时电表详细账单Excel导出数据实体
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@ContentRowHeight(20)
@HeadRowHeight(25)
public class TimeSharingExcelData {

    @ExcelProperty(value = "设备编号", index = 0)
    @ColumnWidth(15)
    private String equipmentCode;

    @ExcelProperty(value = "区域编码", index = 1)
    @ColumnWidth(12)
    private String areaCode;

    @ExcelProperty(value = "读数日期", index = 2)
    @ColumnWidth(12)
    private String readingDate;

    // 尖时数据
    @ExcelProperty(value = "尖时读数", index = 3)
    @ColumnWidth(12)
    private Double tipReading;

    @ExcelProperty(value = "尖时上次", index = 4)
    @ColumnWidth(12)
    private Double previousTipReading;

    @ExcelProperty(value = "尖时用量", index = 5)
    @ColumnWidth(12)
    private Double tipUsage;

    @ExcelProperty(value = "尖时单价", index = 6)
    @ColumnWidth(12)
    private Double tipPrice;

    @ExcelProperty(value = "尖时费用", index = 7)
    @ColumnWidth(12)
    private Double tipCost;

    // 峰时数据
    @ExcelProperty(value = "峰时读数", index = 8)
    @ColumnWidth(12)
    private Double peakReading;

    @ExcelProperty(value = "峰时上次", index = 9)
    @ColumnWidth(12)
    private Double previousPeakReading;

    @ExcelProperty(value = "峰时用量", index = 10)
    @ColumnWidth(12)
    private Double peakUsage;

    @ExcelProperty(value = "峰时单价", index = 11)
    @ColumnWidth(12)
    private Double peakPrice;

    @ExcelProperty(value = "峰时费用", index = 12)
    @ColumnWidth(12)
    private Double peakCost;

    // 平时数据
    @ExcelProperty(value = "平时读数", index = 13)
    @ColumnWidth(12)
    private Double flatReading;

    @ExcelProperty(value = "平时上次", index = 14)
    @ColumnWidth(12)
    private Double previousFlatReading;

    @ExcelProperty(value = "平时用量", index = 15)
    @ColumnWidth(12)
    private Double flatUsage;

    @ExcelProperty(value = "平时单价", index = 16)
    @ColumnWidth(12)
    private Double flatPrice;

    @ExcelProperty(value = "平时费用", index = 17)
    @ColumnWidth(12)
    private Double flatCost;

    // 谷时数据
    @ExcelProperty(value = "谷时读数", index = 18)
    @ColumnWidth(12)
    private Double valleyReading;

    @ExcelProperty(value = "谷时上次", index = 19)
    @ColumnWidth(12)
    private Double previousValleyReading;

    @ExcelProperty(value = "谷时用量", index = 20)
    @ColumnWidth(12)
    private Double valleyUsage;

    @ExcelProperty(value = "谷时单价", index = 21)
    @ColumnWidth(12)
    private Double valleyPrice;

    @ExcelProperty(value = "谷时费用", index = 22)
    @ColumnWidth(12)
    private Double valleyCost;

    // 总计
    @ExcelProperty(value = "总费用", index = 23)
    @ColumnWidth(12)
    private Double totalCost;

    @ExcelProperty(value = "余额", index = 24)
    @ColumnWidth(12)
    private Double currentBalance;

    // 构造方法
    public TimeSharingExcelData() {}

    // Getter和Setter方法
    public String getEquipmentCode() {
        return equipmentCode;
    }

    public void setEquipmentCode(String equipmentCode) {
        this.equipmentCode = equipmentCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getReadingDate() {
        return readingDate;
    }

    public void setReadingDate(String readingDate) {
        this.readingDate = readingDate;
    }

    public Double getTipReading() {
        return tipReading;
    }

    public void setTipReading(Double tipReading) {
        this.tipReading = tipReading;
    }

    public Double getPreviousTipReading() {
        return previousTipReading;
    }

    public void setPreviousTipReading(Double previousTipReading) {
        this.previousTipReading = previousTipReading;
    }

    public Double getTipUsage() {
        return tipUsage;
    }

    public void setTipUsage(Double tipUsage) {
        this.tipUsage = tipUsage;
    }

    public Double getTipPrice() {
        return tipPrice;
    }

    public void setTipPrice(Double tipPrice) {
        this.tipPrice = tipPrice;
    }

    public Double getTipCost() {
        return tipCost;
    }

    public void setTipCost(Double tipCost) {
        this.tipCost = tipCost;
    }

    public Double getPeakReading() {
        return peakReading;
    }

    public void setPeakReading(Double peakReading) {
        this.peakReading = peakReading;
    }

    public Double getPreviousPeakReading() {
        return previousPeakReading;
    }

    public void setPreviousPeakReading(Double previousPeakReading) {
        this.previousPeakReading = previousPeakReading;
    }

    public Double getPeakUsage() {
        return peakUsage;
    }

    public void setPeakUsage(Double peakUsage) {
        this.peakUsage = peakUsage;
    }

    public Double getPeakPrice() {
        return peakPrice;
    }

    public void setPeakPrice(Double peakPrice) {
        this.peakPrice = peakPrice;
    }

    public Double getPeakCost() {
        return peakCost;
    }

    public void setPeakCost(Double peakCost) {
        this.peakCost = peakCost;
    }

    public Double getFlatReading() {
        return flatReading;
    }

    public void setFlatReading(Double flatReading) {
        this.flatReading = flatReading;
    }

    public Double getPreviousFlatReading() {
        return previousFlatReading;
    }

    public void setPreviousFlatReading(Double previousFlatReading) {
        this.previousFlatReading = previousFlatReading;
    }

    public Double getFlatUsage() {
        return flatUsage;
    }

    public void setFlatUsage(Double flatUsage) {
        this.flatUsage = flatUsage;
    }

    public Double getFlatPrice() {
        return flatPrice;
    }

    public void setFlatPrice(Double flatPrice) {
        this.flatPrice = flatPrice;
    }

    public Double getFlatCost() {
        return flatCost;
    }

    public void setFlatCost(Double flatCost) {
        this.flatCost = flatCost;
    }

    public Double getValleyReading() {
        return valleyReading;
    }

    public void setValleyReading(Double valleyReading) {
        this.valleyReading = valleyReading;
    }

    public Double getPreviousValleyReading() {
        return previousValleyReading;
    }

    public void setPreviousValleyReading(Double previousValleyReading) {
        this.previousValleyReading = previousValleyReading;
    }

    public Double getValleyUsage() {
        return valleyUsage;
    }

    public void setValleyUsage(Double valleyUsage) {
        this.valleyUsage = valleyUsage;
    }

    public Double getValleyPrice() {
        return valleyPrice;
    }

    public void setValleyPrice(Double valleyPrice) {
        this.valleyPrice = valleyPrice;
    }

    public Double getValleyCost() {
        return valleyCost;
    }

    public void setValleyCost(Double valleyCost) {
        this.valleyCost = valleyCost;
    }

    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    public Double getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(Double currentBalance) {
        this.currentBalance = currentBalance;
    }

    @Override
    public String toString() {
        return "TimeSharingExcelData{" +
                "equipmentCode='" + equipmentCode + '\'' +
                ", areaCode='" + areaCode + '\'' +
                ", readingDate='" + readingDate + '\'' +
                ", tipUsage=" + tipUsage +
                ", peakUsage=" + peakUsage +
                ", flatUsage=" + flatUsage +
                ", valleyUsage=" + valleyUsage +
                ", totalCost=" + totalCost +
                ", currentBalance=" + currentBalance +
                '}';
    }
}
