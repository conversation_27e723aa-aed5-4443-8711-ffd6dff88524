package com.ymiots.campusos.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;

/**
 * 水电费账单Excel导出数据实体
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@ContentRowHeight(20)
@HeadRowHeight(25)
public class WECostExcelData {

    @ExcelProperty(value = "设备编号", index = 0)
    @ColumnWidth(15)
    private String equipmentCode;

    @ExcelProperty(value = "设备类型", index = 1)
    @ColumnWidth(10)
    private String equipmentType;

    @ExcelProperty(value = "区域编码", index = 2)
    @ColumnWidth(12)
    private String areaCode;

    @ExcelProperty(value = "读数日期", index = 3)
    @ColumnWidth(12)
    private String readingDate;

    @ExcelProperty(value = "当前读数", index = 4)
    @ColumnWidth(12)
    private Double currentReading;

    @ExcelProperty(value = "上次读数", index = 5)
    @ColumnWidth(12)
    private Double previousReading;

    @ExcelProperty(value = "用量", index = 6)
    @ColumnWidth(10)
    private Double usageAmount;

    @ExcelProperty(value = "单价(元)", index = 7)
    @ColumnWidth(10)
    private Double unitPrice;

    @ExcelProperty(value = "费用(元)", index = 8)
    @ColumnWidth(12)
    private Double totalCost;

    @ExcelProperty(value = "余额(元)", index = 9)
    @ColumnWidth(12)
    private Double currentBalance;

    @ExcelProperty(value = "分时信息", index = 10)
    @ColumnWidth(20)
    private String timeSharingInfo;

    // 构造方法
    public WECostExcelData() {}

    public WECostExcelData(String equipmentCode, String equipmentType, String areaCode, String readingDate,
                          Double currentReading, Double previousReading, Double usageAmount, Double unitPrice,
                          Double totalCost, Double currentBalance, String timeSharingInfo) {
        this.equipmentCode = equipmentCode;
        this.equipmentType = equipmentType;
        this.areaCode = areaCode;
        this.readingDate = readingDate;
        this.currentReading = currentReading;
        this.previousReading = previousReading;
        this.usageAmount = usageAmount;
        this.unitPrice = unitPrice;
        this.totalCost = totalCost;
        this.currentBalance = currentBalance;
        this.timeSharingInfo = timeSharingInfo;
    }

    // Getter和Setter方法
    public String getEquipmentCode() {
        return equipmentCode;
    }

    public void setEquipmentCode(String equipmentCode) {
        this.equipmentCode = equipmentCode;
    }

    public String getEquipmentType() {
        return equipmentType;
    }

    public void setEquipmentType(String equipmentType) {
        this.equipmentType = equipmentType;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getReadingDate() {
        return readingDate;
    }

    public void setReadingDate(String readingDate) {
        this.readingDate = readingDate;
    }

    public Double getCurrentReading() {
        return currentReading;
    }

    public void setCurrentReading(Double currentReading) {
        this.currentReading = currentReading;
    }

    public Double getPreviousReading() {
        return previousReading;
    }

    public void setPreviousReading(Double previousReading) {
        this.previousReading = previousReading;
    }

    public Double getUsageAmount() {
        return usageAmount;
    }

    public void setUsageAmount(Double usageAmount) {
        this.usageAmount = usageAmount;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    public Double getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(Double currentBalance) {
        this.currentBalance = currentBalance;
    }

    public String getTimeSharingInfo() {
        return timeSharingInfo;
    }

    public void setTimeSharingInfo(String timeSharingInfo) {
        this.timeSharingInfo = timeSharingInfo;
    }

    @Override
    public String toString() {
        return "WECostExcelData{" +
                "equipmentCode='" + equipmentCode + '\'' +
                ", equipmentType='" + equipmentType + '\'' +
                ", areaCode='" + areaCode + '\'' +
                ", readingDate='" + readingDate + '\'' +
                ", currentReading=" + currentReading +
                ", previousReading=" + previousReading +
                ", usageAmount=" + usageAmount +
                ", unitPrice=" + unitPrice +
                ", totalCost=" + totalCost +
                ", currentBalance=" + currentBalance +
                ", timeSharingInfo='" + timeSharingInfo + '\'' +
                '}';
    }
}
