package com.ymiots.campusos.util;

import com.ymiots.framework.common.MD5;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;

public class SignUtils {

    /**
     * 生成签名
     */
    public static String generateSign(Map<String, String> requestData, String appSecret) throws Exception {
        StringBuilder str = new StringBuilder();
        Map<String, String> sortedParams = new TreeMap<>(requestData);
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            // 跳过空参数和值为 "sign"
            if (entry.getKey().equals("sign")   || entry.getValue().isEmpty()) {
                continue;
            }

            // 参数值进行URL编码并替换空格为 "+"
            String encodedValue = URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.toString()).replace("%20", "+");
            str.append(entry.getKey()).append("=").append(encodedValue).append("&");
            requestData.put(entry.getKey(),encodedValue);
        }
        // 删除最后一个 "&"
        if (str.length() > 0 && str.charAt(str.length() - 1) == '&') {
            str.deleteCharAt(str.length() - 1);
        }
        // 拼接 app_secret
        str.append(appSecret);

        System.out.println("需要签名的字符串：" + str.toString());

        // MD5加密
        return MD5Utils.MD5Encrypt(str.toString());
    }

    /**
     * md5加密
     * @param input
     * @return
     * @throws Exception
     */
    private static String md5(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));

        // 转换为16进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : messageDigest) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
