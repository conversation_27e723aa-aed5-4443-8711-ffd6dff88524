package com.ymiots.campusos.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.Consts;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.io.Serializable;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @date 2024-04-19 9:58:42
 */
@Component
public class HttpRequestUtils implements Serializable {

    private static final long serialVersionUID = -7524694638614823508L;
    private static final Logger logger = Logger.getLogger(HttpRequestUtils.class.getName());
    private static final int TIMEOUT = 5000; // 5 seconds timeout
    private static final int MAX_RETRIES = 3;
    private static final int INITIAL_BACKOFF_DELAY_MS = 1000; // 初始退避时间为 1000ms

    /**
     * 发送 GET 请求
     *
     * @param url          请求地址
     * @param headerParams 请求头参数
     * @param queryParams  查询参数
     * @param clazz        返回对象类型
     * @param <T>          泛型
     * @return 请求结果对应的对象
     * @throws IOException        发送请求异常
     * @throws URISyntaxException URL 语法异常
     */
    public <T> T doGetRequest(String url, Map<String, String> headerParams, Map<String, String> queryParams, Class<T> clazz) throws IOException, URISyntaxException {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        StringBuilder urlWithParams = new StringBuilder(url);

        if (queryParams != null && !queryParams.isEmpty()) {
            urlWithParams.append("?");
            for (Map.Entry<String, String> param : queryParams.entrySet()) {
                urlWithParams.append(param.getKey())
                        .append("=")
                        .append(param.getValue())
                        .append("&");
            }
            urlWithParams.setLength(urlWithParams.length() - 1); // Remove the trailing "&"
        }

        HttpGet httpGet = new HttpGet(new URI(urlWithParams.toString()));
        T result = null;

        // Set headers
        if (headerParams != null && !headerParams.isEmpty()) {
            for (Map.Entry<String, String> param : headerParams.entrySet()) {
                httpGet.addHeader(param.getKey(), param.getValue());
            }
        }

        // Execute request and handle response
        try (CloseableHttpResponse response = httpclient.execute(httpGet)) {
            int code = response.getStatusLine().getStatusCode();
            if (HttpStatus.SC_OK == code) {
                String responseStr = EntityUtils.toString(response.getEntity(), "UTF-8");
                try {
                    result = JSONObject.parseObject(responseStr, clazz);
                } catch (Exception e) {
                    result = (T) responseStr;
                }
            }
        }

        return result;
    }

    /**
     * 发送 GET 请求
     *
     * @param url          请求地址
     * @param headerParams 请求头参数
     * @param queryParams  查询参数
     * @param clazz        返回对象类型
     * @param <T>          泛型
     * @return 请求结果对应的对象
     * @throws IOException        发送请求异常
     * @throws URISyntaxException URL 语法异常
     */
    public <T> T doJJDeptGetRequest(String url, Map<String, String> headerParams, Map<String, String> queryParams, Class<T> clazz) throws IOException, URISyntaxException {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        StringBuilder urlWithParams = new StringBuilder(url);

        if (queryParams != null && !queryParams.isEmpty()) {
            urlWithParams.append("?");
            for (Map.Entry<String, String> param : queryParams.entrySet()) {
                urlWithParams.append(param.getKey())
                        .append("=")
                        .append(param.getValue())
                        .append("&");
            }
            urlWithParams.setLength(urlWithParams.length() - 1); // Remove the trailing "&"
        }

        HttpGet httpGet = new HttpGet(new URI(urlWithParams.toString()));
        T result = null;

        // Set headers
        if (headerParams != null && !headerParams.isEmpty()) {
            for (Map.Entry<String, String> param : headerParams.entrySet()) {
                httpGet.addHeader(param.getKey(), param.getValue());
            }
        }

        // Execute request and handle response
        try (CloseableHttpResponse response = httpclient.execute(httpGet)) {
            int code = response.getStatusLine().getStatusCode();
            if (HttpStatus.SC_OK == code) {
                String responseStr = EntityUtils.toString(response.getEntity(), "UTF-8");
                try {
                    responseStr=responseStr.substring(1,responseStr.length()-1);
                    result = JSONObject.parseObject(responseStr, clazz);
                } catch (Exception e) {
                    result = (T) responseStr;
                }
            }
        }

        return result;
    }

    /**
     * 发送 GET 请求
     *
     * @param url          请求地址
     * @param headerParams 请求头参数
     * @param queryParams  查询参数
     * @param clazz        返回对象类型
     * @param <T>          泛型
     * @return 请求结果对应的对象
     * @throws IOException        发送请求异常
     * @throws URISyntaxException URL 语法异常
     */
    public String doJJPersonGetRequest(String url, Map<String, String> headerParams, Map<String, String> queryParams) throws IOException, URISyntaxException {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        StringBuilder urlWithParams = new StringBuilder(url);

        if (queryParams != null && !queryParams.isEmpty()) {
            urlWithParams.append("?");
            for (Map.Entry<String, String> param : queryParams.entrySet()) {
                urlWithParams.append(param.getKey())
                        .append("=")
                        .append(param.getValue())
                        .append("&");
            }
            urlWithParams.setLength(urlWithParams.length() - 1); // Remove the trailing "&"
        }

        HttpGet httpGet = new HttpGet(new URI(urlWithParams.toString()));
        T result = null;

        // Set headers
        if (headerParams != null && !headerParams.isEmpty()) {
            for (Map.Entry<String, String> param : headerParams.entrySet()) {
                httpGet.addHeader(param.getKey(), param.getValue());
            }
        }
        String responseStr ="";

        // Execute request and handle response
        try (CloseableHttpResponse response = httpclient.execute(httpGet)) {
            int code = response.getStatusLine().getStatusCode();
            if (HttpStatus.SC_OK == code) {
                 responseStr = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        }

        return responseStr;
    }

    /**
     * post请求
     * application/x-www-form-urlencoded
     *
     * @param url          请求地址
     * @param headerParams 请求头参数
     * @param params       请求参数
     * @param clazz        返回对象类型
     */
    public <T> T doPostRequest(String url, Map<String, String> headerParams, List<NameValuePair> params, Class<T> clazz)
            throws IOException {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        T result = null;
        if (null != params) {
            httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
        }
        HttpResponse response = httpclient.execute(httpPost);
        int code = response.getStatusLine().getStatusCode();
        if (code == HttpStatus.SC_OK) {
            String responseStr = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
            result = JSONObject.parseObject(responseStr, clazz);
        }
        return result;
    }

    /**
     * 发送 POST 请求
     *
     * @param url          请求地址
     * @param headerParams 请求头参数
     * @param requestBody  请求参数（JSON 字符串）
     * @param clazz        返回对象类型
     * @param <T>          泛型
     * @return 请求结果对应的对象
     * @throws IOException 发送请求异常
     */
    public <T> T doPostJsonRequest(String url, Map<String, String> headerParams, String requestBody, Class<T> clazz)
            throws IOException {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        T result = null;

        // 设置请求头
        if (headerParams != null && !headerParams.isEmpty()) {
            for (Map.Entry<String, String> param : headerParams.entrySet()) {
                httpPost.addHeader(param.getKey(), param.getValue());
            }
        }

        // 设置请求体
        if (requestBody != null) {
            StringEntity stringEntity = new StringEntity(requestBody, "UTF-8");
            stringEntity.setContentType("application/json"); // 设置 Content-Type
            httpPost.setEntity(stringEntity);
        }

        AtomicInteger retryCount = new AtomicInteger(0);

        try (CloseableHttpResponse response = httpclient.execute(httpPost)) {
            int code = response.getStatusLine().getStatusCode();
            if (code == HttpStatus.SC_OK) {
                String responseStr = EntityUtils.toString(response.getEntity(), "UTF-8");
                logger.info(responseStr);
                result = JSONObject.parseObject(responseStr, clazz);
            } else {
                handleRetry(retryCount.get(), code);
            }
        } catch (IOException e) {
            handleRetry(retryCount.get(), e);
        }

        return result;
    }

    /**
     * 发送 POST 请求
     *
     * @param url          请求地址
     * @param headerParams 请求头参数
     * @param requestBody  请求参数（JSON 字符串）
     * @param clazz        返回对象类型
     * @return 请求结果对应的对象
     * throws IOException 发送请求异常
     */
    public String doPostJsonRequestQY(String url, Map<String, String> headerParams, String requestBody)
            throws IOException {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        String responseStr = null;

        // 设置请求头
        if (headerParams != null && !headerParams.isEmpty()) {
            for (Map.Entry<String, String> param : headerParams.entrySet()) {
                httpPost.addHeader(param.getKey(), param.getValue());
            }
        }

        // 设置请求体
        if (requestBody != null) {
            StringEntity stringEntity = new StringEntity(requestBody, "UTF-8");
            stringEntity.setContentType("application/json"); // 设置 Content-Type
            httpPost.setEntity(stringEntity);
        }

        AtomicInteger retryCount = new AtomicInteger(0);

        try (CloseableHttpResponse response = httpclient.execute(httpPost)) {
            int code = response.getStatusLine().getStatusCode();
            if (code == HttpStatus.SC_OK) {
                 responseStr = EntityUtils.toString(response.getEntity(), "UTF-8");
                logger.info(responseStr);
            } else {
                logger.info(responseStr);
                handleRetry(retryCount.get(), code);
            }
        } catch (IOException e) {
            handleRetry(retryCount.get(), e);
        }

        return responseStr;
    }

    private void handleRetry(int currentRetryCount, int statusCode) throws IOException {
        int nextRetryCount = currentRetryCount + 1;
        if (nextRetryCount >= MAX_RETRIES) {
            throw new IOException("Request failed after " + MAX_RETRIES + " retries, status code: " + statusCode);
        }
        int backoffDelay = INITIAL_BACKOFF_DELAY_MS * (1 << currentRetryCount); // 指数退避
        try {
            Thread.sleep(backoffDelay); // 等待退避时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 处理中断
        }
    }

    private void handleRetry(int currentRetryCount, IOException e) throws IOException {
        int nextRetryCount = currentRetryCount + 1;
        if (nextRetryCount >= MAX_RETRIES) {
            throw new IOException("Request failed after " + MAX_RETRIES + " retries", e);
        }
        int backoffDelay = INITIAL_BACKOFF_DELAY_MS * (1 << currentRetryCount); // 指数退避
        try {
            Thread.sleep(backoffDelay); // 等待退避时间
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt(); // 处理中断
        }
    }

    /**
     * post请求FormData参数
     *
     * @param url    请求地址
     * @param params 请求参数
     * @param clazz  返回对象类型
     */
    public <T> T doPostFormRequest(String url, Map<String, String> params, Class<T> clazz)
            throws IOException {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        T result = null;
        if (null != params && !params.isEmpty()) {
            List<NameValuePair> paraList = new ArrayList<>();
            for (Map.Entry<String, String> param : params.entrySet()) {
                paraList.add(new BasicNameValuePair(param.getKey(), param.getValue()));
            }
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(paraList, StandardCharsets.UTF_8);
            httpPost.setEntity(urlEncodedFormEntity);
        }
        HttpResponse response = httpclient.execute(httpPost);
        int code = response.getStatusLine().getStatusCode();
        if (code == HttpStatus.SC_OK) {
            String responseStr = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
            result = JSONObject.parseObject(responseStr, clazz);
        }
        return result;
    }

    /**
     * put请求
     *
     * @param url          请求地址
     * @param headerParams 请求头参数
     * @param requestBody  请求参数
     * @param clazz        返回对象类型
     */
    public <T> T doPutRequest(String url, Map<String, String> headerParams, String requestBody, Class<T> clazz)
            throws IOException {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPut httpPut = new HttpPut(url);
        T result = null;
        if (null != headerParams && !headerParams.isEmpty()) {
            for (Map.Entry<String, String> param : headerParams.entrySet()) {
                httpPut.addHeader(param.getKey(), param.getValue());
            }
        }
        if (null != requestBody) {
            StringEntity stringEntity = new StringEntity(requestBody, ContentType.APPLICATION_JSON);
            httpPut.setEntity(stringEntity);
        }
        HttpResponse response = httpclient.execute(httpPut);
        int code = response.getStatusLine().getStatusCode();
        if (code == HttpStatus.SC_OK) {
            String responseStr = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
            result = JSONObject.parseObject(responseStr, clazz);
        }
        return result;
    }


    /**
     * post请求
     *
     * @param url          请求地址
     * @param headerParams 请求头参数
     * @param requestBody  请求参数
     * @param clazz        返回对象类型
     */
    public <T> T doPostRequestIgnoreSSL(String url, Map<String, String> headerParams, String requestBody, Class<T> clazz)
            throws IOException, KeyManagementException, NoSuchAlgorithmException {

        //忽略证书
        X509TrustManager x509TrustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] arg0, String arg1) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] arg0, String arg1) {
                // 不验证
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        };

        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, new TrustManager[]{x509TrustManager}, new SecureRandom());
        SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext);
        CloseableHttpClient httpclient = HttpClientBuilder.create().setSSLSocketFactory(sslSocketFactory).build();

        HttpPost httpPost = new HttpPost(url);
        T result = null;
        if (null != headerParams && !headerParams.isEmpty()) {
            for (Map.Entry<String, String> param : headerParams.entrySet()) {
                httpPost.addHeader(param.getKey(), param.getValue());
            }
        }
        if (null != requestBody) {
            StringEntity stringEntity = new StringEntity(requestBody, ContentType.APPLICATION_JSON);
            httpPost.setEntity(stringEntity);
        }
        HttpResponse response = httpclient.execute(httpPost);
        int code = response.getStatusLine().getStatusCode();
        if (code == HttpStatus.SC_OK) {
            String responseStr = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
            result = JSONObject.parseObject(responseStr, clazz);
        }
        return result;
    }

    public <T> T doPostRequestIgnoreSSL2(String url, Map<String, String> headerParams, String requestBody, Class<T> clazz) throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException, IOException, KeyStoreException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;
        //忽略证书
        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());

        CloseableHttpClient httpclient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpPost httpPost = new HttpPost(url);
        T result = null;
        if (null != headerParams && !headerParams.isEmpty()) {
            for (Map.Entry<String, String> param : headerParams.entrySet()) {
                httpPost.addHeader(param.getKey(), param.getValue());
            }
        }
        if (null != requestBody) {
            StringEntity stringEntity = new StringEntity(requestBody, ContentType.APPLICATION_JSON);
            httpPost.setEntity(stringEntity);
        }
        HttpResponse response = httpclient.execute(httpPost);
        int code = response.getStatusLine().getStatusCode();
        if (code == HttpStatus.SC_OK) {
            String responseStr = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
            result = JSONObject.parseObject(responseStr, clazz);
        }
        return result;
    }

//    /**
//     * @param url
//     * @param multipartFiles 文件列表支持多个
//     * @param fileFiledName  目标接口接收文件的字段名
//     * @param params         其他参数
//     * @param headerParams   请求头参数
//     * @param timeout        超时时间(秒)
//     * @param clazz
//     * @return T
//     * @description: 文件传输
//     * @date: 2024/4/12 11:48
//     */
//    public <T> T doPostFileRequest(String url, List<MultipartFile> multipartFiles, String fileFiledName, Map<String, Object> params, Map<String, String> headerParams, int timeout, Class<T> clazz) throws IOException {
//        CloseableHttpClient httpClient = HttpClients.createDefault();
//        T result = null;
//        String responseStr = null;
//
//        HttpPost httpPost = new HttpPost(url);
//        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
//        builder.setCharset(java.nio.charset.Charset.forName("UTF-8"));
//        builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
//
//        String fileName = null;
//        MultipartFile multipartFile = null;
//        for (int i = 0; i < multipartFiles.size(); i++) {
//            multipartFile = multipartFiles.get(i);
//            fileName = multipartFile.getOriginalFilename();
//            //文件流
//            builder.addBinaryBody(fileFiledName, multipartFile.getInputStream(), ContentType.MULTIPART_FORM_DATA, fileName);
//        }
//
//        if (null != headerParams && !headerParams.isEmpty()) {
//            for (Map.Entry<String, String> param : headerParams.entrySet()) {
//                httpPost.addHeader(param.getKey(), param.getValue());
//            }
//        }
//
//        //解决中文乱码
//        if (null != params && !params.isEmpty()) {
//            ContentType contentType = ContentType.create(HTTP.PLAIN_TEXT_TYPE, HTTP.UTF_8);
//            for (Map.Entry<String, Object> entry : params.entrySet()) {
//                if (entry.getValue() == null) {
//                    continue;
//                }
//                //类似浏览器表单提交，对应input的name和value
//                builder.addTextBody(entry.getKey(), entry.getValue().toString(), contentType);
//            }
//        }
//
//        HttpEntity entity = builder.build();
//        httpPost.setEntity(entity);
//        HttpResponse response = httpClient.execute(httpPost);
//
//        //设置连接超时时间
//        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(timeout).setConnectionRequestTimeout(timeout).setSocketTimeout(timeout).build();
//        httpPost.setConfig(requestConfig);
//
//        int code = response.getStatusLine().getStatusCode();
//        if (code == HttpStatus.SC_OK) {
//            responseStr = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
//            result = JSONObject.parseObject(responseStr, clazz);
//        }
//        return result;
//    }　
}
