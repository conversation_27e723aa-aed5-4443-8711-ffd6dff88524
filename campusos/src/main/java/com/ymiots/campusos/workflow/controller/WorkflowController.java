package com.ymiots.campusos.workflow.controller;


import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.workflow.service.WorkflowService;
import com.ymiots.campusos.workflow.vo.AddFormTemplateVo;
import com.ymiots.framework.common.JsonData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2023/10/9 15:41:28
 */
@RestController
@RequestMapping("/Workflow/Template")
public class WorkflowController {

    @Autowired
    private WorkflowService workflowService;


    /**
     * 增加行为模版
     */
    @PostMapping("/addFormTemplate")
    public R<?> addFormTemplate(@RequestBody AddFormTemplateVo vo) {
        return workflowService.addFormTemplate(vo);
    }

    /**
     * 查询行为模版
     */
    @GetMapping("/getFormTemplate")
    public JsonData getFormTemplate(String key, int limit, int start) {
        return workflowService.getFormTemplate(key, limit, start);
    }

    /**
     * 删除行为模版
     */
    @PostMapping("/delFormTemplate")
    public R<?> delFormTemplate(String uid) {
        return workflowService.delFormTemplate(uid);
    }

    /**
     * 修改行为模版
     */
    @PostMapping("/updateFormTemplate")
    public R<?> updateFormTemplate(@RequestBody AddFormTemplateVo vo) {
        return workflowService.updateFormTemplate(vo);
    }
}
