package com.ymiots.campusos.dao.impl;

import com.ymiots.campusos.common.DBService;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dao.CardBalanceDao;
import com.ymiots.campusos.dto.card.CardTeachStudInfoDTO;
import com.ymiots.framework.common.JSONObjectPlus;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public class CardBalanceDaoImpl implements CardBalanceDao {

    @Autowired
    private DBService dbService;

    @Override
    public R<List<CardTeachStudInfoDTO>> selUserBalance(String orgCode,boolean viewChild,String key,int page,int size) {
        String sql = "select ct.name,ct.code,cc.cardno card,ct.uid,cc.uid cardId,cc.ismain isMain,cc.balance,cc.vicewallet viceWallet,cc.waterwallet waterWallet,cc.enddate endDt,cc.createdate createDt,cc.status,cc.des " +
                " from tb_card_teachstudinfo ct " +
                " left join tb_card_cardinfo cc on ct.uid = cc.infoid " +
                " where 1 = 1 and ";
        String where = " ct.status = 1 and cc.status = 1 ";
        if (!StringUtils.isBlank(orgCode)) {
            if (viewChild) {
                where += " and ct.orgcode like '" + orgCode + "%'";
            } else {
                where += " and ct.orgcode = '" + orgCode + "'";
            }
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (ct.name like '" + key + "%' or ct.code like '" + key + "%') ";
        }
        int count = dbService.getCount("tb_card_teachstudinfo ct left join tb_card_cardinfo cc on ct.uid = cc.infoid", where);
        if (page != 0 && size != 0) {
            where += " limit " + page + "," + size;
        }
        List<CardTeachStudInfoDTO> infoDTOS = dbService.queryList(sql+where, CardTeachStudInfoDTO.class);
        for (CardTeachStudInfoDTO infoDTO : infoDTOS) {
            infoDTO.setBalance(GetCardBalance(infoDTO.getCardId(), infoDTO.getBalance()));
            infoDTO.setViceWallet(GetViceWallet(infoDTO.getCardId(), infoDTO.getViceWallet()));
            infoDTO.setWaterWallet(GetWaterWallet(infoDTO.getCardId(), infoDTO.getWaterWallet()));
            infoDTO.setSum(infoDTO.getBalance().add(infoDTO.getViceWallet()).add(infoDTO.getWaterWallet()));
        }
        return R.okPage(infoDTOS,count);
    }


    private BigDecimal GetCardBalance(String cardId, BigDecimal balance) {
        String sql="SELECT sum(t.money) as result FROM ( " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=1 and status=1 and paywallet=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=2 and status=1 and paywallet=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=3 and status=1 and paywallet=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=6 and status=1 and paywallet=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=7 and status=1 and paywallet=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=8 and status=1 and paywallet=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all     " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='1' and status=1 and paywallet=1 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='3' and status=1 and paywallet=1 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='4' and status=1 and paywallet=1 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='5' and status=1 and paywallet=1 and cardid='"+cardId+"' " +
                ") t";
        JSONObjectPlus sumresult= dbService.QueryJSONObjectPlus(sql);
        if (sumresult != null) {
            BigDecimal result = sumresult.getBigDecimal("result");
            balance = balance.add(result);
        }
        return balance;
    }

    private BigDecimal GetViceWallet(String cardId, BigDecimal balance) {
        String sql="SELECT sum(t.money) as result FROM ( " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=1 and status=1 and paywallet=2 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=2 and status=1 and paywallet=2 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=3 and status=1 and paywallet=2 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=6 and status=1 and paywallet=2 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=7 and status=1 and paywallet=2 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=8 and status=1 and paywallet=2 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all     " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='1' and status=1 and paywallet=1 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='3' and status=1 and paywallet=1 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='4' and status=1 and paywallet=1 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='5' and status=1 and paywallet=1 and cardid='"+cardId+"' " +
                ") t";
        JSONObjectPlus sumresult= dbService.QueryJSONObjectPlus(sql);
        if (sumresult != null) {
            BigDecimal result = sumresult.getBigDecimal("result");
            balance = balance.add(result);
        }
        return balance;
    }

    private BigDecimal GetWaterWallet(String cardId, BigDecimal balance) {
        String sql="SELECT sum(t.money) as result FROM ( " +
                "     SELECT sum(money) as money FROM tb_waterctrl_transaction_detail WHERE tradetype=1 and status=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_waterctrl_transaction_detail WHERE tradetype=2 and status=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_waterctrl_transaction_detail WHERE tradetype=3 and status=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_waterctrl_transaction_detail WHERE tradetype=6 and status=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_waterctrl_transaction_detail WHERE tradetype=7 and status=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_waterctrl_transaction_detail WHERE tradetype=8 and status=1 and orderstatus=2 and cardid='"+cardId+"' " +
                "     union all     " +
                "     SELECT sum(money)*-1 as money FROM tb_waterctrl_payrecords WHERE  status=1  and cardid='"+cardId+"' "+
                ") t";
        JSONObjectPlus sumresult= dbService.QueryJSONObjectPlus(sql);
        if (sumresult != null) {
            BigDecimal result = sumresult.getBigDecimal("result");
            balance = balance.add(result);
        }
        return balance;
    }

}
