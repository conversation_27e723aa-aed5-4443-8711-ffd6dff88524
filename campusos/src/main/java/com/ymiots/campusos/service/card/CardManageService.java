package com.ymiots.campusos.service.card;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.entity.CardInfo;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.RedisMessagePublish;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysConfigService;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.campusos.service.alleyway.APOLNameListService;
import com.ymiots.campusos.service.alleyway.RosterDownloadService;
import com.ymiots.campusos.service.alleyway.YOLNameListService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.datafactory.CallbackTransaction;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;

@Repository
public class CardManageService extends BaseService {

    @Autowired
    UserRedis userredis;

    @Autowired
    SysLogsService syslogs;


	@Autowired
	SysConfigService sysconfig;

    @Autowired
    RedisMessagePublish redismsg;

    @Autowired
    RosterDownloadService rosterDownloadService;

    @Autowired
    APOLNameListService apolNameListService;

    @Autowired
    YOLNameListService yolNameListService;

    public CardInfo GetCardInfoByNo(String CardNo) {
        return GetCardInfoByNo(CardNo, "");
    }

    /**
     * 统一方法：根据卡号、序列号获取有效卡信息
     *
     * @param CardNo
     * @return status类型
     * none:未发卡
     * 1:有效
     * 2:挂失
     * 0:无效卡
     */
    public CardInfo GetCardInfoByNo(String CardNo, String CardId) {
        String sql = "SELECT uid as cardid,infoid,usedes,cardno,deposit,balance,vicewallet,waterwallet,timescount,cardsn,enddate,status,ismain FROM tb_card_cardinfo where ";
        if (!StringUtils.isBlank(CardId)) {
            sql += " uid='" + CardId + "' ";
        } else {
            sql += " cardno='" + CardNo + "' ";
        }
        sql += "  order by createdate desc ";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            return null;
        }
        String status = "";
        JSONObjectPlus card = new JSONObjectPlus();
        if (!StringUtils.isBlank(CardId)) {
            card = JSONObjectPlus.ToPlus(list.getJSONObject(0));
            status = card.getString("status");
        } else {
            //获取有效卡
            JSONArray clist = CollectionUtils.where(list, new CallBackFilter<JSONObject>() {
                @Override
                public Boolean filter(JSONObject model) {
                    return model.getIntValue("status") == 1;
                }
            });
            if (clist.size() == 1) {
                card = JSONObjectPlus.ToPlus(clist.getJSONObject(0));
                status = "1";
            } else {
                //获取挂失卡
                clist = CollectionUtils.where(list, new CallBackFilter<JSONObject>() {
                    @Override
                    public Boolean filter(JSONObject model) {
                        return model.getIntValue("status") == 2;
                    }
                });
                if (clist.size() == 1) {
                    card = JSONObjectPlus.ToPlus(clist.getJSONObject(0));
                    status = "2";
                } else {
                    //全部均为无效卡，取最后发的记录
                    card = JSONObjectPlus.ToPlus(clist.getJSONObject(0));
                    status = "0";
                }
            }
        }

        card.remove("status");
        card.put("status", status);

        String cardid = card.getString("cardid");
        BigDecimal balance = card.getBigDecimal("balance");
        card.remove("balance");
        card.put("balance", GetCardBalance(cardid, balance));

        BigDecimal vicewallet = card.getBigDecimal("vicewallet");
        card.remove("vicewallet");
        card.put("vicewallet", GetCardViceWallet(cardid, vicewallet));

        BigDecimal waterwallet = card.getBigDecimal("waterwallet");
        card.remove("waterwallet");
        card.put("waterwallet", GetCardViceWallet(cardid, waterwallet));

        int timescount = card.getIntValue("timescount");
        card.remove("timescount");
        card.put("timescount", GetCardTimesCount(cardid, timescount));

        sql = "select i.code,i.name,i.infotype,i.intoyear,o.name as orgname from tb_card_teachstudinfo i left join tb_card_orgframework o on o.code=i.orgcode where i.uid=?";
        JSONObject item = dbService.QueryJSONObject(sql, card.getString("infoid"));
        if (item != null) {
            card.put("code", item.getString("code"));
            card.put("name", item.getString("name"));
            card.put("infotype", item.getString("infotype"));
            card.put("intoyear", item.getString("intoyear"));
            card.put("orgname", item.getString("orgname"));
        }

        CardInfo info = JSONObject.parseObject(card.toJSONString(), CardInfo.class);
        return info;
    }

    public CardInfo GetCardInfoTxtByResult(CardInfo result) {
        if (result == null) {
            result = new CardInfo();
            result.setStatus("卡无效");
            return result;
        }
        String msg = result.getStatus();
        if (msg.equals("1")) {
            result.setStatus("正常");
        } else if (msg.equals("none")) {
            result.setStatus("未发卡");
        } else if (msg.equals("2")) {
            result.setStatus("卡已挂失");
        } else if (msg.equals("0")) {
            result.setStatus("卡无效");
        } else if (msg.equals("noman")) {
            result.setStatus("卡无持卡人");
        }
        return result;
    }


    /**
     * 临时结算账户余额
     *
     * @param cardid
     * @param balance
     * @return
     */
    public BigDecimal GetCardBalance(String cardid, BigDecimal balance) {
        String sql = "SELECT sum(t.money) as result FROM ( " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=1 and status=1 and orderstatus=2 and paywallet=1 and cardid='" + cardid + "' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=2 and status=1 and orderstatus=2 and paywallet=1 and cardid='" + cardid + "' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=3 and status=1 and orderstatus=2 and paywallet=1 and cardid='" + cardid + "' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=6 and status=1 and orderstatus=2 and paywallet=1 and cardid='" + cardid + "' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=7 and status=1 and orderstatus=2 and paywallet=1 and cardid='" + cardid + "' " +
                "     union all  " +
                "     SELECT sum(money) as money FROM tb_card_transaction_detail WHERE tradetype=8 and status=1 and orderstatus=2 and paywallet=1 and cardid='" + cardid + "' " +
                "     union all     " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='1' and status=1 and paywallet=1 and cardid='" + cardid + "' " +
                "     union all  " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='3' and status=1 and paywallet=1 and cardid='" + cardid + "' " +
                "     union all  " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='4' and status=1 and paywallet=1 and cardid='" + cardid + "' " +
                "     union all  " +
                "     SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='5' and status=1 and paywallet=1 and cardid='" + cardid + "' " +
                "     union all  " +
                "     SELECT sum(crl.money)*-1 as money FROM tb_consume_reserve_list crl LEFT JOIN tb_card_cardinfo cc ON crl.infoid=cc.infoid WHERE crl.isspentfree=1 and crl.isconsumed=0 and crl.status=1 and crl.paywallet=1 and cc.uid='" + cardid + "' " +
                ") t";
        JSONObjectPlus sumresult = dbService.QueryJSONObjectPlus(sql);
        if (sumresult != null) {
            BigDecimal result = sumresult.getBigDecimal("result");
            balance = balance.add(result);
        }
        return balance;
    }

    public BigDecimal GetCardViceWallet(String cardid, BigDecimal vicewallet) {
        String sql = "select sum(t.money) as result from ( " +
                " SELECT sum(money) as money FROM tb_card_transaction_detail where tradetype=4 and status=1 and orderstatus=2 and cardid='" + cardid + "' " +
                "  union all " +
                " SELECT sum(money) as money FROM tb_card_transaction_detail where tradetype=9 and status=1 and orderstatus=2 and cardid='" + cardid + "' " +
                "  union all " +
                " SELECT sum(money)*-1 as money FROM tb_consume_payrecords where paytype='1' and status=1 and paywallet=2 and cardid='" + cardid + "' " +
                "  union all  " +
                " SELECT sum(money)*-1 as money FROM tb_consume_payrecords where paytype='3' and status=1 and paywallet=2 and cardid='" + cardid + "' " +
                "  union all " +
                " SELECT sum(money)*-1 as money FROM tb_consume_payrecords where paytype='4' and status=1 and paywallet=2 and cardid='" + cardid + "' " +
                "  union all  " +
                " SELECT sum(money)*-1 as money FROM tb_consume_payrecords WHERE paytype='5' and status=1 and paywallet=2 and cardid='" + cardid + "' " +
                "  union all  " +
                " SELECT sum(crl.money)*-1 as money FROM tb_consume_reserve_list crl LEFT JOIN tb_card_cardinfo cc ON crl.infoid=cc.infoid WHERE crl.isspentfree=1 and crl.isconsumed=0 and crl.status=1 and crl.paywallet=2 and cc.uid='" + cardid + "' " +
                ") t";
        JSONObjectPlus sumresult = dbService.QueryJSONObjectPlus(sql);
        if (sumresult != null) {
            BigDecimal result = sumresult.getBigDecimal("result");
            vicewallet = vicewallet.add(result);
        }
        return vicewallet;
    }

    public BigDecimal GetCardWaterWallet(String cardid, BigDecimal waterwallet){
        String sql = "SELECT sum(t.money) as result FROM ( " +
                "SELECT sum(money) as money FROM tb_waterctrl_transaction_detail WHERE status=1 and orderstatus=2 and cardid='"+ cardid + "' " +
                "union all     " +
                "SELECT sum(money)*-1 as money FROM tb_waterctrl_payrecords WHERE status=1 and cardid='"+ cardid + "' " +
                ")t";
        JSONObjectPlus sumresult = dbService.QueryJSONObjectPlus(sql);
        if (sumresult != null) {
            BigDecimal result = sumresult.getBigDecimal("result");
            waterwallet = waterwallet.add(result);
        }
        return waterwallet;
    }

    /**
     * 获取卡片计次消费次数
     *
     * @param cardid
     * @param timescount
     * @return
     */
    public int GetCardTimesCount(String cardid, int timescount) {
        String sql = "SELECT count(1) as result FROM tb_consume_payrecords where cardid=? and paytype='2' and status=1";
        JSONObject sumresult = dbService.QueryJSONObject(sql, cardid);
        if (sumresult != null) {
            int result = sumresult.getIntValue("result");
            timescount = timescount + result;
        }
        return timescount;
    }


    public JsonData getCardOpRecord(HttpServletRequest request, HttpServletResponse response, String infoid, String dotype, String startdate, String enddate, String key, int start, int limit) {
        String where = " 1=1 ";
        if(!StringUtils.isBlank(infoid)) {
        	 where += " and find_in_set(infoid,'" + infoid + "')>0 ";
        }

        if (!StringUtils.isBlank(dotype)) {
            where += " and dotype='" + dotype + "' ";
        }
        if (!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate)) {
            where += " and createdate between '" + startdate + "' and '" + enddate + "'";
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (cardno='"+ComHelper.LeftPad(key, 12, '0')+"' or infocode='"+key+"' or infoname like '%"+key+"%' or des like '%"+key+"%') ";
        }

        String sort = ExtSort.Orderby(request, "createdate desc");
        String sqlformat = "select r.uid,r.infoid,r.cardno,r.cardsn,r.oldcardno,r.dotype,dic.name as dotypename,"
                + " r.des,r.createdate,r.usedes,r.ismain,r.creator,r.infocode,r.infoname,r.orgname "
                + " from (select uid,infoid,cardno,cardsn,oldcardno,dotype,des,createdate,usedes,ismain,creator,infocode,infoname,orgname "
                + " from  tb_card_cardinfo_oprecord where %s order by %s limit %s,%s) r "
                + " inner join tb_sys_dictionary dic on dic.code = r.dotype and dic.groupcode = 'SYS0000006' order by %s";
        String sql = String.format(sqlformat, where, sort, start, limit, sort);
        JSONArray list = dbService.QueryList(sql);
        int total = dbService.getCount("tb_card_cardinfo_oprecord", where);
        JsonData result = Json.getJsonData(true, "", list, total);
        return result;
    }

    public JsonResult getCardInfo(HttpServletRequest request, HttpServletResponse response, String cardno) {
        if (StringUtils.isBlank(cardno) || cardno.equals("0")) {
            return Json.getJsonResult("卡号为空");
        }
        cardno = ComHelper.LeftPad(cardno, 12, '0');
        String sql = "select c.uid,c.cardno,c.usedes,c.deposit,c.balance,c.vicewallet,c.waterwallet,c.timescount,c.enddate,c.status,c.infoid,i.name,i.code,i.infotype,i.intoyear,o.name as orgname from tb_card_cardinfo c left join tb_card_teachstudinfo i on i.uid=c.infoid left join tb_card_orgframework o on o.code=i.orgcode where c.cardno=? and c.status in(1,2)";
        JSONArray list = dbService.QueryList(sql, cardno);
        if (list.size() == 0) {
            return Json.getJsonResult("卡片未登记");
        }
        JSONObjectPlus cardplus = JSONObjectPlus.ToPlus(list.getJSONObject(0));
        String cardid = cardplus.getString("uid");

        BigDecimal balance = cardplus.getBigDecimal("balance");
        cardplus.remove("balance");
        cardplus.put("balance", GetCardBalance(cardid, balance));

        BigDecimal vicewallet = cardplus.getBigDecimal("vicewallet");
        cardplus.remove("vicewallet");
        cardplus.put("vicewallet", GetCardViceWallet(cardid, vicewallet));

        BigDecimal waterwallet = cardplus.getBigDecimal("waterwallet");
        cardplus.remove("waterwallet");
        cardplus.put("waterwallet", GetCardWaterWallet(cardid, waterwallet));

        int timescount = cardplus.getIntValue("timescount");
        cardplus.remove("timescount");
        cardplus.put("timescount", GetCardTimesCount(cardid, timescount));
        return Json.getJsonResult(true, cardplus);
    }


    public JsonData getCardTransactionDetail(HttpServletRequest request, HttpServletResponse response, String cardid, int start, int limit) {
        if (StringUtils.isBlank(cardid)) {
            return Json.getJsonData(true);
        }

        String sql = "select t.uid,t.cardno,dic.name as tradetype,t.money,t.orderstatus,dic8.name as payway,dic7.name as paytype,t.des,t.tradedate from ("
                + " SELECT * FROM ("
                + " (select uid,cardno,tradetype,money,orderstatus,payway,paytype,des,tradedate,paywallet from tb_card_transaction_detail where cardid='%s' and paywallet=1 )"
                + " union all "
                + " (select uid,cardno,tradetype,money,orderstatus,payway,paytype,des,tradedate,paywallet from tb_card_transaction_detail_sleep where cardid='%s' and paywallet=1 )"
                + " union all "
                + " (select uid,cardno, 2 as tradetype,money,2 as orderstatus, 4 as payway, (case when paytype='2' then 9 else 8 end) as paytype,des,recordtime as tradedate, paywallet from tb_consume_payrecords where cardid='%s' ) "
                + " union all "
                + " (select uid,cardno, 2 as tradetype,money,2 as orderstatus, 4 as payway, (case when paytype='2' then 9 else 8 end) as paytype,des,recordtime as tradedate, paywallet from tb_consume_payrecords_sleep where cardid='%s' ) "
                + " )d order by tradedate desc ) t "
                + " left join tb_sys_dictionary dic on dic.code=t.tradetype and dic.groupcode='SYS0000009' "
                + " left join tb_sys_dictionary dic8 on dic8.code=t.payway and dic8.groupcode='SYS0000008' "
                + " left join tb_sys_dictionary dic7 on dic7.code=t.paytype and dic7.groupcode='SYS0000007' "
                + " order by tradedate desc limit %s,%s";
        String sqlCount = "select count(*) as count from ("
                + " SELECT * FROM ("
                + " (select uid,cardno,tradetype,money,orderstatus,payway,paytype,des,tradedate,paywallet from tb_card_transaction_detail where cardid='%s' and paywallet=1 )"
                + " union all "
                + " (select uid,cardno,tradetype,money,orderstatus,payway,paytype,des,tradedate,paywallet from tb_card_transaction_detail_sleep where cardid='%s' and paywallet=1 )"
                + " union all "
                + " (select uid,cardno, 2 as tradetype,money,2 as orderstatus, 4 as payway, (case when paytype='2' then 9 else 8 end) as paytype,des,recordtime as tradedate, paywallet from tb_consume_payrecords where cardid='%s' ) "
                + " union all "
                + " (select uid,cardno, 2 as tradetype,money,2 as orderstatus, 4 as payway, (case when paytype='2' then 9 else 8 end) as paytype,des,recordtime as tradedate, paywallet from tb_consume_payrecords_sleep where cardid='%s' ) "
                + " )d order by tradedate desc ) t "
                + " left join tb_sys_dictionary dic on dic.code=t.tradetype and dic.groupcode='SYS0000009' "
                + " left join tb_sys_dictionary dic8 on dic8.code=t.payway and dic8.groupcode='SYS0000008' "
                + " left join tb_sys_dictionary dic7 on dic7.code=t.paytype and dic7.groupcode='SYS0000007' "
                + " order by tradedate desc";
        sqlCount = String.format(sqlCount, cardid, cardid, cardid, cardid);
        int count = dbService.QueryJSONObject(sqlCount).getIntValue("count");

        sql = String.format(sql, cardid, cardid, cardid, cardid, start, limit);
        JsonData list = dbService.QueryJsonData(sql);
        list.setTotal(count);
        return list;
    }

    public JsonData getCardTransactionDetailNew(HttpServletRequest request, HttpServletResponse response, String cardid, int start, int limit) {
        //if (StringUtils.isBlank(cardid)) {
        //    return Json.getJsonData(true);
        //}
        start = 0;
        limit = 10;

        String sql = "select t.uid,t.cardno,dic.name as tradetype,t.money,t.orderstatus,dic8.name as payway,dic7.name as paytype,t.des,t.tradedate from ("
                + " SELECT * FROM ("
                + " (select uid,cardno,tradetype,money,orderstatus,payway,paytype,des,tradedate,paywallet from tb_card_transaction_detail where tradetype=1 and paywallet=1 )"
                + " union all "
                + " (select uid,cardno,tradetype,money,orderstatus,payway,paytype,des,tradedate,paywallet from tb_card_transaction_detail_sleep where tradetype=1 and paywallet=1 )"
                + " )d order by tradedate desc ) t "
                + " left join tb_sys_dictionary dic on dic.code=t.tradetype and dic.groupcode='SYS0000009' "
                + " left join tb_sys_dictionary dic8 on dic8.code=t.payway and dic8.groupcode='SYS0000008' "
                + " left join tb_sys_dictionary dic7 on dic7.code=t.paytype and dic7.groupcode='SYS0000007' "
                + " order by tradedate desc limit %s,%s";
        String sqlCount = "select count(*) as count from ("
                + " SELECT * FROM ("
                + " (select uid,cardno,tradetype,money,orderstatus,payway,paytype,des,tradedate,paywallet from tb_card_transaction_detail where tradetype=1 and paywallet=1 )"
                + " union all "
                + " (select uid,cardno,tradetype,money,orderstatus,payway,paytype,des,tradedate,paywallet from tb_card_transaction_detail_sleep where tradetype=1 and paywallet=1 )"
                + " )d order by tradedate desc ) t "
                + " left join tb_sys_dictionary dic on dic.code=t.tradetype and dic.groupcode='SYS0000009' "
                + " left join tb_sys_dictionary dic8 on dic8.code=t.payway and dic8.groupcode='SYS0000008' "
                + " left join tb_sys_dictionary dic7 on dic7.code=t.paytype and dic7.groupcode='SYS0000007' "
                + " order by tradedate desc";

        int count = dbService.QueryJSONObject(sqlCount).getIntValue("count");
        sql = String.format(sql, start, limit);
        JsonData list = dbService.QueryJsonData(sql);
        list.setTotal(count);
        return list;
    }

    public JsonData getWaterTransactionDetail(HttpServletRequest request, HttpServletResponse response, String cardid, int start, int limit){
        String sql = "SELECT t.card,t.money,dic.name as tradetype,t.tradedate,t.des FROM ( " +
                "SELECT cardno as card,tradetype,money,tradedate,des FROM tb_waterctrl_transaction_detail WHERE cardid='"+cardid+"' " +
                "union all " +
                "SELECT card,2 as tradetype,money,recordtime as tradedate,'' as des FROM tb_waterctrl_payrecords WHERE cardid='"+cardid+"' " +
                "union all  " +
                "SELECT card,2 as tradetype,money,recordtime as tradedate,'水控消费' as des FROM tb_waterctrl_payrecords_sleep WHERE cardid='"+cardid+"' " +
                ")t " +
                "left join tb_sys_dictionary dic on dic.code=t.tradetype and dic.groupcode='SYS0000009' " +
                " order by tradedate desc limit "+start+", "+limit;
        return dbService.QueryJsonData(sql);
    }

    /**
     * 充值
     *
     * @param request
     * @param response
     * @param cardid
     * @param cardno
     * @param paytype
     * @param payway
     * @param des
     * @return
     */
    public JsonResult SaveRecharge(HttpServletRequest request, HttpServletResponse response, String cardid, String cardno, String amountmoney, String paywallet,String paytype, String payway, String des) {
        String orderno = "";
        BigDecimal amount = new BigDecimal(amountmoney);
        if (amount.compareTo(new BigDecimal(0)) > 0) {
            try {
                cardno = ComHelper.LeftPad(cardno, 12, '0');
                CardInfo card = GetCardInfoByNo(cardno);
                if (card != null) {
                    String infoid = card.getInfoid();
                    SysUser sysuser = userredis.get(request);
                    String creatorid = sysuser.getUid();
                    String creator = sysuser.getName();
                    String usedes = card.getUsedes();
                    String infoname = card.getName();
                    String infocode = card.getCode();
                    String orgname = card.getOrgname();
                    int ismain = card.getIsmain();
                    String sql = "";
                    orderno = UUID.randomUUID().toString();

                    if("1".equals(paywallet)){
                        if (StringUtils.isBlank(des)) {
                            des = "人工充值";
                        }
                        sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,tradedate,createdate,creatorid,modifierid,status) "
                                + "values(?,?,?,?,1,?,?,2,?,?,?,now(),now(),?,?,1) ";

                        dbService.excuteSql(sql, orderno, cardid, infoid, cardno, amount, paywallet, payway, paytype, des, creatorid, creatorid);
                    }else {
                        if (StringUtils.isBlank(des)) {
                            des = "水控钱包人工充值";
                        }
                        sql = "insert into tb_waterctrl_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,orderstatus,payway,paytype,des,tradedate,createdate,creatorid,modifierid,status) "
                                + "values(?,?,?,?,1,?,2,?,?,?,now(),now(),?,?,1) ";

                        dbService.excuteSql(sql, orderno, cardid, infoid, cardno, amount, payway, paytype, des, creatorid, creatorid);
                    }

                    syslogs.Write(request, "卡操作", "充值：创建交易记录（" + orderno + "）");

                    String opdes = "人工充值￥" + amount + "元";
                    if("3".equals(paywallet)){
                        opdes = "充值水控钱包￥" + amount + "元";
                    }
                    sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                            + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                    dbService.excuteSql(sql, infoid, cardid, cardno, cardno, "1", ismain, usedes, creator, infocode, infoname, orgname, opdes, creatorid);

                    return Json.getJsonResult(true, orderno);
                } else {
                    return Json.getJsonResult("卡片信息异常");
                }
            } catch (Exception ex) {
                return Json.getJsonResult(ex.getMessage());
            }
        }
        return Json.getJsonResult(false, "充值金额为￥0元");
    }

    /**
     * 挂失
     *
     * @param request
     * @param response
     * @param cardid
     * @param des
     * @return
     */
    public JsonResult SaveLossStatus(HttpServletRequest request, HttpServletResponse response, String cardid, String des) throws Exception {
        if (StringUtils.isBlank(cardid)) {
            return Json.getJsonResult(false, "请选择拟挂失的卡片记录");
        }
        CardInfo card = GetCardInfoByNo("", cardid);
        if (card != null && (card.getStatus().equals("1") || card.getStatus().equals("2"))) {
            int status = 1;
            String dotype = "3";
            if (card.getStatus().equals("1")) {
                status = 2;
                dotype = "3";//挂失
                String infoId = card.getInfoid();
//                dbService.excuteSql("call sp_info_cardoperations(?,?,?)", card.getInfoid(), card.getCardno(), "");
                dbService.excuteTransaction(new CallbackTransaction() {
                    @Override
                    public void execute(Connection connection) throws SQLException {
                        String sql = "update tb_alleyway_apoffline_authrize set mergestatus=0 where infoid=?";
                        PreparedStatement ps = connection.prepareStatement(sql);
                        ps.setString(1, infoId);
                        ps.execute();
                        ps.close();
                        String sql1 = "update tb_alleyway_apoffline_group_access set mergestatus=0 where infoid=?";
                        PreparedStatement ps1 = connection.prepareStatement(sql1);
                        ps1.setString(1, infoId);
                        ps1.execute();
                        ps1.close();
                        String sql2 = "DELETE FROM tb_alleyway_apoffline_namelist where infoid=? and downstatus in (0,2)";
                        PreparedStatement ps2 = connection.prepareStatement(sql2);
                        ps2.setString(1, infoId);
                        ps2.execute();
                        ps2.close();
                        String sql3 = "update tb_alleyway_apoffline_namelist set downstatus=3,downnum=0 where infoid=? and downstatus in(1,5,6)";
                        PreparedStatement ps3 = connection.prepareStatement(sql3);
                        ps3.setString(1, infoId);
                        ps3.execute();
                        ps3.close();
                        String sql4 = "update tb_alleyway_yunoffline_authrize set mergestatus=0 where infoid=?";
                        PreparedStatement ps4 = connection.prepareStatement(sql4);
                        ps4.setString(1, infoId);
                        ps4.execute();
                        ps4.close();
                        String sql5 = "update tb_alleyway_yunoffline_group_access set mergestatus=0 where infoid=?";
                        PreparedStatement ps5 = connection.prepareStatement(sql5);
                        ps5.setString(1, infoId);
                        ps5.execute();
                        ps5.close();
                        String sql6 = "DELETE FROM tb_alleyway_yunoffline_namelist where infoid=? and downstatus in (0,2)";
                        PreparedStatement ps6 = connection.prepareStatement(sql6);
                        ps6.setString(1, infoId);
                        ps6.execute();
                        ps6.close();
                        String sql7 = "update tb_alleyway_yunoffline_namelist set downstatus=3,downnum=0 where infoid=? and downstatus in(1,5,6)";
                        PreparedStatement ps7 = connection.prepareStatement(sql7);
                        ps7.setString(1, infoId);
                        ps7.execute();
                        ps7.close();
                        String sql8 = "update tb_alleyway_offline_authrize set handlestatus=0 where infoid=?";
                        PreparedStatement ps8 = connection.prepareStatement(sql8);
                        ps8.setString(1, infoId);
                        ps8.execute();
                        ps8.close();
                        String sql9 = "DELETE FROM  tb_alleyway_offline_authrize_tasklist where infoid=?  and status in (0,2)";
                        PreparedStatement ps9 = connection.prepareStatement(sql9);
                        ps9.setString(1, infoId);
                        ps9.execute();
                        ps9.close();
                        String sql10 = "update tb_alleyway_offline_authrize_tasklist set status=3,downnum=0 where infoid=? and status in(1,5,6)";
                        PreparedStatement ps10 = connection.prepareStatement(sql10);
                        ps10.setString(1, infoId);
                        ps10.execute();
                        ps10.close();
                        String sql11 = "update tb_alleyway_kre_authrize set downstatus=3 where infoid=?";
                        PreparedStatement ps11 = connection.prepareStatement(sql11);
                        ps11.setString(1, infoId);
                        ps11.execute();
                        ps11.close();
                        String sql12 = "update tb_face_authorizeface set infodownstatus=0,downnum=0 where infoid=? and infodownstatus in (0,2)";
                        PreparedStatement ps12 = connection.prepareStatement(sql12);
                        ps12.setString(1, infoId);
                        ps12.execute();
                        ps12.close();
                        String sql13 = "update tb_face_authorizeface set infodownstatus=5,downnum=0 where infoid=? and infodownstatus in (1,5,6)";
                        PreparedStatement ps13 = connection.prepareStatement(sql13);
                        ps13.setString(1, infoId);
                        ps13.execute();
                        ps13.close();
                        String sql14 = "update tb_face_authorize_group_access set downstatus=0,downnum=0 where infoid=? and downstatus in (0,2)";
                        PreparedStatement ps14 = connection.prepareStatement(sql14);
                        ps14.setString(1, infoId);
                        ps14.execute();
                        ps14.close();
                        String sql15 = "update tb_face_authorize_group_access set downstatus=5,downnum=0 where infoid=? and downstatus in (1,5,6)";
                        PreparedStatement ps15 = connection.prepareStatement(sql15);
                        ps15.setString(1, infoId);
                        ps15.execute();
                        ps15.close();
                    }
                });
                dbService.excuteSql("INSERT INTO tb_alleyway_kre_authrize_backups (`uid`, `infoid`, `cardno`, `devid`, `relays`, `weekindex`, `limits`, `endtime`, `effectivetimes`, `authtype`, `downstatus`, `downnum`, `downmsg`, `creatorid`, `createdate`)\n" +
                        "SELECT `uid`, `infoid`, `cardno`, `devid`, `relays`, `weekindex`, `limits`, `endtime`, `effectivetimes`, `authtype`,0, 0, '', `creatorid`, `createdate`\n" +
                        "FROM tb_alleyway_kre_authrize WHERE infoid = ?",infoId);
            } else {
                return Json.getJsonResult(false, "当前状态无法挂失");
            }
            String infoid = card.getInfoid();
            String cardno = card.getCardno();
            String cardsn = card.getCardsn();
            SysUser sysuser = userredis.get(request);
            String creatorid = sysuser.getUid();
            String creator = sysuser.getName();
            String usedes = card.getUsedes();
            String infoname = card.getName();
            String infocode = card.getCode();
            String orgname = card.getOrgname();
            int ismain = card.getIsmain();

            String sql = "update tb_card_cardinfo set modifydate=now(),modifierid=?,status=? where uid=?";
            dbService.excuteSql(sql, creatorid, status, cardid);

            syslogs.Write(request, "卡操作", "挂失：更新卡信息状态为（" + String.valueOf(status) + "）");

            if (StringUtils.isBlank(des)) {
                des = "挂失";
            } else {
                des = "挂失；" + des;
            }

            sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                    + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
            dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, creatorid);

            String createqrcodeway=sysconfig.get("createqrcodeway");
            if(!createqrcodeway.equals("2")) {
                //生成二维码方式不是按随机字符生成
                sql="delete from tb_card_qrcode where infoid=?";
                dbService.excuteSql(sql, infoid);
            }

            return Json.getJsonResult(true);
        } else {
            card = GetCardInfoTxtByResult(card);
            return Json.getJsonResult(false, "当前卡" + card.getStatus() + "，无法挂失");
        }
    }
 /**
     * 解挂
     *
     * @param request
     * @param response
     * @param cardid
     * @param des
     * @return
     */
    public JsonResult UnlockStatus(HttpServletRequest request, HttpServletResponse response, String cardid, String des) throws Exception {
        if (StringUtils.isBlank(cardid)) {
            return Json.getJsonResult(false, "请选择拟解挂的卡片记录");
        }
        CardInfo card = GetCardInfoByNo("", cardid);
        if (card != null && card.getStatus().equals("2")) {
            int status = 1;
            String dotype = "3";
            if (card.getStatus().equals("2")) {
                status = 1;
                dotype = "4";//解挂
                //西奥门禁授权合并
                rosterDownloadService.handleTask(request,response);
                //脱机门禁授权合并
                apolNameListService.mergeAuthorize(request,card.getInfoid());
                //云门禁授权合并
                yolNameListService.mergeAuthorize(request,card.getInfoid());
            } else {
                return Json.getJsonResult(false, "当前状态无法解挂");
            }
            String infoid = card.getInfoid();
            String cardno = card.getCardno();
            String cardsn = card.getCardsn();
            SysUser sysuser = userredis.get(request);
            String creatorid = sysuser.getUid();
            String creator = sysuser.getName();
            String usedes = card.getUsedes();
            String infoname = card.getName();
            String infocode = card.getCode();
            String orgname = card.getOrgname();
            int ismain = card.getIsmain();

            String sql = "update tb_card_cardinfo set modifydate=now(),modifierid=?,status=? where uid=?";
            dbService.excuteSql(sql, creatorid, status, cardid);

            syslogs.Write(request, "卡操作", "解挂：更新卡信息状态为（" + String.valueOf(status) + "）");
            dbService.excuteSql("INSERT INTO tb_alleyway_kre_authrize (`uid`, `infoid`, `cardno`, `devid`, `relays`, `weekindex`, `limits`, `endtime`, `effectivetimes`, `authtype`, `downstatus`, `downnum`, `downmsg`, `creatorid`, `createdate`)\n" +
                    "SELECT `uid`, `infoid`, `cardno`, `devid`, `relays`, `weekindex`, `limits`, `endtime`, `effectivetimes`, `authtype`,0, 0, '', `creatorid`, `createdate`\n" +
                    "FROM tb_alleyway_kre_authrize_backups WHERE infoid = ?",infoid);
            dbService.excuteSql("delete from tb_alleyway_kre_authrize_backups where infoid = ?",infoid);
            //人脸信息和卡号重新下载
            dbService.excuteSql("update tb_face_authorizeface set infodownstatus=0,downnum=0 where infoid=? and infodownstatus in (0,2)",infoid);
            dbService.excuteSql("update tb_face_authorizeface set infodownstatus=5,downnum=0 where infoid=? and infodownstatus in (1,5,6)",infoid);
            dbService.excuteSql("update tb_face_authorize_group_access set downstatus=0,downnum=0 where infoid=? and downstatus in (0,2)",infoid);
            dbService.excuteSql("update tb_face_authorize_group_access set downstatus=5,downnum=0 where infoid=? and downstatus in (1,5,6)",infoid);
            if (StringUtils.isBlank(des)) {
                des = "解挂";
            } else {
                des = "解挂；" + des;
            }

            sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                    + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
            dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, creatorid);

            String createqrcodeway=sysconfig.get("createqrcodeway");
            if(!createqrcodeway.equals("2")) {
                //生成二维码方式不是按随机字符生成
                sql="delete from tb_card_qrcode where infoid=?";
                dbService.excuteSql(sql, infoid);
            }

            return Json.getJsonResult(true);
        } else {
            card = GetCardInfoTxtByResult(card);
            return Json.getJsonResult(false, "当前卡" + card.getStatus() + "，无法挂解挂");
        }
    }

    /**
     * 是否存在设备离线
     *
     * @param request
     * @param response
     * @param devclass
     * @return
     */
    public JsonResult ExistsDevUnline(HttpServletRequest request, HttpServletResponse response, String devclass) {
        int count = dbService.getCount("tb_dev_accesscontroller", "devclass='" + devclass + "' and devstatus=0");
        if (count > 0) {
            return Json.getJsonResult(true);
        } else {
            return Json.getJsonResult(false);
        }
    }

    /**
     * 退款
     *
     * @param request
     * @param response
     * @param cardid
     * @param des
     * @param refundamount
     * @return
     */
    public JsonResult SaveRefund(HttpServletRequest request, HttpServletResponse response, String cardid, String des, String refundamount,String paywallet) {
        if (StringUtils.isBlank(cardid)) {
            return Json.getJsonResult(false, "请读卡，获取卡片信息");
        }
        CardInfo card = GetCardInfoByNo("", cardid);
        if (card != null) {

            String dotype = "2";

            BigDecimal balance = card.getBalance();
            BigDecimal waterwallet = card.getWaterwallet();
            String infoid = card.getInfoid();
            String cardno = card.getCardno();
            String cardsn = card.getCardsn();

            SysUser sysuser = userredis.get(request);
            String creatorid = sysuser.getUid();
            String creator = sysuser.getName();
            String usedes = card.getUsedes();
            String infoname = card.getName();
            String infocode = card.getCode();
            String orgname = card.getOrgname();
            int ismain = card.getIsmain();

            BigDecimal refundamountvalue = new BigDecimal(refundamount);
            if("1".equals(paywallet)){
                if (refundamountvalue.compareTo(balance) >= 0) {
                    refundamountvalue = balance;
                }
            }else if("3".equals(paywallet)){
                if (refundamountvalue.compareTo(waterwallet) >= 0) {
                    refundamountvalue = waterwallet;
                }
            }

            refundamountvalue = refundamountvalue.multiply(new BigDecimal(-1));

            String orderno = UUID.randomUUID().toString();
            //创建退款流水
            String sql = "";
            String td_des = "";
            if("1".equals(paywallet)){
            	td_des= "退款";
            	sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,tradedate,createdate,creatorid,modifierid,status) "
                        + "values(?,?,?,?,3,?,?,2,?,?,?,now(),now(),?,?,1) ";
            	dbService.excuteSql(sql, orderno, cardid, infoid, cardno, refundamountvalue, paywallet,"3", "3", td_des, creatorid, creatorid);
            }else {
            	td_des = "水控退款";
            	sql = "insert into tb_waterctrl_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,orderstatus,payway,paytype,des,tradedate,createdate,creatorid,modifierid,status) "
                        + "values(?,?,?,?,3,?,2,?,?,?,now(),now(),?,?,1) ";
            	dbService.excuteSql(sql, orderno, cardid, infoid, cardno, refundamountvalue,"3", "3", td_des, creatorid, creatorid);
            }



            syslogs.Write(request, "卡操作", "退款：创建退款交易记录（" + orderno + "）");
            String _des = "";
            if("1".equals(paywallet)){
                _des = String.format("退款￥%s元；", refundamountvalue.multiply(new BigDecimal(-1)));
            }else if("3".equals(paywallet)){
                _des = String.format("水控钱包退款￥%s元；", refundamountvalue.multiply(new BigDecimal(-1)));
            }
            if (!StringUtils.isBlank(des)) {
                des = _des + des;
            } else {
                des = _des;
            }

            sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                    + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
            dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, creatorid);

            JSONObject rs = new JSONObject();
            rs.put("orderno", orderno);
            rs.put("refundamount", refundamountvalue.multiply(new BigDecimal(-1)));
            return Json.getJsonResult(true, "", rs);
        } else {
            card = GetCardInfoTxtByResult(card);
            return Json.getJsonResult(false, "当前卡" + card.getStatus() + "，无法退款");
        }
    }

    /**
     * 权限收回退卡有余额判断不能进行退卡
     *
     * @param request
     * @param response
     * @param cardid
     * @param des
     * @return
     */
    public JsonResult SaveRevokeReturnCard(HttpServletRequest request, HttpServletResponse response, String cardid, String des, boolean isdeposit) {
        if (StringUtils.isBlank(cardid)) {
            return Json.getJsonResult(false, "请读卡，获取卡片信息");
        }
        CardInfo card = GetCardInfoByNo("", cardid);
        if (card != null && (!card.getStatus().equals("0"))) {
            String dotype = "5";
            String infoid = card.getInfoid();
            String cardno = card.getCardno();
            String cardsn = card.getCardsn();
            BigDecimal balance = card.getBalance();
            if(balance.compareTo(BigDecimal.ZERO)>0){
                return Json.getJsonResult(false,"余额不等于0");
            }
            BigDecimal vicewallet = card.getVicewallet();
            String modifierid = userredis.getUserId(request);
            SysUser sysuser = userredis.get(request);
            String creator = sysuser.getName();
            String usedes = card.getUsedes();
            String infoname = card.getName();
            String infocode = card.getCode();
            String orgname = card.getOrgname();
            int ismain = card.getIsmain();

            String sql = "update tb_card_cardinfo set status=0,enddate=now(),des=?,modifydate=now(),modifierid=? where uid=?";
            dbService.excuteSql(sql, "办理退卡，设置为无效卡；", modifierid, cardid);
            syslogs.Write(request, "卡操作", "退卡：更新原卡（" + cardid + "）状态为（0）");

            //sql = "update tb_card_teachstudinfo set card='',cardsn='',modifydate=now(),modifierid='"+modifierid+"' where uid=? and card=? and cardsn=?";
            //dbService.excuteSql(sql, infoid, cardno, cardsn);
            //syslogs.Write(request, "卡操作", "退卡：更新人员卡信息为（" + infoid + "）");

            sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                    + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
            if (StringUtils.isBlank(des)) {
                des = "卡片设置为无效";
            } else {
                des = "卡片设置为无效；" + des;
            }
            dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);

            sql=String.format("call sp_info_cardchange('%s','%s','%s')", infoid, cardno, "");
            dbService.excuteSql(sql);

            String orderno = "";
            BigDecimal realrefund = new BigDecimal(0);
            if (balance.compareTo(new BigDecimal(0)) > 0) {
                realrefund = balance;
                if (realrefund.compareTo(new BigDecimal(0)) < 0) {
                    realrefund = new BigDecimal(0);
                }
                if (realrefund.compareTo(new BigDecimal(0)) > 0) {
                    realrefund = realrefund.multiply(new BigDecimal(-1));
                    orderno = UUID.randomUUID().toString();
                    sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,tradedate,createdate,creatorid,modifierid,status) "
                            + "values(?,?,?,?,3,?,1,2,?,?,?,now(),now(),?,?,1) ";
                    dbService.excuteSql(sql, orderno, cardid, infoid, cardno, realrefund, "3", "3", "退卡退款", modifierid, modifierid);

                    sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                            + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                    des = String.format("退卡退还主钱包余额￥%s元", realrefund.multiply(new BigDecimal(-1)));
                    dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);

                }
            }

            //原卡副钱包余额>0
            if (vicewallet.compareTo(new BigDecimal(0)) > 0) {
                //原卡存在余额，先换卡转出原卡金额，然后再补卡转入新卡
                String orderid = UUID.randomUUID().toString();
                vicewallet = vicewallet.multiply(new BigDecimal(-1));
                sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,tradedate,createdate,creatorid,modifierid,status) "
                        + "values(?,?,?,?,9,?,2,2,?,?,?,now(),now(),?,?,1) ";
                dbService.excuteSql(sql, orderid, cardid, infoid, card.getCardno(), vicewallet, "3", "5", "原卡退卡，副钱包余额归零", modifierid, modifierid);
                syslogs.Write(request, "卡操作", "退卡：创建原卡副钱包归零（" + orderid + "）");

                sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                        + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                des = String.format("退卡归零副钱包余额￥%s元", vicewallet.multiply(new BigDecimal(-1)));
                dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);

            }

            if (isdeposit) {
                sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                        + " values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                des = String.format("退卡退还制卡费￥%s元", card.getDeposit());
                dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);
            }

            String createqrcodeway=sysconfig.get("createqrcodeway");
            if(!createqrcodeway.equals("2")) {
                //生成二维码方式不是按随机字符生成
                sql="delete from tb_card_qrcode where infoid=?";
                dbService.excuteSql(sql, infoid);
            }


            JSONObject rs = new JSONObject();
            rs.put("orderno", orderno);
            rs.put("realrefund", realrefund.multiply(new BigDecimal(-1)));
            rs.put("deposit", card.getDeposit());
            return Json.getJsonResult(true, "", rs);
        } else {
            card = GetCardInfoTxtByResult(card);
            return Json.getJsonResult(false, "当前" + card.getStatus() + "，禁止退卡");
        }
    }




    /**
     * 退卡
     *
     * @param request
     * @param response
     * @param cardid
     * @param des
     * @return
     */
    public JsonResult SaveReturnCard(HttpServletRequest request, HttpServletResponse response, String cardid, String des, boolean isdeposit) {
        if (StringUtils.isBlank(cardid)) {
            return Json.getJsonResult(false, "请读卡，获取卡片信息");
        }
        CardInfo card = GetCardInfoByNo("", cardid);
        if (card != null && (!card.getStatus().equals("0"))) {
            String dotype = "5";
            String infoid = card.getInfoid();
            String cardno = card.getCardno();
            String cardsn = card.getCardsn();
            BigDecimal balance = card.getBalance();
            BigDecimal vicewallet = card.getVicewallet();
            String modifierid = userredis.getUserId(request);
            SysUser sysuser = userredis.get(request);
            String creator = sysuser.getName();
            String usedes = card.getUsedes();
            String infoname = card.getName();
            String infocode = card.getCode();
            String orgname = card.getOrgname();
            int ismain = card.getIsmain();

            String sql = "update tb_card_cardinfo set status=0,enddate=now(),des=?,modifydate=now(),modifierid=? where uid=?";
            dbService.excuteSql(sql, "办理退卡，设置为无效卡；", modifierid, cardid);
            syslogs.Write(request, "卡操作", "退卡：更新原卡（" + cardid + "）状态为（0）");

            //sql = "update tb_card_teachstudinfo set card='',cardsn='',modifydate=now(),modifierid='"+modifierid+"' where uid=? and card=? and cardsn=?";
            //dbService.excuteSql(sql, infoid, cardno, cardsn);
            //syslogs.Write(request, "卡操作", "退卡：更新人员卡信息为（" + infoid + "）");

            sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                    + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
            if (StringUtils.isBlank(des)) {
                des = "卡片设置为无效";
            } else {
                des = "卡片设置为无效；" + des;
            }
            dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);

            sql=String.format("call sp_info_cardchange('%s','%s','%s')", infoid, cardno, "");
            dbService.excuteSql(sql);

            String orderno = "";
            BigDecimal realrefund = new BigDecimal(0);
            if (balance.compareTo(new BigDecimal(0)) > 0) {
                realrefund = balance;
                if (realrefund.compareTo(new BigDecimal(0)) < 0) {
                    realrefund = new BigDecimal(0);
                }
                if (realrefund.compareTo(new BigDecimal(0)) > 0) {
                    realrefund = realrefund.multiply(new BigDecimal(-1));
                    orderno = UUID.randomUUID().toString();
                    sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,tradedate,createdate,creatorid,modifierid,status) "
                            + "values(?,?,?,?,3,?,1,2,?,?,?,now(),now(),?,?,1) ";
                    dbService.excuteSql(sql, orderno, cardid, infoid, cardno, realrefund, "3", "3", "退卡退款", modifierid, modifierid);

                    sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                            + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                    des = String.format("退卡退还主钱包余额￥%s元", realrefund.multiply(new BigDecimal(-1)));
                    dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);

                }
            }

            //原卡副钱包余额>0
            if (vicewallet.compareTo(new BigDecimal(0)) > 0) {
                //原卡存在余额，先换卡转出原卡金额，然后再补卡转入新卡
                String orderid = UUID.randomUUID().toString();
                vicewallet = vicewallet.multiply(new BigDecimal(-1));
                sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,tradedate,createdate,creatorid,modifierid,status) "
                        + "values(?,?,?,?,9,?,2,2,?,?,?,now(),now(),?,?,1) ";
                dbService.excuteSql(sql, orderid, cardid, infoid, card.getCardno(), vicewallet, "3", "5", "原卡退卡，副钱包余额归零", modifierid, modifierid);
                syslogs.Write(request, "卡操作", "退卡：创建原卡副钱包归零（" + orderid + "）");

                sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                        + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                des = String.format("退卡归零副钱包余额￥%s元", vicewallet.multiply(new BigDecimal(-1)));
                dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);

            }

            if (isdeposit) {
                sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                        + " values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                des = String.format("退卡退还制卡费￥%s元", card.getDeposit());
                dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);
            }

            String createqrcodeway=sysconfig.get("createqrcodeway");
            if(!createqrcodeway.equals("2")) {
            	//生成二维码方式不是按随机字符生成
            	sql="delete from tb_card_qrcode where infoid=?";
            	dbService.excuteSql(sql, infoid);
            }


            JSONObject rs = new JSONObject();
            rs.put("orderno", orderno);
            rs.put("realrefund", realrefund.multiply(new BigDecimal(-1)));
            rs.put("deposit", card.getDeposit());
            return Json.getJsonResult(true, "", rs);
        } else {
            card = GetCardInfoTxtByResult(card);
            return Json.getJsonResult(false, "当前卡" + card.getStatus() + "，禁止退卡");
        }
    }


    /**
     * 批量退卡
     * @param infoData 人员卡号信息
     * @return
     */
    public JsonResult batchReturnCard(HttpServletRequest request, String infoData){
        List<JSONObject> list = JSON.parseArray(infoData, JSONObject.class);
        JSONArray jsonArray = new JSONArray();
        StringBuilder msg = new StringBuilder("卡:");
        for (JSONObject obj:list) {
            String cardNo = obj.getString("cardNo");
            if (StringUtils.isBlank(cardNo)) {
                continue;
            }
            CardInfo card = GetCardInfoByNo(cardNo, "");
            if (card != null && (!card.getStatus().equals("0"))) {
                String dotype = "5";
                String infoid = card.getInfoid();
                String cardno = card.getCardno();
                String cardid = card.getCardid();
                String cardsn = card.getCardsn();
                BigDecimal balance = card.getBalance();
                BigDecimal vicewallet = card.getVicewallet();
                String modifierid = userredis.getUserId(request);
                SysUser sysuser = userredis.get(request);
                String creator = sysuser.getName();
                String usedes = card.getUsedes();
                String infoname = card.getName();
                String infocode = card.getCode();
                String orgname = card.getOrgname();
                String des = null;
                boolean isdeposit = false;
                int ismain = card.getIsmain();

                String sql = "update tb_card_cardinfo set status=0,enddate=now(),des=?,modifydate=now(),modifierid=? where uid=?";
                dbService.excuteSql(sql, "办理退卡，设置为无效卡；", modifierid, cardid);
                syslogs.Write(request, "卡操作", "退卡：更新原卡（" + cardid + "）状态为（0）");

                sql = "update tb_card_teachstudinfo set card='',cardsn='',modifydate=now(),modifierid='"+modifierid+"' where uid=? and card=? and cardsn=?";
                dbService.excuteSql(sql, infoid, cardno, cardsn);
                syslogs.Write(request, "卡操作", "退卡：更新人员卡信息为（" + infoid + "）");

                sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                        + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                if (StringUtils.isBlank(des)) {
                    des = "卡片设置为无效";
                } else {
                    des = "卡片设置为无效；" + des;
                }
                dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);

                sql=String.format("call sp_info_cardchange('%s','%s','%s')", infoid, cardno, "");
                dbService.excuteSql(sql);

                String orderno = "";
                BigDecimal realrefund = new BigDecimal(0);
                if (balance.compareTo(new BigDecimal(0)) > 0) {
                    realrefund = balance;
                    if (realrefund.compareTo(new BigDecimal(0)) < 0) {
                        realrefund = new BigDecimal(0);
                    }
                    if (realrefund.compareTo(new BigDecimal(0)) > 0) {
                        realrefund = realrefund.multiply(new BigDecimal(-1));
                        orderno = UUID.randomUUID().toString();
                        sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,tradedate,createdate,creatorid,modifierid,status) "
                                + "values(?,?,?,?,3,?,1,2,?,?,?,now(),now(),?,?,1) ";
                        dbService.excuteSql(sql, orderno, cardid, infoid, cardno, realrefund, "3", "3", "退卡退款", modifierid, modifierid);

                        sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                                + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                        des = String.format("退卡退还主钱包余额￥%s元", realrefund.multiply(new BigDecimal(-1)));
                        dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);

                    }
                }

                //原卡副钱包余额>0
                if (vicewallet.compareTo(new BigDecimal(0)) > 0) {
                    //原卡存在余额，先换卡转出原卡金额，然后再补卡转入新卡
                    String orderid = UUID.randomUUID().toString();
                    vicewallet = vicewallet.multiply(new BigDecimal(-1));
                    sql = "insert into tb_card_transaction_detail(uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,des,tradedate,createdate,creatorid,modifierid,status) "
                            + "values(?,?,?,?,9,?,2,2,?,?,?,now(),now(),?,?,1) ";
                    dbService.excuteSql(sql, orderid, cardid, infoid, card.getCardno(), vicewallet, "3", "5", "原卡退卡，副钱包余额归零", modifierid, modifierid);
                    syslogs.Write(request, "卡操作", "退卡：创建原卡副钱包归零（" + orderid + "）");

                    sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                            + "values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                    des = String.format("退卡归零副钱包余额￥%s元", vicewallet.multiply(new BigDecimal(-1)));
                    dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);

                }

                if (isdeposit) {
                    sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                            + " values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                    des = String.format("退卡退还制卡费￥%s元", card.getDeposit());
                    dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);
                }

                String createqrcodeway=sysconfig.get("createqrcodeway");
                if(!createqrcodeway.equals("2")) {
                    //生成二维码方式不是按随机字符生成
                    sql="delete from tb_card_qrcode where infoid=?";
                    dbService.excuteSql(sql, infoid);
                }


                JSONObject rs = new JSONObject();
                rs.put("orderno", orderno);
                rs.put("realrefund", realrefund.multiply(new BigDecimal(-1)));
                rs.put("deposit", card.getDeposit());
                jsonArray.add(rs);
            } else {
                card = GetCardInfoTxtByResult(card);
                msg.append(cardNo).append("-").append(card.getStatus()).append(",");

            }
        }
        msg.deleteCharAt(msg.length()-1);
        msg.append("禁止退卡");
        JSONObject data = new JSONObject();
        data.put("data",jsonArray);
        if (msg.toString().contains("-")){
            return Json.getJsonResult(true,msg.toString(),data);
        }else {
            return Json.getJsonResult(true,"",data);
        }
    }



    /**
     * 卡片延期
     *
     * @param request
     * @param response
     * @param cardid
     * @param toenddate
     * @return
     */
    public JsonResult SaveDelayCard(HttpServletRequest request, HttpServletResponse response, String cardid, String toenddate) {
        if (StringUtils.isBlank(cardid)) {
            return Json.getJsonResult(false, "请读卡，获取卡片信息");
        }
        CardInfo card = GetCardInfoByNo("", cardid);
        if (card != null && (card.getStatus().equals("1"))) {
            String sql = "update tb_card_cardinfo set enddate=? where uid=?";
            int count = dbService.excuteSql(sql, toenddate, cardid);
            if (count > 0) {
                sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) "
                        + " values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                String des = String.format("卡片延期至%s", toenddate);

                String infoid = card.getInfoid();
                String cardno = card.getCardno();
                String cardsn = card.getCardsn();
                String modifierid = userredis.getUserId(request);
                SysUser sysuser = userredis.get(request);
                String creator = sysuser.getName();
                String usedes = card.getUsedes();
                String infoname = card.getName();
                String infocode = card.getCode();
                String orgname = card.getOrgname();
                int ismain = card.getIsmain();

                dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, 11, ismain, usedes, creator, infocode, infoname, orgname, des, modifierid);
                return Json.getJsonResult(true);
            } else {
                return Json.getJsonResult(false, "延期失败");
            }
        } else {
            card = GetCardInfoTxtByResult(card);
            return Json.getJsonResult(false, "当前卡" + card.getStatus() + "，禁止延期");
        }
    }

    public JsonData getPersonList(HttpServletRequest request, HttpServletResponse response, String key, String viewleave,int start, int limit) {
        String fields = "info.uid,info.code,info.name,info.cardsn,info.status";
        String table = "tb_card_teachstudinfo info";
        String where = "1=1 ";
        SysUser user=userredis.get(request);
        if(user.getUsertype()==1){
            String userid=user.getUid();
            table+= " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='"+userid+"' ";
        }
        if(StringUtils.isNotBlank(viewleave) && Integer.parseInt(viewleave) != -1 ) {
            where+=" and info.status="+viewleave+" ";
        }
        if (StringUtils.isNotBlank(key)) {
            where += " and (info.code like '%" + key + "%' or info.name like '%" + key + "%' ";
            if (ComHelper.isNumeric(key)) {
                String cardno = ComHelper.LeftPad(key, 12, '0');
                where += " or exists(select 1 from tb_card_cardinfo where infoid=info.uid and cardsn='" + cardno + "') ";
            }
            where += " ) ";
        }
        String orderby=ExtSort.Orderby(request, "info.code desc");
        JsonData db = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return db;
    }

    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response,
                                String orgcode, String key, String infotype, boolean viewchild, String intoyear, int start, int limit){
        String fields="b.uid,b.code,b.name,b.sex,b.card,b.cardsn,b.orgcode,b.infotype,b.intoyear,b.focus,b.status,getorgname(b.orgcode) as orgname";
        String table="tb_card_teachstudinfo b";
        if(userredis.get(request).getUsertype()==1){
            String userid=userredis.getUserId(request);
            table+= " inner join tb_card_orgframework_user uo on uo.orgcode=b.orgcode and uo.userid='"+userid+"' ";
        }
        String where=" b.status=1 and (not b.cardsn is null and b.cardsn<>'')";
        if(!infotype.equals("0") && StringUtils.isNotBlank(infotype)){
            where+=" and b.infotype="+infotype;
        }
        if(!intoyear.equals("0") && StringUtils.isNotBlank(intoyear)){
            where+=" and b.intoyear="+intoyear;
        }
        if(!StringUtils.isBlank(orgcode)){
            if(viewchild){
                where+=" and b.orgcode like '"+orgcode+"%' ";
            }else{
                where+=" and b.orgcode='"+orgcode+"' ";
            }
        }
        if(!StringUtils.isBlank(key)){
            where+=" and (b.code='"+key+"' or b.name like '%"+key+"%' or b.mobile like '%"+key+"%' or b.card='"+ ComHelper.LeftPad(key, 12, '0')+"' or b.cardsn='"+ComHelper.LeftPad(key, 12, '0')+"' )";
        }
        String orderby= ExtSort.Orderby(request, "b.createdate desc");
        JsonData result=dbService.QueryJsonData( fields, table, where, orderby, start, limit);
        return result;
    }

    /**
     * 获取卡信息是否存在
     */
    public Integer getCardInfoCount(String infoId) {
        return dbService.getCount("tb_card_cardinfo", "infoid='" + infoId + "' and status = 1");
    }
}
