package com.ymiots.campusos.service.card;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dao.BedDao;
import com.ymiots.campusos.dao.CardDao;
import com.ymiots.campusos.dao.FaceRecordDao;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysConfigService;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.campusos.service.alleyway.AlleywayOpnService;
import com.ymiots.campusos.service.face.IntoFaceService;
import com.ymiots.campusos.service.hotel.CheckOutService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.config.SystemEnv;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Null;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

@Repository
public class OrgFrameworkService extends BaseService {

	@Autowired
	SysLogsService syslogs;
	
	@Autowired
	UserRedis userredis;

	@Autowired
	CheckOutService checkOutService;

	@Autowired
	IntoFaceService intoFaceService;

	@Autowired
	FaceRecordDao faceRecordDao;

	@Autowired
	AlleywayOpnService alleywayOpnService;

	@Autowired
	ChangeOrgRecordService changeOrgRecordService;

	@Autowired
	TeachStudInfoService teachStudInfoService;

	@Autowired
	PlatformTransactionManager transactionManager;

	@Autowired
	CardManageService cardmanage;

	@Autowired
	SysConfigService sysconfig;

	@Autowired
	CardDao cardDao;

	@Autowired
	BedDao bedDao;
	
	public JSONArray getDepartmentList(HttpServletRequest request, HttpServletResponse response,String key,String defaultSelectCode,String disviewchild){
		String sql = "select aof.uid as id,aof.uid,aof.code,aof.orgcode,aof.orgtype,aof.opened,aof.name,aof.parentid,aof.status,d.name as orgtypename,getorgname(aof.code) as orgname from tb_card_orgframework aof inner join tb_sys_dictionary d on aof.orgtype=d.code and d.groupcode='SYS0000003' ";
		if (userredis.get(request).getUsertype() == 1) {
			sql += " inner join tb_card_orgframework_user cou on cou.orgcode = aof.code and cou.userid = '"+userredis.getUserId(request)+"'";
		}
		sql+=" where aof.orgtype in('1','2') ";
		if(!StringUtils.isBlank(defaultSelectCode) && disviewchild.equals("1")){
			JSONArray codelist=JSONArray.parseArray(defaultSelectCode);
			String codewhere="";
			for(int i=0;i<codelist.size();i++){
				if(i==0){
					codewhere="aof.code='"+codelist.getString(i)+"'";
				}else{
					codewhere+=" or aof.code='"+codelist.getString(i)+"'";
				}
			}
			sql+=" and ("+codewhere+") ";
		}
		if(!StringUtils.isBlank(key)){
			sql +=" and (aof.name like '%"+key+"%' or aof.code='"+key+"') ";
		}
		sql+=" order by aof.code asc ";
		JSONArray list =dbService.QueryList(sql);
		JSONArray rootlist= TreeHelper.findRootList(list, "parentid", "id");
		for(Object m:rootlist){
			JSONObject item=(JSONObject)m;

			JSONArray children=TreeHelper.getChildren(list, "parentid",item.getString("id"),"id");
			if (children.size() > 0) {
				item.put("leaf", false);
				item.put("children", children);
				item.put("expanded", true);
			}else{
				item.put("leaf", true);
				item.put("children", new JSONArray());
				item.put("expanded", true);
			}
		}
		return rootlist;
	}
	
	public JSONArray getCollegeTree(HttpServletRequest request, HttpServletResponse response,String key){
		String sql = "select co.uid,co.code,co.orgcode,co.orgtype,co.name,co.parentid,co.status,getorgname(co.code) as orgname from tb_card_orgframework co";
		if (userredis.get(request).getUsertype() == 1) {
			sql += " inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid = '" + userredis.getUserId(request) + "' ";
		}
		sql+=" where co.orgtype in('1') ";
		if(!StringUtils.isBlank(key)){
			sql +=" and (co.name like '%"+key+"%'  or co.code='"+key+"') ";
		}
		sql+=" order by co.code asc ";
		JSONArray list =dbService.QueryList(sql);
		JSONArray rootlist= TreeHelper.findRootList(list, "parentid", "uid");
		for(Object m:rootlist){
			JSONObject item=(JSONObject)m;
			JSONArray children=TreeHelper.getChildren(list, "parentid",item.getString("uid"),"uid");
			if (children.size() > 0) {
				item.put("leaf", false);
				item.put("children", children);
				item.put("expanded", true);
			}else{
				item.put("leaf", true);
				item.put("children", new JSONArray());
				item.put("expanded", true);
			}
		}
		return rootlist;
	}

	public JSONArray getCollegeClassTree(HttpServletRequest request, HttpServletResponse response,String key){	
		String sql="select o.uid,o.orgcode,o.code,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname,o.years from tb_card_orgframework o ";
        SysUser user=userredis.get(request);
		if(user.getUsertype()==1){
        	String userid=user.getUid();
        	sql+= " inner join tb_card_orgframework_user uo on uo.orgcode=o.code and uo.userid='"+userid+"' ";
        }
        sql+=" where o.status=1 ";
		if(WebConfig.getApptype().equals("1")) {
			sql+=" and  o.orgtype in('1','3','4') ";
		}else {
			sql+=" and o.orgtype in('1','2','3','4') ";
		}
		
		if(!StringUtils.isBlank(key)){
			sql +=" and (o.name like '%"+key+"%' or o.code='"+key+"')";
		}
		
		sql+=" order by o.code asc ";
		JSONArray list =dbService.QueryList(sql);
		JSONArray rootlist= TreeHelper.findRootList(list, "parentid", "uid");
		for(Object m:rootlist){
			JSONObject item=(JSONObject)m;
			JSONArray children=TreeHelper.getChildren(list, "parentid",item.getString("uid"),"uid");
			if (children.size() > 0) {
				item.put("leaf", false);
				item.put("children", children);
				item.put("expanded", true);
			}else{
				item.put("leaf", true);
				item.put("children", new JSONArray());
				item.put("expanded", true);
			}
		}
		return rootlist;
	}
	
	public JSONArray getCollegeClassCheckTree(HttpServletRequest request, HttpServletResponse response,String key){
		String sql="select o.uid,o.orgcode,o.code,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o ";
        SysUser user=userredis.get(request);
		if(user.getUsertype()==1){
        	String userid=user.getUid();
        	sql+= " inner join tb_card_orgframework_user uo on uo.orgcode=o.code and uo.userid='"+userid+"' ";
        }
		sql+=" where o.status = 1 ";
		if(WebConfig.getApptype().equals("1")) {
			sql+=" and o.orgtype in('1','3','4') ";
		}else {
			sql+=" and o.orgtype in('1','2','3','4') ";
		}
		if(!StringUtils.isBlank(key)){
			sql +=" and (o.name like '%"+key+"%' or o.code='"+key+"')";
		}
		sql+=" order by o.code asc ";
		JSONArray list =dbService.QueryList(sql);
		JSONArray rootlist= TreeHelper.findRootList(list, "parentid", "uid");
		for(Object m:rootlist){
			JSONObject item=(JSONObject)m;
			JSONArray children=TreeHelper.getChildren(list, "parentid",item.getString("uid"),"uid",true,"");
			if (children.size() > 0) {
				item.put("leaf", false);
				item.put("children", children);
				item.put("expanded", true);
				item.put("checked", false);
			}else{
				item.put("leaf", true);
				item.put("children", new JSONArray());
				item.put("expanded", true);
				item.put("checked", false);
			}
		}
		return rootlist;
	}
	
	public JSONArray getOrgTree(HttpServletRequest request, HttpServletResponse response,String key){
		String sql="select o.uid,o.code,o.orgcode,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o  ";
        SysUser user=userredis.get(request);
		if(user.getUsertype()==1){
        	String userid=user.getUid();
        	sql+= " inner join tb_card_orgframework_user uo on uo.orgcode=o.code and uo.userid='"+userid+"' ";
        }
		sql+=" where o.orgtype in('1','2') and o.status=1 ";
		if(!StringUtils.isBlank(key)){
			sql +=" and (o.name like '%"+key+"%' or o.code='"+key+"') ";
		}
		sql+=" order by o.code asc ";
		JSONArray list =dbService.QueryList(sql);
		JSONArray rootlist= TreeHelper.findRootList(list, "parentid", "uid");
		for(Object m:rootlist){
			JSONObject item=(JSONObject)m;
			JSONArray children=TreeHelper.getChildren(list, "parentid",item.getString("uid"),"uid");
			if (children.size() > 0) {
				item.put("leaf", false);
				item.put("children", children);
				item.put("expanded", true);
			}else{
				item.put("leaf", true);
				item.put("children", new JSONArray());
				item.put("expanded", true);
			}
		}
		return rootlist;
	}
	
    public JSONArray getAllOrgTree(HttpServletRequest request, HttpServletResponse response,String key){
        String sql="select o.uid,o.code,o.orgcode,o.orgtype,o.name,o.parentid,o.status,getorgname(o.code) as orgname from tb_card_orgframework o  ";

        SysUser user=userredis.get(request);
        if(user.getUsertype()==1){
            String userid=user.getUid();
            sql+= " inner join tb_card_orgframework_user uo on uo.orgcode=o.code and uo.userid='"+userid+"' ";
        }
        sql+=" where 1=1 and o.status=1";
        if(!StringUtils.isBlank(key)){
            sql +=" and (o.name like '%"+key+"%' or o.code='"+key+"') ";
        }
        sql+=" order by o.code asc ";
        JSONArray list =dbService.QueryList(sql);
        JSONArray rootlist= TreeHelper.findRootList(list, "parentid", "uid");
        for(Object m:rootlist){
            JSONObject item=(JSONObject)m;
            JSONArray children=TreeHelper.getChildren(list, "parentid",item.getString("uid"),"uid");
            if (children.size() > 0) {
                item.put("leaf", false);
                item.put("children", children);
                item.put("expanded", true);
            }else{
                item.put("leaf", true);
                item.put("children", new JSONArray());
                item.put("expanded", true);
            }
        }
        return rootlist;
    }
	
	public JsonResult SaveOrgFramework(HttpServletRequest request, HttpServletResponse response,String uid,String code,String orgcode,String name ,String orgtype,String parentid,String years){
		String sql="";
		if(StringUtils.isBlank(uid)){
			if(parentid.equals("root")){
				parentid="";
			}
			if(StringUtils.isBlank(parentid)){
				parentid="";
			}
			JSONArray list = dbService.QueryList("select code from tb_card_orgframework where parentid=? order by code desc limit 1", parentid);
			if(list.size()==0){
				code+="001";
			}else{
				if(StringUtils.isBlank(parentid)){
					return Json.getJsonResult("此节点数量已达上限");
				}
				String _code=list.getJSONObject(0).getString("code");
				if(!StringUtils.isBlank(code)){
					_code=_code.substring(code.length(), _code.length());
				}
				int codeint=Integer.parseInt(_code)+1;
				if(codeint>999){
					return Json.getJsonResult("此节点数量已达上限");
				}
				code+=String.format("%03d",codeint);
				int count = dbService.getCount("tb_card_orgframework", "code='" + orgcode + "' or name = '" + name + "'");
				if(count>0){
					return Json.getJsonResult("机构代码或名字重复");
				}
			}
			if(!StringUtils.isBlank(orgcode)){
				int count = dbService.getCount("tb_card_orgframework", "orgcode='" + orgcode + "' or name = '" + name + "'");
				if(count>0){
					return Json.getJsonResult("机构代码重复");
				}
			}
			
		
			String userid=userredis.getUserId(request);
			String uuid = UUID.randomUUID().toString();
			sql="insert into tb_card_orgframework(uid,code,orgcode,orgtype,name,parentid,createdate,creatorid,status,years) values(?,?,?,?,?,?,now(),?,1,?)";
			dbService.excuteSql(sql,uuid, code,orgcode,orgtype,name,parentid,userid,years);
			SysUser sysUser = userredis.get(request);
			if (sysUser.getUsertype()==1){
				sql="insert into tb_card_orgframework_user(uid,userid,orgcode,createdate,creatorid) values(uuid(),?,?,now(),?)";
				dbService.excuteSql(sql,sysUser.getUid(),code,sysUser.getUid());
				JSONArray array = dbService.QueryList("select parentid from tb_sys_user where uid = ?",userid);
				if (StringUtils.isNotEmpty(array.getJSONObject(0).getString("parentid"))){
					authorizatParent(code,array.getJSONObject(0).getString("parentid"),userid);
				}
			}
			syslogs.Write(request, "组织架构", String.format("新增组织架构（%s，%s），类型为%s", name,code,orgtype));
		}else{
			if(!StringUtils.isBlank(orgcode)){
				int count=dbService.getCount("tb_card_orgframework", "orgcode='"+orgcode+"' and  uid<>'"+uid+"'");
				if(count>0){
					return Json.getJsonResult("机构代码重复");
				}
			}
			sql="update tb_card_orgframework set orgcode=?,orgtype=?,name=?,years = ? where uid=?";
			dbService.excuteSql(sql, orgcode,orgtype,name,years,uid);
			syslogs.Write(request, "组织架构", String.format("更新组织架构（%s，%s），类型为%s，代码为%s", name,code,orgtype,uid));
		}
		return Json.getJsonResult(true);
	}
	public JsonResult SaveCollectOnOff(HttpServletRequest request, HttpServletResponse response,String uid,String code,String orgcode,String name ,String orgtype,String parentid,String years,String opened){
		String sql="";
		if(StringUtils.isBlank(uid)){
			if(parentid.equals("root")){
				parentid="";
			}
			if(StringUtils.isBlank(parentid)){
				parentid="";
			}
			JSONArray list= dbService.QueryList("select code from tb_card_orgframework where parentid=? order by code desc limit 1", parentid);
			if(list.size()==0){
				code+="001";
			}else{
				String _code=list.getJSONObject(0).getString("code");
				if(!StringUtils.isBlank(code)){
					_code=_code.substring(code.length(), _code.length());
				}
				int codeint=Integer.parseInt(_code)+1;
				if(codeint>999){
					return Json.getJsonResult("此节点数量已达上限");
				}
				code+=String.format("%03d",codeint);
			}
			if(!StringUtils.isBlank(orgcode)){
				int count=dbService.getCount("tb_card_orgframework", "orgcode='"+orgcode+"'");
				if(count>0){
					return Json.getJsonResult("机构代码重复");
				}
			}


			String userid=userredis.getUserId(request);
			String uuid = UUID.randomUUID().toString();
			sql="insert into tb_card_orgframework(uid,code,orgcode,orgtype,name,parentid,createdate,creatorid,status,years,opened) values(?,?,?,?,?,?,now(),?,1,?,?)";
			dbService.excuteSql(sql,uuid, code,orgcode,orgtype,name,parentid,userid,years,opened);
			SysUser sysUser = userredis.get(request);
			if (sysUser.getUsertype()==1){
				sql="insert into tb_card_orgframework_user(uid,userid,orgcode,createdate,creatorid) values(uuid(),?,?,now(),?)";
				dbService.excuteSql(sql,sysUser.getUid(),code,sysUser.getUid());
				JSONArray array = dbService.QueryList("select parentid from tb_sys_user where uid = ?",userid);
				if (StringUtils.isNotEmpty(array.getJSONObject(0).getString("parentid"))){
					authorizatParent(code,array.getJSONObject(0).getString("parentid"),userid);
				}
			}
			syslogs.Write(request, "组织架构", String.format("新增组织架构（%s，%s），类型为%s", name,code,orgtype));
		}else{
			if(!StringUtils.isBlank(orgcode)){
				int count=dbService.getCount("tb_card_orgframework", "orgcode='"+orgcode+"' and  uid<>'"+uid+"'");
				if(count>0){
					return Json.getJsonResult("机构代码重复");
				}
			}
			sql="update tb_card_orgframework set orgcode=?,orgtype=?,name=?,years = ?,opened =? where uid=?";
			dbService.excuteSql(sql, orgcode,orgtype,name,years,opened,uid);
			syslogs.Write(request, "开启或者关闭收集信息", String.format("更新组织架构（%s，%s），修改为%s", name,code,opened));
		}
		return Json.getJsonResult(true);
	}
	public void authorizatParent(String code,String uid,String creatorid){

		boolean flag = true;
		while (flag){
			dbService.excuteSql("insert into tb_card_orgframework_user(uid,userid,orgcode,createdate,creatorid) values(uuid(),?,?,now(),?)",uid,code,creatorid);
			JSONObject objects = dbService.QueryJSONObject("select uid,parentid from tb_sys_user where uid = ?",uid);
			if (objects!=null){
				if (StringUtils.isNotEmpty(objects.getString("parentid"))){
					uid = objects.getString("parentid");
				}else {
					flag =false;
				}
			}
		}
	}
	public JSONArray getSpecialtyList(HttpServletRequest request, HttpServletResponse response,String orgcode,String key){
		//if(StringUtils.isBlank(orgcode)){
		//	return new JSONArray();
		//}
		String fields="aof.uid,aof.code,aof.orgcode,aof.orgtype,aof.name,aof.parentid,aof.status,d.name as orgtypename,getorgname(aof.code) as orgname,aof.years";
		String table="tb_card_orgframework aof inner join tb_sys_dictionary d on aof.orgtype=d.code and d.groupcode='SYS0000003'";
		String where=" aof.orgtype in('3','4')  and aof.status=1 ";
		if(!StringUtils.isBlank(orgcode)){
			where +=" and aof.code like '"+orgcode+"%' ";
		}
		if(!StringUtils.isBlank(key)){
			where +=" and (aof.name like '%"+key+"%' or aof.code='"+key+"') ";
		}
		String orderby=ExtSort.Orderby(request, " aof.code asc ");
		JSONArray list =dbService.QueryJSONArray(fields, table, where, orderby, 0, 0);
		JSONArray rootlist= TreeHelper.findRootList(list, "parentid", "uid");
		for(Object m:rootlist){
			JSONObject item=(JSONObject)m;
			JSONArray children=TreeHelper.getChildren(list, "parentid",item.getString("uid"),"uid");
			if (children.size() > 0) {
				item.put("leaf", false);
				item.put("children", children);
				item.put("expanded", true);
			}else{
				item.put("leaf", true);
				item.put("children", new JSONArray());
				item.put("expanded", true);
			}
		}
		return rootlist;
	}
	
	
	public JsonResult DelDepartment(HttpServletRequest request, HttpServletResponse response,String uid){
		int count= dbService.getCount("tb_card_orgframework", " parentid='"+uid+"'");
		if(count>0){
			return Json.getJsonResult("存在子节点，禁止删除");
		}
		
		count= dbService.getCount("tb_card_teachstudinfo", "orgcode in(select code from tb_card_orgframework where uid='"+uid+"' )");
		if(count>0){
			return Json.getJsonResult("存在教职工信息，禁止删除");
		}
		

		String code = dbService.QueryJSONObject("select code from tb_card_orgframework where uid='" + uid + "'").getString("code");
		dbService.excuteSql("delete from tb_card_orgframework where uid=?", uid);
		dbService.excuteSql("delete from tb_card_orgframework_user where uid=? and orgcode=?", uid,code);
		syslogs.Write(request, "组织架构", String.format("删除院系部门（%s）", uid));
		return Json.getJsonResult(true);
	}

	public JsonResult EditDepartment(HttpServletRequest request, HttpServletResponse response,String uid,String status){
		if ("N".equals(status)){
			status = "Y";
		}else {
			status = "N";
		}
		String sql="update tb_card_orgframework set opened =? where uid=?";
		dbService.excuteSql(sql,status,uid);
		syslogs.Write(request, "组织架构", String.format("更改VIP部门（%s）", uid));
		return Json.getJsonResult(true);
	}
	
	public JsonResult DelSpecialty(HttpServletRequest request, HttpServletResponse response,String uid){
		int count= dbService.getCount("tb_card_orgframework", " parentid='"+uid+"'");
		if(count>0){
			return Json.getJsonResult("存在子节点，禁止删除");
		}
		dbService.excuteSql("delete from tb_card_orgframework where uid=?", uid);
		syslogs.Write(request, "组织架构", String.format("删除专业（%s）", uid));
		return Json.getJsonResult(true);
	}

	public JsonResult CopySpecialty(HttpServletRequest request, HttpServletResponse response,String uid,String xname,String rname,String replacename,String parentid,String necode){
		JSONArray jsonArray = dbService.QueryList("select uid,code,orgtype,parentid,name from tb_card_orgframework where parentid = ?", uid);
		String userid = userredis.get(request).getUid();
		JSONObject department = dbService.QueryJSONObject(" select code,orgtype,name,parentid from tb_card_orgframework where uid = ?", uid);
		JSONObject parentDepartment = dbService.QueryJSONObject(" select code,parentid from tb_card_orgframework where uid = ?", department.getString("parentid"));
		JSONObject maxCodeDepartment = dbService.QueryJSONObject(" select code,parentid from tb_card_orgframework where parentid = ? order by code desc limit 1", department.getString("parentid"));


		String _code=maxCodeDepartment.getString("code");
		String code = parentDepartment.getString("code");
		if(!StringUtils.isBlank(code)){
			_code=_code.substring(code.length(), _code.length());
		}
		int codeint=Integer.parseInt(_code)+1;
		if(codeint>999){
			return Json.getJsonResult("此节点数量已达上限");
		}
		code+=String.format("%03d",codeint);
		if (StringUtils.isNotEmpty(necode)){
			code = necode;
		}
		String uuid = java.util.UUID.randomUUID().toString();
		String inertsql = "insert into tb_card_orgframework(uid,code,name,orgtype,parentid,createdate,creatorid,status) values(?,?,?,?,?,now(),?,1)";
		if (StringUtils.isNotEmpty(parentid)){
			dbService.excuteSql(inertsql,uuid,code,xname,department.getInteger("orgtype"),parentid,userid);
		}else {
			dbService.excuteSql(inertsql,uuid,code,xname,department.getInteger("orgtype"),department.getString("parentid"),userid);
		}

		if (jsonArray.size()>0){
			for (int i = 0; i < jsonArray.size(); i++) {
				JSONObject object = jsonArray.getJSONObject(i);
				JSONObject department1 = dbService.QueryJSONObject(" select code,orgtype,name,parentid from tb_card_orgframework where uid = ?", uid);
				String newcode =object.getString("code").replace(department1.getString("code"),code);
				String newname ="";
				if (StringUtils.isEmpty(replacename)){
					replacename =xname;
				}
				if (StringUtils.isEmpty(rname)){
					rname =department1.getString("name");
				}
				newname =object.getString("name").replace(rname,replacename);

				JSONArray array = dbService.QueryList("select uid,code,orgtype,parentid,name from tb_card_orgframework where parentid = ?", object.getString("uid"));
				if (array.size()>0){
					CopySpecialty(request,response,object.getString("uid"),newname,rname,replacename,uuid,newcode);
				}else {
					dbService.excuteSql(inertsql,UUID.randomUUID().toString(),newcode,newname,object.getInteger("orgtype"),uuid,userid);
				}
			}
		}
		return Json.getJsonResult(true);
	}

	public R<?> getGrade() {
		//获取当前年份
		LocalDate now = LocalDate.now();
		int year = now.getYear();
		//得出近10年的年份
		List<Map<String,Integer>> years = new ArrayList<>();
		for (int i = 0; i < 10; i++) {
			HashMap<String,Integer> yearMap = new HashMap<>();
			yearMap.put("years",year-i);
			years.add(yearMap);
		}
		return R.ok(years);
	}

	public R<Null> setGrade(String uid) {
		for (String s : uid.split(",")) {
			dbService.excuteSql("update tb_card_orgframework set status = ? where uid=?", 0,s);
		}
		return R.ok("设置成功");
	}

	public JsonData getGradeList(HttpServletRequest request,String years, String key,
								 String viewleave, String infolabel, String property, int start, int limit) {
		String sql = "select code from tb_card_orgframework where status = 0 ";
		if (!StringUtils.isBlank(years)){
			sql += " and years = '"+years+"'";
		}
		List<String> uidList = dbService.queryFields(sql, String.class);
		StringJoiner joiner = new StringJoiner("','", "('", "')");
		for (String s : uidList) {
			joiner.add(s);
		}
		String fields = "uid,code,name,sex,card,date_format(startdate,'%Y-%m-%d') as startdate,date_format(enddate,'%Y-%m-%d') as enddate," +
				"cardsn,mobile,orgcode,getorgname(orgcode) as orgname,nation,birthday,address,idcard,infotype,intoyear,linkman1," +
				"linkmobile1,linkdes1,linkman2,linkmobile2,linkdes2,focus,status,infolabel,property";
		String table = "tb_card_teachstudinfo";
		String where = "infotype=2 and orgcode in " + joiner;
		if (!StringUtils.isBlank(key)) {
			String cardno = ComHelper.LeftPad(key, 12, '0');
			where += " and (code='" + key + "' or name like '%" + key + "%' or mobile like '%" + key + "%' or  card='" + cardno + "' or cardsn='" + cardno + "' )";
		}
		if (StringUtils.isNotBlank(viewleave) && viewleave.equals("-1")) {
			table = " tb_card_teachstudinfo_sleep ";
			where += " and status=" + viewleave + " ";
		}
		if (StringUtils.isNotBlank(property) && !"-1".equals(property)) {
			where += " and property=" + property + " ";
		}
		if (StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)) {
			where += " and infolabel = '" + infolabel + "' ";
		}
		String orderby = ExtSort.Orderby(request, "createdate desc");
		JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}


	public JsonResult revoke(HttpServletRequest request,HttpServletResponse response, String infoids) {
		if (StringUtils.isBlank(infoids)) {
			return Json.getJsonResult(false, "没有正确选择人员");
		}
		ExecutorService threadPoolExecutor = Executors.newFixedThreadPool(SystemEnv.IO);
		List<CompletableFuture<Void>> futures = new ArrayList<>();
		List<String> failInfoIds = new CopyOnWriteArrayList<>();
		for (String infoId : infoids.split(",")) {
			CompletableFuture<Void> future = CompletableFuture.runAsync(
					() -> {
						JsonResult jsonResult = delTeachStudInfo(request, response, infoId);
						if (!jsonResult.isSuccess()) {
							failInfoIds.add(infoId);
						}
					},
					threadPoolExecutor
			);
			futures.add(future);
		}
		CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
				.join();
		int failInfoSize = failInfoIds.size();
		if (failInfoSize == futures.size()) {
			return Json.getJsonResult(false, "所选人员全都删除失败,数据存在引用");
		}
		if (failInfoSize == 0) {
			return Json.getJsonResult(true, "权限删除成功");
		}
		return Json.getJsonResult(true, "所选人员权限部分删除成功,数据存在引用");
	}

	public JsonResult delStudInfo(HttpServletRequest request, HttpServletResponse response, String infoid) {
		if (StringUtils.isBlank(infoid)) {
			return Json.getJsonResult(false, "没有正确选择人员");
		}
		return teachStudInfoService.delBatchTeachStudInfo(request, response, infoid);
	}

	public JsonResult delTeachStudInfo(HttpServletRequest request, HttpServletResponse response, String infoid) {
		DefaultTransactionDefinition def = new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
		TransactionStatus status = transactionManager.getTransaction(def);
		try {
			// 删除人脸记录
			faceRecordDao.delRecord(infoid);
			// 删除门禁关联
			alleywayOpnService.delPerson(infoid);
//			// 退卡
//			String cardId = cardDao.getCardIdByInfoNo(infoid);
//			JsonResult jsonResult = cardmanage.SaveRevokeReturnCard(request, response, cardId, null, true);
//			if (!jsonResult.isSuccess()){
//				return Json.getJsonResult(jsonResult.getMsg());
//			}
			// 退床
			List<String> bedIds = bedDao.getBedIdsByInfoId(infoid);
			for (String bedId : bedIds) {
				checkOutService.SaveCheckOut(request, response, infoid, bedId);
			}
			// 删除人脸
			intoFaceService.clearFace(request, response, infoid);
			syslogs.Write(request, "职工信息", String.format("收回 职工/学生 权限（%s）", infoid));
			transactionManager.commit(status);
		} catch (Exception e) {
			transactionManager.rollback(status);
			syslogs.Write(request, "职工信息", "删除 职工/学生 信息失败 infoId: " + infoid);
			e.printStackTrace();
			return Json.getJsonResult(false, "删除 职工/学生 信息失败 infoId: " + infoid);
		}
		return Json.getJsonResult(true);
	}
}

