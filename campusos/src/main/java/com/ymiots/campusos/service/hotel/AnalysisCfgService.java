package com.ymiots.campusos.service.hotel;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.DBService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;

@Repository
public class AnalysisCfgService extends BaseService{

	public JsonResult getAnalysisCfg(HttpServletRequest request, HttpServletResponse response){
		JSONArray list = dbService.QueryList("select night_checkin_time as nightcheckintime,night_checkin_start_time as nightcheckinstarttime,night_checkin_end_time as nightcheckinendtime,morning_departure as morningdeparture,morning_departure_start_time as morningdeparturestarttime,morning_departure_end_time as morningdepartureendtime,noon_checkin_time as nooncheckintime,noon_checkin_start_time as nooncheckinstarttime,noon_checkin_end_time as nooncheckinendtime,noon_departure as noondeparture,noon_departure_start_time as noondeparturestarttime,noon_departure_end_time as noondepartureendtime from tb_hotel_analysiscfg");
		if(list.size()>0){
			return Json.getJsonResult(true,"",list.getJSONObject(0));
		}else{
			JSONObject item=new JSONObject();
			return Json.getJsonResult(true,"",item);
		}
	}

	public JsonResult SaveAnalysisCfg(HttpServletRequest request, HttpServletResponse response, String morningDeparture, String morningDepartureStartTime,
									  String morningDepartureEndTime, String noonCheckinTime, String noonCheckinStartTime, String noonCheckinEndTime, String noonDeparture,
									  String noonDepartureStartTime, String noonDepartureEndTime, String nightCheckinTime, String nightCheckinStartTime, String nightCheckinEndTime) {
		int count = dbService.getCount("tb_hotel_analysiscfg", "");
		if (count > 0) {
			String sql = "update tb_hotel_analysiscfg set night_checkin_time = ?, night_checkin_start_time = ?,night_checkin_end_time = ? , morning_departure = ?,morning_departure_start_time = ?,morning_departure_end_time = ?,noon_checkin_time = ?,noon_checkin_start_time = ?,noon_checkin_end_time = ?,noon_departure = ?,noon_departure_start_time = ?,noon_departure_end_time = ?";
			dbService.excuteSql(sql, nightCheckinTime, nightCheckinStartTime, nightCheckinEndTime, morningDeparture, morningDepartureStartTime, morningDepartureEndTime, noonCheckinTime, noonCheckinStartTime, noonCheckinEndTime, noonDeparture, noonDepartureStartTime, noonDepartureEndTime);
		} else {
			String sql = "insert into tb_hotel_analysiscfg(uid,night_checkin_time,night_checkin_start_time,night_checkin_end_time,morning_departure,morning_departure_start_time,morning_departure_end_time,noon_checkin_time,noon_checkin_start_time,noon_checkin_end_time,noon_departure,noon_departure_start_time,noon_departure_end_time) values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?)";
			dbService.excuteSql(sql, nightCheckinTime, nightCheckinStartTime, nightCheckinEndTime, morningDeparture, morningDepartureStartTime, morningDepartureEndTime, noonCheckinTime, noonCheckinStartTime, noonCheckinEndTime, noonDeparture, noonDepartureStartTime, noonDepartureEndTime);
		}
		return Json.getJsonResult(true);
	}
}
