package com.ymiots.campusos.service.consume;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.ExcelUtil;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.JsonData;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class AnalysisEverydayService extends BaseService {

    @Autowired
    UserRedis userRedis;

    public JsonData getEvertdayConsumeData(HttpServletRequest request, HttpServletResponse response, String starttime, String endtime, int start, int limit) {
        String schemeSQL = "SELECT DISTINCT code FROM tb_consume_scheme";
        JSONArray schemeja = dbService.QueryList(schemeSQL);
        String field = "";
        if (!schemeja.isEmpty()) {
            for (int i = 0; i < schemeja.size(); i++) {
                String code = schemeja.getJSONObject(i).getString("code");
                field += "SUM(scheme" + code + ") as scheme" + code + ",SUM(schemetime" + code + ") as schemetime" + code + ",";
            }
        }

        String sql = "SELECT date_format(capr.daily, '%Y-%m-%d') as daily," + field + "SUM(capr.schememoney) as schememoney,SUM(capr.schemetimes) as schemetimes " +
                "FROM tb_consume_analysis_person_rate capr ";

        SysUser sysUser = userRedis.get(request);
        if (sysUser.getUsertype() == 1) {
            String userid = sysUser.getUid();
            sql += " inner join tb_card_orgframework co on co.code=capr.orgcode " +
                    "inner join tb_card_orgframework_user cou on cou.orgcode=co.code " +
                    "and cou.userid = '" + userid + "'";
        }
        String where = " capr.daily BETWEEN '" + starttime + "' AND '" + endtime + "' ";
        sql += " WHERE"+ where;
        String countsql = sql;
        if (start == 0 && limit == 0) {
            sql += "GROUP BY capr.daily ORDER BY " + ExtSort.Orderby(request, "capr.daily ASC");
        } else {
            sql += "GROUP BY capr.daily ORDER BY " + ExtSort.Orderby(request, "capr.daily ASC") + " limit " + start + "," + limit;
        }
        String table = "tb_consume_analysis_person_rate capr";
        if (sysUser.getUsertype() == 1) {
            String userid = sysUser.getUid();
            table += " inner join tb_card_orgframework co on co.code=capr.orgcode " +
                    "inner join tb_card_orgframework_user cou on cou.orgcode=co.code " +
                    "and cou.userid = '" + userid + "'";
        }
        JsonData data = dbService.QueryJsonData(countsql + "GROUP BY capr.daily ORDER BY " + ExtSort.Orderby(request, "capr.daily ASC"));
        JSONArray list=getSumConsume("daily", table,"WHERE"+ where);
        JsonData result = dbService.QueryJsonData(sql);
        result.getData().addAll(list);
        result.setTotal(data.getData().size());
        return result;
    }

    public JsonData getSchemeName(HttpServletRequest request, HttpServletResponse response) {
        String sql = "SELECT DISTINCT sc.code,d.name FROM tb_consume_scheme sc LEFT JOIN tb_sys_dictionary d ON d.code=sc.code and d.groupcode='SYS0000051'  ORDER BY code ASC";
        return dbService.QueryJsonData(sql);
    }

    public String ExportEverydayConsume(HttpServletRequest request, HttpServletResponse response, String starttime, String endtime,String currentPage,String pageSize,String size) throws IOException {
        String schemeSQL = "SELECT code,name FROM tb_consume_scheme ORDER BY code ASC";
        JSONArray schemeja = dbService.QueryList(schemeSQL);
        List<String> schemeCode = new ArrayList<String>();
        List<String> schemeName = new ArrayList<String>();
        List<String> schemetimeCode = new ArrayList<String>();
        if (!schemeja.isEmpty()) {
            for (int i = 0; i < schemeja.size(); i++) {
                String code = schemeja.getJSONObject(i).getString("code");
                schemeCode.add("scheme" + code);
                schemetimeCode.add("schemetime"+code);
                schemeName.add(schemeja.getJSONObject(i).getString("name"));
            }
        }
        int star = 1;
        JsonData result = new JsonData();
        if (currentPage.equals("1")) {
            result = getEvertdayConsumeData(request, response, starttime, endtime, 0, Integer.valueOf(size));
        } else if (Integer.valueOf(currentPage) > star) {
            result = getEvertdayConsumeData(request, response, starttime, endtime, (Integer.valueOf(currentPage)-1)*Integer.valueOf(size), Integer.valueOf(size));
        } else {
            result = getEvertdayConsumeData(request, response, starttime, endtime, 0, 0);
        }
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "日期");
        cell.put("datakey", "daily");
        cell.put("size", 10);
        cells.add(cell);

        for (int i = 0; i < schemeCode.size(); i++) {
            cell = new JSONObject();
            cell.put("name", schemeName.get(i));
            cell.put("datakey", schemeCode.get(i));
            cell.put("size", 10);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", schemeName.get(i)+"次数");
            cell.put("datakey", schemetimeCode.get(i));
            cell.put("size", 10);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "合计金额");
        cell.put("datakey", "schememoney");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "合计次数");
        cell.put("datakey", "schemetimes");
        cell.put("size", 15);
        cells.add(cell);

        List<CellRangeAddress> slist = new ArrayList<CellRangeAddress>();
        List<JSONArray> heads = new ArrayList<JSONArray>();
        String title= "每日消费汇总";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
		String filename =  title+format + ".xlsx";
        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename, title,slist, heads,starttime,endtime,"1","1");
        return filepath;

    }

    public JSONArray getSumConsume(String file,String table,String where){
        String sql="select '合计' as '"+file+"',ifnull(sum(scheme1),0) as scheme1,ifnull(sum(schemetime1),0) as schemetime1,ifnull(sum(scheme2),0) as scheme2," +
                "ifnull(sum(schemetime2),0) as schemetime2,ifnull(sum(scheme3),0) as scheme3,ifnull(sum(schemetime3),0) as schemetime3,ifnull(sum(scheme4),0) as scheme4," +
                "ifnull(sum(schemetime4),0) as schemetime4,ifnull(sum(scheme5),0) as scheme5,ifnull(sum(schemetime5),0) as schemetime5,ifnull(sum(scheme6),0) as scheme6," +
                "ifnull(sum(schemetime6),0) as schemetime6,ifnull(sum(scheme7),0) as scheme7,ifnull(sum(schemetime7),0) as schemetime7,ifnull(sum(scheme8),0) as scheme8," +
                "ifnull(sum(schemetime8),0) as schemetime8,ifnull(sum(scheme9),0) as scheme9,ifnull(sum(schemetime9),0) as schemetime9,ifnull(sum(scheme10),0) as scheme10," +
                "ifnull(sum(schemetime10),0) as schemetime10,ifnull(sum(schememoney),0) as schememoney,ifnull(sum(schemetimes),0) as schemetimes from "+table+" "+where;
        return dbService.QueryList(sql);
    }
}
