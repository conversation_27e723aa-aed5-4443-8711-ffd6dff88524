package com.ymiots.campusos.service.equipment;

import com.ymiots.campusos.common.BaseService;
import com.ymiots.framework.common.Log;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 设备读数记录服务
 * 处理设备读数的保存、查询和计算
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Service
public class EquipmentReadingService extends BaseService {

    /**
     * 保存设备读数记录（包含上次读数的尖峰平谷）
     * @param readingData 读数数据
     * @return 保存结果
     */
    public boolean saveEquipmentReading(Map<String, Object> readingData) {
        try {
            String equipmentCode = (String) readingData.get("equipment_code");
            Integer equipmentType = (Integer) readingData.get("equipment_type");
            Boolean isTimeSharing = (Boolean) readingData.get("is_time_sharing");
            
            Log.info(EquipmentReadingService.class, 
                String.format("保存设备读数 - 设备编号: %s, 类型: %d, 分时表: %s", 
                    equipmentCode, equipmentType, isTimeSharing));

            // 1. 获取上次读数记录
            Map<String, Object> previousRecord = getPreviousReading(equipmentCode);
            
            // 2. 设置上次读数字段
            if (previousRecord != null) {
                readingData.put("previous_reading", previousRecord.get("current_reading"));
                readingData.put("previous_reading_time", previousRecord.get("reading_time"));
                
                // 如果是分时电表，设置上次分时读数
                if (equipmentType == 1 && isTimeSharing) {
                    readingData.put("previous_tip_reading", previousRecord.get("tip_reading"));
                    readingData.put("previous_peak_reading", previousRecord.get("peak_reading"));
                    readingData.put("previous_flat_reading", previousRecord.get("flat_reading"));
                    readingData.put("previous_valley_reading", previousRecord.get("valley_reading"));
                }
            }
            
            // 3. 计算用量
            calculateUsage(readingData, equipmentType, isTimeSharing);
            
            // 4. 计算费用
            calculateCost(readingData, equipmentType, isTimeSharing);
            
            // 5. 保存到数据库
            String sql = buildInsertSql();
            int result = jdbcTemplate.update(sql, buildInsertParams(readingData));
            
            Log.info(EquipmentReadingService.class, 
                String.format("设备读数保存完成 - 影响行数: %d", result));
            
            return result > 0;
            
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, 
                String.format("保存设备读数异常: %s", e.getMessage()), e);
            return false;
        }
    }

    /**
     * 获取设备的上次读数记录
     * @param equipmentCode 设备编号
     * @return 上次读数记录
     */
    private Map<String, Object> getPreviousReading(String equipmentCode) {
        try {
            String sql = "SELECT * FROM tb_equipment_reading_records " +
                        "WHERE equipment_code = ? AND status = 1 " +
                        "ORDER BY reading_time DESC LIMIT 1";
            
            List<Map<String, Object>> records = jdbcTemplate.queryForList(sql, equipmentCode);
            return records.isEmpty() ? null : records.get(0);
            
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, 
                String.format("获取上次读数异常: %s", e.getMessage()), e);
            return null;
        }
    }

    /**
     * 计算用量
     * @param readingData 读数数据
     * @param equipmentType 设备类型
     * @param isTimeSharing 是否分时表
     */
    private void calculateUsage(Map<String, Object> readingData, Integer equipmentType, Boolean isTimeSharing) {
        try {
            BigDecimal currentReading = (BigDecimal) readingData.get("current_reading");
            BigDecimal previousReading = (BigDecimal) readingData.get("previous_reading");
            
            // 计算总用量
            if (currentReading != null && previousReading != null) {
                BigDecimal totalUsage = currentReading.subtract(previousReading);
                readingData.put("usage_amount", totalUsage);
            }
            
            // 如果是分时电表，计算各时段用量
            if (equipmentType == 1 && isTimeSharing) {
                calculateTimeSharingUsage(readingData);
            }
            
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, 
                String.format("计算用量异常: %s", e.getMessage()), e);
        }
    }

    /**
     * 计算分时用量
     * @param readingData 读数数据
     */
    private void calculateTimeSharingUsage(Map<String, Object> readingData) {
        // 尖时用量
        BigDecimal tipCurrent = (BigDecimal) readingData.get("tip_reading");
        BigDecimal tipPrevious = (BigDecimal) readingData.get("previous_tip_reading");
        if (tipCurrent != null && tipPrevious != null) {
            readingData.put("tip_usage", tipCurrent.subtract(tipPrevious));
        }
        
        // 峰时用量
        BigDecimal peakCurrent = (BigDecimal) readingData.get("peak_reading");
        BigDecimal peakPrevious = (BigDecimal) readingData.get("previous_peak_reading");
        if (peakCurrent != null && peakPrevious != null) {
            readingData.put("peak_usage", peakCurrent.subtract(peakPrevious));
        }
        
        // 平时用量
        BigDecimal flatCurrent = (BigDecimal) readingData.get("flat_reading");
        BigDecimal flatPrevious = (BigDecimal) readingData.get("previous_flat_reading");
        if (flatCurrent != null && flatPrevious != null) {
            readingData.put("flat_usage", flatCurrent.subtract(flatPrevious));
        }
        
        // 谷时用量
        BigDecimal valleyCurrent = (BigDecimal) readingData.get("valley_reading");
        BigDecimal valleyPrevious = (BigDecimal) readingData.get("previous_valley_reading");
        if (valleyCurrent != null && valleyPrevious != null) {
            readingData.put("valley_usage", valleyCurrent.subtract(valleyPrevious));
        }
    }

    /**
     * 计算费用
     * @param readingData 读数数据
     * @param equipmentType 设备类型
     * @param isTimeSharing 是否分时表
     */
    private void calculateCost(Map<String, Object> readingData, Integer equipmentType, Boolean isTimeSharing) {
        try {
            if (equipmentType == 1 && isTimeSharing) {
                // 分时电表计算
                calculateTimeSharingCost(readingData);
            } else {
                // 非分时表计算
                calculateNormalCost(readingData);
            }
            
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, 
                String.format("计算费用异常: %s", e.getMessage()), e);
        }
    }

    /**
     * 计算分时费用
     * @param readingData 读数数据
     */
    private void calculateTimeSharingCost(Map<String, Object> readingData) {
        BigDecimal tipUsage = (BigDecimal) readingData.get("tip_usage");
        BigDecimal peakUsage = (BigDecimal) readingData.get("peak_usage");
        BigDecimal flatUsage = (BigDecimal) readingData.get("flat_usage");
        BigDecimal valleyUsage = (BigDecimal) readingData.get("valley_usage");
        
        BigDecimal tipPrice = (BigDecimal) readingData.get("tip_price");
        BigDecimal peakPrice = (BigDecimal) readingData.get("peak_price");
        BigDecimal flatPrice = (BigDecimal) readingData.get("flat_price");
        BigDecimal valleyPrice = (BigDecimal) readingData.get("valley_price");
        
        BigDecimal tipCost = BigDecimal.ZERO;
        BigDecimal peakCost = BigDecimal.ZERO;
        BigDecimal flatCost = BigDecimal.ZERO;
        BigDecimal valleyCost = BigDecimal.ZERO;
        
        if (tipUsage != null && tipPrice != null) {
            tipCost = tipUsage.multiply(tipPrice);
            readingData.put("tip_cost", tipCost);
        }
        
        if (peakUsage != null && peakPrice != null) {
            peakCost = peakUsage.multiply(peakPrice);
            readingData.put("peak_cost", peakCost);
        }
        
        if (flatUsage != null && flatPrice != null) {
            flatCost = flatUsage.multiply(flatPrice);
            readingData.put("flat_cost", flatCost);
        }
        
        if (valleyUsage != null && valleyPrice != null) {
            valleyCost = valleyUsage.multiply(valleyPrice);
            readingData.put("valley_cost", valleyCost);
        }
        
        // 计算总费用
        BigDecimal totalCost = tipCost.add(peakCost).add(flatCost).add(valleyCost);
        readingData.put("total_time_sharing_cost", totalCost);
    }

    /**
     * 计算非分时费用
     * @param readingData 读数数据
     */
    private void calculateNormalCost(Map<String, Object> readingData) {
        BigDecimal usage = (BigDecimal) readingData.get("usage_amount");
        BigDecimal unitPrice = (BigDecimal) readingData.get("unit_price");
        
        if (usage != null && unitPrice != null) {
            BigDecimal totalCost = usage.multiply(unitPrice);
            readingData.put("total_cost", totalCost);
        }
    }

    /**
     * 构建插入SQL
     * @return SQL语句
     */
    private String buildInsertSql() {
        return "INSERT INTO tb_equipment_reading_records (" +
               "uid, equipment_code, equipment_type, reading_date, reading_time, " +
               "current_reading, previous_reading, usage_amount, current_balance, unit_price, " +
               "tip_reading, peak_reading, flat_reading, valley_reading, " +
               "previous_tip_reading, previous_peak_reading, previous_flat_reading, previous_valley_reading, " +
               "tip_usage, peak_usage, flat_usage, valley_usage, previous_reading_time, " +
               "tip_price, peak_price, flat_price, valley_price, " +
               "tip_cost, peak_cost, flat_cost, valley_cost, total_time_sharing_cost, " +
               "is_time_sharing, area_code, status, create_date, creator_id, remark" +
               ") VALUES (" +
               "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
               "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?" +
               ")";
    }

    /**
     * 构建插入参数
     * @param readingData 读数数据
     * @return 参数数组
     */
    private Object[] buildInsertParams(Map<String, Object> readingData) {
        return new Object[]{
            UUID.randomUUID().toString(),
            readingData.get("equipment_code"),
            readingData.get("equipment_type"),
            readingData.get("reading_date"),
            readingData.get("reading_time"),
            readingData.get("current_reading"),
            readingData.get("previous_reading"),
            readingData.get("usage_amount"),
            readingData.get("current_balance"),
            readingData.get("unit_price"),
            readingData.get("tip_reading"),
            readingData.get("peak_reading"),
            readingData.get("flat_reading"),
            readingData.get("valley_reading"),
            readingData.get("previous_tip_reading"),
            readingData.get("previous_peak_reading"),
            readingData.get("previous_flat_reading"),
            readingData.get("previous_valley_reading"),
            readingData.get("tip_usage"),
            readingData.get("peak_usage"),
            readingData.get("flat_usage"),
            readingData.get("valley_usage"),
            readingData.get("previous_reading_time"),
            readingData.get("tip_price"),
            readingData.get("peak_price"),
            readingData.get("flat_price"),
            readingData.get("valley_price"),
            readingData.get("tip_cost"),
            readingData.get("peak_cost"),
            readingData.get("flat_cost"),
            readingData.get("valley_cost"),
            readingData.get("total_time_sharing_cost"),
            readingData.get("is_time_sharing"),
            readingData.get("area_code"),
            1, // status
            new Date(),
            readingData.get("creator_id"),
            readingData.get("remark")
        };
    }

    /**
     * 查询设备读数记录列表
     * @param params 查询参数
     * @param start 分页开始
     * @param limit 分页大小
     * @return 记录列表
     */
    public List<Map<String, Object>> getReadingRecords(Map<String, Object> params, int start, int limit) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT * FROM tb_equipment_reading_records WHERE status = 1 ");
            
            List<Object> paramList = new ArrayList<>();
            
            if (params.get("equipment_code") != null) {
                sql.append("AND equipment_code = ? ");
                paramList.add(params.get("equipment_code"));
            }
            
            if (params.get("equipment_type") != null) {
                sql.append("AND equipment_type = ? ");
                paramList.add(params.get("equipment_type"));
            }
            
            if (params.get("start_date") != null) {
                sql.append("AND reading_date >= ? ");
                paramList.add(params.get("start_date"));
            }
            
            if (params.get("end_date") != null) {
                sql.append("AND reading_date <= ? ");
                paramList.add(params.get("end_date"));
            }
            
            sql.append("ORDER BY reading_time DESC LIMIT ?, ?");
            paramList.add(start);
            paramList.add(limit);
            
            return jdbcTemplate.queryForList(sql.toString(), paramList.toArray());
            
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, 
                String.format("查询设备读数记录异常: %s", e.getMessage()), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询设备读数记录总数
     * @param params 查询参数
     * @return 总记录数
     */
    public int getReadingRecordsCount(Map<String, Object> params) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT COUNT(*) FROM tb_equipment_reading_records WHERE status = 1 ");
            
            List<Object> paramList = new ArrayList<>();
            
            if (params.get("equipment_code") != null) {
                sql.append("AND equipment_code = ? ");
                paramList.add(params.get("equipment_code"));
            }
            
            if (params.get("equipment_type") != null) {
                sql.append("AND equipment_type = ? ");
                paramList.add(params.get("equipment_type"));
            }
            
            if (params.get("start_date") != null) {
                sql.append("AND reading_date >= ? ");
                paramList.add(params.get("start_date"));
            }
            
            if (params.get("end_date") != null) {
                sql.append("AND reading_date <= ? ");
                paramList.add(params.get("end_date"));
            }
            
            return jdbcTemplate.queryForObject(sql.toString(), paramList.toArray(), Integer.class);
            
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, 
                String.format("查询设备读数记录总数异常: %s", e.getMessage()), e);
            return 0;
        }
    }
}
