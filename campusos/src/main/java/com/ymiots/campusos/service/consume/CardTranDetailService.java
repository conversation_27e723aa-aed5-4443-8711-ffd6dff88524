package com.ymiots.campusos.service.consume;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.*;
import lombok.Data;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Repository
public class CardTranDetailService extends BaseService{

    @Autowired
    UserRedis userredis;

    public JsonData CardTranDetailList(HttpServletRequest request, HttpServletResponse response, int start, int limit,String infoid, String startdate,String enddate,String tradetype,String dynamicstype){
        String where = " 1=1 AND paywallet<>3";
        if(!StringUtils.isBlank(infoid)) {
            where += " and infoid IN ('"+String.join("','",infoid.split(","))+"') ";
        }
        if (StringUtils.isNotBlank(tradetype) && Integer.parseInt(tradetype)  > 0){
            where += " and tradetype = '"+tradetype+"'";
            if (tradetype.equals("4") && Integer.parseInt(dynamicstype)  > 0) {
                where += " and dynamics_type = " + dynamicstype;
            }
        }

        if(!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate) ){
            where+=" and tradedate between '"+startdate+"' and '"+enddate+"'";
        }

        String orderby = ExtSort.Orderby(request, "ctd.tradedate desc");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ctd.cardno,ct.name,ct.code,ctd.money,d.name tradetype,sd.name AS payway, " +
                "tsd.name AS paytype,date_format(ctd.tradedate, '%Y-%m-%d %H:%i:%s') as tradedate , " +
                "ctd.des, ctd.`status`,user.name as creator, ctd.dynamics_type ");
        sql.append("FROM ((SELECT cardno,infoid,money,cardid,des,status,tradedate,payway,paytype,tradetype,creatorid, dynamics_type FROM tb_card_transaction_detail  WHERE ");
        sql.append(where).append(") UNION ALL (SELECT cardno,infoid,money,cardid,des,status,tradedate,payway,paytype,tradetype,creatorid, dynamics_type FROM tb_card_transaction_detail_sleep  WHERE ").append(where).append("))ctd ");
        sql.append(" INNER JOIN tb_card_teachstudinfo ct on ctd.infoid = ct.uid LEFT JOIN tb_sys_dictionary sd on  ctd.payway = sd.code and sd.groupcode = 'SYS0000008' LEFT JOIN tb_sys_dictionary tsd on ctd.paytype = tsd.code and tsd.groupcode = 'SYS0000007' LEFT JOIN tb_sys_dictionary d on ctd.tradetype = d.code and d.groupcode = 'SYS0000009' " +
                "LEFT JOIN tb_sys_user user on user.uid = ctd.creatorid ");

        SysUser user = userredis.get(request);
        if (user.getUsertype() == 1) {
            String userid = user.getUid();
            sql.append(" inner join tb_card_orgframework co on co.code = ct.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='").append(userid).append("'");
        }

        sql.append(" ORDER BY ").append(orderby);
        if(limit != 0){
            sql.append(" LIMIT ").append(start).append(",").append(limit);
        }

        String sb = " SELECT count(1) as total FROM tb_card_transaction_detail WHERE " +
                where +
                " UNION ALL " +
                " SELECT count(1) as total FROM tb_card_transaction_detail_sleep WHERE " +
                where;
        String totalSql=String.format("SELECT sum(t.total) as total FROM (%s) t", sb);
        int total = dbService.getCountBySQL(totalSql, "total");
        JSONArray ja = dbService.QueryList(sql.toString());
//        int total = ja.size();
        // 补贴类型
        Map<Integer, String> tradeMap = new HashMap<>();
        for (Object o : ja) {
            JSONObject d = (JSONObject) o;
            String tradeType = d.getString("tradetype");
            if (tradeType.equals("补贴")) {
                // 如果是补贴，需要明确补贴类型
                Integer dynamicsType = d.getInteger("dynamics_type");
                if (dynamicsType == null) {
                    continue;
                }
                String sqlType = "select name,code from tb_sys_dictionary where groupcode  = 'SYS0000058' and code ='" + dynamicsType + "'";
                String name = dbService.QueryJSONObject(sqlType).getString("name");
                d.put("tradetype", name);
            }
        }
        JSONObject dirobj=new JSONObject();
        dirobj.put("0", "全部");
        dirobj.put("1", "充值");
        dirobj.put("3", "退款");
        dirobj.put("4", "补贴");
        dirobj.put("5", "手续费");
        dirobj.put("6", "充值退款");
        dirobj.put("7", "换卡转出");
        dirobj.put("8", "补卡转入");
        dirobj.put("9", "补贴归零");

        Map<String, BigDecimal> consumeData = getDeptConsumeData(request, response, infoid, startdate, enddate, tradetype, dynamicstype);
        consumeData.forEach(((str, totalMoney) -> {
            JSONObject cell = new JSONObject();
            cell.put("tradedate", String.format("%s合计", dirobj.getString(str)));
            cell.put("money", totalMoney);
            cell.put("status",3);
            ja.add(cell);
        }));

//        for(int i=0;i<result.size();i++) {
//        	JSONObject item=result.getJSONObject(i);
//        	JSONObject cell = new JSONObject();
//        	if(item.getIntValue("total")>0) {
//        		cell.put("tradedate", String.format("%s合计", dirobj.getString(item.getString("tradetype"))));
//            	cell.put("money", item.getString("money"));
//            	cell.put("status",3);
//            	ja.add(cell);
//        	}
//        }
        JsonData jd = Json.getJsonData(true, "", ja, total);
        return jd;
    }

    private Map<String, BigDecimal> getDeptConsumeData(HttpServletRequest request, HttpServletResponse response,String infoid, String startdate, String enddate,  String tradetype,String dynamicstype) {

        String where = "where 1=1 ";
        if(!StringUtils.isBlank(infoid)) {
            where += " and ct.infoid IN ('"+String.join("','",infoid.split(","))+"') ";
        }

        if (StringUtils.isNotBlank(tradetype) && Integer.parseInt(tradetype)  > 0){
            where += "and ct.tradetype = '"+tradetype+"'";
            if (tradetype.equals("4") && Integer.parseInt(dynamicstype)  > 0) {
                where += " and ct.dynamics_type = " + dynamicstype;
            }
        }

        if(!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate) ){
            where+=" and ct.tradedate between '"+startdate+"' and '"+enddate+"'";
        }
        //交易记录表
        String sql = "select ct.money,ct.tradetype,ct.infoid from tb_card_transaction_detail ct inner join  tb_card_teachstudinfo cts on ct.infoid=cts.uid ";
        sql = extracted(request, sql);
        List<Transaction> transactions = dbService.queryList(sql + where, Transaction.class);

        //交易休眠表
        String sqlSleep = "select ct.money,ct.tradetype,ct.infoid from tb_card_transaction_detail_sleep ct inner join  tb_card_teachstudinfo cts on ct.infoid=cts.uid ";
        sqlSleep = extracted(request,sqlSleep);
        List<Transaction> transactionsSleep = dbService.queryList(sqlSleep + where, Transaction.class);

        //合并两张表
        List<Transaction> transactionsMer = new ArrayList<>();
        transactionsMer.addAll(transactions);
        transactionsMer.addAll(transactionsSleep);

        Map<String, BigDecimal> map = transactionsMer.stream()
                .filter(transaction -> "0".equals(tradetype) || transaction.getTradeType().equals(tradetype))
                .collect(Collectors.groupingBy(Transaction::getTradeType,
                        Collectors.reducing(BigDecimal.ZERO, Transaction::getMoney, BigDecimal::add)));

//    	String sql ="SELECT SUM(ctd.money) as money,COUNT(ctd.total) as total,ctd.tradetype " +
//                "FROM ( " +
//                "select SUM(money) as money,COUNT(uid) as total,tradetype from tb_card_transaction_detail where  "+ where + " group by tradetype " +
//                "UNION ALL " +
//                "SELECT SUM(money) as money,COUNT(uid) as total,tradetype from tb_card_transaction_detail_sleep where  "+ where + " group by tradetype " +
//                ") ctd " +
//                "group by tradetype ";
//        if(!"0".equals(tradetype)) {
//        	sql ="SELECT SUM(ctd.money) as money,COUNT(ctd.total) as total,ctd.tradetype " +
//                    "FROM ( " +
//                    " select SUM(money) as money,COUNT(uid) as total,tradetype from tb_card_transaction_detail where "+ where +
//                    " UNION ALL " +
//                    "select SUM(money) as money,COUNT(uid) as total,tradetype from tb_card_transaction_detail_sleep where "+ where +
//                    " ) ctd group by tradetype ";
//        }
//    	JSONArray list = dbService.QueryList(sql);
        return map;
    }

    @Data
    public static class Transaction {
        private BigDecimal money;
        private String tradeType;
        private String infoId;
    }

    private String extracted(HttpServletRequest request, String sql) {
        SysUser user = userredis.get(request);
        if (user.getUsertype() == 1) {
            String userid = user.getUid();
            sql += " inner join tb_card_teachstudinfo cts on cts.uid=ct.infoid" +
                    " inner join tb_card_orgframework co on co.code = cts.orgcode " +
                    " inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='" + userid + "' ";
        }
        return sql;
    }

    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response,String key, int start,int limit){
        String fields="info.uid,info.code,info.name";
        String table="tb_card_teachstudinfo info";
        String userid=userredis.getUserId(request);
        //如果为普通管理员，需要受二级授权控制
        if(userredis.get(request).getUsertype()==1){
            table+= " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='"+userid+"' ";
        }
        String where=" 1=1 ";
        if (StringUtils.isNotBlank(key)){
            where += " and (info.code like '%"+key+"%' or info.name like '%"+key+"%' ";
            if(ComHelper.isNumeric(key)) {
                String cardno=ComHelper.LeftPad(key, 12, '0');
                where += " or exists(select 1 from tb_card_cardinfo where infoid=info.uid and cardsn='"+cardno+"') ";
            }
            where += " ) ";
        }
        String orderby= ExtSort.Orderby(request, "info.createdate desc");
        JsonData result=dbService.QueryJsonData( fields, table, where, orderby, start, limit);
        return result;
    }

    public String ExportCardTranDetailData(HttpServletRequest request, HttpServletResponse response, String infoid,String startdate,String enddate,String tradetype,String dynamicstype,String currentPage,String pageSize,String size) throws IOException {
        int star = 1;
        JsonData result = new JsonData();
        if (currentPage.equals("1")){
            result = CardTranDetailList(request, response, 0, Integer.valueOf(size),infoid,startdate,enddate,tradetype,dynamicstype);
        }else if(Integer.valueOf(currentPage)>star) {
            result = CardTranDetailList(request, response, (Integer.valueOf(currentPage)-1)*Integer.valueOf(size), Integer.valueOf(size),infoid,startdate,enddate,tradetype,dynamicstype);
        }else {
            result = CardTranDetailList(request, response, 0, 0,infoid,startdate,enddate,tradetype,dynamicstype);

        }
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "状态");
        cell.put("datakey", "statustext");
        cell.put("size", 8);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "交易时间");
        cell.put("datakey", "tradedate");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "金额");
        cell.put("datakey", "money");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "支付渠道");
        cell.put("datakey", "payway");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "支付方式");
        cell.put("datakey", "paytype");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡号");
        cell.put("datakey", "cardno");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "学工号");
        cell.put("datakey", "code");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 12);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "操作人员");
        cell.put("datakey", "creator");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "备注");
        cell.put("datakey", "des");
        cell.put("size", 50);
        cells.add(cell);

        String title= "交易记录";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";
        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp", filename, title,startdate,enddate,"1","1");
        return filepath;
    }


}
