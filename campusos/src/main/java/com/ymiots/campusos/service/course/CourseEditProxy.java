package com.ymiots.campusos.service.course;

import com.ymiots.campusos.redission.DistributedLocker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Supplier;

/**
 * 课程操作代理上锁
 *
 * <AUTHOR>
 * @date 2023/09/12
 */
@Service
public class CourseEditProxy {

    @Autowired
    private DistributedLocker locker;

    @Transactional(propagation = Propagation.MANDATORY)
    public BizRes run(Supplier<BizRes> func, String courseId) {
        String lockKey = "courseBooking:" + courseId;
        // 尝试30s
        boolean isLocked = !locker.tryLock(lockKey, 30);
        if (isLocked) {
            return new GetLockTimeOutError();
        }
        // 获取到锁，可以尝试执行业务
        try {
            return func.get();
        } finally {
            locker.unlock(lockKey);
        }
    }

    protected static class BizRes {

        private boolean res;

        private String msg;

        public BizRes(boolean res, String msg) {
            this.res = res;
            this.msg = msg;
        }

        public boolean getRes() {
            return res;
        }

        public BizRes setRes(boolean res) {
            this.res = res;
            return this;
        }

        public String getMsg() {
            return msg;
        }

        public BizRes setMsg(String msg) {
            this.msg = msg;
            return this;
        }
    }

    protected static class GetLockTimeOutError extends BizRes {

        public GetLockTimeOutError() {
            super(false, "选课人数较多，请稍后重试");
        }
    }


}
