package com.ymiots.campusos.service.alleyway;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysConfigService;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.ComHelper;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;

@Repository
public class AuthorizeManService extends BaseService{

	@Autowired
	SysLogsService syslogs;

	@Autowired
	UserRedis userredis;

	@Autowired
	SysConfigService sysconfig;

	public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response,
			String orgcode,String key,String infotype,String infolabel,boolean viewchild, String intoyear, int start,int limit){
		String fields="b.uid,b.code,b.name,b.sex,b.card,b.cardsn,b.orgcode,b.infotype,b.intoyear,b.focus,b.status, getorgname(b.orgcode) as orgname";
		String table="tb_card_teachstudinfo b ";
        if(userredis.get(request).getUsertype()==1){
        	String userid=userredis.getUserId(request);
            table+= " inner join tb_card_orgframework_user uo on uo.orgcode=b.orgcode and uo.userid='"+userid+"' ";
        }
		String where=" b.status=1 and (not b.cardsn is null and b.cardsn<>'')";
		if(!infotype.equals("0") && StringUtils.isNotBlank(infotype)){
			where+=" and b.infotype="+infotype;
		}
		if(!intoyear.equals("0")  && StringUtils.isNotBlank(intoyear)){
			where+=" and b.intoyear="+intoyear;
		}
        if(StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)){
            where += " and b.infolabel like '%"+infolabel+"%' ";
        }
		if(!StringUtils.isBlank(orgcode)){
			if(viewchild){
				where+=" and b.orgcode like '"+orgcode+"%' ";
			}else{
				where+=" and b.orgcode='"+orgcode+"' ";
			}
		}
		if(!StringUtils.isBlank(key)){
			where+=" and (b.code='"+key+"' or b.name like '%"+key+"%' or b.mobile like '%"+key+"%' or b.card='"+ComHelper.LeftPad(key, 12, '0')+"' or cardsn='"+ComHelper.LeftPad(key, 12, '0')+"' )";
		}
		String orderby=ExtSort.Orderby(request, "b.createdate desc");
		JsonData result=dbService.QueryJsonData( fields, table, where, orderby, start, limit);
		return result;
	}

	public JsonData getDoorList(HttpServletRequest request, HttpServletResponse response,String areacode,
			boolean viewchild, String key, String selecteduid, int start,int limit){
		String fields="door.uid,door.name,door.controllerid,door.inouttype,door.readhead,door.relays,"
				+ "d1.name as inouttypename,d2.name as readheadname,d3.name as relaysname,"
				+ "ctrl.devname,ctrl.devsn,ctrl.machineid,ctrl.server_uid,ctrl.devstatus,ctrl.areacode,getareaname(ctrl.areacode) as areaname";

		String table="tb_dev_door door inner join tb_dev_accesscontroller ctrl on ctrl.uid=door.controllerid ";
		String userid=userredis.getUserId(request);
		//如果为普通管理员，需要受二级授权控制
		if(userredis.get(request).getUsertype()==1){
			 table+= " inner join tb_dev_areaframework_user ua on ua.areacode=ctrl.areacode and ua.userid='"+userid+"' ";
		}
		table+=" left join tb_sys_dictionary d1 on d1.code=door.inouttype and d1.groupcode='SYS0000018' "
				+ " left join tb_sys_dictionary d2 on d2.code=door.readhead and d2.groupcode='SYS0000016' "
				+ " left join tb_sys_dictionary d3 on d3.code=door.relays and d3.groupcode='SYS0000017'";

		String where=" ctrl.status=1 and ctrl.devclass in(2,5) and ctrl.devmodel=1 ";

		if(!StringUtils.isBlank(areacode)){
			if(viewchild){
				where +=" and ctrl.areacode like '"+areacode+"%' ";
			}else{
				where +=" and ctrl.areacode='"+areacode+"' ";
			}
		}

		if(!StringUtils.isBlank(key)){
			where +=" and  (door.name like '%"+key+"%' or ctrl.devname like '%"+key+"%' or ctrl.machineid='"+key+"' or door.readhead='"+key+"')  ";
		}
		JSONArray Selected=JSONArray.parseArray(selecteduid);
		if(Selected.size()>0){
			String[] uidx= ComHelper.ListToArray(Selected);
			where+=" and door.uid not in('"+String.join("','", uidx)+"')";
		}
		String orderby=ExtSort.Orderby(request, " door.uid desc ");
		JsonData result =dbService.QueryJsonData( fields, table, where, orderby, start, limit);
		return result;
	}

	public JsonResult SaveAuthorizeMan(HttpServletRequest request, HttpServletResponse response,
			String authorinfo,String authortime, String authordoor, String startdate, String enddate,
			String week1,String week2,String week3,String week4,String week5,String week6,String week0,
			String calendar,String backdate, String maxusetime) {

		if(StringUtils.isBlank(calendar)){
			calendar="0";
		}
		String creatorid=userredis.getUserId(request);
		JSONArray info=JSONArray.parseArray(authorinfo);
		JSONArray door=JSONArray.parseArray(authordoor);
		JSONObject weekjson=new JSONObject();
		weekjson.put("w1", StringUtils.isBlank(week1)?0:1);
		weekjson.put("w2", StringUtils.isBlank(week2)?0:1);
		weekjson.put("w3", StringUtils.isBlank(week3)?0:1);
		weekjson.put("w4", StringUtils.isBlank(week4)?0:1);
		weekjson.put("w5", StringUtils.isBlank(week5)?0:1);
		weekjson.put("w6", StringUtils.isBlank(week6)?0:1);
		weekjson.put("w0", StringUtils.isBlank(week0)?0:1);

		ArrayList<String> sqllist=new ArrayList<String>();
		StringBuffer sb=new StringBuffer();

		List<String> infoids=new ArrayList<String>();

		sb.append("insert into tb_alleyway_authorize(uid,infouid,infocode,infoname,infotype,card,cardsn,dooruid,doorname,readheader,machineid,devsn,areacode,startdate,enddate,week,calendar,timezone,createdate,creatorid) values");
		for(int x=0;x<info.size();x++){
			JSONObject infoitem=info.getJSONObject(x);
			for(int y=0;y<door.size();y++){
				JSONObject dooritem=door.getJSONObject(y);
				String authorizeuid=UUID.randomUUID().toString();
				//sqllist.add("delete from tb_alleyway_authorize where infouid='"+infoitem.getString("uid")+"' and dooruid='"+dooritem.getString("uid")+"' ");
				sb.append("('"+authorizeuid+"','"+infoitem.getString("uid")+"','"+infoitem.getString("infocode")+"','"+infoitem.getString("name")+"','"+infoitem.getString("infotype")+"','"+infoitem.getString("card")+"','"+infoitem.getString("cardsn")+"','"+dooritem.getString("uid")+"','"+dooritem.getString("name")+"','"+dooritem.getString("readhead")+"','"+dooritem.getString("machineid")+"','"+dooritem.getString("devsn")+"','"+dooritem.getString("areacode")+"','"+startdate+"','"+enddate+"','"+weekjson.toJSONString()+"',"+calendar+",'"+authortime+"',now(),'"+creatorid+"')");
				sb.append(",");
				infoids.add(infoitem.getString("uid"));
			}
		}
		//dbService.excuteBatchSql( ComHelper.ListToArray(sqllist));
		String sql=sb.toString();
		sql= sql.substring(0, sql.length()-1);
		dbService.excuteSql( sql);

		if(StringUtils.isBlank(maxusetime)) {
			maxusetime="0";
		}

		int ctrlusetimesandbacktime=0;
		if(!StringUtils.isBlank(backdate) && infoids.size()>0) {
			String isctrlusetimesandbacktime= sysconfig.get("isctrlusetimesandbacktime");
			if(isctrlusetimesandbacktime.equals("1")) {
				ctrlusetimesandbacktime=1;
			}
		}

		if(ctrlusetimesandbacktime==1) {
			sql="delete from tb_alleyway_authorize_infoconfig where find_in_set(uid, '"+ComHelper.ListToString(infoids)+"')>0";
			dbService.excuteSql(sql);

			sql="insert into tb_alleyway_authorize_infoconfig(uid,maxbacktime,maxusetime,hasusetime) select uid,'"+backdate+"',"+maxusetime+",0 from tb_card_teachstudinfo where find_in_set(uid, '"+ComHelper.ListToString(infoids)+"')>0";
			dbService.excuteSql(sql);
		}else {
			if(infoids.size()>0) {
				sql="delete from tb_alleyway_authorize_infoconfig where find_in_set(uid, '"+ComHelper.ListToString(infoids)+"')>0";
				dbService.excuteSql(sql);
			}
		}

		return Json.getJsonResult(true);
	}

	public JsonResult DelAuthorize(HttpServletRequest request, HttpServletResponse response, String uids) {
		String sql="delete from tb_alleyway_authorize where uid in('"+String.join("','", uids.split(","))+"')";
		dbService.excuteSql(sql);
		syslogs.Write(request, "按人授权", String.format("删除授权信息:%s",uids));
		return Json.getJsonResult(true);
	}

	public JsonData getInfoAuthorize(HttpServletRequest request, HttpServletResponse response,String key, String infouid, int start,int limit) {
		 String where="infouid='"+infouid+"'";
		 if (!StringUtils.isBlank(key)){
				where+=" and (doorname like '%"+key+"%' or machineid like '%"+key+"%' or devsn like '%"+key+"%')";
		}
		JsonData rs=dbService.QueryJsonData( "*,getareaname(areacode) as areaname", "tb_alleyway_authorize", where, "createdate desc,uid desc", start, limit);
		return rs;
	}

    public JSONArray getInfoLabel(HttpServletRequest request, HttpServletResponse response,String infotype) {
        JSONArray ja = new JSONArray();
        if(StringUtils.isNotBlank(infotype) && !"0".equals(infotype)){
            String sql = "SELECT label as name FROM tb_card_infolabel WHERE infotype=?";
            ja = dbService.QueryList(sql, infotype);
        }
        JSONObject jo = new JSONObject();
        jo.put("name", "全部");
        ja.add(0, jo);
        return ja;
    }

}
