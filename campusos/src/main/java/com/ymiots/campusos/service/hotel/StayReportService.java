package com.ymiots.campusos.service.hotel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;

@Repository
public class StayReportService extends BaseService{

	@Autowired
	UserRedis userredis;

	/**
	 *  显示区域、楼宇（普通管理员按区域授权进行控制）
	 * @param request
	 * @param response
	 * @param key
	 * @param authorize
	 * @return
	 */
	public JSONArray getAreaTree(HttpServletRequest request, HttpServletResponse response,String key,boolean authorize){
		String sql="";
		//授权且当前用户为普通管理员
		if(authorize && userredis.get(request).getUsertype()==1){
			String userid=userredis.getUserId(request);
			sql=" select area.uid,area.code,area.areatype,area.name,area.parentid,area.status,(case area.sex when '1' then 'x-fa fa-male bluecolor' when '2' then 'x-fa fa-female redcolor' else 'x-fa fa-file-o' end) as iconCls ";
			sql+=" from tb_dev_areaframework area inner join tb_dev_areaframework_user ua on ua.areacode=area.code and ua.userid='"+userid+"' ";
			sql+=" where areatype in(1,2) ";
			if(!StringUtils.isBlank(key)){
				sql +=" and (area.name like '%"+key+"%'  or area.code='"+key+"') ";
			}
			sql+=" order by area.code asc ";
		}else{
			sql="select uid,code,areatype,name,parentid,status,(case sex when '1' then 'x-fa fa-male bluecolor' when '2' then 'x-fa fa-female redcolor' else 'x-fa fa-file-o' end) as iconCls from tb_dev_areaframework where areatype in(1,2) ";
			if(!StringUtils.isBlank(key)){
				sql +=" and (name like '%"+key+"%'  or code='"+key+"') ";
			}
			sql+=" order by code asc ";
		}

		JSONArray list =dbService.QueryList(sql);
		JSONArray rootlist= TreeHelper.findRootList(list, "parentid", "uid");
		for(Object m:rootlist){
			JSONObject item=(JSONObject)m;
			JSONArray children=TreeHelper.getChildren(list, "parentid",item.getString("uid"),"uid");
			if (children.size() > 0) {
				item.put("leaf", false);
				item.put("children", children);
				item.put("expanded", true);
			}else{
				item.put("leaf", true);
				item.put("children", new JSONArray());
				item.put("expanded", true);
			}
		}
		return rootlist;
	}


	public JsonData getStayReportDetail(HttpServletRequest request, HttpServletResponse response,String areacode,String datatype,String key, int start,int limit) {
		String fields="b.name,b.des,b.infoname,getorgname(b.orgcode) as orgname,b.livetime,b.status";
		String table="tb_hotel_bed b";
		String where=" 1=1 ";
		if(datatype.equals("1")){
			where+=" and b.status=0 ";
		}else if(datatype.equals("2")){
			where+=" and b.status=1 ";
		}
		if(!StringUtils.isBlank(areacode)){
			where+=" and b.code like '"+areacode+"%' ";
		}
		if(!StringUtils.isBlank(key)){
			where+=" and (b.name like '%"+key+"%' or b.des  like '%"+key+"%' or b.infoname  like '%"+key+"%') ";
		}

        //如果为普通管理员，需要受二级授权控制
        if(userredis.get(request).getUsertype()==1){
        	String userid = userredis.getUserId(request);
            table+= " inner join tb_dev_areaframework_user ua on locate(b.code,ua.areacode)>0 and ua.userid='"+userid+"' ";
        }
        String orderby=ExtSort.Orderby(request, "b.code asc");
        return dbService.QueryJsonData(fields, table, where, orderby, start, limit);
	}

	public String ExportStayReportData(HttpServletRequest request, HttpServletResponse response,String areacode,String datatype,String key) throws IOException{
		String fields="name,des,infoname,getorgname(orgcode) as orgname,"
				+ "date_format(livetime, '%Y-%m-%d %H:%i:%s') as livetime, "
				+ "(case status when 1 then '已入住' else  (case  when orgcode is null or orgcode='' then '空床位' else '已规划空床位'  end)  end) as status";
		String where=" 1=1 ";
		if(datatype.equals("1")){
			where+=" and status=0 ";
		}else if(datatype.equals("2")){
			where+=" and status=1 ";
		}
		if(!StringUtils.isBlank(areacode)){
			where+=" and code like '"+areacode+"%' ";
		}
		if(!StringUtils.isBlank(key)){
			where+=" and (name like '%"+key+"%' or des  like '%"+key+"%' or infoname  like '%"+key+"%') ";
		}
		JSONArray list= dbService.QueryJSONArray(fields, "tb_hotel_bed", where, "code asc", 0, 0);
		JSONArray cells=new JSONArray();
		JSONObject cell=new JSONObject();

		cell=new JSONObject();
		cell.put("name", "房间");
		cell.put("datakey", "des");
		cell.put("size", 25);
		cells.add(cell);

		cell=new JSONObject();
		cell.put("name", "床位");
		cell.put("datakey", "name");
		cell.put("size", 10);
		cells.add(cell);

		cell=new JSONObject();
		cell.put("name", "状态");
		cell.put("datakey", "status");
		cell.put("size", 10);
		cells.add(cell);

		cell=new JSONObject();
		cell.put("name", "入住时间");
		cell.put("datakey", "livetime");
		cell.put("size", 20);
		cells.add(cell);

		cell=new JSONObject();
		cell.put("name", "姓名");
		cell.put("datakey", "infoname");
		cell.put("size", 10);
		cells.add(cell);

		cell=new JSONObject();
		cell.put("name", "班级");
		cell.put("datakey", "orgname");
		cell.put("size", 25);
		cells.add(cell);

		String title= "入住信息";
		String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
		String filename =  title+format + ".xlsx";

		String filepath= ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(),"temp",filename,title);

		return filepath;
	}

	public JsonResult getStayReportData(HttpServletRequest request, HttpServletResponse response,String areacode){
		SysUser sysuser= userredis.get(request);
		String code="";
		if(!StringUtils.isBlank(areacode)){
			code=" and code like '"+areacode+"%'";
		}else{
			if(sysuser.getUsertype()!=2 && sysuser.getUsertype()!=9){
				code=" and code in(select areacode from tb_dev_areaframework_user where userid='"+sysuser.getUid()+"' )";
			}
		}
		 //房间总数
		 int housecount=dbService.getCount("tb_dev_areaframework", "areatype=3"+code);
		 //床位总数
		 int bedcount=dbService.getCount("tb_hotel_bed", " 1=1 "+code);
		 //已安排床位
		 int staycount=dbService.getCount("tb_hotel_bed", "status>=1"+code);
		 //剩余床位
		 int remaincount=bedcount-staycount;

		 JSONObject data=new JSONObject();
		 data.put("housecount", housecount);
		 data.put("bedcount", bedcount);
		 data.put("staycount", staycount);
		 data.put("remaincount", remaincount);
		return Json.getJsonResult(true,"",data);
	}

}
