package com.ymiots.campusos.service.videocall.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.constant.videophoto.dto.UserBean;
import com.ymiots.campusos.common.constant.videophoto.dto.UserContactPermissionCheckerDto;
import com.ymiots.campusos.common.constant.videophoto.reponse.UserContactPermissionCheckerResult;
import com.ymiots.campusos.service.videocall.Operation;
import com.ymiots.campusos.vo.videocall.VideoCallVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**根据userUuid获取用户联系人及检查通话权限（刷脸+刷卡）
 * <AUTHOR>
 * @date 2024-05-20 9:31:03
 */
@Service
public class GetGhUseContactOperation extends BaseService implements Operation {
    @Override
    public String getHandlerName() {
        return "getGhUseContact";
    }

    @Override
    public UserContactPermissionCheckerResult execute(VideoCallVo videoCallVo) {
        String uid = videoCallVo.getUserUuid();
        UserBean userBean = null;
        JSONObject jsonObject = dbService.QueryJSONObject("SELECT a.uid,a.name,a.sex,a.card,a.code,getorgname(c.code) as classname,b.imgpath FROM tb_card_teachstudinfo a JOIN tb_face_infoface b on a.uid =b.infoid JOIN tb_card_orgframework c on a.orgcode = c.code WHERE a.uid = ? ", uid);
        if (jsonObject.getIntValue("sex") == 1) {
            jsonObject.put("sex", "男");
        } else {
            jsonObject.put("sex", "女");
        }
        userBean = new UserBean(jsonObject.getString("uid"), jsonObject.getString("name"), jsonObject.getString("code"), jsonObject.getString("card"), "STUDENT", jsonObject.getString("sex"), jsonObject.getString("imgpath").split("/")[2], "http:192.168.9.140:2018" + jsonObject.getString("imgpath"));
        UserContactPermissionCheckerResult result = new UserContactPermissionCheckerResult();
        result.setUserBean(userBean);
        JSONArray list = dbService.QueryList("SELECT a.uid,d.mobile,0 as messageCount,c.uid as userid,d.linkdes1 as salutation,a.otheropenid from tb_weixin_user a JOIN tb_card_teachstudinfo b on a.docid = b.uid and b.infotype=2 JOIN tb_card_parentinfo c on b.uid = c.student_id JOIN tb_card_teachstudinfo d on c.parent_id = d.uid and d.infotype=3 where b.uid = ?",uid);
        List<UserContactPermissionCheckerDto> dtoList = new ArrayList<>();
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                UserContactPermissionCheckerDto dto = new UserContactPermissionCheckerDto();
                dto.setUserContactUuid(list.getJSONObject(i).getString("uid"));
                dto.setPhoneNumber(list.getJSONObject(i).getString("mobile"));
                dto.setMessageCount(list.getJSONObject(i).getIntValue("messageCount"));
                dto.setUserUuid(list.getJSONObject(i).getString("userid"));
                int count = dbService.getCount("tb_phone_person_relation", "wx_id = '" + list.getJSONObject(i).getString("uid") + "' and binding_flag = 'Y'");
                if (count>0){
                    dto.setVoipOpenid(list.getJSONObject(i).getString("otheropenid"));
                }else {
                    dto.setVoipOpenid("");
                }
                dto.setSalutation(list.getJSONObject(i).getString("salutation"));
                dtoList.add(dto);
            }
        }
        result.setList(dtoList);
        JSONObject object = dbService.QueryJSONObject("select costFlag,separateBillingFlag from tb_phone_settings where devid is null");
        String costFlag = object.getString("costFlag");
        String separateBillingFlag = object.getString("separateBillingFlag");
        //是否收费
        if ("Y".equals(costFlag)){
            JSONObject VoiceObject1 = dbService.QueryJSONObject("SELECT (sum(b.talk_time)-sum(a.use_time)) as time from tb_phone_transaction_detail a LEFT JOIN tb_phone_option b on a.phone_option_id = b.uid\n" +
                    "WHERE b.option_type = 1 and b.talk_time>a.use_time and a.info_id = '" + uid + "'");
            int time1 = 0;
            if (VoiceObject1!=null){
                time1 = VoiceObject1.getIntValue("time");
            }
            JSONObject VoiceObject2 = dbService.QueryJSONObject("SELECT (sum(b.talk_time)-sum(a.use_time)) as time from tb_phone_transaction_detail a LEFT JOIN tb_phone_option b on a.phone_option_id = b.uid\n" +
                    "WHERE b.option_type = 2 and NOW() BETWEEN b.start_time AND b.end_time  and b.talk_time>a.use_time and a.info_id = '" + uid + "'");
            int time2 = 0;
            if (VoiceObject2!=null){
                time2 = VoiceObject2.getIntValue("time");
            }

            JSONObject VideoObject1 = dbService.QueryJSONObject("SELECT (sum(b.talk_time)-sum(a.use_time)) as time from tb_phone_transaction_detail a LEFT JOIN tb_phone_option b on a.phone_option_id = b.uid\n" +
                    "WHERE b.option_type = 3 and b.talk_time>a.use_time and a.info_id = '" + uid + "'");
            int time3 = 0;
            if (VideoObject1!=null){
                time3 = VideoObject1.getIntValue("time");
            }
            JSONObject VideoObject2 = dbService.QueryJSONObject("SELECT (sum(b.talk_time)-sum(a.use_time)) as time from tb_phone_transaction_detail a LEFT JOIN tb_phone_option b on a.phone_option_id = b.uid\n" +
                    "WHERE b.option_type = 4 and NOW() BETWEEN b.start_time AND b.end_time  and b.talk_time>a.use_time and a.info_id = '" + uid + "'");
            int time4 = 0;
            if (VideoObject2!=null){
                time4 = VideoObject2.getIntValue("time");
            }
            //是否分开计费
            if ("Y".equals(separateBillingFlag)){
                int voicetime = time1+time2;
                int videotime = time3+time4;
                if ((videotime+voicetime)==0){
                    result.setCheckCallFlag("E");
                    result.setCheckCallTips("通话时间不足");
                }else {
                    if (videotime==0){
                        result.setVideoTotalCallLen(0);
                        result.setTotalCallLen(0);
                    }else {
                        result.setVideoTotalCallLen(videotime);
                    }
                    result.setCheckCallFlag("S");

                }
                result.setVoiceTotalCallLen(voicetime);
            }else if ("N".equals(separateBillingFlag)){
                int voicetime = time1+time2;
                int videotime = time3+time4;
                if ((videotime+voicetime)==0){
                    result.setCheckCallFlag("E");
                    result.setCheckCallTips("通话时间不足");
                }else {
                    result.setVoiceTotalCallLen(videotime+voicetime);
                    result.setCheckCallFlag("S");
                }
            }

        }else if ("N".equals(costFlag)){
            result.setCheckCallFlag("S");
        }
        result.setShrgStatus("S");
        return result;
    }
}
