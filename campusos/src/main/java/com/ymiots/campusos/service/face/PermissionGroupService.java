package com.ymiots.campusos.service.face;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.datafactory.CallbackTransaction;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Repository
public class PermissionGroupService extends BaseService {
    @Autowired
    private UserRedis userRedis;

    @Autowired
    SysLogsService syslogs;

    public JsonData getPermissionGroup(HttpServletRequest request, HttpServletResponse response, String key, int start,
                                       int limit) {
        String where = "1=1";
        if (StringUtils.isNotBlank(key)) {
            where += " AND fag.name like '%" + key + "%'";
        }
        String fields = "fag.uid,fag.name,fag.weekplanid,fag.creatorid,fag.createdate,fag.groupindex,tfaw.name as weekplanname,fag.limit_count limitCount ";
        String table = "tb_face_authorize_group fag LEFT JOIN tb_face_authorize_weekplan tfaw on tfaw.uid=fag.weekplanid";
        String orderby=ExtSort.Orderby(request, "fag.createdate desc");
        JsonData rs = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return rs;
    }

    public JsonData getPermissionPeople(HttpServletRequest request, HttpServletResponse response, String downstatus, String facedownstatus, String infolabel, String property, String groupid, String infotype, String key, int start, int limit) {
        StringBuilder where = new StringBuilder("1=1");
        if (StringUtils.isNotBlank(downstatus) && Integer.parseInt(downstatus) >= 0) {
            where.append(" AND tfaga.downstatus = '").append(downstatus).append("'");
        }
        if (StringUtils.isNotBlank(facedownstatus) && Integer.parseInt(facedownstatus) >= 0) {
            where.append(" AND tfaga.facedownstatus = '").append(facedownstatus).append("'");
        }
        if (StringUtils.isNotBlank(key)) {
            where.append(" AND (tct.name LIKE '").append(key).append("%' OR da.machineid = '").append(key).append("')");
        }
        if (StringUtils.isNotBlank(groupid)) {
            where.append(" AND tfaga.groupid = '").append(groupid).append("'");
        }
        if (StringUtils.isNotBlank(infotype) && !infotype.equals("0")) {
            where.append(" AND tct.infotype = ").append(infotype);
        }
        if (StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)) {
            where.append(" AND tct.infolabel LIKE '").append(infolabel).append("%'");
        }
        if (StringUtils.isNotBlank(property) && !property.equals("-1")) {
            where.append(" AND tct.property = ").append(property);
        }
        String fields = "fag.name as groupname,da.machineid,tct.name,tct.code,tct.orgcode,tfaga.uid,tfaga.groupid,tfaga.infoid,tfaga.downtime,tfaga.downmsg,tfaga.downnum," +
                "tfaga.downstatus,tfaga.facedownstatus,tfaga.createdate,tfaga.fingerdownstatus," +
                "tfaga.uid, d.name as downword,d1.name as downfaceword,d2.name as downfingerword,getorgname(tct.orgcode) as orgname";
        String table = "tb_face_authorize_group_access tfaga LEFT JOIN tb_card_teachstudinfo tct ON tfaga.infoid = tct.uid" +
                " LEFT JOIN tb_face_authorize_group_cfg fagc on fagc.controllerid=tfaga.devid AND fagc.groupid=tfaga.groupid" +
                " LEFT JOIN tb_dev_accesscontroller da on da.uid=fagc.controllerid " +
                " LEFT JOIN tb_face_authorize_group fag on fag.uid = fagc.groupid " +
                " LEFT JOIN tb_sys_dictionary d on d.code=tfaga.downstatus and d.groupcode='SYS0000025'"+
                " LEFT JOIN tb_sys_dictionary d2 on d2.code=tfaga.fingerdownstatus and d2.groupcode='SYS0000025'"+
                " LEFT JOIN tb_sys_dictionary d1 on d1.code=tfaga.facedownstatus and d1.groupcode='SYS0000025'";
        if (userRedis.get(request).getUsertype() == 1) {
            String userid = userRedis.getUserId(request);
            table += " inner join tb_card_orgframework_user uo on tct.orgcode=uo.orgcode and uo.userid='" + userid + "' ";
        }

        String orderby = ExtSort.Orderby(request, "tfaga.createdate DESC");
        JsonData rs = dbService.QueryJsonData(fields, table, where.toString(), orderby, start, limit);
        return rs;
    }

    public JsonResult deletePermissionGroup(HttpServletRequest request, HttpServletResponse response, String uids)
            throws Exception {
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                String delfa = "delete from tb_face_authorize_group_access where downstatus in(0,2) and find_in_set(uid,'" + uids + "')>0 ";
                PreparedStatement ps = connection.prepareStatement(delfa);
                ps.executeUpdate();
                ps.close();
                String upfa = "update tb_face_authorize_group_access set downstatus=3,downnum=0 where downstatus in(1,3,4,5,6) and find_in_set(uid,'" + uids + "')>0 ";
                ps = connection.prepareStatement(upfa);
                ps.executeUpdate();
                ps.close();
            }
        });
        syslogs.Write(request, "授权组", String.format("删除授权,人员:%s", uids));
        return Json.getJsonResult(true);
    }

    public JsonResult deletePermissionGroup(HttpServletRequest request,String infoId,String groupId,String name)
            throws Exception {
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                String delfa = "delete from tb_face_authorize_group_access where downstatus in(0,2) and infoid = '" + infoId + "' and groupid = '" + groupId + "' ";
                PreparedStatement ps = connection.prepareStatement(delfa);
                ps.executeUpdate();
                ps.close();
                String upfa = "update tb_face_authorize_group_access set downstatus=3,downnum=0 where downstatus in(1,3,4,5,6) and infoid = '" + infoId + "' and groupid = '" + groupId + "' ";
                ps = connection.prepareStatement(upfa);
                ps.executeUpdate();
                ps.close();
            }
        });
        syslogs.Write(request, "授权组", String.format("删除授权,人员:%s", infoId));
        return Json.getJsonResult(true);
    }
    public JsonResult addPermissionGroup(HttpServletRequest request, HttpServletResponse response, String infoid, String groupuid) {
        String creatorid = userRedis.get(request).getUid();
        List<String> infoList = Arrays.asList(infoid.split(","));
        StringBuilder insertSQL = new StringBuilder("INSERT INTO tb_face_authorize_group_access (uid, groupid, infoid, downnum, downstatus , creatorid, createdate, devid, status) VALUES");
        StringBuilder insertValue = new StringBuilder();
        //1.获取权限组设备列表
        String controlleridSQL = "SELECT controllerid FROM tb_face_authorize_group_cfg WHERE groupid=?";
        JSONArray ctrlJA = dbService.QueryList(controlleridSQL, groupuid);
        if (ctrlJA.isEmpty()) {
            return Json.getJsonResult(false, "权限组设备列表为空！");
        }
        for (int i = 0; i < ctrlJA.size(); i++) {
            String controllerid = ctrlJA.getJSONObject(i).getString("controllerid");
            //2.1 获取该权限组该设备已授权人员
            String sql = "SELECT infoid FROM tb_face_authorize_group_access WHERE infoid IN ('" + String.join("','", infoList) + "') AND devid=? AND groupid=?";
            JSONArray hadAuthInfoid = dbService.QueryList(sql,controllerid,groupuid);
            ArrayList<String> hadAuthInfoList = new ArrayList<String>();
            ArrayList<String> unAuthList = new ArrayList<String>();
            for (int j = 0; j < hadAuthInfoid.size(); j++) {
                hadAuthInfoList.add(hadAuthInfoid.getJSONObject(j).getString("infoid"));
            }
            //2.2 处理未授权的人员
            for (String info : infoList) {
                if (!hadAuthInfoList.contains(info)) {
                    unAuthList.add(info);
                }
            }
            if (unAuthList.isEmpty()) {
                continue;
            }
            //3.判断未授权的人员列表是否在该设备已下载，并获取已下载的人员，返回一个list（查tb_face_authorizeface，downstatus=1 in(insertInfoidList)（SYS0000025）获取infoid）
            for (int a = 0; a < unAuthList.size(); a++) {
                insertValue.append("(uuid() ,'").append(groupuid).append("','").append(unAuthList.get(a)).append("',0,0,'").append(creatorid).append("',now(),'").append(controllerid).append("',1),");
            }
        }
        if (insertValue.length() != 0) {
            insertValue.deleteCharAt(insertValue.length() - 1);
            insertSQL.append(insertValue);
            dbService.excuteSql(insertSQL.toString());
        } else {
            return Json.getJsonResult(false, "请检测人员是否已下载");
        }

        return Json.getJsonResult(true);
    }

    public JsonResult redown(HttpServletRequest request, HttpServletResponse response, String uids) throws Exception {
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                String sql = "UPDATE tb_face_authorize_group_access SET downstatus=5,facedownstatus = case when facedownstatus is null then null else 0 end,fingerdownstatus = case when fingerdownstatus is null then null else 0 end,downnum=0 WHERE find_in_set(uid,'"
                        + uids + "')>0 and downstatus in(1,3,4,5,6)";
                PreparedStatement ps = connection.prepareStatement(sql);
                ps.executeUpdate();
                ps.close();

                String fsql = "UPDATE tb_face_authorize_group_access SET downstatus=0,facedownstatus = case when facedownstatus is null then null else 0 end,fingerdownstatus = case when fingerdownstatus is null then null else 0 end,downnum=0 WHERE find_in_set(uid,'"
                        + uids + "')>0 and downstatus=2";
                ps = connection.prepareStatement(fsql);
                ps.executeUpdate();
                ps.close();
            }
        });
        return Json.getJsonResult(true);
    }

    public JsonData getPeopleList(HttpServletRequest request, HttpServletResponse response, String groupid,String orgcode, String key,
                                  String infotype,String codes, boolean viewchild, String property,String infolabel, String selecteduid,String ishasmodel,String ismanwoman, int start,
                                  int limit) {
        String fields = "b.uid,b.code,b.name,b.sex,b.card,b.cardsn,b.passpassword,b.orgcode,b.infotype,b.intoyear,b.focus,b.status, getorgname(b.orgcode) as orgname";
        String table = "tb_card_teachstudinfo b";
        if (userRedis.get(request).getUsertype() == 1) {
            String userid = userRedis.getUserId(request);
            table += " inner join tb_card_orgframework_user uo on uo.orgcode=b.orgcode and uo.userid='" + userid + "' ";
        }

        String group_access_sql = "SELECT infoid FROM tb_face_authorize_group_access WHERE groupid=?";
        JSONArray ja = dbService.QueryList(group_access_sql,groupid);
        ArrayList<String> infoList = new ArrayList<>();
        for (int i = 0; i < ja.size(); i++) {
            infoList.add(ja.getJSONObject(i).getString("infoid"));
        }

//        String faceSQL = "select info.uid as infoid " +
//                "from tb_card_teachstudinfo info " +
//                "where info.status=1 AND not exists (SELECT uid FROM tb_face_infoface b WHERE b.status=1 and b.infoid = info.uid) ";
//        JSONArray faceja = dbService.QueryList(faceSQL);
//        for (int j = 0; j < faceja.size(); j++) {
//            infoList.add(faceja.getJSONObject(j).getString("infoid"));
//        }
        String where = " b.status=1 ";
        if (!infotype.equals("0") && StringUtils.isNotBlank(infotype)) {
            where += " and b.infotype=" + infotype;
        }
        if ( StringUtils.isNotBlank(property) && !property.equals("-1")) {
            where += " and b.property=" + property;
        }
        if ( StringUtils.isNotBlank(codes)) {
            where += " and b.code in" + codes +" ";
        }
        if(StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)){
            where += " and b.infolabel like '%"+infolabel+"%' ";
        }
        if (ishasmodel.equals("0")) {
            where += " and not exists(SELECT uid FROM tb_face_infoface fi WHERE fi.infoid = b.uid and fi.status=1)";
        }
        if (ishasmodel.equals("1")) {
            where += " and exists(SELECT uid FROM tb_face_infoface fi WHERE fi.infoid = b.uid and fi.status=1)";
        }
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and b.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and b.orgcode='" + orgcode + "' ";
            }
        }
        if (ismanwoman.equals("0")) {
            where += " and b.sex = '2'";
        }
        if (ismanwoman.equals("1")) {
            where += " and b.sex = '1'";
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (b.code='" + key + "' or b.name like '%" + key + "%' or b.mobile like '%" + key
                    + "%' or b.card='" + ComHelper.LeftPad(key, 12, '0') + "' or b.cardsn='"
                    + ComHelper.LeftPad(key, 12, '0') + "' )";
        }
        JSONArray Selected = JSONArray.parseArray(selecteduid);
        if (Selected.size() > 0) {
            String[] uidx = ComHelper.ListToArray(Selected);
            where += " and b.uid not in('" + String.join("','", uidx) + "')";
        }
        String orderby = ExtSort.Orderby(request, "b.createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JSONArray getInfoLabel(HttpServletRequest request, HttpServletResponse response, String infotype) {
        String sql = "SELECT label as name FROM tb_card_infolabel WHERE infotype=?";
        JSONArray ja = dbService.QueryList(sql, infotype);
        JSONObject jo = new JSONObject();
        jo.put("name", "全部");
        ja.add(0, jo);
        return ja;
    }
}
