package com.ymiots.campusos.service.weixin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.redis.RedisMessageEntity;
import com.ymiots.campusos.redis.RedisPublish;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.campusos.util.weixin.entity.TemplateMessageData;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.UUID;

@Repository
public class ArticleService extends BaseService {

    @Autowired
    private UserRedis userredis;

    @Autowired
    private SysLogsService syslogs;

    @Autowired
    private RedisPublish redispublish;

    public JsonData getArticleMsgList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        String fields = "wa.uid,wa.title,wa.article,wa.hits,wa.createdate,wa.creatorid,wa.modifydate,wa.status,ct.name ";
        String table = "tb_weixin_article wa " +
                "LEFT JOIN tb_card_teachstudinfo ct ON wa.creatorid=ct.uid ";
        String where = "1=1 ";
        if (userredis.get(request).getUsertype() != 9 && userredis.get(request).getUsertype() != 2) {
            String creatorid = userredis.get(request).getEmpid();
            if (StringUtils.isBlank(creatorid)) {
                String selinfoid = "SELECT uid FROM tb_card_teachstudinfo WHERE code='" + userredis.get(request).getCode() + "'";
                JSONArray ja = dbService.QueryList(selinfoid);
                if (!ja.isEmpty()) {
                    creatorid = ja.getJSONObject(0).getString("uid");
                }
            }
            where = "(wa.creatorid='" + creatorid + "' )";
        }
        if (StringUtils.isNotBlank(key)) {
            where += "AND wa.title like '%" + key + "%' ";
        }
        JsonData jd = dbService.QueryJsonData(fields, table, where, "wa.createdate DESC", start, limit);
        return jd;
    }

    public JsonResult saveArticle(HttpServletRequest request, HttpServletResponse response, String uid, String title, String article) {
        //人员信息uid
        String creatorid = userredis.get(request).getEmpid();
        if (StringUtils.isBlank(creatorid)) {
            String selinfoid = "SELECT uid FROM tb_card_teachstudinfo WHERE code='" + userredis.get(request).getCode() + "'";
            JSONArray ja = dbService.QueryList(selinfoid);
            if (!ja.isEmpty()) {
                creatorid = ja.getJSONObject(0).getString("uid");
            }
        }
        if (StringUtils.isBlank(uid)) {
            String insertSQL = "INSERT INTO tb_weixin_article (uid,title,article,createdate,creatorid,modifydate,modifierid,status) VALUES (uuid(),?,?,now(),?,now(),?,1)";
            dbService.excuteSql(insertSQL, title, article, creatorid, creatorid);
            syslogs.Write("推送文章", String.format("新增文章：%s", title));
        } else {
            String updateSQL = "UPDATE tb_weixin_article SET title=?,article=?,modifydate=now(),modifierid=? WHERE uid=?";
            dbService.excuteSql(updateSQL, title, article, creatorid, uid);
            syslogs.Write("推送文章", String.format("修改文章：%s|%s", title, uid));
        }
        return Json.getJsonResult(true);
    }

    public JsonResult delArticle(HttpServletRequest request, HttpServletResponse response, String uid) {
        String countSQL = "SELECT sum(isread) as readnum FROM tb_weixin_article_records WHERE articleid='" + uid + "'";
        JSONArray countja = dbService.QueryList(countSQL);
        if (!countja.isEmpty()) {
            int readnum = countja.getJSONObject(0).getIntValue("readnum");
            if (readnum > 0) {
                return Json.getJsonResult(false, "文章已推送且存在已读，不可删除");
            }
        }
        String delSQL = "DELETE FROM tb_weixin_article WHERE uid=?";
        dbService.excuteSql(delSQL, uid);

        String delRecords = "DELETE FROM tb_weixin_article_records WHERE articleid=?";
        dbService.excuteSql(delRecords, uid);

        syslogs.Write("推送文章", String.format("删除文章：%s", uid));
        return Json.getJsonResult(true);
    }

    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response, String orgcode, String key,
                                boolean viewchild, String infotype, String infolabel, String intoyear, int start, int limit, String selecteduid) {
        String fields = "b.uid,b.code,b.name,b.sex,b.card,b.cardsn,b.orgcode,b.infotype,b.intoyear,b.focus,b.status,getminiorgname(b.orgcode) as orgname";
        String table = "tb_card_teachstudinfo b ";
        if (userredis.get(request).getUsertype() == 1) {
            String userid = userredis.getUserId(request);
            table += " inner join tb_card_orgframework_user uo on uo.orgcode=b.orgcode and uo.userid='" + userid + "' ";
        }
        String where = " b.status=1 ";
        if (StringUtils.isNotBlank(infotype) && !infotype.equals("0")) {
            where += " and b.infotype=" + infotype;
        }
        if (StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)) {
            where += " and b.infolabel like '%" + infolabel + "%' ";
        }
        if (!intoyear.equals("0") && StringUtils.isNotBlank(intoyear)) {
            where += " and b.intoyear=" + intoyear;
        }
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and b.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and b.orgcode='" + orgcode + "' ";
            }
        }
        if (StringUtils.isNotBlank(selecteduid)) {
            where += " and b.uid not in('" + String.join("','", selecteduid.split(",")) + "')";
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (b.code='" + key + "' or b.name like '%" + key + "%' or b.mobile like '%" + key + "%' or b.card='" + ComHelper.LeftPad(key, 12, '0') + "' or b.cardsn='" + ComHelper.LeftPad(key, 12, '0') + "' )";
        }
        String orderby = ExtSort.Orderby(request, "b.createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonResult SendUserArticle(HttpServletRequest request, HttpServletResponse response, final String articleid, final String orgcode, final String infoid) {
        String pushName = userredis.get(request).getName();
        //new Thread(new Runnable() {
        //    @Override
        //    public void run() {
                String fields = "wu.uid as wxuserid,wu.openid,wu.docid,wu.idcode,wu.nickname, wu.usertype ";
                String table = "tb_weixin_user wu " +
                        "INNER JOIN tb_card_teachstudinfo ct ON wu.docid = ct.uid ";
                String where = "wu.status=1 ";
                if (StringUtils.isBlank(infoid) && StringUtils.isNotBlank(orgcode)) {
                    where += " AND ct.orgcode LIKE '" + orgcode + "%'";
                } else if (StringUtils.isNotBlank(infoid)) {
                    where += " AND find_in_set(wu.docid,'" + infoid + "')>0";
                }
                JSONArray list = dbService.QueryJSONArray(fields, table, where, "wu.createdate DESC", 0, 0);
                String sql = "select name from  tb_card_orgframework where code = '001'";
                String schoolName = dbService.queryOneField(sql, String.class);
                String insertSQL = "INSERT INTO tb_weixin_article_records (uid,infoid,articleid,wxuserid,isread,createdate,status) VALUES";
                String articleDataSQL = "SELECT wa.title,ct.name FROM tb_weixin_article wa LEFT JOIN tb_card_teachstudinfo ct ON ct.uid=wa.creatorid WHERE wa.uid=?";
                JSONArray articleDataja = dbService.QueryList(articleDataSQL, articleid);
                String title = "";
                String infoname = "";
                if (!articleDataja.isEmpty()) {
                    title = articleDataja.getJSONObject(0).getString("title");
                    infoname = articleDataja.getJSONObject(0).getString("name");
                    if (StringUtils.isBlank(infoname)) {
                        infoname = "学校";
                    }
                }
                StringBuffer insertData = new StringBuffer();
                if (list.size() > 0) {
                    for (int y = 0; y < list.size(); y++) {
                        String openid = list.getJSONObject(y).getString("openid");
                        String docid = list.getJSONObject(y).getString("docid");
                        String wxuserid = list.getJSONObject(y).getString("wxuserid");
                        String topic = "/campus/weixintemplatemsg";
                        RedisMessageEntity message = new RedisMessageEntity();
                        JSONObject data = new JSONObject();
                        data.put("msgid", UUID.randomUUID().toString());
                        data.put("openid", openid);
                        data.put("wxuserid", wxuserid);
                        data.put("cfgcode", "OPENTM207623991");
                        data.put("url", String.format("%s/article/article?articleid=%s", WebConfig.getWeixinDomain(), articleid));
                        JSONObject wxdata = new JSONObject();
                        if (WebConfig.getStartNew()) {
                            wxdata.put("thing4", new TemplateMessageData(schoolName).toJSONString());
                            wxdata.put("thing15", new TemplateMessageData(pushName).toJSONString());
                            wxdata.put("time12", new TemplateMessageData(DateHelper.format(new Date())).toJSONString());
                        } else {
                            wxdata.put("first", new TemplateMessageData("您有新的文章待查看。").toJSONString());
                            wxdata.put("keyword1", new TemplateMessageData(title).toJSONString());
                            wxdata.put("keyword2", new TemplateMessageData(infoname + "老师").toJSONString());
                            wxdata.put("keyword3", new TemplateMessageData(DateHelper.format(new Date())).toJSONString());
                            wxdata.put("remark", new TemplateMessageData("请家长点击消息查看，并及时处理告知相关事项。").toJSONString());
                        }
                        data.put("wxtempdata", wxdata);
                        message.setCmd("weixintemplatemsg");
                        message.setData(data);
                        message.setDevtype(0);
                        redispublish.Publish(topic, message);

                        int countArticleRecords = dbService.getCount("tb_weixin_article_records", "articleid='" + articleid + "' AND wxuserid='" + wxuserid + "'");
                        if (countArticleRecords > 0) {
                            String updateSQL = "UPDATE tb_weixin_article_records SET isread=0 WHERE articleid='" + articleid + "' AND wxuserid='" + wxuserid + "'";
                            dbService.excuteSql(updateSQL);
                        } else {
                            insertData.append("(uuid(),'").append(docid).append("','").append(articleid).append("','").append(wxuserid).append("',0,now(),1),");
                        }
                    }
                    if (insertData.length() != 0) {
                        insertData.deleteCharAt(insertData.length() - 1);
                        dbService.excuteSql(insertSQL + insertData.toString());
                    }
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        Log.error(this.getClass(), e.getMessage());
                    }
                }
        //    }
        //}).start();

        return Json.getJsonResult(true);
    }

    public JsonResult showQRcode(HttpServletRequest request, HttpServletResponse response, String articleid) throws Exception {
        String content = WebConfig.getWeixinDomain() + "/article/article?articleid=" + articleid;
        byte[] qrcodebyte = QRcodeUtils.createQrCodeByte(content, 1400, "png");
        JSONObject jo = new JSONObject();
        jo.put("qrcode", FileHelper.ImageByte2Base64(qrcodebyte));
        JsonResult jr = new JsonResult();
        jr.setSuccess(true);
        jr.setData(jo);
        return jr;
    }

    public JSONArray getInfoLabel(HttpServletRequest request, HttpServletResponse response, String infotype) {
        String sql = "SELECT label as name FROM tb_card_infolabel WHERE infotype=?";
        JSONArray ja = dbService.QueryList(sql, infotype);
        JSONObject jo = new JSONObject();
        jo.put("name", "全部");
        ja.add(0, jo);
        return ja;
    }
}
