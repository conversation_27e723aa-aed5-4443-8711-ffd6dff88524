package com.ymiots.campusos.service.time;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.config.redis.RedisClient;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.entity.alleyway.AlleywayRecordRow;
import com.ymiots.campusos.entity.card.OrgInfo;
import com.ymiots.campusos.entity.card.TeachStudInfo;
import com.ymiots.campusos.entity.dev.AccessController;
import com.ymiots.campusos.entity.time.TimeRecordRow;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.ExcelUtil;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.utils.excel.Sheet;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Null;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Repository
public class TimeRecordsService extends BaseService {

    @Autowired
    UserRedis userredis;

    //@Autowired
    //private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisClient redisClient;

    public JsonData getTimeRecordsList(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime, int start, int limit) {
        String where = "1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND tr.recordtime BETWEEN '" + starttime + "' AND '" + endtime + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            String sqlInfo = "SELECT uid FROM tb_card_teachstudinfo WHERE (name like '%" + key + "%' or code='" + key + "' or card='" + key + "')";
            JSONArray ja = dbService.QueryList(sqlInfo);
            if (!ja.isEmpty()) {
                List<String> ids = new ArrayList<>();
                for (int i = 0; i < ja.size(); i++) {
                    ids.add(ja.getJSONObject(i).getString("uid"));
                }
                where += " AND tr.infoid IN ('"+String.join("','",ids)+"') ";
            }
        }
        String twhere = "";
        SysUser sysUser = userredis.get(request);
        String orgcode = "(";
        JSONArray orgcodelist = dbService.QueryList("select orgcode from tb_card_orgframework_user where userid = ? order by orgcode", sysUser.getUid());
        if (orgcodelist.size()>0){
            for (int i = 0; i < orgcodelist.size(); i++) {
                String orgcode1 = orgcodelist.getJSONObject(i).getString("orgcode");
                orgcode+="'"+orgcode1+"'";
                if (i+1<orgcodelist.size()){
                    orgcode+=",";
                }else {
                    orgcode+=")";
                }
            }
        }
        if (StringUtils.isNotBlank(orgcode)&&orgcode.length()>2) {
            twhere += " where ct.orgcode in "+orgcode;
        }

        String sql = "SELECT d.uid,d.cardno,ct.name,ct.code,ct.sex,getorgname(d.orgcode) as orgname,d.tiwen,d.machineid,date_format(d.recordtime,'%Y-%m-%d %H:%i:%s') as recordtime,ac.devname,d.ftype " +
                "FROM ( select  tr.uid,tr.cardno,tr.tiwen,tr.machineid,tr.recordtime,tr.ftype,tr.orgcode,tr.infoid " +
                "from  tb_time_records tr " +
                "where " + where;
        sql += "order by tr.orgcode ASC,tr.recordtime desc ) d " +
                "left join tb_dev_accesscontroller ac on ac.machineid=d.machineid " +
                "inner join tb_card_teachstudinfo ct ON ct.uid = d.infoid " + twhere+
                " ORDER BY d.recordtime desc " +
                " limit " + start + "," + limit;
        String totalSQL = "SELECT count(1) as total " +
                "FROM ( select  tr.machineid,tr.infoid " +
                "from  tb_time_records tr " +
                "where " + where +
                "order by tr.recordtime desc ) d " +
                "inner join tb_dev_accesscontroller ac on ac.machineid=d.machineid " +
                "inner join tb_card_teachstudinfo ct ON ct.uid = d.infoid " + twhere;
        JSONArray data = dbService.QueryList(sql);
        int total = dbService.QueryList(totalSQL).getJSONObject(0).getIntValue("total");
        JsonData jd = new JsonData();
        jd.setData(data);
        jd.setTotal(total);
        jd.setSuccess(true);
        return jd;
    }

    public R<List<TimeRecordRow>> getNewTimeRecordsList(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime, int start, int limit) {



            String sql = "select cardno cardsn,ftype,machineid machineId,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordTime,infocode code,infoname name,orgcode from tb_time_records ";
            sql += "where  1=1 ";
            String where = "1=1 ";

            if (!StringUtils.isBlank(starttime) && !StringUtils.isBlank(endtime)) {
                sql += " and recordtime between '" + starttime + "' and '" + endtime + "'";
                where += " and recordtime between '" + starttime + "' and '" + endtime + "'";
            }
            if (StringUtils.isNotBlank(key)&&!"all".equals(key)) {
                sql += " and (infoname like '" + key + "%' or infocode='" + key + "' or cardno='" + key + "')";
                where += " and (infoname like '" + key + "%' or infocode='" + key + "' or cardno='" + key + "')";
            }
            sql += "order by recordtime desc limit "+start+","+limit;
            System.out.println("当前时间" + DateHelper.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            List<TimeRecordRow> timeRecordList = new ArrayList<>();
            JSONArray array = dbService.QueryJsonData(sql).getData();
            for (int i = 0; i < array.size(); i++) {
                TimeRecordRow row = new TimeRecordRow();
                row.setCode(array.getJSONObject(i).getString("code"));
                row.setName(array.getJSONObject(i).getString("name"));
                row.setRecordTime(array.getJSONObject(i).getString("recordTime"));
                row.setCardsn(array.getJSONObject(i).getString("cardsn"));
                row.setMachineId(array.getJSONObject(i).getString("machineId"));
                row.setFtype(array.getJSONObject(i).getString("ftype"));
                row.setOrgcode(array.getJSONObject(i).getString("orgcode"));
                timeRecordList.add(row);
            }
            System.out.println(DateHelper.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            if (CollectionUtil.isEmpty(timeRecordList)) {
                return R.fail(new ArrayList<>());
            }
            //获取到设别信息
            String machineSQL = "select da.devname devName,da.machineid machineId from tb_dev_accesscontroller da";
            List<AccessController> accessControllers = dbService.queryList(machineSQL, AccessController.class);
            Map<String, List<AccessController>> accessListMap = accessControllers.stream()
                    .collect(Collectors.groupingBy(AccessController::getMachineId));
            //获取到机构信息
            String orgSQL = "select code,name,parentid from tb_card_orgframework";
            List<OrgInfo> orgInfoList = dbService.queryList(orgSQL, OrgInfo.class);
            Map<String, List<OrgInfo>> orgInfoListMap = orgInfoList.stream().peek(i->{
                        String orgname = getorgname(i.getParentid(), i.getName());
                        i.setName(orgname);
                    })
                    .collect(Collectors.groupingBy(OrgInfo::getCode));
        Set<String> validOrgcodes = orgInfoList.stream()
                .map(OrgInfo::getCode)
                .collect(Collectors.toSet());
        Set<String> validDevIds = accessControllers.stream()
                .map(AccessController::getMachineId)
                .collect(Collectors.toSet());
            //处理通行记录表合并其他数据
            timeRecordList = timeRecordList.stream()
                    .filter(record -> validDevIds.contains(record.getMachineId())&&validOrgcodes.contains(record.getOrgcode()))
                    .peek(i -> {
                        List<AccessController> controllers = accessListMap.get(i.getMachineId());
                        List<OrgInfo>  orgInfos = orgInfoListMap.get(i.getOrgcode());
                        if (CollectionUtil.isNotEmpty(controllers)) {
                            AccessController accessController = controllers.get(0);
                            i.setDevName(accessController.getDevName());
                        }
                        if (CollectionUtil.isNotEmpty(orgInfos)) {
                            OrgInfo orgInfo = orgInfos.get(0);
                            i.setOrgname(orgInfo.getName());
                        }
                    }).collect(Collectors.toList());
        int count =0;
        if (StringUtils.isNotBlank(key)) {
            count = timeRecordList.size();
        }else {
            count = dbService.getCount("tb_time_records", where);
        }
            return R.okPage(timeRecordList, count);
    }
    public String getorgname(String parentid,String name) {
        if (StringUtils.isEmpty(parentid)){
            return name;
        }
        JSONObject jsonObject = dbService.QueryJSONObject("select name,parentid from tb_card_orgframework where uid = ?", parentid);
        if (jsonObject!=null){
            name = jsonObject.getString("name")+name;
            parentid = jsonObject.getString("parentid");
        }else {
            parentid = "";
        }
        return getorgname(parentid,name);
    }

    public R<Null> exportTimeRecord(HttpServletRequest request, HttpServletResponse response, String key, String startdate, String enddate) {

        String sql = "select cardno cardsn,ftype,machineid machineId,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordTime,infocode code,infoname name,orgcode from tb_time_records ";
        sql += "where  1=1 ";

        if (!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate)) {
            sql += " and recordtime between '" + startdate + "' and '" + enddate + "'";
        }
        if (StringUtils.isNotBlank(key)) {
            sql += " and (infoname like '" + key + "%' or infocode='" + key + "' or cardno='" + key + "')";
        }
        sql += "order by recordtime desc";
        System.out.println("当前时间" + DateHelper.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        List<TimeRecordRow> timeRecordList = new ArrayList<>();
        JSONArray array = dbService.QueryJsonData(sql).getData();
        for (int i = 0; i < array.size(); i++) {
            TimeRecordRow row = new TimeRecordRow();
            row.setCode(array.getJSONObject(i).getString("code"));
            row.setName(array.getJSONObject(i).getString("name"));
            row.setRecordTime(array.getJSONObject(i).getString("recordTime"));
            row.setCardsn(array.getJSONObject(i).getString("cardsn"));
            row.setMachineId(array.getJSONObject(i).getString("machineId"));
            row.setFtype(array.getJSONObject(i).getString("ftype"));
            row.setOrgcode(array.getJSONObject(i).getString("orgcode"));
            timeRecordList.add(row);
        }
        System.out.println(DateHelper.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        //获取到设别信息
        String machineSQL = "select da.devname devName,da.machineid machineId from tb_dev_accesscontroller da";
        List<AccessController> accessControllers = dbService.queryList(machineSQL, AccessController.class);
        Map<String, List<AccessController>> accessListMap = accessControllers.stream()
                .collect(Collectors.groupingBy(AccessController::getMachineId));
        //获取到机构信息
        String orgSQL = "select code,name,parentid from tb_card_orgframework";
        List<OrgInfo> orgInfoList = dbService.queryList(orgSQL, OrgInfo.class);
        Map<String, List<OrgInfo>> orgInfoListMap = orgInfoList.stream().peek(i->{
                    String orgname = getorgname(i.getParentid(), i.getName());
                    i.setName(orgname);
                })
                .collect(Collectors.groupingBy(OrgInfo::getCode));
        //处理通行记录表合并其他数据
        timeRecordList = timeRecordList.stream()
                //.filter(record -> validDevIds.contains(record.getMachineId())&&validOrgcode.contains(record.getOrgcode()))
                .peek(i -> {
                    List<AccessController> controllers = accessListMap.get(i.getMachineId());
                    List<OrgInfo>  orgInfos = orgInfoListMap.get(i.getOrgcode());
                    if (CollectionUtil.isNotEmpty(controllers)) {
                        AccessController accessController = controllers.get(0);
                        i.setDevName(accessController.getDevName());
                    }
                    if (CollectionUtil.isNotEmpty(orgInfos)) {
                        OrgInfo orgInfo = orgInfos.get(0);
                        i.setOrgname(orgInfo.getName());
                    }
                }).collect(Collectors.toList());

        LinkedHashMap<Integer, TimeRecordRow> map = new LinkedHashMap<>();
        Sheet<TimeRecordRow> recordRows = new Sheet<>(map, TimeRecordRow.class);
        int row = 1;
        for (TimeRecordRow vo : timeRecordList) {
            map.put(row, vo);
            row++;
        }
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String extFilePath = "temp/考勤记录原始表" + format + ".xlsx";
        String filePath = WebConfig.getUploaddir() + extFilePath;
        com.ymiots.framework.utils.excel.ExcelUtil.write(recordRows, filePath, "考勤记录原始表", 6,startdate,enddate);
        return R.ok(extFilePath);

    }

    public void DeleteTimeRecordSleep(Date date){
        String year = dbService.QueryJSONObject("SELECT NAME FROM tb_sys_dictionary WHERE groupcode = 'SYS0000067' AND CODE = 0").getString("NAME");
        // 获取当前日期
        Date currentDate = date;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.YEAR, -Integer.parseInt(year));
        Date downDate = calendar.getTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = dateFormat.format(downDate);
        dbService.excuteSql("delete from tb_time_records_sleep where date_format(recordtime, '%Y-%m-%d') < '"+format+"'");
    }

    public JsonData getTimeHistoryRecordsList(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime, int start, int limit) {
        String where = "1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND recordtime BETWEEN '" + starttime + "' AND '" + endtime + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            where+=" and (name like '%" + key + "%' or code='" + key + "' or card='" + key + "')";
        }
        SysUser sysUser = userredis.get(request);
        String orgcode = "(";
        JSONArray orgcodelist = dbService.QueryList("select orgcode from tb_card_orgframework_user where userid = ? order by orgcode", sysUser.getUid());
        if (orgcodelist.size()>0){
            for (int i = 0; i < orgcodelist.size(); i++) {
                String orgcode1 = orgcodelist.getJSONObject(i).getString("orgcode");
                orgcode+="'"+orgcode1+"'";
                if (i+1<orgcodelist.size()){
                    orgcode+=",";
                }else {
                    orgcode+=")";
                }
            }
        }
        if (StringUtils.isNotBlank(orgcode)&&orgcode.length()>2) {
            where += " and orgcode in "+orgcode;
        }

        String fields = "uid,infoid,infoname,infocode,orgcode,cardno,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordtime,machineid,tiwen,ftype,isdel,createdate,isupload,status,name,code,sex,orgname,devname ";
        JsonData data = dbService.QueryJsonData(fields, "tb_time_records_sleep", where, "recordtime desc", start, limit);
        return data;
    }

    public String ExportERPTimeRecords(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime) throws IOException {
        String where = "1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND tr.recordtime BETWEEN '" + starttime + "' AND '" + endtime + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            String sqlInfo = "SELECT uid FROM tb_card_teachstudinfo WHERE (name like '%" + key + "%' or code='" + key + "' or card='" + key + "')";
            JSONArray ja = dbService.QueryList(sqlInfo);
            if (!ja.isEmpty()) {
                List<String> ids = new ArrayList<>();
                for (int i = 0; i < ja.size(); i++) {
                    ids.add(ja.getJSONObject(i).getString("uid"));
                }
                where += " AND tr.infoid IN ('"+String.join("','",ids)+"') ";
            }
        }
        String sql = "SELECT substring(d.cardno,3) as cardno,ct.name,ct.code,ct.sex,getminiorgname(d.orgcode) as orgname,d.machineid,ac.devname,date_format(d.recordtime,'%Y-%m-%d') as rdate,date_format(d.recordtime,'%H:%i:%s') as rtime " +
                "FROM ( select tr.cardno,tr.machineid,tr.recordtime,tr.orgcode,tr.infoid " +
                "from  tb_time_records tr " +
                "where " + where;
        sql += "order by tr.recordtime desc ) d " +
                "inner join tb_dev_accesscontroller ac on ac.machineid=d.machineid " +
                "inner join tb_card_teachstudinfo ct ON ct.uid = d.infoid ";
        SysUser sysUser = userredis.get(request);
        String orgcode = "(";
        JSONArray orgcodelist = dbService.QueryList("select orgcode from tb_card_orgframework_user where userid = ? order by orgcode", sysUser.getUid());
        if (orgcodelist.size()>0){
            for (int i = 0; i < orgcodelist.size(); i++) {
                String orgcode1 = orgcodelist.getJSONObject(i).getString("orgcode");
                orgcode+="'"+orgcode1+"'";
                if (i+1<orgcodelist.size()){
                    orgcode+=",";
                }else {
                    orgcode+=")";
                }
            }
        }
        if (StringUtils.isNotBlank(orgcode)&&orgcode.length()>2) {
            sql += " where ct.orgcode in "+orgcode;
        }
        JsonData result = dbService.QueryJsonData(sql);

        JSONArray list = result.getData();

        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "工号");
        cell.put("datakey", "code");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡号");
        cell.put("datakey", "cardno");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "machineid");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机具名称");
        cell.put("datakey", "devname");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "日期");
        cell.put("datakey", "rdate");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "时间");
        cell.put("datakey", "rtime");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属部门");
        cell.put("datakey", "orgname");
        cell.put("size", 20);
        cells.add(cell);

        String title= "机具情况";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
        return filepath;
    }

    public String ExportTimeRecords(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime) throws IOException {
        String where = "1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND tr.recordtime BETWEEN '" + starttime + "' AND '" + endtime + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            String sqlInfo = "SELECT uid FROM tb_card_teachstudinfo WHERE (name like '%" + key + "%' or code='" + key + "' or card='" + key + "')";
            JSONArray ja = dbService.QueryList(sqlInfo);
            if (!ja.isEmpty()) {
                List<String> ids = new ArrayList<>();
                for (int i = 0; i < ja.size(); i++) {
                    ids.add(ja.getJSONObject(i).getString("uid"));
                }
                where += " AND tr.infoid IN ('"+String.join("','",ids)+"') ";
            }
        }
        String sql = "SELECT substring(d.cardno,3) as cardno,ct.name,ct.code,ct.sex,getminiorgname(d.orgcode) as orgname,d.machineid,ac.devname,date_format(d.recordtime,'%Y-%m-%d %H:%i:%s') as rdate " +
                "FROM ( select tr.cardno,tr.machineid,tr.recordtime,tr.orgcode,tr.infoid " +
                "from  tb_time_records tr " +
                "where " + where;
        sql += "order by tr.orgcode ASC,tr.recordtime desc limit 50000) d " +
                "inner join tb_dev_accesscontroller ac on ac.machineid=d.machineid " +
                "inner join tb_card_teachstudinfo ct ON ct.uid = d.infoid ";
        JsonData result = dbService.QueryJsonData(sql);

        JSONArray list = result.getData();

        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "打卡时间");
        cell.put("datakey", "rdate");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "工号");
        cell.put("datakey", "code");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡号");
        cell.put("datakey", "cardno");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "machineid");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机具名称");
        cell.put("datakey", "devname");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属部门");
        cell.put("datakey", "orgname");
        cell.put("size", 20);
        cells.add(cell);

        String title= "打卡情况";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
        return filepath;
    }

    public String ExportTimeRecordsTxt(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime) throws FileNotFoundException {
        String where = "1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND tr.recordtime BETWEEN '" + starttime + "' AND '" + endtime + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            String sqlInfo = "SELECT uid FROM tb_card_teachstudinfo WHERE (name like '%" + key + "%' or code='" + key + "' or card='" + key + "')";
            JSONArray ja = dbService.QueryList(sqlInfo);
            if (!ja.isEmpty()) {
                List<String> ids = new ArrayList<>();
                for (int i = 0; i < ja.size(); i++) {
                    ids.add(ja.getJSONObject(i).getString("uid"));
                }
                where += " AND tr.infoid IN ('"+String.join("','",ids)+"') ";
            }
        }
        String sql = "SELECT ct.code,date_format(d.recordtime,'%Y-%m-%d %H:%i') as rdate " +
                "FROM ( select tr.recordtime,tr.infoid " +
                "from  tb_time_records tr " +
                "where " + where;
        sql += "order by tr.recordtime asc limit 50000) d " +
                "inner join tb_card_teachstudinfo ct ON ct.uid = d.infoid ";
        SysUser sysUser = userredis.get(request);
        String orgcode = "(";
        JSONArray orgcodelist = dbService.QueryList("select orgcode from tb_card_orgframework_user where userid = ? order by orgcode", sysUser.getUid());
        if (orgcodelist.size()>0){
            for (int i = 0; i < orgcodelist.size(); i++) {
                String orgcode1 = orgcodelist.getJSONObject(i).getString("orgcode");
                orgcode+="'"+orgcode1+"'";
                if (i+1<orgcodelist.size()){
                    orgcode+=",";
                }else {
                    orgcode+=")";
                }
            }
        }
        if (StringUtils.isNotBlank(orgcode)&&orgcode.length()>2) {
            sql += " where ct.orgcode in "+orgcode;
        }
        JsonData result = dbService.QueryJsonData(sql);

        JSONArray list = result.getData();

        StringBuffer data = new StringBuffer();
        if (!list.isEmpty()) {
            for (int i = 0; i < list.size(); i++) {
                JSONObject jo = list.getJSONObject(i);
                String machineid = jo.getString("code");
                String rdate = jo.getString("rdate");
                data.append(machineid).append(" ").append(rdate).append("\r\n");
            }
            String filename = DateHelper.format(new Date(), "MMdd") + ".txt";
            String filepath = WebConfig.getUploaddir() + "temp/" + filename;
            try {
                File file = new File(filepath);
                if (!file.exists()) {
                    file.createNewFile();
                }
                BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8));
                bw.write(data.toString());
                bw.close();
                return "/temp/" + filename;
            } catch (IOException e) {
                Log.error(this.getClass(), e.getMessage());
                return "";
            }
        } else {
            return "";
        }
    }


    public String ExportHistoryERPTimeRecords(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime) throws IOException {
        String where = "1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND recordtime BETWEEN '" + starttime + "' AND '" + endtime + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            where+=" and (name like '%" + key + "%' or code='" + key + "' or card='" + key + "')";
        }
        SysUser sysUser = userredis.get(request);
        String orgcode = "(";
        JSONArray orgcodelist = dbService.QueryList("select orgcode from tb_card_orgframework_user where userid = ? order by orgcode", sysUser.getUid());
        if (orgcodelist.size()>0){
            for (int i = 0; i < orgcodelist.size(); i++) {
                String orgcode1 = orgcodelist.getJSONObject(i).getString("orgcode");
                orgcode+="'"+orgcode1+"'";
                if (i+1<orgcodelist.size()){
                    orgcode+=",";
                }else {
                    orgcode+=")";
                }
            }
        }
        if (StringUtils.isNotBlank(orgcode)&&orgcode.length()>2) {
            where += " and orgcode in "+orgcode;
        }

        String fields = "uid,infoid,infoname,infocode,orgcode,cardno,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordtime,machineid,tiwen,ftype,isdel,createdate,isupload,status,name,code,sex,orgname,devname ";
        JsonData result = dbService.QueryJsonData("select "+fields+" from tb_time_records_sleep where "+where);

        JSONArray list = result.getData();

        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "工号");
        cell.put("datakey", "code");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡号");
        cell.put("datakey", "cardno");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "machineid");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机具名称");
        cell.put("datakey", "devname");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "日期");
        cell.put("datakey", "rdate");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "时间");
        cell.put("datakey", "rtime");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属部门");
        cell.put("datakey", "orgname");
        cell.put("size", 20);
        cells.add(cell);

        String title= "机具情况";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
        return filepath;
    }

    public String ExportHistoryTimeRecords(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime) throws IOException {
        String where = "1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND recordtime BETWEEN '" + starttime + "' AND '" + endtime + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            where+=" and (name like '%" + key + "%' or code='" + key + "' or card='" + key + "')";
        }
        SysUser sysUser = userredis.get(request);
        String orgcode = "(";
        JSONArray orgcodelist = dbService.QueryList("select orgcode from tb_card_orgframework_user where userid = ? order by orgcode", sysUser.getUid());
        if (orgcodelist.size()>0){
            for (int i = 0; i < orgcodelist.size(); i++) {
                String orgcode1 = orgcodelist.getJSONObject(i).getString("orgcode");
                orgcode+="'"+orgcode1+"'";
                if (i+1<orgcodelist.size()){
                    orgcode+=",";
                }else {
                    orgcode+=")";
                }
            }
        }
        if (StringUtils.isNotBlank(orgcode)&&orgcode.length()>2) {
            where += " and orgcode in "+orgcode;
        }

        String fields = "uid,infoid,infoname,infocode,orgcode,cardno,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordtime,machineid,tiwen,ftype,isdel,createdate,isupload,status,name,code,sex,orgname,devname ";
        JsonData result = dbService.QueryJsonData("select "+fields+" from tb_time_records_sleep where "+where);

        JSONArray list = result.getData();

        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "打卡时间");
        cell.put("datakey", "rdate");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "工号");
        cell.put("datakey", "code");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡号");
        cell.put("datakey", "cardno");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "machineid");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机具名称");
        cell.put("datakey", "devname");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属部门");
        cell.put("datakey", "orgname");
        cell.put("size", 20);
        cells.add(cell);

        String title= "打卡情况";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
        return filepath;
    }

    public String ExportHistoryTimeRecordsTxt(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime) throws FileNotFoundException {
        String where = "1=1 ";
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND recordtime BETWEEN '" + starttime + "' AND '" + endtime + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            where+=" and (name like '%" + key + "%' or code='" + key + "' or card='" + key + "')";
        }
        SysUser sysUser = userredis.get(request);
        String orgcode = "(";
        JSONArray orgcodelist = dbService.QueryList("select orgcode from tb_card_orgframework_user where userid = ? order by orgcode", sysUser.getUid());
        if (orgcodelist.size()>0){
            for (int i = 0; i < orgcodelist.size(); i++) {
                String orgcode1 = orgcodelist.getJSONObject(i).getString("orgcode");
                orgcode+="'"+orgcode1+"'";
                if (i+1<orgcodelist.size()){
                    orgcode+=",";
                }else {
                    orgcode+=")";
                }
            }
        }
        if (StringUtils.isNotBlank(orgcode)&&orgcode.length()>2) {
            where += " and orgcode in "+orgcode;
        }

        String fields = "uid,infoid,infoname,infocode,orgcode,cardno,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordtime,machineid,tiwen,ftype,isdel,createdate,isupload,status,name,code,sex,orgname,devname ";
        JsonData result = dbService.QueryJsonData("select "+fields+" from tb_time_records_sleep where "+where);

        JSONArray list = result.getData();

        StringBuffer data = new StringBuffer();
        if (!list.isEmpty()) {
            for (int i = 0; i < list.size(); i++) {
                JSONObject jo = list.getJSONObject(i);
                String machineid = jo.getString("code");
                String rdate = jo.getString("rdate");
                data.append(machineid).append(" ").append(rdate).append("\r\n");
            }
            String filename = DateHelper.format(new Date(), "MMdd") + ".txt";
            String filepath = WebConfig.getUploaddir() + "temp/" + filename;
            try {
                File file = new File(filepath);
                if (!file.exists()) {
                    file.createNewFile();
                }
                BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8));
                bw.write(data.toString());
                bw.close();
                return "/temp/" + filename;
            } catch (IOException e) {
                Log.error(this.getClass(), e.getMessage());
                return "";
            }
        } else {
            return "";
        }
    }

}
