package com.ymiots.campusos.service.thirdapi.Impl;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.DBService;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.constant.qinglu.QingLuRspStatus;
import com.ymiots.campusos.constant.qinglu.QingLuUrl;
import com.ymiots.campusos.dto.thirdapi.qinglu.*;
import com.ymiots.campusos.entity.card.OrgFramework;
import com.ymiots.campusos.service.card.CardManageService;
import com.ymiots.campusos.service.thirdapi.QingLuOrgSycService;
import com.ymiots.campusos.util.HttpRequestUtils;
import com.ymiots.campusos.util.SignUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class QingLuOrgSycServiceImpl implements QingLuOrgSycService {

    @Autowired
    private DBService dbService;

    @Autowired
    private
    HttpRequestUtils httpRequestUtils;

    @Autowired
    CardManageService cardManageService;

    private static final Logger logger = LoggerFactory.getLogger(QingLuOrgSycServiceImpl.class);
    private static final String appid = "SHANHAIJC241128";
    private static final String Secret = "a57157063adceee0fabec57dfa8f4280";

    @Override
    public R<?> getQingLuCampus() throws IOException {
        Map<String, String> params = new HashMap<>();
        long second = Instant.now().getEpochSecond();
        params.put("app_id", appid);
        params.put("timestamp", String.valueOf(second));
        String appSecret = Secret;
        String sign = "";
        try {
            sign = SignUtils.generateSign(params, appSecret);
            System.out.println("生成的签名sign："+sign);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        String url = QingLuUrl.QING_LU_URL_CAMPUS.getUrl();
        QingLuCampusRsp qingLuCampusRsp = null;
        try {
            qingLuCampusRsp = httpRequestUtils.doGetRequest(url, null,params, QingLuCampusRsp.class);
            System.out.println("结果："+qingLuCampusRsp.getCode());
            System.out.println("结果信息："+qingLuCampusRsp.getMessage());
        } catch (URISyntaxException e) {
            System.out.println(e.toString());
        }

        if (!QingLuRspStatus.SUCCESS.getCode().equals(qingLuCampusRsp.getCode())) {
            return R.fail("查询失败");
        }

        List<QingLuCampus> result = qingLuCampusRsp.getResult();

        String insertCampusSql = "insert into tb_card_orgframework (uid,code,orgcode,name,orgtype,parentid,createdate,creatorid,status) values ";
        JSONObject object = dbService.QueryJSONObject("select uid from tb_card_orgframework where code = '001' and orgtype = 1");
        String uid = UUID.randomUUID().toString();
        if (object==null){
            dbService.excuteSql(insertCampusSql+" (?,?,?,?,?,?,now(),?,?)", uid,"001",null,"青鹿学校",1,null,"同步",1);
        }else {
            uid = object.getString("uid");
        }
        AtomicInteger beginNum = new AtomicInteger(1); // 使用AtomicInteger类型
        for (QingLuCampus qingLuCampus : result) {
            //qingLuCampus.setParentUniqueCode("001");
            JSONObject school = dbService.QueryJSONObject("select uid from tb_card_orgframework where uid =? and orgtype = 1", qingLuCampus.getUniqueCode());
            String lastCode = "001";
            String code = lastCode + String.format("%03d", beginNum.get());
            if (school==null){
                //处理最新的组织编号
                String insertql =insertCampusSql+ "('"+qingLuCampus.getUniqueCode()+"','"+code+"','"+qingLuCampus.getId()+"'," +
                        "'"+qingLuCampus.getName()+"',1,'"+uid+"',now(),'数据同步',1)";
                dbService.excuteSql(insertql);
            }else {
                dbService.excuteSql("update tb_card_orgframework set code='"+code+"',name = '"+qingLuCampus.getName()+"',parentid='"+uid+"' where uid = '"+qingLuCampus.getId()+"' and orgtype = 1");
            }
            beginNum.incrementAndGet(); // 自增
        }

        logger.info("开始同步青鹿校区信息{}",qingLuCampusRsp.getMessage());
        return null;
    }

    @Override
    public R<?> getQingLuOrg() throws IOException {
        Map<String, String> params = new HashMap<>();
        long second = Instant.now().getEpochSecond();
        params.put("app_id", appid);
        params.put("timestamp", String.valueOf(second));
        String appSecret = Secret;
        String sign = "";
        try {
          sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        String url1 = QingLuUrl.QING_LU_URL_DEPARTMENT.getUrl();
        QingLuDepartRsp qingLuDepartRsp = null;
        try {
            qingLuDepartRsp = httpRequestUtils.doGetRequest(url1,null, params, QingLuDepartRsp.class);
            System.out.println("结果："+qingLuDepartRsp.getCode());
            System.out.println("结果信息："+qingLuDepartRsp.getMessage());
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }

        if (!QingLuRspStatus.SUCCESS.getCode().equals(qingLuDepartRsp.getCode())) {
            return R.fail("查询失败");
        }

        ////进行同步操作
        List<QingLuDepartment> departments = qingLuDepartRsp.getResult();
        // 使用 Stream API 分组并排序
        Map<Integer, List<QingLuDepartment>> departmentListMap = departments.stream()
                .collect(Collectors.groupingBy(
                        department -> department.getCampusId(),
                        TreeMap::new, // 使用 TreeMap 来保持键的自然顺序
                        Collectors.mapping(json -> json, Collectors.toList())
                ));
        String insertDepartmentSql = "insert into tb_card_orgframework (uid,code,orgcode,name,orgtype,parentid,createdate,creatorid,status) values ";
        Map<String,Integer> pmap = new HashMap<>();
        departmentListMap.forEach((parentId, qingLuDepartments) ->{
            Integer beginNum = 1;
            //查询父节点id
            String selOsOrgFramework = "select uid,code,orgcode from tb_card_orgframework where orgcode = '" + parentId + "' and orgtype = 1";
            OrgFramework orgFrameworkParent = dbService.queryForOne(selOsOrgFramework, OrgFramework.class);
            String orgFrameworkSql = "select uid,code,orgcode from tb_card_orgframework where parentid = '" + orgFrameworkParent.getUid() + "' order by code desc limit 1";
            OrgFramework orgFrameworkParentMax = dbService.queryForOne(orgFrameworkSql, OrgFramework.class);
            if (orgFrameworkParentMax!=null){
                int length = orgFrameworkParentMax.getCode().length();
                pmap.put(orgFrameworkParent.getCode(),Integer.valueOf(orgFrameworkParentMax.getCode().substring(length-3,length))+1);
            }
            qingLuDepartments.sort((o1, o2) -> o1.getDepartmentId());
            if (orgFrameworkParent!=null){
                for (QingLuDepartment department : qingLuDepartments) {
                    if (StringUtils.isEmpty(department.getParentUniqueCode())||department.getParentUniqueCode().equals(department.getUniqueCode())){
                        if (pmap.get(orgFrameworkParent.getCode())!=null){
                            beginNum = pmap.get(orgFrameworkParent.getCode());
                        }
                        String lastCode = orgFrameworkParent.getCode();
                        String code = lastCode + String.format("%03d", beginNum);
                        JSONObject jsonObject = dbService.QueryJSONObject("select uid,code from tb_card_orgframework where orgcode =? and orgtype = 2", department.getUniqueCode());
                        if (jsonObject==null){
                            dbService.excuteSql(insertDepartmentSql +"('"+department.getUniqueCode()+"','"+code+"','"+department.getUniqueCode()+"'," +
                                    "'"+department.getName()+"',2,'"+orgFrameworkParent.getUid()+"',now(),'数据同步',1)");
                        }else {
                            code =jsonObject.getString("code");
                            beginNum--;
                            dbService.excuteSql("update tb_card_orgframework set code='"+code+"',name = '"+department.getName()+"',parentid='"+orgFrameworkParent.getUid()+"' where uid = '"+department.getUniqueCode()+"' and orgtype = 2");
                        }
                        beginNum++;
                        pmap.put(orgFrameworkParent.getCode(),beginNum);
                    }
                }
            }
        });
        Map<String,Integer> map = new HashMap<>();
        departmentListMap.forEach((parentId, qingLuDepartments) ->{
            int beginNum = 1;
            //查询父节点id
            String selOsOrgFramework = "select uid,code from tb_card_orgframework where orgcode = '" + parentId + "' and orgtype = 1";
            OrgFramework orgFrameworkParent = dbService.queryForOne(selOsOrgFramework, OrgFramework.class);
            qingLuDepartments.sort((o1, o2) -> o1.getDepartmentId());
            if (orgFrameworkParent!=null){
                for (QingLuDepartment department : qingLuDepartments) {
                        //查询父节点id
                    if (StringUtils.isNotEmpty(department.getParentUniqueCode())&&!department.getParentUniqueCode().equals(department.getUniqueCode())) {
                        String selOsOrgFramework1 = "select uid,code,orgcode from tb_card_orgframework where orgcode = '" + department.getParentUniqueCode() + "' and orgtype = 2";
                        OrgFramework orgFramework = dbService.queryForOne(selOsOrgFramework1, OrgFramework.class);
                        if (orgFramework==null){
                            orgFramework =orgFrameworkParent;
                        }
                        if (map.get(orgFramework.getCode())!=null){
                            beginNum =map.get(orgFramework.getCode());
                        }
                        String lastCode = orgFramework.getCode();
                        String code = lastCode + String.format("%03d", beginNum);
                        JSONObject jsonObject = dbService.QueryJSONObject("select uid,code from tb_card_orgframework where orgcode =? and orgtype = 2", department.getUniqueCode());
                        if (jsonObject == null) {
                            dbService.excuteSql(insertDepartmentSql + "('" + department.getUniqueCode() + "','" + code + "','" + department.getUniqueCode() + "'," +
                                    "'" + department.getName() + "',2,'" + orgFramework.getUid() + "',now(),'数据同步',1)");
                        } else {
                            code =jsonObject.getString("code");
                            beginNum--;
                            dbService.excuteSql("update tb_card_orgframework set code='"+code+"',name = '"+department.getName()+"',parentid='"+orgFramework.getUid()+"' where uid = '"+department.getUniqueCode()+"' and orgtype = 2");
                        }
                        beginNum++;
                        map.put(orgFramework.getCode(),beginNum);
                        beginNum=1;
                    }
                }
            }

        });

        logger.info("开始同步青鹿部门信息{}",qingLuDepartRsp.getMessage());
        return null;
    }

    @Override
    public R<?> getQingLuGrade() throws IOException {
        Map<String, String> params = new HashMap<>();
        long second = Instant.now().getEpochSecond();
        params.put("app_id", appid);
        params.put("timestamp", String.valueOf(second));
        String appSecret = Secret;
        String sign = "";
        try {
          sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);

        String url = QingLuUrl.QING_LU_URL_GRADE.getUrl();
        QingLuGradeRsp qingLuGradeRsp = null;
        try {
            qingLuGradeRsp = httpRequestUtils.doGetRequest(url,null, params, QingLuGradeRsp.class);
            System.out.println("结果："+qingLuGradeRsp.getCode());
            System.out.println("结果信息："+qingLuGradeRsp.getMessage());
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }

        if (!QingLuRspStatus.SUCCESS.getCode().equals(qingLuGradeRsp.getCode())) {
            return R.fail("查询失败");
        }
        List<QingLuGrade> qingLuGrades = qingLuGradeRsp.getResult();
        //进行同步操作

        // 使用 Stream API 分组并排序
        Map<Integer, List<QingLuGrade>> qingLuGradeListMap = qingLuGrades.stream()
                .collect(Collectors.groupingBy(
                        qingLuGrade -> qingLuGrade.getCampusId(),
                        TreeMap::new, // 使用 TreeMap 来保持键的自然顺序
                        Collectors.mapping(json -> json, Collectors.toList())
                ));
        String insertGradeSql = "insert into tb_card_orgframework (uid,code,orgcode,name,orgtype,parentid,createdate,creatorid,status) values ";
        Map<String,Integer> map = new HashMap<>();
        qingLuGradeListMap.forEach((parentId, qingLuGradeList) ->{
            Integer beginNum = 1; // 使用AtomicInteger类型
            //查询父节点id
            String selOsOrgFramework = "select uid,code,orgcode from tb_card_orgframework where orgcode = '" + parentId + "' and orgtype = 1";
            OrgFramework orgFrameworkParent = dbService.queryForOne(selOsOrgFramework, OrgFramework.class);
            qingLuGradeList.sort((o1, o2) -> o1.getId());
            if (orgFrameworkParent!=null){
                String orgFrameworkSql = "select uid,code,orgcode from tb_card_orgframework where parentid = '" + orgFrameworkParent.getUid() + "' order by code desc limit 1";
                OrgFramework orgFrameworkParentMax = dbService.queryForOne(orgFrameworkSql, OrgFramework.class);
                if (orgFrameworkParentMax!=null&&!map.containsKey(orgFrameworkParent.getCode())){
                    int length = orgFrameworkParentMax.getCode().length();
                    String count = orgFrameworkParentMax.getCode().substring(length - 3, length);
                    map.put(orgFrameworkParent.getCode(),Integer.valueOf(count)+1);
                }
                for (QingLuGrade qingLuGrade : qingLuGradeList) {

                        String lastCode = orgFrameworkParent.getCode();
                        if (map.get(orgFrameworkParent.getCode())!=null){
                            beginNum = map.get(orgFrameworkParent.getCode());
                        }
                        String code = lastCode + String.format("%03d", beginNum);
                        JSONObject jsonObject = dbService.QueryJSONObject("select uid,code from tb_card_orgframework where orgcode =? and orgtype = 3", qingLuGrade.getUniqueCode());
                        if (jsonObject==null){
                            dbService.excuteSql(insertGradeSql +"('"+qingLuGrade.getUniqueCode()+"','"+code+"','"+qingLuGrade.getUniqueCode()+"'," +
                                    "'"+qingLuGrade.getName()+"',3,'"+orgFrameworkParent.getUid()+"',now(),'数据同步',1)");
                        }else {
                            code =jsonObject.getString("code");
                            beginNum--;
                            dbService.excuteSql("update tb_card_orgframework set code='"+code+"',name = '"+qingLuGrade.getName()+"',parentid='"+orgFrameworkParent.getUid()+"' where uid = '"+qingLuGrade.getUniqueCode()+"' and orgtype =  3");
                        }
                        beginNum++;
                        map.put(orgFrameworkParent.getCode(),beginNum);

                }
            }
        });


        logger.info("开始同步青鹿年级信息{}",qingLuGradeRsp.getMessage());
        return null;
    }

    @Override
    public R<?> getQingLuGradeClass() throws IOException {
        Map<String, String> params = new HashMap<>();
        long second = Instant.now().getEpochSecond();
        params.put("app_id", appid);
        params.put("timestamp", String.valueOf(second));
        String appSecret = Secret;
        String sign = "";
        try {
          sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);

        String url = QingLuUrl.QING_LU_URL_GRADE_CLASS.getUrl();
        QingLuGradeClassRsp qingLuGradeClassRsp = null;
        try {
            qingLuGradeClassRsp = httpRequestUtils.doGetRequest(url, null,params, QingLuGradeClassRsp.class);
            System.out.println("结果："+qingLuGradeClassRsp.getCode());
            System.out.println("结果信息："+qingLuGradeClassRsp.getMessage());
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }

        if (!QingLuRspStatus.SUCCESS.getCode().equals(qingLuGradeClassRsp.getCode())) {
            return R.fail("查询失败");
        }


        List<QingLuGradeClass> qingLuGradeClasses = qingLuGradeClassRsp.getResult();
        //进行同步操作

        // 使用 Stream API 分组并排序
        Map<String, List<QingLuGradeClass>> qingLuGradeListMap = qingLuGradeClasses.stream()
                .collect(Collectors.groupingBy(
                        qingLuGradeClass -> qingLuGradeClass.getGradeUniqueCode(),
                        // 使用 TreeMap 来保持键的自然顺序
                        TreeMap::new,
                        Collectors.mapping(json -> json, Collectors.toList())
                ));
        String insertGradeSql = "insert into tb_card_orgframework (uid,code,orgcode,name,orgtype,parentid,createdate,creatorid,status,years) values ";
        Map<String,Integer> map = new HashMap<>();
        qingLuGradeListMap.forEach((parentId, qingLuGradeClassList) ->{
            Integer beginNum = 1; // 使用AtomicInteger类型
            //查询父节点id
            String selOsOrgFramework = "select uid,code,orgcode from tb_card_orgframework where orgcode = '" + parentId + "' and orgtype = 3";
            OrgFramework orgFrameworkParent = dbService.queryForOne(selOsOrgFramework, OrgFramework.class);
            qingLuGradeClassList.sort((o1, o2) -> Integer.compare(o1.getId(), o2.getId()));
            if (orgFrameworkParent!=null){
                String orgFrameworkSql = "select uid,code,orgcode from tb_card_orgframework where parentid = '" + orgFrameworkParent.getUid() + "' order by code desc limit 1";
                OrgFramework orgFrameworkParentMax = dbService.queryForOne(orgFrameworkSql, OrgFramework.class);
                if (orgFrameworkParentMax!=null&&!map.containsKey(orgFrameworkParent.getCode())){
                    int length = orgFrameworkParentMax.getCode().length();
                    String count = orgFrameworkParentMax.getCode().substring(length - 3, length);
                    map.put(orgFrameworkParent.getCode(),Integer.valueOf(count)+1);
                }
                for (QingLuGradeClass qingLuGradeClass : qingLuGradeClassList) {

                        String lastCode = orgFrameworkParent.getCode();
                        if (map.get(orgFrameworkParent.getCode())!=null){
                            beginNum = map.get(orgFrameworkParent.getCode());
                        }
                        String code = lastCode + String.format("%03d", beginNum);
                        JSONObject jsonObject = dbService.QueryJSONObject("select uid,code from tb_card_orgframework where orgcode =? and orgtype = 4", qingLuGradeClass.getUniqueCode());
                        if (jsonObject==null){
                            dbService.excuteSql(insertGradeSql +"('"+qingLuGradeClass.getUniqueCode()+"','"+code+"','"+qingLuGradeClass.getUniqueCode()+"'," +
                                    "'"+qingLuGradeClass.getName()+"',4,'"+orgFrameworkParent.getUid()+"',now(),'数据同步',1,'"+qingLuGradeClass.getYearName()+"')");
                        }else {
                            code =jsonObject.getString("code");
                            beginNum--;
                            dbService.excuteSql("update tb_card_orgframework set code='"+code+"',name = '"+qingLuGradeClass.getName()+"',parentid='"+orgFrameworkParent.getUid()+"',years = '"+qingLuGradeClass.getYearName()+"' where uid = '"+qingLuGradeClass.getUniqueCode()+"' and orgtype =  4");
                        }
                        beginNum++;
                        map.put(orgFrameworkParent.getCode(),beginNum);

                }
            }
        });


        logger.info("开始同步青鹿班级信息{}",qingLuGradeClassRsp.getMessage());
        return null;
    }

    //学生
    @Override
    public R<?> getQingLuStudent() throws IOException {
        Map<String, String> params = new HashMap<>();
        long second = Instant.now().getEpochSecond();
        params.put("app_id", appid);
        params.put("timestamp", String.valueOf(second));
        String appSecret = Secret;
        String sign = "";
        try {
            sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        String url = QingLuUrl.QING_LU_URL_QUERY_STUDENT_INFO.getUrl();
        QingLuStudentRsp qingLuStudentRsp = null;
        try {
            qingLuStudentRsp = httpRequestUtils.doGetRequest(url, null,params, QingLuStudentRsp.class);
            System.out.println("结果："+qingLuStudentRsp.getCode());
            System.out.println("结果信息："+qingLuStudentRsp.getMessage());
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
        if (!QingLuRspStatus.SUCCESS.getCode().equals(qingLuStudentRsp.getCode())) {
            return R.fail("查询失败");
        }
        //进行同步操作
        List<QingLuStudent> qingLuStudents = qingLuStudentRsp.getResult();
        String insertDepartmentSql = "insert into tb_card_teachstudinfo (uid,code,name,sex,birthday,intoyear,address,card,cardsn,nation,mobile,orgcode,infotype,createdate,creatorid,modifydate,modifierid,status) values ";
        for (QingLuStudent  qingLuStudent: qingLuStudents) {
            String orgcode = "";
            if (qingLuStudent.getCurrent_class()==null){
                JSONObject jsonObject = dbService.QueryJSONObject("select code from tb_card_orgframework where orgtype = 1 and orgcode = ?", qingLuStudent.getCampus_id());
                if (jsonObject!=null){
                    orgcode=jsonObject.getString("code");
                }else {
                    orgcode="001";
                }
            }else {
                JSONObject jsonObject = dbService.QueryJSONObject("select code from tb_card_orgframework where orgtype = 4 and orgcode = ?", qingLuStudent.getCurrent_class().getClass_unique_code());
                if (jsonObject!=null){
                    orgcode=jsonObject.getString("code");
                }else {
                    orgcode="001";
                }
            }
            String nation = "";
            if ("1".equals(qingLuStudent.getNation())){
                nation ="汉族";
            }
            String sex = "";
            if (qingLuStudent.getSex()==1){
                sex = "1";
            }else {
                sex = "2";
            }
            String address = "";
            QingLuStudentAddress qingLuStudentAddress = JSONObject.parseObject(qingLuStudent.getHome_address(), QingLuStudentAddress.class);
            if (qingLuStudentAddress!=null){
                address = qingLuStudentAddress.getHome_address();
            }

            String code = qingLuStudent.getStudent_num();
            if (StringUtils.isEmpty(code)){
                code = String.valueOf(qingLuStudent.getId());
            }
            JSONObject jsonObject = dbService.QueryJSONObject("select uid from tb_card_teachstudinfo where uid = ? and infotype = 2", qingLuStudent.getUser_code());
            if (jsonObject==null){
                dbService.excuteSql(insertDepartmentSql+" ('"+qingLuStudent.getUser_code()+"','"+code+"','"+qingLuStudent.getName()+"','"+sex+"',?,'"+qingLuStudent.getEntrance_year()+"',?,'"+qingLuStudent.getIpass()+"','"+qingLuStudent.getIpass()+"','" +nation+"',"+
                        "'"+qingLuStudent.getPhone()+"','"+orgcode+"',2,now(),'数据同步','"+qingLuStudent.getUpdated_at()+"','数据同步',1)",qingLuStudent.getBirthday(),address);
            }else {
                dbService.excuteSql("update tb_card_teachstudinfo set name = ?,code=?,sex=?,birthday=?,intoyear=?,address=?,mobile=?,modifydate=?,nation=? where uid = ?",
                        qingLuStudent.getName(),code,sex,qingLuStudent.getBirthday(),qingLuStudent.getEntrance_year(),address,qingLuStudent.getPhone(),qingLuStudent.getUpdated_at(),nation,qingLuStudent.getUser_code());
            }
        }
        logger.info("开始同步青鹿学生信息{}",qingLuStudentRsp.getMessage());
        return null;
    }
    //教师
    @Override
    public R<?> getQingLuTeacher() throws IOException {
        Map<String, String> params = new HashMap<>();
        long second = Instant.now().getEpochSecond();
        params.put("app_id", appid);
        params.put("timestamp", String.valueOf(second));
        String appSecret = Secret;
        String sign = "";
        try {
          sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        String url = QingLuUrl.QING_LU_URL_QUERY_TEACHER_INFO.getUrl();
        QingLuTeacherRsp qingLuTeacherRsp = null;
        try {
            qingLuTeacherRsp = httpRequestUtils.doGetRequest(url, null,params, QingLuTeacherRsp.class);
            System.out.println("结果："+qingLuTeacherRsp.getCode());
            System.out.println("结果信息："+qingLuTeacherRsp.getMessage());
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
        if (!QingLuRspStatus.SUCCESS.getCode().equals(qingLuTeacherRsp.getCode())) {
            return R.fail("查询失败");
        }
        //进行同步操作
        List<QingLuTeacher> result = qingLuTeacherRsp.getResult();
        String insertDepartmentSql = "insert into tb_card_teachstudinfo (uid,code,name,sex,birthday,startdate,card,cardsn,email,mobile,orgcode,infotype,createdate,creatorid,modifydate,modifierid,status,infolabel) values ";
        for (QingLuTeacher qingLuTeacher : result) {
            String orgcode = "";
            if (qingLuTeacher.getDepartment()==null){
                JSONObject jsonObject = dbService.QueryJSONObject("select code from tb_card_orgframework where orgtype = 1 and orgcode = ?", qingLuTeacher.getCampus_id());
                if (jsonObject!=null){
                    orgcode=jsonObject.getString("code");
                }else {
                    orgcode="001";
                }
            }else {
                JSONObject jsonObject = dbService.QueryJSONObject("select code from tb_card_orgframework where orgtype = 2 and orgcode = ?", qingLuTeacher.getDepartment().getDepartment_unique_code());
                if (jsonObject!=null){
                    orgcode=jsonObject.getString("code");
                }else {
                    orgcode="001";
                }
            }
            String sex = "";
            if (qingLuTeacher.getSex()==1){
                sex = "1";
            }else {
                sex = "2";
            }
            if (StringUtils.isEmpty(qingLuTeacher.getEntry_at())){
                qingLuTeacher.setEntry_at(null);
            }
            String code = qingLuTeacher.getWork_num();
            if (StringUtils.isEmpty(code)){
                code = String.valueOf(qingLuTeacher.getId());
            }
            JSONObject jsonObject = dbService.QueryJSONObject("select uid from tb_card_teachstudinfo where uid = ? and infotype = 1", qingLuTeacher.getUser_code());
            if (jsonObject==null){
                dbService.excuteSql(insertDepartmentSql+" ('"+qingLuTeacher.getUser_code()+"','"+code+"','"+qingLuTeacher.getName()+"','"+sex+"',?,?,'"+qingLuTeacher.getIpass()+"','"+qingLuTeacher.getIpass()+"'," +
                        "'"+qingLuTeacher.getEmail()+"','"+qingLuTeacher.getPhone()+"','"+orgcode+"',1,now(),'数据同步','"+qingLuTeacher.getUpdated_at()+"','数据同步',1,'"+qingLuTeacher.getWorker_category()+"')",qingLuTeacher.getBirthday(),qingLuTeacher.getEntry_at());
            }else {
                dbService.excuteSql("update tb_card_teachstudinfo set name = ?,code=?,sex=?,birthday=?,startdate=?,email=?,mobile=?,modifydate=?,infolabel=? where uid = ?",
                        qingLuTeacher.getName(),code,sex,qingLuTeacher.getBirthday(),qingLuTeacher.getEntry_at(),qingLuTeacher.getEmail(),qingLuTeacher.getPhone(),qingLuTeacher.getUpdated_at(),qingLuTeacher.getWorker_category(),qingLuTeacher.getUser_code());
            }

        }
        logger.info("开始同步青鹿教师信息{}",qingLuTeacherRsp.getMessage());
        return null;
    }

    @Override
    public R<?> intoQingLuCard() throws IOException {

        Map<String, String> params = new HashMap<>();
        long second = Instant.now().getEpochSecond();
        // 添加参数
        List<NameValuePair> paramsList = new ArrayList<>();
        paramsList.add(new BasicNameValuePair("app_id", appid));
        paramsList.add(new BasicNameValuePair("timestamp", String.valueOf(second)));
        params.put("app_id", appid);
        String appSecret = Secret;
        String sign = "";
        List<QingLuCard> cardList = dbService.queryList("SELECT ct.uid user_code,cc.cardno user_card FROM tb_card_teachstudinfo ct JOIN tb_card_cardinfo cc ON ct.uid = cc.infoid WHERE cc.STATUS = 1 AND ct.infotype =2", QingLuCard.class);
        params.put("callback_data",JSONObject.toJSONString(cardList));
        params.put("timestamp", String.valueOf(second));
        params.put("user_type","2");
        try {
            sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        paramsList.add(new BasicNameValuePair("sign", sign));
        paramsList.add(new BasicNameValuePair("callback_data",JSONObject.toJSONString(cardList)));
        paramsList.add(new BasicNameValuePair("user_type","2"));
        String url = QingLuUrl.QING_LU_URL_QUERY_CARD_INFO.getUrl();
        QingLuJsonRsp qingLuStudentRsp = null;
        qingLuStudentRsp = httpRequestUtils.doPostRequest(url, params,paramsList, QingLuJsonRsp.class);
        System.out.println("结果："+qingLuStudentRsp.getCode());
        System.out.println("结果信息："+qingLuStudentRsp.getMessage());
        if (!QingLuRspStatus.SUCCESS.getCode().equals(qingLuStudentRsp.getCode())) {
            return R.fail("学生卡号同步失败");
        }


        params = new HashMap<>();
        second = Instant.now().getEpochSecond();
        // 添加参数
        paramsList = new ArrayList<>();
        paramsList.add(new BasicNameValuePair("app_id", appid));
        paramsList.add(new BasicNameValuePair("timestamp", String.valueOf(second)));
        params.put("app_id", appid);
        appSecret = Secret;
        sign = "";
        cardList = dbService.queryList("SELECT ct.uid user_code,cc.cardno user_card FROM tb_card_teachstudinfo ct JOIN tb_card_cardinfo cc ON ct.uid = cc.infoid WHERE cc.STATUS = 1 AND ct.infotype =1", QingLuCard.class);
        params.put("callback_data",JSONObject.toJSONString(cardList));
        params.put("timestamp", String.valueOf(second));
        params.put("user_type","1");
        try {
            sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        paramsList.add(new BasicNameValuePair("sign", sign));
        paramsList.add(new BasicNameValuePair("callback_data",JSONObject.toJSONString(cardList)));
        paramsList.add(new BasicNameValuePair("user_type","1"));
        url = QingLuUrl.QING_LU_URL_QUERY_CARD_INFO.getUrl();
        qingLuStudentRsp = null;
        qingLuStudentRsp = httpRequestUtils.doPostRequest(url, params,paramsList, QingLuJsonRsp.class);
        System.out.println("结果："+qingLuStudentRsp.getCode());
        System.out.println("结果信息："+qingLuStudentRsp.getMessage());
        if (!QingLuRspStatus.SUCCESS.getCode().equals(qingLuStudentRsp.getCode())) {
            return R.fail("教师卡号同步失败");
        }

        return R.ok(qingLuStudentRsp);
    }

    @Override
    public R<?> intoQingLuCardFare() throws IOException {

        Map<String, String> params = new HashMap<>();
        long second = Instant.now().getEpochSecond();
        // 添加参数
        List<NameValuePair> paramsList = new ArrayList<>();
        paramsList.add(new BasicNameValuePair("app_id", appid));
        paramsList.add(new BasicNameValuePair("timestamp", String.valueOf(second)));
        params.put("app_id", appid);
        String appSecret = Secret;
        String sign = "";
        List<QingLuCardFareMain> qingLuCardFareMains = dbService.queryList("SELECT ct.uid user_code,cc.uid card_id,cc.cardno card_no FROM tb_card_teachstudinfo ct JOIN tb_card_cardinfo cc ON ct.uid = cc.infoid WHERE cc.STATUS = 1 AND ct.infotype =2", QingLuCardFareMain.class);
        List<QingLuCardFare> qingLuCardFares = qingLuCardFareMains.stream().map(qingLuCardFareMain -> {
            QingLuCardFare qingLuCardFare = new QingLuCardFare();
            qingLuCardFare.setUser_code(qingLuCardFareMain.getUser_code());
            qingLuCardFare.setCard_no(qingLuCardFareMain.getCard_no());
            qingLuCardFare.setAmount_type(1);
            qingLuCardFare.setOnline_fare(BigDecimal.ZERO);
            qingLuCardFare.setSubodd_fare(BigDecimal.ZERO);
            qingLuCardFare.setOdd_fare(cardManageService.GetCardBalance(qingLuCardFareMain.getCard_id(), BigDecimal.ZERO));
            return qingLuCardFare;
        }).collect(Collectors.toList());
        params.put("cardFare",JSONObject.toJSONString(qingLuCardFares));
        params.put("timestamp", String.valueOf(second));
        params.put("app_type","1");
        params.put("user_type","2");
        try {
            sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        paramsList.add(new BasicNameValuePair("sign", sign));
        paramsList.add(new BasicNameValuePair("cardFare",JSONObject.toJSONString(qingLuCardFares)));
        paramsList.add(new BasicNameValuePair("app_type","1"));
        paramsList.add(new BasicNameValuePair("user_type","2"));
        String url = QingLuUrl.QING_LU_URL_QUERY_CARD_Fare.getUrl();
        QingLuJsonRsp qingLuStudentRsp = null;
        qingLuStudentRsp = httpRequestUtils.doPostRequest(url, params,paramsList, QingLuJsonRsp.class);
        System.out.println("结果："+qingLuStudentRsp.getCode());
        System.out.println("结果信息："+qingLuStudentRsp.getMessage());
        if (!QingLuRspStatus.SUCCESS.getCode().equals(qingLuStudentRsp.getCode())) {
            return R.fail("学生卡余额同步失败");
        }


        params = new HashMap<>();
        second = Instant.now().getEpochSecond();
        // 添加参数
        paramsList = new ArrayList<>();
        paramsList.add(new BasicNameValuePair("app_id", appid));
        paramsList.add(new BasicNameValuePair("timestamp", String.valueOf(second)));
        params.put("app_id", appid);
        appSecret = Secret;
        sign = "";
        qingLuCardFareMains = dbService.queryList("SELECT ct.uid user_code,cc.cardno card_no FROM tb_card_teachstudinfo ct JOIN tb_card_cardinfo cc ON ct.uid = cc.infoid WHERE cc.STATUS = 1 AND ct.infotype =1", QingLuCardFareMain.class);
        qingLuCardFares = qingLuCardFareMains.stream().map(qingLuCardFareMain -> {
            QingLuCardFare qingLuCardFare = new QingLuCardFare();
            qingLuCardFare.setUser_code(qingLuCardFareMain.getUser_code());
            qingLuCardFare.setCard_no(qingLuCardFareMain.getCard_no());
            qingLuCardFare.setAmount_type(1);
            qingLuCardFare.setOnline_fare(BigDecimal.ZERO);
            qingLuCardFare.setSubodd_fare(BigDecimal.ZERO);
            qingLuCardFare.setOdd_fare(cardManageService.GetCardBalance(qingLuCardFareMain.getCard_id(), BigDecimal.ZERO));
            return qingLuCardFare;
        }).collect(Collectors.toList());
        params.put("cardFare",JSONObject.toJSONString(qingLuCardFares));
        params.put("timestamp", String.valueOf(second));
        params.put("app_type","1");
        params.put("user_type","1");
        try {
            sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        paramsList.add(new BasicNameValuePair("sign", sign));
        paramsList.add(new BasicNameValuePair("cardFare",JSONObject.toJSONString(qingLuCardFares)));
        paramsList.add(new BasicNameValuePair("app_type","1"));
        paramsList.add(new BasicNameValuePair("user_type","1"));
        url = QingLuUrl.QING_LU_URL_QUERY_CARD_Fare.getUrl();
        qingLuStudentRsp = null;
        qingLuStudentRsp = httpRequestUtils.doPostRequest(url, params,paramsList, QingLuJsonRsp.class);
        System.out.println("结果："+qingLuStudentRsp.getCode());
        System.out.println("结果信息："+qingLuStudentRsp.getMessage());
        if (!QingLuRspStatus.SUCCESS.getCode().equals(qingLuStudentRsp.getCode())) {
            return R.fail("教师卡余额同步失败");
        }

        return R.ok(qingLuStudentRsp);
    }


    @Override
    public R<?> intoQingLuConsume() throws IOException {

        Map<String, String> params = new HashMap<>();
        long second = Instant.now().getEpochSecond();
        // 添加参数
        List<NameValuePair> paramsList = new ArrayList<>();
        paramsList.add(new BasicNameValuePair("app_id", appid));
        paramsList.add(new BasicNameValuePair("timestamp", String.valueOf(second)));
        params.put("app_id", appid);
        String appSecret = Secret;
        String sign = "";
        List<QingLuConsumeRecharge> qingLuConsumeList = dbService.queryList("SELECT td.cardid uid,td.money,td.infoid user_code,0 amount,td.des purpose,td.des remark,td.recordtime created_at,td.cardno card_no,td.uid record_code from tb_consume_payrecords td LEFT JOIN tb_card_teachstudinfo ct on td.infoid = ct.uid WHERE  td.status= 2 and ct.infotype = 2 and td.isupload=0", QingLuConsumeRecharge.class);
        List<QingLuConsumeRechargeMain> mainList = qingLuConsumeList.stream()
                .map(consumeRecharge -> {
                    QingLuConsumeRechargeMain main = new QingLuConsumeRechargeMain();
                    main.setRecord_code(consumeRecharge.getRecord_code());
                    main.setCard_no(consumeRecharge.getCard_no());
                    main.setMoney(consumeRecharge.getMoney());
                    main.setUser_code(consumeRecharge.getUser_code());
                    main.setPurpose(consumeRecharge.getPurpose());
                    main.setRemark(consumeRecharge.getRemark());
                    main.setCreated_at(consumeRecharge.getCreated_at());
                    return main;
                })
                .collect(Collectors.toList());
        params.put("consumption_data",JSONObject.toJSONString(mainList));
        params.put("timestamp", String.valueOf(second));
        params.put("app_type","1");
        params.put("user_type","2");
        try {
            sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        paramsList.add(new BasicNameValuePair("sign", sign));
        paramsList.add(new BasicNameValuePair("consumption_data",JSONObject.toJSONString(mainList)));
        paramsList.add(new BasicNameValuePair("app_type","1"));
        paramsList.add(new BasicNameValuePair("user_type","2"));
        String url = QingLuUrl.QING_LU_URL_QUERY_CARD_CONSUME.getUrl();
        QingLuJsonRsp luJsonRsp = null;
        luJsonRsp = httpRequestUtils.doPostRequest(url, params,paramsList, QingLuJsonRsp.class);
        System.out.println("结果："+luJsonRsp.getCode());
        System.out.println("结果信息："+luJsonRsp.getMessage());
        if (!QingLuRspStatus.SUCCESS.getCode().equals(luJsonRsp.getCode())) {
            return R.fail("学生消费记录同步失败");
        }
        List<String> uids = qingLuConsumeList.stream().map(qingLuConsumeRecharge -> qingLuConsumeRecharge.getRecord_code()).collect(Collectors.toList());
        String[] uidArray = new String[uids.size()];
        for (int i = 0; i < uids.size(); i++) {
            uidArray[i] = uids.get(i);
        }
        if (uids.size()>0){
            dbService.excuteSql("update tb_consume_payrecords set isupload = 1 where uid IN ('" + String.join("','", uidArray) + "')");
        }


        params = new HashMap<>();
         second = Instant.now().getEpochSecond();
        // 添加参数
        paramsList = new ArrayList<>();
        paramsList.add(new BasicNameValuePair("app_id", appid));
        paramsList.add(new BasicNameValuePair("timestamp", String.valueOf(second)));
        params.put("app_id", appid);
         appSecret = Secret;
         sign = "";
         qingLuConsumeList = dbService.queryList("SELECT td.cardid uid,td.money,td.infoid user_code,0 amount,td.des purpose,td.des remark,td.recordtime created_at,td.cardno card_no,td.uid record_code from tb_consume_payrecords td LEFT JOIN tb_card_teachstudinfo ct on td.infoid = ct.uid WHERE td.status= 2 and ct.infotype = 1 and td.isupload=0", QingLuConsumeRecharge.class);
        mainList = qingLuConsumeList.stream()
                .map(consumeRecharge -> {
                    QingLuConsumeRechargeMain main = new QingLuConsumeRechargeMain();
                    main.setRecord_code(consumeRecharge.getRecord_code());
                    main.setCard_no(consumeRecharge.getCard_no());
                    main.setMoney(consumeRecharge.getMoney());
                    main.setUser_code(consumeRecharge.getUser_code());
                    main.setPurpose(consumeRecharge.getPurpose());
                    main.setRemark(consumeRecharge.getRemark());
                    main.setCreated_at(consumeRecharge.getCreated_at());
                    return main;
                })
                .collect(Collectors.toList());
         params.put("consumption_data",JSONObject.toJSONString(mainList));
        params.put("timestamp", String.valueOf(second));
        params.put("app_type","1");
        params.put("user_type","1");
        try {
            sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        paramsList.add(new BasicNameValuePair("sign", sign));
        paramsList.add(new BasicNameValuePair("consumption_data",JSONObject.toJSONString(mainList)));
        paramsList.add(new BasicNameValuePair("app_type","1"));
        paramsList.add(new BasicNameValuePair("user_type","1"));
         url = QingLuUrl.QING_LU_URL_QUERY_CARD_CONSUME.getUrl();
         luJsonRsp = null;
        luJsonRsp = httpRequestUtils.doPostRequest(url, params,paramsList, QingLuJsonRsp.class);
        System.out.println("结果："+luJsonRsp.getCode());
        System.out.println("结果信息："+luJsonRsp.getMessage());
        if (!QingLuRspStatus.SUCCESS.getCode().equals(luJsonRsp.getCode())) {
            return R.fail("教师消费记录同步失败");
        }
        uids = qingLuConsumeList.stream().map(qingLuConsumeRecharge -> qingLuConsumeRecharge.getRecord_code()).collect(Collectors.toList());
        uidArray = new String[uids.size()];
        for (int i = 0; i < uids.size(); i++) {
            uidArray[i] = uids.get(i);
        }
        if (uids.size()>0){
            dbService.excuteSql("update tb_consume_payrecords set isupload = 1 where uid IN ('" + String.join("','", uidArray) + "')");
        }
        return R.ok(luJsonRsp);
    }


    @Override
    public R<?> intoQingLuRecharge() throws IOException {

        Map<String, String> params = new HashMap<>();
        long second = Instant.now().getEpochSecond();
        // 添加参数
        List<NameValuePair> paramsList = new ArrayList<>();
        paramsList.add(new BasicNameValuePair("app_id", appid));
        paramsList.add(new BasicNameValuePair("timestamp", String.valueOf(second)));
        params.put("app_id", appid);
        String appSecret = Secret;
        String sign = "";
        List<QingLuConsumeRecharge> qingLuConsumeList = dbService.queryList("SELECT td.cardid uid,td.money,td.infoid user_code,cd.balance amount,td.des purpose,td.des remark,td.tradedate created_at,td.cardno card_no,td.uid record_code from tb_card_transaction_detail td LEFT JOIN tb_card_teachstudinfo ct on td.infoid = ct.uid WHERE cd.`status` = 1 and td.tradetype= 1 and ct.infotype = 2 and (td.isupload=0 or td.isupload is null)", QingLuConsumeRecharge.class);
        List<QingLuConsumeRechargeMain> mainList = qingLuConsumeList.stream()
                .map(consumeRecharge -> {
                    QingLuConsumeRechargeMain main = new QingLuConsumeRechargeMain();
                    main.setRecord_code(consumeRecharge.getRecord_code());
                    main.setCard_no(consumeRecharge.getCard_no());
                    main.setMoney(consumeRecharge.getMoney());
                    main.setUser_code(consumeRecharge.getUser_code());
                    main.setPurpose(consumeRecharge.getPurpose());
                    main.setRemark(consumeRecharge.getRemark());
                    main.setCreated_at(consumeRecharge.getCreated_at());
                    return main;
                })
                .collect(Collectors.toList());
        params.put("recharge_data",JSONObject.toJSONString(mainList));
        params.put("timestamp", String.valueOf(second));
        params.put("app_type","1");
        params.put("user_type","2");
        try {
            sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        paramsList.add(new BasicNameValuePair("sign", sign));
        paramsList.add(new BasicNameValuePair("recharge_data",JSONObject.toJSONString(mainList)));
        paramsList.add(new BasicNameValuePair("app_type","1"));
        paramsList.add(new BasicNameValuePair("user_type","2"));
        String url = QingLuUrl.QING_LU_URL_QUERY_CARD_RECHARGE.getUrl();
        QingLuJsonRsp luJsonRsp = null;
        luJsonRsp = httpRequestUtils.doPostRequest(url, params,paramsList, QingLuJsonRsp.class);
        System.out.println("结果："+luJsonRsp.getCode());
        System.out.println("结果信息："+luJsonRsp.getMessage());
        System.out.println(luJsonRsp.getMessage());
        if (!QingLuRspStatus.SUCCESS.getCode().equals(luJsonRsp.getCode())) {
            return R.fail("学生充值记录同步失败");
        }
        List<String> uids = qingLuConsumeList.stream().map(qingLuConsumeRecharge -> qingLuConsumeRecharge.getRecord_code()).collect(Collectors.toList());
        String[] uidArray = new String[uids.size()];
        for (int i = 0; i < uids.size(); i++) {
            uidArray[i] = uids.get(i);
        }
        if (uids.size()>0){
            dbService.excuteSql("update tb_card_transaction_detail set isupload = 1 where uid IN ('" + String.join("','", uidArray) + "')");
        }
        params = new HashMap<>();
        second = Instant.now().getEpochSecond();
        // 添加参数
        paramsList = new ArrayList<>();
        paramsList.add(new BasicNameValuePair("app_id", appid));
        paramsList.add(new BasicNameValuePair("timestamp", String.valueOf(second)));
        params.put("app_id", appid);
        appSecret = Secret;
        sign = "";
        qingLuConsumeList = dbService.queryList("SELECT td.cardid uid,td.money,td.infoid user_code,0 amount,td.des purpose,td.des remark,td.tradedate created_at,td.cardno card_no,td.uid record_code from tb_card_transaction_detail td LEFT JOIN tb_card_teachstudinfo ct on td.infoid = ct.uid WHERE cd.`status` = 1 and td.tradetype= 1 and ct.infotype = 1 and (td.isupload=0 or td.isupload is null)", QingLuConsumeRecharge.class);
        mainList = qingLuConsumeList.stream()
                .map(consumeRecharge -> {
                    QingLuConsumeRechargeMain main = new QingLuConsumeRechargeMain();
                    main.setRecord_code(consumeRecharge.getRecord_code());
                    main.setCard_no(consumeRecharge.getCard_no());
                    main.setMoney(consumeRecharge.getMoney());
                    main.setUser_code(consumeRecharge.getUser_code());
                    main.setPurpose(consumeRecharge.getPurpose());
                    main.setRemark(consumeRecharge.getRemark());
                    main.setCreated_at(consumeRecharge.getCreated_at());
                    return main;
                })
                .collect(Collectors.toList());
        params.put("recharge_data",JSONObject.toJSONString(mainList));
        params.put("timestamp", String.valueOf(second));
        params.put("app_type","1");
        params.put("user_type","1");
        try {
            sign = SignUtils.generateSign(params, appSecret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sign)) {
            return R.fail("生成签名失败");
        }
        params.put("sign", sign);
        paramsList.add(new BasicNameValuePair("sign", sign));
        paramsList.add(new BasicNameValuePair("recharge_data",JSONObject.toJSONString(mainList)));
        paramsList.add(new BasicNameValuePair("app_type","1"));
        paramsList.add(new BasicNameValuePair("user_type","1"));
        url = QingLuUrl.QING_LU_URL_QUERY_CARD_RECHARGE.getUrl();
        luJsonRsp = null;
        luJsonRsp = httpRequestUtils.doPostRequest(url, params,paramsList, QingLuJsonRsp.class);
        System.out.println("结果："+luJsonRsp.getCode());
        System.out.println("结果信息："+luJsonRsp.getMessage());
        if (!QingLuRspStatus.SUCCESS.getCode().equals(luJsonRsp.getCode())) {
            return R.fail("教师充值记录同步失败");
        }
        uids = qingLuConsumeList.stream().map(qingLuConsumeRecharge -> qingLuConsumeRecharge.getRecord_code()).collect(Collectors.toList());
        uidArray = new String[uids.size()];
        for (int i = 0; i < uids.size(); i++) {
            uidArray[i] = uids.get(i);
        }
        if (uids.size()>0){
            dbService.excuteSql("update tb_card_transaction_detail set isupload = 1 where uid IN ('" + String.join("','", uidArray) + "')");
        }
        return R.ok(luJsonRsp);
    }
}
