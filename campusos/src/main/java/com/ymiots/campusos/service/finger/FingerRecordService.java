package com.ymiots.campusos.service.finger;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.JsonData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

@Repository
public class FingerRecordService extends BaseService {

    @Autowired
    private UserRedis userredis;

    public JsonData getFingerRecord(HttpServletRequest request, HttpServletResponse response,String key,String orgcode,boolean viewchild,String areacode,String devid,String starttime,String endtime,int start,int limit){
        String fields = "fr.uid,fr.devid,da.devname,fr.machineid,fr.infoid,ct.name as infoname,ct.code as infocode,date_format(fr.recordtime,'%Y-%m-%d %H:%i:%s') as recordtime,getorgname(ct.orgcode) as orgname ";
        String table = "tb_finger_record fr " +
                "LEFT JOIN tb_card_teachstudinfo ct ON ct.uid=fr.infoid " +
                "LEFT JOIN tb_dev_accesscontroller da ON fr.devid=da.uid ";
        String where = "1=1 ";
        if (StringUtils.isNotBlank(key)) {
            String infoSQL = "SELECT uid FROM tb_card_teachstudinfo WHERE code='" + key + "' OR name like '%" + key + "%'";
            JSONArray infoja = dbService.QueryList(infoSQL);
            List<String> infoidList = new ArrayList<String>();
            if (!infoja.isEmpty()) {
                for (int i = 0; i < infoja.size(); i++) {
                    infoidList.add(infoja.getJSONObject(i).getString("uid"));
                }
                where += " AND fr.infoid IN ('"+String.join("','",infoidList)+"')";
            }
        }
        if (StringUtils.isNotBlank(devid)) {
            where += " AND fr.devid = '" + devid + "'";
        }
        if (!StringUtils.isBlank(areacode)) {
            if(viewchild) {
                where += " and da.areacode like '" + areacode + "%' ";
            }else {
                where += " and da.areacode='" + areacode + "' ";
            }
        }
        if (StringUtils.isNotBlank(orgcode)) {
            if(viewchild) {
                where += " and ct.orgcode like '" + areacode + "%' ";
            }else {
                where += " and ct.orgcode='" + areacode + "' ";
            }
        }
        where += " AND fr.recordtime BETWEEN '"+starttime+"' AND '"+endtime+"' ";
        String orderby = ExtSort.Orderby(request,"fr.recordtime DESC");
        return dbService.QueryJsonData(fields, table, where, orderby, start, limit);
    }

    public JsonData getFingerDevList(HttpServletRequest request, HttpServletResponse response,String areacode,String key,int start,int limit){
        String fields="ac.uid,ac.machineid,ac.devname";
        String table="tb_dev_accesscontroller ac";
        //如果为普通管理员，需要受二级授权控制
        if(userredis.get(request).getUsertype()==1){
            String userid=userredis.getUserId(request);
            table+= " inner join tb_dev_areaframework_user ua on ua.areacode=ac.areacode and ua.userid='"+userid+"' ";
        }
        String where=" ac.devclass = 10 ";
        if (!StringUtils.isBlank(key)) {
            where += " and (ac.devname like '%" + key + "%' or ac.machineid='" + key + "') ";
        }
        String orderby = ExtSort.Orderby(request, " ac.machineid asc ");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;

    }
}
