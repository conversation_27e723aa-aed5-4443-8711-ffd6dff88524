package com.ymiots.campusos.service.dev;

import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Repository
public class PlatenoService extends BaseService {
	@Autowired
	SysLogsService syslogs;

	@Autowired
	UserRedis userredis;

	// 查询停车场设备
	public JsonData getDeviceList(HttpServletRequest request, HttpServletResponse response, String serveruid,
								  String key, int start, int limit) {
		String fields = "ac.uid,ac.devsn,ac.machineid,ac.devname";
		String table = "tb_dev_accesscontroller ac join tb_sys_dictionary sd on sd.code = ac.devclass and sd.groupcode = 'SYS0000031' and code = 16 ";
		//如果为普通管理员，需要受二级授权控制
		if(userredis.get(request).getUsertype()==1){
			String userid=userredis.getUserId(request);
			table+= " inner join tb_dev_areaframework_user ua on ua.areacode=ac.areacode and ua.userid='"+userid+"' ";
		}
		String where = " 1=1 ";
		if (!StringUtils.isBlank(serveruid)) {
			where += " and ac.server_uid='" + serveruid + "' ";
		}
		if (!StringUtils.isBlank(key)) {
			where += " and (ac.devname like '%" + key + "%' or ac.machineid='" + key + "') ";
		}
		String orderby = ExtSort.Orderby(request, " ac.machineid desc ");
		JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}

	// 根据设备查询车辆信息
	public JsonData getPlatenoList(HttpServletRequest request, HttpServletResponse response, String devid, String key,
								   String infotype, int start, int limit) {
		String fields = " p.uid,p.ishandle,p.plateno,p.name,p.infotype,p.handlenum,p.handletime,p.msg";
		String table = "tb_park_plateno p inner join tb_dev_accesscontroller ac on ac.uid = p.devid ";
		String where = "1=1";
		if(userredis.get(request).getUsertype()==1){
			String userid=userredis.getUserId(request);
			table+= " inner join tb_dev_areaframework_user ua on ua.areacode=ac.areacode and ua.userid='"+userid+"' ";
		}
		if (!StringUtils.isBlank(devid)) {
			where += " and p.devid='" + devid + "'";
		}
		if (!StringUtils.isBlank(infotype)) {
			where += " and p.infotype='" + infotype + "' ";
		}
		if (!StringUtils.isBlank(key)) {
			where += " and (p.name like '%" + key + "%') ";
		}
		String orderby = ExtSort.Orderby(request, " p.handletime desc ");
		JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}

	// 为设备重下车辆信息
	public JsonResult redownload(HttpServletRequest request, HttpServletResponse response, String uid) {
		if (StringUtils.isBlank(uid)) {
			return Json.getJsonResult(false, "uid为空");
		}
		String updateSQL = "UPDATE tb_park_plateno SET ishandle=5,handlenum=0 WHERE uid IN ('" + String.join("','", uid.split(",")) + "') AND ishandle IN (1,3,4,5,6) ";
		dbService.excuteSql(updateSQL);
		String updateSQL2 = "UPDATE tb_park_plateno SET ishandle=0,handlenum=0 WHERE uid IN ('" + String.join("','", uid.split(",")) + "') AND ishandle IN (0,2)";
		dbService.excuteSql(updateSQL2);
		return Json.getJsonResult(true);
	}

	// 将车辆信息加入黑名单
	public JsonResult addBlack(HttpServletRequest request, HttpServletResponse response, String uid) {
		if (StringUtils.isBlank(uid)) {
			return Json.getJsonResult(false, "uid为空");
		}
		String insertSQL = "INSERT INTO tb_visitor_black (uid,name,idcard,sex,reason,createdate,creatorid,status) " +
				"SELECT uuid(),vv.name,vv.idcard,vv.sex,'车辆拉黑',now(),'"+userredis.get(request).getUid()+"',1 FROM tb_visitor_visitorinfo vv INNER JOIN tb_park_plateno pp ON pp.infoid=vv.uid WHERE pp.infotype=2 AND pp.uid IN ('" + String.join("','", uid.split(",")) + "') ";
		dbService.excuteSql(insertSQL);
		String updatePlateno = "UPDATE tb_park_plateno SET ishandle=3,handlenum=0 WHERE infotype=2 AND uid IN ('" + String.join("','", uid.split(",")) + "') AND ishandle in (1,3,4,5,6)";
		dbService.excuteSql(updatePlateno);
		String delPlateno = "delete from tb_park_plateno where infotype=2 AND uid IN ('" + String.join("','", uid.split(",")) + "')  and ishandle in (0,2) ";
		dbService.excuteSql(delPlateno);
		return Json.getJsonResult(true);
	}

	// 删除设备中车辆信息
	public JsonResult DelClick(HttpServletRequest request, HttpServletResponse response, String uid) {
		if (StringUtils.isBlank(uid)) {
			return Json.getJsonResult(false, "uid为空");
		}
		String updatePlateno = "update tb_park_plateno set ishandle=3,handlenum=0 where find_in_set(infoid,'" + uid + "')>0  and ishandle in (1,3,4,5,6) ";
		dbService.excuteSql(updatePlateno);
		String delPlateno = "delete from tb_park_plateno where find_in_set(infoid,'" + uid + "')>0  and ishandle in(0,2) ";
		dbService.excuteSql(delPlateno);
		return Json.getJsonResult(true);
	}
}
