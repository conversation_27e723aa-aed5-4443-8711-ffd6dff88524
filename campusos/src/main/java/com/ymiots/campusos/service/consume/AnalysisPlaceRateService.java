package com.ymiots.campusos.service.consume;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.*;
import com.ymiots.framework.websocket.WebSocketSets;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class AnalysisPlaceRateService extends BaseService {

    @Autowired
    private UserRedis userRedis;

    public JsonData getPlaceList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit, boolean viewchild) {
        String fields = "cmp.uid,cmp.merid,cmp.code,cmp.name,cmp.status cmpstatus,cmp.status";
        String table = "tb_consume_merchant_place cmp ";
        SysUser sysUser = userRedis.get(request);
        if (sysUser.getUsertype() == 1) {
            table += " inner join tb_consume_merchant_user cmu on cmu.merid = cmp.merid and cmu.userid = '" + sysUser.getUid() + "'";
        }
        String where = " cmp.status<>2 ";
        if (StringUtils.isNotBlank(key)) {
            where += " and (cmp.code like '%" + key + "%' or cmp.name like '%" + key + "%')";
        }
        if(!viewchild){
            where += "and cmp.status=1 ";
        }
        JsonData db = dbService.QueryJsonData(fields, table, where, "cmp.status asc,cmp.createdate ASC", start, limit);
        return db;
    }

    public JsonData getPlaceRateData(HttpServletRequest request, HttpServletResponse response,String placeid, boolean viewchild, String starttime, String endtime, int start, int limit){
        String fields = "capr.uid,date_format(capr.daily,'%Y-%m-%d') as daily,capr.placeid,cmp.code as placecode,cmp.name as placename," +
                "capr.scheme1,capr.schemetime1,capr.scheme2,capr.schemetime2,capr.scheme3,capr.schemetime3,capr.scheme4,capr.schemetime4,capr.scheme5,capr.schemetime5,capr.scheme6,capr.schemetime6,capr.scheme7,capr.schemetime7,capr.scheme8,capr.schemetime8,capr.scheme9,capr.schemetime9,capr.scheme10,capr.schemetime10,capr.schememoney,capr.schemetimes,capr.createdate  ";
        String table = "tb_consume_analysis_place_rate capr " +
                "INNER JOIN tb_consume_merchant_place cmp ON cmp.uid=capr.placeid AND cmp.status<>2";
        SysUser sysUser = userRedis.get(request);
        if (sysUser.getUsertype() == 1) {
            table += " inner join tb_consume_merchant_user cmu on cmu.merid = cmp.merid and cmu.userid = '" + sysUser.getUid() + "'";
        }
        String where = " capr.daily BETWEEN '" + starttime + "' AND '" + endtime + "'";
        if (!viewchild) {
            where += " AND cmp.status=1";
        }
        if (StringUtils.isNotBlank(placeid)) {
            where += " AND capr.placeid IN ('"+String.join("','",placeid.split(","))+"')";
        }
        JsonData result = dbService.QueryJsonData(fields, table, where, "capr.daily DESC", start, limit);
        String sql = "select '合计' as 'daily',ifnull(sum(capr.scheme1),0) as scheme1,ifnull(sum(capr.schemetime1),0) as schemetime1,ifnull(sum(capr.scheme2),0) as scheme2," +
                "ifnull(sum(capr.schemetime2),0) as schemetime2,ifnull(sum(capr.scheme3),0) as scheme3,ifnull(sum(capr.schemetime3),0) as schemetime3,ifnull(sum(capr.scheme4),0) as scheme4," +
                "ifnull(sum(capr.schemetime4),0) as schemetime4,ifnull(sum(capr.scheme5),0) as scheme5,ifnull(sum(capr.schemetime5),0) as schemetime5,ifnull(sum(capr.scheme6),0) as scheme6," +
                "ifnull(sum(capr.schemetime6),0) as schemetime6,ifnull(sum(capr.scheme7),0) as scheme7,ifnull(sum(capr.schemetime7),0) as schemetime7,ifnull(sum(capr.scheme8),0) as scheme8," +
                "ifnull(sum(capr.schemetime8),0) as schemetime8,ifnull(sum(capr.scheme9),0) as scheme9,ifnull(sum(capr.schemetime9),0) as schemetime9,ifnull(sum(capr.scheme10),0) as scheme10," +
                "ifnull(sum(capr.schemetime10),0) as schemetime10,ifnull(sum(capr.schememoney),0) as schememoney,ifnull(sum(capr.schemetimes),0) as schemetimes " +
                "from tb_consume_analysis_place_rate capr " +
                "INNER JOIN tb_consume_merchant_place cmp ON cmp.uid=capr.placeid AND cmp.status<>2 ";
        if (sysUser.getUsertype() == 1) {
            sql += " inner join tb_consume_merchant_user cmu on cmu.merid = cmp.merid and cmu.userid = '" + sysUser.getUid() + "'";
        }
        sql += " WHERE" + where;
        JSONArray list = dbService.QueryList(sql);
        result.getData().addAll(list);
        return result;
    }

    public JsonData getSchemeName(HttpServletRequest request, HttpServletResponse response) {
        String sql = "SELECT DISTINCT sc.code,d.name FROM tb_consume_scheme sc LEFT JOIN tb_sys_dictionary d ON d.code=sc.code and d.groupcode='SYS0000051'  ORDER BY code ASC";
        return dbService.QueryJsonData(sql);
    }

    public String ExportPlaceRate(HttpServletRequest request, HttpServletResponse response, String placeid, boolean viewchild, String starttime, String endtime,String currentPage,String pageSize,String size) throws IOException {
        String schemeSQL = "SELECT code,name FROM tb_consume_scheme ORDER BY code ASC";
        JSONArray schemeja = dbService.QueryList(schemeSQL);
        List<String> schemeCode = new ArrayList<String>();
        List<String> schemeName = new ArrayList<String>();
        List<String> schemetimeCode = new ArrayList<String>();
        if (!schemeja.isEmpty()) {
            for (int i = 0; i < schemeja.size(); i++) {
                String code = schemeja.getJSONObject(i).getString("code");
                schemeCode.add("scheme" + code);
                schemetimeCode.add("schemetime"+code);
                schemeName.add(schemeja.getJSONObject(i).getString("name"));
            }
        }
        int star = 1;
        JsonData result = new JsonData();
        if (currentPage.equals("1")){
            result = getPlaceRateData(request, response,placeid, viewchild, starttime, endtime, 0, Integer.valueOf(size));
        }else if(Integer.valueOf(currentPage)>star) {
            result = getPlaceRateData(request, response,placeid, viewchild, starttime, endtime,  (Integer.valueOf(currentPage)-1)*Integer.valueOf(size), Integer.valueOf(size));
        }else {
            result = getPlaceRateData(request, response,placeid, viewchild, starttime, endtime, 0,0);
        }

        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "日期");
        cell.put("datakey", "daily");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "营业场所编号");
        cell.put("datakey", "placecode");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "营业场所名称");
        cell.put("datakey", "placename");
        cell.put("size", 15);
        cells.add(cell);

        for (int i = 0; i < schemeCode.size(); i++) {
            cell = new JSONObject();
            cell.put("name", schemeName.get(i));
            cell.put("datakey", schemeCode.get(i));
            cell.put("size", 10);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", schemeName.get(i)+"次数");
            cell.put("datakey", schemetimeCode.get(i));
            cell.put("size", 10);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "合计金额");
        cell.put("datakey", "schememoney");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "合计次数");
        cell.put("datakey", "schemetimes");
        cell.put("size", 15);
        cells.add(cell);

        List<CellRangeAddress> slist = new ArrayList<CellRangeAddress>();
        List<JSONArray> heads = new ArrayList<JSONArray>();
        String title= "场所每日消费";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";
        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title, slist, heads,starttime,endtime,"1","1");
        return filepath;

    }

    public JsonData getAnalysisDailyLits(HttpServletRequest request, HttpServletResponse response, String start, String end){
        String fields="distinct daily as dayname";
        String table="tb_consume_analysis_place_rate";
        String where="daily between '"+start+"' and '"+end+"' ";
        String sort= ExtSort.Orderby(request, "daily asc");
        JsonData result=dbService.QueryJsonData(fields, table, where, sort, 0, 0);
        return result;
    }

    public JsonResult AnalysisDaily(HttpServletRequest request, HttpServletResponse response, String daily){
        Date day= DateHelper.parse(daily+" 00:00:00");
        if (day.after(DateHelper.addDays(new Date(), -1))){
            String lastday=DateHelper.format(DateHelper.addDays(new Date(), -1), "yyyy-MM-dd");
            return Json.getJsonResult("无法分析"+lastday+"以后的数据");
        }
        dbService.excuteSql("call sp_consume_place_analysis(?)",daily);
        return Json.getJsonResult(true);
    }

    public JsonResult AnalysisHandle(HttpServletRequest request, HttpServletResponse response, final String startdate, final String enddate){
        try {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    long startTimeMillis=System.currentTimeMillis();
                    try {
                        dbService.excuteSql("call sp_consume_place_analysis_handle(?,?)", startdate, enddate);
                        JSONObject msg = new JSONObject();
                        msg.put("msgtype", 1000);
                        msg.put("msg", "分析成功");
                        WebSocketSets.getInstance().send("sysmsg", msg.toJSONString());
                        long endTimeMillis=System.currentTimeMillis();
                        Log.info(AnalysisPersonRateService.class,"场所每日消费统计一键分析执行花费时间:"+(endTimeMillis-startTimeMillis));
                    } catch (Exception e) {
                        Log.error(AnalysisPersonRateService.class, "场所每日消费统计一键分析：" + e.getMessage());
                    }
                }
            }).start();
            return Json.getJsonResult(true);
        } catch (Exception e) {
            Log.error(AnalysisPersonRateService.class, "场所每日消费统计一键分析：" + e.getMessage());
            return Json.getJsonResult(false, e.getMessage());
        }
    }
}
