package com.ymiots.campusos.service.weixin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class WeixinUserService extends BaseService{

    public JsonData getWeixinUserList(HttpServletRequest request, HttpServletResponse response, String key,String usertype, int start, int limit){
    	if(StringUtils.isBlank(usertype) || !usertype.equals("4")) {
        	String fields = "wu.uid, wu.headimgurl as img,ct.name,ct.code ,getorgname(ct.orgcode) as orgname, wu.nickname,sd.name as sex,d.name usertype,date_format(wu.lastlogintime, '%Y-%m-%d %H:%i:%s') as lastlogintime, wu.logintimes ,wu.status";
            String table = "tb_weixin_user wu "+
                           "LEFT JOIN tb_sys_dictionary sd on wu.sex = sd.code and sd.groupcode='SYS0000022' "+
                           "LEFT JOIN tb_sys_dictionary d on wu.usertype = d.code and d.groupcode = 'SYS0000029' "+
                           "LEFT JOIN tb_card_teachstudinfo ct on wu.docid = ct.uid  ";
            String where = "1=1 ";
            if (StringUtils.isNotBlank(key)){
                where += " and (ct.name like '%"+key+"%' or ct.code like '%"+key+"%'"+" or wu.nickname like '%"+key+"%')";
            }
            if (StringUtils.isNotBlank(usertype) && Integer.parseInt(usertype)  >= 0 ){
                where += " and wu.usertype='"+usertype+"' ";
            }else {
            	  where += " and wu.usertype<>'4' ";
            }
            JsonData db = dbService.QueryJsonData(fields, table, where, "wu.createdate desc", start, limit);
            return db;
    	}else {
    		String fields = "wu.uid, wu.headimgurl as img,ct.name,concat(company,department,`position`) as orgname, wu.nickname,sd.name as sex,d.name usertype,date_format(wu.lastlogintime, '%Y-%m-%d %H:%i:%s') as lastlogintime, wu.logintimes, wu.status";
            String table = "tb_weixin_user wu "+
                           "LEFT JOIN tb_sys_dictionary sd on wu.sex = sd.code and sd.groupcode='SYS0000022' "+
                           "LEFT JOIN tb_sys_dictionary d on wu.usertype = d.code and d.groupcode = 'SYS0000029' "+
                           "LEFT JOIN tb_visitor_visitorinfo ct on wu.docid = ct.uid  ";
            String where = "1=1 ";
            if (StringUtils.isNotBlank(key)){
                where += " and (ct.name like '%"+key+"%' or wu.nickname like '%"+key+"%')";
            }
            where += " and wu.usertype='4' ";
            JsonData db = dbService.QueryJsonData(fields, table, where, "wu.createdate desc", start, limit);
            return db;
    	}
    }

    public JsonResult updateWeixinStatus(String uid, String status){
        if(StringUtils.isBlank(uid)){
            return Json.getJsonResult(false, "uid为空！");
        }
        List<String> onStatus = new ArrayList<String>();
        List<String> offStatus = new ArrayList<String>();
        String[] statuslist = status.split(",");
        String[] uids = uid.split(",");
        for (int i = 0; i < statuslist.length; i++) {
            if ("1".equals(statuslist[i])) {
                onStatus.add(uids[i]);
            }else if ("0".equals(statuslist[i])){
                offStatus.add(uids[i]);
            }
        }
        String updateSQL = "UPDATE tb_weixin_user SET status=0 WHERE uid IN('"+String.join("','",onStatus)+"') AND status=1";
        dbService.excuteSql(updateSQL);
        String updateSQL2 = "UPDATE tb_weixin_user SET status=1 WHERE uid IN('"+String.join("','",offStatus)+"') AND status=0";
        dbService.excuteSql(updateSQL2);
        return Json.getJsonResult(true);
    }

    public JsonResult DeleteWeixinUser(String uid){
        String sql = "delete from tb_weixin_user where uid in('"+String.join("','",uid.split(","))+"')";
        dbService.excuteSql(sql);
        return Json.getJsonResult(true);
    }

    public JsonResult delWeiXinUserFromCP(HttpServletRequest request, HttpServletResponse response,String uid,String nodeid,String appid,String timestamp,String signature ){
        if(!WebConfig.EnableCampusCPService()) {
            return Json.getJsonResult(false,"节点未受平台控制");
        }
        if (StringUtils.isBlank(appid)) {
            return Json.getJsonResult(false,"appid is null");
        }
        if (StringUtils.isBlank(signature)) {
            return Json.getJsonResult(false,"signature is null");
        }
        if (StringUtils.isBlank(nodeid)) {
            return Json.getJsonResult(false,"nodeid is null");
        }
        if (StringUtils.isBlank(timestamp)) {
            return Json.getJsonResult(false,"timestamp is null");
        }
        if(!appid.equals(WebConfig.getCampusCPAppId())) {
            return Json.getJsonResult(false,"appid is error");
        }
        if(!nodeid.equals(WebConfig.getNodeid())) {
            return Json.getJsonResult(false,"nodeid is error");
        }
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "uid is null");
        }
        String sql = "delete from tb_weixin_user where uid in('"+String.join("','",uid.split(","))+"')";
        dbService.excuteSql(sql);
        return Json.getJsonResult(true);
    }

    public JsonResult updateWeixinStatusFromCP(HttpServletRequest request, HttpServletResponse response,String uid,String status,String nodeid,String appid,String timestamp,String signature){
        if(!WebConfig.EnableCampusCPService()) {
            return Json.getJsonResult(false,"节点未受平台控制");
        }
        if (StringUtils.isBlank(appid)) {
            return Json.getJsonResult(false,"appid is null");
        }
        if (StringUtils.isBlank(signature)) {
            return Json.getJsonResult(false,"signature is null");
        }
        if (StringUtils.isBlank(nodeid)) {
            return Json.getJsonResult(false,"nodeid is null");
        }
        if (StringUtils.isBlank(timestamp)) {
            return Json.getJsonResult(false,"timestamp is null");
        }
        if(!appid.equals(WebConfig.getCampusCPAppId())) {
            return Json.getJsonResult(false,"appid is error");
        }
        if(!nodeid.equals(WebConfig.getNodeid())) {
            return Json.getJsonResult(false,"nodeid is error");
        }
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "uid is null");
        }
        if (StringUtils.isBlank(status)) {
            return Json.getJsonResult(false, "status is null");
        }
        return updateWeixinStatus(uid, status);
    }

    public String export(HttpServletRequest request, HttpServletResponse response, String key, String usertype) throws IOException {
        JsonData result = getWeixinUserList(request, response, key, usertype, 0, 0);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray data = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();
        for (Object datum : data) {
            JSONObject jo = (JSONObject) datum;
            Integer status = jo.getInteger("status");
            if (status == 1) {
                jo.put("status", "有效");
            } else {
                jo.put("status", "无效");
            }
        }
        cell = new JSONObject();
        cell.put("name", "微信昵称");
        cell.put("datakey", "nickname");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "学工号");
        cell.put("datakey", "code");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属单位");
        cell.put("datakey", "orgname");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "性别");
        cell.put("datakey", "sex");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "用户类型");
        cell.put("datakey", "usertype");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "账号状态");
        cell.put("datakey", "status");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "登录次数");
        cell.put("datakey", "logintimes");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "最后登录时间");
        cell.put("datakey", "lastlogintime");
        cell.put("size", 15);
        cells.add(cell);

        String title= "微信用户信息";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(data, cells, WebConfig.getUploaddir(), "temp",filename,title);
        return filepath;
    }
}
