package com.ymiots.campusos.service.card.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dao.impl.CardBalanceDaoImpl;
import com.ymiots.campusos.dto.card.CardTeachStudInfoDTO;
import com.ymiots.campusos.service.card.CardBalanceService;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.utils.excel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Null;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

@Service
public class CardBalanceServiceImpl implements CardBalanceService {

    @Autowired
    private CardBalanceDaoImpl cardBalanceDao;

    @Override
    public R<List<CardTeachStudInfoDTO>> getUserBalance(HttpServletRequest request, String orgCode, boolean viewChild,String key, int page, int size) {
        return cardBalanceDao.selUserBalance(orgCode,viewChild,key,page,size);
    }

    @Override
    public R<Null> exportUserBalance(String orgCode, boolean viewChild, String key) {
        R<List<CardTeachStudInfoDTO>> userBalance = cardBalanceDao.selUserBalance(orgCode, viewChild, key, 0, 0);
        if (!userBalance.isSuccess()) {
            return R.fail("无导出数据");
        }
        List<CardTeachStudInfoDTO> data = userBalance.getData();
        if (CollectionUtil.isEmpty(data)) {
            return R.fail("无导出数据");
        }
        LinkedHashMap<Integer, CardTeachStudInfoDTO> map = new LinkedHashMap<Integer, CardTeachStudInfoDTO>();
        Sheet<CardTeachStudInfoDTO> sheet = new Sheet<>(map, CardTeachStudInfoDTO.class);
        int row = 1;
        for (CardTeachStudInfoDTO vo : data) {
            map.put(row, vo);
            row++;
        }
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String extFilePath = "temp/用户余额表" + format + ".xlsx";
        String filePath = WebConfig.getUploaddir() + extFilePath;
        com.ymiots.framework.utils.excel.ExcelUtil.write(sheet, filePath, "用户余额表", 10,null,null);
        return R.ok(extFilePath);
    }
}
