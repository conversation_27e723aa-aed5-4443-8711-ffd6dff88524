package com.ymiots.campusos.service.consume;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.ExcelUtil;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.JsonData;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

@Repository
public class AnalysisPersonalConsumeService extends BaseService {

    @Autowired
    private AnalysisEverydayService everydayService;

    @Autowired
    private UserRedis userRedis;

    public JsonData getPersonalConsumeData(HttpServletRequest request, HttpServletResponse response, String orgcode, String key, String starttime, String endtime, String status, boolean viewchild, int start, int limit) {
        String schemeSQL = "SELECT DISTINCT code FROM tb_consume_scheme";
        JSONArray schemeja = dbService.QueryList(schemeSQL);
        String field = "";
        if (!schemeja.isEmpty()) {
            for (int i = 0; i < schemeja.size(); i++) {
                String code = schemeja.getJSONObject(i).getString("code");
                field += "SUM(capr.scheme" + code + ") as scheme" + code + ",SUM(capr.schemetime" + code + ") as schemetime" + code + ",";
            }
        }
        String sql = "SELECT capr.code,capr.name," + field + "SUM(schememoney) as schememoney,SUM(schemetimes) as schemetimes " +
                "FROM tb_consume_analysis_person_rate capr LEFT JOIN  tb_card_teachstudinfo ct on ct.uid=capr.infoid ";
        SysUser sysUser = userRedis.get(request);
        if (sysUser.getUsertype() == 1) {
            String userid = sysUser.getUid();
            sql += " inner join tb_card_orgframework co on co.code=ct.orgcode " +
                    "inner join tb_card_orgframework_user cou on cou.orgcode=co.code " +
                    "and cou.userid = '" + userid + "'";
        }
        String where = " capr.daily BETWEEN '" + starttime + "' AND '" + endtime + "'";
        if(StringUtils.isNotBlank(status) && !"-1".equals(status)){
            where += " AND ct.status="+status;
        }
        if (StringUtils.isNotBlank(orgcode)) {
            if (viewchild) {
                where += " AND capr.orgcode like '" + orgcode + "%'";
            } else {
                where += " AND capr.orgcode='" + orgcode + "'";
            }
        }
        if (StringUtils.isNotBlank(key)) {
            where += " AND (capr.code='" + key + "' or capr.name like '%" + key + "%')";
        }
        sql += "WHERE"+where;

        String totalSQL = "("+sql+" GROUP BY capr.code,capr.name) a";

        if (start == 0 && limit == 0) {
            sql += " GROUP BY capr.code,capr.name ";
        } else {
            sql += " GROUP BY capr.code,capr.name limit " + start + "," + limit;
        }
        String table = "tb_consume_analysis_person_rate capr LEFT JOIN  tb_card_teachstudinfo ct on ct.uid=capr.infoid ";
        if (sysUser.getUsertype() == 1) {
            String userid = sysUser.getUid();
            table += " inner join tb_card_orgframework co on co.code=ct.orgcode " +
                    "inner join tb_card_orgframework_user cou on cou.orgcode=co.code " +
                    "and cou.userid = '" + userid + "'";
        }
        int total = dbService.getCount(totalSQL, "");
        JSONArray list = everydayService.getSumConsume("code",table, "WHERE "+where);
        JsonData result = dbService.QueryJsonData(sql);
        result.getData().addAll(list);
        result.setTotal(total);
        return result;
    }

    public JsonData getSchemeName(HttpServletRequest request, HttpServletResponse response) {
        String sql = "SELECT DISTINCT sc.code,d.name FROM tb_consume_scheme sc LEFT JOIN tb_sys_dictionary d ON d.code=sc.code and d.groupcode='SYS0000051'  ORDER BY code ASC";
        return dbService.QueryJsonData(sql);
    }

    public String ExportPersonalConsume(HttpServletRequest request, HttpServletResponse response, String orgcode, String key, String starttime, String endtime,String status, boolean viewchild,String currentPage,String pageSize,String size) throws IOException {
        String schemeSQL = "SELECT code,name FROM tb_consume_scheme ORDER BY code ASC";
        JSONArray schemeja = dbService.QueryList(schemeSQL);
        List<String> schemeCode = new ArrayList<String>();
        List<String> schemeName = new ArrayList<String>();
        List<String> schemetimeCode = new ArrayList<String>();
        Map<String,String> map = new HashMap<>();
        map.put("1","早餐");
        map.put("2","中餐");
        map.put("3","晚餐");
        map.put("5","宵夜");
        map.put("6","全天");
        if (!schemeja.isEmpty()) {
            for (int i = 0; i < schemeja.size(); i++) {
                String code = schemeja.getJSONObject(i).getString("code");
                if (!schemeCode.contains("scheme" + code)){
                    schemeCode.add("scheme" + code);
                    schemetimeCode.add("schemetime"+code);
                    schemeName.add(map.get(code));
                }

            }
        }
        int star = 1;
        JsonData result = new JsonData();
        if (currentPage.equals("1")) {
            result = getPersonalConsumeData(request, response, orgcode, key, starttime, endtime, status, viewchild, 0, Integer.valueOf(size));
        } else if (Integer.valueOf(currentPage) > star) {
            result = getPersonalConsumeData(request, response, orgcode, key, starttime, endtime, status, viewchild, (Integer.valueOf(currentPage)-1)*Integer.valueOf(size), Integer.valueOf(size));
        } else {
            result = getPersonalConsumeData(request, response, orgcode, key, starttime, endtime, status, viewchild, 0, 0);
        }

        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        if ("1".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        } else if ("2".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 10);
        cells.add(cell);

        for (int i = 0; i < schemeCode.size(); i++) {
            cell = new JSONObject();
            cell.put("name", schemeName.get(i));
            cell.put("datakey", schemeCode.get(i));
            cell.put("size", 10);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", schemeName.get(i)+"次数");
            cell.put("datakey", schemetimeCode.get(i));
            cell.put("size", 10);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "合计金额");
        cell.put("datakey", "schememoney");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "合计次数");
        cell.put("datakey", "schemetimes");
        cell.put("size", 15);
        cells.add(cell);

        List<CellRangeAddress> slist = new ArrayList<CellRangeAddress>();
        CellRangeAddress cellRangeAddress = new CellRangeAddress(list.size(), list.size(), 0, 1);
        slist.add(cellRangeAddress);
        List<JSONArray> heads = new ArrayList<JSONArray>();
        String title= "个人消费汇总";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";
        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp", filename, title, slist, heads,starttime,endtime,"1","1");
        return filepath;

    }
}
