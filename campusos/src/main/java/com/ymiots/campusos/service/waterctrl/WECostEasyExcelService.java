package com.ymiots.campusos.service.waterctrl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import net.sf.json.JSONArray;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 水电费账单导出服务（EasyExcel版本）
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class WECostEasyExcelService extends BaseService {

    /**
     * 导出Excel格式账单（使用EasyExcel）
     */
    public void exportToExcel(String startDate, String endDate, String areaCode, HttpServletResponse response) throws Exception {
        Log.info(WECostEasyExcelService.class, 
            String.format("开始导出Excel账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

        // 1. 查询费用记录数据
        JSONArray costRecords = getCostRecords(startDate, endDate, areaCode);
        
        if (costRecords.size() == 0) {
            throw new RuntimeException("没有找到符合条件的费用记录");
        }

        // 2. 转换数据为Excel实体
        List<WECostExcelData> excelDataList = convertToExcelData(costRecords);
        
        // 3. 设置响应头
        String fileName = String.format("水电费账单_%s至%s.xlsx", startDate, endDate);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", 
            "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        
        // 4. 使用EasyExcel写入数据
        EasyExcel.write(response.getOutputStream(), WECostExcelData.class)
                .sheet("水电费账单")
                .doWrite(excelDataList);
        
        Log.info(WECostEasyExcelService.class, 
            String.format("Excel账单导出成功 - 文件: %s, 记录数: %d", fileName, costRecords.size()));
    }

    /**
     * 导出分时电表详细账单
     */
    public void exportTimeSharingExcel(String startDate, String endDate, String areaCode, HttpServletResponse response) throws Exception {
        Log.info(WECostEasyExcelService.class, 
            String.format("开始导出分时电表账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

        // 1. 查询分时电表数据
        JSONArray costRecords = getTimeSharingRecords(startDate, endDate, areaCode);
        
        if (costRecords.size() == 0) {
            throw new RuntimeException("没有找到符合条件的分时电表记录");
        }

        // 2. 转换数据为分时电表Excel实体
        List<TimeSharingExcelData> excelDataList = convertToTimeSharingExcelData(costRecords);
        
        // 3. 设置响应头
        String fileName = String.format("分时电表账单_%s至%s.xlsx", startDate, endDate);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", 
            "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        
        // 4. 使用EasyExcel写入数据
        EasyExcel.write(response.getOutputStream(), TimeSharingExcelData.class)
                .sheet("分时电表账单")
                .doWrite(excelDataList);
        
        Log.info(WECostEasyExcelService.class, 
            String.format("分时电表账单导出成功 - 文件: %s, 记录数: %d", fileName, costRecords.size()));
    }

    /**
     * 生成打印内容
     */
    public String generatePrintContent(String startDate, String endDate, String areaCode) {
        try {
            Log.info(WECostEasyExcelService.class, 
                String.format("生成打印内容 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            // 1. 查询费用记录数据
            JSONArray costRecords = getCostRecords(startDate, endDate, areaCode);
            
            // 2. 生成HTML内容
            return generateBillHTML(costRecords, startDate, endDate, areaCode);
            
        } catch (Exception e) {
            Log.error(WECostEasyExcelService.class, 
                String.format("生成打印内容失败: %s", e.getMessage()), e);
            throw new RuntimeException("生成打印内容失败", e);
        }
    }

    /**
     * 查询费用记录
     */
    public JSONArray getCostRecords(String startDate, String endDate, String areaCode) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT r.equipment_code, r.equipment_type, r.area_code, r.reading_date, ");
            sql.append("r.current_reading, r.previous_reading, r.usage_amount, r.unit_price, ");
            sql.append("r.tip_reading, r.previous_tip_reading, r.tip_usage, r.tip_price, r.tip_cost, ");
            sql.append("r.peak_reading, r.previous_peak_reading, r.peak_usage, r.peak_price, r.peak_cost, ");
            sql.append("r.flat_reading, r.previous_flat_reading, r.flat_usage, r.flat_price, r.flat_cost, ");
            sql.append("r.valley_reading, r.previous_valley_reading, r.valley_usage, r.valley_price, r.valley_cost, ");
            sql.append("r.total_cost, r.is_time_sharing, r.current_balance ");
            sql.append("FROM tb_equipment_reading_records r ");
            sql.append("WHERE r.status = 1 ");
            sql.append("AND DATE(r.reading_date) >= '").append(startDate).append("' ");
            sql.append("AND DATE(r.reading_date) <= '").append(endDate).append("' ");
            
            if (areaCode != null && !areaCode.trim().isEmpty()) {
                sql.append("AND r.area_code = '").append(areaCode).append("' ");
            }
            
            sql.append("ORDER BY r.area_code, r.equipment_code, r.reading_date");
            
            Log.info(WECostEasyExcelService.class, "执行查询SQL: " + sql.toString());
            
            return dbService.QueryList(sql.toString());
            
        } catch (Exception e) {
            Log.error(WECostEasyExcelService.class, 
                String.format("查询费用记录失败: %s", e.getMessage()), e);
            return new JSONArray();
        }
    }

    /**
     * 查询分时电表记录
     */
    private JSONArray getTimeSharingRecords(String startDate, String endDate, String areaCode) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT r.equipment_code, r.equipment_type, r.area_code, r.reading_date, ");
            sql.append("r.tip_reading, r.previous_tip_reading, r.tip_usage, r.tip_price, r.tip_cost, ");
            sql.append("r.peak_reading, r.previous_peak_reading, r.peak_usage, r.peak_price, r.peak_cost, ");
            sql.append("r.flat_reading, r.previous_flat_reading, r.flat_usage, r.flat_price, r.flat_cost, ");
            sql.append("r.valley_reading, r.previous_valley_reading, r.valley_usage, r.valley_price, r.valley_cost, ");
            sql.append("r.total_cost, r.current_balance ");
            sql.append("FROM tb_equipment_reading_records r ");
            sql.append("WHERE r.status = 1 AND r.equipment_type = '1' AND r.is_time_sharing = 1 ");
            sql.append("AND DATE(r.reading_date) >= '").append(startDate).append("' ");
            sql.append("AND DATE(r.reading_date) <= '").append(endDate).append("' ");
            
            if (areaCode != null && !areaCode.trim().isEmpty()) {
                sql.append("AND r.area_code = '").append(areaCode).append("' ");
            }
            
            sql.append("ORDER BY r.area_code, r.equipment_code, r.reading_date");
            
            return dbService.QueryList(sql.toString());
            
        } catch (Exception e) {
            Log.error(WECostEasyExcelService.class, 
                String.format("查询分时电表记录失败: %s", e.getMessage()), e);
            return new JSONArray();
        }
    }

    /**
     * 转换数据为Excel实体
     */
    private List<WECostExcelData> convertToExcelData(JSONArray costRecords) {
        List<WECostExcelData> excelDataList = new ArrayList<>();
        
        for (int i = 0; i < costRecords.size(); i++) {
            net.sf.json.JSONObject record = costRecords.getJSONObject(i);
            
            WECostExcelData excelData = new WECostExcelData();
            excelData.setEquipmentCode(getString(record, "equipment_code"));
            excelData.setEquipmentType(getEquipmentTypeName(getString(record, "equipment_type")));
            excelData.setAreaCode(getString(record, "area_code"));
            excelData.setReadingDate(getString(record, "reading_date"));
            excelData.setCurrentReading(getDouble(record, "current_reading"));
            excelData.setPreviousReading(getDouble(record, "previous_reading"));
            excelData.setUsageAmount(getDouble(record, "usage_amount"));
            excelData.setUnitPrice(getDouble(record, "unit_price"));
            excelData.setTotalCost(getDouble(record, "total_cost"));
            excelData.setCurrentBalance(getDouble(record, "current_balance"));
            
            // 分时电表信息
            if ("1".equals(getString(record, "equipment_type")) && getInt(record, "is_time_sharing") == 1) {
                excelData.setTimeSharingInfo(String.format(
                    "尖:%.2f峰:%.2f平:%.2f谷:%.2f", 
                    getDouble(record, "tip_usage"),
                    getDouble(record, "peak_usage"),
                    getDouble(record, "flat_usage"),
                    getDouble(record, "valley_usage")
                ));
            } else {
                excelData.setTimeSharingInfo("非分时");
            }
            
            excelDataList.add(excelData);
        }
        
        return excelDataList;
    }

    /**
     * 转换数据为分时电表Excel实体
     */
    private List<TimeSharingExcelData> convertToTimeSharingExcelData(JSONArray costRecords) {
        List<TimeSharingExcelData> excelDataList = new ArrayList<>();
        
        for (int i = 0; i < costRecords.size(); i++) {
            net.sf.json.JSONObject record = costRecords.getJSONObject(i);
            
            TimeSharingExcelData excelData = new TimeSharingExcelData();
            excelData.setEquipmentCode(getString(record, "equipment_code"));
            excelData.setAreaCode(getString(record, "area_code"));
            excelData.setReadingDate(getString(record, "reading_date"));
            
            // 尖时
            excelData.setTipReading(getDouble(record, "tip_reading"));
            excelData.setPreviousTipReading(getDouble(record, "previous_tip_reading"));
            excelData.setTipUsage(getDouble(record, "tip_usage"));
            excelData.setTipPrice(getDouble(record, "tip_price"));
            excelData.setTipCost(getDouble(record, "tip_cost"));
            
            // 峰时
            excelData.setPeakReading(getDouble(record, "peak_reading"));
            excelData.setPreviousPeakReading(getDouble(record, "previous_peak_reading"));
            excelData.setPeakUsage(getDouble(record, "peak_usage"));
            excelData.setPeakPrice(getDouble(record, "peak_price"));
            excelData.setPeakCost(getDouble(record, "peak_cost"));
            
            // 平时
            excelData.setFlatReading(getDouble(record, "flat_reading"));
            excelData.setPreviousFlatReading(getDouble(record, "previous_flat_reading"));
            excelData.setFlatUsage(getDouble(record, "flat_usage"));
            excelData.setFlatPrice(getDouble(record, "flat_price"));
            excelData.setFlatCost(getDouble(record, "flat_cost"));
            
            // 谷时
            excelData.setValleyReading(getDouble(record, "valley_reading"));
            excelData.setPreviousValleyReading(getDouble(record, "previous_valley_reading"));
            excelData.setValleyUsage(getDouble(record, "valley_usage"));
            excelData.setValleyPrice(getDouble(record, "valley_price"));
            excelData.setValleyCost(getDouble(record, "valley_cost"));
            
            // 总计
            excelData.setTotalCost(getDouble(record, "total_cost"));
            excelData.setCurrentBalance(getDouble(record, "current_balance"));
            
            excelDataList.add(excelData);
        }
        
        return excelDataList;
    }

    /**
     * 生成账单HTML
     */
    private String generateBillHTML(JSONArray costRecords, String startDate, String endDate, String areaCode) {
        StringBuilder html = new StringBuilder();
        
        html.append("<!DOCTYPE html>");
        html.append("<html><head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>水电费账单</title>");
        html.append("<style>");
        html.append("body { font-family: Arial, sans-serif; margin: 20px; }");
        html.append(".header { text-align: center; margin-bottom: 30px; }");
        html.append(".title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }");
        html.append(".info { margin-bottom: 20px; }");
        html.append(".info-item { margin: 5px 0; }");
        html.append("table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
        html.append("th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }");
        html.append("th { background-color: #f2f2f2; font-weight: bold; }");
        html.append(".money { text-align: right; }");
        html.append("@media print { body { margin: 0; } }");
        html.append("</style>");
        html.append("</head><body>");
        
        // 标题和基本信息
        html.append("<div class='header'>");
        html.append("<div class='title'>水电费账单</div>");
        html.append("</div>");
        
        html.append("<div class='info'>");
        html.append("<div class='info-item'><strong>账单期间：</strong>")
            .append(startDate).append(" 至 ").append(endDate).append("</div>");
        html.append("<div class='info-item'><strong>生成时间：</strong>")
            .append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("</div>");
        
        if (areaCode != null && !areaCode.trim().isEmpty()) {
            html.append("<div class='info-item'><strong>区域编码：</strong>")
                .append(areaCode).append("</div>");
        }
        html.append("</div>");
        
        // 费用明细表
        html.append("<table>");
        html.append("<thead><tr>");
        html.append("<th>设备编号</th><th>设备类型</th><th>区域编码</th><th>读数日期</th>");
        html.append("<th>当前读数</th><th>上次读数</th><th>用量</th><th>单价</th><th>费用</th><th>余额</th>");
        html.append("</tr></thead>");
        html.append("<tbody>");
        
        BigDecimal totalCost = BigDecimal.ZERO;
        
        for (int i = 0; i < costRecords.size(); i++) {
            net.sf.json.JSONObject record = costRecords.getJSONObject(i);
            html.append("<tr>");
            html.append("<td>").append(getString(record, "equipment_code")).append("</td>");
            html.append("<td>").append(getEquipmentTypeName(getString(record, "equipment_type"))).append("</td>");
            html.append("<td>").append(getString(record, "area_code")).append("</td>");
            html.append("<td>").append(getString(record, "reading_date")).append("</td>");
            html.append("<td>").append(getDouble(record, "current_reading")).append("</td>");
            html.append("<td>").append(getDouble(record, "previous_reading")).append("</td>");
            html.append("<td>").append(getDouble(record, "usage_amount")).append("</td>");
            html.append("<td class='money'>￥").append(getDouble(record, "unit_price")).append("</td>");
            html.append("<td class='money'>￥").append(getDouble(record, "total_cost")).append("</td>");
            html.append("<td class='money'>￥").append(getDouble(record, "current_balance")).append("</td>");
            html.append("</tr>");
            
            totalCost = totalCost.add(getBigDecimal(record, "total_cost"));
        }
        
        html.append("</tbody></table>");
        
        // 费用汇总
        html.append("<div style='margin-top: 30px;'>");
        html.append("<table style='width: 300px;'>");
        html.append("<tr style='font-weight: bold; background-color: #f2f2f2;'>");
        html.append("<td><strong>总计费用：</strong></td>");
        html.append("<td class='money'>￥").append(totalCost.toPlainString()).append("</td>");
        html.append("</tr>");
        html.append("</table>");
        html.append("</div>");
        
        html.append("</body></html>");
        
        return html.toString();
    }

    /**
     * 获取设备类型名称
     */
    private String getEquipmentTypeName(String equipmentType) {
        switch (equipmentType) {
            case "1": return "电表";
            case "2": return "水表";
            case "4": return "热水表";
            case "221": return "气表";
            default: return "未知";
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getString(net.sf.json.JSONObject obj, String key) {
        try {
            Object value = obj.get(key);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 安全获取double值
     */
    private double getDouble(net.sf.json.JSONObject obj, String key) {
        try {
            Object value = obj.get(key);
            if (value == null) return 0.0;
            return Double.parseDouble(value.toString());
        } catch (Exception e) {
            return 0.0;
        }
    }

    /**
     * 安全获取int值
     */
    private int getInt(net.sf.json.JSONObject obj, String key) {
        try {
            Object value = obj.get(key);
            if (value == null) return 0;
            return Integer.parseInt(value.toString());
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimal(net.sf.json.JSONObject obj, String key) {
        try {
            Object value = obj.get(key);
            if (value == null) return BigDecimal.ZERO;
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }
}
