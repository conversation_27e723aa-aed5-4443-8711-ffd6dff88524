package com.ymiots.campusos.service.card;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dao.BedDao;
import com.ymiots.campusos.dao.CardDao;
import com.ymiots.campusos.dao.FaceRecordDao;
import com.ymiots.campusos.dto.card.StudentRecordExport;
import com.ymiots.campusos.dto.card.TeachStudInfoDTO;
import com.ymiots.campusos.entity.CardInfo;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.entity.card.StudentFinanceRecord;
import com.ymiots.campusos.entity.card.TeachStudInfo;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysConfigService;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.campusos.service.alleyway.AlleywayOpnService;
import com.ymiots.campusos.service.face.IntoFaceService;
import com.ymiots.campusos.service.face.PermissionGroupService;
import com.ymiots.campusos.service.hotel.CheckOutService;
import com.ymiots.campusos.util.excel.Cell;
import com.ymiots.campusos.util.excel.CellsValidator;
import com.ymiots.campusos.util.excel.Row;
import com.ymiots.framework.common.*;
import com.ymiots.framework.config.SystemEnv;
import com.ymiots.framework.datafactory.CallbackTransaction;
import com.ymiots.framework.websocket.WebSocketSets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Null;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Repository
public class TeachStudInfoService extends BaseService {

    @Autowired
    SysLogsService syslogs;

    @Autowired
    UserRedis userredis;

    @Autowired
    CardManageService cardmanage;

    @Autowired
    SysConfigService sysconfig;

    @Autowired
    CardDao cardDao;

    @Autowired
    BedDao bedDao;

    @Autowired
    CheckOutService checkOutService;

    @Autowired
    IntoFaceService intoFaceService;

    @Autowired
    FaceRecordDao faceRecordDao;

    @Autowired
    AlleywayOpnService alleywayOpnService;

    @Autowired
    ChangeOrgRecordService changeOrgRecordService;

    @Autowired
    PlatformTransactionManager transactionManager;

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Autowired
    PermissionGroupService permissionGroupService;

    public JsonData getTeacherList(HttpServletRequest request, HttpServletResponse response, String orgcode, String key, boolean viewchild, String viewleave, String infolabel, String property, int start, int limit) {
        String fields = "ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate," +
                "date_format(ct.enddate,'%Y-%m-%d') as enddate,ct.cardsn,ct.position,ct.posrank,d.name as positionname," +
                "ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype," +
                "ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.email,ct.focus," +
                "ct.plateno,ct.yunfei,ct.volume,ct.status,ct.infoindex,ct.passpassword,ct.infolabel,d1.name as propertyname,ct.infolabel,ct.property";
        String table = "tb_card_teachstudinfo ct " +
                "LEFT JOIN tb_sys_dictionary d on d.code=ct.position and d.groupcode='SYS0000045' " +
                "LEFT JOIN tb_sys_dictionary d1 on d1.code=ct.property and d1.groupcode='SYS0000055' ";
        String where = "ct.infotype=1";
        if (StringUtils.isNotBlank(viewleave) && !viewleave.equals("-1")) {
            where += " and ct.status=" + viewleave + " ";
        }
        if (StringUtils.isNotBlank(property) && !"-1".equals(property)) {
            where += " and ct.property=" + property + " ";
        }
        if (StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)) {
            where += " and ct.infolabel = '" + infolabel + "' ";
        }
        if ("x".equals(orgcode)){
            orgcode =null;
        }
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and ct.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and ct.orgcode='" + orgcode + "' ";
            }
        }
        if (!StringUtils.isBlank(key)) {
            String cardno = ComHelper.LeftPad(key, 12, '0');
            where += " and (ct.code='" + key + "' or ct.name like '%" + key + "%' or ct.mobile like '%" + key + "%' or  ct.card='" + cardno + "' or ct.cardsn='" + cardno + "' )";
        }
        //if (!StringUtils.isBlank(name)) {
        //    where += " and ct.name like '%" + name + "%'";
        //}
        //if (!StringUtils.isBlank(code)) {
        //    where += " and ct.code = '" + code + "'";
        //}
        //if (!StringUtils.isBlank(card)) {
        //    where += " and ct.card = '" + card + "'";
        //}
        String orderby = ExtSort.Orderby(request, "ct.createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData getStudentList(HttpServletRequest request, HttpServletResponse response, String orgcode, String key,
                                   boolean viewchild, String viewleave, String infolabel, String property, int start, int limit) {
        String fields = "ct.uid,ct.code,ct.name,ct.sex,ct.card,date_format(ct.startdate,'%Y-%m-%d') as startdate,date_format(ct.enddate,'%Y-%m-%d') as enddate," +
                "ct.cardsn,ct.mobile,ct.orgcode,getorgname(ct.orgcode) as orgname,ct.nation,ct.birthday,ct.address,ct.idcard,ct.infotype,ct.intoyear,ct.linkman1," +
                "ct.linkmobile1,ct.linkdes1,ct.linkman2,ct.linkmobile2,ct.linkdes2,ct.focus,ct.status,ct.infolabel,ct.property,d.name AS propertyname";
        String table = "tb_card_teachstudinfo ct join tb_card_orgframework co on co.code = ct.orgcode and co.status = 1 LEFT JOIN tb_sys_dictionary d on d.code=ct.property and d.groupcode='SYS0000055'  ";
        String where = "ct.infotype=2";

        if (StringUtils.isNotBlank(viewleave) && !viewleave.equals("-1")) {
            where += " and ct.status=" + viewleave + " ";
        }
        if("x".equals(orgcode)){
            orgcode = null;
        }

        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and ct.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and ct.orgcode='" + orgcode + "' ";
            }
        }
        if (StringUtils.isNotBlank(property) && !"-1".equals(property)) {
            where += " and ct.property=" + property + " ";
        }
        if (StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)) {
            where += " and ct.infolabel = '" + infolabel + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            String cardno = ComHelper.LeftPad(key, 12, '0');
            where += " and (ct.code='" + key + "' or ct.name like '%" + key + "%' or ct.mobile like '%" + key + "%' or  ct.card='" + cardno + "' or ct.cardsn='" + cardno + "' )";
        }
        String orderby = ExtSort.Orderby(request, "ct.createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData getParentsList(HttpServletRequest request, HttpServletResponse response, String orgcode, String key,
                                   boolean viewchild, String viewleave, String infolabel, String property, int start, int limit) {
        String fields = "s.code,s.name,s.sex,getorgname(s.orgcode) as orgname,a.name as pname,a.card, a.mobile,a.linkdes1,a.uid as pid";
        String table = "tb_card_teachstudinfo a left join tb_card_parentinfo p on a.uid = p.parent_id left join tb_card_teachstudinfo s on p.student_id = s.uid and s.infotype=2";
        String where = "a.infotype=3";

        if (StringUtils.isNotBlank(viewleave) && !viewleave.equals("-1")) {
            where += " and a.status=" + viewleave + " ";
        }
        if ("x".equals(orgcode)){
            orgcode=null;
        }
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and a.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and a.orgcode='" + orgcode + "' ";
            }
        }
        if (StringUtils.isNotBlank(property) && !"-1".equals(property)) {
            where += " and a.property=" + property + " ";
        }
        if (StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)) {
            where += " and a.infolabel = '" + infolabel + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (a.code like '%" + key + "%' or a.name like '%" + key + "%' )";
        }
        String orderby = ExtSort.Orderby(request, "a.createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }


    public JSONArray getInfoLabel(HttpServletRequest request, HttpServletResponse response, String infotype) {
        String sql = "SELECT label as name FROM tb_card_infolabel WHERE infotype=?";
        JSONArray ja = dbService.QueryList(sql, infotype);
        JSONObject jo = new JSONObject();
        jo.put("name", "全部");
        ja.add(0, jo);
        return ja;
    }

    public JsonResult SaveTeacher(HttpServletRequest request, HttpServletResponse response, String uid, String code, String name, String sex, String card, String position, String posrank,
                                  String mobile, String orgcode, String nation, String birthday, String address, String idcard, String email, String linkman1, String linkmobile1,
                                  String linkdes1, String linkman2, String linkmobile2, String linkdes2, String startdate, String enddate, String plateno, String yunfei,
                                  String volume, String passpassword, String infolabel, String property,String groupUid,String userId) {
        if (StringUtils.isNotBlank(groupUid)) {
            if (StringUtils.isEmpty(userId)){
                userId = uid;
            }
            //int count = dbService.getCount("tb_face_infoface", "infoid ='" + userId + "'");
            if (StringUtils.isEmpty(card)) {
                return Json.getJsonResult("选择权限组，必需填写卡号");
            }
        }
        SysUser sysuser = userredis.get(request);
        String creatorid = sysuser.getUid();
        String creator = sysuser.getName();
        if (StringUtils.isBlank(yunfei)) {
            yunfei = "0.00";
        }
        if (StringUtils.isBlank(volume)) {
            volume = "0.00";
        }
        JSONObject data = new JSONObject();

        if (StringUtils.isBlank(property)) {
            property = "1";
        }

        if (StringUtils.isBlank(uid)) {
            if (StringUtils.isNotBlank(card)) {
                card = ComHelper.LeftPad(card, 12, '0');
                int cardcount = dbService.getCount("tb_card_cardinfo", "cardno='" + card + "' AND status=1");
                if (cardcount > 0) {
                    return Json.getJsonResult(false, "卡号已存在！");
                }
            }
            int count = dbService.getCount("tb_card_teachstudinfo", "code='" + code + "' ");
            if (count > 0) {
                return Json.getJsonResult("工号已存在");
            }
            if (!StringUtils.isBlank(idcard)) {
                count = dbService.getCount("tb_card_teachstudinfo", "idcard='" + idcard + "' ");
                if (count > 0) {
                    return Json.getJsonResult("身份证号码已登记");
                }
            }
            Calendar c = Calendar.getInstance();
            int intoyear = c.get(Calendar.YEAR);
//            String teachid = UUID.randomUUID().toString();
            StringBuffer sql = new StringBuffer();
            sql.append("insert into tb_card_teachstudinfo(uid,code,name,sex,mobile,orgcode,nation,birthday,address,card,cardsn,position,posrank,startdate,enddate,email,idcard,infotype,intoyear,linkman1,linkmobile1,linkdes1,linkman2,linkmobile2,linkdes2,focus,plateno,yunfei,volume,passpassword,createdate,creatorid,modifydate,modifierid,status,endsisdaily,infolabel,property) ");
            sql.append("values(?,?,?,?,?,?,?,?,?,?,?,?,?,");
            if (StringUtils.isBlank(startdate)) {
                sql.append("null,");
            } else {
                sql.append("'" + startdate + "',");
            }
            if (StringUtils.isBlank(enddate)) {
                sql.append("null,");
            } else {
                sql.append("'" + enddate + "',");
            }
            sql.append("?,?,1,?,?,?,?,?,?,?,0,?,?,?,?,now(),?,now(),?,1,99999999,?,?)");
            String autoCard = sysconfig.get("autoCard");
            if (StringUtils.isBlank(card)&&StringUtils.isNotEmpty(autoCard)&&autoCard.equals("1")){
                boolean flag = true;
                while (flag){
                    card = generateRandomNumberString(8);
                    card = ComHelper.LeftPad(card, 12, '0');
                    flag = isCard(card);
                }
            }
            dbService.excuteSql(sql.toString(), userId, code, name, sex, mobile, orgcode, nation, birthday, address, card, card, position, posrank, email, idcard, intoyear, linkman1, linkmobile1, linkdes1, linkman2, linkmobile2, linkdes2, plateno, yunfei, volume, passpassword, creatorid, creatorid, infolabel, property);
            if (StringUtils.isNotBlank(card)) {
                String cardid = UUID.randomUUID().toString();
                //// 获取当前日期
                //LocalDate currentDate = LocalDate.now();
                //
                //// 定义日期格式
                //DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                //
                //// 格式化当前日期
                //String formattedCurrentDate = currentDate.format(formatter);
                //System.out.println("当前日期: " + formattedCurrentDate);
                //
                //// 计算一年后的日期
                //LocalDate oneYearLater = currentDate.plusYears(1);
                //
                //// 格式化一年后的日期
                //String formattedOneYearLater = oneYearLater.format(formatter);
                String str = "insert into tb_card_cardinfo(uid,infoid,usedes,cardno,cardsn,deposit,ismain,balance,vicewallet,waterwallet,timescount,enddate,createdate,creatorid,modifydate,modifierid,status) values " +
                        "('" + cardid + "','" + userId + "','本人', '" + card + "', '" + card + "',0,1,0,0,0,0,'2050-01-01',now(),'" + creatorid + "',now(),'" + creatorid + "',1)";
                dbService.excuteSql(str);
                String str2 = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,des,createdate,creatorid) values" +
                        "(uuid(),'" + userId + "','" + cardid + "','" + card + "','" + card + "',0,1,'本人','" + creator + "','0','人员档案创建发卡',now(),'" + creatorid + "')";
                dbService.excuteSql(str2);
                String dopsql = "update tb_card_cardinfo_oprecord op inner join tb_card_teachstudinfo info on info.uid=op.infoid set op.infocode=info.code,op.infoname=info.name,op.orgname=getorgname(info.orgcode) where op.infocode='0' ";
                dbService.excuteSql(dopsql);
            }
            data.put("extenduid", userId);

            if (StringUtils.isNotBlank(groupUid)) {
                for (String groupId : groupUid.split(",")) {
                    permissionGroupService.addPermissionGroup(request, response, userId, groupId);
                }
                syslogs.Write(request, "职工信息", String.format("新增权限组信息（人员%s，权限组id%s）", name, groupUid));
            }
            syslogs.Write(request, "职工信息", String.format("新增职工信息（%s，%s）", name, code));

        } else {
            int count = dbService.getCount("tb_card_teachstudinfo", " uid<>'" + uid + "' and code='" + code + "' ");
            if (count > 0) {
                return Json.getJsonResult("工号已存在");
            }
            if (!StringUtils.isBlank(idcard)) {
                count = dbService.getCount("tb_card_teachstudinfo", "uid<>'" + uid + "' and idcard='" + idcard + "' ");
                if (count > 0) {
                    return Json.getJsonResult("身份证号码已登记");
                }
            }
            String userid = userredis.getUserId(request);
            String sql = "update tb_card_teachstudinfo set code=?,name=?,sex=?,mobile=?,nation=?,birthday=?,address=?,position=?,posrank=?,passpassword=?,email=?,";
            if (StringUtils.isBlank(startdate)) {
                sql += "startdate=null,";
            } else {
                sql += "startdate='" + startdate + "',";
            }
            if (StringUtils.isBlank(enddate)) {
                sql += "enddate=null,";
            } else {
                sql += "enddate='" + enddate + "',";
            }

            sql += "idcard=?,linkman1=?,linkmobile1=?,linkdes1=?,linkman2=?,linkmobile2=?,linkdes2=?,plateno=?,yunfei=?,volume=?,modifydate=now(),modifierid=?,infolabel=? ,property=? ";
            sql += " where uid=? ";

            JSONArray oldinfolist = dbService.QueryList("SELECT code,name FROM tb_card_teachstudinfo WHERE uid='" + uid + "'");

            int isOk = dbService.excuteSql(sql, code, name, sex, mobile, nation, birthday, address, position, posrank, passpassword, email, idcard, linkman1, linkmobile1, linkdes1, linkman2, linkmobile2, linkdes2, plateno, yunfei, volume, userid, infolabel, property, uid);
            if (isOk < 0) {
                return Json.getJsonResult(false,"修改失败,系统繁忙请稍后重试！");
            }
            if (StringUtils.isNotBlank(passpassword)) {
                String updateSLQ = "UPDATE tb_dev_admin SET status=5,downnum=0,downmsg='' WHERE infoid=? AND status IN (1,3,4,5,6)";
                dbService.excuteSql(updateSLQ, uid);
                String updateSLQ2 = "UPDATE tb_dev_admin SET status=0,downnum=0,downmsg='' WHERE infoid=? AND status IN (0,2)";
                dbService.excuteSql(updateSLQ2, uid);
            }

            if (oldinfolist.size() > 0) {
                JSONObject item = oldinfolist.getJSONObject(0);
                String infocode = item.getString("code");
                String infoname = item.getString("name");
                if (!infoname.equals(name) || !infocode.equals(code)) {
                    dbService.excuteSql("call sp_info_updatename(?,?,?)", code, name, uid);
                }
            }
            data.put("extenduid", uid);
            syslogs.Write(request, "职工信息", String.format("更新职工信息（%s，%s），代码为%s", name, code, uid));
            int faceCount = dbService.getCount("tb_face_infoface", "infoid ='" + uid + "'");
            if (faceCount > 0) {
                //当前人员授权的权限组
                String selPermissionSql = "select distinct groupid from tb_face_authorize_group_access where infoid = '" + uid + "'";
                List<String> infoPermissionList = dbService.queryFields(selPermissionSql, String.class);

                StringJoiner joiner = new StringJoiner("','","('","')");
                for (String groupId : groupUid.split(",")) {
                    joiner.add(groupId);
                    //判断当前人员是否已经授权，如果已经授权跳过，未授权添加，授权列表没有该权限组就需要删除。
                    if (!infoPermissionList.contains(groupId)) {
                        permissionGroupService.addPermissionGroup(request, response, uid, groupId);
                        syslogs.Write(request, "职工信息", String.format("修改权限组新增信息（人员%s，权限组id%s）", name, groupId));
                    }
                }
                //先查询所有的权限组
                String selGroupIdSql = "select uid from tb_face_authorize_group where uid not in " + joiner;
                List<String> groupIdList = dbService.queryFields(selGroupIdSql, String.class);
                //如果存在就设置为待删除
                List<String> delGroupList = groupIdList.stream()
                        .filter(infoPermissionList::contains)
                        .collect(Collectors.toList());
                for (String s : delGroupList) {
                    try {
                        permissionGroupService.deletePermissionGroup(request,uid,s,name);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                syslogs.Write(request, "职工信息", String.format("修改权限组删除信息（人员%s，权限组id%s）", name, delGroupList));
            }
        }

        if (StringUtils.isNotBlank(infolabel)) {
            String sqlLabel = "SELECT label FROM tb_card_infolabel WHERE infotype=1";
            JSONArray labelja = dbService.QueryList(sqlLabel);
            List<String> labelList = new ArrayList<String>();
            if (!labelja.isEmpty()) {
                for (int i = 0; i < labelja.size(); i++) {
                    labelList.add(labelja.getJSONObject(i).getString("label"));
                }
            }
            List<String> infoLabelList = new ArrayList<String>(Arrays.asList(infolabel.split(",")));

            infoLabelList.removeAll(labelList);
            StringBuilder insertLabelSQL = new StringBuilder("INSERT INTO tb_card_infolabel(uid,label,infotype) VALUES");
            if (!infoLabelList.isEmpty()) {
                for (String oneInfolabel : infoLabelList) {
                    if (!labelList.contains(oneInfolabel)) {
                        insertLabelSQL.append("(uuid(),'").append(oneInfolabel).append("',1),");
                    }
                }
                insertLabelSQL.deleteCharAt(insertLabelSQL.length() - 1);
                dbService.excuteSql(insertLabelSQL.toString());
            }
        }
        return Json.getJsonResult(true, data);
    }

    public JsonResult SaveStudent(HttpServletRequest request, HttpServletResponse response, String uid, String code, String name, String sex, String card,
                                  String mobile, String orgcode, String nation, String birthday, String address, String idcard, String intoyear, String linkman1, String linkmobile1, String linkdes1, String linkman2, String linkmobile2, String linkdes2, String startdate, String enddate, String infolabel, String property, String groupUid,String userId) {

        if (StringUtils.isNotBlank(groupUid)) {
            if (StringUtils.isEmpty(userId)){
                userId = uid;
            }
             //int count = dbService.getCount("tb_face_infoface", "infoid ='" + userId + "'");
             if (StringUtils.isEmpty(card)) {
                 return Json.getJsonResult("选择权限组，必需填写卡号");
             }
        }
        SysUser sysuser = userredis.get(request);
        String creatorid = sysuser.getUid();
        String creator = sysuser.getName();

        JSONObject data = new JSONObject();
        if (StringUtils.isBlank(property)) {
            property = "2";
        }
        if (StringUtils.isBlank(uid)) {
            if (StringUtils.isNotBlank(card)) {
                card = ComHelper.LeftPad(card, 12, '0');
                int cardcount = dbService.getCount("tb_card_cardinfo", "cardno='" + card + "' AND status=1");
                if (cardcount > 0) {
                    return Json.getJsonResult(false, "卡号已存在！");
                }
            }
            int count = dbService.getCount("tb_card_teachstudinfo", "code='" + code + "' ");
            if (count > 0) {
                return Json.getJsonResult("工号已存在");
            }
            if (!StringUtils.isBlank(idcard)) {
                count = dbService.getCount("tb_card_teachstudinfo", "idcard='" + idcard + "' ");
                if (count > 0) {
                    return Json.getJsonResult("身份证号码已登记");
                }
            }
//            String studentid = UUID.randomUUID().toString();
            StringBuffer sql = new StringBuffer();
            sql.append("insert into tb_card_teachstudinfo(uid,code,name,sex,card,cardsn,mobile,nation,birthday,address,idcard,orgcode,infotype,intoyear,linkman1,linkmobile1,linkdes1,linkman2,linkmobile2,linkdes2,startdate,enddate,focus,createdate,creatorid,modifydate,modifierid,status,endsisdaily,infolabel,property) ");
            sql.append(" values(?,?,?,?,?,?,?,?,?,?,?,?,2,?,?,?,?,?,?,?,");

            if (StringUtils.isBlank(startdate)) {
                sql.append("null,");
            } else {
                sql.append("'" + startdate + "',");
            }
            if (StringUtils.isBlank(enddate)) {
                sql.append("null,");
            } else {
                sql.append("'" + enddate + "',");
            }
            if (StringUtils.isBlank(intoyear)) {
                intoyear = DateHelper.format(new Date(), "yyyy");
            }
            sql.append("0,now(),?,now(),?,1,99999999,?,?)");
            String autoCard = sysconfig.get("autoCard");
            if (StringUtils.isBlank(card) && StringUtils.isNotEmpty(autoCard) && autoCard.equals("1")) {
                boolean flag = true;
                while (flag) {
                    card = generateRandomNumberString(8);
                    card = ComHelper.LeftPad(card, 12, '0');
                    flag = isCard(card);
                }
            }
            dbService.excuteSql(sql.toString(), userId, code, name, sex, card, card, mobile, nation, birthday, address, idcard, orgcode, intoyear, linkman1, linkmobile1, linkdes1, linkman2, linkmobile2, linkdes2, creatorid, creatorid, infolabel, property);
            if (StringUtils.isNotBlank(card)) {
                String cardid = UUID.randomUUID().toString();
                String str = "insert into tb_card_cardinfo(uid,infoid,usedes,cardno,cardsn,deposit,ismain,balance,vicewallet,waterwallet,timescount,enddate,createdate,creatorid,modifydate,modifierid,status) values " +
                        "('" + cardid + "','" + userId + "','本人', '" + card + "', '" + card + "',0,1,0,0,0,0,'2050-01-01',now(),'" + creatorid + "',now(),'" + creatorid + "',1)";
                dbService.excuteSql(str);
                String str2 = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,des,createdate,creatorid) values" +
                        "(uuid(),'" + userId + "','" + cardid + "','" + card + "','" + card + "',0,1,'本人','" + creator + "','0','人员档案创建发卡',now(),'" + creatorid + "')";
                dbService.excuteSql(str2);
                String dopsql = "update tb_card_cardinfo_oprecord op inner join tb_card_teachstudinfo info on info.uid=op.infoid set op.infocode=info.code,op.infoname=info.name,op.orgname=getorgname(info.orgcode) where op.infocode='0' ";
                dbService.excuteSql(dopsql);
            }
            data.put("extenduid", userId);
            if (StringUtils.isNotBlank(groupUid)) {
                for (String groupId : groupUid.split(",")) {
                    permissionGroupService.addPermissionGroup(request, response, userId, groupId);
                }
                syslogs.Write(request, "学生信息", String.format("修改权限组信息（人员%s，权限组id%s）", name, groupUid));
            }
            syslogs.Write(request, "学生信息", String.format("新增学生信息（%s，%s）", name, code));
        } else {
            int count = dbService.getCount("tb_card_teachstudinfo", " uid<>'" + uid + "' and code='" + code + "' ");
            if (count > 0) {
                return Json.getJsonResult("学号已存在");
            }
            if (!StringUtils.isBlank(idcard)) {
                count = dbService.getCount("tb_card_teachstudinfo", "uid<>'" + uid + "' and idcard='" + idcard + "' ");
                if (count > 0) {
                    return Json.getJsonResult("身份证号码已登记");
                }
            }

            String userid = userredis.getUserId(request);
            String sql = "update tb_card_teachstudinfo set code=?,name=?,sex=?,mobile=?,nation=?,birthday=?,address=?,idcard=?,intoyear=?,";
            sql += "linkman1=?,linkmobile1=?,linkdes1=?,linkman2=?,linkmobile2=?,linkdes2=?,";

            if (StringUtils.isBlank(startdate)) {
                sql += "startdate=null,";
            } else {
                sql += "startdate='" + startdate + "',";
            }
            if (StringUtils.isBlank(enddate)) {
                sql += "enddate=null,";
            } else {
                sql += "enddate='" + enddate + "',";
            }
            if (StringUtils.isBlank(intoyear)) {
                intoyear = DateHelper.format(new Date(), "yyyy");
            }
            sql += " modifydate=now(),modifierid=?,infolabel=?,property=? where uid=? ";

            JSONArray oldinfolist = dbService.QueryList("SELECT code,name FROM tb_card_teachstudinfo WHERE uid='" + uid + "'");

            int ok = dbService.excuteSql(sql, code, name, sex, mobile, nation, birthday, address, idcard, intoyear, linkman1, linkmobile1, linkdes1, linkman2, linkmobile2, linkdes2, userid, infolabel, property, uid);
            if (ok < 0) {
                return Json.getJsonResult(false, "修改失败,系统繁忙请稍后重试！");
            }
            if (oldinfolist.size() > 0) {
                JSONObject item = oldinfolist.getJSONObject(0);
                String infocode = item.getString("code");
                String infoname = item.getString("name");
                if (!infoname.equals(name) || !infocode.equals(code)) {
                    dbService.excuteSql("call sp_info_updatename(?,?,?)", code, name, uid);
                }
            }
            data.put("extenduid", uid);
            syslogs.Write(request, "学生信息", String.format("更新学生信息（%s，%s），代码为%s", name, code, uid));
            int faceCount = dbService.getCount("tb_face_infoface", "infoid ='" + uid + "'");
            if (faceCount > 0) {
                String selPermissionSql = "select distinct groupid from tb_face_authorize_group_access where infoid = '" + uid + "'";
                List<String> infoPermissionList = dbService.queryFields(selPermissionSql, String.class);

                StringJoiner joiner = new StringJoiner("','", "('", "')");
                for (String groupId : groupUid.split(",")) {
                    joiner.add(groupId);
                    //判断当前人员是否已经授权，如果已经授权跳过，未授权添加，授权列表没有该权限组就需要删除。
                    if (!infoPermissionList.contains(groupId)) {
                        syslogs.Write(request, "学生信息", String.format("修改权限组新增人员信息（人员%s，权限组id%s）", name, groupId));
                        permissionGroupService.addPermissionGroup(request, response, uid, groupId);
                    }
                }
                //先查询所有的权限组
                String selGroupIdSql = "select uid from tb_face_authorize_group where uid not in " + joiner;
                List<String> groupIdList = dbService.queryFields(selGroupIdSql, String.class);
                //如果存在就设置为待删除
                List<String> delGroupList = groupIdList.stream()
                        .filter(infoPermissionList::contains)
                        .collect(Collectors.toList());
                for (String s : delGroupList) {
                    try {
                        syslogs.Write(request, "学生信息", String.format("修改权限组新增人员信息（人员%s，权限组id%s）", name, s));
                        permissionGroupService.deletePermissionGroup(request, uid, s,name);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        if (StringUtils.isNotBlank(infolabel)) {
            String sqlLabel = "SELECT label FROM tb_card_infolabel WHERE infotype=2";
            JSONArray labelja = dbService.QueryList(sqlLabel);
            List<String> labelList = new ArrayList<String>();
            if (!labelja.isEmpty()) {
                for (int i = 0; i < labelja.size(); i++) {
                    labelList.add(labelja.getJSONObject(i).getString("label"));
                }
            }
            List<String> infoLabelList = new ArrayList<String>(Arrays.asList(infolabel.split(",")));

            infoLabelList.removeAll(labelList);
            StringBuilder insertLabelSQL = new StringBuilder("INSERT INTO tb_card_infolabel(uid,label,infotype) VALUES");
            if (!infoLabelList.isEmpty()) {
                for (String oneInfolabel : infoLabelList) {
                    if (!labelList.contains(oneInfolabel)) {
                        insertLabelSQL.append("(uuid(),'").append(oneInfolabel).append("',2),");
                    }
                }
                insertLabelSQL.deleteCharAt(insertLabelSQL.length() - 1);
                dbService.excuteSql(insertLabelSQL.toString());
            }
        }
        return Json.getJsonResult(true);
    }

    // 生成指定长度的随机数字字符串
    public static String generateRandomNumberString(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("长度必须大于0");
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(ThreadLocalRandom.current().nextInt(10)); // 生成0-9之间的随机数
        }
        return sb.toString();
    }
    public  boolean isCard(String card) {
        int cardcount = dbService.getCount("tb_card_cardinfo", "cardno='" + card + "' AND status=1");
        if (cardcount > 0) {
            return true;
        }
        return false;
    }

    public JsonResult MoveTeachStudInfo(HttpServletRequest request, String orgcode, String infoid, String fromOrgs, String title) {
        String userid = userredis.get(request).getUid();
        String[] infoIdArr = infoid.split(",");
        String[] fromOrgArr = fromOrgs.split(",");

        dbService.excuteSql("update tb_card_teachstudinfo set orgcode=?,modifydate=now(),modifierid='" + userid + "' where uid in('" + String.join("','", infoIdArr) + "')", orgcode);
        syslogs.Write(request, title, String.format("移动%s（%s）所属院系、部门、班级为（%s）", title, infoid, orgcode));
        dbService.excuteSql("update tb_hotel_bed set orgcode=? where infoid in('" + String.join("','", infoIdArr) + "')", orgcode);
        syslogs.Write(request, title, String.format("移动%s（%s）所属院系、部门、班级为（%s），同步住宿信息", title, infoid, orgcode));
        // 生成记录
        for (int i = 0; i < infoIdArr.length; i++) {
            changeOrgRecordService.orgMove(infoIdArr[i], fromOrgArr[i], orgcode);
        }
        return Json.getJsonResult(true);
    }

    public boolean delTeachStudInfo(HttpServletRequest request, HttpServletResponse response, String infoid) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            // 退卡
            String cardId = cardDao.getCardIdByInfoNo(infoid);
            if (StringUtils.isNotBlank(cardId)) {
                cardmanage.SaveReturnCard(request, response, cardId, null, true);
            }
            // 退床
            List<String> bedIds = bedDao.getBedIdsByInfoId(infoid);
            for (String bedId : bedIds) {
                checkOutService.SaveCheckOut(request, response, infoid, bedId);
            }
            // 删除人脸
            intoFaceService.clearFace(request, response, infoid);
            // 删除人脸记录
            faceRecordDao.delRecord(infoid);
            // 删除门禁关联
            alleywayOpnService.delPerson(infoid);
            //备份人员
            dbService.excuteSql("insert into tb_card_teachstudinfo_sleep (uid,code,name,sex,mobile,orgcode,nation,birthday,address,card,cardsn,position,posrank,startdate,enddate,email,idcard,infotype,intoyear,linkman1,linkmobile1,linkdes1,linkman2,linkmobile2,linkdes2,focus,plateno,yunfei,volume,passpassword,createdate,creatorid,modifydate,modifierid,status,endsisdaily,infolabel,property) " +
                    " select uid,code,name,sex,mobile,orgcode,nation,birthday,address,card,cardsn,position,posrank,startdate,enddate,email,idcard,infotype,intoyear,linkman1,linkmobile1,linkdes1,linkman2,linkmobile2,linkdes2,focus,plateno,yunfei,volume,passpassword,createdate,creatorid,modifydate,modifierid,-1,endsisdaily,infolabel,property from tb_card_teachstudinfo where uid = '" + infoid + "'");
            // 删除人员信息
            dbService.excuteSql("delete from tb_card_teachstudinfo where uid=?", infoid);
            // 删除人员关联信息
            dbService.excuteSql("delete from tb_card_teachstudinfo_detail where uid=?", infoid);

            syslogs.Write(request, "职工信息", String.format("删除 职工/学生 信息（%s）", infoid));
            transactionManager.commit(status);
            return true;
        } catch (Exception e) {
            transactionManager.rollback(status);
            syslogs.Write(request, "职工信息", "删除 职工/学生 信息失败 infoId: " + infoid);
            e.printStackTrace();
            return false;
        }
    }

    public R<Null> selTeachStudInfo(HttpServletRequest request, HttpServletResponse response, String infoid) {
        try {
            // 退床
            List<String> bedIds = bedDao.getBedIdsByInfoId(infoid);
            if (bedIds.size() > 0) {
                return R.fail("该人员已分配床位，不能删除！"+infoid);
            }
            // 删除人脸授权
            Integer faceCount = intoFaceService.getFaceCount(infoid);
            Integer authorizeCount = intoFaceService.getFaceAuthorizeCount(infoid);
            if (faceCount > 0 || authorizeCount > 0) {
                return R.fail("该人员已授权人脸，不能删除！" + infoid);
            }
            // 删除门禁关联
            boolean selPerson = alleywayOpnService.selPerson(infoid);
            if (!selPerson) {
                return R.fail("该人员已关联门禁，不能删除！"+infoid);
            }
            String cardId = cardDao.getCardIdByInfoNo(infoid);
            JsonResult jsonResult = Json.getJsonResult(true);
            if (StringUtils.isNotBlank(cardId)) {
                jsonResult = cardmanage.SaveRevokeReturnCard(request, response, cardId, null, true);
            }
            if (!jsonResult.isSuccess()){
                return R.fail(jsonResult.getMsg());
            }
            // 退卡
            Integer cardInfoCount = cardmanage.getCardInfoCount(infoid);
            if (cardInfoCount > 0) {
                return R.fail("该人员已发卡，不能删除！");
            }
            boolean res = delTeachStudInfo(request, response, infoid);
            if (!res) {
                return R.fail("删除失败，请重试！");
            }
            syslogs.Write(request, "职工信息", String.format("删除 职工/学生 信息（%s）", infoid));
            return R.ok("可以删除");
        } catch (Exception e) {
            syslogs.Write(request, "职工信息", "删除 职工/学生 信息失败 infoId: " + infoid);
            e.printStackTrace();
            return R.fail("删除失败，未知错误");
        }
    }

    public JsonResult delBatchTeachStudInfo(HttpServletRequest request, HttpServletResponse response, String infoids) {
        if (StringUtils.isBlank(infoids)) {
            return Json.getJsonResult(false, "没有正确选择人员");
        }
        ExecutorService threadPoolExecutor = Executors.newFixedThreadPool(SystemEnv.IO);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        Map<String, String> failInfoReasons = new ConcurrentHashMap<>();
        List<String> failInfoIds = new CopyOnWriteArrayList<>();
        for (String infoId : infoids.split(",")) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(
                    () -> {
                        R<Null> selStatus = selTeachStudInfo(request, response, infoId);
                        if (!selStatus.isSuccess()) {
                            failInfoIds.add(infoId);
                            // 保存具体的失败原因
                            String selNameSql = "select name from tb_card_teachstudinfo where uid = '" + infoId + "'";
                            String name = dbService.queryOneField(selNameSql, String.class);
                            failInfoReasons.put(name, selStatus.getMsg());
                        }
                    },
                    threadPoolExecutor
            );
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .join();
        int failInfoSize = failInfoIds.size();
        if (failInfoSize == futures.size()) {
            return Json.getJsonResult(false, "所选人员全都删除失败,数据存在引用: " + failInfoReasons);
        }
        if (failInfoSize == 0) {
            Json.getJsonResult(true, "所选人员删除成功");
        }
        return Json.getJsonResult(true, "所选人员部分删除成功,数据存在引用" + failInfoReasons);
    }

   public JsonResult DelParent(HttpServletRequest request, HttpServletResponse response, String infoids) {
        if (StringUtils.isBlank(infoids)) {
            return Json.getJsonResult(false, "没有正确选择人员");
        }
       String[] split = infoids.split(",");
       // 删除人员信息
       for (int i = 0; i < split.length; i++) {
           dbService.excuteSql("delete from tb_card_teachstudinfo where uid=?", split[i]);
           dbService.excuteSql("delete from tb_card_parentinfo where parent_id=?", split[i]);
       }
        return Json.getJsonResult(true, "删除成功");
    }

    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        String fields = "i.uid,i.code,i.name,i.sex,i.infotype,i.intoyear,o.name as orgname";
        String table = "tb_card_teachstudinfo i inner join tb_card_orgframework o on o.code=i.orgcode";
        String where = "i.status=1 and infotype in (1,2)";
        if (!StringUtils.isBlank(key)) {
            String cardno = ComHelper.LeftPad(key, 12, '0');
            where += " and (i.cardsn='" + cardno + "' or i.code='" + key + "' or i.name like '%" + key + "%' or i.mobile='" + key + "' or o.name like '%" + key + "%')";
        }
        String orderby = ExtSort.Orderby(request, "i.createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonResult SetInfoStatus(HttpServletRequest request, HttpServletResponse response, String uids, int status) {
        String[] uidx = uids.split(",");
        SysUser sysuser = userredis.get(request);
        String creatorid = sysuser.getUid();
        String creator = sysuser.getName();
        String createqrcodeway = sysconfig.get("createqrcodeway");
        List<String> infolist = new ArrayList<String>();
        if (status == 0) {
            String starttime = DateHelper.format(new Date(), "yyyy-MM-dd 00:00:00");
            String endtime = DateHelper.format(new Date(), "yyyy-MM-dd 23:59:59");
            for (int i = 0; i < uidx.length; i++) {
                String infoid = uidx[i];
                int count = dbService.getCount("tb_card_transaction_detail td inner join tb_card_cardinfo card on card.uid=td.cardid", " card.infoid='" + infoid + "' and td.tradedate between '" + starttime + "' and '" + endtime + "' AND td.status=1 ");
                if (count == 0) {
                    count = dbService.getCount("tb_consume_payrecords", " infoid='" + infoid + "' and recordtime between '" + starttime + "' and '" + endtime + "' AND status=1");
                    if (count == 0) {
                        infolist.add(infoid);
                    }
                }
            }

            String endsisdaily = DateHelper.format(DateHelper.addDays(new Date(), 7), "yyyyMMdd");

            String handuid = String.join(",", ComHelper.ListToArray(infolist));
            String sql = "update tb_card_teachstudinfo set enddate=date_format(now(),'%Y-%m-%d') ,status=0,endsisdaily=?,modifierid=?,modifydate=now() where uid in('" + String.join("','", ComHelper.ListToArray(infolist)) + "')";
            dbService.excuteSql(sql, endsisdaily, creatorid);
            syslogs.Write(request, "职工信息", String.format("修改职工或学生信息（%s）状态为（%s）", handuid, status));

            StringBuilder queryuids = new StringBuilder("select uid,code from tb_sys_user where find_in_set(empid,'").append(handuid).append("')");
            JSONArray userids = dbService.QueryList(queryuids.toString());
            StringBuilder userid = new StringBuilder();
            StringBuilder codes = new StringBuilder();
            for (int i = 0; i < userids.size(); i++) {
                userid.append(userids.getJSONObject(i).getString("uid") + ",");
                codes.append(userids.getJSONObject(i).getString("code") + ",");
            }
            if (StringUtils.isNotBlank(userid)) {
                userid.deleteCharAt(userid.length() - 1);
                StringBuilder delrole = new StringBuilder("delete from tb_sys_role_user where find_in_set(userid,'").append(userid).append("')");
                dbService.excuteSql(delrole.toString());
                syslogs.Write(request, "系统账号", String.format("删除系统用户（%s）角色", handuid));
            }
            if (StringUtils.isNotBlank(codes)) {
                codes.deleteCharAt(codes.length() - 1);
                StringBuilder deluser = new StringBuilder("delete from tb_sys_user where find_in_set(empid,'").append(handuid).append("')");
                dbService.excuteSql(deluser.toString());
                syslogs.Write(request, "系统账号", String.format("删除系统账号（%s）", codes));
            }
            for (int y = 0; y < uidx.length; y++) {
                String infoid = uidx[y];
                //人员信息设置为无效时，将所有正常的卡片更新为过期
                JSONArray cardlist = dbService.QueryList("SELECT uid FROM tb_card_cardinfo where infoid='" + infoid + "' and (status=1 or status=2) ;");
                for (int i = 0; i < cardlist.size(); i++) {
                    String cardid = cardlist.getJSONObject(i).getString("uid");
                    dbService.excuteSql("update tb_card_cardinfo set enddate=now(),status=0,modifydate=now(),modifierid='" + creatorid + "',des='人员信息设置为无效卡自动作废' where uid='" + cardid + "' ");
                    sql = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,infoname,orgname,des,createdate,creatorid) values(uuid(),?,?,?,?,?,?,?,?,?,?,?,?,now(),?);";
                    CardInfo card = cardmanage.GetCardInfoByNo("", cardid);
                    String cardno = card.getCardno();
                    String cardsn = card.getCardsn();
                    String usedes = card.getUsedes();
                    String infoname = card.getName();
                    String infocode = card.getCode();
                    String orgname = card.getOrgname();
                    int ismain = card.getIsmain();
                    dbService.excuteSql(sql, infoid, cardid, cardno, cardsn, "9", ismain, usedes, creator, infocode, infoname, orgname, "人员信息设置为无效卡自动作废", creatorid);
                }
                //离职人员，同步处理其他表数据
                dbService.excuteSql("call sp_info_leaveoffice('" + infoid + "')");

                if (!createqrcodeway.equals("2")) {
                    //生成二维码方式不是按随机字符生成
                    sql = "delete from tb_card_qrcode where infoid=?";
                    dbService.excuteSql(sql, infoid);
                }
            }

            if (infolist.size() < uidx.length) {
                return Json.getJsonResult(true, "部分人员当天存在充值或消费记录，无法设置为无效！");
            } else {
                return Json.getJsonResult(true);
            }
        } else {
            String sql = "update tb_card_teachstudinfo set startdate=date_format(now(),'%Y-%m-%d'),card='',cardsn='',status=1,modifydate=now(),modifierid='" + creatorid + "' ,endsisdaily=99999999 where uid in('" + String.join("','", uidx) + "')";
            dbService.excuteSql(sql);
            syslogs.Write(request, "职工信息", String.format("修改职工或学生信息（%s）状态为（%s）", uids, status));
            return Json.getJsonResult(true);
        }
    }

    public JsonResult GetStudentIdCardInfo(HttpServletRequest request, HttpServletResponse response, String uid) {
        String sql = "select name,sex,nation,birthday,address,regorg,startdate,enddate,cardface,idcard from tb_card_teachstudinfo where uid='" + uid + "' ";
        JSONArray list = dbService.QueryList(sql);
        if (list.size() == 0) {
            return Json.getJsonResult("用户信息不存在");
        }
        JSONObject student = list.getJSONObject(0);
        if (StringUtils.isBlank(student.getString("idcard")) || StringUtils.isBlank(student.getString("cardface")) || StringUtils.isBlank(student.getString("regorg"))) {
            return Json.getJsonResult("未采集学生身份证信息");
        }
        return Json.getJsonResult(true, student);
    }

    public JsonData UploadStudentFile(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws Exception {
        MultipartFile[] files = new MultipartFile[]{file};
        List<String> filelist = FileHelper.SaveMultipartFile(files, WebConfig.getUploaddir(), "excel");
        if (filelist.size() == 0) {
            return Json.getJsonData(false, "文件保存失败");
        }
        String filepath = filelist.get(0);
        InputStream filestream = file.getInputStream();
        JSONArray firstrow = ExcelUtil.getFirstRow(filestream, filepath);
        return Json.getJsonData(true, filepath, firstrow);
    }

    public JsonResult ImportStudentFile(HttpServletRequest request, HttpServletResponse response, String excelpath, String columncfg) throws Exception {
        String filepath = String.format("%s%s", WebConfig.getUploaddir(), excelpath);
        File file = new File(filepath);
        if (!file.exists()) {
            return Json.getJsonResult("文档不存在");
        }
        InputStream stream = new FileInputStream(file);
        JSONArray exceltablerows = ExcelUtil.getBankListByExcel(stream, excelpath);
        if (exceltablerows.size() == 0) {
            return Json.getJsonResult("文档为空文件");
        }
        JSONArray columncfgarr = JSONArray.parseArray(columncfg);
        JSONArray clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("name");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择姓名对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("code");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择学号对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("sex");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择性别对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("orgcode");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择专业或班级代码对应列");
        }

        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("cardsn");
            }
        });
        if (clist.size() > 0) {
            JSONObject item = new JSONObject();
            item.put("columncode", "card");
            item.put("column", clist.getJSONObject(0).getString("column"));
            columncfgarr.add(item);
        }else {
            JSONObject item = new JSONObject();
            item.put("columncode", "cardsn");
            item.put("column", "");
            columncfgarr.add(item);

            item = new JSONObject();
            item.put("columncode", "card");
            item.put("column", "");
            columncfgarr.add(item);
        }


        final StringBuffer cardsql = new StringBuffer();
        final StringBuffer cardopsql = new StringBuffer();

        final StringBuffer sbsql = new StringBuffer();
        String columnstr = "";
        for (int r = 0; r < columncfgarr.size(); r++) {
            JSONObject c = columncfgarr.getJSONObject(r);
            columnstr += "," + c.getString("columncode");
        }
        List<String> codelist = new ArrayList<String>();
        List<String> cardlist = new ArrayList<String>();


        String disallowmsg = "";
        SysUser sysuser = userredis.get(request);
        final String userid = sysuser.getUid();
        String creator = sysuser.getName();
        sbsql.append("insert into tb_card_teachstudinfo(uid" + columnstr + ",infotype,focus,createdate,creatorid,modifydate,status,endsisdaily) values");

        //目前的组织架构
        String selSql = "select name,code from tb_card_orgframework where orgtype in (1,3,4)";
        List<TeachStudInfo> orgNameList = dbService.queryList(selSql, TeachStudInfo.class);

        //列头
        JSONArray cells = new JSONArray();
        JSONArray existslist = new JSONArray();
        JSONArray allowlist = new JSONArray();
        String cardsn = "";
        for (int t = 0; t < exceltablerows.size(); t++) {
            boolean allowImport = true;
            JSONObject row = exceltablerows.getJSONObject(t);
            JSONObject datarow = new JSONObject();
            tag1:
            for (int r = 0; r < columncfgarr.size(); r++) {
                JSONObject c = columncfgarr.getJSONObject(r);
                String columnvalue = row.getString(c.getString("column"));
                String columncode = c.getString("columncode");
                if (StringUtils.isBlank(columnvalue)) {
                    columnvalue = "";
                }
                if (t == 0) {
                    //组装列头
                    JSONObject cell = new JSONObject();
                    cell.put("name", columncode);
                    cell.put("datakey", columncode);
                    cell.put("size", 15);
                    cells.add(cell);
                }
                datarow.put(columncode, columnvalue);

                if (columncode.equals("code")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("学号不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        if (columnvalue.indexOf(".00") != -1) {
                            columnvalue = columnvalue.replace(".00", "");
                        }
                        if (codelist.contains(columnvalue)) {
                            allowImport = false;
                            disallowmsg = String.format("学号不能重复，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                        codelist.add(columnvalue);
                    }
                }

                if ((columncode.equals("cardsn")) && !StringUtils.isBlank(columnvalue)) {
                    if (columnvalue.indexOf(".00") != -1) {
                        columnvalue = columnvalue.replace(".00", "");
                    }
                    columnvalue = ComHelper.LeftPad(columnvalue, 12, '0');
                    cardsn = columnvalue;
                    if (columncode.equals("cardsn")) {
                        if (cardlist.contains(columnvalue)) {
                            allowImport = false;
                            disallowmsg = String.format("卡序列号不能重复，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                        cardlist.add(columnvalue);
                    }
                } else if ((columncode.equals("cardsn")) && StringUtils.isBlank(columnvalue)){
                    String autoCard = sysconfig.get("autoCard");
                    if (StringUtils.isNotEmpty(autoCard)&&autoCard.equals("1")){
                        boolean flag = true;
                        while (flag){
                            columnvalue = generateRandomNumberString(8);
                            columnvalue = ComHelper.LeftPad(columnvalue, 12, '0');
                            flag = isCard(columnvalue);
                        }
                        cardsn = columnvalue;
                        cardlist.add(columnvalue);
                    }
                }

                if ((columncode.equals("card")) && StringUtils.isBlank(columnvalue)) {
                    columnvalue = cardsn;
                }


                if (columncode.equals("name")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("姓名不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    }
                }

                if (columncode.equals("sex")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("性别不能为空，允许值为1或2(1表示男,2表示女)，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        if (columnvalue.indexOf(".00") != -1) {
                            columnvalue = columnvalue.replace(".00", "");
                        }
                        if (columnvalue.equals("男")) {
                            columnvalue = "1";
                        } else if (columnvalue.equals("女")) {
                            columnvalue = "2";
                        }
                        if (!columnvalue.equals("1") && !columnvalue.equals("2")) {
                            allowImport = false;
                            disallowmsg = String.format("性别允许值为1或2(1表示男,2表示女)，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                    }
                }
                if (columncode.equals("mobile")) {
                    if (!StringUtils.isBlank(columnvalue)) {
                        if (columnvalue.indexOf(".00") != -1) {
                            columnvalue = columnvalue.replace(".00", "");
                        }
                        if (columnvalue.length() != 11) {
                            allowImport = false;
                            disallowmsg = String.format("手机号码长度必须为11位，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                    }
                }
                if (columncode.equals("orgcode")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("专业或班级代码不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        String finalColumnvalue = columnvalue;
                        List<TeachStudInfo> studInfoList = orgNameList.stream()
                                .filter(i -> i.getName().equals(finalColumnvalue.replaceAll("\\s+", "")) || i.getCode().equals(finalColumnvalue.replaceAll("\\s+", "")))
                                .collect(Collectors.toList());
                        if (CollectionUtil.isEmpty(studInfoList)) {
                            allowImport = false;
                            disallowmsg = String.format("专业或班级代码错误，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                        if (studInfoList.size() > 1) {
                            allowImport = false;
                            disallowmsg = String.format("专业或班级代码不唯一错误，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                        String code = studInfoList.get(0).getCode();
                        columnvalue = code;
                        if (code.contains(".00")) {
                            columnvalue = code.replace(".00", "");
                        }
                        if ((studInfoList.get(0).getCode().length() % 3) != 0) {
                            columnvalue = ComHelper.LeftPad(code, code.length() + (3 - columnvalue.length() % 3), '0');
                        }
                    }
                }

                //当人员性质值为空时，默认保存为3.住宿生
                if (columncode.equals("property")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        columnvalue = "3";
                    }
                }
                datarow.put(columncode, columnvalue);
            }
            if (!allowImport) {
                datarow.put("disallowmsg", disallowmsg);
                existslist.add(datarow);
            } else {
                allowlist.add(datarow);
            }
        }

        JSONArray ImportData = new JSONArray();
        if (codelist.size() > 0 && allowlist.size() > 0) {
            final List<String> existscode = new ArrayList<String>();
            String sql = "select code from tb_card_teachstudinfo where find_in_set(code,'" + ComHelper.ListToString(codelist) + "')>0";
            JSONArray codejsonarray = dbService.QueryList(sql);
            for (int i = 0; i < codejsonarray.size(); i++) {
                JSONObject item = codejsonarray.getJSONObject(i);
                existscode.add(item.getString("code"));
            }

            if (existscode.size() > 0) {
                for (int i = 0; i < allowlist.size(); i++) {
                    JSONObject model = allowlist.getJSONObject(i);
                    if (existscode.contains(model.getString("code"))) {
                        model.put("disallowmsg", "学号在系统已存在");
                        existslist.add(model);
                    } else {
                        ImportData.add(model);
                    }
                }
            } else {
                ImportData.addAll(allowlist);
            }
        } else {
            ImportData.addAll(allowlist);
        }
        allowlist.clear();

        if (cardlist.size() > 0 && ImportData.size() > 0) {
            List<String> existscard = new ArrayList<String>();
            String sql = "select cardsn from tb_card_cardinfo where status in(1,2) and find_in_set(cardsn,'" + ComHelper.ListToString(cardlist) + "')>0";
            JSONArray cardjsonarray = dbService.QueryList(sql);
            for (int i = 0; i < cardjsonarray.size(); i++) {
                JSONObject item = cardjsonarray.getJSONObject(i);
                existscard.add(item.getString("cardsn"));
            }
            if (existscard.size() > 0) {
                for (int i = 0; i < ImportData.size(); i++) {
                    JSONObject model = ImportData.getJSONObject(i);
                    if (existscard.contains(model.getString("cardsn"))) {
                        model.put("disallowmsg", "卡序列号在系统已存在并且有效");
                        existslist.add(model);
                    } else {
                        allowlist.add(model);
                    }
                }
            } else {
                allowlist.addAll(ImportData);
            }
        } else {
            allowlist.addAll(ImportData);
        }
        ImportData.clear();

        final Set<String> labelSet = new HashSet<String>();

        for (int t = 0; t < allowlist.size(); t++) {
            JSONObject row = allowlist.getJSONObject(t);
            String infoid = UUID.randomUUID().toString();
            sbsql.append("('" + infoid + "'");
            for (int r = 0; r < columncfgarr.size(); r++) {
                JSONObject c = columncfgarr.getJSONObject(r);
                String columncode = c.getString("columncode");
                String columnvalue = row.getString(columncode);

                if (columncode.equals("sex")) {
                    if (columnvalue.equals("男")) {
                        columnvalue = "1";
                    } else if (columnvalue.equals("女")) {
                        columnvalue = "2";
                    }
                }
                if ((columncode.equals("cardsn") || columncode.equals("card")) && !StringUtils.isBlank(columnvalue)) {
                    columnvalue = ComHelper.LeftPad(columnvalue, 12, '0');
                }
                if (columncode.equals("orgcode")) {
                    if (columnvalue.length() % 3 != 0) {
                        columnvalue = ComHelper.LeftPad(columnvalue, ((columnvalue.length() - columnvalue.length() % 3) / 3 + 1) * 3, '0');
                    }
                }

                if ("infolabel".equals(columncode)) {
                    String[] labelSplit = columnvalue.split(",");
                    labelSet.addAll(Arrays.asList(labelSplit));
                }

                sbsql.append(",'" + columnvalue + "'");

                if (columncode.equals("cardsn") && !StringUtils.isBlank(columnvalue)) {
                    String cardid = UUID.randomUUID().toString();
                    if (cardsql.length() > 0) {
                        cardsql.append(",");
                    }
                    cardsql.append("('" + cardid + "','" + infoid + "','本人', '" + columnvalue + "', '" + columnvalue + "',0,1,0,0,0,0,'2050-01-01',now(),'" + userid + "',now(),'" + userid + "',1)");
                    if (cardopsql.length() > 0) {
                        cardopsql.append(",");
                    }
                    cardopsql.append("(uuid(),'" + infoid + "','" + cardid + "','" + columnvalue + "','" + columnvalue + "',0,1,'本人','" + creator + "','0','导入卡号',now(),'" + userid + "')");
                }
            }
            sbsql.append(",2,0,now(),'" + userid + "',now(),1,99999999)");
            if (t < allowlist.size() - 1) {
                sbsql.append(",");
            }
        }

        if (allowlist.size() == 0) {
            sbsql.delete(0, sbsql.length());
        }

        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                // TODO Auto-generated method stub
                if (sbsql.length() > 0) {
                    Log.info(this.getClass(), "导入人员信息" + sbsql);
                    PreparedStatement ps = connection.prepareStatement(sbsql.toString());
                    ps.executeUpdate();
                    ps.close();

                    String sql = "update tb_card_teachstudinfo info inner join tb_card_orgframework org on org.orgcode=info.orgcode set info.orgcode=org.code,info.modifydate=now(),info.modifierid='" + userid + "';";
                    ps = connection.prepareStatement(sql.toString());
                    ps.executeUpdate();
                    ps.close();
                }

                if (cardsql.length() > 0) {
                    String str = "insert into tb_card_cardinfo(uid,infoid,usedes,cardno,cardsn,deposit,ismain,balance,vicewallet,waterwallet,timescount,enddate,createdate,creatorid,modifydate,modifierid,status) values";
                    cardsql.insert(0, str);
                    dbService.excuteSql(cardsql.toString());

                    if (cardopsql.length() > 0) {
                        str = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,des,createdate,creatorid) values";
                        cardopsql.insert(0, str);
                        PreparedStatement ps = connection.prepareStatement(cardopsql.toString());
                        ps.executeUpdate();
                        ps.close();

                        String dopsql = "update tb_card_cardinfo_oprecord op inner join tb_card_teachstudinfo info on info.uid=op.infoid set op.infocode=info.code,op.infoname=info.name,op.orgname=getorgname(info.orgcode) where op.infocode='0' ";
                        ps = connection.prepareStatement(dopsql.toString());
                        ps.executeUpdate();
                        ps.close();
                    }
                }

                if (!labelSet.isEmpty()) {
                    //查找表内已存在的标签
                    String sqlLabel = "SELECT label FROM tb_card_infolabel WHERE infotype=2 AND label IN ('" + String.join("','", labelSet) + "')";
                    JSONArray labelja = dbService.QueryList(sqlLabel);
                    ArrayList<String> labelList = new ArrayList<>();
                    if (!labelja.isEmpty()) {
                        for (int i = 0; i < labelja.size(); i++) {
                            labelList.add(labelja.getJSONObject(i).getString("label"));
                        }
                    }
                    //去除已存在的标签
                    labelSet.removeAll(labelList);
                    //插入新标签
                    if (!labelSet.isEmpty()) {
                        StringBuilder insertLabel = new StringBuilder("INSERT INTO tb_card_infolabel (uid,label,infotype) VALUES");
                        for (String label : labelSet) {
                            insertLabel.append("(uuid(),'").append(label).append("',2),");
                        }
                        insertLabel.deleteCharAt(insertLabel.length() - 1);
                        PreparedStatement ps = connection.prepareStatement(insertLabel.toString());
                        ps.executeUpdate();
                        ps.close();
                    }
                }
            }
        });
        syslogs.Write(request, "学生信息", "导入学生信息【" + excelpath + "】");
        String existsfilepath = "";
        if (existslist.size() > 0) {
            JSONObject cell = new JSONObject();
            cell.put("name", "备注");
            cell.put("datakey", "disallowmsg");
            cell.put("size", 20);
            cells.add(cell);
            String title= "学生信息";
            String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
            String filename =  title+format + ".xlsx";
            existsfilepath = ExcelUtil.ExportExcel(existslist, cells, WebConfig.getUploaddir(), "temp",filename,title);
        }
        return Json.getJsonResult(true, existsfilepath);
    }

    public JsonResult ImportMoneyFile(HttpServletRequest request, HttpServletResponse response, String excelpath, String columncfg) throws Exception {
        String filepath = String.format("%s%s", WebConfig.getUploaddir(), excelpath);
        File file = new File(filepath);
        if (!file.exists()) {
            return Json.getJsonResult("文档不存在");
        }
        InputStream stream = new FileInputStream(file);
        JSONArray exceltablerows = ExcelUtil.getBankListByExcel(stream, excelpath);
        if (exceltablerows.size() == 0) {
            return Json.getJsonResult("文档为空文件");
        }
        JSONArray columncfgarr = JSONArray.parseArray(columncfg);
        JSONArray clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("name");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择姓名对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("code");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择学号对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("sex");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择性别对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("money");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择充值金额对应列");
        }

        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("cardsn");
            }
        });
        if (clist.size() > 0) {
            JSONObject item = new JSONObject();
            item.put("columncode", "card");
            item.put("column", clist.getJSONObject(0).getString("column"));
            columncfgarr.add(item);
        }


        String columnstr = "";
        for (int r = 0; r < columncfgarr.size(); r++) {
            JSONObject c = columncfgarr.getJSONObject(r);
            columnstr += "," + c.getString("columncode");
        }
        List<String> codelist = new ArrayList<String>();


        String disallowmsg = "";
        SysUser sysuser = userredis.get(request);
        final String userid = sysuser.getUid();

        //列头
        JSONArray cells = new JSONArray();
        JSONArray existslist = new JSONArray();
        JSONArray allowlist = new JSONArray();
        for (int t = 0; t < exceltablerows.size(); t++) {
            boolean allowImport = true;
            JSONObject row = exceltablerows.getJSONObject(t);
            JSONObject datarow = new JSONObject();
            tag1:
            for (int r = 0; r < columncfgarr.size(); r++) {
                JSONObject c = columncfgarr.getJSONObject(r);
                String columnvalue = row.getString(c.getString("column"));
                String columncode = c.getString("columncode");
                if (StringUtils.isBlank(columnvalue)) {
                    columnvalue = "";
                }
                if (t == 0) {
                    //组装列头
                    JSONObject cell = new JSONObject();
                    cell.put("name", columncode);
                    cell.put("datakey", columncode);
                    cell.put("size", 15);
                    cells.add(cell);
                }
                datarow.put(columncode, columnvalue);

                if (columncode.equals("code")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("学号不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        if (columnvalue.indexOf(".00") != -1) {
                            columnvalue = columnvalue.replace(".00", "");
                        }
                        codelist.add(columnvalue);
                    }
                }


                if (columncode.equals("name")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("姓名不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    }
                }

                if (columncode.equals("sex")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("性别不能为空，允许值为1或2(1表示男,2表示女)，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        if (columnvalue.indexOf(".00") != -1) {
                            columnvalue = columnvalue.replace(".00", "");
                        }
                        if (columnvalue.equals("男")) {
                            columnvalue = "1";
                        } else if (columnvalue.equals("女")) {
                            columnvalue = "2";
                        }
                        if (!columnvalue.equals("1") && !columnvalue.equals("2")) {
                            allowImport = false;
                            disallowmsg = String.format("性别允许值为1或2(1表示男,2表示女)，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                    }
                }

                if (columncode.equals("orgcode")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("充值金额不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        if(new BigDecimal(columnvalue).compareTo(BigDecimal.ZERO)<1){
                            disallowmsg = String.format("充值金额不能为负或零，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                    }
                }

                datarow.put(columncode, columnvalue);
            }
            if (!allowImport) {
                datarow.put("disallowmsg", disallowmsg);
                existslist.add(datarow);
            } else {
                allowlist.add(datarow);
            }
        }

        JSONArray ImportData = new JSONArray();
        if (codelist.size() > 0 && allowlist.size() > 0) {
            final List<String> existscode = new ArrayList<String>();
            String sql = "select code from tb_card_teachstudinfo where find_in_set(code,'" + ComHelper.ListToString(codelist) + "')>0";
            JSONArray codejsonarray = dbService.QueryList(sql);
            for (int i = 0; i < codejsonarray.size(); i++) {
                JSONObject item = codejsonarray.getJSONObject(i);
                existscode.add(item.getString("code"));
            }

            if (existscode.size() > 0) {
                for (int i = 0; i < allowlist.size(); i++) {
                    JSONObject model = allowlist.getJSONObject(i);
                    if (existscode.contains(model.getString("code"))) {
                        String sqlcard = "SELECT a.`code` FROM tb_card_teachstudinfo a JOIN tb_card_cardinfo b ON a.uid = b.infoid WHERE b.`status` =1 AND a.code = '"+model.getString("code")+"'";
                        JSONObject jsonObject = dbService.QueryJSONObject(sqlcard);
                        if (jsonObject==null){
                            model.put("disallowmsg", "该学生对应的卡不存在");
                            existslist.add(model);
                        }else {
                            ImportData.add(model);
                        }
                    } else {
                        model.put("disallowmsg", "该学号不存在");
                        existslist.add(model);
                    }
                }
            } else {
                ImportData.addAll(allowlist);
            }
        } else {
            ImportData.addAll(allowlist);
        }
        final StringBuffer sbsql = new StringBuffer();
        for (int t = 0; t < allowlist.size(); t++) {
            JSONObject row = allowlist.getJSONObject(t);
            String code = row.getString("code");
            String money = row.getString("money");
            String sqlcard = "SELECT a.`uid` as suid,b.`uid` as cuid , b.`cardno` FROM tb_card_teachstudinfo a JOIN tb_card_cardinfo b ON a.uid = b.infoid WHERE b.`status` =1 AND a.code = '"+code+"'";
            String uid = UUID.randomUUID().toString();
            JSONObject jsonObject = dbService.QueryJSONObject(sqlcard);
            if (jsonObject!=null){
                String suid = jsonObject.getString("suid");
                String cuid = jsonObject.getString("cuid");
                String cardno = jsonObject.getString("cardno");
                sbsql.append("('" + uid + "','"+ cuid + "','"+ suid + "','"+ cardno + "',1"+ ","+ money+",1,2,3,3,null,now(),'批量导入充值金额',now(),'"+userid+"',now(),'"+userid+"',1,null,null)");
                if (t < allowlist.size() - 1) {
                    sbsql.append(",");
                }
            }
        }
        //dbService.excuteSql("INSERT tb_card_transaction_detail (uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,interfixid,tradedate,des,createdate,creatorid,modifydate,modifierid,status,isupload,dynamics_type) VALUES"+sbsql.toString(),null);
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                PreparedStatement ps = connection.prepareStatement("INSERT tb_card_transaction_detail (uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,interfixid,tradedate,des,createdate,creatorid,modifydate,modifierid,status,isupload,dynamics_type) VALUES"+sbsql.toString());
                ps.executeUpdate();
                ps.close();
            }
        });
        syslogs.Write(request, "导入金额信息", "导入金额信息【" + excelpath + "】");
        String existsfilepath = "";
        if (existslist.size() > 0) {
            JSONObject cell = new JSONObject();
            cell.put("name", "备注");
            cell.put("datakey", "disallowmsg");
            cell.put("size", 20);
            cells.add(cell);
            String title= "学生充值金额信息";
            String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
            String filename =  title+format + ".xlsx";
            existsfilepath = ExcelUtil.ExportExcel(existslist, cells, WebConfig.getUploaddir(), "temp",filename,title);
        }
        return Json.getJsonResult(true, existsfilepath);
    }

    public JsonResult ImportTeachMoneyFile(HttpServletRequest request, HttpServletResponse response, String excelpath, String columncfg) throws Exception {
        String filepath = String.format("%s%s", WebConfig.getUploaddir(), excelpath);
        File file = new File(filepath);
        if (!file.exists()) {
            return Json.getJsonResult("文档不存在");
        }
        InputStream stream = new FileInputStream(file);
        JSONArray exceltablerows = ExcelUtil.getBankListByExcel(stream, excelpath);
        if (exceltablerows.size() == 0) {
            return Json.getJsonResult("文档为空文件");
        }
        JSONArray columncfgarr = JSONArray.parseArray(columncfg);
        JSONArray clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("name");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择姓名对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("code");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择工号对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("sex");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择性别对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("money");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择充值金额对应列");
        }

        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("cardsn");
            }
        });
        if (clist.size() > 0) {
            JSONObject item = new JSONObject();
            item.put("columncode", "card");
            item.put("column", clist.getJSONObject(0).getString("column"));
            columncfgarr.add(item);
        }


        String columnstr = "";
        for (int r = 0; r < columncfgarr.size(); r++) {
            JSONObject c = columncfgarr.getJSONObject(r);
            columnstr += "," + c.getString("columncode");
        }
        List<String> codelist = new ArrayList<String>();


        String disallowmsg = "";
        SysUser sysuser = userredis.get(request);
        final String userid = sysuser.getUid();

        //列头
        JSONArray cells = new JSONArray();
        JSONArray existslist = new JSONArray();
        JSONArray allowlist = new JSONArray();
        for (int t = 0; t < exceltablerows.size(); t++) {
            boolean allowImport = true;
            JSONObject row = exceltablerows.getJSONObject(t);
            JSONObject datarow = new JSONObject();
            tag1:
            for (int r = 0; r < columncfgarr.size(); r++) {
                JSONObject c = columncfgarr.getJSONObject(r);
                String columnvalue = row.getString(c.getString("column"));
                String columncode = c.getString("columncode");
                if (StringUtils.isBlank(columnvalue)) {
                    columnvalue = "";
                }
                if (t == 0) {
                    //组装列头
                    JSONObject cell = new JSONObject();
                    cell.put("name", columncode);
                    cell.put("datakey", columncode);
                    cell.put("size", 15);
                    cells.add(cell);
                }
                datarow.put(columncode, columnvalue);

                if (columncode.equals("code")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("工号不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        if (columnvalue.indexOf(".00") != -1) {
                            columnvalue = columnvalue.replace(".00", "");
                        }
                        codelist.add(columnvalue);
                    }
                }


                if (columncode.equals("name")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("姓名不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    }
                }

                if (columncode.equals("sex")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("性别不能为空，允许值为1或2(1表示男,2表示女)，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        if (columnvalue.indexOf(".00") != -1) {
                            columnvalue = columnvalue.replace(".00", "");
                        }
                        if (columnvalue.contains("男")) {
                            columnvalue = "1";
                        } else if (columnvalue.contains("女")) {
                            columnvalue = "2";
                        }
                        if (!columnvalue.equals("1") && !columnvalue.equals("2")) {
                            allowImport = false;
                            disallowmsg = String.format("性别允许值为1或2(1表示男,2表示女)，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                    }
                }

                if (columncode.equals("orgcode")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("充值金额不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        if(new BigDecimal(columnvalue).compareTo(BigDecimal.ZERO)<1){
                            disallowmsg = String.format("充值金额不能为负或零，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                    }
                }

                datarow.put(columncode, columnvalue);
            }
            if (!allowImport) {
                datarow.put("disallowmsg", disallowmsg);
                existslist.add(datarow);
            } else {
                allowlist.add(datarow);
            }
        }

        JSONArray ImportData = new JSONArray();
        if (codelist.size() > 0 && allowlist.size() > 0) {
            final List<String> existscode = new ArrayList<String>();
            String sql = "select code from tb_card_teachstudinfo where find_in_set(code,'" + ComHelper.ListToString(codelist) + "')>0";
            JSONArray codejsonarray = dbService.QueryList(sql);
            for (int i = 0; i < codejsonarray.size(); i++) {
                JSONObject item = codejsonarray.getJSONObject(i);
                existscode.add(item.getString("code"));
            }

            if (existscode.size() > 0) {
                for (int i = 0; i < allowlist.size(); i++) {
                    JSONObject model = allowlist.getJSONObject(i);
                    if (existscode.contains(model.getString("code"))) {
                        String sqlcard = "SELECT a.`code` FROM tb_card_teachstudinfo a JOIN tb_card_cardinfo b ON a.uid = b.infoid WHERE b.`status` =1 AND a.code = '"+model.getString("code")+"'";
                        JSONObject jsonObject = dbService.QueryJSONObject(sqlcard);
                        if (jsonObject==null){
                            model.put("disallowmsg", "该员工对应的卡不存在");
                            existslist.add(model);
                        }else {
                            ImportData.add(model);
                        }
                    } else {
                        model.put("disallowmsg", "该工号不存在");
                        existslist.add(model);
                    }
                }
            } else {
                ImportData.addAll(allowlist);
            }
        } else {
            ImportData.addAll(allowlist);
        }
        final StringBuffer sbsql = new StringBuffer();
        for (int t = 0; t < allowlist.size(); t++) {
            JSONObject row = allowlist.getJSONObject(t);
            String code = row.getString("code");
            String money = row.getString("money");
            String sqlcard = "SELECT a.`uid` as suid,b.`uid` as cuid , b.`cardno` FROM tb_card_teachstudinfo a JOIN tb_card_cardinfo b ON a.uid = b.infoid WHERE b.`status` =1 AND a.code = '"+code+"'";
            String uid = UUID.randomUUID().toString();
            JSONObject jsonObject = dbService.QueryJSONObject(sqlcard);
            if (jsonObject!=null){
                String suid = jsonObject.getString("suid");
                String cuid = jsonObject.getString("cuid");
                String cardno = jsonObject.getString("cardno");
                sbsql.append("('" + uid + "','"+ cuid + "','"+ suid + "','"+ cardno + "',1"+ ","+ money+",2,2,3,3,null,now(),'批量导入充值金额',now(),'"+userid+"',now(),'"+userid+"',1,null,null)");
                if (t < allowlist.size() - 1) {
                    sbsql.append(",");
                }
            }
        }
        //dbService.excuteSql("INSERT tb_card_transaction_detail (uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,interfixid,tradedate,des,createdate,creatorid,modifydate,modifierid,status,isupload,dynamics_type) VALUES"+sbsql.toString(),null);
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                PreparedStatement ps = connection.prepareStatement("INSERT tb_card_transaction_detail (uid,cardid,infoid,cardno,tradetype,money,paywallet,orderstatus,payway,paytype,interfixid,tradedate,des,createdate,creatorid,modifydate,modifierid,status,isupload,dynamics_type) VALUES"+sbsql.toString());
                ps.executeUpdate();
                ps.close();
            }
        });
        syslogs.Write(request, "导入金额信息", "导入金额信息【" + excelpath + "】");
        String existsfilepath = "";
        if (existslist.size() > 0) {
            JSONObject cell = new JSONObject();
            cell.put("name", "备注");
            cell.put("datakey", "disallowmsg");
            cell.put("size", 20);
            cells.add(cell);
            String title= "员工充值金额信息";
            String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
            String filename =  title+format + ".xlsx";
            existsfilepath = ExcelUtil.ExportExcel(existslist, cells, WebConfig.getUploaddir(), "temp",filename,title);
        }
        return Json.getJsonResult(true, existsfilepath);
    }
    public JsonResult ImportTeacherFile(HttpServletRequest request, HttpServletResponse response, String excelpath, String columncfg) throws Exception {
        String filepath = String.format("%s%s", WebConfig.getUploaddir(), excelpath);
        File file = new File(filepath);
        if (!file.exists()) {
            return Json.getJsonResult("文档不存在");
        }
        InputStream stream = new FileInputStream(file);
        JSONArray exceltablerows = ExcelUtil.getBankListByExcel(stream, excelpath);
        if (exceltablerows.size() == 0) {
            return Json.getJsonResult("文档为空文件");
        }
        JSONArray columncfgarr = JSONArray.parseArray(columncfg);

        JSONArray clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("name");
            }
        });

        if (clist.size() == 0) {
            return Json.getJsonResult("请选择姓名对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("code");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择工号对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("sex");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择性别对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("orgcode");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择专业或班级代码对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("cardsn");
            }
        });

        if (clist.size() > 0) {
            JSONObject item = new JSONObject();
            item.put("columncode", "card");
            item.put("column", clist.getJSONObject(0).getString("column"));
            columncfgarr.add(item);
        }else {
            JSONObject item = new JSONObject();
            item.put("columncode", "cardsn");
            item.put("column", "");
            columncfgarr.add(item);

            item = new JSONObject();
            item.put("columncode", "card");
            item.put("column", "");
            columncfgarr.add(item);
        }

        final StringBuffer cardsql = new StringBuffer();
        final StringBuffer cardopsql = new StringBuffer();

        final StringBuffer sbsql = new StringBuffer();
        String columnstr = "";

        for (int r = 0; r < columncfgarr.size(); r++) {
            JSONObject c = columncfgarr.getJSONObject(r);
            columnstr += "," + c.getString("columncode");
        }

        List<String> codelist = new ArrayList<String>();
        List<String> cardlist = new ArrayList<String>();
        //目前的组织架构
        String selSql = "select name,code from tb_card_orgframework where orgtype in (1,2)";
        List<TeachStudInfo> orgNameList = dbService.queryList(selSql, TeachStudInfo.class);

        String disallowmsg = "";
        SysUser sysuser = userredis.get(request);
        final String userid = sysuser.getUid();
        String creator = sysuser.getName();
        sbsql.append("insert into tb_card_teachstudinfo(uid" + columnstr + ",infotype,focus,createdate,creatorid,modifydate,status,endsisdaily) values");

        //列头
        JSONArray cells = new JSONArray();
        JSONArray existslist = new JSONArray();
        JSONArray allowlist = new JSONArray();
        String cardsn = "";
        for (int t = 0; t < exceltablerows.size(); t++) {
            JSONObject row = exceltablerows.getJSONObject(t);
            boolean allowImport = true;
            JSONObject datarow = new JSONObject();
            tag1:
            for (int r = 0; r < columncfgarr.size(); r++) {
                JSONObject c = columncfgarr.getJSONObject(r);
                String columnvalue = row.getString(c.getString("column"));
                String columncode = c.getString("columncode");
                if (StringUtils.isBlank(columnvalue)) {
                    columnvalue = "";
                }
                if (t == 0) {
                    //组装列头
                    JSONObject cell = new JSONObject();
                    cell.put("name", columncode);
                    cell.put("datakey", columncode);
                    cell.put("size", 15);
                    cells.add(cell);
                }
                datarow.put(columncode, columnvalue);

                if (columncode.equals("code")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("工号不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        if (columnvalue.indexOf(".00") != -1) {
                            columnvalue = columnvalue.replace(".00", "");
                        }
                        if (codelist.contains(columnvalue)) {
                            allowImport = false;
                            disallowmsg = String.format("工号不能重复，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                        codelist.add(columnvalue);
                    }
                }

                if ((columncode.equals("cardsn") || columncode.equals("card")) && !StringUtils.isBlank(columnvalue)) {
                    if (columnvalue.indexOf(".00") != -1) {
                        columnvalue = columnvalue.replace(".00", "");
                    }
                    columnvalue = ComHelper.LeftPad(columnvalue, 12, '0');
                    cardsn = columnvalue;
                    if (columncode.equals("cardsn")) {
                        if (cardlist.contains(columnvalue)) {
                            allowImport = false;
                            disallowmsg = String.format("卡号不能重复，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                        cardlist.add(columnvalue);
                    }
                }else if ((columncode.equals("cardsn")) && StringUtils.isBlank(columnvalue)){
                    String autoCard = sysconfig.get("autoCard");
                    if (StringUtils.isNotEmpty(autoCard)&&autoCard.equals("1")){
                        boolean flag = true;
                        while (flag){
                            columnvalue = generateRandomNumberString(8);
                            columnvalue = ComHelper.LeftPad(columnvalue, 12, '0');
                            flag = isCard(columnvalue);
                        }
                        cardsn = columnvalue;
                        cardlist.add(columnvalue);
                    }
                }

                if ((columncode.equals("card")) && StringUtils.isBlank(columnvalue)) {
                    columnvalue = cardsn;
                }

                if (columncode.equals("name")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("姓名不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    }
                }

                if (columncode.equals("sex")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("性别不能为空，允许值为1或2(1表示男,2表示女)，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        if (columnvalue.indexOf(".00") != -1) {
                            columnvalue = columnvalue.replace(".00", "");
                        }
                        if (columnvalue.contains("男")) {
                            columnvalue = "1";
                        } else if (columnvalue.contains("女")) {
                            columnvalue = "2";
                        }
                        if (!columnvalue.equals("1") && !columnvalue.equals("2")) {
                            allowImport = false;
                            disallowmsg = String.format("性别允许值为1或2(1表示男,2表示女)，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                    }
                }
                if (columncode.equals("mobile")) {
                    if (!StringUtils.isBlank(columnvalue)) {
                        if (columnvalue.indexOf(".00") != -1) {
                            columnvalue = columnvalue.replace(".00", "");
                        }
                        if (columnvalue.length() != 11) {
                            allowImport = false;
                            disallowmsg = String.format("手机号码长度必须为11位，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                    }
                }
                if (columncode.equals("orgcode")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("组织架构信息不能为空，行号=%s！", String.valueOf(t + 1));
                        break tag1;
                    } else {
                        String finalColumnvalue = columnvalue;
                        List<TeachStudInfo> studInfoList = orgNameList.stream()
                                .filter(i -> i.getName().equals(finalColumnvalue.replaceAll("\\s+", "")) || i.getCode().equals(finalColumnvalue.replaceAll("\\s+", "")))
                                .collect(Collectors.toList());
                        if (CollectionUtil.isEmpty(studInfoList)) {
                            allowImport = false;
                            disallowmsg = String.format("组织架构信息错误，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                        if (studInfoList.size() > 1) {
                            allowImport = false;
                            disallowmsg = String.format("组织架构信息不唯一错误，行号=%s！", String.valueOf(t + 1));
                            break tag1;
                        }
                        String code = studInfoList.get(0).getCode();
                        columnvalue = code;
                        if (code.contains(".00")) {
                            columnvalue = code.replace(".00", "");
                        }
                        if ((studInfoList.get(0).getCode().length() % 3) != 0) {
                            columnvalue = ComHelper.LeftPad(code, code.length() + (3 - columnvalue.length() % 3), '0');
                        }
                    }
                }

                //当人员性质值为空时，默认保存为1.正式员工
                if (columncode.equals("property")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        columnvalue = "1";
                    }
                }
                datarow.put(columncode, columnvalue);
            }
            if (!allowImport) {
                datarow.put("disallowmsg", disallowmsg);
                existslist.add(datarow);
            } else {
                allowlist.add(datarow);
            }
        }

        JSONArray ImportData = new JSONArray();
        if (codelist.size() > 0 && allowlist.size() > 0) {
            final List<String> existscode = new ArrayList<String>();
            String sql = "select code from tb_card_teachstudinfo where find_in_set(code,'" + ComHelper.ListToString(codelist) + "')>0";
            JSONArray codejsonarray = dbService.QueryList(sql);
            for (int i = 0; i < codejsonarray.size(); i++) {
                JSONObject item = codejsonarray.getJSONObject(i);
                existscode.add(item.getString("code"));
            }

            if (existscode.size() > 0) {
                for (int i = 0; i < allowlist.size(); i++) {
                    JSONObject model = allowlist.getJSONObject(i);
                    if (existscode.contains(model.getString("code"))) {
                        model.put("disallowmsg", "工号在系统已存在");
                        existslist.add(model);
                    } else {
                        ImportData.add(model);
                    }
                }
            } else {
                ImportData.addAll(allowlist);
            }
        } else {
            ImportData.addAll(allowlist);
        }
        allowlist.clear();

        if (cardlist.size() > 0 && ImportData.size() > 0) {
            List<String> existscard = new ArrayList<String>();
            String sql = "select cardsn from tb_card_cardinfo where status in(1,2) and find_in_set(cardsn,'" + ComHelper.ListToString(cardlist) + "')>0";
            JSONArray cardjsonarray = dbService.QueryList(sql);
            for (int i = 0; i < cardjsonarray.size(); i++) {
                JSONObject item = cardjsonarray.getJSONObject(i);
                existscard.add(item.getString("cardsn"));
            }
            if (existscard.size() > 0) {
                for (int i = 0; i < ImportData.size(); i++) {
                    JSONObject model = ImportData.getJSONObject(i);
                    if (existscard.contains(model.getString("cardsn"))) {
                        model.put("disallowmsg", "卡号在系统已存在并且有效");
                        existslist.add(model);
                    } else {
                        allowlist.add(model);
                    }
                }
            } else {
                allowlist.addAll(ImportData);
            }
        } else {
            allowlist.addAll(ImportData);
        }
        ImportData.clear();

        final Set<String> labelSet = new HashSet<String>();

        for (int t = 0; t < allowlist.size(); t++) {
            JSONObject row = allowlist.getJSONObject(t);
            String infoid = UUID.randomUUID().toString();
            sbsql.append("('" + infoid + "'");
            for (int r = 0; r < columncfgarr.size(); r++) {
                JSONObject c = columncfgarr.getJSONObject(r);
                String columncode = c.getString("columncode");
                String columnvalue = row.getString(columncode);

                if (columncode.equals("sex")) {
                    if (columnvalue.equals("男")) {
                        columnvalue = "1";
                    } else if (columnvalue.equals("女")) {
                        columnvalue = "2";
                    }
                }
                if ((columncode.equals("cardsn") || columncode.equals("card")) && !StringUtils.isBlank(columnvalue)) {
                    columnvalue = ComHelper.LeftPad(columnvalue, 12, '0');
                }
                if (columncode.equals("orgcode")) {
                    if (columnvalue.length() % 3 != 0) {
                        columnvalue = ComHelper.LeftPad(columnvalue, ((columnvalue.length() - columnvalue.length() % 3) / 3 + 1) * 3, '0');
                    }
                }

                if ("infolabel".equals(columncode)) {
                    String[] labelSplit = columnvalue.split(",");
                    labelSet.addAll(Arrays.asList(labelSplit));
                }

                sbsql.append(",'" + columnvalue + "'");

                if (columncode.equals("cardsn") && !StringUtils.isBlank(columnvalue)) {
                    String cardid = UUID.randomUUID().toString();
                    if (cardsql.length() > 0) {
                        cardsql.append(",");
                    }
                    cardsql.append("('" + cardid + "','" + infoid + "','本人', '" + columnvalue + "', '" + columnvalue + "',0,1,0,0,0,0,'2050-01-01',now(),'" + userid + "',now(),'" + userid + "',1)");
                    if (cardopsql.length() > 0) {
                        cardopsql.append(",");
                    }
                    cardopsql.append("(uuid(),'" + infoid + "','" + cardid + "','" + columnvalue + "','" + columnvalue + "',0,1,'本人','" + creator + "','0','导入卡号',now(),'" + userid + "')");
                }
            }
            sbsql.append(",1,0,now(),'" + userid + "',now(),1,99999999)");
            if (t < allowlist.size() - 1) {
                sbsql.append(",");
            }
        }

        if (allowlist.size() == 0) {
            sbsql.delete(0, sbsql.length());
        }

        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                // TODO Auto-generated method stub
                if (sbsql.length() > 0) {
                    Log.info(this.getClass(), "导入人员信息" + sbsql);
                    PreparedStatement ps = connection.prepareStatement(sbsql.toString());
                    ps.executeUpdate();
                    ps.close();


                    String sql = "update tb_card_teachstudinfo info inner join tb_card_orgframework org on org.orgcode=info.orgcode set info.orgcode=org.code,info.modifydate=now(),info.modifierid='" + userid + "';";
                    ps = connection.prepareStatement(sql.toString());
                    ps.executeUpdate();
                    ps.close();

                    sql = "update tb_card_teachstudinfo info left join tb_card_orgframework org on org.code=info.orgcode set info.orgcode='001',info.modifydate=now(),info.modifierid='" + userid + "' where ifnull(org.uid, '')='';";
                    ps = connection.prepareStatement(sql.toString());
                    ps.executeUpdate();
                    ps.close();
                }

                if (cardsql.length() > 0) {
                    String str = "insert into tb_card_cardinfo(uid,infoid,usedes,cardno,cardsn,deposit,ismain,balance,vicewallet,waterwallet,timescount,enddate,createdate,creatorid,modifydate,modifierid,status) values";
                    cardsql.insert(0, str);
                    dbService.excuteSql(cardsql.toString());

                    if (cardopsql.length() > 0) {
                        str = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,des,createdate,creatorid) values";
                        cardopsql.insert(0, str);
                        PreparedStatement ps = connection.prepareStatement(cardopsql.toString());
                        ps.executeUpdate();
                        ps.close();

                        String dopsql = "update tb_card_cardinfo_oprecord op inner join tb_card_teachstudinfo info on info.uid=op.infoid set op.infocode=info.code,op.infoname=info.name,op.orgname=getorgname(info.orgcode) where op.infocode='0' ";
                        ps = connection.prepareStatement(dopsql.toString());
                        ps.executeUpdate();
                        ps.close();
                    }
                }

                if (!labelSet.isEmpty()) {
                    //查找表内已存在的标签
                    String sqlLabel = "SELECT label FROM tb_card_infolabel WHERE infotype=1 AND label IN ('" + String.join("','", labelSet) + "')";
                    JSONArray labelja = dbService.QueryList(sqlLabel);
                    ArrayList<String> labelList = new ArrayList<>();
                    if (!labelja.isEmpty()) {
                        for (int i = 0; i < labelja.size(); i++) {
                            labelList.add(labelja.getJSONObject(i).getString("label"));
                        }
                    }
                    //去除已存在的标签
                    labelSet.removeAll(labelList);
                    //插入新标签
                    if (!labelSet.isEmpty()) {
                        StringBuilder insertLabel = new StringBuilder("INSERT INTO tb_card_infolabel (uid,label,infotype) VALUES");
                        for (String label : labelSet) {
                            insertLabel.append("(uuid(),'").append(label).append("',1),");
                        }
                        insertLabel.deleteCharAt(insertLabel.length() - 1);
                        PreparedStatement ps = connection.prepareStatement(insertLabel.toString());
                        ps.executeUpdate();
                        ps.close();
                    }
                }

            }
        });
        syslogs.Write(request, "职工信息", "导入职工档案【" + excelpath + "】");
        String existsfilepath = "";
        if (existslist.size() > 0) {
            JSONObject cell = new JSONObject();
            cell.put("name", "备注");
            cell.put("datakey", "disallowmsg");
            cell.put("size", 20);
            cells.add(cell);

            String title= "职工信息";
            String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
            String filename =  title+format + ".xlsx";

            existsfilepath = ExcelUtil.ExportExcel(existslist, cells, WebConfig.getUploaddir(), "temp",filename,title);
        }
        return Json.getJsonResult(true, existsfilepath);
    }

    public JsonResult ImportLeavelTeacherFile(HttpServletRequest request, HttpServletResponse response, String excelpath, String columncfg) throws Exception {
        String filepath = String.format("%s%s", WebConfig.getUploaddir(), excelpath);
        File file = new File(filepath);
        if (!file.exists()) {
            return Json.getJsonResult("文档不存在");
        }
        InputStream stream = new FileInputStream(file);
        JSONArray exceltablerows = ExcelUtil.getBankListByExcel(stream, excelpath);
        if (exceltablerows.size() == 0) {
            return Json.getJsonResult("文档为空文件");
        }
        JSONArray columncfgarr = JSONArray.parseArray(columncfg);
        JSONArray clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("code");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择工号对应列");
        }

        boolean allowImport = true;
        String disallowmsg = "";

        SysUser sysuser = userredis.get(request);
        final String userid = sysuser.getUid();
        String creator = sysuser.getName();

        List<String> codelist = new ArrayList<String>();
        for (int t = 0; t < exceltablerows.size(); t++) {
            JSONObject row = exceltablerows.getJSONObject(t);
            for (int r = 0; r < columncfgarr.size(); r++) {
                JSONObject c = columncfgarr.getJSONObject(r);
                String columnvalue = row.getString(c.getString("column"));
                String columncode = c.getString("columncode");
                if (StringUtils.isBlank(columnvalue)) {
                    columnvalue = "";
                }

                if (columncode.equals("code")) {
                    if (StringUtils.isBlank(columnvalue)) {
                        allowImport = false;
                        disallowmsg = String.format("工号不能为空，行号=%s！", String.valueOf(t + 1));
                        break;
                    }
                    codelist.add(columnvalue);
                }
            }
        }
        if (!allowImport) {
            return Json.getJsonResult(disallowmsg);
        }

        final JSONArray list = dbService.QueryList("select uid,code from tb_card_teachstudinfo where find_in_set(code, '" + String.join(",", ComHelper.ListToArray(codelist)) + "')>0  ");

        new Thread(new Runnable() {
            @Override
            public void run() {
                // TODO Auto-generated method stub
                try {
                    if (list.size() > 0) {
                        dbService.excuteTransaction(new CallbackTransaction() {
                            @Override
                            public void execute(Connection connection) throws SQLException {
                                // TODO Auto-generated method stub
                                for (int i = 0; i < list.size(); i++) {
                                    String infoid = list.getJSONObject(i).getString("uid");

                                    final String sql = "update tb_card_teachstudinfo set status=0,enddate=date_format(now(),'%Y-%m-%d'),card='',cardsn='',modifydate=now(),modifierid='" + userid + "' where uid='" + infoid + "';";
                                    final String sql2 = "update tb_card_cardinfo set des='人员离职，设置为无效卡；',modifierid='" + userid + "',modifydate=now(),enddate=now(),status=0 where infoid='" + infoid + "' and status in(1,2);";
                                    final String queryuid = "select uid from tb_sys_user where empid ='" + infoid + "'";

                                    PreparedStatement ps = connection.prepareStatement(sql);
                                    ps.executeUpdate();
                                    ps.close();

                                    ps = connection.prepareStatement(sql2);
                                    ps.executeUpdate();
                                    ps.close();

                                    ps = connection.prepareStatement(queryuid);
                                    ResultSet rs = ps.executeQuery();
                                    String uid = null;
                                    while (rs.next()) {
                                        uid = rs.getString("uid");
                                    }
                                    ps.close();
                                    rs.close();

                                    final String delrole = "delete from tb_sys_role_user where userid = '" + uid + "'";
                                    ps = connection.prepareStatement(delrole);
                                    ps.executeUpdate();
                                    ps.close();

                                    final String deluser = "delete from tb_sys_user where empid ='" + infoid + "'";
                                    ps = connection.prepareStatement(deluser);
                                    ps.executeUpdate();
                                    ps.close();

                                    ps = connection.prepareStatement("call sp_info_leaveoffice('" + infoid + "')");
                                    ps.executeUpdate();
                                    ps.close();

                                    Log.info(TeachStudCardService.class, "成功处理人员离职:" + infoid);
                                }
                            }
                        });
                    }

                    try {
                        JSONObject info = new JSONObject();
                        info.put("msgtype", 1000);
                        info.put("msg", "导入离职人员名单处理已完成");
                        WebSocketSets.getInstance().send("sysmsg", info.toJSONString());
                    } catch (Exception ex) {
                        Log.error(this.getClass(), ex.getMessage());
                    }

                } catch (Exception ex) {
                    Log.error(TeachStudCardService.class, ex.getMessage());
                }
            }
        }).start();

        return Json.getJsonResult(true, String.format("成功提交%s个工号，等待系统自动化处理", list.size()));
    }

    public String ExportTeacher(HttpServletRequest request, HttpServletResponse response, String viewleave, String property, String orgcode, boolean viewchild) throws IOException {

        String SQL = "SELECT ct.code,ct.name,(case  when ct.sex=1 then '男' when ct.sex=2 then '女' end) sex,ct.nation," +
                "date_format(ct.birthday,'%Y-%m-%d') birthday,ct.address,date_format(ct.startdate,'%Y-%m-%d') startdate," +
                "date_format(ct.enddate,'%Y-%m-%d') enddate,d.name as positionname,ct.card,ct.cardsn,ct.mobile,ct.idcard," +
                "getorgname(ct.orgcode) as orgname,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2," +
                "ct.linkmobile2,ct.linkdes2,ct.plateno,(case  when ct.status=0 then '离职' when ct.status=1 then '在职' end)status," +
                "d2.name as posrankname, d3.name as propertyname, ct.property " +
                "FROM tb_card_teachstudinfo ct " +
                "LEFT JOIN tb_sys_dictionary d on d.code=ct.position and d.groupcode='SYS0000045' " +
                "LEFT JOIN tb_sys_dictionary d2 on d2.code=ct.posrank and d2.groupcode='SYS0000046' " +
                "LEFT JOIN tb_sys_dictionary d3 on d3.code=ct.property and d3.groupcode='SYS0000055' " +
                "WHERE infotype=1";
        if (StringUtils.isNotBlank(viewleave)) {
            SQL += " AND ct.status=" + viewleave + " ";
        }
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                SQL += " AND ct.orgcode like '" + orgcode + "%' ";
            } else {
                SQL += " AND ct.orgcode='" + orgcode + "' ";
            }
        }
        JsonData result = dbService.QueryJsonData(SQL);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        if ("1".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        } else if ("2".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "性别");
        cell.put("datakey", "sex");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "民族");
        cell.put("datakey", "nation");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "生日");
        cell.put("datakey", "birthday");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "地址");
        cell.put("datakey", "adress");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "入职日期");
        cell.put("datakey", "startdate");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "离职日期");
        cell.put("datakey", "enddate");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "职工职位");
        cell.put("datakey", "positionname");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡号");
        cell.put("datakey", "card");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡序列号");
        cell.put("datakey", "cardsn");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "电话");
        cell.put("datakey", "mobile");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "身份证号");
        cell.put("datakey", "idcard");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属单位");
        cell.put("datakey", "orgname");
        cell.put("size", 30);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "紧急联系人");
        cell.put("datakey", "linkman1");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "紧急联系人电话");
        cell.put("datakey", "linkmobile1");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "紧急联系人关系");
        cell.put("datakey", "linkdes1");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "备用联系人");
        cell.put("datakey", "linkman2");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "备用联系人电话");
        cell.put("datakey", "linkmobile2");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "备用联系人关系");
        cell.put("datakey", "linkdes2");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "车牌号码");
        cell.put("datakey", "plateno");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "在职状态");
        cell.put("datakey", "status");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "人员类型");
        cell.put("datakey", "posrankname");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "人员性质");
        cell.put("datakey", "propertyname");
        cell.put("size", 15);
        cells.add(cell);

        String title= "职工信息";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
        return filepath;
    }

    public String ExportStudent(HttpServletRequest request, HttpServletResponse response, String orgcode, boolean viewchild) throws IOException {

        String SQL = "SELECT ct.code,ct.name,(case  when ct.sex=1 then '男' when ct.sex=2 then '女' end) sex,ct.nation," +
                "date_format(ct.birthday,'%Y-%m-%d') birthday,ct.address,ct.card,ct.cardsn,ct.mobile,ct.idcard," +
                "getorgname(ct.orgcode) as orgname,ct.infotype,ct.intoyear,ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2," +
                "ct.linkmobile2,ct.linkdes2,ct.infolabel,sd.name as property " +
                "FROM tb_card_teachstudinfo ct left join tb_sys_dictionary sd on ct.property = sd.code and sd.groupcode = 'SYS0000055' " +
                "WHERE infotype=2";
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                SQL += " AND ct.orgcode like '" + orgcode + "%' ";
            } else {
                SQL += " AND ct.orgcode='" + orgcode + "' ";
            }
        }
        JsonData result = dbService.QueryJsonData(SQL);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        if ("1".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        } else if ("2".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "性别");
        cell.put("datakey", "sex");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "民族");
        cell.put("datakey", "nation");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "生日");
        cell.put("datakey", "birthday");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "地址");
        cell.put("datakey", "address");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡号");
        cell.put("datakey", "card");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡序列号");
        cell.put("datakey", "cardsn");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "电话");
        cell.put("datakey", "mobile");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "身份证号");
        cell.put("datakey", "idcard");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属班级");
        cell.put("datakey", "orgname");
        cell.put("size", 30);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "紧急联系人");
        cell.put("datakey", "linkman1");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "紧急联系人电话");
        cell.put("datakey", "linkmobile1");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "紧急联系人关系");
        cell.put("datakey", "linkdes1");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "备用联系人");
        cell.put("datakey", "linkman2");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "备用联系人电话");
        cell.put("datakey", "linkmobile2");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "备用联系人关系");
        cell.put("datakey", "linkdes2");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "人员性质");
        cell.put("datakey", "property");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "标签");
        cell.put("datakey", "infolabel");
        cell.put("size", 15);
        cells.add(cell);

        String title= "学生信息";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
        return filepath;
    }

    public String ExportTemplate(HttpServletRequest request, HttpServletResponse response, String name) throws IOException {

        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        if ("学生信息导入模板".equals(name)) {

            cell = new JSONObject();
            cell.put("name", "学号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "姓名");
            cell.put("datakey", "name");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "卡序列号");
            cell.put("datakey", "cardsn");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "性别(1男2女)");
            cell.put("datakey", "sex");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "手机号");
            cell.put("datakey", "mobile");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "班级或专业代码");
            cell.put("datakey", "orgname");
            cell.put("size", 30);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "身份证号");
            cell.put("datakey", "idcard");
            cell.put("size", 30);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "入校年份");
            cell.put("datakey", "intoyear");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "民族");
            cell.put("datakey", "nation");
            cell.put("size", 10);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "生日");
            cell.put("datakey", "birthday");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "地址");
            cell.put("datakey", "address");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "标签");
            cell.put("datakey", "infolabel");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "人员性质");
            cell.put("datakey", "property");
            cell.put("size", 10);
            cells.add(cell);
        }
        else if ("职工信息导入模板".equals(name)) {

            if ("1".equals(WebConfig.getApptype())) {
                cell = new JSONObject();
                cell.put("name", "学工号");
                cell.put("datakey", "code");
                cell.put("size", 15);
                cells.add(cell);
            } else if ("2".equals(WebConfig.getApptype())) {
                cell = new JSONObject();
                cell.put("name", "工号");
                cell.put("datakey", "code");
                cell.put("size", 15);
                cells.add(cell);
            }

            cell = new JSONObject();
            cell.put("name", "姓名");
            cell.put("datakey", "name");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "卡序列号");
            cell.put("datakey", "cardsn");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "性别(1男2女)");
            cell.put("datakey", "sex");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "职位编号");
            cell.put("datakey", "position");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "职级编号");
            cell.put("datakey", "posrank");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "手机号");
            cell.put("datakey", "mobile");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "邮箱");
            cell.put("datakey", "email");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "机构代码");
            cell.put("datakey", "orgcode");
            cell.put("size", 30);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "身份证号");
            cell.put("datakey", "idcard");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "民族");
            cell.put("datakey", "nation");
            cell.put("size", 10);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "生日");
            cell.put("datakey", "birthday");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "地址");
            cell.put("datakey", "adress");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "入职日期");
            cell.put("datakey", "startdate");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "离职日期");
            cell.put("datakey", "enddate");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "通行密码");
            cell.put("datakey", "passpassword");
            cell.put("size", 15);
            cells.add(cell);


            cell = new JSONObject();
            cell.put("name", "紧急联系人");
            cell.put("datakey", "linkman1");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "紧急联系人电话");
            cell.put("datakey", "linkmobile1");
            cell.put("size", 20);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "紧急联系人关系");
            cell.put("datakey", "linkdes1");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "备用联系人");
            cell.put("datakey", "linkman2");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "备用联系人电话");
            cell.put("datakey", "linkmobile2");
            cell.put("size", 20);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "备用联系人关系");
            cell.put("datakey", "linkdes2");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "标签");
            cell.put("datakey", "infolabel");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "人员性质");
            cell.put("datakey", "property");
            cell.put("size", 15);
            cells.add(cell);
        }
        else if ("人员信息导入模板".equals(name)) {

            if ("1".equals(WebConfig.getApptype())) {
                cell = new JSONObject();
                cell.put("name", "学工号");
                cell.put("datakey", "code");
                cell.put("size", 15);
                cells.add(cell);
            } else if ("2".equals(WebConfig.getApptype())) {
                cell = new JSONObject();
                cell.put("name", "工号");
                cell.put("datakey", "code");
                cell.put("size", 15);
                cells.add(cell);
            }

            cell = new JSONObject();
            cell.put("name", "姓名");
            cell.put("datakey", "name");
            cell.put("size", 17);
            cells.add(cell);

        }
        else if ("家长信息导入模板".equals(name)) {

            if ("1".equals(WebConfig.getApptype())) {
                cell = new JSONObject();
                cell.put("name", "学号");
                cell.put("datakey", "code");
                cell.put("size", 15);
                cells.add(cell);
            } else if ("2".equals(WebConfig.getApptype())) {
                cell = new JSONObject();
                cell.put("name", "工号");
                cell.put("datakey", "code");
                cell.put("size", 15);
                cells.add(cell);
            }

            cell = new JSONObject();
            cell.put("name", "姓名");
            cell.put("datakey", "name");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "紧急联系人姓名");
            cell.put("datakey", "linkman");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "紧急联系人电话");
            cell.put("datakey", "linkmobile");
            cell.put("size", 20);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "与本人的关系");
            cell.put("datakey", "linkdes");
            cell.put("size", 15);
            cells.add(cell);
        }
        else if ("学生充值金额导入模板".equals(name)){
            cell = new JSONObject();
            cell.put("name", "学号");
            cell.put("datakey", "code");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "姓名");
            cell.put("datakey", "name");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "性别(1男2女)");
            cell.put("datakey", "sex");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "充值金额");
            cell.put("datakey", "money");
            cell.put("size", 15);
            cells.add(cell);
        }
        else if ("员工充值金额导入模板".equals(name)){
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "code");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "姓名");
            cell.put("datakey", "name");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "性别(1男2女)");
            cell.put("datakey", "sex");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "充值金额");
            cell.put("datakey", "money");
            cell.put("size", 15);
            cells.add(cell);
        }
        else if ("学生缴费情况导入模板".equals(name)){
            cell = new JSONObject();
            cell.put("name", "学号");
            cell.put("datakey", "code");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "姓名");
            cell.put("datakey", "name");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "性别(1男2女)");
            cell.put("datakey", "sex");
            cell.put("size", 17);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "学年");
            cell.put("datakey", "year");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "学费");
            cell.put("datakey", "xfmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "住宿费");
            cell.put("datakey", "zsmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "教材费");
            cell.put("datakey", "jcmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "保险费");
            cell.put("datakey", "bxmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "体检费");
            cell.put("datakey", "tjmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "服装费");
            cell.put("datakey", "fzmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "材料费");
            cell.put("datakey", "clmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "生活用品");
            cell.put("datakey", "shmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "押金");
            cell.put("datakey", "yjmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "缴费合计");
            cell.put("datakey", "totalpayment");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "升学费");
            cell.put("datakey", "sxmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "大专费");
            cell.put("datakey", "dzmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "资格证");
            cell.put("datakey", "zgzmoney");
            cell.put("size", 15);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", "欠费情况");
            cell.put("datakey", "qfmoney");
            cell.put("size", 15);
            cells.add(cell);

        }


        String title= name;
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        JSONArray array = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("template",1);
        array.add(jsonObject);
        String filepath = ExcelUtil.ExportExcel(array, cells, WebConfig.getUploaddir(), "temp",filename,title);
        return filepath;
    }

    public JSONArray getInfoLabel(HttpServletRequest request, HttpServletResponse response, int infotype) {
        String sql = "SELECT uid,label,infotype FROM tb_card_infolabel WHERE infotype=?";
        return dbService.QueryList(sql, infotype);
    }

    public JsonResult VerificationCPUserDocment(HttpServletRequest request, HttpServletResponse response, String idtype, String infocode,
                                                String usertype, String name, String idcard, String mobile, String nodeid, String appid, String signature,
                                                String timestamp) {
        if (!WebConfig.EnableCampusCPService()) {
            return Json.getJsonResult(false, "节点未受平台控制");
        }
        if (StringUtils.isBlank(appid)) {
            return Json.getJsonResult(false, "appid is null");
        }
        if (StringUtils.isBlank(signature)) {
            return Json.getJsonResult(false, "signature is null");
        }
        if (StringUtils.isBlank(nodeid)) {
            return Json.getJsonResult(false, "nodeid is null");
        }
        if (StringUtils.isBlank(timestamp)) {
            return Json.getJsonResult(false, "timestamp is null");
        }
        if (!appid.equals(WebConfig.getCampusCPAppId())) {
            return Json.getJsonResult(false, "appid is error");
        }
        if (!nodeid.equals(WebConfig.getNodeid())) {
            return Json.getJsonResult(false, "nodeid is error");
        }
        if (StringUtils.isBlank(usertype)) {
            return Json.getJsonResult(false, "usertype is null");
        }
        if (StringUtils.isBlank(name)) {
            return Json.getJsonResult(false, "name is null");
        }
        if (StringUtils.isBlank(mobile)) {
            return Json.getJsonResult(false, "mobile is null");
        }
        if (StringUtils.isNotBlank(idtype)) {
            if ("1".equals(idtype)) {
                if (StringUtils.isBlank(idcard)) {
                    return Json.getJsonResult(false, "idcard is null");
                }
            } else if (StringUtils.isBlank(infocode)) {
                return Json.getJsonResult(false, "infocode is null");
            }
        } else {
            return Json.getJsonResult(false, "idtype is null");
        }

        String query = String.format("appid=%s&idcard=%s&infocode=%s&mobile=%s&name=%s&nodeid=%s&timestamp=%s&usertype=%s&key=%s", appid, idcard, infocode, mobile, name, nodeid, timestamp, usertype, WebConfig.getCampusCPAppSecret());
        String versignature = MD5.MD5Encode(query);
        if (!versignature.equals(signature)) {
            return Json.getJsonResult(false, "signature is error");
        }
        String sql = "select uid,name,mobile,linkmobile1,linkmobile2,infotype from tb_card_teachstudinfo where idcard=?";
        JSONArray list = new JSONArray();
        if ("1".equals(idtype)) {
            list = dbService.QueryList(sql, idcard);
        } else if ("2".equals(idtype)) {
            sql = "select uid,name,mobile,linkmobile1,linkmobile2,infotype from tb_card_teachstudinfo where code=?";
            list = dbService.QueryList(sql, infocode);
        }

        if (list.size() == 0) {
            if ("2".equals(idtype)) {
                return Json.getJsonResult(false, "学工号不存在");
            }
            return Json.getJsonResult(false, "身份证号码不存在");
        }
        JSONObject info = list.getJSONObject(0);
        if (!info.getString("name").equals(name)) {
            return Json.getJsonResult(false, "姓名不一致");
        }

        String infomobile = info.getString("mobile");
        String linkmobile1 = info.getString("linkmobile1");
        String linkmobile2 = info.getString("linkmobile2");
        if (!mobile.equals(linkmobile1) && !mobile.equals(linkmobile2) && !mobile.equals(infomobile)) {
            return Json.getJsonResult(false, "联系人手机号未登记或与登记不符");
        }

        if (usertype.equals("2")) {
            if (!info.getString("infotype").equals("2")) {
                return Json.getJsonResult(false, "用户类型不一致");
            }
        }
        if (usertype.equals("2") || usertype.equals("1")) {
            if (!info.getString("infotype").equals("2")) {
                return Json.getJsonResult(false, "用户类型不一致");
            }
        } else {
            if (!info.getString("infotype").equals("1")) {
                return Json.getJsonResult(false, "用户类型不一致");
            }
        }
        return Json.getJsonResult(true, info.getString("uid"));
    }

    public JsonResult exportQRCode(HttpServletRequest request, HttpServletResponse response, String infoid) {
        String sql = "SELECT code,name,card FROM tb_card_teachstudinfo WHERE uid IN ('" + String.join("','", infoid.split(",")) + "')";
        JSONArray infoja = dbService.QueryList(sql);
        if (infoja.isEmpty()) {
            return Json.getJsonResult(false, "无可导出的二维码");
        }
        try {
            List<File> fileList = new ArrayList<>();
            for (int i = 0; i < infoja.size(); i++) {
                String card = infoja.getJSONObject(i).getString("card").replaceAll("^(0+)", "");
                String code = infoja.getJSONObject(i).getString("code");
                String name = infoja.getJSONObject(i).getString("name");
                File file = new File(WebConfig.getUploaddir() + "temp/" + code + "_" + name + ".png");
                BufferedImage qrCodeImage = QRcodeUtils.createQrCodeImage(card, 1060, "");
                ImageIO.write(qrCodeImage, "png", file);
                fileList.add(file);
            }
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            String zippath = "temp/" + timestamp.getTime() + ".zip";
            File zipfile = new File(WebConfig.getUploaddir() + zippath);
            zipfile.createNewFile();
            ZipUtils.toZip(fileList, WebConfig.getUploaddir() + zippath);
            for (File file : fileList) {
                file.delete();
            }
            return Json.getJsonResult(true, "/" + zippath);
        } catch (Exception e) {
            return Json.getJsonResult(false);
        }
    }

    public JsonResult resetPassword(HttpServletRequest request, HttpServletResponse response, String infoid) {
        String sql = "update tb_card_teachstudinfo set mp_password = null WHERE uid IN ('" + String.join("','", infoid.split(",")) + "')";
        dbService.excuteSql(sql);
        return Json.getJsonResult(true);
    }

    public JsonData uploadUpdateFile(MultipartFile file) throws Exception {
        MultipartFile[] files = new MultipartFile[]{file};
        List<String> filelist = FileHelper.SaveMultipartFile(files, WebConfig.getUploaddir(), "tmp");
        if (filelist.size() == 0) {
            return Json.getJsonData(false, "文件保存失败");
        }
        String filepath = filelist.get(0);
        InputStream filestream = file.getInputStream();
        JSONArray firstrow = ExcelUtil.getFirstRow(filestream, filepath);
        return Json.getJsonData(true, filepath, firstrow);
    }

    public JsonResult updatePersonInfoByExcel(String excelPath, String columnCfgStr) throws Exception {
        String filepath = String.format("%s%s", WebConfig.getUploaddir(), excelPath);
        File file = new File(filepath);
        if (!file.exists()) {
            return Json.getJsonResult("文档不存在");
        }
        InputStream stream = new FileInputStream(file);
        JSONArray tableArr = ExcelUtil.getBankListByExcel(stream, excelPath);
        if (tableArr.size() == 0) {
            return Json.getJsonResult("文档为空文件");
        }
        JSONArray columnCfg = JSONArray.parseArray(columnCfgStr);
        // 数据库中对应的 列名
        Set<String> colunmnCodeSet = columnCfg.stream()
                .map(i -> ((JSONObject) i).getString("columncode"))
                .collect(Collectors.toSet());

        if (!colunmnCodeSet.contains("code")) {
            return Json.getJsonResult("请选择 学号/工号 对应列");
        }
        if (!colunmnCodeSet.contains("idcard")) {
            return Json.getJsonResult("请选择 身份证 对应列");
        }


        // List< 列头code，Excel单元格 >
        List<Row> rows = new ArrayList<>(tableArr.size());

        // Map<列code, 所在列当前for行的 单元格>
        Map<String, Cell> columnCellMap = new HashMap<>();
        for (int i = 0; i < tableArr.size(); i++) {
            JSONObject excelRow = (JSONObject) tableArr.get(i);
            int finalI = i;

            Row row = new Row();
            columnCfg.forEach(_cfg -> {
                JSONObject cfg = (JSONObject) _cfg;
                // 列头名
                String columnName = cfg.getString("column");
                String val = excelRow.getString(columnName);
                String code = cfg.getString("columncode");
                Cell cell = new Cell(finalI)
                        .setCode(code)
                        .setName(columnName)
                        .setVal(val);
                row.putCell(code, cell);

                // 以列方向连接单元格，类似于单链表形式
                // 当前单元格所在列的 前一行单元格
                Cell preCell = columnCellMap.get(code);
                if (preCell == null) {
                    columnCellMap.put(code, cell);
                    return;
                }
                preCell.setNextColumnCell(cell);
            });
            rows.add(row);
        }

        Map<String, CellsValidator> validatorMap = new LinkedHashMap<>();

        CellsValidator codeValidator = new CellsValidator() {

            // Map< 学工号, cell > 合法的 学号/工号
            final Map<String, Row> codeRowMap = new HashMap<>();

            // 已经存在的 学号/工号
            final Set<String> presentCodes = new HashSet<>();

            @Override
            public boolean valid(Row row, Cell cell) {
                String val = cell.getVal();
                if (StrUtil.isBlank(val)) {
                    row.addError("学号/工号 不能为空;");
                    return false;
                }
                // 验证学工号是否重复
                // 学工号相同的单元格
                if (presentCodes.contains(val)) {
                    Row presentRow = codeRowMap.get(val);
                    if (presentRow != null) {
                        presentRow.addError("学号/工号 重复;");
                        codeRowMap.remove(val);
                    }
                    row.addError("学号/工号 重复;");
                    return false;
                }
                presentCodes.add(val);
                codeRowMap.put(val, row);
                return true;
            }

            @Override
            public boolean then() {
                StringJoiner joiner = new StringJoiner(",", "(", ")");
                // Map< 学工号, 学工号在数据库中的 单元格>
                for (String code : codeRowMap.keySet()) {
                    joiner.add(code);
                }
                String sql = "select code from tb_card_teachstudinfo where code in " + joiner;
                // 在数据库中的 学工号
                List<String> codes = dbService.queryFields(sql, String.class);
                boolean res = true;
                for (String code : codes) {
                    codeRowMap.remove(code);
                }
                for (Row row : codeRowMap.values()) {
                    row.addError("学号/工号 在数据库中不存在");
                    res = false;
                }
                return res;
            }
        };
        validatorMap.put("code", codeValidator);

        CellsValidator idCardValidator = new CellsValidator() {

            final Set<String> ids = new HashSet<>();

            final Map<String, Row> persentIdRowMap = new HashMap<>();

            @Override
            public boolean valid(Row row, Cell cell) {
                String val = cell.getVal();
                if (StrUtil.isBlank(val)) {
                    return true;
                }
                // 验证学工号是否重复
                // 学工号相同的单元格
                if (ids.contains(val)) {
                    Row persentRow = persentIdRowMap.get(val);
                    if (persentRow != null) {
                        persentRow.addError("身份证号 重复;");
                        persentIdRowMap.remove(val);
                    }
                    row.addError("身份证号 重复;");
                    return false;
                }
                persentIdRowMap.put(val, row);
                ids.add(val);
                return true;
            }

            @Override
            public boolean then() {
                return true;
            }
        };
        validatorMap.put("idcard", idCardValidator);

        AtomicBoolean isValid = new AtomicBoolean(true);
        // 行校验
        for (Row row : rows) {
            row.forEachCell((code, cell) -> {
                CellsValidator validator = validatorMap.get(code);
                if (validator == null) {
                    return;
                }
                isValid.set(validator.valid(row, cell) && isValid.get());
            });
        }
        for (CellsValidator validator : validatorMap.values()) {
            isValid.set(validator.then() && isValid.get());
        }

        if (!isValid.get()) {
            // 不通过校验
            JSONArray cells = new JSONArray();
            // 列头行
            for (Object _cfg : columnCfg) {
                JSONObject cfg = (JSONObject) _cfg;
                String code = cfg.getString("columncode");
                JSONObject cell = new JSONObject();
                cell.put("name", code);
                cell.put("datakey", code);
                cell.put("size", 15);
                cells.add(cell);
            }
            // 错误列头
            JSONObject errColumnHead = new JSONObject();
            errColumnHead.put("name", "错误");
            errColumnHead.put("datakey", "err");
            errColumnHead.put("size", 80);
            cells.add(errColumnHead);

            JSONArray list = new JSONArray();
            for (Row row : rows) {
                JSONObject dataRow = new JSONObject();
                row.forEachCell((code, cell) -> dataRow.put(code, cell.getVal()));
                String error = row.getError() != null ? row.getError().toString() : "";
                dataRow.put("err", error);
                list.add(dataRow);
            }
            String title= "人员更新信息";
            String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
            String filename =  title+format + ".xlsx";
            filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
            return Json.getJsonResult(true, filepath);
        }

        String sql = "update tb_card_teachstudinfo " +
                "set idcard = '%s' " +
                "where code = '%s'";
        List<String> update = new ArrayList<>();
        for (Row row : rows) {
            String idCard = row.getCell("idcard").getVal();
            if (StrUtil.isBlank(idCard)) {
                continue;
            }
            String code = row.getCell("code").getVal();
            update.add(String.format(sql, idCard, code));
        }

        dbService.excuteBatchSql(update.toArray(new String[0]));
        return Json.getJsonResult(true, "");
    }

    public JsonResult updatePersonMobileInfoByExcel(String excelPath, String columnCfgStr) throws Exception {
        String filepath = String.format("%s%s", WebConfig.getUploaddir(), excelPath);
        File file = new File(filepath);
        if (!file.exists()) {
            return Json.getJsonResult("文档不存在");
        }
        InputStream stream = new FileInputStream(file);
        JSONArray tableArr = ExcelUtil.getBankListByExcel(stream, excelPath);
        if (tableArr.size() == 0) {
            return Json.getJsonResult("文档为空文件");
        }
        JSONArray columnCfg = JSONArray.parseArray(columnCfgStr);
        // 数据库中对应的 列名
        Set<String> colunmnCodeSet = columnCfg.stream()
                .map(i -> ((JSONObject) i).getString("columncode"))
                .collect(Collectors.toSet());

        if (!colunmnCodeSet.contains("code")) {
            return Json.getJsonResult("请选择 学号/工号 对应列");
        }
//        if (!colunmnCodeSet.contains("linkmobile1")) {
//            return Json.getJsonResult("请选择 紧急联系人1手机号 对应列");
//        }
//
//        if (!colunmnCodeSet.contains("linkman1")) {
//            return Json.getJsonResult("请选择 紧急联系人1姓名 对应列");
//        }
//
//        if (!colunmnCodeSet.contains("linkdes1")) {
//            return Json.getJsonResult("请选择 紧急联系人1与学生的关系 对应列");
//        }
//
//        if (!colunmnCodeSet.contains("linkmobile2")) {
//            return Json.getJsonResult("请选择 紧急联系人2手机号 对应列");
//        }
//
//        if (!colunmnCodeSet.contains("linkman2")) {
//            return Json.getJsonResult("请选择 紧急联系人2姓名 对应列");
//        }
//
//        if (!colunmnCodeSet.contains("linkdes2")) {
//            return Json.getJsonResult("请选择 紧急联系人2与学生的关系 对应列");
//        }


        // List< 列头code，Excel单元格 >
        List<Row> rows = new ArrayList<>(tableArr.size());

        // Map<列code, 所在列当前for行的 单元格>
        Map<String, Cell> columnCellMap = new HashMap<>();
        for (int i = 0; i < tableArr.size(); i++) {
            JSONObject excelRow = (JSONObject) tableArr.get(i);
            int finalI = i;

            Row row = new Row();
            columnCfg.forEach(_cfg -> {
                JSONObject cfg = (JSONObject) _cfg;
                // 列头名
                String columnName = cfg.getString("column");
                String val = excelRow.getString(columnName);
                String code = cfg.getString("columncode");
                Cell cell = new Cell(finalI)
                        .setCode(code)
                        .setName(columnName)
                        .setVal(val);
                row.putCell(code, cell);

                // 以列方向连接单元格，类似于单链表形式
                // 当前单元格所在列的 前一行单元格
                Cell preCell = columnCellMap.get(code);
                if (preCell == null) {
                    columnCellMap.put(code, cell);
                    return;
                }
                preCell.setNextColumnCell(cell);
            });
            rows.add(row);
        }

        Map<String, CellsValidator> validatorMap = new LinkedHashMap<>();

        CellsValidator codeValidator = new CellsValidator() {

            // Map< 学工号, cell > 合法的 学号/工号
            final Map<String, Row> codeRowMap = new HashMap<>();

            // 已经存在的 学号/工号
            final Set<String> presentCodes = new HashSet<>();

            @Override
            public boolean valid(Row row, Cell cell) {
                String val = cell.getVal();
                if (StrUtil.isBlank(val)) {
                    row.addError("学号/工号 不能为空;");
                    return false;
                }
                // 验证学工号是否重复
                // 学工号相同的单元格
                if (presentCodes.contains(val)) {
                    Row presentRow = codeRowMap.get(val);
                    if (presentRow != null) {
                        presentRow.addError("学号/工号 重复;");
                        codeRowMap.remove(val);
                    }
                    row.addError("学号/工号 重复;");
                    return false;
                }
                presentCodes.add(val);
                codeRowMap.put(val, row);
                return true;
            }

            @Override
            public boolean then() {
                StringJoiner joiner = new StringJoiner("','", "('", "')");
                // Map< 学工号, 学工号在数据库中的 单元格>
                for (String code : codeRowMap.keySet()) {
                    joiner.add(code);
                }
                String sql = "select code from tb_card_teachstudinfo where code in " + joiner;
                // 在数据库中的 学工号
                List<String> codes = dbService.queryFields(sql, String.class);
                boolean res = true;
                for (String code : codes) {
                    codeRowMap.remove(code);
                }
                for (Row row : codeRowMap.values()) {
                    row.addError("学号/工号 在数据库中不存在");
                    res = false;
                }
                return res;
            }
        };
        validatorMap.put("code", codeValidator);

        CellsValidator orgCodeValidator = new CellsValidator() {

            // Map< 学工号, cell > 合法的 组织架构
            final Map<String, Row> codeRowMap = new HashMap<>();

            // 不合法的 组织架构
            final Set<String> presentCodes = new HashSet<>();

            @Override
            public boolean valid(Row row, Cell cell) {
                String val = cell.getVal();
                // 验证学工号是否重复
                // 学工号相同的单元格
                presentCodes.add(val);
                codeRowMap.put(val, row);
                return true;
            }

            @Override
            public boolean then() {
                StringJoiner joiner = new StringJoiner("','", "('", "')");
                // Map< 学工号, 学工号在数据库中的 单元格>
                for (String code : codeRowMap.keySet()) {
                    joiner.add(code);
                }
                String sql = "select code from tb_card_orgframework where code in " + joiner;

                // 在数据库中的 学工号
                List<String> codes = dbService.queryFields(sql, String.class);
                boolean res = true;
                for (String code : codes) {
                    codeRowMap.remove(code);
                }
                for (Row row : codeRowMap.values()) {
                    row.addError("组织编号/名字 在数据库中不存在");
                    res = false;
                }
                return res;
            }
        };
        validatorMap.put("orgcode", orgCodeValidator);

        AtomicBoolean isValid = new AtomicBoolean(true);
        // 行校验
        for (Row row : rows) {
            row.forEachCell((code, cell) -> {
                CellsValidator validator = validatorMap.get(code);
                if (validator == null) {
                    return;
                }
                isValid.set(validator.valid(row, cell) && isValid.get());
            });
        }
        for (CellsValidator validator : validatorMap.values()) {
            isValid.set(validator.then() && isValid.get());
        }

        if (!isValid.get()) {
            // 不通过校验
            JSONArray cells = new JSONArray();
            // 列头行
            for (Object _cfg : columnCfg) {
                JSONObject cfg = (JSONObject) _cfg;
                String code = cfg.getString("columncode");
                JSONObject cell = new JSONObject();
                cell.put("name", code);
                cell.put("datakey", code);
                cell.put("size", 15);
                cells.add(cell);
            }
            // 错误列头
            JSONObject errColumnHead = new JSONObject();
            errColumnHead.put("name", "错误");
            errColumnHead.put("datakey", "err");
            errColumnHead.put("size", 80);
            cells.add(errColumnHead);

            JSONArray list = new JSONArray();
            for (Row row : rows) {
                JSONObject dataRow = new JSONObject();
                row.forEachCell((code, cell) -> dataRow.put(code, cell.getVal()));
                String error = row.getError() != null ? row.getError().toString() : "";
                dataRow.put("err", error);
                list.add(dataRow);
            }

            String title= "人员更新信息";
            String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
            String filename =  title+format + ".xlsx";
            filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
            return Json.getJsonResult(true, filepath);
        }

        String baseSql = "update tb_card_teachstudinfo set ";
        for (Row row : rows) {
            StringBuilder sql = new StringBuilder(baseSql);
            List<Object> params = new ArrayList<>();

            Cell name = row.getCell("name");
            Cell orgCode = row.getCell("orgcode");
            Cell property = row.getCell("property");
            Cell linkmobile1 = row.getCell("linkmobile1");
            Cell linkmobile2 = row.getCell("linkmobile2");
            Cell linkdes1 = row.getCell("linkdes1");
            Cell linkdes2 = row.getCell("linkdes2");
            Cell linkman1 = row.getCell("linkman1");
            Cell linkman2 = row.getCell("linkman2");
            Cell code = row.getCell("code");

            if (linkmobile1 != null && StrUtil.isNotBlank(linkmobile1.getVal())) {
                sql.append("linkmobile1 = ?, ");
                params.add(linkmobile1.getVal());
            }
            if (name != null && StrUtil.isNotBlank(name.getVal())) {
                sql.append("name = ?, ");
                params.add(name.getVal());
            }
            if (property != null && StrUtil.isNotBlank(property.getVal())) {
                sql.append("property = ?, ");
                params.add(property.getVal());
            }
            if (orgCode != null && StrUtil.isNotBlank(orgCode.getVal())) {
                sql.append("orgcode = ?, ");
                params.add(orgCode.getVal());
            }
            if (linkmobile2 != null && StrUtil.isNotBlank(linkmobile2.getVal())) {
                sql.append("linkmobile2 = ?, ");
                params.add(linkmobile2.getVal());
            }
            if (linkdes1 != null && StrUtil.isNotBlank(linkdes1.getVal())) {
                sql.append("linkdes1 = ?, ");
                params.add(linkdes1.getVal());
            }
            if (linkdes2 != null && StrUtil.isNotBlank(linkdes2.getVal())) {
                sql.append("linkdes2 = ?, ");
                params.add(linkdes2.getVal());
            }
            if (linkman1 != null && StrUtil.isNotBlank(linkman1.getVal())) {
                sql.append("linkman1 = ?, ");
                params.add(linkman1.getVal());
            }
            if (linkman2 != null && StrUtil.isNotBlank(linkman2.getVal())) {
                sql.append("linkman2 = ?, ");
                params.add(linkman2.getVal());
            }

            if (code != null && StrUtil.isNotBlank(code.getVal())) {
                // 移除最后的 ", "
                if (params.size() > 0) {
                    sql.setLength(sql.length() - 2);
                    sql.append(" where code = ?");
                    params.add(code.getVal());
                }
            }
            jdbcTemplate.update(sql.toString(), params.toArray(new Object[0]));
        }

        return Json.getJsonResult(true, "");
    }
    public JsonResult importParentsByExcel(HttpServletRequest request, HttpServletResponse response,String excelPath, String columnCfgStr) throws Exception {
        String filepath = String.format("%s%s", WebConfig.getUploaddir(), excelPath);
        File file = new File(filepath);
        if (!file.exists()) {
            return Json.getJsonResult("文档不存在");
        }
        InputStream stream = new FileInputStream(file);
        JSONArray tableArr = ExcelUtil.getBankListByExcel(stream, excelPath);
        if (tableArr.size() == 0) {
            return Json.getJsonResult("文档为空文件");
        }
        JSONArray columnCfg = JSONArray.parseArray(columnCfgStr);
        // 数据库中对应的 列名
        Set<String> colunmnCodeSet = columnCfg.stream()
                .map(i -> ((JSONObject) i).getString("columncode"))
                .collect(Collectors.toSet());

        if (!colunmnCodeSet.contains("code")) {
            return Json.getJsonResult("请选择 学号/工号 对应列");
        }
        if (!colunmnCodeSet.contains("linkmobile")) {
            return Json.getJsonResult("请选择 紧急联系人手机号 对应列");
        }

        if (!colunmnCodeSet.contains("linkman")) {
            return Json.getJsonResult("请选择 紧急联系人姓名 对应列");
        }

        if (!colunmnCodeSet.contains("linkdes")) {
            return Json.getJsonResult("请选择 紧急联系人与学生的关系 对应列");
        }


        // List< 列头code，Excel单元格 >
        List<Row> rows = new ArrayList<>(tableArr.size());

        // Map<列code, 所在列当前for行的 单元格>
        Map<String, Cell> columnCellMap = new HashMap<>();
        for (int i = 0; i < tableArr.size(); i++) {
            JSONObject excelRow = (JSONObject) tableArr.get(i);
            int finalI = i;

            Row row = new Row();
            columnCfg.forEach(_cfg -> {
                JSONObject cfg = (JSONObject) _cfg;
                // 列头名
                String columnName = cfg.getString("column");
                String val = excelRow.getString(columnName);
                String code = cfg.getString("columncode");
                Cell cell = new Cell(finalI)
                        .setCode(code)
                        .setName(columnName)
                        .setVal(val);
                row.putCell(code, cell);

                // 以列方向连接单元格，类似于单链表形式
                // 当前单元格所在列的 前一行单元格
                Cell preCell = columnCellMap.get(code);
                if (preCell == null) {
                    columnCellMap.put(code, cell);
                    return;
                }
                preCell.setNextColumnCell(cell);
            });
            rows.add(row);
        }

        Map<String, CellsValidator> validatorMap = new LinkedHashMap<>();

        CellsValidator codeValidator = new CellsValidator() {

            // Map< 学工号, cell > 合法的 学号/工号
            final Map<String, Row> codeRowMap = new HashMap<>();

            // 已经存在的 学号/工号
            final Set<String> presentCodes = new HashSet<>();

            @Override
            public boolean valid(Row row, Cell cell) {
                String val = cell.getVal();
                if (StrUtil.isBlank(val)) {
                    row.addError("学号/工号 不能为空;");
                    return false;
                }
                presentCodes.add(val);
                codeRowMap.put(val, row);
                return true;
            }

            @Override
            public boolean then() {
                StringJoiner joiner = new StringJoiner(",", "(", ")");
                // Map< 学工号, 学工号在数据库中的 单元格>
                for (String code : codeRowMap.keySet()) {
                    joiner.add(code);
                }
                String sql = "select code from tb_card_teachstudinfo where code in " + joiner;
                // 在数据库中的 学工号
                List<String> codes = dbService.queryFields(sql, String.class);
                boolean res = true;
                for (String code : codes) {
                    codeRowMap.remove(code);
                }
                for (Row row : codeRowMap.values()) {
                    row.addError("学号/工号 在数据库中不存在");
                    res = false;
                }
                return res;
            }
        };
        validatorMap.put("code", codeValidator);

        if (colunmnCodeSet.contains("card")) {
            CellsValidator cardValidator = new CellsValidator() {

                // Map< 学工号, cell > 合法的 卡号
                final Map<String, Row> cardRowMap = new HashMap<>();

                // 已经存在的 学号/工号
                final Set<String> presentCards = new HashSet<>();

                @Override
                public boolean valid(Row row, Cell cell) {
                    String val = cell.getVal();
                    presentCards.add(val);
                    cardRowMap.put(val, row);
                    return true;
                }

                @Override
                public boolean then() {
                    StringJoiner joiner = new StringJoiner(",", "(", ")");
                    // Map< 学工号, 学工号在数据库中的 单元格>
                    for (String code : cardRowMap.keySet()) {
                        joiner.add(code);
                    }
                    String sql = "select cardno from tb_card_cardinfo where status !=0 and cardno in "+joiner;
                    // 在数据库中的 卡号
                    List<String> cards = dbService.queryFields(sql, String.class);
                    boolean res = true;
                    for (String card : cards) {
                        if (cardRowMap.get(card)!=null){
                            cardRowMap.get(card).addError("卡号已存在不能发卡");
                            res = false;
                        }
                    }
                    return res;
                }
            };
            validatorMap.put("card", cardValidator);
        }
        AtomicBoolean isValid = new AtomicBoolean(true);
        // 行校验
        for (Row row : rows) {
            row.forEachCell((code, cell) -> {
                CellsValidator validator = validatorMap.get(code);
                if (validator == null) {
                    return;
                }
                isValid.set(validator.valid(row, cell) && isValid.get());
            });
        }
        for (CellsValidator validator : validatorMap.values()) {
            isValid.set(validator.then() && isValid.get());
        }

        if (!isValid.get()) {
            // 不通过校验
            JSONArray cells = new JSONArray();
            // 列头行
            for (Object _cfg : columnCfg) {
                JSONObject cfg = (JSONObject) _cfg;
                String code = cfg.getString("columncode");
                JSONObject cell = new JSONObject();
                cell.put("name", code);
                cell.put("datakey", code);
                cell.put("size", 15);
                cells.add(cell);
            }
            // 错误列头
            JSONObject errColumnHead = new JSONObject();
            errColumnHead.put("name", "错误");
            errColumnHead.put("datakey", "err");
            errColumnHead.put("size", 80);
            cells.add(errColumnHead);

            JSONArray list = new JSONArray();
            for (Row row : rows) {
                JSONObject dataRow = new JSONObject();
                row.forEachCell((code, cell) -> dataRow.put(code, cell.getVal()));
                String error = row.getError() != null ? row.getError().toString() : "";
                dataRow.put("err", error);
                list.add(dataRow);
            }

            String title= "家长导入信息";
            String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
            String filename =  title+format + ".xlsx";
            filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
            return Json.getJsonResult(true, filepath);
        }
        SysUser sysuser = userredis.get(request);
        final String userid = sysuser.getUid();
        String creator = sysuser.getName();
        String sql = "INSERT INTO tb_card_teachstudinfo (uid,code, name,mobile,card,cardsn, linkdes1,linkid,infotype,createdate,status,orgcode) VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s','%s', 3, now(), 1,'%s')";
        List<String> insert = new ArrayList<>();
        final StringBuffer cardsql = new StringBuffer();
        final StringBuffer cardopsql = new StringBuffer();
        for (Row row : rows) {
            String linkmobile = row.getCell("linkmobile").getVal();

            String linkdes = row.getCell("linkdes").getVal();

            String linkman = row.getCell("linkman").getVal();

            String name = row.getCell("name").getVal();
            String card = row.getCell("card").getVal();
            if (StringUtils.isNotEmpty(card)){
                card =  ComHelper.LeftPad(card, 12, '0');
            }else {
                String autoCard = sysconfig.get("autoCard");
                if (StringUtils.isNotEmpty(autoCard)){
                    if ("1".equals(autoCard)){
                        boolean flag = true;
                        String nowcard = "";
                        while (flag){
                            nowcard = generateRandomNumberString(8);
                            nowcard = ComHelper.LeftPad(nowcard, 12, '0');
                            flag = isCard(nowcard);
                        }
                        card = nowcard;
                    }
                }
                card = "";
            }

            if (StrUtil.isBlank(linkmobile) || StrUtil.isBlank(linkdes) || StrUtil.isBlank(linkman)) {
                continue;
            }
            String code = row.getCell("code").getVal();
            Random random = new Random();
            JSONObject jsonObject = dbService.QueryJSONObject("SELECT uid,orgcode FROM tb_card_teachstudinfo WHERE code = '" + code + "' and name = '" + name + "'");
            int randomNumber = random.nextInt(90) + 10; // 生成10到99之间的随机数
            code = code + String.valueOf(randomNumber);
            String uid = jsonObject.getString("uid");
            String puid = UUID.randomUUID().toString();
            String orgCode = jsonObject.getString("orgcode");
            JSONObject object = dbService.QueryJSONObject("select uid from tb_card_teachstudinfo where infotype=3 and mobile='" + linkmobile + "' limit 1");
            if (object!=null){
                puid = object.getString("uid");
            }else {
                insert.add(String.format(sql,puid,code,linkman, linkmobile,card,card,linkdes,uid,orgCode));
            }
            dbService.excuteSql("INSERT INTO tb_card_parentinfo (parent_id,student_id,create_dt)  VALUES (?, ?, now())",puid,uid);

            if (!StringUtils.isBlank(card)) {
                String cardid = UUID.randomUUID().toString();
                if (cardsql.length() > 0) {
                    cardsql.append(",");
                }
                cardsql.append("('" + cardid + "','" + uid + "','本人', '" + card + "', '" + card + "',0,1,0,0,0,0,'2050-01-01',now(),'" + userid + "',now(),'" + userid + "',1)");
                if (cardopsql.length() > 0) {
                    cardopsql.append(",");
                }
                cardopsql.append("(uuid(),'" + uid + "','" + cardid + "','" + card + "','" + card + "',0,1,'本人','" + creator + "','0','导入卡号',now(),'" + userid + "')");
            }
        }
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {

                if (cardsql.length() > 0) {
                    String str = "insert into tb_card_cardinfo(uid,infoid,usedes,cardno,cardsn,deposit,ismain,balance,vicewallet,waterwallet,timescount,enddate,createdate,creatorid,modifydate,modifierid,status) values";
                    cardsql.insert(0, str);
                    dbService.excuteSql(cardsql.toString());

                    if (cardopsql.length() > 0) {
                        str = "insert into tb_card_cardinfo_oprecord(uid,infoid,cardid,cardno,cardsn,dotype,ismain,usedes,creator,infocode,des,createdate,creatorid) values";
                        cardopsql.insert(0, str);
                        PreparedStatement ps = connection.prepareStatement(cardopsql.toString());
                        ps.executeUpdate();
                        ps.close();

                        String dopsql = "update tb_card_cardinfo_oprecord op inner join tb_card_teachstudinfo info on info.uid=op.infoid set op.infocode=info.code,op.infoname=info.name,op.orgname=getorgname(info.orgcode) where op.infocode='0' ";
                        ps = connection.prepareStatement(dopsql.toString());
                        ps.executeUpdate();
                        ps.close();
                    }
                }


            }
        });
        syslogs.Write(request, "家长信息", "导入家长档案【" +excelPath + "】");
        dbService.excuteBatchSql(insert.toArray(new String[0]));
        return Json.getJsonResult(true, "");
    }

    public JsonData getPerfectStudentList(HttpServletRequest request, HttpServletResponse response, String orgcode, String key,
                                          boolean viewchild, String viewleave, String infolabel, String property, int start, int limit) {
        String fields = "ct.uid,code,name,sex,card,date_format(startdate,'%Y-%m-%d') as startdate,date_format(enddate,'%Y-%m-%d') as enddate," +
                "cardsn,mobile,orgcode,getorgname(orgcode) as orgname,nation,birthday,ct.address infoAddress,idcard,infotype,intoyear,linkman1," +
                "ctd.address,link1_employer as link1Employer," +
                "link2_employer as link2Employer,is_local as isLocal,local_address as localAddress,registration_num as registrationNum,registration_code as registrationCode," +
                "registered," +
                "linkmobile1,linkdes1,linkman2,linkmobile2,linkdes2,focus,status,infolabel,property,ctd.grade,ctd.schoolnumber,ctd.oldschool,ctd.isTransfer,ctd.transferreason,ctd.transfertype ";
        String table = "tb_card_teachstudinfo ct join tb_card_teachstudinfo_detail ctd on ct.uid = ctd.uid ";
        String where = "infotype=2";

        if (StringUtils.isNotBlank(viewleave) && !viewleave.equals("-1")) {
            where += " and status=" + viewleave + " ";
        }
        if ("x".equals(orgcode)){
            orgcode=null;
        }
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and orgcode like '" + orgcode + "%' ";
            } else {
                where += " and orgcode='" + orgcode + "' ";
            }
        }
        if (StringUtils.isNotBlank(property) && !"-1".equals(property)) {
            where += " and property=" + property + " ";
        }
        if (StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)) {
            where += " and infolabel = '" + infolabel + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            String cardno = ComHelper.LeftPad(key, 12, '0');
            where += " and (code='" + key + "' or name like '%" + key + "%' or mobile like '%" + key + "%' or  card='" + cardno + "' or cardsn='" + cardno + "' )";
        }
        String orderby = ExtSort.Orderby(request, "createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData getReplenishStudentList(HttpServletRequest request, HttpServletResponse response,  String key,String orgcode, int start, int limit) {
        String fields = "*";
        String table = "tb_card_teachstudinfo_collect";
        String where = "1=1 and orgcode = '"+orgcode+"'";
        if (!StringUtils.isBlank(key)) {
            where += " and name='" + key + "'";
        }
        String orderby = ExtSort.Orderby(request, "create_time desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public String ExportReplenishStudent(HttpServletRequest request, HttpServletResponse response,String orgcode) throws IOException {
        String SQL = "SELECT * FROM tb_card_teachstudinfo_collect where orgcode = '"+orgcode+"'";
        JsonData result = dbService.QueryJsonData(SQL);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "学生姓名");
        cell.put("datakey", "name");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "学生性别");
        cell.put("datakey", "sex");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "户籍地址");
        cell.put("datakey", "registered");
        cell.put("size", 35);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "现居住地址");
        cell.put("datakey", "nowaddress");
        cell.put("size", 35);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "监护人姓名");
        cell.put("datakey", "linkman1");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "监护人联系方式");
        cell.put("datakey", "linkmobile1");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "与学生关系");
        cell.put("datakey", "link");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "现就读学校");
        cell.put("datakey", "schoolname");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "补充说明");
        cell.put("datakey", "remark");
        cell.put("size", 30);
        cells.add(cell);

        String title= "学生信息详情";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
        return filepath;
    }

    public String ExportPerfectStudent(HttpServletRequest request, HttpServletResponse response, String orgcode, boolean viewchild) throws IOException {
        String SQL = "SELECT ct.name,(case  when ct.sex=1 then '男' when ct.sex=2 then '女' end) sex," +
                "ct.linkman1,ct.linkmobile1,ct.linkdes1,ct.linkman2," +
                "ct.linkmobile2,ct.linkdes2," +
                "ctd.address,link1_employer as link1Employer," +
                "link2_employer as link2Employer,is_local as isLocal,local_address as localAddress,registration_num as registrationNum,registration_code as registrationCode," +
                "registered,ctd.grade,ctd.schoolnumber,ctd.oldschool,ctd.isTransfer,ctd.transferreason,ctd.transfertype " +
                "FROM tb_card_teachstudinfo ct " +
                "join tb_card_teachstudinfo_detail ctd on ct.uid = ctd.uid " +
                "WHERE infotype=2";
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                SQL += " AND ct.orgcode like '" + orgcode + "%' ";
            } else {
                SQL += " AND ct.orgcode='" + orgcode + "' ";
            }
        }
        JsonData result = dbService.QueryJsonData(SQL);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        for (int i = 0; i < list.size(); i++) {
            if ("1".equals(list.getJSONObject(i).getString("grade"))){
                list.getJSONObject(i).put("grade","初一");
            }else if ("2".equals(list.getJSONObject(i).getString("grade"))){
                list.getJSONObject(i).put("grade","初二");
            }else if ("3".equals(list.getJSONObject(i).getString("grade"))){
                list.getJSONObject(i).put("grade","初三");
            }

            if ("1".equals(list.getJSONObject(i).getString("isTransfer"))){
                list.getJSONObject(i).put("isTransfer","是");
            }else if ("2".equals(list.getJSONObject(i).getString("isTransfer"))){
                list.getJSONObject(i).put("isTransfer","否");
            }

            if ("2".equals(list.getJSONObject(i).getString("isLocal"))){
                list.getJSONObject(i).put("isLocal","是");
            }else if ("1".equals(list.getJSONObject(i).getString("isLocal"))){
                list.getJSONObject(i).put("isLocal","否");
            }

            if ("1".equals(list.getJSONObject(i).getString("transfertype"))){
                list.getJSONObject(i).put("transfertype","市内");
            }else if ("2".equals(list.getJSONObject(i).getString("transfertype"))){
                list.getJSONObject(i).put("transfertype","本省外市");
            }else if ("3".equals(list.getJSONObject(i).getString("transfertype"))){
                list.getJSONObject(i).put("transfertype","外省");
            }
        }
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "学生姓名");
        cell.put("datakey", "name");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "学生性别");
        cell.put("datakey", "sex");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "学生报名号");
        cell.put("datakey", "registrationNum");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "学生注册码");
        cell.put("datakey", "registrationCode");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "入读年级");
        cell.put("datakey", "grade");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "学籍号");
        cell.put("datakey", "schoolnumber");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "原就读学校名称");
        cell.put("datakey", "oldschool");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "现居住地址");
        cell.put("datakey", "address");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "户籍地址");
        cell.put("datakey", "registered");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "是否转学");
        cell.put("datakey", "isTransfer");
        cell.put("size", 8);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "转学原因");
        cell.put("datakey", "transferreason");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "转学类型");
        cell.put("datakey", "transfertype");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "监护人1姓名");
        cell.put("datakey", "linkman1");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "监护人1手机号");
        cell.put("datakey", "linkmobile1");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "与学生关系");
        cell.put("datakey", "linkdes1");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "工作单位");
        cell.put("datakey", "link1Employer");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "监护人2姓名");
        cell.put("datakey", "linkman2");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "监护人2手机号");
        cell.put("datakey", "linkmobile2");
        cell.put("size", 30);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "与学生关系");
        cell.put("datakey", "linkdes2");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "工作单位");
        cell.put("datakey", "link2Employer");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "是否为当前区域户籍");
        cell.put("datakey", "isLocal");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "户口所在社区");
        cell.put("datakey", "localAddress");
        cell.put("size", 15);
        cells.add(cell);

        String title= "学生报名信息详情";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
        return filepath;
    }

    public JsonResult addParentInfo(HttpServletRequest request, HttpServletResponse response,String name,String pid,String linkman,String linkmobile,String linkdes) throws IOException {

        if (StringUtils.isEmpty(pid)) {
            return Json.getJsonResult(false, "请选择需要添加家长信息人员");
        }
        if (StringUtils.isEmpty(linkman)) {
            return Json.getJsonResult(false, "请填写姓名");
        }
        if (StringUtils.isEmpty(linkmobile)) {
            return Json.getJsonResult(false, "请填写手机号");
        }
        if (StringUtils.isEmpty(linkdes)) {
            return Json.getJsonResult(false, "请填写关系");
        }
        String uuid = UUID.randomUUID().toString();
        JSONObject jsonObject = dbService.QueryJSONObject("SELECT `code`,orgcode FROM tb_card_teachstudinfo WHERE uid = '" + pid + "'");
        String code = jsonObject.getString("code");
        String orgCode = jsonObject.getString("orgcode");
        Random random = new Random();
        int randomNumber = random.nextInt(90) + 10; // 生成10到99之间的随机数
        code = code + String.valueOf(randomNumber);

        JSONObject object = dbService.QueryJSONObject("select uid from tb_card_teachstudinfo where infotype=3 and mobile='" + linkmobile + "' limit 1");
        if (object!=null){
            dbService.excuteSql("INSERT INTO tb_card_parentinfo (parent_id,student_id,create_dt)  VALUES (?, ?, now())",object.getString("uid"),pid);
        }else {
            String insertSQL = "INSERT INTO tb_card_teachstudinfo (uid,code, name,mobile, linkdes1,linkid,infotype,createdate,status,orgcode) VALUES (?, ?, ?, ?, ?, ?, ?,now(),?,?)";
            dbService.excuteSql(insertSQL, uuid,code, linkman, linkmobile, linkdes, pid, 3, 1,orgCode);
            dbService.excuteSql("INSERT INTO tb_card_parentinfo (parent_id,student_id,create_dt)  VALUES (?, ?, now())",uuid,pid);
            syslogs.Write(request, "新增家长信息", String.format("新增:家长信息姓名:%s", linkman));
        }
        return Json.getJsonResult(true);
    }

    public JsonResult editParentInfo(HttpServletRequest request, HttpServletResponse response,String pname,String pid,String mobile,String linkdes1) throws IOException {

        String updateSQL = "UPDATE tb_card_teachstudinfo SET name=?, mobile = ?,linkdes1=? WHERE uid=?";
        dbService.excuteSql(updateSQL,pname,mobile,linkdes1,pid);

        return Json.getJsonResult(true);
    }

    public R<?> exportStudentInfo(String uids) {
        List<File> fileList = new ArrayList<>();
        for (String uid : uids.split(",")) {
            // 每次循环重新获取模板文件流
            InputStream templateStream = this.getClass().getResourceAsStream("/templates/StudentInfoForm.xlsx");

            // 获取员工工资数据
            StudentRecordExport studentRecordEntities = getStudentRecordEntities(uid);
            String name = studentRecordEntities.getName();
            String title= name + "学生学籍信息表";
            String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
            // 输出文件路径
            String filename =  title + format + ".xlsx";
            String filePath = "temp/" + filename;
            String outFilePath = WebConfig.getUploaddir() + filePath;
            File file = new File(outFilePath);

            // 创建 ExcelWriter 实例
            ExcelWriter writer = EasyExcel
                    // 写入到
                    .write(outFilePath)
                    // 指定模板
                    .withTemplate(templateStream)
                    .build();

            WriteSheet sheet = EasyExcel.writerSheet().build();

            // 执行填充操作
            writer.fill(studentRecordEntities, sheet);

            // 结束
            writer.finish();
            fileList.add(file);

            // 关闭模板文件流
            try {
                templateStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        String zippath = null;
        try {
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            zippath = "temp/" + timestamp.getTime() + ".zip";
            File zipFile = new File(WebConfig.getUploaddir() + zippath);
            zipFile.createNewFile();
            ZipUtils.toZip(fileList, WebConfig.getUploaddir() + zippath);
            for (File file : fileList) {
                file.delete();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return R.ok("/" + zippath);
    }

    private StudentRecordExport getStudentRecordEntities(String uid) {
        String sql = "select code, name, address, " +
                "CASE WHEN sex = '1' THEN '男生' " +
                "WHEN sex = '2' THEN '女生' " +
                "ELSE '未知' END as sex, " +
                "getminiorgname(orgcode) className,enddate," +
                "startdate, birthday, mobile, linkman1, linkmobile1, linkdes1, " +
                "linkman2, linkmobile2, linkdes2, linkwork1, linkwork2, graduatego, " +
                "diplomanumber, recruitername, major2, oldteacher, major1, teachername, " +
                "nowteacher, major, imgpath " +
                "from tb_card_teachstudinfo ct " +
                "left join tb_face_infoface fi on ct.uid = fi.infoid " +
                "where ct.uid = '" + uid + "'";

        TeachStudInfoDTO studInfoDTO = dbService.queryForOne(sql, TeachStudInfoDTO.class);
        String moneySql = "SELECT uid, infoid, year, xmoney, zsmoney, jcmoney, bxmoney, tjmoney, fzmoney, clmoney, shmoney, yjmoney, " +
                "sxmoney, dzmoney, zgzmoney, qfmoney, createdate, creatorid, modifydate, modifierid,totalpayment " +
                "FROM tb_card_teachstudinfo_money WHERE infoid = '"+uid+"' order by year ";
        List<StudentFinanceRecord> records = dbService.queryList(moneySql, StudentFinanceRecord.class);
        StudentRecordExport student = new StudentRecordExport();
        int size = records.size();
        if (size == 1) {
            StudentFinanceRecord financeRecord = records.get(0);
            student.setAcademicYear1(financeRecord.getYear());
            student.setTuitionFee1(financeRecord.getXmoney());
            student.setAccommodationFee1(financeRecord.getZsmoney());
            student.setTextbookFee1(financeRecord.getJcmoney());
            student.setInsurancePremium1(financeRecord.getBxmoney());
            student.setMedicalExaminationFee1(financeRecord.getTjmoney());
            student.setClothingCost1(financeRecord.getFzmoney());
            student.setMaterialCost1(financeRecord.getClmoney());
            student.setDailyNecessities1(financeRecord.getShmoney());
            student.setDeposit1(financeRecord.getYjmoney());
            student.setQualificationCertificate1(financeRecord.getZgzmoney());
            student.setDebtSituation1(financeRecord.getQfmoney());
            student.setTotalPayment1(financeRecord.getTotalpayment());
            student.setCollegeFee1(financeRecord.getDzmoney());
            student.setIncreaseTuitionFee1(financeRecord.getSxmoney());

        }else if (size==2){
            StudentFinanceRecord financeRecord = records.get(0);
            student.setAcademicYear1(financeRecord.getYear());
            student.setTuitionFee1(financeRecord.getXmoney());
            student.setAccommodationFee1(financeRecord.getZsmoney());
            student.setTextbookFee1(financeRecord.getJcmoney());
            student.setInsurancePremium1(financeRecord.getBxmoney());
            student.setMedicalExaminationFee1(financeRecord.getTjmoney());
            student.setClothingCost1(financeRecord.getFzmoney());
            student.setMaterialCost1(financeRecord.getClmoney());
            student.setDailyNecessities1(financeRecord.getShmoney());
            student.setDeposit1(financeRecord.getYjmoney());
            student.setQualificationCertificate1(financeRecord.getZgzmoney());
            student.setDebtSituation1(financeRecord.getQfmoney());
            student.setTotalPayment1(financeRecord.getTotalpayment());
            student.setCollegeFee1(financeRecord.getDzmoney());
            student.setIncreaseTuitionFee1(financeRecord.getSxmoney());

            financeRecord = records.get(1);
            student.setAcademicYear2(financeRecord.getYear());
            student.setTuitionFee2(financeRecord.getXmoney());
            student.setAccommodationFee2(financeRecord.getZsmoney());
            student.setTextbookFee2(financeRecord.getJcmoney());
            student.setInsurancePremium2(financeRecord.getBxmoney());
            student.setMedicalExaminationFee2(financeRecord.getTjmoney());
            student.setClothingCost2(financeRecord.getFzmoney());
            student.setMaterialCost2(financeRecord.getClmoney());
            student.setDailyNecessities2(financeRecord.getShmoney());
            student.setDeposit2(financeRecord.getYjmoney());
            student.setQualificationCertificate2(financeRecord.getZgzmoney());
            student.setDebtSituation2(financeRecord.getQfmoney());
            student.setTotalPayment2(financeRecord.getTotalpayment());
            student.setCollegeFee2(financeRecord.getDzmoney());
            student.setIncreaseTuitionFee2(financeRecord.getSxmoney());

        } else if (size == 3) {
            StudentFinanceRecord financeRecord = records.get(0);
            student.setAcademicYear1(financeRecord.getYear());
            student.setTuitionFee1(financeRecord.getXmoney());
            student.setAccommodationFee1(financeRecord.getZsmoney());
            student.setTextbookFee1(financeRecord.getJcmoney());
            student.setInsurancePremium1(financeRecord.getBxmoney());
            student.setMedicalExaminationFee1(financeRecord.getTjmoney());
            student.setClothingCost1(financeRecord.getFzmoney());
            student.setMaterialCost1(financeRecord.getClmoney());
            student.setDailyNecessities1(financeRecord.getShmoney());
            student.setDeposit1(financeRecord.getYjmoney());
            student.setQualificationCertificate1(financeRecord.getZgzmoney());
            student.setDebtSituation1(financeRecord.getQfmoney());
            student.setTotalPayment1(financeRecord.getTotalpayment());
            student.setCollegeFee1(financeRecord.getDzmoney());
            student.setIncreaseTuitionFee1(financeRecord.getSxmoney());

            financeRecord = records.get(1);
            student.setAcademicYear2(financeRecord.getYear());
            student.setTuitionFee2(financeRecord.getXmoney());
            student.setAccommodationFee2(financeRecord.getZsmoney());
            student.setTextbookFee2(financeRecord.getJcmoney());
            student.setInsurancePremium2(financeRecord.getBxmoney());
            student.setMedicalExaminationFee2(financeRecord.getTjmoney());
            student.setClothingCost2(financeRecord.getFzmoney());
            student.setMaterialCost2(financeRecord.getClmoney());
            student.setDailyNecessities2(financeRecord.getShmoney());
            student.setDeposit2(financeRecord.getYjmoney());
            student.setQualificationCertificate2(financeRecord.getZgzmoney());
            student.setDebtSituation2(financeRecord.getQfmoney());
            student.setTotalPayment2(financeRecord.getTotalpayment());
            student.setCollegeFee2(financeRecord.getDzmoney());
            student.setIncreaseTuitionFee2(financeRecord.getSxmoney());

            financeRecord = records.get(2);
            student.setAcademicYear3(financeRecord.getYear());
            student.setTuitionFee3(financeRecord.getXmoney());
            student.setAccommodationFee3(financeRecord.getZsmoney());
            student.setTextbookFee3(financeRecord.getJcmoney());
            student.setInsurancePremium3(financeRecord.getBxmoney());
            student.setMedicalExaminationFee3(financeRecord.getTjmoney());
            student.setClothingCost3(financeRecord.getFzmoney());
            student.setMaterialCost3(financeRecord.getClmoney());
            student.setDailyNecessities3(financeRecord.getShmoney());
            student.setDeposit3(financeRecord.getYjmoney());
            student.setQualificationCertificate3(financeRecord.getZgzmoney());
            student.setDebtSituation3(financeRecord.getQfmoney());
            student.setTotalPayment3(financeRecord.getTotalpayment());
            student.setCollegeFee3(financeRecord.getDzmoney());
            student.setIncreaseTuitionFee3(financeRecord.getSxmoney());

        }
        student.setStudentId(studInfoDTO.getCode());
        student.setSex(studInfoDTO.getSex());
        student.setEnrollmentTime(studInfoDTO.getStartdate());
        if (studInfoDTO.getImgpath() != null) {
            student.setPhoto(WebConfig.getUploaddir() + studInfoDTO.getImgpath());
        }
        student.setOriginalTeach(studInfoDTO.getOldteacher());
        student.setTeach(studInfoDTO.getNowteacher());
        student.setAdmissionsTeacher(studInfoDTO.getRecruitername());
        student.setMajor(studInfoDTO.getMajor());
        student.setOriginalMajor(studInfoDTO.getMajor1());
        student.setBirth(studInfoDTO.getBirthday());
        student.setMobile(studInfoDTO.getMobile());
        student.setClassName(studInfoDTO.getClassName());
        student.setName(studInfoDTO.getName());
        student.setAddress(studInfoDTO.getAddress());
        student.setFamilyName1(studInfoDTO.getLinkman1());
        student.setFamilyName2(studInfoDTO.getLinkman2());
        student.setRelation1(studInfoDTO.getLinkdes1());
        student.setRelation2(studInfoDTO.getLinkdes2());
        student.setWorkUnit1(studInfoDTO.getLinkwork1());
        student.setWorkUnit2(studInfoDTO.getLinkwork2());
        student.setLinkMobile1(studInfoDTO.getLinkmobile1());
        student.setLinkMobile2(studInfoDTO.getLinkmobile2());
        student.setGraduationTime(studInfoDTO.getEnddate());
        student.setGoAfterGraduation(studInfoDTO.getGraduatego());
        student.setGraduationCertificateNumber(studInfoDTO.getDiplomanumber());
        student.setLinkMobile(studInfoDTO.getMobile());
        return student;
    }

    /**
     * 学生档案更新，必输入学号，除开卡号不能更新
     */
//    public JsonResult updateStudentInfoByExcel(String excelPath, String columnCfgStr) throws Exception {
//        String filepath = String.format("%s%s", WebConfig.getUploaddir(), excelPath);
//        File file = new File(filepath);
//        if (!file.exists()) {
//            return Json.getJsonResult("文档不存在");
//        }
//        InputStream stream = new FileInputStream(file);
//        JSONArray tableArr = ExcelUtil.getBankListByExcel(stream, excelPath);
//        if (tableArr.size() == 0) {
//            return Json.getJsonResult("文档为空文件");
//        }
//        JSONArray columnCfg = JSONArray.parseArray(columnCfgStr);
//        // 数据库中对应的 列名
//        Set<String> colunmnCodeSet = columnCfg.stream()
//                .map(i -> ((JSONObject) i).getString("columncode"))
//                .collect(Collectors.toSet());
//
//        if (!colunmnCodeSet.contains("code")) {
//            return Json.getJsonResult("请选择 学号/工号 对应列");
//        }
//
//
//        // List< 列头code，Excel单元格 >
//        List<Row> rows = new ArrayList<>(tableArr.size());
//
//        // Map<列code, 所在列当前for行的 单元格>
//        Map<String, Cell> columnCellMap = new HashMap<>();
//        for (int i = 0; i < tableArr.size(); i++) {
//            JSONObject excelRow = (JSONObject) tableArr.get(i);
//            int finalI = i;
//
//            Row row = new Row();
//            columnCfg.forEach(_cfg -> {
//                JSONObject cfg = (JSONObject) _cfg;
//                // 列头名
//                String columnName = cfg.getString("column");
//                String val = excelRow.getString(columnName);
//                String code = cfg.getString("columncode");
//                Cell cell = new Cell(finalI)
//                        .setCode(code)
//                        .setName(columnName)
//                        .setVal(val);
//                row.putCell(code, cell);
//
//                // 以列方向连接单元格，类似于单链表形式
//                // 当前单元格所在列的 前一行单元格
//                Cell preCell = columnCellMap.get(code);
//                if (preCell == null) {
//                    columnCellMap.put(code, cell);
//                    return;
//                }
//                preCell.setNextColumnCell(cell);
//            });
//            rows.add(row);
//        }
//
//        Map<String, CellsValidator> validatorMap = new LinkedHashMap<>();
//
//        CellsValidator codeValidator = new CellsValidator() {
//
//            // Map< 学工号, cell > 合法的 学号/工号
//            final Map<String, Row> codeRowMap = new HashMap<>();
//
//            // 已经存在的 学号/工号
//            final Set<String> presentCodes = new HashSet<>();
//
//            @Override
//            public boolean valid(Row row, Cell cell) {
//                String val = cell.getVal();
//                if (StrUtil.isBlank(val)) {
//                    row.addError("学号/工号 不能为空;");
//                    return false;
//                }
//                // 验证学工号是否重复
//                // 学工号相同的单元格
//                if (presentCodes.contains(val)) {
//                    Row presentRow = codeRowMap.get(val);
//                    if (presentRow != null) {
//                        presentRow.addError("学号/工号 重复;");
//                        codeRowMap.remove(val);
//                    }
//                    row.addError("学号/工号 重复;");
//                    return false;
//                }
//                presentCodes.add(val);
//                codeRowMap.put(val, row);
//                return true;
//            }
//
//            @Override
//            public boolean then() {
//                StringJoiner joiner = new StringJoiner(",", "(", ")");
//                // Map< 学工号, 学工号在数据库中的 单元格>
//                for (String code : codeRowMap.keySet()) {
//                    joiner.add(code);
//                }
//                String sql = "select code from tb_card_teachstudinfo where code in " + joiner;
//                // 在数据库中的 学工号
//                List<String> codes = dbService.queryFields(sql, String.class);
//                boolean res = true;
//                for (String code : codes) {
//                    codeRowMap.remove(code);
//                }
//                for (Row row : codeRowMap.values()) {
//                    row.addError("学号/工号 在数据库中不存在");
//                    res = false;
//                }
//                return res;
//            }
//        };
//        validatorMap.put("code", codeValidator);
//
//        CellsValidator idCardValidator = new CellsValidator() {
//            //合理的组织架构
//            final Set<String> ids = new HashSet<>();
//            //不合理的组织架构
//            final Map<String, Row> orgCodeRowMap = new HashMap<>();
//
//            @Override
//            public boolean valid(Row row, Cell cell) {
//                String val = cell.getVal();
//                if (StrUtil.isBlank(val)) {
//                    return true;
//                }
//                // 验证学工号是否重复
//                // 学工号相同的单元格
//                if (ids.contains(val)) {
//                    Row persentRow = persentIdRowMap.get(val);
//                    if (persentRow != null) {
//                        persentRow.addError("身份证号 重复;");
//                        persentIdRowMap.remove(val);
//                    }
//                    row.addError("身份证号 重复;");
//                    return false;
//                }
//                persentIdRowMap.put(val, row);
//                ids.add(val);
//                return true;
//            }
//
//            @Override
//            public boolean then() {
//                return true;
//            }
//        };
//        validatorMap.put("orgcode", idCardValidator);
//
//        AtomicBoolean isValid = new AtomicBoolean(true);
//        // 行校验
//        for (Row row : rows) {
//            row.forEachCell((code, cell) -> {
//                CellsValidator validator = validatorMap.get(code);
//                if (validator == null) {
//                    return;
//                }
//                isValid.set(validator.valid(row, cell) && isValid.get());
//            });
//        }
//        for (CellsValidator validator : validatorMap.values()) {
//            isValid.set(validator.then() && isValid.get());
//        }
//
//        if (!isValid.get()) {
//            // 不通过校验
//            JSONArray cells = new JSONArray();
//            // 列头行
//            for (Object _cfg : columnCfg) {
//                JSONObject cfg = (JSONObject) _cfg;
//                String code = cfg.getString("columncode");
//                JSONObject cell = new JSONObject();
//                cell.put("name", code);
//                cell.put("datakey", code);
//                cell.put("size", 15);
//                cells.add(cell);
//            }
//            // 错误列头
//            JSONObject errColumnHead = new JSONObject();
//            errColumnHead.put("name", "错误");
//            errColumnHead.put("datakey", "err");
//            errColumnHead.put("size", 80);
//            cells.add(errColumnHead);
//
//            JSONArray list = new JSONArray();
//            for (Row row : rows) {
//                JSONObject dataRow = new JSONObject();
//                row.forEachCell((code, cell) -> dataRow.put(code, cell.getVal()));
//                String error = row.getError() != null ? row.getError().toString() : "";
//                dataRow.put("err", error);
//                list.add(dataRow);
//            }
//            String title= "人员更新信息";
//            String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
//            String filename =  title+format + ".xlsx";
//            filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
//            return Json.getJsonResult(true, filepath);
//        }
//
//        String sql = "update tb_card_teachstudinfo " +
//                "set orgcode = '%s' " +
//                "where code = '%s'";
//        List<String> update = new ArrayList<>();
//        for (Row row : rows) {
//            String orgCode = row.getCell("orgcode").getVal();
//            if (StrUtil.isBlank(orgCode)) {
//                continue;
//            }
//            String code = row.getCell("code").getVal();
//            update.add(String.format(sql, orgCode, code));
//        }
//
//        dbService.excuteBatchSql(update.toArray(new String[0]));
//        return Json.getJsonResult(true, "");
//    }

    /**
     *生成人员uid
     */
    public JsonData getTeacherUid(HttpServletRequest request, HttpServletResponse response, String orgcode) {
        String s = UUID.randomUUID().toString();
        JSONObject js = new JSONObject();
        js.put("uid", s);
        JSONArray j = new JSONArray();
        j.add(js);
        JsonData jsonData = new JsonData();
        jsonData.setData(j);
        jsonData.setSuccess(true);
        return jsonData;
    }


    public JSONArray getGroupStore(HttpServletRequest request, HttpServletResponse response, String infoId) {
        String sql = "select ag.uid,ag.name from tb_face_authorize_group ag " +
                " join tb_face_authorize_group_access ga on ag.uid = ga.groupid " +
                " where infoid = '" + infoId + "' " +
                " GROUP BY uid,name ";
        return dbService.QueryList(sql);
    }
}


