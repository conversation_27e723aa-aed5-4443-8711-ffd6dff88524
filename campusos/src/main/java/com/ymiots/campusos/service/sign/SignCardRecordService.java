package com.ymiots.campusos.service.sign;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.ymiots.framework.common.*;
import com.ymiots.campusos.entity.ApiResponse;
import com.ymiots.campusos.entity.DeviceData;
import com.ymiots.framework.common.DateHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;

@Repository
public class SignCardRecordService extends BaseService {
	@Autowired
	SysLogsService syslogs;

	@Autowired
	UserRedis userredis;

	public JsonData getSignCardRecordList(HttpServletRequest request, HttpServletResponse response, String key,
			String starttime, String endtime, int start, int limit) {
		String fields = "sc.uid,info.code,info.name,info.card,date_format(sc.recordtime, '%Y-%m-%d %H:%i:%s') recordtime,sc.machineid,ct.devname";
		String table = "tb_sign_cardrecord sc INNER JOIN tb_card_teachstudinfo info on  sc.infoid=info.uid INNER JOIN tb_dev_accesscontroller ct on ct.machineid=sc.machineid";
		String userid = userredis.getUserId(request);
		// 如果为普通管理员，需要受二级授权控制
		if (userredis.get(request).getUsertype() == 1) {
			table += " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='" + userid + "' ";
		}
		String where = "sc.recordtime between '" + starttime + "' and '" + endtime + "'";
		if (!StringUtils.isBlank(key)) {
			where += " and (info.name like '%" + key + "%' or info.code  like '%" + key + "%' or info.card  like '%"+ key + "%' or sc.machineid like '%" + key + "%' ) ";
		}
		String orderby = ExtSort.Orderby(request, " sc.recordtime desc ");
		JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}

	public JsonData getPCWGSignCardRecordList1(HttpServletRequest request, HttpServletResponse response, String areacode) {
		String sql ="SELECT\n" +
				"\tb.name as infoname,getorgname(b.orgcode) as class, date_format(a.recordtime,'%Y-%m-%d %H:%i:%s') as recordtime,b.uid as infoid, c.imgpath as recordimg1, '' as recordimg2\n" +
				"FROM\n" +
				"\ttb_sign_wg_records a\n" +
				"\tLEFT JOIN tb_card_teachstudinfo b ON a.info_id = b.uid\n" +
				"\tLEFT JOIN tb_face_infoface c ON a.info_id = c.infoid\n" +
				"\tLEFT JOIN tb_dev_accesscontroller d on a.wg_id = d.machineid\n" +
				"WHERE\n" +
				"\t1=1 ";
		if (StringUtils.isNotEmpty(areacode)){
			sql+=" and d.areacode like '"+areacode+"%'";
		}
		sql+=" order by a.recordtime desc limit 0,4";
		JSONArray array = dbService.QueryList(sql);
		// 解析时间字符串
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		JSONArray objects = new JSONArray();
		for (int i = 0; i < array.size(); i++) {
			String infoid = array.getJSONObject(i).getString("infoid");
			String recordtime = array.getJSONObject(i).getString("recordtime");
			LocalDateTime dateTime = LocalDateTime.parse(recordtime, formatter);
			// 获取前10分钟时间
			LocalDateTime tenMinutesBefore = dateTime.minusMinutes(10);
			// 格式化为字符串输出
			String formattedTime = tenMinutesBefore.format(formatter);
			JSONArray array1 = dbService.QueryList("select uid,facepath from tb_face_record where infoid = ? and recordtime BETWEEN ? and ?", infoid,formattedTime,recordtime);
			if (array1.size()>0){
				array.getJSONObject(i).put("recordimg2",array1.getJSONObject(0).getString("facepath"));
				objects.add(array.getJSONObject(i));
				return Json.getJsonData(true,objects);
			}
		}
		return Json.getJsonData(true,objects);
	}


	public JsonData getPCWGSignCardRecordList(HttpServletRequest request, HttpServletResponse response, String areacode) {
		String sql = "select b.name as infoname,date_format(a.recordtime,'%Y-%m-%d %H:%i:%s') as recordtime,b.uid as infoid,getorgname(b.orgcode) as class,a.facepath as recordimg  from tb_face_record a left join tb_card_teachstudinfo b on a.infoid = b.uid LEFT JOIN tb_dev_accesscontroller d on a.machineid = d.machineid where 1=1";
		if (StringUtils.isNotEmpty(areacode)){
			sql+=" and d.areacode like '"+areacode+"%'";
		}
		sql+=" order by a.recordtime desc limit 0,8";
		JSONArray array = dbService.QueryList(sql);
		// 解析时间字符串
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		for (int i = 0; i < array.size(); i++) {
			String infoid = array.getJSONObject(i).getString("infoid");
			String recordtime = array.getJSONObject(i).getString("recordtime");
			LocalDateTime dateTime = LocalDateTime.parse(recordtime, formatter);
			// 获取加10分钟时间
			LocalDateTime tenMinutesBefore = dateTime.plusMinutes(10);
			// 格式化为字符串输出
			String formattedTime = tenMinutesBefore.format(formatter);
			JSONArray array1 = dbService.QueryList("select * from tb_sign_wg_records where info_id = ? and recordtime BETWEEN ? and ?", infoid,recordtime,formattedTime);
			if (array1.size()>0){
				array.getJSONObject(i).put("flag",1);
			}else {
				array.getJSONObject(i).put("flag",0);
			}
		}
		return Json.getJsonData(true,array);
	}

	public String ExportSignCardRecordData(HttpServletRequest request, HttpServletResponse response, String key,
			String starttime, String endtime) throws IOException {

		JsonData result = getSignCardRecordList(request, response, key, starttime, endtime, 0, 0);
		if (!result.isSuccess()) {
			return "";
		}
		JSONArray list = result.getData();
		JSONArray cells = new JSONArray();
		JSONObject cell = new JSONObject();

		cell = new JSONObject();
		cell.put("name", "签到时间");
		cell.put("datakey", "recordtime");
		cell.put("size", 10);
		cells.add(cell);

		cell = new JSONObject();
		cell.put("name", "工号");
		cell.put("datakey", "code");
		cell.put("size", 10);
		cells.add(cell);

		cell = new JSONObject();
		cell.put("name", "姓名");
		cell.put("datakey", "name");
		cell.put("size", 10);
		cells.add(cell);

		cell = new JSONObject();
		cell.put("name", "卡号");
		cell.put("datakey", "card");
		cell.put("size", 10);
		cells.add(cell);

		cell = new JSONObject();
		cell.put("name", "机号");
		cell.put("datakey", "machineid");
		cell.put("size", 10);
		cells.add(cell);

		cell = new JSONObject();
		cell.put("name", "设备名称");
		cell.put("datakey", "devname");
		cell.put("size", 10);
		cells.add(cell);

		String title= "签到信息";
		String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
		String filename =  title+format + ".xlsx";

		String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
		return filepath;
	}

	public JsonData getWGSignCardRecordList(HttpServletRequest request, HttpServletResponse response, String key,
										  String starttime, String endtime, int start, int limit) {
		String fields = "sc.uid,info.code,info.name,info.card,date_format(sc.recordtime, '%Y-%m-%d %H:%i:%s') recordtime,sc.sn,ct.devsn,ct.machineid,ct.devname,sc.direction";
		String table = "tb_sign_wg_records sc INNER JOIN tb_card_teachstudinfo info on  sc.info_id=info.uid INNER JOIN tb_dev_accesscontroller ct on ct.devsn=sc.sn";
		String userid = userredis.getUserId(request);
		// 如果为普通管理员，需要受二级授权控制
		if (userredis.get(request).getUsertype() == 1) {
			table += " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='" + userid + "' ";
		}
		String where = "sc.recordtime between '" + starttime + "' and '" + endtime + "'";
		if (!StringUtils.isBlank(key)) {
			where += " and (info.name like '%" + key + "%' or info.code  like '%" + key + "%' or info.card  like '%"+ key + "%' or sc.sn like '%" + key + "%' ) ";
		}
		String orderby = ExtSort.Orderby(request, " sc.recordtime desc ");
		JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}

	public JsonData getWGDateViewSignCardRecordList(HttpServletRequest request, HttpServletResponse response, String machineids,
											String starttime, String endtime, int start, int limit) {
		String fields = "sc.uid,info.code,info.name infoname,fi.imgpath recordimg,getminiorgname(info.orgcode) class,date_format(sc.recordtime, '%Y-%m-%d %H:%i:%s') recordtime,sc.sn,ct.devsn,ct.machineid,ct.devname,CASE sc.direction WHEN '1' THEN '进' WHEN '2' THEN '出' ELSE '' END AS direction";
		String table = "tb_sign_wg_records sc " +
				"INNER JOIN tb_card_teachstudinfo info on  sc.info_id=info.uid " +
				"left join tb_face_infoface fi on fi.infoid = info.uid " +
				"INNER JOIN tb_dev_accesscontroller ct on ct.devsn=sc.sn";
		String userid = userredis.getUserId(request);
		// 如果为普通管理员，需要受二级授权控制
		if (userredis.get(request).getUsertype() == 1) {
			table += " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='" + userid + "' ";
		}
		String where = "sc.recordtime between '" + starttime + "' and '" + endtime + "'";
		if (!StringUtils.isBlank(machineids)) {
			where += " and ct.uid = '"+machineids+"'";
		}
		String orderby = ExtSort.Orderby(request, " sc.recordtime desc ");
		JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}

	// 设备的最后更新时间映射表，键为设备的 SN，值为设备的最后更新时间
	private final Map<String, LocalDateTime> lastUpdateTimeMap = new HashMap<>();
	// 定时任务执行器，用于定期检查设备状态
	private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
	// 设备离线阈值，假设为30秒
	private static final long OFFLINE_THRESHOLD_SECONDS = 30;

	//刷卡记录保存
	private static final String DEVICE_REPORT_PROBE_DATA = "device_report_probe_data";

	//设备心跳状态
	private static final String DEVICE_REPORT_HEARTBEAT_DATA = "device_report_heartbeat_data";

	public ApiResponse notifyCardRecord(DeviceData deviceData) {
		//如果method请求方法为device_report_probe_data,保存数据
		if (DEVICE_REPORT_HEARTBEAT_DATA.equals(deviceData.getMethod())) {
			lastUpdateTimeMap.put(deviceData.getSn(), LocalDateTime.now());
		}
		// 封装返回值
		ApiResponse.Status status = new ApiResponse.Status(200, "请求成功！");
		if (DEVICE_REPORT_PROBE_DATA.equals(deviceData.getMethod())) {
			DeviceData.DataConfig config = deviceData.getData();
			DeviceData.Tag tag = config.getTagList().get(0);
			String sql = "insert into tb_sign_wg_records(uid,wg_id,temperature,direction,epc,rssi,method,sn,recordtime,info_id) values (uuid(),?,?,?,?,?,?,?,?,?)";
			int length = tag.getEpc().length();
			String cardSn = "";
			if (length < 10&&length>0) {
				int num = 12 - length;
				cardSn = String.format("%0"+num+"d%s", 0, tag.getEpc());
			} else {
				cardSn =String.format("%02d%s", 0, tag.getEpc().substring(length - 10));
			}
			String selInfoId = "select uid from tb_card_teachstudinfo" +
					" where cardsn='" + cardSn + "'";
			JSONObject object = dbService.QueryJSONObject(selInfoId);
			if (object != null) {
				String infoId = object.getString("uid");
				dbService.excuteSql(sql, config.getId(), config.getTemperature(), tag.getDirection(), cardSn, tag.getRssi(), deviceData.getMethod(), deviceData.getSn(), deviceData.getTimestamp(), infoId);
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
				LocalDateTime dateTime = LocalDateTime.parse(deviceData.getTimestamp(), formatter);
				LocalDateTime tenMinutesBefore = dateTime.minusMinutes(10);
				String formattedTime = tenMinutesBefore.format(formatter);
				JSONArray array1 = dbService.QueryList("select uid,facepath from tb_face_record where infoid = ? and recordtime BETWEEN ? and ?", infoId,formattedTime,deviceData.getTimestamp());
				if (array1.size()==0){
					ApiResponse.WaringInfo warn = new ApiResponse.WaringInfo("2", "5");
					ApiResponse.Custom custom = new ApiResponse.Custom(warn);
					Log.info(this.getClass(),"==1================WaringInfo(1, 5)======================");
					ApiResponse apiResponse = new ApiResponse(status, custom);
					Log.info(this.getClass(), JSON.toJSONString(apiResponse));
					return new ApiResponse(status, custom);
				}
			} else {
				ApiResponse.WaringInfo warn = new ApiResponse.WaringInfo("2", "5");
				ApiResponse.Custom custom = new ApiResponse.Custom(warn);
				dbService.excuteSql(sql, config.getId(), config.getTemperature(), tag.getDirection(), cardSn, tag.getRssi(), deviceData.getMethod(), deviceData.getSn(), deviceData.getTimestamp(), "");
				Log.info(this.getClass(),"==2==================WaringInfo(1, 5)====================");
				ApiResponse apiResponse = new ApiResponse(status, custom);
				Log.info(this.getClass(), JSON.toJSONString(apiResponse));
				return new ApiResponse(status, custom);
			}
		}
		ApiResponse.WaringInfo warn = new ApiResponse.WaringInfo("0", "0");
		ApiResponse.Custom custom = new ApiResponse.Custom(warn);
		ApiResponse apiResponse = new ApiResponse(status, custom);
		Log.info(this.getClass(), JSON.toJSONString(apiResponse));
		Log.info(this.getClass(),"==3===========WaringInfo(0, 0)===================");
		return new ApiResponse(status, custom);
	}

	// 检查设备状态
	private synchronized void checkDeviceStatus() {
		LocalDateTime currentTime = LocalDateTime.now();
		for (String sn : lastUpdateTimeMap.keySet()) {
			LocalDateTime lastUpdateTime = lastUpdateTimeMap.get(sn);
			Duration duration = Duration.between(lastUpdateTime, currentTime);
			long secondsSinceLastUpdate = duration.getSeconds();
			// 如果超过离线阈值，更新设备状态为离线
			if (secondsSinceLastUpdate > OFFLINE_THRESHOLD_SECONDS) {
				updateDeviceStatus(sn, 0);
			} else {
				updateDeviceStatus(sn,1);
			}
		}
	}

	// 更新设备状态
	private void updateDeviceStatus(String sn, int status) {
		String sql = "update tb_dev_accesscontroller set devstatus = ?,livestime = now() where devsn = ?";
		dbService.excuteSql(sql, status, sn);
	}

	// 启动定时任务执行器
	@PostConstruct
	private void startScheduler() {
		// 每隔一定时间（30秒）执行一次 checkDeviceStatus 方法
		scheduler.scheduleAtFixedRate(this::checkDeviceStatus, 0, 30, TimeUnit.SECONDS);
	}
}
