package com.ymiots.campusos.service.workflow;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.DBService;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * @Author: 邱文彬
 * @Date: 2022/2/2511:31
 * @Version 1.0
 */

@Service
public class NodeService {

    /**
     * 导入日志功能类
     */
    @Autowired
    private SysLogsService syslogs;

    /**
     * 审核人模块service层
     */
    @Autowired
    private ApproverService approverService;

    @Autowired
    private UserRedis userRedis;

    @Autowired
    DBService dbService;

    @Autowired
    JdbcTemplate jdbcTemplate;

    /**
     * 查找节点(根据模板ID)
     *
     * @param request
     * @param response
     * @param mouldid
     * @param start
     * @param limit
     * @return
     */
    public JsonData getNodelist(HttpServletRequest request, HttpServletResponse response, String mouldid, int start, int limit) {

        String where = "mouldid='" + mouldid + "'";
        String orderby = ExtSort.Orderby(request, "createdate desc");
        //不用星号
        JsonData result = dbService.QueryJsonData("uid,nodelevel,isfinal,mouldid,audittype,createdate,behavior_status as behaviorStatus,createid,modifydate,modifyid", "tb_workflow_node", where, orderby, start, limit);
        syslogs.Write(request, "查找数据", "查找节点：" + "tb_workflow_node 模板ID:" + mouldid);
        return result;
    }

    /**
     * 根据节点uid删除节点
     *
     * @param request
     * @param response
     * @param uid
     * @return
     */
    public JsonResult delNodeList(HttpServletRequest request, HttpServletResponse response, String uid) {
        // 判断需要删除的节点是否是最高节点,不是则不能删除
        String mid = "select mouldid from tb_workflow_node where uid in ('" + String.join("','", uid.split(",")) + "') ";
        JSONObject jsonObject = dbService.QueryJSONObject(mid);
        JsonData approverList = approverService.getApproverList(request, response, "", uid, 0, 10);
        if (approverList.getData().size() == 0) {
            String delSQL = "DELETE FROM tb_workflow_node WHERE uid IN ('" + String.join("','", uid.split(",")) + "') and isfinal = 1";
            int result = dbService.excuteSql(delSQL);
            if (result == 0) {
                return Json.getJsonResult(false, "当前删除节点不是最高节点");
            }
            //更改节点
            String mouldId = jsonObject.getString("mouldid");
            String maxLevelSQL = "select max(nodelevel) as nodeLevel from tb_workflow_node where mouldid = '" + mouldId + "'";
            String maxLevel = jdbcTemplate.queryForObject(maxLevelSQL, String.class);
            String updateSqlNodeLevel = "update tb_workflow_node set isfinal = 1 where nodelevel = " + maxLevel + " and mouldid ='" + mouldId + "'";
            dbService.excuteSql(updateSqlNodeLevel);
            syslogs.Write(request, "删除数据", "删除节点：" + uid);
            return Json.getJsonResult(true);
        }
        return Json.getJsonResult(false, "当前节点下存在审核人,请清空审核人后删除");
    }

    /**
     * @param request
     * @param response
     * @param uid
     * @param nodelevel
     * @param mouldid
     * @param audittype
     * @return
     */
    public JsonResult saveNode(HttpServletRequest request, HttpServletResponse response, String uid, int nodelevel, String mouldid, int audittype,int behaviorStatus){
        if (StringUtils.isBlank(uid)) {
//            判断是否已经存在
            String uuid = UUID.randomUUID().toString();
            String selSqlNodeLevel = "select max(nodelevel) as nodeLevel from tb_workflow_node where mouldid =  '" + mouldid + "'";
            JSONObject jo = dbService.QueryJSONObject(selSqlNodeLevel);
            if (jo.isEmpty()) {
                if (nodelevel != 1) {
                    return Json.getJsonResult(false, "当前模块节点为空,请添加第一节点");
                }
                String insertSql = "insert into tb_workflow_node (uid,nodelevel,mouldid,audittype,isfinal,createdate,modifydate,createid,modifyid,behavior_status) values (?,?,?,?,?,now(),now() ,?,?,?)";
                dbService.excuteSql(insertSql, uuid, 1, mouldid, audittype, 1, userRedis.getUserId(request), userRedis.getUserId(request),behaviorStatus);
                return Json.getJsonResult(true);
            } else {
                int nodeLevel = jo.getInteger("nodeLevel");
                if (nodelevel - nodeLevel != 1) {
                    return Json.getJsonResult(false, "请按顺序添加节点等级！！！");
                } else {
                    //新增的首先更改是否最后审核状态
                    String updateSql = "update tb_workflow_node set isfinal = ? where isfinal = 1 and mouldid = '" + mouldid + "'";
                    dbService.excuteSql(updateSql, 0);

                    String insertSql = "insert into tb_workflow_node (uid,nodelevel,mouldid,audittype,isfinal,createdate,modifydate,createid,modifyid,behavior_status) values (?,?,?,?,?,now(),now() ,?,?,?)";
                    dbService.excuteSql(insertSql, uuid, nodelevel, mouldid, audittype, 1, userRedis.getUserId(request), userRedis.getUserId(request), behaviorStatus);
                    syslogs.Write(request, "新增节点", String.format("新增模板节点数据:模板名称:%s,节点等级:%s,审核类型:%s", mouldid, nodelevel, audittype));
                    return Json.getJsonResult(true);
                }
            }
        } else {
            // 判断审核单据中是否存在未审核完成的表单
            if (dbService.QueryJSONObject("select count(uid) from tb_workflow_approval_record where mouldid = '" + mouldid + "'").getIntValue("uid") == 0) {
                String updateSql = "UPDATE tb_workflow_node SET mouldid=?,audittype=?,behavior_status=?,modifyid=?  WHERE uid=? ";
                int result = dbService.excuteSql(updateSql, mouldid, audittype, behaviorStatus, userRedis.getUserId(request), uid);
                if (result == 0) {
                    return Json.getJsonResult(false, "当前节点等级已存在");
                }
                return Json.getJsonResult(true);
            }
            return Json.getJsonResult(false, "当前模块下存在未审核完成的单据");
        }
    }
}
