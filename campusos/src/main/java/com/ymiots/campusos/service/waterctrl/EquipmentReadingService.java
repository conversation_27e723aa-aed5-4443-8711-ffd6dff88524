package com.ymiots.campusos.service.waterctrl;

import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.dto.thirdapi.anlide.Equipment;
import com.ymiots.campusos.dto.thirdapi.anlide.EquipmentDataResponse;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 设备读数服务
 * <AUTHOR>
 * @date 2025-06-28
 */
@Service
public class EquipmentReadingService extends BaseService {


    public static final Logger logger = LoggerFactory.getLogger(EquipmentReadingService.class);

    /**
     * 保存设备读数记录
     * @param equipmentCode 设备编号
     * @param equipmentDataResponse 设备读数响应
     * @param equipment 设备信息
     * @return 保存结果
     */
    public JsonResult saveEquipmentReading(String equipmentCode, EquipmentDataResponse equipmentDataResponse, Equipment equipment) {
        try {
            Date currentDate = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String todayStr = dateFormat.format(currentDate);

            // 检查今天是否已有记录
            String checkSql = "SELECT COUNT(*) FROM tb_equipment_reading_records WHERE equipment_code = ? AND DATE(reading_date) = ?";
            int existCount = dbService.getCountBySQL(checkSql, equipmentCode, todayStr);

            if (existCount > 0) {
                // 更新现有记录
                return updateEquipmentReading(equipmentCode, equipmentDataResponse, equipment);
            } else {
                // 插入新记录
                return insertEquipmentReading(equipmentCode, equipmentDataResponse, equipment);
            }
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, "保存设备读数记录失败: " + e.getMessage());
            return Json.getJsonResult(false, "保存设备读数记录失败: " + e.getMessage());
        }
    }

    /**
     * 插入新的设备读数记录
     */
    private JsonResult insertEquipmentReading(String equipmentCode, EquipmentDataResponse equipmentDataResponse, Equipment equipment) {
        try {
            Date currentDate = new Date();
            String uid = UUID.randomUUID().toString();

            // 获取上次读数记录（包含分时读数）
            Map<String, Object> previousRecord = getPreviousReadingRecord(equipmentCode);

            // 获取上次基础读数
            BigDecimal previousReading = getPreviousReading(equipmentCode);

            // 计算基础用量
            BigDecimal usageAmount = null;
            if (equipmentDataResponse.getNumber() != null && previousReading != null) {
                usageAmount = BigDecimal.valueOf(equipmentDataResponse.getNumber()).subtract(previousReading);
                if (usageAmount.compareTo(BigDecimal.ZERO) < 0) {
                    usageAmount = BigDecimal.ZERO; // 防止负数
                }
            }

            // 获取上次分时读数
            BigDecimal previousTipReading = null;
            BigDecimal previousPeakReading = null;
            BigDecimal previousFlatReading = null;
            BigDecimal previousValleyReading = null;
            Date previousReadingTime = null;

            if (previousRecord != null) {
                previousTipReading = (BigDecimal) previousRecord.get("tip_reading");
                previousPeakReading = (BigDecimal) previousRecord.get("peak_reading");
                previousFlatReading = (BigDecimal) previousRecord.get("flat_reading");
                previousValleyReading = (BigDecimal) previousRecord.get("valley_reading");

                // 处理时间类型转换
                previousReadingTime = convertToDate(previousRecord.get("reading_time"));
            }

            // 计算各时段用量
            BigDecimal tipUsage = null;
            BigDecimal peakUsage = null;
            BigDecimal flatUsage = null;
            BigDecimal valleyUsage = null;

            if (equipmentDataResponse.isTimeSharingMeter()) {
                if (equipmentDataResponse.getTipNumber() != null && previousTipReading != null) {
                    tipUsage = BigDecimal.valueOf(equipmentDataResponse.getTipNumber()).subtract(previousTipReading);
                    if (tipUsage.compareTo(BigDecimal.ZERO) < 0) tipUsage = BigDecimal.ZERO;
                }

                if (equipmentDataResponse.getPeakNumber() != null && previousPeakReading != null) {
                    peakUsage = BigDecimal.valueOf(equipmentDataResponse.getPeakNumber()).subtract(previousPeakReading);
                    if (peakUsage.compareTo(BigDecimal.ZERO) < 0) peakUsage = BigDecimal.ZERO;
                }

                if (equipmentDataResponse.getFlatNumber() != null && previousFlatReading != null) {
                    flatUsage = BigDecimal.valueOf(equipmentDataResponse.getFlatNumber()).subtract(previousFlatReading);
                    if (flatUsage.compareTo(BigDecimal.ZERO) < 0) flatUsage = BigDecimal.ZERO;
                }

                if (equipmentDataResponse.getValleyNumber() != null && previousValleyReading != null) {
                    valleyUsage = BigDecimal.valueOf(equipmentDataResponse.getValleyNumber()).subtract(previousValleyReading);
                    if (valleyUsage.compareTo(BigDecimal.ZERO) < 0) valleyUsage = BigDecimal.ZERO;
                }
            }

            // 获取设备区域编码
            String areaCode = getEquipmentAreaCode(equipmentCode);

            String insertSql = "INSERT INTO tb_equipment_reading_records " +
                    "(uid, equipment_code, equipment_type, reading_date, reading_time, current_reading, " +
                    "previous_reading, usage_amount, current_balance, unit_price, tip_reading, peak_reading, " +
                    "flat_reading, valley_reading, " +
                    "previous_tip_reading, previous_peak_reading, previous_flat_reading, previous_valley_reading, " +
                    "tip_usage, peak_usage, flat_usage, valley_usage, previous_reading_time, " +
                    "is_time_sharing, area_code, status, create_date, creator_id) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, 'system')";

            int result = dbService.excuteSql(insertSql,
                    uid,
                    equipmentCode,
                    equipment != null ? 1 : null,
                    currentDate,
                    currentDate,
                    equipmentDataResponse.getNumber() != null ? BigDecimal.valueOf(equipmentDataResponse.getNumber()) : null,
                    previousReading,
                    usageAmount,
                    equipmentDataResponse.getBalance(),
                    equipment != null ? equipment.getUnivalent() : null,
                    equipmentDataResponse.getTipNumber() != null ? BigDecimal.valueOf(equipmentDataResponse.getTipNumber()) : null,
                    equipmentDataResponse.getPeakNumber() != null ? BigDecimal.valueOf(equipmentDataResponse.getPeakNumber()) : null,
                    equipmentDataResponse.getFlatNumber() != null ? BigDecimal.valueOf(equipmentDataResponse.getFlatNumber()) : null,
                    equipmentDataResponse.getValleyNumber() != null ? BigDecimal.valueOf(equipmentDataResponse.getValleyNumber()) : null,
                    // 新增：上次分时读数
                    previousTipReading,
                    previousPeakReading,
                    previousFlatReading,
                    previousValleyReading,
                    // 新增：各时段用量
                    tipUsage,
                    peakUsage,
                    flatUsage,
                    valleyUsage,
                    // 新增：上次读表时间
                    previousReadingTime,
                    equipmentDataResponse.isTimeSharingMeter() ? 1 : 0,
                    areaCode,
                    currentDate
            );

            if (result > 0) {
                Log.info(EquipmentReadingService.class,
                    String.format("设备读数记录保存成功: %s, 基础用量: %s, 尖峰平谷用量: %s/%s/%s/%s",
                        equipmentCode, usageAmount, tipUsage, peakUsage, flatUsage, valleyUsage));
                return Json.getJsonResult(true, "设备读数记录保存成功");
            } else {
                return Json.getJsonResult(false, "设备读数记录保存失败");
            }
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, "插入设备读数记录失败: " + e.getMessage());
            return Json.getJsonResult(false, "插入设备读数记录失败: " + e.getMessage());
        }
    }

    /**
     * 更新现有的设备读数记录
     */
    private JsonResult updateEquipmentReading(String equipmentCode, EquipmentDataResponse equipmentDataResponse, Equipment equipment) {
        try {
            Date currentDate = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String todayStr = dateFormat.format(currentDate);

            // 获取上次读数记录（包含分时读数）
            Map<String, Object> previousRecord = getPreviousReadingRecord(equipmentCode);

            // 获取上次基础读数
            BigDecimal previousReading = getPreviousReading(equipmentCode);

            // 计算基础用量
            BigDecimal usageAmount = null;
            if (equipmentDataResponse.getNumber() != null && previousReading != null) {
                usageAmount = BigDecimal.valueOf(equipmentDataResponse.getNumber()).subtract(previousReading);
                if (usageAmount.compareTo(BigDecimal.ZERO) < 0) {
                    usageAmount = BigDecimal.ZERO; // 防止负数
                }
            }

            // 获取上次分时读数
            BigDecimal previousTipReading = null;
            BigDecimal previousPeakReading = null;
            BigDecimal previousFlatReading = null;
            BigDecimal previousValleyReading = null;
            Date previousReadingTime = null;

            if (previousRecord != null) {
                previousTipReading = (BigDecimal) previousRecord.get("tip_reading");
                previousPeakReading = (BigDecimal) previousRecord.get("peak_reading");
                previousFlatReading = (BigDecimal) previousRecord.get("flat_reading");
                previousValleyReading = (BigDecimal) previousRecord.get("valley_reading");

                // 处理时间类型转换
                previousReadingTime = convertToDate(previousRecord.get("reading_time"));
            }

            // 计算各时段用量
            BigDecimal tipUsage = null;
            BigDecimal peakUsage = null;
            BigDecimal flatUsage = null;
            BigDecimal valleyUsage = null;

            if (equipmentDataResponse.isTimeSharingMeter()) {
                if (equipmentDataResponse.getTipNumber() != null && previousTipReading != null) {
                    tipUsage = BigDecimal.valueOf(equipmentDataResponse.getTipNumber()).subtract(previousTipReading);
                    if (tipUsage.compareTo(BigDecimal.ZERO) < 0) tipUsage = BigDecimal.ZERO;
                }

                if (equipmentDataResponse.getPeakNumber() != null && previousPeakReading != null) {
                    peakUsage = BigDecimal.valueOf(equipmentDataResponse.getPeakNumber()).subtract(previousPeakReading);
                    if (peakUsage.compareTo(BigDecimal.ZERO) < 0) peakUsage = BigDecimal.ZERO;
                }

                if (equipmentDataResponse.getFlatNumber() != null && previousFlatReading != null) {
                    flatUsage = BigDecimal.valueOf(equipmentDataResponse.getFlatNumber()).subtract(previousFlatReading);
                    if (flatUsage.compareTo(BigDecimal.ZERO) < 0) flatUsage = BigDecimal.ZERO;
                }

                if (equipmentDataResponse.getValleyNumber() != null && previousValleyReading != null) {
                    valleyUsage = BigDecimal.valueOf(equipmentDataResponse.getValleyNumber()).subtract(previousValleyReading);
                    if (valleyUsage.compareTo(BigDecimal.ZERO) < 0) valleyUsage = BigDecimal.ZERO;
                }
            }

            String updateSql = "UPDATE tb_equipment_reading_records SET " +
                    "reading_time = ?, current_reading = ?, previous_reading = ?, usage_amount = ?, " +
                    "current_balance = ?, unit_price = ?, tip_reading = ?, peak_reading = ?, " +
                    "flat_reading = ?, valley_reading = ?, " +
                    "previous_tip_reading = ?, previous_peak_reading = ?, previous_flat_reading = ?, previous_valley_reading = ?, " +
                    "tip_usage = ?, peak_usage = ?, flat_usage = ?, valley_usage = ?, " +
                    "previous_reading_time = ?, is_time_sharing = ?, modify_date = ?, modifier_id = 'system' " +
                    "WHERE equipment_code = ? AND DATE(reading_date) = ?";

            int result = dbService.excuteSql(updateSql,
                    currentDate,
                    equipmentDataResponse.getNumber() != null ? BigDecimal.valueOf(equipmentDataResponse.getNumber()) : null,
                    previousReading,
                    usageAmount,
                    equipmentDataResponse.getBalance(),
                    equipment != null ? equipment.getUnivalent() : null,
                    equipmentDataResponse.getTipNumber() != null ? BigDecimal.valueOf(equipmentDataResponse.getTipNumber()) : null,
                    equipmentDataResponse.getPeakNumber() != null ? BigDecimal.valueOf(equipmentDataResponse.getPeakNumber()) : null,
                    equipmentDataResponse.getFlatNumber() != null ? BigDecimal.valueOf(equipmentDataResponse.getFlatNumber()) : null,
                    equipmentDataResponse.getValleyNumber() != null ? BigDecimal.valueOf(equipmentDataResponse.getValleyNumber()) : null,
                    // 新增：上次分时读数
                    previousTipReading,
                    previousPeakReading,
                    previousFlatReading,
                    previousValleyReading,
                    // 新增：各时段用量
                    tipUsage,
                    peakUsage,
                    flatUsage,
                    valleyUsage,
                    // 新增：上次读表时间
                    previousReadingTime,
                    equipmentDataResponse.isTimeSharingMeter() ? 1 : 0,
                    currentDate,
                    equipmentCode,
                    todayStr
            );

            if (result > 0) {
                Log.info(EquipmentReadingService.class,
                    String.format("设备读数记录更新成功: %s, 基础用量: %s, 尖峰平谷用量: %s/%s/%s/%s",
                        equipmentCode, usageAmount, tipUsage, peakUsage, flatUsage, valleyUsage));
                return Json.getJsonResult(true, "设备读数记录更新成功");
            } else {
                return Json.getJsonResult(false, "设备读数记录更新失败");
            }
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, "更新设备读数记录失败: " + e.getMessage());
            return Json.getJsonResult(false, "更新设备读数记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取设备上次读数
     */
    private BigDecimal getPreviousReading(String equipmentCode) {
        try {
            String sql = "SELECT current_reading FROM tb_equipment_reading_records " +
                    "WHERE equipment_code = ? AND status = 1 AND DATE(reading_date) < DATE(NOW()) " +
                    "ORDER BY reading_date DESC, reading_time DESC LIMIT 1";
            
            Object result = dbService.queryObject(sql, equipmentCode);
            if (result != null) {
                return new BigDecimal(result.toString());
            }
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, "获取设备上次读数失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 获取设备的上次读数记录（包含分时读数）
     */
    private Map<String, Object> getPreviousReadingRecord(String equipmentCode) {
        try {
            String sql = "SELECT * FROM tb_equipment_reading_records " +
                    "WHERE equipment_code = ? AND status = 1 AND DATE(reading_date) < DATE(NOW()) " +
                    "ORDER BY reading_date DESC, reading_time DESC LIMIT 1";

            List<Map<String, Object>> records = dbService.queryMapList(sql, equipmentCode);
            if (!records.isEmpty()) {
                return records.get(0);
            }
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, "获取设备上次读数记录失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 转换时间对象为Date类型
     */
    private Date convertToDate(Object timeObj) {
        if (timeObj == null) {
            return null;
        }

        if (timeObj instanceof LocalDateTime) {
            return Date.from(((LocalDateTime) timeObj).atZone(ZoneId.systemDefault()).toInstant());
        } else if (timeObj instanceof Date) {
            return (Date) timeObj;
        } else if (timeObj instanceof java.sql.Timestamp) {
            return new Date(((java.sql.Timestamp) timeObj).getTime());
        } else {
            Log.warn(EquipmentReadingService.class, "未知的时间类型: " + timeObj.getClass().getName());
            return null;
        }
    }

    /**
     * 获取设备区域编码
     */
    private String getEquipmentAreaCode(String equipmentCode) {
        try {
            String sql = "SELECT areacode FROM tb_dev_accesscontroller WHERE devsn = ? ";
            Object result = dbService.queryObject(sql, equipmentCode);
            if (result != null) {
                return result.toString();
            }
        } catch (Exception e) {
            Log.error(EquipmentReadingService.class, "获取设备区域编码失败: " + e.getMessage());
        }
        return null;
    }
}
