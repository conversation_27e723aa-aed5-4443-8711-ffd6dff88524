package com.ymiots.campusos.service.workflow;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: 邱文彬
 * @Date: 2022/2/2511:50
 * @Version 1.0
 */

@Service
public class ApproverService extends BaseService {

    /**
     * 导入日志功能类
     */
    @Autowired
    private SysLogsService syslogs;


    @Autowired
    UserRedis userredis;

    /**
     * 查询审核人(根据节点ID)
     *
     * @param request
     * @param response
     * @param nodeid
     * @param start
     * @param limit
     * @return
     */
    public JsonData getApproverList(HttpServletRequest request, HttpServletResponse response, String key, String nodeid, int start, int limit) {
        String fields = " wa.uid,wa.infoid,wa.nodeid,ct.code,ct.orgcode,ct.name,ct.sex,d.name as positionname,getorgname(ct.orgcode) as orgname";
        String table = "tb_workflow_approvar wa  " +
                "left join tb_card_teachstudinfo ct on wa.infoid = ct.uid " +
                "LEFT JOIN tb_sys_dictionary d on d.code=ct.position and d.groupcode='SYS0000045' ";
        String where = "1=1 AND nodeid ='"+nodeid+"' ";
        if (StringUtils.isNotBlank(key)) {
            where += " AND (ct.code='"+key+"' or ct.name like '%"+key+"%')";
        }
        JsonData result = dbService.QueryJsonData(fields, table, where, "wa.createdate DESC", start, limit);
        syslogs.Write(request, "查找数据", "查找审核人：" + "tb_workflow_node 节点ID:" + nodeid);
        return result;
    }

    /**
     * @param request
     * @param response
     * @param uid      数据uid
     * @return
     */
    public JsonResult delNameList(HttpServletRequest request, HttpServletResponse response, String uid) {
        String delSQL = "DELETE FROM tb_workflow_approvar WHERE uid IN ('" + String.join("','", uid.split(",")) + "')";
        dbService.excuteSql(delSQL);
        syslogs.Write(request, "删除数据", "删除审核人：" + uid);
        return Json.getJsonResult(true);
    }


    public JsonResult saveApprover(HttpServletRequest request, String nodeid, String listInfoid) {
//        根据request获取用户信息
//        强制条件 (nodeid不能为空)
        String[] infolist = listInfoid.split(",");
        if(infolist.length>0){
            String  insertsql = "INSERT INTO tb_workflow_approvar (uid,nodeid,infoid,createdate,modifydate,createid,modifyid) VALUES (uuid(),?,?,now(),now(),?,?)";
            for (String infoid:infolist) {
                dbService.excuteSql(insertsql,nodeid,infoid,userredis.getUserId(request),userredis.getUserId(request));
                syslogs.Write(request, "新增节点审批人",String.format("新增节点审批人:节点id:%s,审批人:%s",nodeid,infoid));
            }
        }else {
            return Json.getJsonResult(false,"节点id为空或者找不到当前审核人为空");
        }
        return Json.getJsonResult(true);
    }

    public JsonData getInfoList(HttpServletRequest request, String orgcode, String key, String infotype, boolean viewchild,
                                String property, String infolabel, String selecteduid, int start, int limit) {
        String fields = "info.uid,info.code,info.name,info.sex,info.card,info.cardsn,info.orgcode,info.infotype,info.intoyear,info.focus,info.status, getorgname(info.orgcode) as orgname";
        String table = "tb_card_teachstudinfo info";
        if (userredis.get(request).getUsertype() == 1) {
            String userid = userredis.getUserId(request);
            table += " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='" + userid + "' ";
        }
        String where = " info.status=1 ";

        if (StringUtils.isNotBlank(infotype) && !infotype.equals("0")) {
            where += " and info.infotype=" + infotype;
        }
        if (StringUtils.isNotBlank(property) && !property.equals("-1")) {
            where += " and info.property=" + property;
        }
        if (StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)) {
            where += " and info.infolabel = '" + infolabel + "' ";
        }

        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and info.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and info.orgcode='" + orgcode + "' ";
            }
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (info.code='" + key + "' or info.name like '%" + key + "%' or info.mobile like '%" + key + "%' or info.card='" + ComHelper.LeftPad(key, 12, '0') + "' or info.cardsn='" + ComHelper.LeftPad(key, 12, '0') + "' )";
        }
        JSONArray Selected = JSONArray.parseArray(selecteduid);
        if (Selected.size() > 0) {
            String[] uidx = ComHelper.ListToArray(Selected);
            where += " and info.uid not in('" + String.join("','", uidx) + "')";
        }
        String orderby = ExtSort.Orderby(request, "info.createdate desc");
        return dbService.QueryJsonData(fields, table, where, orderby, start, limit);
    }

    public JsonResult setDefaultApprover(HttpServletRequest request, HttpServletResponse response, String uid) {
        String editSQL = "update tb_workflow_approvar set status = 1 WHERE uid IN ('" + String.join("','", uid.split(",")) + "')";
        dbService.excuteSql(editSQL);
        syslogs.Write(request, "设置默认审核人", "设置默认审核人：" + uid);
        return Json.getJsonResult(true);
    }
}
