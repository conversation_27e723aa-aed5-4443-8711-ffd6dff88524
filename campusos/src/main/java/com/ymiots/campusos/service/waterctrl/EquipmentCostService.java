package com.ymiots.campusos.service.waterctrl;

import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.entity.waterctrl.EquipmentReadingRecord;
import com.ymiots.campusos.service.redis.RedisService;
import com.ymiots.campusos.service.system.SystemConfigService;
import com.ymiots.campusos.util.DateTimeUtil;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 设备费用计算服务
 * <AUTHOR>
 * @date 2025-06-28
 */
@Service
public class EquipmentCostService extends BaseService {

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private RedisService redisService;

    /**
     * 计算设备费用
     * @return 计算结果
     */
    public JsonResult calculateEquipmentCost(String equipmentCode,String todayStr) {
        try {
            // 获取读数记录
            EquipmentReadingRecord record = getReadingRecord(equipmentCode,todayStr);
            if (record == null) {
                return Json.getJsonResult(false, "读数记录不存在");
            }

            if (record.getUsageAmount() == null || record.getUsageAmount().compareTo(BigDecimal.ZERO) <= 0) {
                return Json.getJsonResult(false, "用量为空或为零，无需计算费用");
            }

            // 检查是否已有费用计算记录
            String checkSql = "SELECT COUNT(*) FROM tb_equipment_cost_calculation " +
                    "WHERE reading_record_id = ? AND status = 1";
            int existCount = dbService.getCountBySQL(checkSql, record.getUid());

            if (existCount > 0) {
                // 更新现有记录
                return updateCostCalculation(record);
            } else {
                // 插入新记录
                return insertCostCalculation(record);
            }
        } catch (Exception e) {
            Log.error(EquipmentCostService.class, "计算设备费用失败: " + e.getMessage());
            return Json.getJsonResult(false, "计算设备费用失败: " + e.getMessage());
        }
    }

    /**
     * 批量计算指定日期的所有设备费用
     * @param calculationDate 计算日期
     * @return 计算结果
     */
    public JsonResult batchCalculateCost(Date calculationDate) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = dateFormat.format(calculationDate);

            // 获取指定日期的所有读数记录
            String sql = "SELECT equipment_code FROM tb_equipment_reading_records " +
                    "WHERE DATE(reading_date) = ? AND status = 1 AND usage_amount > 0";
            
            List<String> equipmentCodeLists = dbService.queryList(sql, String.class, dateStr);

            int successCount = 0;
            int failCount = 0;

            for (String equipmentCode : equipmentCodeLists) {
                JsonResult result = calculateEquipmentCost(equipmentCode,dateStr);
                if (result.isSuccess()) {
                    successCount++;
                } else {
                    failCount++;
                    Log.warn(EquipmentCostService.class, "费用计算失败，设备ID: " + equipmentCode + ", 原因: " + result.getMsg());
                }
            }

            String message = String.format("批量费用计算完成，成功: %d, 失败: %d", successCount, failCount);
            Log.info(EquipmentCostService.class, message);
            return Json.getJsonResult(true, message);

        } catch (Exception e) {
            Log.error(EquipmentCostService.class, "批量计算设备费用失败: " + e.getMessage());
            return Json.getJsonResult(false, "批量计算设备费用失败: " + e.getMessage());
        }
    }

    /**
     * 插入费用计算记录
     */
    private JsonResult insertCostCalculation(EquipmentReadingRecord record) {
        try {
            String uid = UUID.randomUUID().toString();
            Date currentDate = new Date();

            // 计算费用
            CostCalculationResult costResult = calculateCost(record);

            String insertSql = "INSERT INTO tb_equipment_cost_calculation " +
                    "(uid, equipment_code, equipment_type, calculation_date, reading_record_id, " +
                    "usage_amount, unit_price, total_cost, tip_usage, tip_cost, peak_usage, peak_cost, " +
                    "flat_usage, flat_cost, valley_usage, valley_cost, area_code, status, create_date, creator_id) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, 'system')";

            int result = dbService.excuteSql(insertSql,
                    uid,
                    record.getEquipmentCode(),
                    record.getEquipmentType(),
                    record.getReadingDate(),
                    record.getUid(),
                    record.getUsageAmount(),
                    record.getUnitPrice(),
                    costResult.getTotalCost(),
                    costResult.getTipUsage(),
                    costResult.getTipCost(),
                    costResult.getPeakUsage(),
                    costResult.getPeakCost(),
                    costResult.getFlatUsage(),
                    costResult.getFlatCost(),
                    costResult.getValleyUsage(),
                    costResult.getValleyCost(),
                    record.getAreaCode(),
                    currentDate
            );

            if (result > 0) {
                Log.info(EquipmentCostService.class, "费用计算记录保存成功: " + record.getEquipmentCode());
                return Json.getJsonResult(true, "费用计算记录保存成功");
            } else {
                return Json.getJsonResult(false, "费用计算记录保存失败");
            }
        } catch (Exception e) {
            Log.error(EquipmentCostService.class, "插入费用计算记录失败: " + e.getMessage());
            return Json.getJsonResult(false, "插入费用计算记录失败: " + e.getMessage());
        }
    }

    /**
     * 更新费用计算记录
     */
    private JsonResult updateCostCalculation(EquipmentReadingRecord record) {
        try {
            Date currentDate = new Date();

            // 计算费用
            CostCalculationResult costResult = calculateCost(record);

            String updateSql = "UPDATE tb_equipment_cost_calculation SET " +
                    "usage_amount = ?, unit_price = ?, total_cost = ?, " +
                    "tip_usage = ?, tip_price = ?, tip_cost = ?, " +
                    "peak_usage = ?, peak_price = ?, peak_cost = ?, " +
                    "flat_usage = ?, flat_price = ?, flat_cost = ?, " +
                    "valley_usage = ?, valley_price = ?, valley_cost = ?, " +
                    "modify_date = ?, modifier_id = 'system' " +
                    "WHERE reading_record_id = ? AND status = 1";

            int result = dbService.excuteSql(updateSql,
                    record.getUsageAmount(),
                    record.getUnitPrice(),
                    costResult.getTotalCost(),
                    // 尖时
                    costResult.getTipUsage(),
                    priceInfo.getTipPrice(),
                    costResult.getTipCost(),
                    // 峰时
                    costResult.getPeakUsage(),
                    priceInfo.getPeakPrice(),
                    costResult.getPeakCost(),
                    // 平时
                    costResult.getFlatUsage(),
                    priceInfo.getFlatPrice(),
                    costResult.getFlatCost(),
                    // 谷时
                    costResult.getValleyUsage(),
                    priceInfo.getValleyPrice(),
                    costResult.getValleyCost(),
                    currentDate,
                    record.getUid()
            );

            if (result > 0) {
                Log.info(EquipmentCostService.class,
                    String.format("费用计算记录更新成功: %s, 总费用: %s, 分时费用: 尖%s/峰%s/平%s/谷%s",
                        record.getEquipmentCode(), costResult.getTotalCost(),
                        costResult.getTipCost(), costResult.getPeakCost(),
                        costResult.getFlatCost(), costResult.getValleyCost()));
                return Json.getJsonResult(true, "费用计算记录更新成功");
            } else {
                return Json.getJsonResult(false, "费用计算记录更新失败");
            }
        } catch (Exception e) {
            Log.error(EquipmentCostService.class, "更新费用计算记录失败: " + e.getMessage());
            return Json.getJsonResult(false, "更新费用计算记录失败: " + e.getMessage());
        }
    }

    /**
     * 从缓存获取电价信息
     */
    private PriceInfo getPriceInfoFromCache(EquipmentReadingRecord record) {
        try {
            PriceInfo priceInfo = new PriceInfo();

            // 获取设备类型对应的电价配置键
            String equipmentType = String.valueOf(record.getEquipmentType());
            String areaCode = record.getAreaCode();

            if (record.getIsTimeSharing() == 1 && "1".equals(equipmentType)) {
                // 分时电表，从缓存获取四时段电价
                priceInfo.setTipPrice(getPriceFromCache("tip_price", areaCode, equipmentType));
                priceInfo.setPeakPrice(getPriceFromCache("peak_price", areaCode, equipmentType));
                priceInfo.setFlatPrice(getPriceFromCache("flat_price", areaCode, equipmentType));
                priceInfo.setValleyPrice(getPriceFromCache("valley_price", areaCode, equipmentType));

                Log.info(EquipmentCostService.class,
                    String.format("从缓存获取分时电价 - 设备: %s, 尖: %s, 峰: %s, 平: %s, 谷: %s",
                        record.getEquipmentCode(), priceInfo.getTipPrice(), priceInfo.getPeakPrice(),
                        priceInfo.getFlatPrice(), priceInfo.getValleyPrice()));
            } else {
                // 非分时表，获取统一单价
                BigDecimal unitPrice = getPriceFromCache("unit_price", areaCode, equipmentType);
                priceInfo.setUnitPrice(unitPrice);

                Log.info(EquipmentCostService.class,
                    String.format("从缓存获取统一单价 - 设备: %s, 单价: %s",
                        record.getEquipmentCode(), unitPrice));
            }

            return priceInfo;

        } catch (Exception e) {
            Log.error(EquipmentCostService.class,
                String.format("从缓存获取电价信息失败: %s", e.getMessage()));
            return getDefaultPriceInfo(record);
        }
    }

    /**
     * 从缓存获取具体的电价
     */
    private BigDecimal getPriceFromCache(String priceType, String areaCode, String equipmentType) {
        try {
            // 构建缓存键：price_type_area_equipment
            String cacheKey = String.format("%s_%s_%s", priceType, areaCode, equipmentType);

            // 从Redis缓存获取
            String priceStr = redisService.get(cacheKey);
            if (priceStr != null && !priceStr.isEmpty()) {
                return new BigDecimal(priceStr);
            }

            // 缓存未命中，从数据库获取并缓存
            BigDecimal price = getPriceFromDatabase(priceType, areaCode, equipmentType);
            if (price != null) {
                // 缓存1小时
                redisService.setex(cacheKey, 3600, price.toString());
                return price;
            }

            // 返回默认价格
            return getDefaultPrice(priceType, equipmentType);

        } catch (Exception e) {
            Log.error(EquipmentCostService.class,
                String.format("从缓存获取电价失败 - 类型: %s, 区域: %s, 设备: %s, 错误: %s",
                    priceType, areaCode, equipmentType, e.getMessage()));
            return getDefaultPrice(priceType, equipmentType);
        }
    }

    /**
     * 从数据库获取电价
     */
    private BigDecimal getPriceFromDatabase(String priceType, String areaCode, String equipmentType) {
        try {
            String sql = "SELECT config_value FROM tb_sys_config " +
                        "WHERE config_key = ? AND area_code = ? AND equipment_type = ? AND status = 1";

            String configKey = String.format("%s_%s", priceType, equipmentType);
            List<Map<String, Object>> results = dbService.queryList(sql, configKey, areaCode, equipmentType);

            if (!results.isEmpty()) {
                Object value = results.get(0).get("config_value");
                return value != null ? new BigDecimal(value.toString()) : null;
            }

            // 如果区域特定配置不存在，尝试获取全局配置
            sql = "SELECT config_value FROM tb_sys_config " +
                  "WHERE config_key = ? AND (area_code IS NULL OR area_code = '') AND equipment_type = ? AND status = 1";

            results = dbService.queryList(sql, configKey, equipmentType);
            if (!results.isEmpty()) {
                Object value = results.get(0).get("config_value");
                return value != null ? new BigDecimal(value.toString()) : null;
            }

            return null;

        } catch (Exception e) {
            Log.error(EquipmentCostService.class,
                String.format("从数据库获取电价失败: %s", e.getMessage()));
            return null;
        }
    }

    /**
     * 获取默认电价
     */
    private BigDecimal getDefaultPrice(String priceType, String equipmentType) {
        // 根据设备类型和价格类型返回默认价格
        switch (equipmentType) {
            case "1": // 电表
                switch (priceType) {
                    case "tip_price": return new BigDecimal("1.5860");
                    case "peak_price": return new BigDecimal("1.5000");
                    case "flat_price": return new BigDecimal("1.2000");
                    case "valley_price": return new BigDecimal("0.9850");
                    case "unit_price": return new BigDecimal("1.2000");
                }
                break;
            case "2": // 水表
                return new BigDecimal("3.0000");
            case "4": // 热水表
                return new BigDecimal("4.0000");
            case "221": // 气表
                return new BigDecimal("2.5000");
        }
        return new BigDecimal("1.0000");
    }

    /**
     * 获取默认电价信息
     */
    private PriceInfo getDefaultPriceInfo(EquipmentReadingRecord record) {
        PriceInfo priceInfo = new PriceInfo();
        String equipmentType = String.valueOf(record.getEquipmentType());

        if (record.getIsTimeSharing() == 1 && "1".equals(equipmentType)) {
            // 分时电表默认价格
            priceInfo.setTipPrice(new BigDecimal("1.5860"));
            priceInfo.setPeakPrice(new BigDecimal("1.5000"));
            priceInfo.setFlatPrice(new BigDecimal("1.2000"));
            priceInfo.setValleyPrice(new BigDecimal("0.9850"));
        } else {
            // 非分时表默认价格
            priceInfo.setUnitPrice(getDefaultPrice("unit_price", equipmentType));
        }

        Log.warn(EquipmentCostService.class,
            String.format("使用默认电价 - 设备: %s, 类型: %s",
                record.getEquipmentCode(), equipmentType));

        return priceInfo;
    }

    /**
     * 计算费用（包含电价信息）
     */
    private CostCalculationResult calculateCostWithPrice(EquipmentReadingRecord record, PriceInfo priceInfo) {
        CostCalculationResult result = new CostCalculationResult();

        try {
            if (record.getIsTimeSharing() == 1 && record.getEquipmentType() == 1) {
                // 分时电表费用计算
                result = calculateTimeSharingCost(record, priceInfo);
            } else {
                // 非分时表费用计算
                result = calculateNormalCost(record, priceInfo);
            }

            Log.info(EquipmentCostService.class,
                String.format("费用计算完成 - 设备: %s, 总费用: %s",
                    record.getEquipmentCode(), result.getTotalCost()));

            return result;

        } catch (Exception e) {
            Log.error(EquipmentCostService.class,
                String.format("费用计算失败: %s", e.getMessage()));
            return new CostCalculationResult(); // 返回空结果
        }
    }

    /**
     * 计算分时费用
     */
    private CostCalculationResult calculateTimeSharingCost(EquipmentReadingRecord record, PriceInfo priceInfo) {
        CostCalculationResult result = new CostCalculationResult();

        // 设置用量
        result.setTipUsage(record.getTipUsage() != null ? record.getTipUsage() : BigDecimal.ZERO);
        result.setPeakUsage(record.getPeakUsage() != null ? record.getPeakUsage() : BigDecimal.ZERO);
        result.setFlatUsage(record.getFlatUsage() != null ? record.getFlatUsage() : BigDecimal.ZERO);
        result.setValleyUsage(record.getValleyUsage() != null ? record.getValleyUsage() : BigDecimal.ZERO);

        // 计算各时段费用
        BigDecimal tipCost = result.getTipUsage().multiply(priceInfo.getTipPrice());
        BigDecimal peakCost = result.getPeakUsage().multiply(priceInfo.getPeakPrice());
        BigDecimal flatCost = result.getFlatUsage().multiply(priceInfo.getFlatPrice());
        BigDecimal valleyCost = result.getValleyUsage().multiply(priceInfo.getValleyPrice());

        result.setTipCost(tipCost);
        result.setPeakCost(peakCost);
        result.setFlatCost(flatCost);
        result.setValleyCost(valleyCost);

        // 计算总费用
        BigDecimal totalCost = tipCost.add(peakCost).add(flatCost).add(valleyCost);
        result.setTotalCost(totalCost);

        Log.info(EquipmentCostService.class,
            String.format("分时费用计算 - 设备: %s, 尖: %s×%s=%s, 峰: %s×%s=%s, 平: %s×%s=%s, 谷: %s×%s=%s, 总计: %s",
                record.getEquipmentCode(),
                result.getTipUsage(), priceInfo.getTipPrice(), tipCost,
                result.getPeakUsage(), priceInfo.getPeakPrice(), peakCost,
                result.getFlatUsage(), priceInfo.getFlatPrice(), flatCost,
                result.getValleyUsage(), priceInfo.getValleyPrice(), valleyCost,
                totalCost));

        return result;
    }

    /**
     * 计算非分时费用
     */
    private CostCalculationResult calculateNormalCost(EquipmentReadingRecord record, PriceInfo priceInfo) {
        CostCalculationResult result = new CostCalculationResult();

        BigDecimal usage = record.getUsageAmount() != null ? record.getUsageAmount() : BigDecimal.ZERO;
        BigDecimal totalCost = usage.multiply(priceInfo.getUnitPrice());

        result.setTotalCost(totalCost);

        Log.info(EquipmentCostService.class,
            String.format("非分时费用计算 - 设备: %s, 用量: %s, 单价: %s, 费用: %s",
                record.getEquipmentCode(), usage, priceInfo.getUnitPrice(), totalCost));

        return result;
    }

    /**
     * 计算费用
     */
    private CostCalculationResult calculateCost(EquipmentReadingRecord record) {
        CostCalculationResult result = new CostCalculationResult();

        if (record.getUnitPrice() == null || record.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
            // 如果没有单价，使用默认单价
            BigDecimal defaultPrice = getDefaultUnitPrice(record.getEquipmentType());
            record.setUnitPrice(defaultPrice);
        }

        if (record.isTimeSharingMeter()) {
            // 分时表计算
            calculateTimeSharingCost(record, result);
        } else {
            // 普通表计算
            calculateNormalCost(record, result);
        }

        return result;
    }

    /**
     * 计算普通表费用
     */
    private void calculateNormalCost(EquipmentReadingRecord record, CostCalculationResult result) {
        if (record.getUsageAmount() != null && record.getUnitPrice() != null) {
            BigDecimal totalCost = record.getUsageAmount().multiply(record.getUnitPrice())
                    .setScale(2, RoundingMode.HALF_UP);
            result.setTotalCost(totalCost);
        }
    }

    /**
     * 计算分时表费用
     */
    private void calculateTimeSharingCost(EquipmentReadingRecord record, CostCalculationResult result) {
        BigDecimal totalCost = BigDecimal.ZERO;

        // 获取分时电价配置
        TimeSharingPrice prices = getTimeSharingPrices(record.getEquipmentType());

        // 计算各时段费用
        if (record.getTipReading() != null) {
            BigDecimal tipUsage = calculateTimePeriodUsage(record.getEquipmentCode(), record.getTipReading(), "tip");
            BigDecimal tipCost = tipUsage.multiply(prices.getTipPrice()).setScale(2, RoundingMode.HALF_UP);
            result.setTipUsage(tipUsage);
            result.setTipCost(tipCost);
            totalCost = totalCost.add(tipCost);
        }

        if (record.getPeakReading() != null) {
            BigDecimal peakUsage = calculateTimePeriodUsage(record.getEquipmentCode(), record.getPeakReading(), "peak");
            BigDecimal peakCost = peakUsage.multiply(prices.getPeakPrice()).setScale(2, RoundingMode.HALF_UP);
            result.setPeakUsage(peakUsage);
            result.setPeakCost(peakCost);
            totalCost = totalCost.add(peakCost);
        }

        if (record.getFlatReading() != null) {
            BigDecimal flatUsage = calculateTimePeriodUsage(record.getEquipmentCode(), record.getFlatReading(), "flat");
            BigDecimal flatCost = flatUsage.multiply(prices.getFlatPrice()).setScale(2, RoundingMode.HALF_UP);
            result.setFlatUsage(flatUsage);
            result.setFlatCost(flatCost);
            totalCost = totalCost.add(flatCost);
        }

        if (record.getValleyReading() != null) {
            BigDecimal valleyUsage = calculateTimePeriodUsage(record.getEquipmentCode(), record.getValleyReading(), "valley");
            BigDecimal valleyCost = valleyUsage.multiply(prices.getValleyPrice()).setScale(2, RoundingMode.HALF_UP);
            result.setValleyUsage(valleyUsage);
            result.setValleyCost(valleyCost);
            totalCost = totalCost.add(valleyCost);
        }

        result.setTotalCost(totalCost);
    }

    /**
     * 计算时段用量
     */
    private BigDecimal calculateTimePeriodUsage(String equipmentCode, BigDecimal currentReading, String timePeriod) {
        try {
            String sql = "SELECT " + timePeriod + "_reading FROM tb_equipment_reading_records " +
                    "WHERE equipment_code = ? AND status = 1 AND DATE(reading_date) < DATE(NOW()) " +
                    "ORDER BY reading_date DESC, reading_time DESC LIMIT 1 OFFSET 1";
            
            Object result = dbService.queryObject(sql, equipmentCode);
            if (result != null) {
                BigDecimal previousReading = new BigDecimal(result.toString());
                BigDecimal usage = currentReading.subtract(previousReading);
                return usage.compareTo(BigDecimal.ZERO) > 0 ? usage : BigDecimal.ZERO;
            }
        } catch (Exception e) {
            Log.error(EquipmentCostService.class, "计算时段用量失败: " + e.getMessage());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取读数记录
     */
    private EquipmentReadingRecord getReadingRecord(String equipmentCode,String todayStr) {
        try {
            String sql = "SELECT uid, equipment_code, equipment_type, reading_date, reading_time, " +
                    "current_reading, previous_reading, usage_amount, current_balance, unit_price, " +
                    "tip_reading, peak_reading, flat_reading, valley_reading, is_time_sharing, " +
                    "area_code, status, create_date, creator_id, modify_date, modifier_id, remark " +
                    "FROM tb_equipment_reading_records WHERE equipment_code = ? AND DATE(reading_date) = ? AND status = 1";

            java.util.Map<String, Object> result = dbService.queryMap(sql, equipmentCode,todayStr);
            if (result != null && !result.isEmpty()) {
                EquipmentReadingRecord record = new EquipmentReadingRecord();
                record.setUid((String) result.get("uid"));
                record.setEquipmentCode((String) result.get("equipment_code"));
                record.setEquipmentType((Integer) result.get("equipment_type"));
                // 使用 DateTimeUtil 处理时间类型转换
                record.setReadingDate(DateTimeUtil.convertToDate(result.get("reading_date")));
                record.setReadingTime(DateTimeUtil.convertToDate(result.get("reading_time")));

                // 处理BigDecimal字段
                if (result.get("current_reading") != null) {
                    record.setCurrentReading(new java.math.BigDecimal(result.get("current_reading").toString()));
                }
                if (result.get("previous_reading") != null) {
                    record.setPreviousReading(new java.math.BigDecimal(result.get("previous_reading").toString()));
                }
                if (result.get("usage_amount") != null) {
                    record.setUsageAmount(new java.math.BigDecimal(result.get("usage_amount").toString()));
                }
                if (result.get("current_balance") != null) {
                    record.setCurrentBalance(new java.math.BigDecimal(result.get("current_balance").toString()));
                }
                if (result.get("unit_price") != null) {
                    record.setUnitPrice(new java.math.BigDecimal(result.get("unit_price").toString()));
                }
                if (result.get("tip_reading") != null) {
                    record.setTipReading(new java.math.BigDecimal(result.get("tip_reading").toString()));
                }
                if (result.get("peak_reading") != null) {
                    record.setPeakReading(new java.math.BigDecimal(result.get("peak_reading").toString()));
                }
                if (result.get("flat_reading") != null) {
                    record.setFlatReading(new java.math.BigDecimal(result.get("flat_reading").toString()));
                }
                if (result.get("valley_reading") != null) {
                    record.setValleyReading(new java.math.BigDecimal(result.get("valley_reading").toString()));
                }

                record.setIsTimeSharing((Boolean) result.get("is_time_sharing"));
                record.setAreaCode((String) result.get("area_code"));
                record.setStatus((Integer) result.get("status"));
                record.setCreateDate(DateTimeUtil.convertToDate(result.get("create_date")));
                record.setCreatorId((String) result.get("creator_id"));
                record.setModifyDate(DateTimeUtil.convertToDate(result.get("modify_date")));
                record.setModifierId((String) result.get("modifier_id"));
                record.setRemark((String) result.get("remark"));

                return record;
            }
        } catch (Exception e) {
            Log.error(EquipmentCostService.class, "获取读数记录失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 获取默认单价（从数据库系统配置表中读取）
     */
    private BigDecimal getDefaultUnitPrice(Integer equipmentType) {
        try {
            String configKey = getUnitPriceConfigKey(equipmentType);
            if (configKey == null) {
                Log.warn(EquipmentCostService.class,
                    String.format("未知的设备类型: %d，使用默认单价1.0", equipmentType));
                return new BigDecimal("1.0");
            }

            // 从系统配置表中读取单价
            String sql = "SELECT cvalue FROM tb_sys_config WHERE cname = ?";
            String priceValue = dbService.queryOneField(sql, String.class, configKey);

            if (StringUtils.isNotBlank(priceValue)) {
                BigDecimal price = new BigDecimal(priceValue);
                Log.debug(EquipmentCostService.class,
                    String.format("设备类型 %d 从配置读取单价: %s", equipmentType, price));
                return price;
            } else {
                Log.warn(EquipmentCostService.class,
                    String.format("配置项 %s 未找到或为空，使用默认单价", configKey));
                return getHardcodedDefaultPrice(equipmentType);
            }

        } catch (Exception e) {
            Log.error(EquipmentCostService.class,
                String.format("读取设备类型 %d 的单价配置失败: %s", equipmentType, e.getMessage()));
            return getHardcodedDefaultPrice(equipmentType);
        }
    }

    /**
     * 根据设备类型获取对应的配置键名
     */
    private String getUnitPriceConfigKey(Integer equipmentType) {
        switch (equipmentType) {
            case 1: return "ele_unit_price";     // 电表单价
            case 2: return "water_unit_price";   // 水表单价
            case 4: return "hot_water_price";    // 热水表单价
            case 221: return "air_price";        // 气表单价
            default: return null;
        }
    }

    /**
     * 获取硬编码的默认单价（作为兜底方案）
     */
    private BigDecimal getHardcodedDefaultPrice(Integer equipmentType) {
        switch (equipmentType) {
            case 1: return new BigDecimal("1.0");   // 电表默认单价
            case 2: return new BigDecimal("1.0");   // 水表默认单价
            case 4: return new BigDecimal("1.0");   // 热水表默认单价
            case 221: return new BigDecimal("1.0"); // 气表默认单价
            default: return new BigDecimal("1.0");
        }
    }

    /**
     * 获取分时电价配置（从数据库系统配置表中读取）
     */
    private TimeSharingPrice getTimeSharingPrices(Integer equipmentType) {
        TimeSharingPrice prices = new TimeSharingPrice();

        if (equipmentType == 1) { // 电表，使用分时电价
            try {
                // 使用 SystemConfigService 读取分时电价配置
                prices.setTipPrice(systemConfigService.getPriceConfig("tip_price", "1.2"));     // 尖时电价
                prices.setPeakPrice(systemConfigService.getPriceConfig("peak_price", "0.8"));   // 峰时电价
                prices.setFlatPrice(systemConfigService.getPriceConfig("flat_price", "0.6"));   // 平时电价
                prices.setValleyPrice(systemConfigService.getPriceConfig("valley_price", "0.3")); // 谷时电价

                Log.debug(EquipmentCostService.class,
                    String.format("电表分时电价配置 - 尖时:%.2f, 峰时:%.2f, 平时:%.2f, 谷时:%.2f",
                        prices.getTipPrice(), prices.getPeakPrice(),
                        prices.getFlatPrice(), prices.getValleyPrice()));

            } catch (Exception e) {
                Log.error(EquipmentCostService.class, "读取分时电价配置失败，使用硬编码默认值: " + e.getMessage());
                // 使用硬编码默认值
                prices.setTipPrice(new BigDecimal("1.2"));
                prices.setPeakPrice(new BigDecimal("0.8"));
                prices.setFlatPrice(new BigDecimal("0.6"));
                prices.setValleyPrice(new BigDecimal("0.3"));
            }
        } else {
            // 其他类型使用统一价格
            BigDecimal defaultPrice = getDefaultUnitPrice(equipmentType);
            prices.setTipPrice(defaultPrice);
            prices.setPeakPrice(defaultPrice);
            prices.setFlatPrice(defaultPrice);
            prices.setValleyPrice(defaultPrice);

            Log.debug(EquipmentCostService.class,
                String.format("设备类型 %d 使用统一单价: %.2f", equipmentType, defaultPrice));
        }

        return prices;
    }



    /**
     * 电价信息类
     */
    private static class PriceInfo {
        private BigDecimal unitPrice;      // 统一单价
        private BigDecimal tipPrice;       // 尖时电价
        private BigDecimal peakPrice;      // 峰时电价
        private BigDecimal flatPrice;      // 平时电价
        private BigDecimal valleyPrice;    // 谷时电价

        public BigDecimal getUnitPrice() {
            return unitPrice != null ? unitPrice : BigDecimal.ZERO;
        }

        public void setUnitPrice(BigDecimal unitPrice) {
            this.unitPrice = unitPrice;
        }

        public BigDecimal getTipPrice() {
            return tipPrice != null ? tipPrice : BigDecimal.ZERO;
        }

        public void setTipPrice(BigDecimal tipPrice) {
            this.tipPrice = tipPrice;
        }

        public BigDecimal getPeakPrice() {
            return peakPrice != null ? peakPrice : BigDecimal.ZERO;
        }

        public void setPeakPrice(BigDecimal peakPrice) {
            this.peakPrice = peakPrice;
        }

        public BigDecimal getFlatPrice() {
            return flatPrice != null ? flatPrice : BigDecimal.ZERO;
        }

        public void setFlatPrice(BigDecimal flatPrice) {
            this.flatPrice = flatPrice;
        }

        public BigDecimal getValleyPrice() {
            return valleyPrice != null ? valleyPrice : BigDecimal.ZERO;
        }

        public void setValleyPrice(BigDecimal valleyPrice) {
            this.valleyPrice = valleyPrice;
        }
    }

    /**
     * 费用计算结果内部类
     */
    private static class CostCalculationResult {
        private BigDecimal totalCost = BigDecimal.ZERO;
        private BigDecimal tipUsage = BigDecimal.ZERO;
        private BigDecimal tipCost = BigDecimal.ZERO;
        private BigDecimal peakUsage = BigDecimal.ZERO;
        private BigDecimal peakCost = BigDecimal.ZERO;
        private BigDecimal flatUsage = BigDecimal.ZERO;
        private BigDecimal flatCost = BigDecimal.ZERO;
        private BigDecimal valleyUsage = BigDecimal.ZERO;
        private BigDecimal valleyCost = BigDecimal.ZERO;

        // getter和setter方法
        public BigDecimal getTotalCost() { return totalCost; }
        public void setTotalCost(BigDecimal totalCost) { this.totalCost = totalCost; }
        public BigDecimal getTipUsage() { return tipUsage; }
        public void setTipUsage(BigDecimal tipUsage) { this.tipUsage = tipUsage; }
        public BigDecimal getTipCost() { return tipCost; }
        public void setTipCost(BigDecimal tipCost) { this.tipCost = tipCost; }
        public BigDecimal getPeakUsage() { return peakUsage; }
        public void setPeakUsage(BigDecimal peakUsage) { this.peakUsage = peakUsage; }
        public BigDecimal getPeakCost() { return peakCost; }
        public void setPeakCost(BigDecimal peakCost) { this.peakCost = peakCost; }
        public BigDecimal getFlatUsage() { return flatUsage; }
        public void setFlatUsage(BigDecimal flatUsage) { this.flatUsage = flatUsage; }
        public BigDecimal getFlatCost() { return flatCost; }
        public void setFlatCost(BigDecimal flatCost) { this.flatCost = flatCost; }
        public BigDecimal getValleyUsage() { return valleyUsage; }
        public void setValleyUsage(BigDecimal valleyUsage) { this.valleyUsage = valleyUsage; }
        public BigDecimal getValleyCost() { return valleyCost; }
        public void setValleyCost(BigDecimal valleyCost) { this.valleyCost = valleyCost; }
    }

    /**
     * 分时电价内部类
     */
    private static class TimeSharingPrice {
        private BigDecimal tipPrice;
        private BigDecimal peakPrice;
        private BigDecimal flatPrice;
        private BigDecimal valleyPrice;

        // getter和setter方法
        public BigDecimal getTipPrice() { return tipPrice; }
        public void setTipPrice(BigDecimal tipPrice) { this.tipPrice = tipPrice; }
        public BigDecimal getPeakPrice() { return peakPrice; }
        public void setPeakPrice(BigDecimal peakPrice) { this.peakPrice = peakPrice; }
        public BigDecimal getFlatPrice() { return flatPrice; }
        public void setFlatPrice(BigDecimal flatPrice) { this.flatPrice = flatPrice; }
        public BigDecimal getValleyPrice() { return valleyPrice; }
        public void setValleyPrice(BigDecimal valleyPrice) { this.valleyPrice = valleyPrice; }
    }
}
