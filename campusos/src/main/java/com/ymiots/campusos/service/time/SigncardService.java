package com.ymiots.campusos.service.time;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;

@Repository
public class SigncardService extends BaseService{
    @Autowired
    UserRedis userredis;

    @Autowired
    SysLogsService syslogs;

    public JsonData getSigncardList(HttpServletRequest request, HttpServletResponse response, String key,String workdate,String auditingstatus, int start, int limit){
        String fields = "tsc.uid,date_format(tsc.signtime, '%Y-%m-%d %H:%i') as signtime,date_format(tsc.workdate, '%Y-%m-%d') as workdate,tsc.remark,tsc.cardindex,tsc.ftype," +
                "date_format(tsc.createdate, '%Y-%m-%d %H:%i:%s') as createdate,date_format(tsc.modifydate, '%Y-%m-%d %H:%i:%s') as modifydate,date_format(tsc.auditingdate, '%Y-%m-%d %H:%i:%s') as auditingdate,tsc.auditing," +
                "ct.name,ct.code ,getorgname(ct.orgcode) as orgname,u.name as createname,u2.name as modifiername,u3.name as auditingname ";
        String table = "tb_time_signcard tsc " +
                "LEFT JOIN tb_card_teachstudinfo ct ON ct.uid = tsc.infoid  " +
                "LEFT JOIN tb_sys_user u ON u.uid = tsc.creatorid " +
                "LEFT JOIN tb_sys_user u2 ON u2.uid = tsc.modifierid " +
                "LEFT JOIN tb_sys_user u3 ON u3.uid = tsc.auditingid ";
        String where = "1=1";
        String orderby = ExtSort.Orderby(request, "tsc.createdate DESC");
        SysUser sysUser = userredis.get(request);
        String orgcode = "(";
        JSONArray list = dbService.QueryList("select orgcode from tb_card_orgframework_user where userid = ? order by orgcode", sysUser.getUid());
        if (list.size()>0){
            for (int i = 0; i < list.size(); i++) {
                String orgcode1 = list.getJSONObject(i).getString("orgcode");
                orgcode+="'"+orgcode1+"'";
                if (i+1<list.size()){
                    orgcode+=",";
                }else {
                    orgcode+=")";
                }
            }
        }
        if (StringUtils.isNotBlank(orgcode)&&orgcode.length()>2) {
            where += " and ct.orgcode in "+orgcode;
        }
        if (StringUtils.isNotBlank(key)) {
            where += " and (ct.name like '%"+key+"%' or ct.code='"+key+"')";
        }
        if (StringUtils.isNotBlank(auditingstatus)&& Integer.parseInt(auditingstatus)>0) {
            where += " and tsc.auditing="+auditingstatus;
        }
        if (StringUtils.isNotBlank(workdate)) {
            where += " and tsc.workdate='"+workdate+"'";
        }
        JsonData jd = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return jd;
    }

    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response,String orgcode,String key,boolean viewchild,String selecteduid, int start,int limit){
        String fields="b.uid,b.code,b.name,b.sex,b.card,b.cardsn,b.orgcode,b.focus,b.status, getorgname(b.orgcode) as orgname";
        String table="tb_card_teachstudinfo b";
        if(userredis.get(request).getUsertype()==1){
            String userid=userredis.getUserId(request);
            table+= " inner join tb_card_orgframework_user uo on uo.orgcode=b.orgcode and uo.userid='"+userid+"' ";
        }
        String where=" b.status=1 ";
        if(!StringUtils.isBlank(orgcode)){
            if(viewchild){
                where+=" and b.orgcode like '"+orgcode+"%' ";
            }else{
                where+=" and b.orgcode='"+orgcode+"' ";
            }
        }
        if(!StringUtils.isBlank(key)){
            where+=" and (b.code='"+key+"' or b.name like '%"+key+"%' or b.mobile like '%"+key+"%' or b.card='"+ ComHelper.LeftPad(key, 12, '0')+"' or b.cardsn='"+ComHelper.LeftPad(key, 12, '0')+"' )";
        }
        JSONArray Selected=JSONArray.parseArray(selecteduid);
        if(Selected.size()>0){
            String[] uidx= ComHelper.ListToArray(Selected);
            where+=" and b.uid not in('"+String.join("','", uidx)+"')";
        }
        String orderby=ExtSort.Orderby(request, "b.createdate desc");
        JsonData result=dbService.QueryJsonData( fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonResult saveSigncard(HttpServletRequest request, HttpServletResponse response,String uid, String infoid, 
    		String signtime,String cardindex,String ftype, String remark,String auditing) throws ParseException {
        String creatorid = userredis.getUserId(request);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String workdate = sdf.format(sdf.parse(signtime));
        if(StringUtils.isBlank(uid)){
            String sql = "";
            if("2".equals(auditing)){
                sql = "INSERT INTO tb_time_signcard(uid,infoid,signtime,workdate,cardindex,ftype,remark,createdate,creatorid,modifydate,modifierid,auditing,status) " +
                        "SELECT uuid(),uid,'"+signtime+"','"+workdate+"',"+cardindex+","+ftype+",'"+remark+"',now(),'"+creatorid+"',now(),'"+creatorid+"','"+auditing+"',1 FROM tb_card_teachstudinfo WHERE LOCATE(uid,'" + String.join(",", infoid) + "')>0";
            }else if("1".equals(auditing)){
                sql = "INSERT INTO tb_time_signcard(uid,infoid,signtime,workdate,cardindex,ftype,remark,createdate,creatorid,modifydate,modifierid,auditingdate,auditingid,auditing,status) " +
                        "SELECT uuid(),uid,'"+signtime+"','"+workdate+"',"+cardindex+","+ftype+",'"+remark+"',now(),'"+creatorid+"',now(),'"+creatorid+"',now(),'"+creatorid+"','"+auditing+"',1 FROM tb_card_teachstudinfo WHERE LOCATE(uid,'" + String.join(",", infoid) + "')>0";
            }
            //JSONObject person = dbService.QueryJSONObject("select code,orgcode,name from tb_card_teachstudinfo where uid = ?", infoid);
            //JSONObject dev = dbService.QueryJSONObject("select machineid from tb_dev_accesscontroller order by livestime limit 1");
            //String timeSql= "insert into tb_time_records(uid,infoid,infoname,infocode,orgcode,recordtime,machineid,createdate,status) value(uuid(),?,?,?,?,?,?,now(),1)";
            //dbService.excuteSql(timeSql,infoid,person.getString("name"),person.getString("code"),person.getString("orgcode"),signtime,dev.getString("machineid"));
            dbService.excuteSql(sql);
            syslogs.Write(request, "签卡管理",String.format("添加签卡信息,人员：%s",infoid));
        }else{
            if("2".equals(auditing)){
                String updatesql = "UPDATE tb_time_signcard SET signtime=?,workdate=?,cardindex=?,ftype=?,remark=?,modifydate=now(),modifierid=?,auditingid=null,auditingdate=null,auditing=? WHERE uid=?";
                dbService.excuteSql(updatesql,signtime,workdate,cardindex,ftype, remark,creatorid,auditing,uid);
            }else if("1".equals(auditing)){
                String updatesql = "UPDATE tb_time_signcard SET signtime=?,workdate=?,cardindex=?,ftype=?,remark=?,modifydate=now(),modifierid=?,auditingid=?,auditingdate=now(),auditing=? WHERE uid=?";
                dbService.excuteSql(updatesql,signtime,workdate,cardindex,ftype, remark,creatorid,creatorid,auditing,uid);
            }
            syslogs.Write(request, "签卡管理",String.format("修改签卡信息,uid:%s",uid));
        }
        return Json.getJsonResult(true);
    }

    public JsonResult delSigncard(HttpServletRequest request, HttpServletResponse response, String uid){
        if(StringUtils.isNotBlank(uid)){
            String sql = "DELETE FROM tb_time_signcard WHERE LOCATE(uid,'" + String.join(",", uid) + "')>0";
            dbService.excuteSql(sql);
            syslogs.Write(request, "签卡管理",String.format("删除签卡信息,uid:%s",uid));
            return Json.getJsonResult(true);
        }else {
            return Json.getJsonResult(false,"uid为空");
        }
    }

    public JsonResult auditing(HttpServletRequest request, HttpServletResponse response,String uid,String auditingstatus){
        String auditingid = userredis.getUserId(request);
        if (StringUtils.isNotBlank(uid)) {
            if("1".equals(auditingstatus)){
                String updateSql = "UPDATE tb_time_signcard SET auditingid=?,auditing=1,auditingdate=now() WHERE auditing=2 and LOCATE(uid,'" + String.join(",", uid) + "')>0";
                dbService.excuteSql(updateSql,auditingid);
                syslogs.Write(request, "签卡管理",String.format("审核，uid:%s",uid));
            }else if("2".equals(auditingstatus)){
                String updateSql = "UPDATE tb_time_signcard SET auditingid=?,auditing=2,auditingdate=now() WHERE auditing=1 and LOCATE(uid,'" + String.join(",", uid) + "')>0";
                dbService.excuteSql(updateSql,auditingid);
                syslogs.Write(request, "签卡管理",String.format("反审，uid:%s",uid));
            }
            return Json.getJsonResult(true);
        }else{
            return Json.getJsonResult(false,"uid为空");
        }
    }
}
