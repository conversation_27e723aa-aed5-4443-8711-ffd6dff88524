package com.ymiots.campusos.service.thirdapi.Impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.DBService;
import com.ymiots.campusos.entity.jjoa.DeptDto;
import com.ymiots.campusos.entity.jjoa.PersonInfo;
import com.ymiots.campusos.service.thirdapi.JingJiaOAService;
import com.ymiots.campusos.util.HttpRequestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create-date 2024/12/11 15:16
 */
@Service
public class JingJiaOAServiceImpl implements JingJiaOAService {
    @Autowired
    HttpRequestUtils httpRequestUtils;

    @Autowired
    DBService dbService;
    @Override
    public void PersonInfoSync() throws IOException, URISyntaxException {
        String responseStr = httpRequestUtils.doJJPersonGetRequest("http://58.60.3.180:8084/restcloud/oa/api/MealEmployeeInfoService?appkey=62294ea53d7c97248778c4c4", null, null);
        JSONArray jsonArray = dbService.QueryList("select code,name from tb_card_orgframework order by code");
        Map<String,String> dept = new HashMap<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String name = jsonObject.getString("name");
            String code = jsonObject.getString("code");
            if (!dept.containsKey(name)){
               dept.put(name,code);
            }
        }
        JSONArray personArray = dbService.QueryList("select uid,code from tb_card_teachstudinfo order by code");
        Map<String,String> person = new HashMap<>();
        for (int i = 0; i < personArray.size(); i++) {
            JSONObject jsonObject = personArray.getJSONObject(i);
            String code = jsonObject.getString("code");
            String uid = jsonObject.getString("uid");
            if (!person.containsKey(code)){
                person.put(code,uid);
            }
        }
        StringBuilder insertsql = new StringBuilder("insert into tb_card_teachstudinfo(uid,code,name,orgcode,mobile,infolabel,infotype,status,property,createdate,creatorid) VALUES ");
        if (StringUtils.isNotEmpty(responseStr)){
            StringBuilder builder = new StringBuilder();
            List<PersonInfo> personInfos = JSONObject.parseArray(responseStr, PersonInfo.class);
            for (PersonInfo personInfo : personInfos) {
                if (!person.containsKey(personInfo.getEmpcode())){
                    String orgcode= "";
                    if (StringUtils.isNotEmpty(personInfo.getCompanyname())){
                        JSONObject object = dbService.QueryJSONObject("select code from tb_card_orgframework where name = '" + personInfo.getCompanyname() + "' limit 1");
                        if (object!=null){
                            orgcode = object.getString("code");
                        }
                    }else {
                        orgcode = "001";
                    }
                builder.append("(uuid(),'").append(personInfo.getEmpcode()).append("','").append(personInfo.getEmpname()).append("','").append(orgcode).append("','").append(personInfo.getMobile())
                        .append("','").append(personInfo.getJobtitlename()).append("',").append(1).append(",").append(1).append(",").append(personInfo.getStatus()).append(",now(),'OA数据同步'),");
                }else {
                    int count = dbService.getCount("tb_card_teachstudinfo", " code = '" + personInfo.getEmpcode() + "' and name = '"+personInfo.getEmpname()+"' and orgcode='"+dept.get(personInfo.getCompanyname())+"' and mobile='"+personInfo.getMobile()+"' and  infolabel= '"+personInfo.getJobtitlename()+"' ");
                    if (count==0){
                        dbService.excuteSql("update tb_card_teachstudinfo set code=?,name=?,orgcode=?,mobile=?,infolabel=?,property=? where uid = ?",personInfo.getEmpcode(),personInfo.getEmpname(),dept.get(personInfo.getCompanyname()),personInfo.getMobile(),personInfo.getJobtitlename(),personInfo.getStatus(),person.get(personInfo.getEmpcode()));
                    }
                }
                person.remove(personInfo.getEmpcode());
            }
            for (String key : person.keySet()) {
                dbService.excuteSql("update tb_card_teachstudinfo set status=0 where code = ?",key);
            }
            if (builder.length()>0){
                dbService.excuteSql(insertsql.append(builder.toString().substring(0,builder.length()-1)).toString());
            }
        }
    }

    @Override
    public void DeptSync() throws IOException, URISyntaxException {
        DeptDto deptDto = httpRequestUtils.doJJDeptGetRequest("http://58.60.3.180:8084/restcloud/oa/api/OaOrgInfoService?appkey=62294ea53d7c97248778c4c4", null, null, DeptDto.class);

        int count = dbService.getCount("tb_card_orgframework", " uid = '" + deptDto.getId() + "' ");
        String insert = "insert into tb_card_orgframework(uid,code,orgtype,name,parentid,createdate,status) VALUES(?,?,2,?,?,now(),1)";
        if (count==0){
            dbService.excuteSql(insert,deptDto.getId(),"001",deptDto.getTitle(),"");
        }
        String lastCode = "001";
        List<DeptDto> children = deptDto.getChildren();
        Integer beginNum = 1;
        for (DeptDto child : children) {
            int countchild = dbService.getCount("tb_card_orgframework", " uid = '" + child.getId() + "' ");
            String code = lastCode + String.format("%03d", beginNum);
            if (countchild==0){
                dbService.excuteSql(insert,child.getId(),code,child.getTitle(),child.getPid());
            }else {
                if (StringUtils.isNotEmpty(child.getPid())&&child.getId().equals("1")){
                    code = lastCode + String.format("%03d", beginNum);
                    int count1 = dbService.getCount("tb_card_orgframework", " uid = '" + code + "' ");
                    if (count1==0){
                        dbService.excuteSql(insert,code,code,child.getTitle(),child.getPid());
                    }
                }
            }
            beginNum++;
        }
    }
}
