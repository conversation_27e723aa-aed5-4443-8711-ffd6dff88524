package com.ymiots.campusos.service;

import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymiots.campusos.config.redis.RedisClient;
import com.ymiots.campusos.util.IPUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.CallBackFilter;
import com.ymiots.framework.common.CampusLicense;
import com.ymiots.framework.common.CollectionUtils;
import com.ymiots.framework.common.ComHelper;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.HttpRequest;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.common.MD5;
import com.ymiots.framework.common.RandomHelper;
import com.ymiots.framework.common.TreeHelper;

@Service
@Repository
public class SysUserService extends BaseService{

	@Autowired
	UserRedis userredis;

	@Autowired
	RedisClient redisclient;

	@Autowired
	SysLogsService syslogs;

	@Autowired
	SysConfigService sysconfig;

	@Autowired
	SysLicenseService license;

    public JsonResult Login(HttpServletRequest request, HttpServletResponse response, String code, String pwd) throws IOException {
        if (StringUtils.isBlank(code)) {
            return Json.getJsonResult("系统账号为空");
        }
        if (StringUtils.isBlank(pwd)) {
            return Json.getJsonResult("登录密码为空");
        }
		//判断是否需要检测
		if (!StringUtils.isBlank(WebConfig.getWxAppId())) {
			String clientIp = getClientIP(request);
			if (IPUtils.isInternalIP(clientIp)) {
				syslogs.Write(request, "系统账号", String.format("账号（%s）内网访问", code));
			} else {
				syslogs.Write(request, "系统账号", String.format("账号（%s）外网访问", code));
				String paid = isServicePaid(request);
				if ("0".equals(paid)) {
					return Json.getJsonResult("外网服务器已到期，请联系管理员！！！");
				}
			}
		}
        String sql = "select uid,code,name,empid,usertype,status from tb_sys_user where code=? and password=?;";
        SysUser user = dbService.QueryObject(sql, SysUser.class, code, MD5.MD5Encode(pwd));
        if (user == null) {
            syslogs.Write(request, "系统账号", String.format("账号（%s）密码（%s）登录失败", code, pwd));
            return Json.getJsonResult("账号密码错误");
        }
        if (user.getUsertype() < 1) {
            syslogs.Write(request, "系统账号", String.format("账号（%s）级别不够，拒绝登录系统", code));
            return Json.getJsonResult("用户级别不够");
        }
        if (user.getStatus() == 0) {
            syslogs.Write(request, "系统账号", String.format("账号（%s）状态已锁定，拒绝登录系统", code));
            return Json.getJsonResult("用户状态已锁定");
        }
        String userId = userredis.getUserId(request);
        if (!StringUtils.isBlank(userId) && userId.equals(user.getUid())) {
            if (userredis.exists(request, userId)) {
                redisclient.deleteStringKey("menus");
                syslogs.Write(request, "系统账号", String.format("用户（%s）非正常退出，返回系统成功", code));
                return Json.getJsonResult(true);
            }
        }

        if (user.getUsertype() != 9) {
            int count = dbService.getCount("tb_sys_user", "usertype in(1,2)");
            if (count > CampusLicense.getLicenseMaxuser()) {
                return Json.getJsonResult(String.format("许可证授权%s个用户，非法操作导致用户数超过上限！", CampusLicense.getLicenseMaxuser()));
            }
        }
        if (user.getUsertype() == 9) {
            //超级管理员或开发人员无需授权
            sql = "SELECT uid as menuid,code,iconcls as iconCls,menuos,dynamiccode,menutype,name as text,parentid,viewclass,opbutton,sort,maxopbutton FROM tb_sys_menu where status=1;";
        }else if (user.getUsertype() == 2){
			sql = "SELECT uid as menuid,code,iconcls as iconCls,menuos,dynamiccode,menutype,name as text,parentid,viewclass,opbutton,sort,maxopbutton FROM tb_sys_menu where status=1 and name != '微信菜单' ;";
		}
		else {
            sql = "select rm.menuid,m.code,m.iconcls as iconCls,m.menuos,m.dynamiccode,menutype,m.name as text,m.parentid,m.viewclass,rm.opbutton,m.sort,m.maxopbutton from tb_sys_role_menu rm inner join tb_sys_menu m on m.uid=rm.menuid where m.status=1 and m.name != '微信菜单' and rm.roleid in(select roleid from tb_sys_role_user where userid='" + user.getUid() + "' );";
        }
        JSONArray menus = dbService.QueryList(sql);
        if (!(user.getUsertype() == 2 || user.getUsertype() == 9)) {
            //非超级管理员和开发人员，对菜单去重和操作按钮进行叠加
            for (int i = 0; i < menus.size(); i++) {
                final JSONObject menu = menus.getJSONObject(i);
                JSONArray othermenus = CollectionUtils.where(menus, new CallBackFilter<JSONObject>() {
                    @Override
                    public Boolean filter(JSONObject model) {
                        if (model.getString("menuid").equals(menu.getString("menuid")) && !model.getString("opbutton").equals(menu.getString("opbutton"))) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                });

                JSONArray opbuttons = menu.getJSONArray("opbutton");
                for (int n = 0; n < othermenus.size(); n++) {
                    JSONObject othermenu = othermenus.getJSONObject(n);
                    opbuttons.addAll(othermenu.getJSONArray("opbutton"));
                }
                opbuttons = ComHelper.RemoveRepeat(opbuttons, "itemId");
                opbuttons = ComHelper.SortJsonArray(opbuttons, "sort");
                menu.remove("opbutton");
                menu.put("opbutton", opbuttons.toJSONString());
            }
            menus = ComHelper.RemoveRepeat(menus, "menuid");
        }
        if (menus.size() == 0) {
            return Json.getJsonResult("用户未经授权");
        }
        user.setMenu(menus.toJSONString());
        String tokenid = UUID.randomUUID().toString();
        user.setTokenid(tokenid);
        userredis.add(response, user);
        sql = "update tb_sys_user set logintime=logintime+1, lastlogindate=now(),lastloginip=? where uid=?";
        dbService.excuteSql(sql, ComHelper.getAddressIP(request), user.getUid());
        syslogs.Write(request, "系统账号", String.format("账号（%s）登录成功", code));
        return Json.getJsonResult(true);
    }

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private String isServicePaid(HttpServletRequest request) throws IOException {
        String key = "WECHAT_SERVICE_PAID";
        if ("1".equals(redisTemplate.opsForValue().get(key))) {
            return "1";
        } else {
            long timeMillis = System.currentTimeMillis();
            String signature = MD5Encrypt("appId=fbd0057b18bd457a8cbc25383e6e54a3&mpAppId="+WebConfig.getWxAppId()+"&timestamp=" + timeMillis + "&key=HySsf1xw4Ihs1rCMlEgNNJ2QDJQoH49J");
            Map<String, String> params = new HashMap<>();
            params.put("mpAppId", WebConfig.getWxAppId());
            params.put("appId", "fbd0057b18bd457a8cbc25383e6e54a3");
            params.put("timestamp", String.valueOf(timeMillis));
            params.put("signature", signature);
			JsonResult result = null;
			try {
				result = HttpRequest.HttpPost("http://47.112.126.84:2019/weChat/getServicePaid", params);
			} catch (Exception e) {
				e.printStackTrace();
			}
			if (result != null) {
				if (result.getData() != null) {
					JSONObject jsonObject = result.getData();
					if (!jsonObject.getBoolean("success")) {
						return "0";
					}
				}
			}
            redisTemplate.opsForValue().set(key, "1", 1, TimeUnit.DAYS);
            return "1";
        }
    }

    public static String MD5Encrypt(String value) {
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        try {
            byte[] btInput = value.getBytes("utf-8");
            // 获得MD5摘要算法的 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(btInput);
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String getClientIP(HttpServletRequest request) {
        String xfHeader = request.getHeader("X-Forwarded-For");
        if (xfHeader == null) {
            return request.getRemoteAddr();
        }
        return xfHeader.split(",")[0];
    }

    // 将字符串进行 SHA-256 加密
    public static String sha256(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] hash = md.digest(input.getBytes());
        StringBuilder hexString = new StringBuilder(2 * hash.length);
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

	public JsonResult Logout(HttpServletRequest request, HttpServletResponse response) {
		redisclient.deleteStringKey("menus"+userredis.get(request).getUid());
		userredis.remove(request);
		return Json.getJsonResult(true);
	}

	public JsonResult getUserInfo(HttpServletRequest request, HttpServletResponse response) {
		SysUser user=  userredis.get(request);

		if(user!=null){
			String sql = "SELECT uid,imgpath FROM tb_face_infoface WHERE infoid = '" + user.getEmpid()+ "' ORDER BY createdate ASC limit 1";
			JSONObject jsonObject = dbService.QueryObject(sql, JSONObject.class);

			JSONObject cf= sysconfig.get("websocketurl","cardtype","needvalidate","isctrlusetimesandbacktime");
			JSONObject item=new JSONObject();
			if (jsonObject!=null){
				item.put("imgpath", jsonObject.getString("imgpath"));
			}else {
				item.put("imgpath","");
			}
			item.put("userid", user.getUid());
			item.put("code", user.getCode());
			item.put("name", user.getName());
			item.put("empid", user.getEmpid());
			item.put("usertype", user.getUsertype());
			item.put("wssurl",  cf.getString("websocketurl"));
			item.put("cardtype",  cf.getString("cardtype"));
			item.put("needvalidate",  cf.getString("needvalidate"));
			item.put("isctrlusetimesandbacktime",  cf.getString("isctrlusetimesandbacktime"));
			item.put("apptype",  CampusLicense.getApptype());
			item.put("orgname", CampusLicense.getName());
			item.put("apptypename", WebConfig.getApptypename());
			String ostitle = sysconfig.get("ostitle");
			if (StringUtils.isNotEmpty(ostitle)){
				item.put("ostitle", ostitle.replace("<br>",""));
				item.put("osmaintitle", ostitle);
			}else {
				item.put("ostitle", CampusLicense.getOstitle());
			}
			item.put("resourcedomain", WebConfig.getResourceDomain());
			return Json.getJsonResult(true,item);
		}
		else{
			return Json.getJsonResult(false);
		}
	}


	public JsonResult KeepAlive(HttpServletRequest request, HttpServletResponse response) {
		String expire=userredis.keepAlive(request);
		if(expire.equals("0/0")){
			return Json.getJsonResult(false,expire);
		}else{
			return Json.getJsonResult(true,expire);
		}
	}

	public JSONArray NavList(HttpServletRequest request, HttpServletResponse response, String menuos) {
		SysUser user=  userredis.get(request);
		if(user!=null){
			 int menuosint=StringUtils.isBlank(menuos)?1:Integer.parseInt(menuos);
			 if(menuosint==0){
				 if(user.getUsertype()==2 || user.getUsertype()==9){
					 menuosint=1;
				 }else{
				     //获取人员授权系统菜单中的第一个系统序号
					 String sql="select m.menuos from tb_sys_role_menu rm inner join tb_sys_menu m on m.uid=rm.menuid where rm.roleid in(SELECT roleid FROM tb_sys_role_user where userid='"+user.getUid()+"')  and (m.parentid='' or m.parentid is null) order by sort asc limit 1;";
					 JSONArray menuosx=dbService.QueryList( sql);
					 if(menuosx.size()>0){
						 menuosint=menuosx.getJSONObject(0).getIntValue("menuos");
					 }
				 }
			 }
			// 获取系统菜单的功能菜单
			JSONArray menus = redisclient.getJSONArray("menus" + user.getUid());

			if (menus == null || menus.isEmpty()) {
				JSONArray mlist = JSONArray.parseArray(user.getMenu());
				menus = new JSONArray();

				// 封装获取完整菜单路径的方法
				Function<String, String> getMenuPath = (parentid) -> {
					StringBuilder path = new StringBuilder();
					while (StringUtils.isNotEmpty(parentid)) {
						JSONObject jsonObject = dbService.QueryObject(
								"SELECT name, parentid FROM tb_sys_menu WHERE uid = ?",
								JSONObject.class,
								parentid
						);
						if (jsonObject == null) {
							break;
						}
						path.insert(0, " / " + jsonObject.getString("name"));
						parentid = jsonObject.getString("parentid");
					}
					return path.toString();
				};

				// 遍历用户菜单列表
				for (Object m : mlist) {
					JSONObject menu = (JSONObject) m;
					String parentid = menu.getString("parentid");
					String name = " / " + menu.getString("text");
					name = getMenuPath.apply(parentid) + name;
					menu.put("textAdress", name);
					menus.add(menu);
				}

				// 缓存菜单数据到 Redis，设置过期时间为 7 天
				redisclient.setJSONArray("menus" + user.getUid(), menus);
				redisclient.setExpire("menus" + user.getUid(), 7 * 24 * 60 * 60, TimeUnit.SECONDS);
			}


			//处理成树状结构
			JSONArray rootlist= TreeHelper.findRootList(menus, "parentid", "menuid");
			rootlist=ComHelper.SortJsonArray(rootlist, "sort");
			for(Object m:rootlist){
				JSONObject menu=(JSONObject)m;

				JSONArray children=TreeHelper.getChildren(menus, "parentid",menu.getString("menuid"),"menuid",false,"sort");
				if (children.size() > 0) {
					menu.put("leaf", false);
					children=ComHelper.SortJsonArray(children, "sort");
					menu.put("children", children);
					menu.put("expanded", true);
				}else{
					menu.put("leaf", true);
					menu.put("children", new JSONArray());
					menu.put("expanded", true);
				}
			}
			//if( rootlist.size()==1){
				 if(StringUtils.isBlank(rootlist.getJSONObject(0).getString("parentid"))){
					 JSONArray osmenu= rootlist.getJSONObject(0).getJSONArray("children");
                        //处理收藏菜单
						if(menuosint==1){
							JSONArray favmenu=CollectionUtils.where(osmenu, new CallBackFilter<JSONObject>(){
								@Override
								public Boolean filter(JSONObject model) {
									   if(model.getString("menuid").equals("96cec9cf-07c6-11e8-9075-9c5c8e6dafdd")){
										   return true;
									   }else{
										   return false;
									   }
								}
							});

							if(favmenu.size()>0){
								String fields="f.menuid,m.code,m.iconcls as iconCls,m.menuos,m.dynamiccode,m.menutype,m.name as text,m.parentid,m.viewclass,rm.opbutton,f.sort,m.maxopbutton";
								String table="tb_sys_menu_userfav f inner join tb_sys_menu m on m.uid=f.menuid inner join tb_sys_role_menu rm on rm.menuid=m.uid inner join tb_sys_role_user ru on ru.roleid=rm.roleid and ru.userid='"+user.getUid()+"'";
								String where=" m.status=1 and f.userid='"+user.getUid()+"' ";
								String orderby="f.sort asc";
								if(user.getUsertype()==2 || user.getUsertype()==9){
									fields="f.menuid,m.code,m.iconcls as iconCls,m.menuos,m.dynamiccode,m.menutype,m.name as text,m.parentid,m.viewclass,m.opbutton,f.sort,m.maxopbutton";
									table="tb_sys_menu_userfav f inner join tb_sys_menu m on m.uid=f.menuid";
								}
								JSONArray favmenus=dbService.QueryJSONArray( fields, table, where, orderby, 0, 0);
								favmenus=ComHelper.SortJsonArray(favmenus, "sort");
								if(favmenus.size()>0){
									for(Object favm:favmenus){
										JSONObject favms=(JSONObject)favm;
										String parentid = favms.getString("parentid");
										String name = " / "+favms.getString("text");
										while (true){
											JSONObject jsonObject = dbService.QueryObject("SELECT name,parentid FROM tb_sys_menu WHERE uid = '" + parentid + "'", JSONObject.class);
											if (jsonObject!=null){
												name = " / "+jsonObject.getString("name")+name;

												if (StringUtils.isNotEmpty(jsonObject.getString("parentid"))) {
													parentid = jsonObject.getString("parentid");
												} else {
													break;
												}
											}else {
												break;
											}
										}
										favms.put("textAdress", name);
										favms.put("leaf", true);
									}
									favmenu.getJSONObject(0).remove("leaf");
									favmenu.getJSONObject(0).put("leaf", false);
									favmenu.getJSONObject(0).put("children", favmenus);
								}
							}
						}
						rootlist.getJSONObject(0).put("children",osmenu);
						return rootlist;
				 }
				 return rootlist;
			//}
			//return rootlist;
		}
		else{
			return new JSONArray();
		}
	}

	public JsonData OsMenusNavList(HttpServletRequest request, HttpServletResponse response) {
		SysUser user=  userredis.get(request);
		if(user!=null){
			JSONArray menus=new JSONArray();
			JSONArray mlist=JSONArray.parseArray(user.getMenu());
			for(Object m:mlist){
				JSONObject menu=(JSONObject)m;
				if(StringUtils.isBlank(menu.getString("parentid"))){
					menus.add(menu);
				}
			}
			return Json.getJsonData(true, ComHelper.SortJsonArray(menus, "sort"));
		}else{
			return Json.getJsonData(true);
		}
	}

	public JsonData getSysUserList(HttpServletRequest request, HttpServletResponse response,String usergroup, String usertype, String key,int start,int limit) {
		SysUser user=  userredis.get(request);

		String fields="uid,code,name,groupid,empid,email,phone,weixinid,usertype,logintime,lastlogindate,lastloginip,createdate,status";
		String table="tb_sys_user";

		String where=" usertype<="+String.valueOf(user.getUsertype());

		if(!StringUtils.isBlank(usertype)){
			where+=" and usertype="+usertype+" ";
		}
		if(!StringUtils.isBlank(usergroup)){
			where+=" and groupid="+usergroup+" ";
		}
		if(!StringUtils.isBlank(key)){
			where+=" and (code='"+key+"' or name like '%"+key+"%') ";
		}
		if (user.getUsertype()==1){
			List<String> ids= new ArrayList<>();
			List<String> sonids2 = getSonids2(user.getUid(), ids);
			sonids2.add(user.getUid());
			String result = sonids2.stream()
					.map(id -> "'" + id + "'")
					.collect(Collectors.joining(","));
			where+=" and uid in ("+result+")";
		}
		String sort=ExtSort.Orderby(request, "createdate desc");
		return dbService.QueryJsonData( fields, table, where, sort, start, limit);
	}

	public List<String> getSonids2(String uid,List<String> sonIds){
		JSONArray list = dbService.QueryList("SELECT b.uid  FROM tb_sys_user a INNER JOIN tb_sys_user b on a.uid = b.parentid where a.uid = ?",uid);
		if (list.size()==0){
			return sonIds;
		}
		for (int i = 0; i < list.size(); i++) {
			sonIds.add(list.getJSONObject(i).getString("uid"));
			getSonids2(list.getJSONObject(i).getString("uid"),sonIds);
		}
		return sonIds;
	}

	public JsonData getSysUserRuleList(HttpServletRequest request, HttpServletResponse response,String userid,int start,int limit) {
		String fields="ru.uid,r.name";
		String table="tb_sys_role_user ru inner join tb_sys_role r on r.uid=ru.roleid";
		String where="ru.userid='"+userid+"'";
		String sort=ExtSort.Orderby(request, "ru.createdate desc");
		return dbService.QueryJsonData( fields, table, where, sort, start, limit);
	}

	public JsonResult SaveSysUser(HttpServletRequest request, HttpServletResponse response,String uid,String code,String password,String name,String groupid,
			String empid,String email,String phone,String usertype,int status) {

		int count=dbService.getCount("tb_sys_user", "usertype in(1,2)");
		if(count>=CampusLicense.getLicenseMaxuser()) {
			return Json.getJsonResult(String.format("许可证授权%s个用户，用户数已达上限！", CampusLicense.getLicenseMaxuser()));
		}
		SysUser sysUser = userredis.get(request);
		if (sysUser.getUsertype()<Integer.valueOf(usertype)){
			return Json.getJsonResult("非法操作，低权限用户非法创建高权限用户");
		}
		String where=" code='"+code+"' ";
		if(!StringUtils.isBlank(uid)){
			where+=" and uid<>'"+uid+"' ";
		}
		count=dbService.getCount("tb_sys_user", where);
		if(count>0){
			return Json.getJsonResult("系统账号已被占用");
		}
		if(!StringUtils.isBlank(empid)){
			String sql="select code from tb_sys_user where empid='"+empid+"' ";
			if(!StringUtils.isBlank(uid)){
				sql+=" and uid<>'"+uid+"' ";
			}
			JSONArray list=dbService.QueryList( sql);
			if(list.size()>0){
				JSONObject item=list.getJSONObject(0);
				return Json.getJsonResult(String.format("档案已存在系统账号（%s）",item.getString("code")));
			}
		}
		String userid=userredis.get(request).getUid();
		if(StringUtils.isBlank(uid)){
			int usercount=dbService.getCount("tb_sys_user", "usertype in(1,2)");
			if(usercount>=CampusLicense.getLicenseMaxuser()) {
				return Json.getJsonResult("系统账号数量已达上限");
			}

			if(!StringUtils.isBlank(password)){
				password=MD5.MD5Encode(password);
			}
			String sql="insert into tb_sys_user(uid,code,password,name,groupid,empid,email,phone,usertype,logintime,createdate,creatorid,status,parentid) ";
			sql+=" values(uuid(),?,?,?,?,?,?,?,?,0,now(),?,?,?)";
			String parentid = "";
			if (userredis.get(request).getUsertype()==1){
				parentid =userredis.get(request).getUid();
			}else {
				parentid =null;
			}
			dbService.excuteSql( sql, code,password,name,groupid,empid,email,phone,usertype,userid,status,parentid);
			syslogs.Write(request, "系统账号", String.format("新增系统账号（%s）", code));
		}else{
			String passwordstr="";
			if(!StringUtils.isBlank(password)){
				passwordstr="password='"+MD5.MD5Encode(password)+"',";
			}
			String sql="update tb_sys_user set code=?,"+passwordstr+"name=?,groupid=?,empid=?,email=?,phone=?,usertype=?,modifydate=now(),modifierid=?,status=? where uid=? ";
			dbService.excuteSql( sql, code,name,groupid,empid,email,phone,usertype,userid,status,uid);
			syslogs.Write(request, "系统账号", String.format("修改系统账号（%s）", code));
		}
		return Json.getJsonResult(true);
	}

	public JsonResult DelSysUser(HttpServletRequest request, HttpServletResponse response,String uid) {
		String sql="";
		SysUser user=  userredis.get(request);
		if(user.getUsertype()!=9) {
			if(user.getUid().equals(uid)) {
				return Json.getJsonResult("禁止删除该账号");
			}
		}

		sql="delete from tb_sys_role_user where userid=?";
		dbService.excuteSql( sql, uid);
		syslogs.Write(request, "系统账号", String.format("删除系统用户（%s）角色", uid));

		sql="delete from tb_sys_user where uid=?";
		dbService.excuteSql( sql, uid);
		syslogs.Write(request, "系统账号", String.format("删除系统账号（%s）", uid));
		return Json.getJsonResult(true);
	}

	public JsonResult AuthSysUserRule(HttpServletRequest request, HttpServletResponse response,String userid,String roleid) {
		  String sql="insert into tb_sys_role_user(uid,userid,roleid,createdate) select uuid(),'"+userid+"',uid,now() from tb_sys_role where find_in_set(uid, '"+roleid+"')>0 ";
		  dbService.excuteSql( sql);
		  syslogs.Write(request, "系统账号", String.format("新增系统用户（%s）角色", userid));
		  return Json.getJsonResult(true);
	}

	public JsonResult RemoveSysUserRule(HttpServletRequest request, HttpServletResponse response,String uid) {
		 String sql="delete from tb_sys_role_user where uid=? ";
		dbService.excuteSql( sql, uid);
		syslogs.Write(request, "系统账号", String.format("删除系统用户角色（%s）", uid));
		return Json.getJsonResult(true);
	}

	public JsonResult ResetPwd(HttpServletRequest request, HttpServletResponse response,String uid,String pwd) {
		  int count=dbService.getCount( "tb_sys_user", "uid='"+uid+"'");
		  if(count==0){
			  return Json.getJsonResult("系统用户信息不存在");
		  }
		  String sql="update tb_sys_user set password=? where uid=? ";
		  dbService.excuteSql( sql, MD5.MD5Encode(pwd),uid);
		  syslogs.Write(request, "系统账号", String.format("重置系统账号密码（%s）", uid));
		  return Json.getJsonResult(true);
	}

	public JsonResult DisabledUser(HttpServletRequest request, HttpServletResponse response,String uid,String status) {
		  int count=dbService.getCount( "tb_sys_user", "uid='"+uid+"'");
		  if(count==0){
			  return Json.getJsonResult("系统用户信息不存在");
		  }
		  String sql="update tb_sys_user set status=? where uid=? ";
		  dbService.excuteSql( sql, status,uid);
		  syslogs.Write(request, "系统账号", String.format("变更系统账号（%s）状态为（%s）", uid,status));
		  return Json.getJsonResult(true);
	}

	public JsonData getSysRuleList(HttpServletRequest request, HttpServletResponse response,String userid) {
		String where="status=1 and uid not in(select roleid from tb_sys_role_user where userid='"+userid+"')";
		if (userredis.get(request).getUsertype()==1){
			where +=" and parentid = '"+userredis.get(request).getUid()+"'";
		}
		JsonData result=dbService.QueryJsonData( "uid,name", "tb_sys_role", where, "createdate desc");
		return result;
	}

	public JSONArray getSysRoleMenuList(HttpServletRequest request, HttpServletResponse response,String roleid) {
		if(StringUtils.isBlank(roleid)){
			return new JSONArray();
		}
		String sql="SELECT m.uid,m.parentid,m.code,m.name,m.iconcls as iconCls,m.menuos,m.dynamiccode,m.menutype,rm.opbutton FROM tb_sys_role_menu rm inner join tb_sys_menu m on m.uid=rm.menuid";
		sql+=" where m.status=1 and rm.status=1 ";
		sql+=" and rm.roleid='"+roleid+"' ";
		JSONArray list=dbService.QueryList( sql);
		JSONArray rootlist= TreeHelper.findRootList(list, "parentid", "uid");
		for(Object m:rootlist){
			JSONObject menu=(JSONObject)m;
			JSONArray children=TreeHelper.getChildren(list, "parentid",menu.getString("uid"),"uid");
			if (children.size() > 0) {
				menu.put("leaf", false);
				menu.put("children", children);
				menu.put("expanded", true);
			}else{
				menu.put("leaf", true);
				menu.put("children", new JSONArray());
				menu.put("expanded", true);
			}
		}
		return rootlist;
	}

	public JsonData getEmpList(HttpServletRequest request, HttpServletResponse response,String key,int start,int limit){
		String fields="uid,code,name,sex,getorgname(orgcode) as orgname";
		String table="tb_card_teachstudinfo";
		String where=" status=1 ";
		if(!StringUtils.isBlank(key)){
			where+=" and (code='"+key+"' or name like '%"+key+"%' or mobile like '%"+key+"%' or  card='"+key+"' or cardsn='"+key+"' )";
		}
		String orderby=ExtSort.Orderby(request, "createdate desc");
		JsonData result=dbService.QueryJsonData( fields, table, where, orderby, start, limit);
		return result;
	}

	public JsonResult ModifySysUserPassword(HttpServletRequest request, HttpServletResponse response,
			String userid,String code,String oldpassword,String newpassword,String crmnewpassword){
		if(StringUtils.isBlank(userid)){
			return Json.getJsonResult("账号代码为空");
		}
		if(StringUtils.isBlank(code)){
			return Json.getJsonResult("系统账号为空");
		}
		if(StringUtils.isBlank(oldpassword)){
			return Json.getJsonResult("原始密码为空");
		}
		if(StringUtils.isBlank(newpassword)){
			return Json.getJsonResult("新密码为空");
		}
		if(StringUtils.isBlank(crmnewpassword)){
			return Json.getJsonResult("请确认新密码");
		}
		if(!newpassword.equals(crmnewpassword)){
			return Json.getJsonResult("确认新密码错误");
		}
		if(!userredis.getUserId(request).equals(userid)){
			return Json.getJsonResult("违例操作，请勿继续执行非法操作");
		}

		String password=MD5.MD5Encode(oldpassword);
        if(WebConfig.EnableCampusCPService()) {
            String url=String.format("%s/business/bizuser/setPrivatepwd", WebConfig.getCampusCPDomain());
            Map<String, String> params=new HashMap<String,String>();
            String name = userredis.get(request).getName();
            String timestamp=String.valueOf(System.currentTimeMillis());
            String newpwd = MD5.MD5Encode(newpassword);
            String query=String.format("appid=%s&code=%s&name=%s&oldpwd=%s&newpwd=%s&nodeid=%s&timestamp=%s&key=%s", WebConfig.getCampusCPAppId(),code,name,password,newpwd,WebConfig.getNodeid(),timestamp,WebConfig.getCampusCPAppSecret());
            String signature = MD5.MD5Encode(query);
            params.put("appid", WebConfig.getCampusCPAppId());
            params.put("nodeid", WebConfig.getNodeid());
            params.put("code", code);
            params.put("name", name);
            params.put("oldpwd", password);
            params.put("newpwd", newpwd);
            params.put("signature", signature);
            params.put("timestamp", timestamp);
            JsonResult result= HttpRequest.HttpPost(url, params);
            if(!result.getData().getBoolean("success")) {
                return Json.getJsonResult(false,result.getData().getString("msg"));
            }
        }else {
            int count=dbService.getCount("tb_sys_user", "uid='"+userid+"' and code='"+code+"' and password='"+password+"'");
            if(count==0){
                return Json.getJsonResult("原始密码错误！");
            }
            String sql="update tb_sys_user set password=? where uid=? ";
            dbService.excuteSql(sql, MD5.MD5Encode(newpassword),userid);
        }
		return Json.getJsonResult(true);
	}

	public JsonResult getCampusCPAccessToken(HttpServletRequest request, HttpServletResponse response,String code){
		String url=String.format("%s/Oauth2/access_token", WebConfig.getCampusCPDomain());
		Map<String, String> params=new HashMap<String,String>();
		params.put("appid", WebConfig.getCampusCPAppId());
		params.put("secret", WebConfig.getCampusCPAppSecret());
		params.put("code", code);
		params.put("grant_type", "authorization_code");
		JsonResult result= HttpRequest.HttpPost(url, params);
		return result;
	}

	public JsonResult OAuth2Login(HttpServletRequest request, HttpServletResponse response,String access_token,int expires_in,String refresh_token,String openid) {
		String url=String.format("%s/Oauth2/snsapi_userinfo", WebConfig.getCampusCPDomain());
		Map<String, String> params=new HashMap<String,String>();
		params.put("access_token", access_token);
		params.put("openid", openid);
		JsonResult result= HttpRequest.HttpPost(url, params);
		if(!result.isSuccess()) {
			return Json.getJsonResult(result.getMsg());
		}
		JSONObject resultdata=result.getData();
		if(!resultdata.getBooleanValue("success")) {
			return Json.getJsonResult(resultdata.getString("msg"));
		}
		JSONObject userinfo=resultdata.getJSONObject("data");
		int usertype=userinfo.getIntValue("usertype");
		String name=userinfo.getString("name");
		openid=userinfo.getString("openid");
		String code=userinfo.getString("code");
		JSONArray menusoauth=userinfo.getJSONArray("menus");

		String sql="";
		if(usertype==9 || usertype==2){
			//超级管理员或开发人员无需授权
			sql="SELECT uid as menuid,code,iconcls as iconCls,menuos,dynamiccode,menutype,name as text,parentid,viewclass,opbutton,sort,maxopbutton FROM tb_sys_menu where status=1;";
		}else{
			List<String> menuids=new ArrayList<String>();
			for(int i=0;i<menusoauth.size();i++) {
				JSONObject m=menusoauth.getJSONObject(i);
				menuids.add(m.getString("menuid"));
			}
			sql="select uid as menuid,code,iconcls as iconCls,menuos,dynamiccode,menutype,name as text,parentid,viewclass,sort,maxopbutton from tb_sys_menu where uid in('"+String.join("','", ComHelper.ListToArray(menuids))+"');";
		}
		JSONArray menus=dbService.QueryList(sql);
		if(!(usertype==9 || usertype==2)){
			//非超级管理员和开发人员，对菜单去重和操作按钮进行叠加
			for(int i=0;i<menus.size();i++){
				final JSONObject menu=menus.getJSONObject(i);

				JSONArray othermenus=CollectionUtils.where(menusoauth, new CallBackFilter<JSONObject>(){
					@Override
					public Boolean filter(JSONObject model) {
						if(model.getString("menuid").equals(menu.getString("menuid"))){
							return true;
						}else{
							return false;
						}
					}
				});

				if(othermenus.size()>0) {
					menu.put("opbutton", othermenus.getJSONObject(0).getString("opbutton"));
				}
			}
		}
		if(menus.size()==0){
			return Json.getJsonResult("用户未经授权");
		}
		SysUser user= new SysUser();
		user.setUid(openid);
		user.setCode(code);
		user.setName(name);
		user.setUsertype(usertype);
		user.setStatus(1);
		user.setMenu(menus.toJSONString());
		String tokenid=UUID.randomUUID().toString();
		user.setTokenid(tokenid);
		userredis.add(response,user);
		return Json.getJsonResult(true);
	}


	public JsonResult SyncOsCPBizUser(HttpServletRequest request, HttpServletResponse response){
		String jsonstr=HttpRequest.GetNoKeyData(request);
		JSONObject data=JSONObject.parseObject(jsonstr);
		String uid=data.getString("uid");
		String code=data.getString("code");
		String name=data.getString("name");
		String usertype=data.getString("usertype");
		String status=data.getString("status");
		int count=dbService.getCount("tb_sys_user", "uid='"+uid+"'");
		if(count>0) {
			String sql="update tb_sys_user set code=?,name=?,usertype=?,status=? where uid=?";
			dbService.excuteSql(sql, code,name,usertype,status,uid);
		}else {
			String sql="INSERT INTO tb_sys_user(uid,code,password,name,groupid,usertype,logintime,createdate,modifydate,status) values(?,?,?,?,'1',?,0,now(),now(),?)";
			String password=RandomHelper.generateString(16);
			password=MD5.MD5Encode(password, "utf-8");
			dbService.excuteSql(sql, uid,code,password,name,usertype,status);
		}
		return Json.getJsonResult(true);
	}

	public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response, String orgcode, String key, boolean viewchild, String selecteduids,int start,int limit) {
		String fields = "ct.uid,ct.code,ct.name,ct.sex,ct.card,ct.cardsn,ct.orgcode,ct.infotype,ct.intoyear,ct.focus,ct.status, getorgname(ct.orgcode) as orgname";
		String table = "tb_card_teachstudinfo ct";
		StringBuilder where = new StringBuilder("status = 1 and infotype = 1");
		where.append(" and not EXISTS (select ct.uid from tb_sys_user su where su.empid = ct.uid)");
		if(StringUtils.isNotBlank(orgcode)){
			if(viewchild){
				where.append(" and ct.orgcode like '").append(orgcode).append("%'");
			}else{
				where.append(" and ct.orgcode = '").append(orgcode).append("'");
			}
		}
		if(StringUtils.isNotBlank(key)){
			where.append(" and ct.name like '%").append(key).append("%' or ct.code = '").append(key).append("'");
		}
		String[] splituids = selecteduids.split(",");
		where.append(" and ct.uid not in ('").append(String.join("','",splituids)).append("')");
		String orderby = ExtSort.Orderby(request,"ct.code asc");
		return dbService.QueryJsonData(fields,table,where.toString(),orderby,start,limit);
	}

	public JsonResult batchAdd(HttpServletRequest request, HttpServletResponse response,String infoids,String roleid){
		int count=dbService.getCount("tb_sys_user", "usertype in(1,2)");
		if(count>=CampusLicense.getLicenseMaxuser()) {
			return Json.getJsonResult(String.format("许可证授权%s个用户，用户数已达上限！", CampusLicense.getLicenseMaxuser()));
		}
		String userid=userredis.get(request).getUid();
		String password = MD5.MD5Encode("123");
		StringBuilder insertuser = new StringBuilder("insert into tb_sys_user(uid,code,password,name,empid,usertype,createdate,creatorid,status)")
				.append(" select uuid(),code,'").append(password).append("',name,uid,'1',now(),'").append(userid).append("','1'")
				.append(" from tb_card_teachstudinfo where uid in ('").append(String.join("','",infoids.split(","))).append("')");
		dbService.excuteSql( insertuser.toString());

		StringBuilder queryuid = new StringBuilder("select uid,code from tb_sys_user where find_in_set(empid,'").append(infoids).append("')");
		JSONArray users = dbService.QueryList(queryuid.toString());
		StringBuilder uids = new StringBuilder();
		StringBuilder codes = new StringBuilder();
		for(int i=0;i<users.size();i++){
			uids.append(users.getJSONObject(i).getString("uid")+",");
			codes.append(users.getJSONObject(i).getString("code")+",");
		}
		uids.deleteCharAt(uids.length()-1);
		codes.deleteCharAt(codes.length()-1);
		syslogs.Write(request, "系统账号", String.format("新增系统账号（%s）", codes));
		StringBuilder insertrole = new StringBuilder("insert into tb_sys_role_user(uid,userid,roleid,createdate) select uuid(),su.uid,sr.uid,now() ")
				.append(" from tb_sys_role sr,tb_sys_user su where find_in_set(sr.uid, '").append(roleid).append("') and find_in_set(su.uid,'")
				.append(uids).append("')");
		dbService.excuteSql(insertrole.toString());
		syslogs.Write(request, "系统账号", String.format("新增系统用户（%s）角色", infoids));
		return Json.getJsonResult(true);
	}

	public JsonData getLicenseInfo(HttpServletRequest request, HttpServletResponse response) {
		int count=dbService.getCount("tb_sys_user", "usertype in(1,2)");
		int maxUser = CampusLicense.getLicenseMaxuser();
		JSONObject json = new JSONObject();
		json.put("count", count);
		json.put("maxUser", maxUser);
		JsonData jsonData = new JsonData();
		JSONArray jsonArray = new JSONArray();
		jsonArray.add(json);
		jsonData.setData(jsonArray);
		jsonData.setSuccess(true);
		return jsonData;
	}
}
