package com.ymiots.campusos.service.elevator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.ymiots.campusos.common.BaseService;
import com.ymiots.framework.common.ComHelper;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.JsonData;

@Repository
public class EvRecordService extends BaseService{

	
	public JsonData getEvRecordsList(HttpServletRequest request, HttpServletResponse response,String starttime, String endtime, String key,int start,int limit){
		String fields="uid,machineid,devname,areacode,recordtime,cardsn,infocode,infoname,orgcode,isvisitor,getorgname(orgcode) as orgname,getareaname(areacode) as areaname";
		String table="tb_elevator_records";
		String where=" 1=1 ";
		if(!StringUtils.isBlank(starttime) && !StringUtils.isBlank(endtime)) {
			where+=" and (recordtime between '"+starttime+"' and '"+endtime+"') ";
		}
		if(!StringUtils.isBlank(key)) {
			where+=" and (machineid='"+key+"' or devname like '%"+key+"%' or cardsn='"+ComHelper.LeftPad(key, 12, '0')+"' or infoname like '%"+key+"%' ) ";
		}
		String orderby=ExtSort.Orderby(request, "recordtime desc,uid asc");
		JsonData result=dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}
	
}
