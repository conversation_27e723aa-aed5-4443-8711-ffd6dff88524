package com.ymiots.campusos.service.consume;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.entity.card.TeachStudInfo;
import com.ymiots.campusos.entity.consume.ConsumeInfo;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.datafactory.CallbackTransaction;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Repository
public class PayRecordsService extends BaseService{

    @Autowired
    private SysLogsService syslogs;

    @Autowired
    UserRedis userredis;

    public JsonData getPayRecordsList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit, String startdate, String enddate,String merid,String placeid,String schemeid,String paytype,String infoid){
        String where = " 1=1 ";
        if(!StringUtils.isBlank(infoid)) {
            where += " and infoid IN ('"+String.join("','",infoid.split(","))+"') ";
        }
        if (StringUtils.isNotBlank(key)){
            if(key.length()==12) {
                where += " and cardno='"+key+"' ";
            }else {
                where += " and machineid='"+key+"' ";
            }
        }
        if(StringUtils.isNotBlank(merid)&&!merid.equals("1")){
            where += " and merid = '"+merid+"' ";
        }
        if(StringUtils.isNotBlank(placeid)){
            where += " and placeid = '"+placeid+"' ";
        }
        if(StringUtils.isNotBlank(schemeid)){
            where += " and schemeid = '"+schemeid+"' ";
        }
        if(StringUtils.isNotBlank(paytype) && Integer.parseInt(paytype)>0){
            where += " and paytype = '"+paytype+"' ";
        }

        if(!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate) ){
            where+=" and recordtime between '"+startdate+"' and '"+enddate+"' ";
        }
        String orderby= ExtSort.Orderby(request, "pay.createdate DESC");

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT pay.uid, pay.cardno, date_format(pay.recordtime, '%Y-%m-%d %H:%i:%s') as recordtime, pay.money, pay.machineid,pay.paywallet,pay.paytype,pay.des, d.name as paytypename,dev.devname,mer.mername,mer.name, place.name as placename,info.name as infoname,info.code,getminiorgname(info.orgcode) as orgname,pay.status,pay.recordimg  " +
                "FROM ((SELECT uid,cardno,cardid,devid,merid,placeid,schemeid,infoid,recordtime,money,machineid,paywallet,paytype,des,status,createdate,recordimg  FROM tb_consume_payrecords WHERE ");
        sql.append(where).append(") UNION ALL (SELECT uid,cardno,cardid,devid,merid,placeid,schemeid,infoid,recordtime,money,machineid,paywallet,paytype,des,status,createdate,recordimg FROM tb_consume_payrecords_sleep WHERE ").append(where).append("))pay");
        sql.append(" LEFT JOIN tb_dev_accesscontroller dev ON dev.uid = pay.devid " +
                " LEFT JOIN tb_consume_merchant mer ON mer.uid = pay.merid LEFT JOIN tb_consume_merchant_place place ON place.uid = pay.placeid " +
                " LEFT JOIN tb_card_teachstudinfo info ON info.uid = pay.infoid  LEFT JOIN tb_sys_dictionary d ON  pay.paytype = d.code and d.groupcode = 'SYS0000030' ");
        if (StringUtils.isEmpty(merid)&&userredis.get(request).getUsertype()==1){
            sql.append(" join tb_consume_merchant_user meru on meru.merid = mer.uid and userid = '"+userredis.get(request).getUid()+"' ");
        }
        SysUser user = userredis.get(request);
        if (user.getUsertype() == 1) {
            String userid = user.getUid();
            sql.append(" inner join tb_card_orgframework co on co.code = info.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='").append(userid).append("' ");
            sql.append(" inner join tb_consume_merchant_user cmu on cmu.merid = mer.uid and cmu.userid = '").append(userid).append("'");
        }

        sql.append(" ORDER BY ").append(orderby);
        if(limit != 0){
            sql.append(" LIMIT ").append(start).append(",").append(limit);
        }

        StringBuffer sb=new StringBuffer();
        sb.append(" SELECT count(1) as total,sum(money) sum FROM tb_consume_payrecords WHERE ");
        sb.append(where);
        sb.append(" UNION ALL ");
        sb.append(" SELECT count(1) as total,sum(money) sum FROM tb_consume_payrecords_sleep WHERE ");
        sb.append(where);
        String totalsql = String.format("SELECT sum(t.total) as total,sum(sum) sum FROM (%s) t", sb.toString());
        JSONObject object = dbService.QueryJSONObject(totalsql);
        String sum = object.getString("sum");
        JSONObject obj = new JSONObject();
        obj.put("recordtime", "消费合计：");
        obj.put("money", sum);
        int total = object.getIntValue("total");
        JSONArray ja = dbService.QueryList(sql.toString());
        ja.add(obj);
        JsonData jd = Json.getJsonData(true, "", ja, total);
        return jd;
    }
    public JsonData getPayRecordsHistoryList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit, String startdate, String enddate,String merid,String placeid,String schemeid,String paytype,String infoid){
        String where = " 1=1 ";
        if(!StringUtils.isBlank(infoid)) {
            where += " and infoid IN ('"+String.join("','",infoid.split(","))+"') ";
        }
        if (StringUtils.isNotBlank(key)){
            if(key.length()==12) {
                where += " and cardno='"+key+"' ";
            }else {
                where += " and machineid='"+key+"' ";
            }
        }
        if(StringUtils.isNotBlank(merid)&&!merid.equals("1")){
            where += " and merid = '"+merid+"' ";
        }
        if(StringUtils.isNotBlank(placeid)){
            where += " and placeid = '"+placeid+"' ";
        }
        if(StringUtils.isNotBlank(schemeid)){
            where += " and schemeid = '"+schemeid+"' ";
        }
        if(StringUtils.isNotBlank(paytype) && Integer.parseInt(paytype)>0){
            where += " and paytype = '"+paytype+"' ";
        }

        if(!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate) ){
            where+=" and recordtime between '"+startdate+"' and '"+enddate+"' ";
        }
        String orderby= ExtSort.Orderby(request, "createdate DESC");
        JsonData data = new JsonData();
        StringBuilder table = new StringBuilder();
        table.append(" tb_consume_payrecords_sleep_history a ");
        if (StringUtils.isEmpty(merid)&&userredis.get(request).getUsertype()==1){
            table.append(" join tb_consume_merchant_user meru on meru.merid = a.merid and meru.userid = '"+userredis.get(request).getUid()+"' ");
        }
        if (limit==0){
            data = dbService.QueryJsonData(" a.*", table.toString(), where, orderby);
        }else {
            data = dbService.QueryJsonData(" a.*", table.toString(), where, orderby, start, limit);
        }
        return data;
    }

    public String ExportPayRecordsData(HttpServletRequest request, HttpServletResponse response, String key,String startdate,String enddate,String merid,String placeid,String schemeid,String paytype,String infoid,String currentPage ,String pageSize,String size) throws IOException {
        int star = 1;
        JsonData result = new JsonData();
        if (currentPage.equals("1")){
            result = getPayRecordsList(request, response, key, 0, Integer.valueOf(size),startdate,enddate,merid,placeid,schemeid,paytype,infoid);
        }else if(Integer.valueOf(currentPage)>star) {
            result = getPayRecordsList(request, response, key, (Integer.valueOf(currentPage)-1)*Integer.valueOf(size), Integer.valueOf(size),startdate,enddate,merid,placeid,schemeid,paytype,infoid);
        }else {
            result = getPayRecordsList(request, response, key, 0,0,startdate,enddate,merid,placeid,schemeid,paytype,infoid);
        }

        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        if("1".equals(WebConfig.getApptype())){
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "code");
            cell.put("size", 20);
            cells.add(cell);
        }else if("2".equals(WebConfig.getApptype())){
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "code");
            cell.put("size", 20);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "infoname");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "部门");
        cell.put("datakey", "orgname");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "消费时间");
        cell.put("datakey", "recordtime");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "消费金额");
        cell.put("datakey", "money");
        cell.put("size", 10);
        cells.add(cell);


        for (int i = 0; i < list.size(); i++) {
            JSONObject jo = list.getJSONObject(i);
            String paystr = jo.getString("paywallet");
            if ("1".equals(paystr)) {
                String payName = "主钱包";
                jo.put("paywallet", payName);
            } else if ("2".equals(paystr)) {
                String payName = "副钱包";
                jo.put("paywallet", payName);
            } else {
                String payName = "计次";
                jo.put("paywallet", payName);
            }
        }

        cell = new JSONObject();
        cell.put("name", "消费钱包");
        cell.put("datakey", "paywallet");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡号");
        cell.put("datakey", "cardno");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "machineid");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "消费模式");
        cell.put("datakey", "paytypename");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "设备名称");
        cell.put("datakey", "devname");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "商户名称");
        cell.put("datakey", "mername");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "营业场所");
        cell.put("datakey", "placename");
        cell.put("size", 20);
        cells.add(cell);

        String title= "消费记录";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";
        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp", filename, title,startdate,enddate,currentPage,pageSize );
        return filepath;
    }
    public String ExportALLData(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String infoSQL = "select ct.uid,ct.name,ct.card,ct.code,getorgname(ct.orgcode) orgCode from tb_card_teachstudinfo ct  where ct.status=1 ";
        String sql = "SELECT uid, infoid, recordtime, -money money,'消费' des\n" +
                "FROM tb_consume_payrecords \n" +
                "WHERE infoid = '%s'\n" +
                "UNION ALL\n" +
                "SELECT uid, infoid, recordtime, -money money,'消费' des\n" +
                "FROM tb_consume_payrecords_sleep \n" +
                "WHERE infoid = '%s'\n" +
                "UNION ALL\n" +
                "SELECT uid, infoid, recordtime, -money money,'消费' des\n" +
                "FROM tb_consume_payrecords_sleep_history \n" +
                "WHERE infoid = '%s'\n" +
                "UNION ALL\n" +
                "SELECT uid, infoid, tradedate AS recordtime, money,des\n" +
                "FROM tb_card_transaction_detail \n" +
                "WHERE infoid = '%s'\n" +
                "UNION ALL\n" +
                "SELECT uid, infoid, tradedate AS recordtime, money,des\n" +
                "FROM tb_card_transaction_detail_sleep \n" +
                "WHERE infoid = '%s'\n" +
                "ORDER BY recordtime";
        List<TeachStudInfo> teachStudInfos = dbService.queryList(infoSQL, TeachStudInfo.class);
        List<String> excelFiles = new ArrayList<>();
        for (TeachStudInfo teachStudInfo : teachStudInfos) {
            String uid = teachStudInfo.getUid();
            String newString = String.format(sql, uid,uid,uid,uid,uid);
            List<ConsumeInfo> consumeInfos = dbService.queryList(newString, ConsumeInfo.class);
            if (consumeInfos.size()>0){
                BigDecimal decimal = BigDecimal.ZERO;
                for (ConsumeInfo consumeInfo : consumeInfos) {
                    consumeInfo.setCode(teachStudInfo.getCode());
                    consumeInfo.setName(teachStudInfo.getName());
                    consumeInfo.setOrgname(teachStudInfo.getOrgCode());
                    decimal = decimal.add(consumeInfo.getMoney());
                    consumeInfo.setBalance(decimal);
                }
                BigDecimal zeroBigDecimal= BigDecimal.ZERO;
                JSONArray array = dbService.QueryList("select balance from tb_card_cardinfo where status = 1 and cardno=?", teachStudInfo.getCard());
                if (array.size()>0){
                    zeroBigDecimal = array.getJSONObject(0).getBigDecimal("balance");
                }
                ConsumeInfo consumeInfo = new ConsumeInfo();
                consumeInfo.setDes("现在目前余额");
                consumeInfo.setBalance(zeroBigDecimal);
                consumeInfos.add(consumeInfo);
                JSONArray list = convertToJSONArray(consumeInfos);
                JSONArray cells = new JSONArray();
                JSONObject cell = new JSONObject();

                if("1".equals(WebConfig.getApptype())){
                    cell = new JSONObject();
                    cell.put("name", "学工号");
                    cell.put("datakey", "code");
                    cell.put("size", 20);
                    cells.add(cell);
                }else if("2".equals(WebConfig.getApptype())){
                    cell = new JSONObject();
                    cell.put("name", "工号");
                    cell.put("datakey", "code");
                    cell.put("size", 20);
                    cells.add(cell);
                }

                cell = new JSONObject();
                cell.put("name", "姓名");
                cell.put("datakey", "name");
                cell.put("size", 20);
                cells.add(cell);

                cell = new JSONObject();
                cell.put("name", "机构名称");
                cell.put("datakey", "orgname");
                cell.put("size", 40);
                cells.add(cell);

                cell = new JSONObject();
                cell.put("name", "充值/消费时间");
                cell.put("datakey", "recordtime");
                cell.put("size", 20);
                cells.add(cell);

                cell = new JSONObject();
                cell.put("name", "充值/消费金额");
                cell.put("datakey", "money");
                cell.put("size", 20);
                cells.add(cell);

                cell = new JSONObject();
                cell.put("name", "余额");
                cell.put("datakey", "balance");
                cell.put("size", 20);
                cells.add(cell);


                cell = new JSONObject();
                cell.put("name", "备注");
                cell.put("datakey", "des");
                cell.put("size", 20);
                cells.add(cell);

                String title= "全部充值消费余额记录明细";
                String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
                String filename =  teachStudInfo.getCode()+"-"+teachStudInfo.getName()+"-"+title+format + ".xlsx";
                String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp", filename, title);
                excelFiles.add(filepath);
            }

        }

        // 创建 ZIP 文件
        String zipFilename = "exported_files_" + DateHelper.format(new Date(), "yyyyMMddHHmmss") + ".zip";
        String zipFilePath = "/faceimagezip/"+zipFilename;
        try (FileOutputStream fos = new FileOutputStream(WebConfig.getUploaddir() +"/faceimagezip/"+ zipFilename);
             ZipOutputStream zipOut = new ZipOutputStream(fos)) {
            for (String excelFile : excelFiles) {
                File fileToZip = new File(WebConfig.getUploaddir() +excelFile);
                FileInputStream fis = new FileInputStream(fileToZip);
                ZipEntry zipEntry = new ZipEntry(fileToZip.getName());
                zipOut.putNextEntry(zipEntry);

                byte[] bytes = new byte[1024];
                int length;
                while ((length = fis.read(bytes)) >= 0) {
                    zipOut.write(bytes, 0, length);
                }
                fis.close();
            }
        }
        //try (FileInputStream fis = new FileInputStream(WebConfig.getUploaddir() +zipFilePath);
        //     OutputStream os = response.getOutputStream()) {
        //    byte[] buffer = new byte[4096];
        //    int bytesRead;
        //    while ((bytesRead = fis.read(buffer)) != -1) {
        //        os.write(buffer, 0, bytesRead);
        //    }
        //}

        return zipFilePath;
    }

    public  JSONArray convertToJSONArray(List<ConsumeInfo> consumeInfos) {
        JSONArray jsonArray = new JSONArray();
        for (ConsumeInfo consumeInfo : consumeInfos) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", consumeInfo.getName());
            jsonObject.put("code", consumeInfo.getCode());
            jsonObject.put("orgname", consumeInfo.getOrgname());
            jsonObject.put("recordtime", consumeInfo.getRecordtime());
            jsonObject.put("money", consumeInfo.getMoney());
            jsonObject.put("balance", consumeInfo.getBalance());
            jsonObject.put("des", consumeInfo.getDes());
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    public void DeletePayRecordSleep(Date date){
        String year = dbService.QueryJSONObject("SELECT NAME FROM tb_sys_dictionary WHERE groupcode = 'SYS0000069' AND CODE = 0").getString("NAME");
        // 获取当前日期
        Date currentDate = date;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.YEAR, -Integer.parseInt(year));
        Date downDate = calendar.getTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = dateFormat.format(downDate);
        dbService.excuteSql("delete from tb_consume_payrecords_sleep_history where date_format(recordtime, '%Y-%m-%d') < '"+format+"'");
    }

    public String ExportPayRecordsHistoryData(HttpServletRequest request, HttpServletResponse response, String key,String startdate,String enddate,String merid,String placeid,String schemeid,String paytype,String infoid,String currentPage ,String pageSize,String size) throws IOException {
        int star = 1;
        JsonData result = new JsonData();
        result = getPayRecordsHistoryList(request, response, key, 0,0,startdate,enddate,merid,placeid,schemeid,paytype,infoid);

        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        if("1".equals(WebConfig.getApptype())){
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "code");
            cell.put("size", 20);
            cells.add(cell);
        }else if("2".equals(WebConfig.getApptype())){
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "code");
            cell.put("size", 20);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "infoname");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "部门");
        cell.put("datakey", "orgname");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "消费时间");
        cell.put("datakey", "recordtime");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "消费金额");
        cell.put("datakey", "money");
        cell.put("size", 10);
        cells.add(cell);


        for (int i = 0; i < list.size(); i++) {
            JSONObject jo = list.getJSONObject(i);
            String paystr = jo.getString("paywallet");
            if ("1".equals(paystr)) {
                String payName = "主钱包";
                jo.put("paywallet", payName);
            } else if ("2".equals(paystr)) {
                String payName = "副钱包";
                jo.put("paywallet", payName);
            } else {
                String payName = "计次";
                jo.put("paywallet", payName);
            }
        }

        cell = new JSONObject();
        cell.put("name", "消费钱包");
        cell.put("datakey", "paywallet");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡号");
        cell.put("datakey", "cardno");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "machineid");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "消费模式");
        cell.put("datakey", "paytypename");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "设备名称");
        cell.put("datakey", "devname");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "商户名称");
        cell.put("datakey", "mername");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "营业场所");
        cell.put("datakey", "placename");
        cell.put("size", 20);
        cells.add(cell);

        String title= "消费记录";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";
        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp", filename, title,startdate,enddate,currentPage,pageSize );
        return filepath;
    }

    public JsonResult backpay(HttpServletRequest request, HttpServletResponse response,final String uid,final String money,final String des) throws Exception{
        String selpaytype  = "SELECT paytype FROM view_consume_payrecords where uid = ?";
        JSONArray paytypeja = dbService.QueryList(selpaytype, uid);
        if(paytypeja.isEmpty()){
            return Json.getJsonResult(false, "不存在消费记录，不可回退");
        }
        JSONObject paytypejo = paytypeja.getJSONObject(0);
        String paytype = paytypejo.getString("paytype");
        if("2".equals(paytype)){
            return Json.getJsonResult(false,"计次消费不可回退");
        }

        final String newuid = UUID.randomUUID().toString();
        String sum = "SELECT SUM(money) as summoney FROM view_consume_payrecords WHERE backid = ? or uid=?";
        JSONArray ja = dbService.QueryList(sum, uid,uid);
        JSONObject jo = ja.getJSONObject(0);
        String summoney = jo.getString("summoney");
        BigDecimal bdsummoney = new BigDecimal(summoney);
        if(bdsummoney.compareTo(BigDecimal.ZERO) == 0){
            return Json.getJsonResult(false,"该记录已无余额供退回!!!");
        }else if(bdsummoney.compareTo(new BigDecimal(money)) == -1){
            return Json.getJsonResult(false,"回退金额大于可退金额:"+summoney);
        }
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {

                String insertSql = "INSERT INTO tb_consume_payrecords(uid,cardid,cardno,recordtime,money,machineid,eventcode,paytype,devid,merid,placeid,schemeid,infoid,createdate,status,serviceuid,paywallet,des,backid) " +
                        "SELECT '"+newuid+"',cardid,cardno,now(),'-"+money+"',machineid,eventcode,paytype,devid,merid,placeid,schemeid,infoid,now(),1,serviceuid,paywallet,'"+des+"','"+uid+"'  FROM view_consume_payrecords WHERE uid = '"+uid+"'";
                PreparedStatement ps= connection.prepareStatement(insertSql);
                ps.executeUpdate();
                ps.close();

                JSONArray desarr = dbService.QueryList("SELECT des FROM view_consume_payrecords WHERE uid=?", uid);
                JSONObject desjo = desarr.getJSONObject(0);
                String olddes = desjo.getString("des");
                String newdes = "已回退￥"+money+"元";
                if (StringUtils.isNotBlank(olddes)){
                    newdes = olddes+";已回退￥"+money+"元";
                }
                String updateSql = "UPDATE tb_consume_payrecords SET des='"+newdes+"' WHERE uid='"+uid+"' ";
                ps= connection.prepareStatement(updateSql);
                ps.executeUpdate();
                ps.close();

                String updateSql2 = "UPDATE tb_consume_payrecords_sleep SET des='"+newdes+"' WHERE uid='"+uid+"' ";
                ps= connection.prepareStatement(updateSql2);
                ps.executeUpdate();
                ps.close();
            }
        });

        syslogs.Write(request, "消费记录",String.format("消费回退:%s,金额:%s,备注:%s", newuid,money,des));
        return Json.getJsonResult(true);
    }

    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response,String key, int start,int limit){
        String fields="info.uid,info.code,info.name";
        String table="tb_card_teachstudinfo info";
        String userid=userredis.getUserId(request);
        //如果为普通管理员，需要受二级授权控制
        if(userredis.get(request).getUsertype()==1){
            table+= " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='"+userid+"' ";
        }
        String where=" 1=1 ";
        if (StringUtils.isNotBlank(key)){
            where += " and (info.code like '%"+key+"%' or info.name like '%"+key+"%' ";
            if(ComHelper.isNumeric(key)) {
                String cardno=ComHelper.LeftPad(key, 12, '0');
                where += " or exists(select 1 from tb_card_cardinfo where infoid=info.uid and cardsn='"+cardno+"') ";
            }
            where += " ) ";
        }
        String orderby= ExtSort.Orderby(request, "info.createdate desc");
        JsonData result=dbService.QueryJsonData( fields, table, where, orderby, start, limit);
        return result;
    }

    public JSONArray getMer(HttpServletRequest request, HttpServletResponse response) {
        String fields=" mer.uid,mer.mername name ";
        String table=" tb_consume_merchant mer ";
        String userid=userredis.getUserId(request);
        //如果为普通管理员，需要受二级授权控制
        if(userredis.get(request).getUsertype()==1){
            table+= " inner join tb_consume_merchant_user uo on uo.merid=mer.uid and uo.userid='"+userid+"' ";
        }
        JSONArray ja = dbService.QueryList("select "+fields+" from "+table);
        if(userredis.get(request).getUsertype()!=1){
            JSONObject jo = new JSONObject();
            jo.put("name", "全部");
            jo.put("uid", "1");
            ja.add(0, jo);
        }
        return ja;
    }

    public JsonResult SignPayRecord(HttpServletRequest request, HttpServletResponse response,
                                    String infoid, String recordtime,String schemeid, String paytype, String starttime, String placeid, String merid, float money, String des) {

        String cardsql="SELECT uid as cardid,cardno FROM tb_card_cardinfo where infoid='"+infoid+"' and ismain=1 and status=1;";
        JSONArray cardlist= dbService.QueryList(cardsql);
        if(cardlist.size()==0) {
            return Json.getJsonResult("本人无有效卡");
        }
        JSONObject card=cardlist.getJSONObject(0);
        String cardid=card.getString("cardid");
        String cardno=card.getString("cardno");

        String devsql="SELECT md.devid,ac.machineid,ac.server_uid FROM tb_consume_merchant_device md inner join tb_dev_accesscontroller ac on ac.uid=md.devid  where md.merid='"+merid+"' and md.placeid='"+placeid+"' limit 1;";
        JSONArray devlist= dbService.QueryList(devsql);
        if(devlist.size()==0) {
            return Json.getJsonResult("营业场所无消费设备");
        }
        JSONObject dev=devlist.getJSONObject(0);
        String devid=dev.getString("devid");
        String machineid=dev.getString("machineid");
        String serviceuid=dev.getString("server_uid");
        recordtime=String.format("%s %s:00", recordtime, starttime);

        SysUser u= userredis.get(request);
        if(StringUtils.isBlank(des)) {
            des=String.format("%s操作签卡消费", u.getName());
        }else {
            des=String.format("%s操作签卡消费：%s", u.getName(),des);
        }

        String sql="INSERT INTO tb_consume_payrecords(uid,cardid,cardno,recordtime,money,machineid,paytype,devid,merid,placeid,schemeid,infoid,createdate,status,serviceuid,paywallet,des) "
                + "VALUES(uuid(),?,?,?,?,?,?,?,?,?,?,?,now(),1,?,1,?)";
        int count= dbService.excuteSql(sql, cardid,cardno,recordtime,money,machineid,paytype,devid,merid,placeid,schemeid,infoid,serviceuid,des);
        if(count==-1) {
            return Json.getJsonResult("操作失败");
        }
        return Json.getJsonResult(true);
    }

    public JsonResult delPayRecords(HttpServletRequest request, HttpServletResponse response,String date){
        String delSQL = "DELETE FROM tb_consume_payrecords WHERE status <> 1 AND date_format(recordtime, '%Y-%m-%d') < '"+date+"'";
        dbService.excuteSql(delSQL);
        String delSleeppSQL = "DELETE FROM tb_consume_payrecords_sleep WHERE status <> 1 AND date_format(recordtime, '%Y-%m-%d') < '"+date+"'";
        dbService.excuteSql(delSleeppSQL);
        String delPlaceRate = "DELETE FROM tb_consume_analysis_place_rate WHERE daily < ?";
        dbService.excuteSql(delPlaceRate, date);
        String delPersonRate = "DELETE FROM tb_consume_analysis_person_rate WHERE daily < ?";
        dbService.excuteSql(delPersonRate, date);
        String delDeviceRate = "DELETE FROM tb_consume_analysis_device_rate WHERE daily < ?";
        dbService.excuteSql(delDeviceRate, date);
        return Json.getJsonResult(true);
    }
}
