package com.ymiots.campusos.service.time;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.campusos.service.consume.AnalysisPersonRateService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.datafactory.CallCallableStatement;
import com.ymiots.framework.websocket.WebSocketSets;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Repository
public class DailyReportService extends BaseService {

    @Autowired
    private UserRedis userRedis;

    @Autowired
    SysLogsService syslogs;

    public JsonData getDailyReportList(HttpServletRequest request, HttpServletResponse response,String orgcode, final String key, String extend, final int start, final int limit) {
        final String[] date = extend.split(",");
        String sql = "CALL sp_dynamicgrid_timeresult(?,?,?,?,?,?,?)";
        JsonData data = dbService.QueryStoreList(sql, new CallCallableStatement() {
            @Override
            public void before(CallableStatement cs) throws SQLException {
                cs.setString(1, key);
                cs.setString(2, date[0]);
                cs.setString(3, date[1]);
                cs.setInt(4, start);
                cs.setInt(5, limit);

                cs.registerOutParameter(6, java.sql.Types.INTEGER);
                cs.setString(7, orgcode);
            }
        });
        return data;
    }

    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response,
                                String orgcode, String key, String infotype, boolean viewchild, String intoyear, String selecteduid, int start, int limit) {
        String fields = "info.uid,info.code,info.name,info.sex,info.card,info.cardsn,info.orgcode,info.infotype,info.intoyear,info.focus,info.status, getorgname(info.orgcode) as orgname";
        String table = "tb_card_teachstudinfo info";
        if (userRedis.get(request).getUsertype() == 1) {
            String userid = userRedis.getUserId(request);
            table += " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='" + userid + "' ";
        }
        String where = " info.status=1 ";
        if (!infotype.equals("0") && StringUtils.isNotBlank(infotype)) {
            where += " and info.infotype=" + infotype;
        }
        if (!intoyear.equals("0") && StringUtils.isNotBlank(infotype)) {
            where += " and info.intoyear=" + intoyear;
        }
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and info.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and info.orgcode='" + orgcode + "' ";
            }
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (info.code='" + key + "' or info.name like '%" + key + "%' or info.mobile like '%" + key + "%' or info.card='" + ComHelper.LeftPad(key, 12, '0') + "' or info.cardsn='" + ComHelper.LeftPad(key, 12, '0') + "' )";
        }
        JSONArray Selected = JSONArray.parseArray(selecteduid);
        if (Selected.size() > 0) {
            String[] uidx = ComHelper.ListToArray(Selected);
            where += " and info.uid not in('" + String.join("','", uidx) + "')";
        }
        String orderby = ExtSort.Orderby(request, "info.code desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonResult analysisTimeDaily(HttpServletRequest request, HttpServletResponse response, final String infoid,
                                        final String orgcode, final int ischildorg, final String startdate, final String enddate) {
        if (StrUtil.isBlank(infoid)) {
            return Json.getJsonResult(false, "人员为空");
        }
        String[] ids = infoid.split(",");
        if (ArrayUtil.isEmpty(ids)) {
            return Json.getJsonResult(false, "人员为空");
        }

        List<String> idLs = Arrays.stream(ids)
                .distinct()
                .collect(Collectors.toList());

        int batchSize = 75;
        int totalSize = ids.length;
        List<String> idBatchLs = IntStream.range(0, totalSize)
                .filter(i -> i % batchSize == 0)
                .mapToObj(i -> {
                    List<String> idBatch = idLs.subList(i, Math.min(i + batchSize, totalSize));
                    StringJoiner joiner = new StringJoiner(",");
                    for (String id : idBatch) {
                        joiner.add(id);
                    }
                    return joiner.toString();
                })
                .collect(Collectors.toList());

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Log.info(this.getClass(), String.format("开始分析考勤日报表%s至%s", startdate, enddate));
                    for (String idBatch : idBatchLs) {
                        dbService.excuteTransaction(connection -> {
                            String sql = "CALL sp_time_daily_analysis(?,?,?,?,?,?)";
                            PreparedStatement ps = connection.prepareStatement(sql);
                            ps.setString(1, startdate);
                            ps.setString(2, enddate);
                            ps.setString(3, idBatch);
                            ps.setString(4, null);
                            ps.setString(5, orgcode);
                            ps.setInt(6, ischildorg);
                            ps.executeUpdate();
                            ps.close();
                        }, Connection.TRANSACTION_READ_COMMITTED);
                    }

                    Log.info(this.getClass(), String.format("分析考勤日报表%s至%s结束", startdate, enddate));
                    JSONObject msg = new JSONObject();
                    msg.put("msgtype", 1000);
                    msg.put("msg", "分析成功");
                    WebSocketSets.getInstance().send("sysmsg", msg.toJSONString());

                } catch (Exception e) {
                    JSONObject msg = new JSONObject();
                    msg.put("msgtype", 1000);
                    msg.put("msg", "分析失败");
                    try {
                        WebSocketSets.getInstance().send("sysmsg",e.getMessage());
                    } catch (Exception ex) {
                        Log.error(this.getClass(), String.format("分析考勤日报表异常：%s", ex.getMessage()));
                    }
                    Log.error(this.getClass(), String.format("分析考勤日报表异常：%s", e.getMessage()));
                }
            }
        }).start();
        return Json.getJsonResult(true, "操作成功，等待分析结果");
    }

    /**
     * 返回分析结果
     *
     */

    public JsonResult saveSigncard(HttpServletRequest request, HttpServletResponse response, String infoid, String signtime, String cardindex, String ftype,
                                   String banzhi, boolean gotowork, boolean gooffwork, String remark, String auditing) {
        String creatorid = userRedis.getUserId(request);
        String selectBanzhi = "SELECT startminute,endminute FROM tb_time_scheme_worktime WHERE schemeid='" + banzhi + "' AND timeindex='" + cardindex + "'";
        JSONArray banzhija = dbService.QueryList(selectBanzhi);
        if (banzhija.size() > 0) {
            int startminute = banzhija.getJSONObject(0).getIntValue("startminute");
            int endminute = banzhija.getJSONObject(0).getIntValue("endminute");

            if (gotowork) {
                Date date = DateHelper.addMinutes(DateHelper.parse(signtime, "yyyy-MM-dd"), startminute);
                String time = DateHelper.format(date);
                String sql = "INSERT INTO tb_time_signcard(uid,infoid,signtime,workdate,cardindex,ftype,remark,createdate,creatorid,modifydate,modifierid,auditingdate,auditingid,auditing,status) " +
                        "SELECT uuid(),uid,'" + time + "','" + signtime + "'," + cardindex + "," + 7 + ",'" + remark + "',now(),'" + creatorid + "',now(),'" + creatorid + "',now(),'" + creatorid + "','" + auditing + "',1 FROM tb_card_teachstudinfo WHERE uid = '" + infoid + "'";
                dbService.excuteSql(sql);
            }
            if (gooffwork) {
                Date date = DateHelper.addMinutes(DateHelper.parse(signtime, "yyyy-MM-dd"), endminute);
                String time = DateHelper.format(date);
                int index = Integer.parseInt(cardindex) + 1;
                String sql = "INSERT INTO tb_time_signcard(uid,infoid,signtime,workdate,cardindex,ftype,remark,createdate,creatorid,modifydate,modifierid,auditingdate,auditingid,auditing,status) " +
                        "SELECT uuid(),uid,'" + time + "','" + signtime + "'," + index + "," + 8 + ",'" + remark + "',now(),'" + creatorid + "',now(),'" + creatorid + "',now(),'" + creatorid + "','" + auditing + "',1 FROM tb_card_teachstudinfo WHERE uid = '" + infoid + "'";
                dbService.excuteSql(sql);
            }
            syslogs.Write(request, "考勤日报表", String.format("添加签卡信息,人员：%s", infoid));
        } else {
            return Json.getJsonResult(false, "班制不存在");
        }
        return Json.getJsonResult(true);
    }

    public String ExportData(HttpServletRequest request, HttpServletResponse response,String orgcode, String key, String extend, String columns) throws IOException {
        JSONArray columnslist = JSONArray.parseArray(columns);
        JsonData result = getDailyReportList(request, response,orgcode, key, extend, 0, 0);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray datalist = result.getData();

        JSONArray cells = new JSONArray();
        for (int i = 0; i < columnslist.size(); i++) {
            JSONObject c = columnslist.getJSONObject(i);
            JSONObject cell = new JSONObject();
            cell.put("name", c.getString("text"));
            cell.put("datakey", c.getString("dataIndex"));
            cell.put("size", c.getIntValue("width") / 10);
            cells.add(cell);
        }
        List<CellRangeAddress> slist = new ArrayList<CellRangeAddress>();
        List<JSONArray> heads = new ArrayList<JSONArray>();
        String title= "日常情况";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";
        String filepath = ExcelUtil.ExportExcel(datalist, cells, WebConfig.getUploaddir(), "temp", filename,title,slist, heads);
        return filepath;
    }

    public JsonResult analysisAttendance(HttpServletRequest request, HttpServletResponse response, final String startdate, final String enddate) {
        try {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    long startTimeMillis = System.currentTimeMillis();
                    Log.info(AnalysisPersonRateService.class, "开始每日考勤报表分析:" + startTimeMillis);
                    try {
                        //将String类型的数据转换Date,方便后续Calendar类型自增
                        String sql = "SELECT infoid FROM `campusos`.`tb_time_schedule` WHERE `daily` = ? ";
                        JSONArray list = dbService.QueryList(sql, startdate);
                        if (list.size()>0){
                            List<String> idLs = new ArrayList<>();
                            for (int i = 0; i < list.size(); i++) {
                                idLs.add(list.getJSONObject(i).getString("infoid"));
                            }
                            int batchSize = 75;
                            int totalSize = list.size();
                            List<String> idBatchLs = IntStream.range(0, totalSize)
                                    .filter(i -> i % batchSize == 0)
                                    .mapToObj(i -> {
                                        List<String> idBatch = idLs.subList(i, Math.min(i + batchSize, totalSize));
                                        StringJoiner joiner = new StringJoiner(",");
                                        for (String id : idBatch) {
                                            joiner.add(id);
                                        }
                                        return joiner.toString();
                                    })
                                    .collect(Collectors.toList());

                            Log.info(this.getClass(), String.format("开始分析考勤日报表%s至%s", startdate, enddate));
                            for (String idBatch : idBatchLs) {
                                dbService.excuteTransaction(connection -> {
                                    String sql1 = "CALL sp_time_daily_analysis(?,?,?,?,?,?)";
                                    PreparedStatement ps = connection.prepareStatement(sql1);
                                    ps.setString(1, startdate);
                                    ps.setString(2, enddate);
                                    ps.setString(3, idBatch);
                                    ps.setString(4, null);
                                    ps.setString(5, null);
                                    ps.setInt(6, 0);
                                    ps.executeUpdate();
                                    ps.close();
                                });
                            }

                            Log.info(this.getClass(), String.format("分析考勤日报表%s至%s结束", startdate, enddate));

                        }
                        JSONObject msg = new JSONObject();
                        msg.put("msgtype", 1000);
                        msg.put("msg", "分析成功");
                        WebSocketSets.getInstance().send("sysmsg", msg.toJSONString());
                        long endTimeMillis = System.currentTimeMillis();
                        Log.info(AnalysisPersonRateService.class, "分析考勤日报表执行花费时间:" + (endTimeMillis - startTimeMillis));
                    } catch (Exception e) {
                        Log.error(AnalysisPersonRateService.class, "分析考勤日报表异常：" + e.getMessage());
                    }
                }
            }).start();
            return Json.getJsonResult(true);
        } catch (Exception e) {
            Log.error(AnalysisPersonRateService.class, "分析考勤日报表：" + e.getMessage());
            return Json.getJsonResult(false, e.getMessage());
        }
    }
}
