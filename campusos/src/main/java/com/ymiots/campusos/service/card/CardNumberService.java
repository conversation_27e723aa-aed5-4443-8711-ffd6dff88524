package com.ymiots.campusos.service.card;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.DBService;
import com.ymiots.campusos.entity.ConsumeApplyRecord;
import com.ymiots.campusos.service.SysConfigService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.datafactory.CallbackTransaction;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CardNumberService {

    @Autowired
    private DBService dbService;

    @Autowired
    SysConfigService sysconfig;

    /**
     * 自动生成卡号
     */
    public JsonResult autoGenerateCardNumber() throws Exception {
        // 如果配置文件开启就进行
        String autoCard = sysconfig.get("autoCard");
        if (StringUtils.isNotEmpty(autoCard) && autoCard.equals("1")) {
            // 查询没有卡号的人员信息
            String sql = "select uid from tb_card_teachstudinfo where card is null ";
            List<String> infosList = dbService.queryFields(sql, String.class);

            for (String userId : infosList) {
                dbService.excuteTransaction(new CallbackTransaction() {
                    @Override
                    public void execute(Connection connection) throws SQLException {
                        // 生成卡号
                        String card = generateCardNumber();
                        if (StringUtils.isNotBlank(card)) {
                            // 更新人员表的卡号
                            String updateTeachStudInfoSql = "update tb_card_teachstudinfo set card = ?, cardsn = ? where uid = ?";
                            try (PreparedStatement ps = connection.prepareStatement(updateTeachStudInfoSql)) {
                                ps.setString(1, card);
                                ps.setString(2, card);
                                ps.setString(3, userId);
                                ps.executeUpdate();
                            }

                            // 插入卡信息表
                            String cardId = UUID.randomUUID().toString();
                            String insertCardInfoSql = "insert into tb_card_cardinfo(uid, infoid, usedes, cardno, cardsn, deposit, ismain, balance, vicewallet, waterwallet, timescount, enddate, createdate, creatorid, modifydate, modifierid, status) "
                                    + "values (?, ?, '本人', ?, ?, 0, 1, 0, 0, 0, 0, '2050-01-01', now(), 'dev', now(), 'dev', 1)";
                            try (PreparedStatement ps = connection.prepareStatement(insertCardInfoSql)) {
                                ps.setString(1, cardId);
                                ps.setString(2, userId);
                                ps.setString(3, card);
                                ps.setString(4, card);
                                ps.executeUpdate();
                            }

                            // 插入卡操作记录表
                            String insertCardOpRecordSql = "insert into tb_card_cardinfo_oprecord(uid, infoid, cardid, cardno, cardsn, dotype, ismain, usedes, creator, infocode, des, createdate, creatorid) "
                                    + "values (uuid(), ?, ?, ?, ?, 0, 1, '本人', 'dev', '0', '自动人员档案创建发卡', now(), 'dev')";
                            try (PreparedStatement ps = connection.prepareStatement(insertCardOpRecordSql)) {
                                ps.setString(1, userId);
                                ps.setString(2, cardId);
                                ps.setString(3, card);
                                ps.setString(4, card);
                                ps.executeUpdate();
                            }

                            // 更新操作记录表的 infocode
                            String updateOpRecordSql = "update tb_card_cardinfo_oprecord op "
                                    + "inner join tb_card_teachstudinfo info on info.uid = op.infoid "
                                    + "set op.infocode = info.code, op.infoname = info.name, op.orgname = getorgname(info.orgcode) "
                                    + "where op.infocode = '0'";
                            try (PreparedStatement ps = connection.prepareStatement(updateOpRecordSql)) {
                                ps.executeUpdate();
                            }
                        }
                    }
                });
            }
        }
        JsonResult jsonResult = FaceAutoDownTask();
        return jsonResult;
    }


    public String generateCardNumber() {
        Random random = new Random();
        String card = "";
        boolean isDuplicate = true;

        while (isDuplicate) {
            // 生成一个以 1、2 或 3 开头的 8 位随机数
            int prefix = random.nextInt(3) + 1; // 生成 1 到 3 的随机数
            int number = random.nextInt(100000000); // 生成 0 到 99999999 的随机数
            card = String.format("00%d%08d", prefix, number); // 格式化成 10 位数，前两位补 00

            // 检查数据库中是否存在该卡号
            int count = dbService.getCount("tb_card_teachstudinfo", "card = '" + card + "'");
            if (count == 0) {
                isDuplicate = false; // 如果数据库中不存在，退出循环
            }
        }

        return card; // 返回生成的卡号
    }

    public JsonResult FaceAutoDownTask() {
        //防止预约成功以后没有推送
        String queryNow = "select ar.uid infoId,fi.uid faceId " +
                "FROM tb_card_teachstudinfo ar join tb_face_infoface fi on fi.infoid = ar.uid " +
                " where  ar.status = 1 and ar.createdate > DATE_SUB(NOW(), INTERVAL 3 DAY)";
        // 获取当前日期
        List<ConsumeApplyRecord> applyRecords = dbService.queryList(queryNow, ConsumeApplyRecord.class);
        try {
            if (!CollectionUtil.isEmpty(applyRecords)) {
                //如果为人脸授权
                if ("1".equals(sysconfig.get("EnableFaceGroup"))) {
                    //获取权限组
                    String selFaceGroupSql = "select groupid groupId,controllerid controllerId from tb_face_authorize_group_cfg order by createdate desc ";
                    List<FaceGroupDto> groupDtos = dbService.queryList(selFaceGroupSql, FaceGroupDto.class);

                    // 1. 查询已存在的 (infoid, devid) 组合
                    String existSql = "select infoid infoId, devid controllerId from tb_face_authorize_group_access";
                    List<FaceGroupDto> existList = dbService.queryList(existSql, FaceGroupDto.class);
                    Set<String> existSet = existList.stream()
                            .map(arr -> arr.getInfoId() + "_" + arr.getControllerId())
                            .collect(Collectors.toSet());

                    if (!CollectionUtil.isEmpty(groupDtos)) {
                        // 只保留每个 controllerId 的第一个对象
                        List<FaceGroupDto> uniqueControllerList = new ArrayList<>(groupDtos.stream()
                                .collect(Collectors.toMap(
                                        FaceGroupDto::getControllerId, // 以 controllerId 作为 key
                                        dto -> dto,                    // value 就是对象本身
                                        (existing, replacement) -> existing // 有重复时保留第一个
                                ))
                                .values());

                        // 对每个唯一的controllerId进行处理
                        String insertSql = "INSERT INTO tb_face_authorize_group_access " +
                                "(uid, groupid, infoid, devid, downmsg, downnum, facedownstatus, fingerdownstatus, downstatus, createdate, creatorid, status) " +
                                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, now(), ?, ?)";

                        for (FaceGroupDto faceGroupDto : uniqueControllerList) {
                            String groupId = faceGroupDto.getGroupId();
                            String controllerId = faceGroupDto.getControllerId(); // 这里就是devid

                            for (ConsumeApplyRecord applyRecord : applyRecords) {
                                String infoId = applyRecord.getInfoId();
                                // 你可以根据业务逻辑判断是否需要授权，比如是否已经授权过等
                                String key = infoId + "_" + controllerId;
                                if (existSet.contains(key)) {
                                    continue; // 已存在，跳过
                                }
                                // 示例插入
                                dbService.excuteSql(
                                        insertSql,
                                        UUID.randomUUID().toString(), // uid
                                        groupId,
                                        infoId,
                                        controllerId,
                                        "", // downmsg
                                        0, // downnum
                                        null, // facedownstatus
                                        null, // fingerdownstatus
                                        0, // downstatus
                                        "dev", // creatorid
                                        1 // status
                                );
                            }
                        }
                    }
                } else {
                    String allDevIdSql = "select uid from tb_dev_accesscontroller where devclass = 9 ";
                    List<String> allDevIdList = dbService.queryFields(allDevIdSql, String.class);
                    for (ConsumeApplyRecord consumeApplyRecord : applyRecords) {
                        String downDevIdSql = "select devid from tb_face_authorizeface where infoid = '" + consumeApplyRecord.getInfoId() + "'";
                        List<String> downDevIdList = dbService.queryFields(downDevIdSql, String.class);

                        //取出需要下载的
                        List<String> needDownDevIdList = allDevIdList.stream()
                                .filter(i -> !downDevIdList.contains(i))
                                .collect(Collectors.toList());
                        //取出时段方案(为1的)
                        String timeSql = "select uid from tb_face_timescheme order by code limit 1 ";
                        String timeId = dbService.queryOneField(timeSql, String.class);
                        String saveSql = "INSERT INTO `tb_face_authorizeface` (`uid`, `devid`, `infoid`, `timeid`, `faceid`, `allowcard`, `fingerdownstatus`, `infodownstatus`, `timedownstatus`, `downmsg`, `downnum`, `downtime`, `status`, `creatorid`, `createdate`, `fdid`) " +
                                "VALUES (uuid(), ?, ?, ?, ?, 1, NULL, 0, 0, NULL, 0, NULL, 1, null, now(), NULL)";
                        for (String s : needDownDevIdList) {
                            dbService.excuteSql(saveSql, s, consumeApplyRecord.getInfoId(), timeId, consumeApplyRecord.getFaceId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Json.getJsonResult(true);
    }

    public void autoDeleteTask() {
        //获取到离职人员信息
        final JSONArray list = dbService.QueryList("select uid,code from tb_card_teachstudinfo where status = 0  ");

//        new Thread(new Runnable() {
//            @Override
//            public void run() {
                // TODO Auto-generated method stub
                try {
                    if (list.size() > 0) {
                        dbService.excuteTransaction(new CallbackTransaction() {
                            @Override
                            public void execute(Connection connection) throws SQLException {
                                // TODO Auto-generated method stub
                                for (int i = 0; i < list.size(); i++) {
                                    String infoid = list.getJSONObject(i).getString("uid");

                                    final String sql = "update tb_card_teachstudinfo set status=0,enddate=date_format(now(),'%Y-%m-%d'),card='',cardsn='',modifydate=now(),modifierid='dev' where uid='" + infoid + "';";
                                    final String sql2 = "update tb_card_cardinfo set des='人员离职，设置为无效卡；',modifierid='dev',modifydate=now(),enddate=now(),status=0 where infoid='" + infoid + "' and status in(1,2);";
                                    final String queryuid = "select uid from tb_sys_user where empid ='" + infoid + "'";

                                    PreparedStatement ps = connection.prepareStatement(sql);
                                    ps.executeUpdate();
                                    ps.close();

                                    ps = connection.prepareStatement(sql2);
                                    ps.executeUpdate();
                                    ps.close();

                                    ps = connection.prepareStatement(queryuid);
                                    ResultSet rs = ps.executeQuery();
                                    String uid = null;
                                    while (rs.next()) {
                                        uid = rs.getString("uid");
                                    }
                                    ps.close();
                                    rs.close();

                                    final String delrole = "delete from tb_sys_role_user where userid = '" + uid + "'";
                                    ps = connection.prepareStatement(delrole);
                                    ps.executeUpdate();
                                    ps.close();

                                    final String deluser = "delete from tb_sys_user where empid ='" + infoid + "'";
                                    ps = connection.prepareStatement(deluser);
                                    ps.executeUpdate();
                                    ps.close();

                                    ps = connection.prepareStatement("call sp_info_leaveoffice('" + infoid + "')");
                                    ps.executeUpdate();
                                    ps.close();

                                    Log.info(TeachStudCardService.class, "成功处理人员离职:" + infoid);
                                }
                            }
                        });
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
//            }
//        });
    }

    @Data
    public static class FaceGroupDto {
        private String controllerId;
        private String groupId;
        private String infoId;
    }
}
