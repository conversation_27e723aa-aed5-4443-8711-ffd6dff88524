package com.ymiots.campusos.service.hotel;


import java.util.ArrayList;
import java.util.Date;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymiots.campusos.redis.RedisPublish;
import com.ymiots.campusos.util.weixin.entity.TemplateMessageData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.redis.RedisMessageEntity;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysConfigService;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;

@Repository
public class InAuditService extends BaseService{
	@Autowired
	UserRedis userredis;

	@Autowired
	SysConfigService sysconfig;

	@Autowired
    RedisPublish redispublish;

	public JsonData getSubscribeList(HttpServletRequest request, HttpServletResponse response, int status,
			String key, String starttime, String endtime, int start, int limit) {
		String fields = "ha.uid,ha.infoid,ha.checkintime,ha.checkouttime,ha.createdate,ha.remark, " +
                "ha.auditremark,ha.status,ct.name,ct.sex,ct.idcard,ct.mobile,getminiorgname(ct.orgcode) as orgname,vn.name as reviewername ";
		String table = "tb_hotel_apply ha " +
                "left join tb_card_teachstudinfo ct on ha.infoid=ct.uid " +
                "left join tb_card_teachstudinfo vn on vn.uid=ha.reviewerid" ;
		String where = " 1=1 ";
		if(status != -2) {
			where+=" and  ha.status='"+status+"' ";
		}
		if (!StringUtils.isBlank(starttime) && !StringUtils.isBlank(endtime)) {
			where += " and (ha.createdate between '" + starttime + "' and '" + endtime + "')";
		}
		if (!StringUtils.isBlank(key)) {
			where += " and (ct.name like '%" + key + "%' or ct.idcard like '%" + key + "%' or ct.mobile like '%"
					+ key + "%') ";
		}
		String orderby = ExtSort.Orderby(request, "ha.createdate desc");
		JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);

		return result;
	}

	public JsonResult DelSubscribe(HttpServletRequest request, HttpServletResponse response, String uids) {
		String sql = "delete from tb_hotel_apply where find_in_set(uid,'" + uids + "')>0";
		dbService.excuteSql(sql);
		return Json.getJsonResult(true);
	}


	public JsonResult AuditTempInfo(HttpServletRequest request, HttpServletResponse response, String status, String auditremark, String uid) {
		String reviewerid=userredis.get(request).getEmpid();
        //获取系统人员信息，判断是否管理员
        int administratorsCount = dbService.getCount("tb_hotel_administrators", "infoid='" + reviewerid + "'");
        if (administratorsCount == 0) {
            return Json.getJsonResult(false, "您不是宿舍管理员");
        }
        int isAuditor = dbService.getCount("tb_hotel_administrators", "infoid='" + reviewerid + "' AND isauditor=1");

        //查询记录状态，如果是待审核人审核，目前审核人是管理员，则无法审核
        String applySQL = "SELECT ha.infoid,ct.name,ha.status,ct.orgcode,getminiorgname(ct.orgcode) as orgname " +
                "FROM tb_hotel_apply ha LEFT JOIN tb_card_teachstudinfo ct on ha.infoid=ct.uid WHERE uid=?";;
        JSONArray applyJa = dbService.QueryList(applySQL, uid);
        if (applyJa.isEmpty()) {
            return Json.getJsonResult(false, "无法获取记录");
        }
        int nowStatus = applyJa.getJSONObject(0).getIntValue("status");
        if(isAuditor == 0 && nowStatus == 0){
            return Json.getJsonResult(false, "审核人暂未审核，管理员无法审核");
        }

        String userName = userredis.get(request).getName();
        String remark = userName + ":审核通过";
        if (StringUtils.isNotBlank(auditremark)) {
            remark += "," + auditremark;
        }
        String updateSQL = "UPDATE tb_hotel_apply SET status=?,auditremark=?,reviewerid=? WHERE uid=?";

        //审核员审核通过
        if(isAuditor == 1 && "1".equals(status)){
            dbService.excuteSql(updateSQL, "1", auditremark, reviewerid,uid);

            String sql = "SELECT wu.uid,wu.openid FROM tb_weixin_user wu INNER JOIN tb_hotel_administrators ha ON ha.infoid=wu.docid WHERE wu.status=1 AND ha.isauditor=0 ";
            JSONArray wxja = dbService.QueryList(sql);
            if (!wxja.isEmpty()) {
                for (int i = 0; i < wxja.size(); i++) {
                    String openid = wxja.getJSONObject(i).getString("openid");
                    String wxuserid = wxja.getJSONObject(i).getString("uid");
                    this.pushAuditToAdmin(uid, openid, wxuserid, remark, userName);
                }
            }
        }else if(isAuditor == 2 && "1".equals(status)){ //管理员审核通过
            dbService.excuteSql(updateSQL, "2", auditremark, reviewerid,uid);

            String sql = "SELECT wu.uid,wu.openid FROM tb_weixin_user wu INNER JOIN tb_hotel_apply ha ON ha.infoid = wu.docid WHERE ha.uid=?";
            JSONArray ja = dbService.QueryList(sql, uid);
            if (!ja.isEmpty()) {
                String wxuserid = ja.getJSONObject(0).getString("uid");
                String openid = ja.getJSONObject(0).getString("openid");
                this.pushSuccessToInfo(openid, wxuserid, uid);
            }

            //通知主管后勤的院一级别的领导审核完成
            String leaderSQL = "SELECT uid FROM tb_card_teachstudinfo WHERE posrank=1";
            JSONArray leaderJa = dbService.QueryList(leaderSQL);
            if (!leaderJa.isEmpty()) {
                ArrayList<String> leaderList = new ArrayList<>();
                String applierName = applyJa.getJSONObject(0).getString("name");
                String orgname = applyJa.getJSONObject(0).getString("orgname");
                for (int j = 0; j < leaderJa.size(); j++) {
                    leaderList.add(leaderJa.getJSONObject(j).getString("uid"));
                    String wxSQL = "SELECT wu.uid,wu.openid FROM tb_weixin_user wu WHERE wu.status=1 AND wu.docid IN ('"+String.join("','",leaderList)+"')";
                    JSONArray wxJa = dbService.QueryList(wxSQL);
                    if (!wxJa.isEmpty()) {
                        for (int i = 0; i < wxJa.size(); i++) {
                            String wxuserid = ja.getJSONObject(0).getString("uid");
                            String openid = ja.getJSONObject(0).getString("openid");
                            this.pushMsgToLeader(wxuserid, openid, applierName, orgname);
                        }
                    }
                }
            }
        }else{ //管理员或审核员审核不通过
            dbService.excuteSql(updateSQL, "3", auditremark, reviewerid,uid);

            String sql = "SELECT wu.uid,wu.openid FROM tb_weixin_user wu INNER JOIN tb_hotel_apply ha ON ha.infoid = wu.docid WHERE ha.uid=?";
            JSONArray ja = dbService.QueryList(sql, uid);
            if (!ja.isEmpty()) {
                String wxuserid = ja.getJSONObject(0).getString("uid");
                String openid = ja.getJSONObject(0).getString("openid");
                this.pushFalseToInfo(openid, wxuserid, remark, uid);
            }
        }
		return Json.getJsonResult(true);
	}

    public void pushAuditToAdmin(String uid, String openid,String wxuserid,String remark,String name){
        String topic = "/campus/weixintemplatemsg";
        RedisMessageEntity message = new RedisMessageEntity();
        JSONObject data = new JSONObject();
        data.put("msgid", UUID.randomUUID().toString());
        data.put("openid", openid);
        data.put("wxuserid", wxuserid);
        data.put("cfgcode", "OPENTM206831895");
        data.put("url", String.format("%s/hotel/tempcheckin/auditcheckinapply?uid=%s", WebConfig.getDomain(), uid));

        JSONObject wxdata = new JSONObject();
        wxdata.put("first", new TemplateMessageData("您好，您有一条入住申请待审核。", "#4caf50").toJSONString());
        wxdata.put("keyword1", new TemplateMessageData(name).toJSONString());
        wxdata.put("keyword2", new TemplateMessageData("入住申请").toJSONString());
        wxdata.put("keyword3", new TemplateMessageData(DateHelper.format(new Date())).toJSONString());
        wxdata.put("keyword4", new TemplateMessageData(remark).toJSONString());
        wxdata.put("remark", new TemplateMessageData("点击查看详情，请及时处理！").toJSONString());
        data.put("wxtempdata", wxdata);
        message.setCmd("weixintemplatemsg");
        message.setData(data);
        message.setDevtype(0);
        redispublish.Publish(topic, message);
    }

    public void pushMsgToLeader(String wxuserid, String openid,String applierName,String orgname){
        String topic = "/campus/weixintemplatemsg";
        RedisMessageEntity message = new RedisMessageEntity();
        JSONObject data = new JSONObject();
        data.put("msgid", UUID.randomUUID().toString());
        data.put("openid", openid);
        data.put("wxuserid", wxuserid);
        data.put("cfgcode", "OPENTM411211455");
        JSONObject wxdata = new JSONObject();
        wxdata.put("first", new TemplateMessageData("您有一条入住申请审核结果。").toJSONString());
        wxdata.put("keyword1", new TemplateMessageData(applierName+"入住申请").toJSONString());
        wxdata.put("keyword2", new TemplateMessageData("宿舍入住申请-审核完成").toJSONString());
        wxdata.put("keyword3", new TemplateMessageData(DateHelper.format(new Date())).toJSONString());
        wxdata.put("remark", new TemplateMessageData(orgname+applierName+"宿舍入住申请通过。").toJSONString());
        data.put("wxtempdata", wxdata);
        message.setCmd("weixintemplatemsg");
        message.setData(data);
        message.setDevtype(0);
        redispublish.Publish(topic, message);
    }

    public void pushSuccessToInfo(String openid,String wxuserid,String uid){
        String topic = "/campus/weixintemplatemsg";
        RedisMessageEntity message = new RedisMessageEntity();
        JSONObject data = new JSONObject();
        data.put("msgid", UUID.randomUUID().toString());
        data.put("openid", openid);
        data.put("wxuserid", wxuserid);
        data.put("cfgcode", "OPENTM411211455");
        data.put("url", String.format("%s/hotel/tempcheckin/auditcheckinapply?uid=%s", WebConfig.getDomain(), uid));

        JSONObject wxdata = new JSONObject();
        wxdata.put("first", new TemplateMessageData("您好，您提交的宿舍入住申请已审核完成").toJSONString());
        wxdata.put("keyword1", new TemplateMessageData("宿舍入住申请").toJSONString());
        wxdata.put("keyword2", new TemplateMessageData("审核已通过！", "#4caf50").toJSONString());
        wxdata.put("keyword3", new TemplateMessageData(DateHelper.format(new Date())).toJSONString());
        wxdata.put("remark", new TemplateMessageData("点击查看详情，请尽快联系宿舍管理员安排入住！").toJSONString());
        data.put("wxtempdata", wxdata);
        message.setCmd("weixintemplatemsg");
        message.setData(data);
        message.setDevtype(0);
        redispublish.Publish(topic, message);
    }

    public void pushFalseToInfo(String openid,String wxuserid,String remark,String uid){
        String topic = "/campus/weixintemplatemsg";
        RedisMessageEntity message = new RedisMessageEntity();
        JSONObject data = new JSONObject();
        data.put("msgid", UUID.randomUUID().toString());
        data.put("openid", openid);
        data.put("wxuserid", wxuserid);
        data.put("cfgcode", "OPENTM403107564");
        data.put("url", String.format("%s/hotel/tempcheckin/auditcheckinapply?uid=%s", WebConfig.getDomain(), uid));

        JSONObject wxdata = new JSONObject();
        wxdata.put("first", new TemplateMessageData("您好，您提交的宿舍入住申请已审核完成").toJSONString());
        wxdata.put("keyword1", new TemplateMessageData("审核未通过!", "#ce0404").toJSONString());
        wxdata.put("keyword2", new TemplateMessageData(remark).toJSONString());
        wxdata.put("keyword3", new TemplateMessageData(DateHelper.format(new Date())).toJSONString());
        wxdata.put("remark", new TemplateMessageData("点击查看详情，如有疑问请联系被访人或宿舍管理员。").toJSONString());
        data.put("wxtempdata", wxdata);
        message.setCmd("weixintemplatemsg");
        message.setData(data);
        message.setDevtype(0);
        redispublish.Publish(topic, message);
    }

}
