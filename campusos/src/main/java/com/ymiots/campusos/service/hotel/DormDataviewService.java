package com.ymiots.campusos.service.hotel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.DBService;
import com.ymiots.campusos.config.redis.RedisClient;
import com.ymiots.campusos.dto.OrgframeworkDto;
import com.ymiots.campusos.dto.SignStudentDto;
import com.ymiots.campusos.dto.SignStudentDtos;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.vo.hotel.BuildingVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Repository
public class DormDataviewService {
    @Autowired
    DBService dbService;
    @Autowired
    UserRedis userredis;
    @Value("${property}")
    String property;

    @Autowired
    RedisClient redisclient;



    public HashMap<String, String> getDormTotal(HttpServletRequest request, HttpServletResponse response, String id){
        HashMap<String, String> map = new HashMap<>();
        if (StringUtils.isEmpty(id)){
            map.put("allhouse","0");
            map.put("allpeople","0");
            map.put("noreturnpeople","0");
            map.put("unusedhouse","0");
            map.put("noreturnhouse","0");
            map.put("rate","0");
        }else {
            String sql="SELECT uid from tb_dev_areaframework where parentid='"+id+"'";
            JSONArray list = dbService.QueryList(sql);
            if (list.size()>0){
                map.put("allhouse",String.valueOf(list.size()));
            }else {
                map.put("allhouse","0");
            }
            String sqlpeople="SELECT a.infoid as id,a.houseid as houseid from tb_hotel_bed a join tb_dev_areaframework b on a.houseid = b.uid where a.infoid is not null and b.parentid='"+id+"'";
            JSONArray peopleList = dbService.QueryList(sqlpeople);
            if (peopleList.size()>0){
                map.put("allpeople",String.valueOf(peopleList.size()));
            }else {
                map.put("allpeople","0");
            }
            String sqlNoReturn1="SELECT direction FROM tb_alleyway_records WHERE infoid = '";
            String sqlNoReturn2="' ORDER BY  recordtime DESC LIMIT 1";
            //未归房间数
            List<String> arrayList = new ArrayList<>();
            int count = 0;
            for (int i = 0; i < peopleList.size(); i++) {
                String infoid =peopleList.getJSONObject(i).getString("id");
                String houseid =peopleList.getJSONObject(i).getString("houseid");
                JSONObject jsonObject = dbService.QueryObject(sqlNoReturn1 + infoid + sqlNoReturn2, JSONObject.class);
                if (jsonObject!=null){
                    String direction = jsonObject.getString("direction");
                    if (direction.equals("2")){
                        count++;
                        if (!arrayList.contains(houseid)){
                            arrayList.add(houseid);
                        }
                    }
                }else {
                    count++;
                    if (!arrayList.contains(houseid)) {
                        arrayList.add(houseid);
                    }
                }
            }
            map.put("noreturnpeople",String.valueOf(count));

//        String sqlNoHouse="SELECT b.uid from tb_hotel_bed a join tb_dev_areaframework b on a.houseid = b.uid JOIN tb_alleyway_records c ON c.infoid = b.uid where a.infoid is not null AND c.direction = '2' AND b.parentid='"+id+"' GROUP by b.uid";
//        JSONArray sqlNoHouseList = dbService.QueryList(sqlNoHouse);
            if (arrayList.size()>0){
                map.put("noreturnhouse",String.valueOf(arrayList.size()));
            }else {
                map.put("noreturnhouse","0");
            }

            String sqlunused="SELECT b.uid,count( 1 ) AS count,c.count FROM tb_hotel_bed a JOIN tb_dev_areaframework b ON a.houseid = b.uid JOIN (SELECT b.uid,count( 1 ) AS count FROM tb_hotel_bed a JOIN tb_dev_areaframework b ON a.houseid = b.uid WHERE b.parentid = '"+id+"' GROUP BY b.uid ) c ON c.uid = b.uid WHERE a.infoid IS NULL AND b.parentid = '"+id+"' GROUP BY b.uid HAVING count = c.count";
            JSONArray sqlNoUsedList = dbService.QueryList(sqlunused);
            if (sqlNoUsedList.size()>0){
                map.put("unusedhouse",String.valueOf(sqlNoUsedList.size()));
            }else {
                map.put("unusedhouse",String.valueOf(list.size()));
            }
            Integer backpeople = Integer.valueOf(map.get("allpeople"))-Integer.valueOf(map.get("noreturnpeople"));
            new BigDecimal(backpeople);
            if (backpeople>=0&&Integer.valueOf(map.get("allpeople"))!=0){
                BigDecimal rate= new BigDecimal(backpeople).divide(new BigDecimal(map.get("allpeople")),2,BigDecimal.ROUND_HALF_UP);
                map.put("rate",String.valueOf((rate.multiply(new BigDecimal(100))).setScale(0)));
            }else {
                map.put("rate","0");
            }
        }
        return map;
    }

    public HashMap<String, String> getDormTotal2(HttpServletRequest request, HttpServletResponse response, String id) {
        HashMap<String, String> map = new HashMap<>();
        if (StringUtils.isEmpty(id)) {
            map.put("allpeople", "0");
            map.put("noreturnpeople", "0");
            map.put("returnpeople", "0");
            map.put("rate", "0");
        } else {
            String sqlpeople = "SELECT count(*) as count FROM tb_card_teachstudinfo WHERE property = 3 AND `status` = 1 AND infotype = 2 AND sex =" + id;
            int count = dbService.QueryJSONObject(sqlpeople).getIntValue("count");
            //未归
            int size = getLateRecord(request, response, id).size();

            map.put("allpeople", String.valueOf(count));

            map.put("noreturnpeople", String.valueOf(size));
            //晚归
            Integer backpeople = getNoHomeRecord(request, response, id).size();
            map.put("returnpeople", String.valueOf(backpeople));
            new BigDecimal(backpeople);
            if (backpeople >= 0 && Integer.valueOf(map.get("allpeople")) != 0) {
                BigDecimal rate = new BigDecimal(count-size-backpeople).divide(new BigDecimal(map.get("allpeople")), 2, BigDecimal.ROUND_HALF_UP);
                map.put("rate", String.valueOf((rate.multiply(new BigDecimal(100))).setScale(0)));
            } else {
                map.put("rate", "0");
            }
        }
        return map;
    }
    public HashMap<String, Integer> getQuYuTotal(HttpServletRequest request, HttpServletResponse response) {
        HashMap<String, Integer> map = new HashMap<>();
        JSONObject object = dbService.QueryJSONObject("SELECT \n" +
                "    SUM(CASE WHEN direction = 1 THEN 1 ELSE 0 END) AS jingpeople,\n" +
                "    SUM(CASE WHEN direction = 2 THEN 1 ELSE 0 END) AS chupeople,\n" +
                "    COUNT(*) AS allpeople,\n" +
                "    ROUND(SUM(CASE WHEN direction = 2 THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) AS rate\n" +
                "FROM \n" +
                "    (SELECT \n" +
                "         t1.areacode,t1.direction \n" +
                "     FROM \n" +
                "         tb_alleyway_records t1\n" +
                "     INNER JOIN \n" +
                "         (SELECT \n" +
                "              infoid, MAX(recordtime) AS max_time\n" +
                "          FROM \n" +
                "              tb_alleyway_records\n" +
                "          WHERE \n" +
                "              recordtime >= DATE_SUB(CURDATE(), INTERVAL 14 DAY) -- 过滤当天的数据\n" +
                "          GROUP BY \n" +
                "              infoid) t2\n" +
                "     ON \n" +
                "         t1.infoid = t2.infoid AND t1.recordtime = t2.max_time\n" +
                "     WHERE \n" +
                "         t1.status = 1 -- 可选：过滤有效记录\n" +
                "    ) latest_records;");
        map.put("allpeople", object.getIntValue("allpeople"));
        map.put("jingpeople", object.getIntValue("jingpeople"));
        map.put("chupeople", object.getIntValue("chupeople"));
        map.put("rate", object.getIntValue("rate"));
        return map;
    }

    public List<JSONObject> getQuYuRecord(HttpServletRequest request, HttpServletResponse response) {
        //JSONArray array = dbService.QueryList("SELECT \n" +
        //        "    getminiareaname(areacode) AS areaname,\n" +
        //        "    SUM(CASE WHEN direction = 1 THEN 1 ELSE 0 END) AS jingpeople,\n" +
        //        "    SUM(CASE WHEN direction = 2 THEN 1 ELSE 0 END) AS chupeople,\n" +
        //        "    COUNT(*) as allpeople,\n" +
        //        "    (COUNT(*)-SUM(CASE WHEN direction = 2 THEN 1 ELSE 0 END)) AS onpeople\n" +
        //        "FROM \n" +
        //        "    (SELECT \n" +
        //        "         t1.areacode,t1.direction \n" +
        //        "     FROM \n" +
        //        "         tb_alleyway_records t1\n" +
        //        "     INNER JOIN \n" +
        //        "         (SELECT \n" +
        //        "              areacode,infoid, MAX(recordtime) AS max_time\n" +
        //        "          FROM \n" +
        //        "              tb_alleyway_records\n" +
        //        "          WHERE \n" +
        //        "              DATE(recordtime) = CURDATE()\n" +
        //        "          GROUP BY \n" +
        //        "              areacode,infoid) t2\n" +
        //        "     ON \n" +
        //        "         t1.infoid = t2.infoid AND t1.recordtime = t2.max_time\n" +
        //        "     WHERE \n" +
        //        "         t1.status = 1\n" +
        //        "    ) latest_records\n" +
        //        "GROUP BY \n" +
        //        "    areacode\n" +
        //        "ORDER BY \n" +
        //        "    areacode;\n");
        JSONArray total = dbService.QueryList("SELECT \n" +
                "         t1.direction,count(1) count\n" +
                "     FROM \n" +
                "         tb_alleyway_records t1\n" +
                "     INNER JOIN \n" +
                "         (SELECT \n" +
                "              infoid, MAX(recordtime) AS max_time\n" +
                "          FROM \n" +
                "              tb_alleyway_records\n" +
                "          WHERE \n" +
                "              recordtime >= DATE_SUB(CURDATE(), INTERVAL 14 DAY) and machineid not in ('104','153')\n" +
                "          GROUP BY \n" +
                "              infoid) t2\n" +
                "     ON \n" +
                "         t1.infoid = t2.infoid AND t1.recordtime = t2.max_time\n" +
                "     WHERE \n" +
                "         t1.status = 1 GROUP BY t1.direction");
        JSONObject object = dbService.QueryJSONObject("SELECT \n" +
                "         count(1) count\n" +
                "     FROM \n" +
                "         tb_alleyway_records t1\n" +
                "     INNER JOIN \n" +
                "         (SELECT \n" +
                "              infoid, MAX(recordtime) AS max_time\n" +
                "          FROM \n" +
                "              tb_alleyway_records\n" +
                "          WHERE \n" +
                "              recordtime >= DATE_SUB(CURDATE(), INTERVAL 14 DAY) and machineid in ('104','153')\n" +
                "          GROUP BY \n" +
                "              infoid) t2\n" +
                "     ON \n" +
                "         t1.infoid = t2.infoid AND t1.recordtime = t2.max_time\n" +
                "     WHERE \n" +
                "         t1.status = 1 and t1.direction =1");
        JSONObject kuqu= new JSONObject();
        JSONObject shh= new JSONObject();
        JSONObject shch= new JSONObject();
        String kuquperson=object.getString("count");
        String shenghuoperson="0";
        String shengchangperson="0";
        for (int i = 0; i < total.size(); i++) {
            if ("1".equals(total.getJSONObject(i).getString("direction"))){
                shengchangperson= total.getJSONObject(i).getString("count");
            }
        }
        shenghuoperson = String.valueOf(Integer.valueOf(kuquperson)-Integer.valueOf(shengchangperson));
        kuqu.put("allpeople",kuquperson);
        kuqu.put("areaname","油气库总人数");

        shh.put("allpeople",shenghuoperson);
        shh.put("areaname","生活区人数");

        shch.put("allpeople",shengchangperson);
        shch.put("areaname","生产区人数");
        List<JSONObject> list = new ArrayList<>();
        list.add(kuqu);
        list.add(shh);
        list.add(shch);
        return list;
    }

    public HashMap<String, String> getDormTotal3(HttpServletRequest request, HttpServletResponse response, String id) {
        HashMap<String, String> map = new HashMap<>();

            map.put("allpeople", "0");
            map.put("noreturnpeople", "0");
            map.put("returnpeople", "0");
            map.put("rate", "0");

            String sqlpeople = "SELECT count(*) as count FROM tb_card_teachstudinfo WHERE `status` = 1 AND infotype = 2 ";
            if ("1".equals(property)){
              sqlpeople+= "AND property = 3";
            }
            if (StringUtils.isNotEmpty(id)){
                sqlpeople+="AND sex =" + id;
            }
            int count = dbService.QueryJSONObject(sqlpeople).getIntValue("count");
            //未归
            int size = getNoDormStu(request, response, id).size();

            map.put("allpeople", String.valueOf(count));

            map.put("noreturnpeople", String.valueOf(size));
            //已归
            Integer backpeople = count -size;
            map.put("returnpeople", String.valueOf(backpeople));
            new BigDecimal(backpeople);
            if (backpeople >= 0 && Integer.valueOf(map.get("allpeople")) != 0) {
                BigDecimal rate = new BigDecimal(backpeople).divide(new BigDecimal(map.get("allpeople")), 2, BigDecimal.ROUND_HALF_UP);
                map.put("rate", String.valueOf((rate.multiply(new BigDecimal(100))).setScale(0)));
            } else {
                map.put("rate", "0");
            }

        return map;
    }
    //最近30条通行记录
    public List<JSONObject> getTrafficRecord(HttpServletRequest request, HttpServletResponse response,String sex,String machineid) {
        List<JSONObject> list = new ArrayList<>();
        String trafficRecordSql = "SELECT a.infoname,getorgname(a.orgcode) as class,CASE a.direction WHEN '1' THEN '进' WHEN '2' THEN '出' ELSE '' END AS direction,a.recordimg,DATE_FORMAT(a.recordtime, '%Y-%m-%d %H:%i:%s') AS recordtime FROM tb_alleyway_records a LEFT JOIN tb_card_teachstudinfo b on a.infoid =b.uid  WHERE  DATE_FORMAT( a.recordtime, '%Y-%m-%d' ) = DATE_FORMAT( NOW(), '%Y-%m-%d' ) ";

        if (StringUtils.isNotEmpty(machineid)) {
            trafficRecordSql += "AND a.machineid = '" + machineid + "'";
        }
        if (StringUtils.isNotEmpty(sex)) {
            trafficRecordSql += "AND b.sex = '" + sex + "'";
        }
        trafficRecordSql+=" ORDER BY a.recordtime DESC LIMIT 30";
        JSONArray objects = dbService.QueryList(trafficRecordSql);
        for (int i = 0; i < objects.size(); i++) {
            objects.getJSONObject(i).put("recordtime",objects.getJSONObject(i).getString("recordtime").replace("T"," "));
            list.add(objects.getJSONObject(i));
        }
        return list;
    }

    //最近30条通行记录
    public List<JSONObject> getTrafficAreaRecord(HttpServletRequest request, HttpServletResponse response, String areacode, String machineids){
        List<JSONObject> list = new ArrayList<>();
        String trafficRecordSql = "SELECT a.infoname,getorgname(a.orgcode) as class,CASE a.direction WHEN '1' THEN '进' WHEN '2' THEN '出' ELSE '' END AS direction,a.recordimg,DATE_FORMAT(a.recordtime, '%Y-%m-%d %H:%i:%s') AS recordtime FROM tb_alleyway_records a WHERE  DATE_FORMAT( a.recordtime, '%Y-%m-%d' ) = DATE_FORMAT( NOW(), '%Y-%m-%d' ) ";
        //if (machineids.size()>0){
        //    if (machineids.size()==1){
        //        if (StringUtils.isNotEmpty(machineids.get(0).toString())){
        //            trafficRecordSql += "AND a.machineid = '" + machineids.get(0) + "'";
        //        }
        //    }else {
                //String machineidssql = machineids.stream().collect(Collectors.joining(",", "(", ")"));
        if (machineids.length()>2){
            trafficRecordSql += "AND a.machineid in (" + machineids + ")";
        }
        //    }
        //}

        if (StringUtils.isNotEmpty(areacode)) {
            trafficRecordSql += "AND a.areacode = '" + areacode + "'";
        }
        trafficRecordSql+="  ORDER BY a.recordtime DESC LIMIT 30";
        JSONArray objects = dbService.QueryList(trafficRecordSql);
        for (int i = 0; i < objects.size(); i++) {
            objects.getJSONObject(i).put("recordtime",objects.getJSONObject(i).getString("recordtime").replace("T"," "));
            list.add(objects.getJSONObject(i));
        }
        return list;
    }

    //最近15条通行记录
    public List<JSONObject> getAreaRecord(HttpServletRequest request, HttpServletResponse response){
        List<JSONObject> list = new ArrayList<>();
        String trafficRecordSql = "SELECT a.infoname,getminiorgname(a.orgcode) as class,CASE a.direction WHEN '1' THEN '进' WHEN '2' THEN '出' ELSE '' END AS direction,a.recordimg,DATE_FORMAT(a.recordtime, '%Y-%m-%d %H:%i:%s') AS recordtime FROM tb_alleyway_records a WHERE  DATE_FORMAT( a.recordtime, '%Y-%m-%d' ) = DATE_FORMAT( NOW(), '%Y-%m-%d' ) ";
        trafficRecordSql+="  ORDER BY a.recordtime DESC LIMIT 12";
        JSONArray objects = dbService.QueryList(trafficRecordSql);
        for (int i = 0; i < objects.size(); i++) {
            list.add(objects.getJSONObject(i));
        }
        return list;
    }

    //晚归加未归 和 无数据记录
    public List<JSONObject> getComeBackLateRecord(HttpServletRequest request, HttpServletResponse response,String id) {
        String sex = "";
        if ("1".equals(id)){
            sex ="男";
        }else {
            sex ="女";
        }
        List<JSONObject> list = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDate now = LocalDate.now();
        Date dateTime = new Date();
        String date = dateFormat.format(dateTime);
        String date2 = formatter.format(now);
        //当天的晚上 10 点
        String totdayTen = date2 + " 22:00:00";
        LocalDate yesterday = now.minusDays(1);
        String yester = formatter.format(yesterday);
        String yesterTen = yester.split(" ")[0] + " 22:00:00";
        Date nowDate =null;
        Date TenDate =null;
        try {
            //现在时间
            nowDate = dateFormat.parse(date);
            //当天10点
            TenDate = dateFormat.parse(totdayTen);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (nowDate.after(TenDate)){
            String sql = "SELECT\n" +
                    "\tgetorgname ( t.orgcode ) AS class,\n" +
                    "\tt.NAME AS infoname,\n" +
                    "\tf.imgpath \n" +
                    "FROM\n" +
                    "\ttb_card_teachstudinfo t\n" +
                    "\tLEFT JOIN (\n" +
                    "\tSELECT\n" +
                    "\t\ta.infoid,\n" +
                    "\t\tmax( a.recordtime ) AS outtime \n" +
                    "\tFROM\n" +
                    "\t\ttb_alleyway_records a\n" +
                    "\t\tJOIN tb_dev_accesscontroller b ON a.machineid = b.machineid\n" +
                    "\tWHERE\n" +
                    "\t\tDATE_FORMAT( a.recordtime, '%Y-%m-%d' ) = '"+date2+"'\n" +
                    //"\t\tAND a.recordtime <= '"+totdayTen+"'\n" +
                    "\tGROUP BY\n" +
                    "\t\tinfoid \n" +
                    "\t) AS s ON t.uid = s.infoid\n" +
                    "\tLEFT JOIN tb_face_infoface f ON t.uid = f.infoid\n" +
                    "\tLEFT JOIN tb_alleyway_records r ON s.infoid = r.infoid \n" +
                    "\tJOIN tb_dev_accesscontroller d ON r.machineid = d.machineid\n" +
                    "\tAND s.outtime = r.recordtime \n" +
                    "WHERE\n" +
                    "\t( direction = 2 OR direction IS NULL ) \n" +
                    "\tAND t.infotype = 2 and t.status = 1 and t.property = 3 and d.devname LIKE '"+sex+"%'";
            JSONArray array = dbService.QueryList(sql);
            for (int i = 0; i < array.size(); i++) {
                list.add(array.getJSONObject(i));
            }
        }else {
            String sql = "SELECT\n" +
                    "\tgetorgname ( t.orgcode ) AS class,\n" +
                    "\tt.NAME AS infoname,\n" +
                    "\tf.imgpath \n" +
                    "FROM\n" +
                    "\ttb_card_teachstudinfo t\n" +
                    "\tLEFT JOIN (\n" +
                    "\tSELECT\n" +
                    "\t\ta.infoid,\n" +
                    "\t\tmax( a.recordtime ) AS outtime \n" +
                    "\tFROM\n" +
                    "\t\ttb_alleyway_records a\n" +
                    "\t\tJOIN tb_dev_accesscontroller b ON a.machineid = b.machineid\n" +
                    "\tWHERE\n" +
                    "\t\tDATE_FORMAT( a.recordtime, '%Y-%m-%d' ) = '"+yester+"'\n" +
                    //"\t\tAND a.recordtime <= '"+yesterTen+"'\n" +
                    "\tGROUP BY\n" +
                    "\t\tinfoid \n" +
                    "\t) AS s ON t.uid = s.infoid\n" +
                    "\tLEFT JOIN tb_face_infoface f ON t.uid = f.infoid\n" +
                    "\tLEFT JOIN tb_alleyway_records r ON s.infoid = r.infoid \n" +
                    "\tJOIN tb_dev_accesscontroller d ON r.machineid = d.machineid\n" +
                    "\tAND s.outtime = r.recordtime \n" +
                    "WHERE\n" +
                    "\t( direction = 2 OR direction IS NULL ) \n" +
                    "\tAND t.infotype = 2 and t.status = 1 and t.property = 3 and d.devname LIKE '"+sex+"%'";
            JSONArray array = dbService.QueryList(sql);
            for (int i = 0; i < array.size(); i++) {
                list.add(array.getJSONObject(i));
            }
        }
        return list;
    }

    public List<JSONObject> getLateRecord(HttpServletRequest request, HttpServletResponse response,String id) {
        String sex = "";
        if ("1".equals(id)){
            sex ="男";
        }else {
            sex ="女";
        }
        List<JSONObject> list = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDate now = LocalDate.now();
        Date dateTime = new Date();
        String date = dateFormat.format(dateTime);
        String date2 = formatter.format(now);
        //当天的晚上 10 点
        String totdayTen = date2 + " 22:00:00";
        //当天的晚上 9点20分
        String totdayNine = date2 + " 21:20:00";
        //当天的晚上 9点整
        String totdayNinezheng = date2 + " 21:00:00";
        //当天的晚上 9点30分
        String totdayNineban = date2 + " 21:30:00";
        //当天的晚上 23点59分59秒
        String totdayFinally = date2 + " 23:59:59";
        LocalDate yesterday = now.minusDays(1);
        String yester = formatter.format(yesterday);
        //昨天的晚上 10 点
        String yesterTen = yester.split(" ")[0] + " 22:00:00";
        //昨天的晚上9点20分
        String yesterNine = yester.split(" ")[0] + " 21:20:00";
        Date nowDate =null;
        Date TenDate =null;
        Date NineDate =null;
        Date NinezhengDate =null;
        Date NinebanDate =null;
        try {
            //现在时间
            nowDate = dateFormat.parse(date);
            //当天10点
            TenDate = dateFormat.parse(totdayTen);
            NineDate = dateFormat.parse(totdayNine);
            NinezhengDate = dateFormat.parse(totdayNinezheng);
            NinebanDate = dateFormat.parse(totdayNineban);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (nowDate.after(NinezhengDate)){
            if(nowDate.before(NinebanDate)){
                return null;
            }else {
                String sql = "SELECT\n" +
                        "\tgetorgname ( t.orgcode ) AS class,\n" +
                        "\tt.NAME AS infoname,\n" +
                        "\tf.imgpath \n" +
                        "FROM\n" +
                        "\ttb_card_teachstudinfo t\n" +
                        "\tLEFT JOIN (\n" +
                        "\tSELECT\n" +
                        "\t\ta.infoid,\n" +
                        "\t\tmax( a.recordtime ) AS outtime \n" +
                        "\tFROM\n" +
                        "\t\ttb_alleyway_records a\n" +
   //                     "\t\tJOIN tb_dev_accesscontroller b ON a.machineid = b.machineid\n" +
                        "\tWHERE\n" +
                        "\t\tDATE_FORMAT( a.recordtime, '%Y-%m-%d' ) = '"+date2+"'\n" +
                        "\tGROUP BY\n" +
                        "\t\tinfoid \n" +
                        "\t) AS s ON t.uid = s.infoid\n" +
                        "\tLEFT JOIN (SELECT infoid,max(imgpath)imgpath FROM tb_face_infoface GROUP BY infoid) f ON t.uid = f.infoid\n" +
                        "\tLEFT JOIN tb_alleyway_records r ON s.infoid = r.infoid \n" +
                        "\tAND s.outtime = r.recordtime \n" +
                        "\tJOIN tb_dev_accesscontroller d ON r.machineid = d.machineid\n" +
                        "WHERE\n" +
                        "\tdirection = 2\n" +
                        "\tAND t.infotype = 2 and t.status = 1 and t.property = 3 and d.devname LIKE '"+sex+"%'" +
                        "\t\tAND r.recordtime <= '"+totdayFinally+"'";
                JSONArray array = dbService.QueryList(sql);
                for (int i = 0; i < array.size(); i++) {
                    list.add(array.getJSONObject(i));
                }
            }

        }else {
            String sql = "SELECT\n" +
                    "\tgetorgname ( t.orgcode ) AS class,\n" +
                    "\tt.NAME AS infoname,\n" +
                    "\tf.imgpath \n" +
                    "FROM\n" +
                    "\ttb_card_teachstudinfo t\n" +
                    "\tLEFT JOIN (\n" +
                    "\tSELECT\n" +
                    "\t\ta.infoid,\n" +
                    "\t\tmax( a.recordtime ) AS outtime \n" +
                    "\tFROM\n" +
                    "\t\ttb_alleyway_records a\n" +
//                    "\t\tJOIN tb_dev_accesscontroller b ON a.machineid = b.machineid\n" +
                    "\tWHERE\n" +
                    "\t\tDATE_FORMAT( a.recordtime, '%Y-%m-%d' ) = '"+yester+"'\n" +
                    "\tGROUP BY\n" +
                    "\t\tinfoid \n" +
                    "\t) AS s ON t.uid = s.infoid\n" +
                    "\tLEFT JOIN (SELECT infoid,max(imgpath)imgpath FROM tb_face_infoface GROUP BY infoid) f ON t.uid = f.infoid\n" +
                    "\tLEFT JOIN tb_alleyway_records r ON s.infoid = r.infoid \n" +
                    "\tAND s.outtime = r.recordtime \n" +
                    "\tJOIN tb_dev_accesscontroller d ON r.machineid = d.machineid\n" +
                    "WHERE\n" +
                    "\tdirection = 2\n" +
                    "\tAND t.infotype = 2 and t.status = 1 and t.property = 3 and d.devname LIKE '"+sex+"%'" +
                    "\t\tAND r.recordtime <= '"+yesterNine+"'";
            JSONArray array = dbService.QueryList(sql);
            for (int i = 0; i < array.size(); i++) {
                list.add(array.getJSONObject(i));
            }
        }
        return list;
    }

    public List<JSONObject> getNoHomeRecord(HttpServletRequest request, HttpServletResponse response,String id) {
        String sex = "";
        if ("1".equals(id)){
            sex ="男";
        }else {
            sex ="女";
        }
        List<JSONObject> list = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDate now = LocalDate.now();
        Date dateTime = new Date();
        String date = dateFormat.format(dateTime);
        String date2 = formatter.format(now);
        //当天的晚上 10 点
        String totdayTen = date2 + " 22:00:00";
        LocalDate yesterday = now.minusDays(1);
        String yester = formatter.format(yesterday);
        String yesterTen = yester.split(" ")[0] + " 22:00:00";
        Date nowDate =null;
        Date TenDate =null;
        try {
            //现在时间
            nowDate = dateFormat.parse(date);
            //当天10点
            TenDate = dateFormat.parse(totdayTen);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (nowDate.after(TenDate)){
            String sql = "SELECT\n" +
                    "\tgetorgname ( t.orgcode ) AS class,\n" +
                    "\tt.NAME AS infoname,\n" +
                    "\tf.imgpath \n" +
                    "FROM\n" +
                    "\ttb_card_teachstudinfo t\n" +
                    "\tLEFT JOIN (\n" +
                    "\tSELECT\n" +
                    "\t\ta.infoid,\n" +
                    "\t\tmax( a.recordtime ) AS outtime \n" +
                    "\tFROM\n" +
                    "\t\ttb_alleyway_records a\n" +
             //       "\t\tJOIN tb_dev_accesscontroller b ON a.machineid = b.machineid\n" +
                    "\tWHERE\n" +
                    "\t\tDATE_FORMAT( a.recordtime, '%Y-%m-%d' ) = '"+date2+"'\n" +
                    "\tGROUP BY\n" +
                    "\t\tinfoid \n" +
                    "\t) AS s ON t.uid = s.infoid\n" +
                    "\tLEFT JOIN (SELECT infoid,max(imgpath)imgpath FROM tb_face_infoface GROUP BY infoid) f ON t.uid = f.infoid\n" +
                    "\tLEFT JOIN tb_alleyway_records r ON s.infoid = r.infoid \n" +
                    "\tAND s.outtime = r.recordtime \n" +
                    "\tJOIN tb_dev_accesscontroller d ON r.machineid = d.machineid\n" +
                    "WHERE\n" +
                    "\t(direction = 2 or direction is null)\n" +
                    "\t and t.infotype = 2 and t.status = 1 and t.property = 3 and d.devname LIKE '"+sex+"%'";
                    //"\t\tAND r.recordtime > '"+totdayTen+"'";
            JSONArray array = dbService.QueryList(sql);
            for (int i = 0; i < array.size(); i++) {
                list.add(array.getJSONObject(i));
            }
        }else {
            String sql = "SELECT\n" +
                    "\tgetorgname ( t.orgcode ) AS class,\n" +
                    "\tt.NAME AS infoname,\n" +
                    "\tf.imgpath \n" +
                    "FROM\n" +
                    "\ttb_card_teachstudinfo t\n" +
                    "\tLEFT JOIN (\n" +
                    "\tSELECT\n" +
                    "\t\ta.infoid,\n" +
                    "\t\tmax( a.recordtime ) AS outtime \n" +
                    "\tFROM\n" +
                    "\t\ttb_alleyway_records a\n" +
             //       "\t\tJOIN tb_dev_accesscontroller b ON a.machineid = b.machineid\n" +
                    "\tWHERE\n" +
                    "\t\tDATE_FORMAT( a.recordtime, '%Y-%m-%d' ) = '"+yester+"'\n" +
                    "\tGROUP BY\n" +
                    "\t\tinfoid \n" +
                    "\t) AS s ON t.uid = s.infoid\n" +
                    "\tLEFT JOIN (SELECT infoid,max(imgpath)imgpath FROM tb_face_infoface GROUP BY infoid) f ON t.uid = f.infoid\n" +
                    "\tLEFT JOIN tb_alleyway_records r ON s.infoid = r.infoid \n" +
                    "\tAND s.outtime = r.recordtime \n" +
                    "\tJOIN tb_dev_accesscontroller d ON r.machineid = d.machineid\n" +
                    "WHERE\n" +
                    "\t(direction = 2 or direction is null)\n" +
                    "\t and t.infotype = 2 and t.status = 1 and t.property = 3 and d.devname LIKE '"+sex+"%'";
                    //"\t\tAND r.recordtime > '"+yesterTen+"'";
            JSONArray array = dbService.QueryList(sql);
            for (int i = 0; i < array.size(); i++) {
                list.add(array.getJSONObject(i));
            }
        }
        return list;
    }

    public List<JSONObject> getNoDormStu(HttpServletRequest request, HttpServletResponse response,String sex) {
        List<JSONObject> list = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDate now = LocalDate.now();
        Date dateTime = new Date();
        String date = dateFormat.format(dateTime);
        LocalDate threeDay = now.minusDays(3);
        String threeDate = (formatter.format(threeDay));
        threeDate +=" 00:00:00";

        String sql = "SELECT\n" +
                "\tgetorgname ( t.orgcode ) AS class,\n" +
                "\tt.NAME AS infoname,\n" +
                "\tr.recordimg,\n" +
                "\tr.recordtime,\n" +
                "\tCASE\n" +
                "\t\tr.direction \n" +
                "\t\tWHEN '1' THEN\n" +
                "\t\t'进' \n" +
                "\t\tWHEN '2' THEN\n" +
                "\t\t'出' ELSE '' \n" +
                "\tEND AS direction,\n" +
                "\tf.imgpath \n" +
                "FROM\n" +
                "\ttb_card_teachstudinfo t\n" +
                "\tLEFT JOIN (\n" +
                "\tSELECT\n" +
                "\t\ta.infoid,\n" +
                "\t\tmax( a.recordtime ) AS outtime \n" +
                "\tFROM\n" +
                "\t\ttb_alleyway_records a\n" +
                "\tWHERE\n" +
                "\t\ta.recordtime < '"+date+"' and a.recordtime > '"+threeDate+"'\n" +
                "\tGROUP BY\n" +
                "\t\tinfoid \n" +
                "\t) AS s ON t.uid = s.infoid\n" +
                "\tLEFT JOIN (SELECT infoid,max(imgpath)imgpath FROM tb_face_infoface GROUP BY infoid) f ON t.uid = f.infoid\n" +
                "\tLEFT JOIN tb_alleyway_records r ON s.infoid = r.infoid AND s.outtime = r.recordtime \n" +
                "WHERE\n" +
                "\t\t(r.direction = 2 or r.direction is NULL) AND ";
        if (StringUtils.isNotEmpty(sex)){
            sql+="\t t.infotype = 2 and t.status = 1 AND t.sex = '"+sex+"' ";
        }else {
            sql+="\t t.infotype = 2 and t.status = 1";
        }
        if ("1".equals(property)){
            sql+= "AND t.property = 3";
        }
        sql+=" ORDER BY r.recordtime DESC";

        JSONArray array = dbService.QueryList(sql);
        for (int i = 0; i < array.size(); i++) {
            if (StringUtils.isEmpty(array.getJSONObject(i).getString("recordimg"))){
                array.getJSONObject(i).put("recordimg",array.getJSONObject(i).getString("imgpath"));
            }
            if (StringUtils.isEmpty(array.getJSONObject(i).getString("recordtime"))){
                array.getJSONObject(i).put("des","三日内无进出数据");
            }else {
                array.getJSONObject(i).put("recordtime",array.getJSONObject(i).getString("recordtime").replace("T"," "));
            }
            list.add(array.getJSONObject(i));
        }
        return list;
    }


    public HashMap<String, List<BuildingVo>> getDormBuilding(HttpServletRequest request, HttpServletResponse response, String id){
        HashMap<String, List<BuildingVo>> map = new HashMap<>();
        if (StringUtils.isEmpty(id)){
            return map;
        }else {
            String sql="SELECT `level` FROM tb_dev_areaframework WHERE parentid = '"+id+"' GROUP BY `level`";
            JSONArray list = dbService.QueryList(sql);
            for (int i = 0; i < list.size(); i++) {
                String leave = list.getJSONObject(i).getString("level");
                List<BuildingVo> listVo = new ArrayList<>();
                String sql1= "SELECT `uid` as 'id',`name` as 'name' FROM tb_dev_areaframework WHERE parentid = '"+id+"' and `level`="+leave;
                JSONArray levelList = dbService.QueryList(sql1);
                for (int i1 = 0; i1 < levelList.size(); i1++) {
                    String houseid = String.valueOf(levelList.getJSONObject(i1).get("id"));
                    String name = String.valueOf(levelList.getJSONObject(i1).get("name"));
                    BuildingVo vo = new BuildingVo();
                    vo.setHouseid(houseid);
                    vo.setHouseno(name);
                    int molecule = dbService.getCount("tb_hotel_bed", "houseid = '" + houseid + "' AND infoid is not null AND infoid != ''");
                    int denominator = dbService.getCount("tb_hotel_bed", "houseid = '" + houseid + "'");
                    vo.setMolecule(molecule);
                    vo.setDenominator(denominator);
                    listVo.add(vo);
                }
                map.put(leave,listVo);
            }
        }

        return map;
    }

    public List<Map<String,String>> getPhoto(HttpServletRequest request, HttpServletResponse response, String id){
        List<Map<String,String>> list = new ArrayList<>();
        String sql = "SELECT infoid from tb_hotel_bed WHERE infoid is NOT NULL AND houseid = '"+id+"'";
        String sqlNoReturn1="SELECT direction,recordimg,recordtime FROM tb_alleyway_records WHERE infoid = '";
        String sqlNoReturn2="' ORDER BY recordtime DESC LIMIT 1";
        JSONArray peoples = dbService.QueryList(sql);
        for (int i = 0; i < peoples.size(); i++) {
            String infoid = String.valueOf(peoples.getJSONObject(i).get("infoid"));
            if (StringUtils.isEmpty(infoid)){
                continue;
            }
            HashMap<String, String> map = new HashMap<>();
            JSONObject jsonObject = dbService.QueryObject(sqlNoReturn1 + infoid + sqlNoReturn2, JSONObject.class);
            JSONObject object = dbService.QueryObject("SELECT `name` FROM tb_card_teachstudinfo WHERE uid='" + infoid + "'", JSONObject.class);
            String name = object.getString("name");
            map.put("name",name);
            if (jsonObject==null){
                map.put("direction","2");
                JSONObject face = dbService.QueryObject("SELECT imgpath FROM tb_face_infoface WHERE infoid = '" + infoid + "'", JSONObject.class);
                if (face!=null){
                    map.put("recordimg",face.getString("imgpath"));
                }else {
                    map.put("recordimg","");
                }

                map.put("recordtime","暂无数据");
                map.put("color","#ff0000");
            }else {
                String direction =jsonObject.getString("direction");
                String recordimg = jsonObject.getString("recordimg");
                String recordtime = jsonObject.getString("recordtime");
                String time = "";
                DateTimeFormatter matter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                if (recordtime.contains("-")){
                    LocalDateTime formatter = jsonObject.getObject("recordtime",LocalDateTime.class);
                    time = formatter.format(matter);
                }else {
                    Long milliseconds = Long.valueOf(recordtime);
                    // 使用 Instant 类将毫秒数转化为Instant对象
                    Instant instant = Instant.ofEpochMilli(milliseconds);
                    // 将 Instant 对象转化为 LocalDateTime 对象
                    LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, java.time.ZoneId.systemDefault());
                    // 使用 DateTimeFormatter 格式化输出，保留两位小数
                    time = localDateTime.format(matter);
                }
                map.put("direction",direction);
                map.put("recordimg",recordimg);
                if ("1".equals(direction)){
                    map.put("recordtime","已归寝 "+time);
                    map.put("color","#0BB20C");
                }else {
                    map.put("recordtime","已出寝 "+time);
                    map.put("color","#ff0000");
                }
            }
            list.add(map);
        }
        return  list;
    }
    public List<Map<String,String>> getdromlist(HttpServletRequest request, HttpServletResponse response){
        List<Map<String,String>> list = new ArrayList<>();
        String sql = "";
        sql = "SELECT name,uid FROM tb_dev_areaframework WHERE areatype='2'";
        JSONArray array = dbService.QueryList(sql);
        if (array.size()>0){
            for (int i = 0; i < array.size(); i++) {
                String dromid = array.getJSONObject(i).getString("uid");
                //String sqlhouse = "SELECT `name` FROM tb_dev_areaframework WHERE parentid = '"+dromid+"' AND areatype = 3";
                //if (dbService.QueryList(sqlhouse).size()==0) {
                //    continue;
                //}
                String name = array.getJSONObject(i).getString("name");
                Map<String, String> map = new HashMap<>();
                map.put("id",dromid);
                map.put("name",name);
                list.add(map);
            }
        }
        return  list;
    }

    public List<OrgframeworkDto> getClasslist(HttpServletRequest request, HttpServletResponse response){
        List<Map<String,String>> list = new ArrayList<>();
        String sql = "";
        sql = "SELECT code,name FROM tb_card_orgframework WHERE orgtype='4' ORDER BY CAST( code AS UNSIGNED)";
        List<OrgframeworkDto> dtos = dbService.queryList(sql, OrgframeworkDto.class);
        return  dtos;
    }


    public SignStudentDtos getClassStudentlist(HttpServletRequest request, HttpServletResponse respons, String orgcode){

        String sql = "";
        List<SignStudentDto> signStudentDtos = (List<SignStudentDto>) redisclient.getObject("studentlist:"+orgcode);
        if (signStudentDtos==null||signStudentDtos.size()==0){
            sql = "SELECT code,uid infoid,name from tb_card_teachstudinfo where infotype = 2 ";
            if (StringUtils.isNotEmpty(orgcode)){
                sql += " and orgcode = '"+orgcode+"' ";
            }
            sql+=" order by code ";

            signStudentDtos = dbService.queryList(sql, SignStudentDto.class);
            redisclient.setObject("studentlist:"+orgcode,signStudentDtos,1, TimeUnit.DAYS);
        }
        List<SignStudentDto> attended = new ArrayList<>();
        List<SignStudentDto> notAttended = new ArrayList<>();
        for (SignStudentDto signStudentDto : signStudentDtos) {
            Integer entity = redisclient.getEntity("accessstatus:" + signStudentDto.getInfoid(), Integer.class);
            Integer signsentity = redisclient.getEntity("signstatus:" + signStudentDto.getInfoid(), Integer.class);
            //在校 离校状态
            if (entity!=null){
                if (entity==0){
                    signStudentDto.setStatus("在校");
                }else if (entity==1){
                    signStudentDto.setStatus("已离校");
                }else if (entity==-1) {
                    signStudentDto.setStatus("在校");
                }
            }else {
                signStudentDto.setStatus("在校");
            }
            //签到 未签到状态
            if (signsentity!=null){
                if (signsentity==1){
                    signStudentDto.setSignstatus("已签到");
                    attended.add(signStudentDto);
                }else {
                    signStudentDto.setSignstatus("未签到");
                    notAttended.add(signStudentDto);
                }
            }else {
                signStudentDto.setSignstatus("未签到");
                notAttended.add(signStudentDto);
            }
        }
        SignStudentDtos studentDtos = new SignStudentDtos();
        studentDtos.setAttended(attended);
        studentDtos.setNotAttended(notAttended);

        return  studentDtos;
    }


    public SignStudentDtos getAllClassStudentlist(HttpServletRequest request, HttpServletResponse respons){
        List<OrgframeworkDto> classlist = getClasslist(request, respons);
        for (int i = 0; i < classlist.size(); i++) {
            if (StringUtils.isEmpty(redisclient.getString("classcode:"+i))){
                redisclient.setObject("classcode:"+i,classlist.get(i).getCode(),1,TimeUnit.DAYS);
                redisclient.setObject("classcodename:"+i,classlist.get(i).getName(),1,TimeUnit.DAYS);
            }
        }

        Integer nowclaascode = redisclient.getEntity("nowclaascode:nowclaascode", Integer.class);
        if (nowclaascode==null){
            redisclient.setObject("nowclaascode:nowclaascode",0,1,TimeUnit.DAYS);
            nowclaascode = 0;
        }else {
            if (nowclaascode>=classlist.size()){
                nowclaascode = 0;
            }
        }
        String orgcode = redisclient.getString("classcode:"+nowclaascode);
        orgcode=orgcode.replace("\"","");
        String name = redisclient.getString("classcodename:"+nowclaascode);
        redisclient.setObject("nowclaascode:nowclaascode",nowclaascode+1,1,TimeUnit.DAYS);
        String sql = "";
        List<SignStudentDto> signStudentDtos = (List<SignStudentDto>) redisclient.getObject("studentlist:"+orgcode);
        if (signStudentDtos==null||signStudentDtos.size()==0){
            sql = "SELECT code,uid infoid,name from tb_card_teachstudinfo where infotype = 2 ";
            if (StringUtils.isNotEmpty(orgcode)){
                sql += " and orgcode = '"+orgcode+"'";
            }
            sql+=" order by code ";

            signStudentDtos = dbService.queryList(sql, SignStudentDto.class);
            redisclient.setObject("studentlist:"+orgcode,signStudentDtos,1, TimeUnit.DAYS);
        }
        List<SignStudentDto> attended = new ArrayList<>();
        List<SignStudentDto> notAttended = new ArrayList<>();
        for (SignStudentDto signStudentDto : signStudentDtos) {

            Integer entity = redisclient.getEntity("accessstatus:" + signStudentDto.getInfoid(), Integer.class);
            Integer signsentity = redisclient.getEntity("signstatus:" + signStudentDto.getInfoid(), Integer.class);
            //在校 离校状态
            if (entity!=null){
                if (entity==0){
                    signStudentDto.setStatus("在校");
                }else if (entity==1){
                    signStudentDto.setStatus("离校");
                }else if (entity==-1) {
                    signStudentDto.setStatus("未知");
                }
            }else {
                signStudentDto.setStatus("未知");
            }
            //签到 未签到状态
            if (signsentity!=null){
                if (signsentity==1){
                    signStudentDto.setSignstatus("已签到");
                    attended.add(signStudentDto);
                }else {
                    signStudentDto.setSignstatus("未签到");
                    //notAttended.add(signStudentDto);
                }
            }else {
                signStudentDto.setSignstatus("未签到");
                //notAttended.add(signStudentDto);
            }
        }
        SignStudentDtos studentDtos = new SignStudentDtos();
        studentDtos.setAttended(attended);
        studentDtos.setNotAttended(notAttended);
        studentDtos.setName(name);

        return  studentDtos;
    }



    public List<Map<String,String>> getmachinelist(HttpServletRequest request, HttpServletResponse response,String areacode){
        List<Map<String,String>> list = new ArrayList<>();
        String sql = "";
        sql = "SELECT devname,machineid FROM tb_dev_accesscontroller WHERE  status = 1 AND devclass = 9 " ;
        if (StringUtils.isNotEmpty(areacode)){
            sql+=" and areacode = '"+areacode+"'";
        }
        sql+="ORDER BY devname";
        JSONArray array = dbService.QueryList(sql);
        if (array.size()>0){
            for (int i = 0; i < array.size(); i++) {
                String machineid = array.getJSONObject(i).getString("machineid");
                //String sqlhouse = "SELECT `name` FROM tb_dev_areaframework WHERE parentid = '"+dromid+"' AND areatype = 3";
                //if (dbService.QueryList(sqlhouse).size()==0) {
                //    continue;
                //}
                String devname = array.getJSONObject(i).getString("devname");
                Map<String, String> map = new HashMap<>();
                map.put("machineid",machineid);
                map.put("name",devname);
                list.add(map);
            }
        }
        return  list;
    }

    public List<Map<String,String>> getarealist(HttpServletRequest request, HttpServletResponse response){
        List<Map<String,String>> list = new ArrayList<>();
        String sql = "";
        sql = "SELECT a.code ,getareaname(a.code) as name FROM tb_dev_areaframework a JOIN tb_dev_accesscontroller b ON a.`code` = b.areacode WHERE b.devclass = 9 GROUP BY `code`";
        JSONArray array = dbService.QueryList(sql);
        if (array.size()>0){
            for (int i = 0; i < array.size(); i++) {
                String code = array.getJSONObject(i).getString("code");
                String name = array.getJSONObject(i).getString("name");
                Map<String, String> map = new HashMap<>();
                map.put("code",code);
                map.put("name",name);
                list.add(map);
            }
        }
        return  list;
    }

    public List<Map<String,String>> getsignarealist(HttpServletRequest request, HttpServletResponse response){
        List<Map<String,String>> list = new ArrayList<>();
        String sql = "";
        sql = "SELECT a.code ,getareaname(a.code) as name FROM tb_dev_areaframework a JOIN tb_dev_accesscontroller b ON a.`code` = b.areacode GROUP BY `code`";
        JSONArray array = dbService.QueryList(sql);
        if (array.size()>0){
            for (int i = 0; i < array.size(); i++) {
                String code = array.getJSONObject(i).getString("code");
                String name = array.getJSONObject(i).getString("name");
                Map<String, String> map = new HashMap<>();
                map.put("code",code);
                map.put("name",name);
                list.add(map);
            }
        }
        return  list;
    }

    public List<Map<String,String>> getareaweihanlist(HttpServletRequest request, HttpServletResponse response){
        List<Map<String,String>> list = new ArrayList<>();
        String sql = "";
        sql = "SELECT a.code ,getareaname(a.code) as name FROM tb_dev_areaframework a JOIN tb_dev_accesscontroller b ON a.`code` = b.areacode WHERE a.code like '001%' and b.devclass = 9 GROUP BY `code`";
        JSONArray array = dbService.QueryList(sql);
        if (array.size()>0){
            for (int i = 0; i < array.size(); i++) {
                String code = array.getJSONObject(i).getString("code");
                String name = array.getJSONObject(i).getString("name");
                Map<String, String> map = new HashMap<>();
                map.put("code",code);
                map.put("name",name);
                list.add(map);
            }
        }
        return  list;
    }


}
