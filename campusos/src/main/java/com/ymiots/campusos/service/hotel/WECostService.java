package com.ymiots.campusos.service.hotel;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.WECost;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.List;

@Repository
public class WECostService extends BaseService{

    @Autowired
    UserRedis userRedis;

    public JsonData getWECostList(HttpServletRequest request, HttpServletResponse response, String areaCode,boolean viewChild, String daily, int start, int limit) {
        String fields = "wec.id,wec.areaCode,wec.daily,water,electric,(water+electric) as totalmoney,wec.remark,da.name as housename,wec.creatorid,wec.createdate,wec.status," +
                "coldwaterprice,coldwaterlastmonth,coldwatermonth,coldwaterpracticalnum,coldwatermoney,hotwaterprice,hotwaterlastmonth," +
                "hotwatermonth,hotwaterpracticalnum,hotwatermoney,electricprice,electricwaterlastmonth,electricwatermonth,electricwaterpracticalnum";
        String table = "tb_hotel_water_electric_cost wec left join tb_dev_areaframework da on da.code = wec.areacode";
        StringBuilder where = new StringBuilder("1 = 1");
        if(StringUtils.isNotBlank(daily)){
            where.append(" and daily = '").append(daily).append("'");
        }
        if(StringUtils.isNotBlank(areaCode)){
            if(viewChild){
                where.append(" and areacode like '").append(areaCode).append("%'");
            }else{
                where.append(" and areacode = '").append(areaCode).append("'");
            }
        }
        return dbService.QueryJsonData(fields, table, where.toString(), "code asc", start, limit);
    }

    public JsonResult saveWECost(WECost weCost) {
        String sql = "";
        int r = 0;
        if(weCost.getId() == 0){
            if(StringUtils.isNotBlank(weCost.getRemark())){
                sql = "insert into tb_hotel_water_electric_cost (areacode,daily,water,electric,remark,creatorid,createdate,status) values (?,?,?,?,?,?,now(),1)";
                r = dbService.excuteSql(sql,weCost.getAreaCode(),weCost.getDaily(),weCost.getWater(),weCost.getElectric(),weCost.getRemark(),weCost.getCreatorId());
            }else{
                sql = "insert into tb_hotel_water_electric_cost (areacode,daily,water,electric,creatorid,createdate,status) values (?,?,?,?,?,now(),1)";
                r = dbService.excuteSql(sql,weCost.getAreaCode(),weCost.getDaily(),weCost.getWater(),weCost.getElectric(),weCost.getCreatorId());
            }
        }else{
            sql = "update tb_hotel_water_electric_cost set daily = ?,water = ? ,electric = ?,remark = ? where id = ?";
            r = dbService.excuteSql(sql,weCost.getDaily(),weCost.getWater(),weCost.getElectric(),weCost.getRemark(),weCost.getId());
        }
        if(r > 0){
            return Json.getJsonResult(true,"操作成功");
        }else {
            return Json.getJsonResult(false,"操作失败");
        }
    }

    public JsonResult delWECost(Integer id) {
        String sql = "delete from tb_hotel_water_electric_cost where id = ?";
        int r = dbService.excuteSql(sql,id);
        if(r > 0){
            return Json.getJsonResult(true,"操作成功");
        }else {
            return Json.getJsonResult(false,"操作失败");
        }
    }

    public String export(HttpServletRequest request, HttpServletResponse response,String areaCode, boolean viewChild, String daily) throws Exception{
        JsonData result = this.getWECostList(request,response,areaCode,viewChild,daily,0,0);
        if(!result.isSuccess()){
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "日期");
        cell.put("datakey", "daily");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "房间");
        cell.put("datakey", "housename");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "水费");
        cell.put("datakey", "water");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "电费");
        cell.put("datakey", "electric");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "合计");
        cell.put("datakey", "totalmoney");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "备注");
        cell.put("datakey", "remark");
        cell.put("size", 50);
        cells.add(cell);

        String title= "水电消费信息";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        return ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
    }

    public JsonResult ImportExcel(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws Exception {
        MultipartFile[] files = new MultipartFile[] { file };
        List<String> filelist = FileHelper.SaveMultipartFile(files, WebConfig.getUploaddir(),"excel");
        if (filelist.size() == 0) {
            return Json.getJsonResult(false, "文件保存失败");
        }
        String filepath = filelist.get(0);
        InputStream stream = new FileInputStream(WebConfig.getUploaddir()+filepath);
        JSONArray exceltablerows = ExcelUtil.getBankListByExcel(stream, filepath);
        stream.close();
        if (exceltablerows.size() == 0) {
            return Json.getJsonResult("文档为空文件");
        }
        for (int t = 0; t < exceltablerows.size(); t++) {
            JSONObject row = exceltablerows.getJSONObject(t);
            String month = row.getString("0");
            String house = row.getString("1");
            String coldwaterprice = row.getBigDecimal("2").toString();
            String coldwaterlastmonth = row.getBigDecimal("3").toString();
            String coldwatermonth = row.getBigDecimal("4").toString();
            String coldwaterpracticalnum = row.getBigDecimal("5").toString();
            String coldwatermoney = row.getBigDecimal("6").toString();
            String hotwaterprice = row.getBigDecimal("7").toString();
            String hotwaterlastmonth = row.getBigDecimal("8").toString();
            String hotwatermonth = row.getBigDecimal("9").toString();
            String hotwaterpracticalnum = row.getBigDecimal("10").toString();
            String hotwatermoney = row.getBigDecimal("11").toString();

            String water = row.getBigDecimal("12").toString();
            String electricprice = row.getBigDecimal("13").toString();
            String electricwaterlastmonth = row.getBigDecimal("14").toString();
            String electricwatermonth =row.getBigDecimal("15").toString();
            String electricwaterpracticalnum = row.getBigDecimal("16").toString();
            String electric = row.getBigDecimal("17").toString();
            String remark = row.getString("18");

            String insertSQL = "INSERT INTO tb_hotel_water_electric_cost (areacode,daily,water,electric,remark,creatorid,createdate,status," +
                    "coldwaterprice,coldwaterlastmonth,coldwatermonth,coldwaterpracticalnum,coldwatermoney,hotwaterprice,hotwaterlastmonth," +
                    "hotwatermonth,hotwaterpracticalnum,hotwatermoney,electricprice,electricwaterlastmonth,electricwatermonth,electricwaterpracticalnum) " +
                    " SELECT code,'"+month+"','"+water+"','"+electric+"','"+remark+"','"+userRedis.getUserId(request)+"',now(),1,'"+coldwaterprice+"','"+coldwaterlastmonth+"','"+coldwatermonth+"','" +
                    ""+coldwaterpracticalnum+"','"+coldwatermoney+"','"+hotwaterprice+"','"+hotwaterlastmonth+"','"+hotwatermonth+"','"+hotwaterpracticalnum+"','"+hotwatermoney+"','"+electricprice+"','"+electricwaterlastmonth+"','"+electricwatermonth+"'," +
                    "'"+electricwaterpracticalnum+"' FROM tb_dev_areaframework WHERE getareaname(code) = '"+house+"'";
            dbService.excuteSql(insertSQL);
        }

        return Json.getJsonResult(true);
    }
}
