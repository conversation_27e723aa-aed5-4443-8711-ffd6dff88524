package com.ymiots.campusos.service.consume;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.entity.consume.ConsumeAnalysisPersonRate;
import com.ymiots.campusos.entity.face.FaceRecordRow;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.hotel.HotelDashboardHeaderService;
import com.ymiots.campusos.task.AllewayLeaveSchoolAnalysisTask;
import com.ymiots.framework.common.*;
import com.ymiots.framework.websocket.WebSocketSets;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class AnalysisPersonRateService extends BaseService {

    @Autowired
    private AnalysisEverydayService everydayService;

    @Autowired
    private UserRedis userRedis;

    public JsonData getPersonRate(HttpServletRequest request, HttpServletResponse response, String orgcode, String key, String starttime, String endtime, String status, boolean viewchild, int start, int limit) {
        String fields = "capr.uid,date_format(capr.daily, '%Y-%m-%d') daily,capr.infoid,capr.code,capr.name,getminiorgname(capr.orgcode) as deptname,capr.scheme1,capr.schemetime1," +
                "capr.scheme2,capr.schemetime2,capr.scheme3,capr.schemetime3,capr.scheme4,capr.schemetime4,capr.scheme5,capr.schemetime5,capr.scheme6,capr.schemetime6,capr.scheme7,capr.schemetime7," +
                "capr.scheme8,capr.schemetime8,capr.scheme9,capr.schemetime9,capr.scheme10,capr.schemetime10,capr.schememoney,capr.schemetimes,capr.createdate";
        String table = "tb_consume_analysis_person_rate capr " +
                "LEFT JOIN  tb_card_teachstudinfo ct on ct.uid=capr.infoid ";
        SysUser sysUser = userRedis.get(request);
        if (sysUser.getUsertype() == 1) {
            String userid = sysUser.getUid();
            table += " inner join tb_card_orgframework co on co.code=ct.orgcode " +
                    "inner join tb_card_orgframework_user cou on cou.orgcode=co.code " +
                    "and cou.userid = '" + userid + "'";
        }

        String where = "1=1 ";
        if (StringUtils.isNotBlank(status) && !"-1".equals(status)) {
            where += " AND ct.status=" + status;
        }
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and capr.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and capr.orgcode='" + orgcode + "' ";
            }
        }
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where += " AND capr.daily BETWEEN '" + starttime + "' AND '" + endtime + "'";
        }
        if (StringUtils.isNotBlank(key)) {
            where += " and (capr.code='" + key + "' or capr.name like '%" + key + "%')";
        }
        JsonData result = dbService.QueryJsonData(fields, table, where, "capr.daily DESC", start, limit);

        JSONArray list = everydayService.getSumConsume("daily", table, "WHERE " + where);
        result.getData().addAll(list);
        return result;
    }


    public String ExportPersonRate(HttpServletRequest request, HttpServletResponse response, String orgcode, String key, String starttime, String endtime, String status, boolean viewchild, String currentPage, String pageSize, String size) throws IOException {
        String schemeSQL = "SELECT code,name FROM tb_consume_scheme ORDER BY code ASC";
        JSONArray schemeja = dbService.QueryList(schemeSQL);
        List<String> schemeCode = new ArrayList<String>();
        List<String> schemeName = new ArrayList<String>();
        List<String> schemetimeCode = new ArrayList<String>();
        if (!schemeja.isEmpty()) {
            for (int i = 0; i < schemeja.size(); i++) {
                String code = schemeja.getJSONObject(i).getString("code");
                schemeCode.add("scheme" + code);
                schemetimeCode.add("schemetime" + code);
                schemeName.add(schemeja.getJSONObject(i).getString("name"));
            }
        }
        int star = 1;
        JsonData result = new JsonData();
        if (currentPage.equals("1")) {
            result = getPersonRate(request, response, orgcode, key, starttime, endtime, status, viewchild, 0, Integer.valueOf(size));
        } else if (Integer.valueOf(currentPage) > star) {
            result = getPersonRate(request, response, orgcode, key, starttime, endtime, status, viewchild, (Integer.valueOf(currentPage) - 1) * Integer.valueOf(size), Integer.valueOf(size));
        } else {
            result = getPersonRate(request, response, orgcode, key, starttime, endtime, status, viewchild, 0, 0);
        }

        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "日期");
        cell.put("datakey", "daily");
        cell.put("size", 10);
        cells.add(cell);

        if ("1".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        } else if ("2".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "部门");
        cell.put("datakey", "deptname");
        cell.put("size", 10);
        cells.add(cell);

        for (int i = 0; i < schemeCode.size(); i++) {
            cell = new JSONObject();
            cell.put("name", schemeName.get(i));
            cell.put("datakey", schemeCode.get(i));
            cell.put("size", 10);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", schemeName.get(i) + "次数");
            cell.put("datakey", schemetimeCode.get(i));
            cell.put("size", 10);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "合计金额");
        cell.put("datakey", "schememoney");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "合计次数");
        cell.put("datakey", "schemetimes");
        cell.put("size", 15);
        cells.add(cell);

        List<CellRangeAddress> slist = new ArrayList<CellRangeAddress>();
        List<JSONArray> heads = new ArrayList<JSONArray>();
        String title = "个人每日消费";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename = title + format + ".xlsx";
        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp", filename, title, slist, heads, starttime, endtime, "1", "1");
        return filepath;
    }

    public JsonData getAnalysisDailyLits(HttpServletRequest request, HttpServletResponse response, String start, String end) {
        String fields = "distinct daily as dayname";
        String table = "tb_consume_analysis_person_rate";
        String where = "daily between '" + start + "' and '" + end + "' ";
        String sort = ExtSort.Orderby(request, "daily asc");
        JsonData result = dbService.QueryJsonData(fields, table, where, sort, 0, 0);
        return result;
    }


    public JsonResult AnalysisDaily(HttpServletRequest request, HttpServletResponse response, String daily) throws Exception {
        Date day = DateHelper.parse(daily + " 00:00:00");
        if (day.after(DateHelper.addDays(new Date(), 0))) {
            String lastday = DateHelper.format(DateHelper.addDays(new Date(), 0), "yyyy-MM-dd");
            return Json.getJsonResult("无法分析" + lastday + "以后的数据");
        }
        dbService.excuteSql("call sp_consume_person_analysis(?)", daily);
        return Json.getJsonResult(true);
    }

    public JsonResult analysisHandle(HttpServletRequest request, HttpServletResponse response, final String startdate, final String enddate) {
        try {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    long startTimeMillis = System.currentTimeMillis();
                    try {
                        dbService.excuteSql("call sp_consume_person_analysis_handle(?,?)", startdate, enddate);
                        JSONObject msg = new JSONObject();
                        msg.put("msgtype", 1000);
                        msg.put("msg", "分析成功");
                        WebSocketSets.getInstance().send("sysmsg", msg.toJSONString());
                        long endTimeMillis = System.currentTimeMillis();
                        Log.info(AnalysisPersonRateService.class, "个人每日消费统计一键分析执行花费时间:" + (endTimeMillis - startTimeMillis));
                    } catch (Exception e) {
                        Log.error(AnalysisPersonRateService.class, "个人每日消费统计一键分析：" + e.getMessage());
                    }
                }
            }).start();
            return Json.getJsonResult(true);
        } catch (Exception e) {
            Log.error(AnalysisPersonRateService.class, "个人每日消费统计一键分析：" + e.getMessage());
            return Json.getJsonResult(false, e.getMessage());
        }
    }


    public JsonResult analysisDailyForFaceHandle(HttpServletRequest request, HttpServletResponse response, final String startdate, final String enddate) {
        try {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    long startTimeMillis = System.currentTimeMillis();
                    try {

                        processAnalysisForDateRange(request, response, startdate, enddate);
                        JSONObject msg = new JSONObject();
                        msg.put("msgtype", 1000);
                        msg.put("msg", "分析成功");
                        WebSocketSets.getInstance().send("sysmsg", msg.toJSONString());
                        long endTimeMillis = System.currentTimeMillis();
                        Log.info(AnalysisPersonRateService.class, "个人每日消费统计一键分析执行花费时间:" + (endTimeMillis - startTimeMillis));
                    } catch (Exception e) {
                        Log.error(AnalysisPersonRateService.class, "个人每日消费统计一键分析：" + e.getMessage());
                    }
                }
            }).start();
            return Json.getJsonResult(true);
        } catch (Exception e) {
            Log.error(AnalysisPersonRateService.class, "个人每日消费统计一键分析：" + e.getMessage());
            return Json.getJsonResult(false, e.getMessage());
        }
    }

    private final Logger logger = LoggerFactory.getLogger(AnalysisPersonRateService.class);

    private void processAnalysisForDateRange(HttpServletRequest request, HttpServletResponse response, String startdate, String enddate) throws Exception {
        Date startDate = DateHelper.parse(startdate + " 00:00:00");
        Date endDate = DateHelper.parse(enddate + " 23:59:59");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        while (!calendar.getTime().after(endDate)) {
            String currentDate = DateHelper.format(calendar.getTime(), "yyyy-MM-dd");
            AnalysisDailyForFaceRecord(request, response, currentDate);
            calendar.add(Calendar.DATE, 1);
        }
    }


    /**
     * 从人脸识别记录获取记录
     */
    public JsonResult AnalysisDailyForFaceRecord(HttpServletRequest request, HttpServletResponse response, String daily) {
        Date day = DateHelper.parse(daily + " 00:00:00");
        if (day.after(DateHelper.addDays(new Date(), 0))) {
            String lastday = DateHelper.format(DateHelper.addDays(new Date(), -1), "yyyy-MM-dd");
            return Json.getJsonResult("无法分析" + lastday + "以后的数据");
        }

        //删除完成当天的数据
        String delSQL = "delete from tb_consume_analysis_person_rate where daily = '" + daily + "'";
        dbService.excuteSql(delSQL);

        // 获取当天的起始时间和结束时间
        String startTime = daily + " 00:00:00";
        String endTime = daily + " 23:59:59";

        // 获取当天的人脸识别记录
        String faceSQL = "select devid devId, machineid machineId, infoid infoId, date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordTime, recordtype recordType, ct.code, ct.name, ct.orgcode org " +
                "from tb_face_record fr " +
                "join tb_card_teachstudinfo ct on fr.infoid = ct.uid " +
                "where recordtime between '" + startTime + "' and '" + endTime + "'";
        List<FaceRecordRow> recordRows = dbService.queryList(faceSQL, FaceRecordRow.class);

        Map<String, List<FaceRecordRow>> infoMap = recordRows.stream()
                .collect(Collectors.groupingBy(FaceRecordRow::getInfoId));

        //获取到消费时段
        String sql = "select code,starttime startTime,endtime endTime,everymoney everyMoney from tb_consume_scheme where consumetype = 2 ";
        List<ConsumeScheme> consumeSchemes = dbService.queryList(sql, ConsumeScheme.class);

        //根据消费餐别进行分组
        Map<Integer, List<ConsumeScheme>> codeMap = consumeSchemes.stream()
                .sorted(Comparator.comparing(ConsumeScheme::getCode))
                .map(i -> {
                    ConsumeScheme consumeScheme = new ConsumeScheme();
                    consumeScheme.setCode(i.getCode());
                    consumeScheme.setEveryMoney(i.getEveryMoney());
                    consumeScheme.setStartTime(daily + " " + i.getStartTime());
                    consumeScheme.setEndTime(daily + " " + i.getEndTime());
                    return consumeScheme;
                })
                .collect(Collectors.groupingBy(ConsumeScheme::getCode));
        infoMap.forEach((k1, v1) -> {
            FaceRecordRow recordRow = v1.get(0);
            ConsumeAnalysisPersonRate consumeAnalysisPersonRate = new ConsumeAnalysisPersonRate();
            consumeAnalysisPersonRate.setDaily(day);
            consumeAnalysisPersonRate.setInfoId(k1);
            consumeAnalysisPersonRate.setCode(recordRow.getCode());
            consumeAnalysisPersonRate.setName(recordRow.getName());
            consumeAnalysisPersonRate.setOrgCode(recordRow.getOrg());
            consumeAnalysisPersonRate.setCreateDate(new Date());
            consumeAnalysisPersonRate.setScheme1(BigDecimal.ZERO);
            consumeAnalysisPersonRate.setSchemeTime1(0);
            consumeAnalysisPersonRate.setScheme2(BigDecimal.ZERO);
            consumeAnalysisPersonRate.setSchemeTime2(0);
            consumeAnalysisPersonRate.setScheme3(BigDecimal.ZERO);
            consumeAnalysisPersonRate.setSchemeTime3(0);

            codeMap.forEach((k2, v2) -> {
//                logger.info("分析{}餐别{}", daily, k2);
//                String scheme = "scheme" + k2;
//                String schemeTime = "schemeTime" + k2;
                ConsumeScheme consumeScheme = v2.get(0);
                FaceRecordRow faceRecordRow = v1.stream().filter(i -> i.getRecordTime().compareTo(consumeScheme.getStartTime()) > 0
                                && i.getRecordTime().compareTo(consumeScheme.getEndTime()) < 0)
                        .min(Comparator.comparing(FaceRecordRow::getRecordTime))
                        .orElse(null);
                BigDecimal money = BigDecimal.ZERO;
                int time = 0;
                //插入到每日分析表中
                if (faceRecordRow != null) {
                    money = consumeScheme.getEveryMoney();
                    time = 1;
                }
                if (k2 == 1) {
                    String selSql = "select name orgCode,attr1 everyMoney from tb_sys_dictionary where groupcode = 'SYS0000070' ";
                    List<ConsumeSchemeForOrgCode> schemeForOrgCodes = dbService.queryList(selSql, ConsumeSchemeForOrgCode.class);
                    if (!CollectionUtils.isEmpty(schemeForOrgCodes)) {
                        for (ConsumeSchemeForOrgCode schemeForOrgCode : schemeForOrgCodes) {
                            if (faceRecordRow != null) {
                                if (faceRecordRow.getOrg().equals(schemeForOrgCode.getOrgCode())) {
                                    money = schemeForOrgCode.getEveryMoney();
                                }
                            }
                        }
                    }
                    consumeAnalysisPersonRate.setScheme1(money);
                    consumeAnalysisPersonRate.setSchemeTime1(time);
                } else if (k2 == 2) {
                    consumeAnalysisPersonRate.setScheme2(money);
                    consumeAnalysisPersonRate.setSchemeTime2(time);
                } else if (k2 == 3) {
                    consumeAnalysisPersonRate.setScheme3(money);
                    consumeAnalysisPersonRate.setSchemeTime3(time);
                }

            });
            int sumSchemeTime = consumeAnalysisPersonRate.getSchemeTime1() + consumeAnalysisPersonRate.getSchemeTime2() + consumeAnalysisPersonRate.getSchemeTime3();
            BigDecimal bigDecimal = consumeAnalysisPersonRate.getScheme1().add(consumeAnalysisPersonRate.getScheme2()).add(consumeAnalysisPersonRate.getScheme3());
            consumeAnalysisPersonRate.setSchemeTimes(sumSchemeTime);
            consumeAnalysisPersonRate.setSchemeMoney(bigDecimal);
            String insertSQL = "insert into tb_consume_analysis_person_rate (uid,daily,infoid,code,name,orgcode,scheme1,schemetime1,scheme2,schemetime2,scheme3,schemetime3,schememoney,schemetimes,createdate) " +
                    "values (uuid(),?,?,?,?,?,?,?,?,?,?,?,?,?,now())";
            dbService.excuteSql(insertSQL, daily, k1, consumeAnalysisPersonRate.getCode(), consumeAnalysisPersonRate.getName(), consumeAnalysisPersonRate.getOrgCode(),
                    consumeAnalysisPersonRate.getScheme1(), consumeAnalysisPersonRate.getSchemeTime1(), consumeAnalysisPersonRate.getScheme2(), consumeAnalysisPersonRate.getSchemeTime2(),
                    consumeAnalysisPersonRate.getScheme3(), consumeAnalysisPersonRate.getSchemeTime3(), consumeAnalysisPersonRate.getSchemeMoney(), consumeAnalysisPersonRate.getSchemeTimes());
        });
        return Json.getJsonResult(true);
    }


    @Data
    public static class ConsumeSchemeForOrgCode {
        private String orgCode;
        private BigDecimal everyMoney;
    }

    @Data
    public static class ConsumeScheme {
        private int code;
        private String startTime;
        private String endTime;
        private BigDecimal everyMoney;
    }
}
