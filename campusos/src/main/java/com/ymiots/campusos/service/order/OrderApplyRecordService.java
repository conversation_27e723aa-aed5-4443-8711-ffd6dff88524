package com.ymiots.campusos.service.order;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dto.WorkFlowNodeDto;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.consume.AnalysisPersonRateService;
import com.ymiots.campusos.service.workflow.ApprovalFormService;
import com.ymiots.campusos.util.workflow.OrderingHandler;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.utils.excel.Row;
import com.ymiots.framework.utils.excel.Sheet;
import lombok.Data;
import lombok.EqualsAndHashCode;
import okhttp3.MediaType;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Null;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-20 16:34
 * @since 2022-12-20 16:34
 */
@Repository
public class OrderApplyRecordService extends BaseService {

    public static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    @Autowired
    UserRedis userRedis;

    @Autowired
    OrderingHandler handler;

    @Autowired
    ApprovalFormService approvalFormService;

    public JsonData OrderAuditRecord(HttpServletRequest request, HttpServletResponse response,String uid){
        String[] split = uid.split(",");
        for (int i = 0; i < split.length; i++) {
            JSONObject object = dbService.QueryJSONObject("select uid,mouldid,node_level from tb_workflow_approval_record where formid = ? and auditcondition = 0 and auditid IS null ", split[i]);
            if (object!=null){
                JSONObject jsonObject = dbService.QueryJSONObject("SELECT c.nodelevel from tb_workflow_mould b INNER JOIN tb_workflow_node c on c.mouldid = b.uid INNER JOIN tb_workflow_approvar d on d.nodeid = c.uid WHERE b.uid = ? and d.infoid= ?", object.getString("mouldid"), userRedis.get(request).getEmpid());
                if (jsonObject!=null){
                    if (object.getString("node_level").equals(String.valueOf(jsonObject.getInteger("nodelevel")))){



                        try {
                            // 创建URL对象
                            URL url = new URL("http://localhost:2022/lab/auditOrderApplyOS");

                            // 打开连接
                            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

                            // 设置请求方法为POST
                            connection.setRequestMethod("POST");

                            // 设置请求头
                            connection.setRequestProperty("Content-Type", "application/json");
                            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

                            // 发送POST请求必须设置
                            connection.setDoOutput(true);

                            JSONObject json = new JSONObject();
                            json.put("infoid",userRedis.get(request).getEmpid());
                            json.put("name",userRedis.get(request).getName());
                            json.put("uid",split[i]);
                            json.put("status",1);
                            json.put("auditremark","");

                            // 发送请求体
                            try(OutputStream os = connection.getOutputStream()) {
                                byte[] input = json.toJSONString().getBytes("utf-8");
                                os.write(input, 0, input.length);
                            }

                            // 获取响应码
                            int responseCode = connection.getResponseCode();
                            System.out.println("Response Code: " + responseCode);

                            // 读取响应内容
                            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));
                            String inputLine;
                            StringBuilder newresponse = new StringBuilder();

                            while ((inputLine = in.readLine()) != null) {
                                newresponse.append(inputLine.trim());
                            }

                            // 关闭输入流
                            in.close();

                            // 打印响应内容
                            System.out.println("Response: " + response.toString());

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }

        return Json.getJsonData(true);
    }

    public JsonData OrderAuditJCanRecord(HttpServletRequest request, HttpServletResponse response,String uid,Integer type,String reason){
        String[] split = uid.split(",");
        for (int i = 0; i < split.length; i++) {
            JSONObject object = dbService.QueryJSONObject("select uid,mouldid,node_level from tb_workflow_approval_record where formid = ? and auditcondition = 0 and auditid IS null ", split[i]);
            if (object!=null){
                JSONObject jsonObject = dbService.QueryJSONObject("select ct.name from tb_consume_apply_record car left join tb_card_teachstudinfo ct on car.info_id = ct.uid where car.status = 0 and car.uid = ?",split[i]);
                if (type==1){
                    reason="管理员审核通过 "+reason;
                    dbService.excuteSql("update tb_consume_apply_record set status = 1 where uid = ?",split[i]);
                    handler.pushAskResultApply(split[i],jsonObject.getString("name"),"已通过");
                }else {
                    reason="管理员审核不通过 "+reason;
                    dbService.excuteSql("update tb_consume_apply_record set status = -1 where uid = ?",split[i]);
                    handler.pushAskResultApply(split[i],jsonObject.getString("name"),"不通过 "+reason);
                }
                dbService.excuteSql("update tb_workflow_approval_record set auditcondition = 1 , auditdate = now() , auditid = '' , remark = '"+reason+"' where formid = ?",split[i]);
            }
        }
        return Json.getJsonData(true);
    }

    public JsonData OrderTurnDownRecord(HttpServletRequest request, HttpServletResponse response,String uid){
        String[] split = uid.split(",");
        for (int i = 0; i < split.length; i++) {
            JSONObject object1 = dbService.QueryJSONObject("select uid,node_level from tb_workflow_approval_record where formid = ? and auditid=?", split[i], userRedis.get(request).getEmpid());
            if (object1!=null){
                JSONObject jsonObject2 = dbService.QueryJSONObject("select uid,auditid from tb_workflow_approval_record where formid = ? and node_level = ?", split[i],object1.getInteger("node_level")+1);
                if (jsonObject2!=null){
                    if (StringUtils.isEmpty(jsonObject2.getString("auditid"))){
                        dbService.excuteSql("update tb_workflow_approval_record set auditcondition = 0 , auditdate = null , auditid = null , remark = null where uid = ?",object1.getString("uid"));
                        dbService.excuteSql("delete from tb_workflow_approval_record where uid = ? ",jsonObject2.getString("uid"));
                    }else {
                        return Json.getJsonData(false,"下级已完成审核无法驳回");
                    }
                }else {
                    return Json.getJsonData(false,"您是最后一级审核无法进行驳回");
                }
            }else {
                return Json.getJsonData(false,"您还未审核无法进行驳回");
            }
        }

        return Json.getJsonData(true);
    }
    public JsonData getOrderApplyRecordList(HttpServletRequest request, HttpServletResponse response,String starttime,String endtime, String status, String key, int start,int limit){
        String sql = generateSQL(starttime, endtime, status, key);
        String page = " limit " + start + "," + limit + " ";
        JSONArray jsonArray = dbService.QueryList(sql);
        JsonData result = dbService.QueryJsonData(sql + page);
        result.setTotal(jsonArray.size());
        return result;
    }

    @NotNull
    private String generateSQL(String starttime, String endtime, String status, String key) {
        String sql = "select lar.uid,ct.code as infocode ,getminiorgname(lar.dining_dep) orgname,ct.name as infoname,date_format(lar.breakfast_start_time,'%Y-%m-%d %H:%i:%s') bstarttime,date_format(lar.breakfast_end_time,'%Y-%m-%d %H:%i:%s') bendtime,lar.reason, lar.status," +
                " date_format(lar.lunch_start_time,'%Y-%m-%d %H:%i:%s') as lstarttime ,date_format(lar.lunch_end_time,'%Y-%m-%d %H:%i:%s') lendtime,date_format(lar.create_dt,'%Y-%m-%d %H:%i:%s') createdate from tb_consume_apply_record lar " +
                " INNER JOIN tb_card_teachstudinfo ct ON ct.uid = lar.info_id ";
        sql += " where 1=1";
        if (StringUtils.isNotBlank(status) && !("-2").equals(status)) {
            sql += " AND lar.status=" + status;
        }
        if (StringUtils.isNotBlank(starttime)&& StringUtils.isNotBlank(endtime)) {
            sql+=" AND (lar.breakfast_start_time BETWEEN '"+ starttime +" 00:00:00' AND '"+ endtime +" 23:59:59' OR lar.lunch_start_time BETWEEN '"+ starttime +" 00:00:00'  AND '"+ endtime +" 23:59:59' ) ";
        }
        if (StringUtils.isNotBlank(key)) {
            sql += " AND (ct.name like '%" + key + "%' or ct.code='" + key + "')";
        }
        sql += "order by lar.create_dt DESC ";
        return sql;
    }

    public JsonData getOrderApplyRecord(HttpServletRequest request, HttpServletResponse response, String key, String status, String starttime, String endtime, Integer mouldType,String type, int start, int limit) {
        String docId = userRedis.get(request).getEmpid();
        List<WorkFlowNodeDto> nodeDtoList = getNodeLevel(request, docId, mouldType);
        if (CollectionUtil.isEmpty(nodeDtoList)) {
            return Json.getJsonData(false, "暂无审核权限，请联系管理员！");
        }
        StringJoiner nodeLevelList = new StringJoiner("','", "('", "')");
        String mouldId = nodeDtoList.get(0).getMouldId();
        Integer nodeLevelCheck = nodeDtoList.get(0).getNodeLevel();
        String fields = "lar.uid,ct.code as infocode ,lar.room_name as roomname,lar.infoid,date_format(lar.starttime, '%Y-%m-%d %H:%i:%s') starttime,date_format(lar.endtime, '%Y-%m-%d %H:%i:%s') endtime,ct.name as infoname,lar.remark,date_format(lar.createdate,'%Y-%m-%d %H:%i')as createdate, lar.status,war.auditcondition as auditStatus";
        String table = "tb_laboratory_apply_record lar " +
                "inner join tb_workflow_approval_record war on lar.uid = war.formid " +
                "INNER JOIN tb_card_teachstudinfo ct ON ct.uid = lar.infoid ";
        StringBuilder where = new StringBuilder("(");
        for (WorkFlowNodeDto nodeDto : nodeDtoList) {
            int level = nodeDto.getNodeLevel();
            if (nodeDto.getBehaviorStatus() == 0){
                where.append(" 1=1 ");
            }else if (nodeDto.getBehaviorStatus() == 2) {
                String sql = "select area_id from tb_workflow_manager where info_id = '" + docId + "' ";
                where.append(" lar.laboratoryid in (").append(sql).append(")");
            } else if (nodeDto.getBehaviorStatus() == 1){
                String infoListSql = "select infoid from tb_card_teachstudinfo_child where parentinfoid = '" + docId + "' and mouldid = '" + nodeDto.getMouldId() + "'";
                //如果是二级或以上审核人，需要根据表单的审核人找到对应的审核人
                if (nodeDto.getNodeLevel() > 1) {
                    level = nodeDto.getNodeLevel() - 1;
                    where.append(" war.auditid in (").append(infoListSql).append(")");
                } else {
                    where.append(" lar.infoid in (").append(infoListSql).append(")");
                }
            }else if (nodeDto.getBehaviorStatus() == 3) {
                String behaviorSql = "select group_id from tb_workflow_group_approvar where info_id = '" + docId + "'";
                where.append(" lar.group_id in (").append(behaviorSql).append(")");
            }
            where.append(" or ");
            nodeLevelList.add(String.valueOf(level));
        }
        // 删除最后一个 "or"
        where.setLength(where.length() - 4);
        where.append(")");
        if (StringUtils.isNotBlank(status) && !("-2").equals(status)) {
            where.append(" AND lar.status=").append(status);
        }
        if (StringUtils.isNotBlank(starttime) && StringUtils.isNotBlank(endtime)) {
            where.append(" AND  lar.createdate between '").append(starttime).append("' and '").append(endtime).append("'");
        }
        if (StringUtils.isNotBlank(key)) {
            where.append(" AND (ct.name like '%").append(key).append("%' or ct.code='").append(key).append("%' or lar.room_name like '%").append(key).append("%')");
        }
        //确认好表单后，任选其中一个审核层级进行唯一确认表单，否则有重复，这里我不想使用去重
        where.append(" AND war.node_level = ").append(nodeLevelCheck).append(" AND war.mouldid = '").append(mouldId).append("'");
        String orderby = ExtSort.Orderby(request, "CASE WHEN lar.status = 0 THEN 0 WHEN lar.status = -1 THEN 1 ELSE 2 END,lar.createdate DESC");
        JsonData result = dbService.QueryJsonData(fields, table, where.toString(), orderby, start, limit);
        //获取表单id
        JSONArray data = result.getData();
        if (data!=null) {
            for (int i = 0; i < data.size(); i++) {
                String formId = data.getJSONObject(i).getString("uid");
                //获取审核状态
                String auditStatusSQL = "select auditcondition from tb_workflow_approval_record where formid = '" + formId + "'and node_level in " + nodeLevelList + " and mouldid = '" + mouldId + "' ";
                List<String> auditStatusList = dbService.queryFields(auditStatusSQL, String.class);
                String auditStatus = "0";
                if (!CollectionUtil.isEmpty(auditStatusList)) {
                    long count = auditStatusList.stream()
                            .filter("1"::equals)
                            .count();
                    if (count == auditStatusList.size()) {
                        auditStatus = "1";
                    }
                }
                data.getJSONObject(i).put("auditStatus", auditStatus);
            }
        }
        if (StringUtils.isNotBlank(type)&&!"-2".equals(type)) {
            // 创建一个新的 JSONArray 用于存储筛选后的结果
            JSONArray filteredData = new JSONArray();

            // 遍历原始 JSONArray 并筛选出 type 等于 1 的对象
            for (int i = 0; i < Objects.requireNonNull(data).size(); i++) {
                JSONObject item = data.getJSONObject(i);
                if (type.equals(item.getString("auditStatus"))) {
                    if ("2".equals(item.getString("status"))){
                        item.put("auditStatus","2");
                    }else {
                        filteredData.add(item);
                    }

                }
            }
            result.setData(filteredData);
        }else {
            // 遍历原始 JSONArray 并筛选出 type 等于 1 的对象
            for (int i = 0; i < Objects.requireNonNull(data).size(); i++) {
                JSONObject item = data.getJSONObject(i);
                if ("2".equals(item.getString("status"))){
                    item.put("auditStatus","2");
                }

            }

            result.setData(data);
        }
        return result;
    }

    public List<WorkFlowNodeDto> getNodeLevel(HttpServletRequest request, String userId, Integer mouldType) {
        return getNodeLevel(request, userId, mouldType, null);
    }

    @Nullable
    public List<WorkFlowNodeDto> getNodeLevel(HttpServletRequest request, String userId, Integer mouldType,Integer level) {
        //获取到当前人所对应的节点，根据节点类型获取到审核人员
        String sql = "SELECT nodelevel as nodeLevel,isfinal as isFinal,behavior_status as behaviorStatus,wm.uid as mouldId " +
                " FROM tb_workflow_mould wm " +
                " left join tb_workflow_node wn on wm.uid = wn.mouldid " +
                " left join tb_workflow_approvar wa on wa.nodeid = wn.uid" +
                " WHERE mouldtype=" + mouldType + " and wa.infoid = '" + userId + "'";
        if (level != null) {
            sql += " and wn.nodelevel = " + level;
        }
        return dbService.queryList(sql, WorkFlowNodeDto.class);
    }


    public R<Null> exportRecord(HttpServletRequest request, HttpServletResponse response, String starttime,
                                String endtime, String status, String key, Integer start, Integer limit) {
       String sql = "select ct.code as code ,lar.dining_dep diningDepCode,getminiorgname(lar.dining_dep) orgName,date_format(COALESCE(lar.breakfast_start_time, lar.lunch_start_time), '%Y-%m-%d') daily,date_format(lar.breakfast_start_time,'%Y-%m-%d %H:%i:%s') breakfastStartTime,date_format(lar.breakfast_end_time,'%Y-%m-%d %H:%i:%s') breakfastEndTime,lar.status," +
                " date_format(lar.lunch_start_time,'%Y-%m-%d %H:%i:%s') as lunchStartTime ,date_format(lar.lunch_end_time,'%Y-%m-%d %H:%i:%s') lunchEndTime,date_format(lar.create_dt,'%Y-%m-%d %H:%i:%s') createDt from tb_consume_apply_record lar " +
                " INNER JOIN tb_card_teachstudinfo ct ON ct.uid = lar.info_id ";
        sql += " where 1=1";
        if (StringUtils.isNotBlank(status) && !("-2").equals(status)) {
            sql += " AND lar.status=" + status;
        }
        if (StringUtils.isNotBlank(starttime)&& StringUtils.isNotBlank(endtime)) {
            sql+=" AND (lar.breakfast_start_time BETWEEN '"+ starttime +" 00:00:00' AND '"+ endtime +" 23:59:59' OR lar.lunch_start_time BETWEEN '"+ starttime +" 00:00:00'  AND '"+ endtime +" 23:59:59' ) ";
        }
        if (StringUtils.isNotBlank(key)) {
            sql += " AND (ct.name like '%" + key + "%' or ct.code='" + key + "')";
        }
        sql += "order by lar.create_dt DESC ";
        List<ConsumeApplyRecord> applyRecords = dbService.queryList(sql, ConsumeApplyRecord.class);

        String queryMoneySql = "select everymoney everyMoney,code from tb_consume_scheme where consumetype = 2 ";
        List<ConsumeSchemeDto> schemeDtoList = dbService.queryList(queryMoneySql, ConsumeSchemeDto.class);
        TreeMap<String, BigDecimal> schemeMap = new TreeMap<>();

        for (ConsumeSchemeDto consumeSchemeDto : schemeDtoList) {
            schemeMap.put(consumeSchemeDto.getCode(), consumeSchemeDto.getEveryMoney());
        }
        String selSql = "select name orgCode,attr1 everyMoney from tb_sys_dictionary where groupcode = 'SYS0000070' ";
        List<AnalysisPersonRateService.ConsumeSchemeForOrgCode> schemeForOrgCodes = dbService.queryList(selSql, AnalysisPersonRateService.ConsumeSchemeForOrgCode.class);
        for (AnalysisPersonRateService.ConsumeSchemeForOrgCode schemeForOrgCode : schemeForOrgCodes) {
            schemeMap.put(schemeForOrgCode.getOrgCode(), schemeForOrgCode.getEveryMoney());
        }
        List<DepartmentStatsRow> data = new ArrayList<>();
        Map<String, Map<String, DepartmentStats>> mapMap = applyRecords.stream()
                .collect(Collectors.groupingBy(ConsumeApplyRecord::getDaily, Collectors.groupingBy(ConsumeApplyRecord::getDiningDepCode,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                (List<ConsumeApplyRecord> records) -> {
                                    // 对同一部门在某一天的所有记录进行统计
                                    DepartmentStats stats = new DepartmentStats(records.get(0).getOrgName());
                                    for (ConsumeApplyRecord record : records) {
                                        if (record.getBreakfastStartTime() != null) {
                                            if (schemeMap.containsKey(record.getDiningDepCode())) {
                                                stats.addToTotalAmount(schemeMap.get(record.getDiningDepCode()));
                                                stats.addBreakfastAmount(schemeMap.get(record.getDiningDepCode()));  // 加上早餐金额
                                            } else {
                                                stats.addToTotalAmount(schemeMap.get("1"));
                                                stats.addBreakfastAmount(schemeMap.get("1"));  // 加上早餐金额
                                            }
                                            stats.incrementBreakfastCount(); // 增加早餐次数
                                        }
                                        if (record.getLunchStartTime() != null) {
                                            stats.incrementLunchCount(); // 增加中餐次数
                                            stats.addLunchAmount(schemeMap.get("2"));  // 加上中餐金额
                                            stats.addToTotalAmount(schemeMap.get("2"));
                                        }
                                    }
                                    return stats;
                                }))
                ));

        // 遍历外层 map (按日期分组)
        mapMap.forEach((daily, departmentStatsMap) -> {
            // 遍历内层 map (按部门分组)
            departmentStatsMap.forEach((orgName, stats) -> {
                DepartmentStatsRow row = new DepartmentStatsRow();
                row.setDaily(daily); // 设置日期
                row.setOrgName(stats.getOrgName()); // 设置部门
                row.setBreakfastCount(stats.getBreakfastCount()); // 设置早餐次数
                row.setLunchCount(stats.getLunchCount()); // 设置中餐次数
                row.setTotalAmount(stats.getTotalAmount()); // 设置总金额

                // 可以根据需求将早餐和中餐金额分别设置（假设分别存储在不同字段中）
                row.setBreakfastAmount(stats.getBreakfastAmount());
                row.setLunchAmount(stats.getLunchAmount());

                // 将 row 添加到 data 列表中
                data.add(row);
            });
        });
        if (CollectionUtil.isEmpty(data)) {
            return R.fail("导出失败");
        }

        LinkedHashMap<Integer, DepartmentStatsRow> map = new LinkedHashMap<Integer, DepartmentStatsRow>();
        Sheet<DepartmentStatsRow> sheet = new Sheet<>(map, DepartmentStatsRow.class);
        int row = 1;
        for (DepartmentStatsRow vo : data) {
            map.put(row, vo);
            row++;
        }
        String extFilePath = "temp/" + UUID.fastUUID() + ".xlsx";
        String filePath = WebConfig.getUploaddir() + extFilePath;
        com.ymiots.framework.utils.excel.ExcelUtil.write(sheet, filePath);
        return R.ok(extFilePath);
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DepartmentStatsRow extends Row {
        @ExcelProperty("日期")
        private String daily;

        @ExcelProperty("部门")
        private String orgName;

        @ExcelProperty("早餐次数")
        private int breakfastCount;

        @ExcelProperty("早餐金额")
        private BigDecimal breakfastAmount;

        @ExcelProperty("中餐次数")
        private int lunchCount;

        @ExcelProperty("中餐金额")
        private BigDecimal lunchAmount;

        @ExcelProperty("总金额")
        private BigDecimal totalAmount;
    }

    public static class DepartmentStats {
        private String orgName;
        private int breakfastCount;
        private int lunchCount;
        private BigDecimal breakfastAmount;
        private BigDecimal lunchAmount;
        private BigDecimal totalAmount;

        public DepartmentStats(String orgName) {
            this.orgName = orgName;
            this.breakfastCount = 0;
            this.lunchCount = 0;
            this.totalAmount = BigDecimal.ZERO;
            this.breakfastAmount = BigDecimal.ZERO;
            this.lunchAmount = BigDecimal.ZERO;
        }

        public void incrementBreakfastCount() {
            this.breakfastCount++;
        }

        public void incrementLunchCount() {
            this.lunchCount++;
        }

        public void addBreakfastAmount(BigDecimal amount) {
            this.breakfastAmount = this.breakfastAmount.add(amount);
        }
        public void addLunchAmount(BigDecimal amount) {
            this.lunchAmount = this.lunchAmount.add(amount);
        }

        public void addToTotalAmount(BigDecimal amount) {
            this.totalAmount = this.totalAmount.add(amount);
        }

        public String getOrgName() {
            return orgName;
        }

        public int getBreakfastCount() {
            return breakfastCount;
        }

        public int getLunchCount() {
            return lunchCount;
        }

        public BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public void setOrgName(String orgName) {
            this.orgName = orgName;
        }

        public void setBreakfastCount(int breakfastCount) {
            this.breakfastCount = breakfastCount;
        }

        public void setLunchCount(int lunchCount) {
            this.lunchCount = lunchCount;
        }

        public void setTotalAmount(BigDecimal totalAmount) {
            this.totalAmount = totalAmount;
        }

        public BigDecimal getBreakfastAmount() {
            return breakfastAmount;
        }

        public void setBreakfastAmount(BigDecimal breakfastAmount) {
            this.breakfastAmount = breakfastAmount;
        }

        public BigDecimal getLunchAmount() {
            return lunchAmount;
        }

        public void setLunchAmount(BigDecimal lunchAmount) {
            this.lunchAmount = lunchAmount;
        }
    }



    @Data
    public static class ConsumeApplyRecord {
        private String code;
        private String orgName;
        private String diningDepCode;
        private String daily;
        private String breakfastStartTime;
        private String breakfastEndTime;
        private String lunchStartTime;
        private String lunchEndTime;
        private String createDt;
        private int status;
    }

    @Data
    public static class ConsumeSchemeDto {
        private BigDecimal everyMoney;
        private String code;
    }


}
