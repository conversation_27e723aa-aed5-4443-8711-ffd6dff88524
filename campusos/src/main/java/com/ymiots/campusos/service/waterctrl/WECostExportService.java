package com.ymiots.campusos.service.waterctrl;

import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.DateHelper;
import com.ymiots.campusos.common.ExcelUtil;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import net.sf.json.JSONArray;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 水电费账单导出服务（简化版）
 * 使用您现有的数据库方法
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class WECostExportService extends BaseService {


    /**
     * 导出Excel格式账单
     */
    public JsonResult exportToExcel(String startDate, String endDate, String areaCode) {
        try {
            Log.info(WECostExportService.class,
                String.format("开始导出Excel账单 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            // 1. 查询费用记录数据
            JSONArray costRecords = getCostRecords(startDate, endDate, areaCode);

            if (costRecords.size() == 0) {
                JsonResult result = new JsonResult();
                result.setSuccess(false);
                result.setMsg("没有找到符合条件的费用记录");
                return result;
            }

            // 2. 转换数据格式
            JSONArray list = convertToExportFormat(costRecords);

            // 3. 定义Excel列结构
            JSONArray cells = createExcelColumns();

            // 4. 生成Excel文件
            String title = "水电费账单";
            String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
            String fileName = title + format + ".xlsx";

            String filePath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp", fileName, title);

            Log.info(WECostExportService.class,
                String.format("Excel账单导出成功 - 文件: %s, 记录数: %d", fileName, costRecords.size()));

            // 5. 返回结果
            JSONObject data = new JSONObject();
            data.put("fileName", fileName);
            data.put("filePath", filePath);
            data.put("recordCount", costRecords.size());
            data.put("exportTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            JsonResult result = new JsonResult();
            result.setSuccess(true);
            result.setMsg("导出成功");
            result.setData(data);
            return result;

        } catch (Exception e) {
            Log.error(WECostExportService.class,
                String.format("Excel导出失败: %s", e.getMessage()), e);

            JsonResult result = new JsonResult();
            result.setSuccess(false);
            result.setMsg("Excel导出失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 生成打印内容
     */
    public String generatePrintContent(String startDate, String endDate, String areaCode) {
        try {
            Log.info(WECostExportService.class,
                String.format("生成打印内容 - 日期: %s至%s, 区域: %s", startDate, endDate, areaCode));

            // 1. 查询费用记录数据
            JSONArray costRecords = getCostRecords(startDate, endDate, areaCode);

            // 2. 生成HTML内容
            return generateBillHTML(costRecords, startDate, endDate, areaCode);

        } catch (Exception e) {
            Log.error(WECostExportService.class,
                String.format("生成打印内容失败: %s", e.getMessage()));
            throw new RuntimeException("生成打印内容失败", e);
        }
    }

    /**
     * 查询费用记录
     */
    public com.alibaba.fastjson.JSONArray getCostRecords(String startDate, String endDate, String areaCode) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT * ");
            sql.append("FROM tb_equipment_reading_records r join tb_equipment_cost_calculation c on r.uid = c.reading_record_id ");
            sql.append("WHERE r.status = 1 ");
            sql.append("AND DATE(r.reading_date) >= '").append(startDate).append("' ");
            sql.append("AND DATE(r.reading_date) <= '").append(endDate).append("' ");

            if (areaCode != null && !areaCode.trim().isEmpty()) {
                sql.append("AND r.area_code = '").append(areaCode).append("' ");
            }

            sql.append("ORDER BY r.area_code, r.equipment_code, r.reading_date");

            Log.info(WECostExportService.class, "执行查询SQL: " + sql.toString());

            // 使用您现有的 QueryList 方法
            net.sf.json.JSONArray result = dbService.QueryList(sql.toString());

            // 转换为 com.alibaba.fastjson.JSONArray
            com.alibaba.fastjson.JSONArray convertedResult = new com.alibaba.fastjson.JSONArray();
            for (int i = 0; i < result.size(); i++) {
                net.sf.json.JSONObject item = result.getJSONObject(i);
                com.alibaba.fastjson.JSONObject convertedItem = new com.alibaba.fastjson.JSONObject();
                for (Object key : item.keySet()) {
                    convertedItem.put(key.toString(), item.get(key));
                }
                convertedResult.add(convertedItem);
            }

            return convertedResult;

        } catch (Exception e) {
            Log.error(WECostExportService.class,
                String.format("查询费用记录失败: %s", e.getMessage()));
            return new com.alibaba.fastjson.JSONArray();
        }
    }

    /**
     * 写入Excel数据
     */
    private void writeExcelData(Sheet sheet, JSONArray costRecords, String startDate, String endDate, String areaCode) {
        int rowNum = 0;

        // 1. 写入标题
        Row titleRow = sheet.createRow(rowNum++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("水电费账单");

        // 2. 写入基本信息
        rowNum++; // 空行
        Row infoRow1 = sheet.createRow(rowNum++);
        infoRow1.createCell(0).setCellValue("账单期间：");
        infoRow1.createCell(1).setCellValue(startDate + " 至 " + endDate);

        Row infoRow2 = sheet.createRow(rowNum++);
        infoRow2.createCell(0).setCellValue("生成时间：");
        infoRow2.createCell(1).setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        if (areaCode != null && !areaCode.trim().isEmpty()) {
            Row infoRow3 = sheet.createRow(rowNum++);
            infoRow3.createCell(0).setCellValue("区域编码：");
            infoRow3.createCell(1).setCellValue(areaCode);
        }

        // 3. 写入表头
        rowNum++; // 空行
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = {
            "设备编号", "设备类型", "区域编码", "读数日期", "当前读数", "上次读数",
            "用量", "单价", "费用", "余额"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 4. 写入数据
        BigDecimal totalCost = BigDecimal.ZERO;

        for (int i = 0; i < costRecords.size(); i++) {
            JSONObject record = costRecords.getJSONObject(i);
            Row dataRow = sheet.createRow(rowNum++);

            dataRow.createCell(0).setCellValue(getString(record, "equipment_code"));
            dataRow.createCell(1).setCellValue(getEquipmentTypeName(getString(record, "equipment_type")));
            dataRow.createCell(2).setCellValue(getString(record, "area_code"));
            dataRow.createCell(3).setCellValue(getString(record, "reading_date"));
            dataRow.createCell(4).setCellValue(getDouble(record, "current_reading"));
            dataRow.createCell(5).setCellValue(getDouble(record, "previous_reading"));
            dataRow.createCell(6).setCellValue(getDouble(record, "usage_amount"));
            dataRow.createCell(7).setCellValue(getDouble(record, "unit_price"));
            dataRow.createCell(8).setCellValue(getDouble(record, "total_cost"));
            dataRow.createCell(9).setCellValue(getDouble(record, "current_balance"));

            totalCost = totalCost.add(getBigDecimal(record, "total_cost"));
        }

        // 5. 写入汇总
        rowNum++; // 空行
        Row summaryRow = sheet.createRow(rowNum++);
        summaryRow.createCell(0).setCellValue("总计费用：");
        summaryRow.createCell(1).setCellValue(totalCost.doubleValue());

        // 6. 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 生成账单HTML
     */
    private String generateBillHTML(JSONArray costRecords, String startDate, String endDate, String areaCode) {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>");
        html.append("<html><head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>水电费账单</title>");
        html.append("<style>");
        html.append("body { font-family: Arial, sans-serif; margin: 20px; }");
        html.append(".header { text-align: center; margin-bottom: 30px; }");
        html.append(".title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }");
        html.append(".info { margin-bottom: 20px; }");
        html.append(".info-item { margin: 5px 0; }");
        html.append("table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
        html.append("th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }");
        html.append("th { background-color: #f2f2f2; font-weight: bold; }");
        html.append(".money { text-align: right; }");
        html.append("@media print { body { margin: 0; } }");
        html.append("</style>");
        html.append("</head><body>");

        // 标题和基本信息
        html.append("<div class='header'>");
        html.append("<div class='title'>水电费账单</div>");
        html.append("</div>");

        html.append("<div class='info'>");
        html.append("<div class='info-item'><strong>账单期间：</strong>")
            .append(startDate).append(" 至 ").append(endDate).append("</div>");
        html.append("<div class='info-item'><strong>生成时间：</strong>")
            .append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("</div>");

        if (areaCode != null && !areaCode.trim().isEmpty()) {
            html.append("<div class='info-item'><strong>区域编码：</strong>")
                .append(areaCode).append("</div>");
        }
        html.append("</div>");

        // 费用明细表
        html.append("<table>");
        html.append("<thead><tr>");
        html.append("<th>设备编号</th><th>设备类型</th><th>区域编码</th><th>读数日期</th>");
        html.append("<th>当前读数</th><th>上次读数</th><th>用量</th><th>单价</th><th>费用</th><th>余额</th>");
        html.append("</tr></thead>");
        html.append("<tbody>");

        BigDecimal totalCost = BigDecimal.ZERO;

        for (int i = 0; i < costRecords.size(); i++) {
            JSONObject record = costRecords.getJSONObject(i);
            html.append("<tr>");
            html.append("<td>").append(getString(record, "equipment_code")).append("</td>");
            html.append("<td>").append(getEquipmentTypeName(getString(record, "equipment_type"))).append("</td>");
            html.append("<td>").append(getString(record, "area_code")).append("</td>");
            html.append("<td>").append(getString(record, "reading_date")).append("</td>");
            html.append("<td>").append(getDouble(record, "current_reading")).append("</td>");
            html.append("<td>").append(getDouble(record, "previous_reading")).append("</td>");
            html.append("<td>").append(getDouble(record, "usage_amount")).append("</td>");
            html.append("<td class='money'>￥").append(getDouble(record, "unit_price")).append("</td>");
            html.append("<td class='money'>￥").append(getDouble(record, "total_cost")).append("</td>");
            html.append("<td class='money'>￥").append(getDouble(record, "current_balance")).append("</td>");
            html.append("</tr>");

            totalCost = totalCost.add(getBigDecimal(record, "total_cost"));
        }

        html.append("</tbody></table>");

        // 费用汇总
        html.append("<div style='margin-top: 30px;'>");
        html.append("<table style='width: 300px;'>");
        html.append("<tr style='font-weight: bold; background-color: #f2f2f2;'>");
        html.append("<td><strong>总计费用：</strong></td>");
        html.append("<td class='money'>￥").append(totalCost.toPlainString()).append("</td>");
        html.append("</tr>");
        html.append("</table>");
        html.append("</div>");

        html.append("</body></html>");

        return html.toString();
    }

    /**
     * 获取设备类型名称
     */
    private String getEquipmentTypeName(String equipmentType) {
        switch (equipmentType) {
            case "1": return "电表";
            case "2": return "水表";
            case "4": return "热水表";
            case "221": return "气表";
            default: return "未知";
        }
    }

    /**
     * 安全获取字符串值
     */
    private String getString(JSONObject obj, String key) {
        try {
            Object value = obj.get(key);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 安全获取double值
     */
    private double getDouble(JSONObject obj, String key) {
        try {
            Object value = obj.get(key);
            if (value == null) return 0.0;
            return Double.parseDouble(value.toString());
        } catch (Exception e) {
            return 0.0;
        }
    }

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimal(JSONObject obj, String key) {
        try {
            Object value = obj.get(key);
            if (value == null) return BigDecimal.ZERO;
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 转换数据为Excel实体
     */
    private List<WECostExcelData> convertToExcelData(JSONArray costRecords) {
        List<WECostExcelData> excelDataList = new ArrayList<>();

        for (int i = 0; i < costRecords.size(); i++) {
            JSONObject record = costRecords.getJSONObject(i);

            WECostExcelData excelData = new WECostExcelData();
            excelData.setEquipmentCode(getString(record, "equipment_code"));
            excelData.setEquipmentType(getEquipmentTypeName(getString(record, "equipment_type")));
            excelData.setAreaCode(getString(record, "area_code"));
            excelData.setReadingDate(getString(record, "reading_date"));
            excelData.setCurrentReading(getDouble(record, "current_reading"));
            excelData.setPreviousReading(getDouble(record, "previous_reading"));
            excelData.setUsageAmount(getDouble(record, "usage_amount"));
            excelData.setUnitPrice(getDouble(record, "unit_price"));
            excelData.setTotalCost(getDouble(record, "total_cost"));
            excelData.setCurrentBalance(getDouble(record, "current_balance"));

            // 分时电表信息
            if ("1".equals(getString(record, "equipment_type")) && "1".equals(getString(record, "is_time_sharing"))) {
                excelData.setTimeSharingInfo(String.format(
                        "尖:%.2f峰:%.2f平:%.2f谷:%.2f",
                        getDouble(record, "tip_usage"),
                        getDouble(record, "peak_usage"),
                        getDouble(record, "flat_usage"),
                        getDouble(record, "valley_usage")
                ));
            } else {
                excelData.setTimeSharingInfo("非分时");
            }

            excelDataList.add(excelData);
        }

        return excelDataList;
    }

    /**
     * 转换数据为分时电表Excel实体
     */
    private List<TimeSharingExcelData> convertToTimeSharingExcelData(JSONArray costRecords) {
        List<TimeSharingExcelData> excelDataList = new ArrayList<>();

        for (int i = 0; i < costRecords.size(); i++) {
            JSONObject record = costRecords.getJSONObject(i);

            TimeSharingExcelData excelData = new TimeSharingExcelData();
            excelData.setEquipmentCode(getString(record, "equipment_code"));
            excelData.setAreaCode(getString(record, "area_code"));
            excelData.setReadingDate(getString(record, "reading_date"));

            // 尖时
            excelData.setTipReading(getDouble(record, "tip_reading"));
            excelData.setPreviousTipReading(getDouble(record, "previous_tip_reading"));
            excelData.setTipUsage(getDouble(record, "tip_usage"));
            excelData.setTipPrice(getDouble(record, "tip_price"));
            excelData.setTipCost(getDouble(record, "tip_cost"));

            // 峰时
            excelData.setPeakReading(getDouble(record, "peak_reading"));
            excelData.setPreviousPeakReading(getDouble(record, "previous_peak_reading"));
            excelData.setPeakUsage(getDouble(record, "peak_usage"));
            excelData.setPeakPrice(getDouble(record, "peak_price"));
            excelData.setPeakCost(getDouble(record, "peak_cost"));

            // 平时
            excelData.setFlatReading(getDouble(record, "flat_reading"));
            excelData.setPreviousFlatReading(getDouble(record, "previous_flat_reading"));
            excelData.setFlatUsage(getDouble(record, "flat_usage"));
            excelData.setFlatPrice(getDouble(record, "flat_price"));
            excelData.setFlatCost(getDouble(record, "flat_cost"));

            // 谷时
            excelData.setValleyReading(getDouble(record, "valley_reading"));
            excelData.setPreviousValleyReading(getDouble(record, "previous_valley_reading"));
            excelData.setValleyUsage(getDouble(record, "valley_usage"));
            excelData.setValleyPrice(getDouble(record, "valley_price"));
            excelData.setValleyCost(getDouble(record, "valley_cost"));

            // 总计
            excelData.setTotalCost(getDouble(record, "total_cost"));
            excelData.setCurrentBalance(getDouble(record, "current_balance"));

            excelDataList.add(excelData);
        }

        return excelDataList;
    }

    /**
     * 转换为导出格式
     */
    private net.sf.json.JSONArray convertToExportFormat(JSONArray costRecords) {
        net.sf.json.JSONArray list = new net.sf.json.JSONArray();

        for (int i = 0; i < costRecords.size(); i++) {
            JSONObject record = costRecords.getJSONObject(i);
            net.sf.json.JSONObject item = new net.sf.json.JSONObject();

            item.put("equipment_code", getString(record, "equipment_code"));
            item.put("equipment_type_name", getEquipmentTypeName(getString(record, "equipment_type")));
            item.put("area_code", getString(record, "area_code"));
            item.put("reading_date", getString(record, "reading_date"));
            item.put("current_reading", formatNumber(getDouble(record, "current_reading")));
            item.put("previous_reading", formatNumber(getDouble(record, "previous_reading")));
            item.put("usage_amount", formatNumber(getDouble(record, "usage_amount")));
            item.put("unit_price", formatMoney(getDouble(record, "unit_price")));
            item.put("total_cost", formatMoney(getDouble(record, "total_cost")));
            item.put("current_balance", formatMoney(getDouble(record, "current_balance")));

            // 分时信息
            if ("1".equals(getString(record, "equipment_type")) && "1".equals(getString(record, "is_time_sharing"))) {
                item.put("time_sharing_info", String.format(
                    "尖:%.2f峰:%.2f平:%.2f谷:%.2f",
                    getDouble(record, "tip_usage"),
                    getDouble(record, "peak_usage"),
                    getDouble(record, "flat_usage"),
                    getDouble(record, "valley_usage")
                ));
            } else {
                item.put("time_sharing_info", "非分时");
            }

            list.add(item);
        }

        return list;
    }

    /**
     * 创建Excel列结构
     */
    private net.sf.json.JSONArray createExcelColumns() {
        net.sf.json.JSONArray cells = new net.sf.json.JSONArray();
        net.sf.json.JSONObject cell;

        cell = new net.sf.json.JSONObject();
        cell.put("name", "设备编号");
        cell.put("datakey", "equipment_code");
        cell.put("size", 20);
        cells.add(cell);

        cell = new net.sf.json.JSONObject();
        cell.put("name", "设备类型");
        cell.put("datakey", "equipment_type_name");
        cell.put("size", 15);
        cells.add(cell);

        cell = new net.sf.json.JSONObject();
        cell.put("name", "区域编码");
        cell.put("datakey", "area_code");
        cell.put("size", 15);
        cells.add(cell);

        cell = new net.sf.json.JSONObject();
        cell.put("name", "读数日期");
        cell.put("datakey", "reading_date");
        cell.put("size", 15);
        cells.add(cell);

        cell = new net.sf.json.JSONObject();
        cell.put("name", "当前读数");
        cell.put("datakey", "current_reading");
        cell.put("size", 15);
        cells.add(cell);

        cell = new net.sf.json.JSONObject();
        cell.put("name", "上次读数");
        cell.put("datakey", "previous_reading");
        cell.put("size", 15);
        cells.add(cell);

        cell = new net.sf.json.JSONObject();
        cell.put("name", "用量");
        cell.put("datakey", "usage_amount");
        cell.put("size", 12);
        cells.add(cell);

        cell = new net.sf.json.JSONObject();
        cell.put("name", "单价(元)");
        cell.put("datakey", "unit_price");
        cell.put("size", 12);
        cells.add(cell);

        cell = new net.sf.json.JSONObject();
        cell.put("name", "费用(元)");
        cell.put("datakey", "total_cost");
        cell.put("size", 15);
        cells.add(cell);

        cell = new net.sf.json.JSONObject();
        cell.put("name", "余额(元)");
        cell.put("datakey", "current_balance");
        cell.put("size", 15);
        cells.add(cell);

        cell = new net.sf.json.JSONObject();
        cell.put("name", "分时信息");
        cell.put("datakey", "time_sharing_info");
        cell.put("size", 30);
        cells.add(cell);

        return cells;
    }

    /**
     * 格式化数字
     */
    private String formatNumber(double value) {
        return String.format("%.2f", value);
    }

    /**
     * 格式化金额
     */
    private String formatMoney(double value) {
        return String.format("%.2f", value);
    }

    /**
     * 安全获取字符串值
     */
    private String getString(com.alibaba.fastjson.JSONObject obj, String key) {
        try {
            Object value = obj.get(key);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 安全获取double值
     */
    private double getDouble(com.alibaba.fastjson.JSONObject obj, String key) {
        try {
            Object value = obj.get(key);
            if (value == null) return 0.0;
            return Double.parseDouble(value.toString());
        } catch (Exception e) {
            return 0.0;
        }
    }

    /**
     * 生成账单HTML
     */
    private String generateBillHTML(com.alibaba.fastjson.JSONArray costRecords, String startDate, String endDate, String areaCode) {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>");
        html.append("<html><head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>水电费账单</title>");
        html.append("<style>");
        html.append("body { font-family: Arial, sans-serif; margin: 20px; }");
        html.append(".header { text-align: center; margin-bottom: 30px; }");
        html.append(".title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }");
        html.append(".info { margin-bottom: 20px; }");
        html.append(".info-item { margin: 5px 0; }");
        html.append("table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
        html.append("th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }");
        html.append("th { background-color: #f2f2f2; font-weight: bold; }");
        html.append(".money { text-align: right; }");
        html.append("@media print { body { margin: 0; } }");
        html.append("</style>");
        html.append("</head><body>");

        // 标题和基本信息
        html.append("<div class='header'>");
        html.append("<div class='title'>水电费账单</div>");
        html.append("</div>");

        html.append("<div class='info'>");
        html.append("<div class='info-item'><strong>账单期间：</strong>")
            .append(startDate).append(" 至 ").append(endDate).append("</div>");
        html.append("<div class='info-item'><strong>生成时间：</strong>")
            .append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("</div>");

        if (areaCode != null && !areaCode.trim().isEmpty()) {
            html.append("<div class='info-item'><strong>区域编码：</strong>")
                .append(areaCode).append("</div>");
        }
        html.append("</div>");

        // 费用明细表
        html.append("<table>");
        html.append("<thead><tr>");
        html.append("<th>设备编号</th><th>设备类型</th><th>区域编码</th><th>读数日期</th>");
        html.append("<th>当前读数</th><th>上次读数</th><th>用量</th><th>单价</th><th>费用</th><th>余额</th>");
        html.append("</tr></thead>");
        html.append("<tbody>");

        BigDecimal totalCost = BigDecimal.ZERO;

        for (int i = 0; i < costRecords.size(); i++) {
            com.alibaba.fastjson.JSONObject record = costRecords.getJSONObject(i);
            html.append("<tr>");
            html.append("<td>").append(getString(record, "equipment_code")).append("</td>");
            html.append("<td>").append(getEquipmentTypeName(getString(record, "equipment_type"))).append("</td>");
            html.append("<td>").append(getString(record, "area_code")).append("</td>");
            html.append("<td>").append(getString(record, "reading_date")).append("</td>");
            html.append("<td>").append(getDouble(record, "current_reading")).append("</td>");
            html.append("<td>").append(getDouble(record, "previous_reading")).append("</td>");
            html.append("<td>").append(getDouble(record, "usage_amount")).append("</td>");
            html.append("<td class='money'>￥").append(getDouble(record, "unit_price")).append("</td>");
            html.append("<td class='money'>￥").append(getDouble(record, "total_cost")).append("</td>");
            html.append("<td class='money'>￥").append(getDouble(record, "current_balance")).append("</td>");
            html.append("</tr>");

            totalCost = totalCost.add(new BigDecimal(String.valueOf(getDouble(record, "total_cost"))));
        }

        html.append("</tbody></table>");

        // 费用汇总
        html.append("<div style='margin-top: 30px;'>");
        html.append("<table style='width: 300px;'>");
        html.append("<tr style='font-weight: bold; background-color: #f2f2f2;'>");
        html.append("<td><strong>总计费用：</strong></td>");
        html.append("<td class='money'>￥").append(totalCost.toPlainString()).append("</td>");
        html.append("</tr>");
        html.append("</table>");
        html.append("</div>");

        html.append("</body></html>");

        return html.toString();
    }
}
