package com.ymiots.campusos.service.waterctrl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 水电费账单导出服务
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class WECostExportService extends BaseService {

    /**
     * 导出Excel格式账单
     */
    public JsonResult exportToExcel(String startDate, String endDate, String areaCode, HttpServletResponse response) {
        try {
            // 1. 获取账单数据
            Map<String, Object> billData = generateBillData(startDate, endDate, areaCode);

            // 2. 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("水电费账单");

            // 3. 创建样式
            Map<String, CellStyle> styles = createExcelStyles(workbook);

            // 4. 写入数据
            writeExcelData(sheet, billData, styles);

            // 5. 设置响应头
            String fileName = String.format("水电费账单_%s至%s.xlsx", startDate, endDate);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 6. 输出文件
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            workbook.close();
            outputStream.flush();
            outputStream.close();

            Log.info(WECostExportService.class,
                    String.format("Excel账单导出成功 - 文件: %s", fileName));

            // 创建JSONObject返回数据（使用fastjson）
            com.alibaba.fastjson.JSONObject result = new com.alibaba.fastjson.JSONObject();
            result.put("fileName", fileName);
            result.put("downloadUrl", "/download/" + fileName); // 下载链接
            result.put("exportTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            JsonResult jsonResult = new JsonResult();
            jsonResult.setSuccess(true);
            jsonResult.setMsg("导出成功");
            jsonResult.setData(result);
            return jsonResult;

        } catch (Exception e) {
            Log.error(WECostExportService.class,
                    String.format("Excel导出失败: %s", e.getMessage()));
            return Json.getJsonResult(false, "Excel导出失败: " + e.getMessage());
        }
    }

    /**
     * 导出PDF格式账单
     */
    public JsonResult exportToPDF(String startDate, String endDate, String areaCode, HttpServletResponse response) {
        try {
            // 1. 获取账单数据
            Map<String, Object> billData = generateBillData(startDate, endDate, areaCode);

            // 2. 生成HTML内容
            String htmlContent = generateBillHTML(billData);

            // 3. 转换为PDF（这里需要引入PDF库，如iText或Flying Saucer）
            // 暂时返回HTML内容，实际项目中需要转换为PDF
            String fileName = String.format("水电费账单_%s至%s.pdf", startDate, endDate);

            // 设置响应头
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 这里应该写入PDF内容到response.getOutputStream()
            // 暂时写入HTML内容作为示例
            response.getOutputStream().write(htmlContent.getBytes("UTF-8"));
            response.getOutputStream().flush();
            response.getOutputStream().close();

            Log.info(WECostExportService.class,
                    String.format("PDF账单导出成功 - 文件: %s", fileName));

            // 创建JSONObject返回数据
            JSONObject result = new JSONObject();
            result.put("fileName", fileName);
            result.put("downloadUrl", "/download/" + fileName);

            JsonResult jsonResult = new JsonResult();
            jsonResult.setSuccess(true);
            jsonResult.setMsg("导出成功");
            jsonResult.setData(result);
            return jsonResult;

        } catch (Exception e) {
            Log.error(WECostExportService.class,
                    String.format("PDF导出失败: %s", e.getMessage()));
            return Json.getJsonResult(false, "PDF导出失败: " + e.getMessage());
        }
    }

    /**
     * 生成打印内容
     */
    public String generatePrintContent(String startDate, String endDate, String areaCode) {
        try {
            Map<String, Object> billData = generateBillData(startDate, endDate, areaCode);
            return generateBillHTML(billData);

        } catch (Exception e) {
            Log.error(WECostExportService.class,
                    String.format("生成打印内容失败: %s", e.getMessage()));
            throw new RuntimeException("生成打印内容失败", e);
        }
    }

    /**
     * 生成账单数据
     */
    public Map<String, Object> generateBillData(String startDate, String endDate, String areaCode) {
        try {
            Map<String, Object> billData = new HashMap<>();

            // 1. 基本信息
            billData.put("startDate", startDate);
            billData.put("endDate", endDate);
            billData.put("generateDate", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            billData.put("areaCode", areaCode);

            // 2. 查询设备费用数据
            List<Map<String, Object>> costRecords = getCostRecords(startDate, endDate, areaCode);
            billData.put("costRecords", costRecords);

            // 3. 统计汇总
            Map<String, Object> summary = calculateSummary(costRecords);
            billData.put("summary", summary);

            // 4. 区域信息
            Map<String, Object> areaInfo = getAreaInfo(areaCode);
            billData.put("areaInfo", areaInfo);

            Log.info(WECostExportService.class,
                    String.format("生成账单数据成功 - 记录数: %d", costRecords.size()));

            return billData;

        } catch (Exception e) {
            Log.error(WECostExportService.class,
                    String.format("生成账单数据失败: %s", e.getMessage()));
            throw new RuntimeException("生成账单数据失败", e);
        }
    }

    /**
     * 批量导出
     */
    public JsonResult batchExport(String startDate, String endDate, String[] areaCodes,
                                  String exportType, HttpServletResponse response) {
        try {
            // 创建压缩包，包含多个区域的账单
            // 这里简化处理，实际项目中需要创建ZIP文件

            Log.info(WECostExportService.class,
                    String.format("批量导出开始 - 区域数量: %d", areaCodes.length));

            // 暂时只导出第一个区域的账单
            if (areaCodes.length > 0) {
                return exportToExcel(startDate, endDate, areaCodes[0], response);
            }

            return Json.getJsonResult(false, "没有指定导出区域");

        } catch (Exception e) {
            Log.error(WECostExportService.class,
                    String.format("批量导出失败: %s", e.getMessage()));
            return Json.getJsonResult(false, "批量导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取费用记录
     */
    private List<Map<String, Object>> getCostRecords(String startDate, String endDate, String areaCode) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT r.equipment_code, r.equipment_type, r.area_code, r.reading_date, ");
            sql.append("r.current_reading, r.previous_reading, r.usage_amount, r.unit_price, ");
            sql.append("r.tip_reading, r.previous_tip_reading, r.tip_usage, r.tip_price, r.tip_cost, ");
            sql.append("r.peak_reading, r.previous_peak_reading, r.peak_usage, r.peak_price, r.peak_cost, ");
            sql.append("r.flat_reading, r.previous_flat_reading, r.flat_usage, r.flat_price, r.flat_cost, ");
            sql.append("r.valley_reading, r.previous_valley_reading, r.valley_usage, r.valley_price, r.valley_cost, ");
            sql.append("r.total_cost, r.is_time_sharing, r.current_balance ");
            sql.append("FROM tb_equipment_reading_records r ");
            sql.append("WHERE r.status = 1 ");
            sql.append("AND DATE(r.reading_date) >= '").append(startDate).append("' ");
            sql.append("AND DATE(r.reading_date) <= '").append(endDate).append("' ");

            if (areaCode != null && !areaCode.trim().isEmpty()) {
                sql.append("AND r.area_code = '").append(areaCode).append("' ");
            }

            sql.append("ORDER BY r.area_code, r.equipment_code, r.reading_date");

            // 使用 QueryList 方法
            JSONArray jsonArray = dbService.QueryList(sql.toString());

            // 转换为 List<Map<String, Object>>
            List<Map<String, Object>> result = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                Map<String, Object> map = new HashMap<>();

                // 转换JSONObject到Map
                for (Object key : jsonObject.keySet()) {
                    map.put(key.toString(), jsonObject.get(key));
                }
                result.add(map);
            }

            return result;

        } catch (Exception e) {
            Log.error(WECostExportService.class,
                    String.format("查询费用记录失败: %s", e.getMessage()));
            return new ArrayList<>();
        }
    }

    /**
     * 计算汇总统计
     */
    private Map<String, Object> calculateSummary(List<Map<String, Object>> costRecords) {
        Map<String, Object> summary = new HashMap<>();

        BigDecimal totalElectricUsage = BigDecimal.ZERO;
        BigDecimal totalElectricCost = BigDecimal.ZERO;
        BigDecimal totalWaterUsage = BigDecimal.ZERO;
        BigDecimal totalWaterCost = BigDecimal.ZERO;
        BigDecimal totalHotWaterUsage = BigDecimal.ZERO;
        BigDecimal totalHotWaterCost = BigDecimal.ZERO;
        BigDecimal totalGasUsage = BigDecimal.ZERO;
        BigDecimal totalGasCost = BigDecimal.ZERO;

        int electricCount = 0;
        int waterCount = 0;
        int hotWaterCount = 0;
        int gasCount = 0;

        for (Map<String, Object> record : costRecords) {
            String equipmentType = String.valueOf(record.get("equipment_type"));
            BigDecimal usage = getBigDecimal(record.get("usage_amount"));
            BigDecimal cost = getBigDecimal(record.get("total_cost"));

            switch (equipmentType) {
                case "1": // 电表
                    totalElectricUsage = totalElectricUsage.add(usage);
                    totalElectricCost = totalElectricCost.add(cost);
                    electricCount++;
                    break;
                case "2": // 水表
                    totalWaterUsage = totalWaterUsage.add(usage);
                    totalWaterCost = totalWaterCost.add(cost);
                    waterCount++;
                    break;
                case "4": // 热水表
                    totalHotWaterUsage = totalHotWaterUsage.add(usage);
                    totalHotWaterCost = totalHotWaterCost.add(cost);
                    hotWaterCount++;
                    break;
                case "221": // 气表
                    totalGasUsage = totalGasUsage.add(usage);
                    totalGasCost = totalGasCost.add(cost);
                    gasCount++;
                    break;
            }
        }

        summary.put("totalElectricUsage", totalElectricUsage);
        summary.put("totalElectricCost", totalElectricCost);
        summary.put("totalWaterUsage", totalWaterUsage);
        summary.put("totalWaterCost", totalWaterCost);
        summary.put("totalHotWaterUsage", totalHotWaterUsage);
        summary.put("totalHotWaterCost", totalHotWaterCost);
        summary.put("totalGasUsage", totalGasUsage);
        summary.put("totalGasCost", totalGasCost);

        summary.put("electricCount", electricCount);
        summary.put("waterCount", waterCount);
        summary.put("hotWaterCount", hotWaterCount);
        summary.put("gasCount", gasCount);

        BigDecimal grandTotal = totalElectricCost.add(totalWaterCost).add(totalHotWaterCost).add(totalGasCost);
        summary.put("grandTotal", grandTotal);

        return summary;
    }

    /**
     * 获取区域信息
     */
    private Map<String, Object> getAreaInfo(String areaCode) {
        try {
            if (areaCode == null || areaCode.trim().isEmpty()) {
                Map<String, Object> defaultInfo = new HashMap<>();
                defaultInfo.put("area_name", "全部区域");
                defaultInfo.put("area_code", "ALL");
                return defaultInfo;
            }

            String sql = "SELECT code as area_code, name as area_name, parentid as parent_code, areatype as area_type " +
                    "FROM tb_dev_areaframework WHERE code = '" + areaCode + "' AND status = 1";

            JSONArray jsonArray = dbService.QueryList(sql);

            if (jsonArray.size() > 0) {
                JSONObject jsonObject = jsonArray.getJSONObject(0);
                Map<String, Object> result = new HashMap<>();

                // 转换JSONObject到Map
                for (Object key : jsonObject.keySet()) {
                    result.put(key.toString(), jsonObject.get(key));
                }
                return result;
            } else {
                Map<String, Object> defaultInfo = new HashMap<>();
                defaultInfo.put("area_name", "未知区域");
                defaultInfo.put("area_code", areaCode);
                return defaultInfo;
            }

        } catch (Exception e) {
            Log.error(WECostExportService.class,
                    String.format("获取区域信息失败: %s", e.getMessage()));
            Map<String, Object> defaultInfo = new HashMap<>();
            defaultInfo.put("area_name", "未知区域");
            defaultInfo.put("area_code", areaCode);
            return defaultInfo;
        }
    }

    /**
     * 创建Excel样式
     */
    private Map<String, CellStyle> createExcelStyles(Workbook workbook) {
        Map<String, CellStyle> styles = new HashMap<>();

        // 标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        styles.put("title", titleStyle);

        // 表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        styles.put("header", headerStyle);

        // 数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        styles.put("data", dataStyle);

        // 金额样式
        CellStyle moneyStyle = workbook.createCellStyle();
        moneyStyle.setAlignment(HorizontalAlignment.RIGHT);
        moneyStyle.setBorderTop(BorderStyle.THIN);
        moneyStyle.setBorderBottom(BorderStyle.THIN);
        moneyStyle.setBorderLeft(BorderStyle.THIN);
        moneyStyle.setBorderRight(BorderStyle.THIN);
        DataFormat format = workbook.createDataFormat();
        moneyStyle.setDataFormat(format.getFormat("￥#,##0.00"));
        styles.put("money", moneyStyle);

        return styles;
    }

    /**
     * 写入Excel数据
     */
    private void writeExcelData(Sheet sheet, Map<String, Object> billData, Map<String, CellStyle> styles) {
        int rowNum = 0;

        // 1. 写入标题
        Row titleRow = sheet.createRow(rowNum++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("水电费账单");
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(0, 0, 0, 10));

        // 2. 写入基本信息
        rowNum++; // 空行
        Row infoRow1 = sheet.createRow(rowNum++);
        infoRow1.createCell(0).setCellValue("账单期间：");
        infoRow1.createCell(1).setCellValue(billData.get("startDate") + " 至 " + billData.get("endDate"));

        Row infoRow2 = sheet.createRow(rowNum++);
        infoRow2.createCell(0).setCellValue("生成时间：");
        infoRow2.createCell(1).setCellValue((String) billData.get("generateDate"));

        Map<String, Object> areaInfo = (Map<String, Object>) billData.get("areaInfo");
        if (areaInfo != null && !areaInfo.isEmpty()) {
            Row infoRow3 = sheet.createRow(rowNum++);
            infoRow3.createCell(0).setCellValue("区域：");
            infoRow3.createCell(1).setCellValue((String) areaInfo.get("area_name"));
        }

        // 3. 写入表头
        rowNum++; // 空行
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = {
                "设备编号", "设备类型", "区域编码", "读数日期", "当前读数", "上次读数",
                "用量", "单价", "费用", "余额", "备注"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(styles.get("header"));
        }

        // 4. 写入数据
        List<Map<String, Object>> costRecords = (List<Map<String, Object>>) billData.get("costRecords");
        for (Map<String, Object> record : costRecords) {
            Row dataRow = sheet.createRow(rowNum++);

            dataRow.createCell(0).setCellValue((String) record.get("equipment_code"));
            dataRow.createCell(1).setCellValue(getEquipmentTypeName((String) record.get("equipment_type")));
            dataRow.createCell(2).setCellValue((String) record.get("area_code"));
            dataRow.createCell(3).setCellValue(formatDate(record.get("reading_date")));
            dataRow.createCell(4).setCellValue(getBigDecimal(record.get("current_reading")).doubleValue());
            dataRow.createCell(5).setCellValue(getBigDecimal(record.get("previous_reading")).doubleValue());
            dataRow.createCell(6).setCellValue(getBigDecimal(record.get("usage_amount")).doubleValue());
            dataRow.createCell(7).setCellValue(getBigDecimal(record.get("unit_price")).doubleValue());

            Cell costCell = dataRow.createCell(8);
            costCell.setCellValue(getBigDecimal(record.get("total_cost")).doubleValue());
            costCell.setCellStyle(styles.get("money"));

            Cell balanceCell = dataRow.createCell(9);
            balanceCell.setCellValue(getBigDecimal(record.get("current_balance")).doubleValue());
            balanceCell.setCellStyle(styles.get("money"));

            dataRow.createCell(10).setCellValue(""); // 备注
        }

        // 5. 写入汇总
        Map<String, Object> summary = (Map<String, Object>) billData.get("summary");
        rowNum++; // 空行
        Row summaryHeaderRow = sheet.createRow(rowNum++);
        summaryHeaderRow.createCell(0).setCellValue("费用汇总");

        Row summaryRow = sheet.createRow(rowNum++);
        summaryRow.createCell(0).setCellValue("总计费用：");
        Cell totalCell = summaryRow.createCell(1);
        totalCell.setCellValue(getBigDecimal(summary.get("grandTotal")).doubleValue());
        totalCell.setCellStyle(styles.get("money"));

        // 6. 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 生成账单HTML
     */
    private String generateBillHTML(Map<String, Object> billData) {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>");
        html.append("<html><head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>水电费账单</title>");
        html.append("<style>");
        html.append("body { font-family: Arial, sans-serif; margin: 20px; }");
        html.append(".header { text-align: center; margin-bottom: 30px; }");
        html.append(".title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }");
        html.append(".info { margin-bottom: 20px; }");
        html.append(".info-item { margin: 5px 0; }");
        html.append("table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
        html.append("th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }");
        html.append("th { background-color: #f2f2f2; font-weight: bold; }");
        html.append(".money { text-align: right; }");
        html.append(".summary { margin-top: 30px; }");
        html.append(".summary-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }");
        html.append("@media print { body { margin: 0; } }");
        html.append("</style>");
        html.append("</head><body>");

        // 标题和基本信息
        html.append("<div class='header'>");
        html.append("<div class='title'>水电费账单</div>");
        html.append("</div>");

        html.append("<div class='info'>");
        html.append("<div class='info-item'><strong>账单期间：</strong>")
                .append(billData.get("startDate")).append(" 至 ").append(billData.get("endDate")).append("</div>");
        html.append("<div class='info-item'><strong>生成时间：</strong>")
                .append(billData.get("generateDate")).append("</div>");

        Map<String, Object> areaInfo = (Map<String, Object>) billData.get("areaInfo");
        if (areaInfo != null && !areaInfo.isEmpty()) {
            html.append("<div class='info-item'><strong>区域：</strong>")
                    .append(areaInfo.get("area_name")).append("</div>");
        }
        html.append("</div>");

        // 费用明细表
        html.append("<table>");
        html.append("<thead><tr>");
        html.append("<th>设备编号</th><th>设备类型</th><th>区域编码</th><th>读数日期</th>");
        html.append("<th>当前读数</th><th>上次读数</th><th>用量</th><th>单价</th><th>费用</th><th>余额</th>");
        html.append("</tr></thead>");
        html.append("<tbody>");

        List<Map<String, Object>> costRecords = (List<Map<String, Object>>) billData.get("costRecords");
        for (Map<String, Object> record : costRecords) {
            html.append("<tr>");
            html.append("<td>").append(record.get("equipment_code")).append("</td>");
            html.append("<td>").append(getEquipmentTypeName((String) record.get("equipment_type"))).append("</td>");
            html.append("<td>").append(record.get("area_code")).append("</td>");
            html.append("<td>").append(formatDate(record.get("reading_date"))).append("</td>");
            html.append("<td>").append(getBigDecimal(record.get("current_reading")).toPlainString()).append("</td>");
            html.append("<td>").append(getBigDecimal(record.get("previous_reading")).toPlainString()).append("</td>");
            html.append("<td>").append(getBigDecimal(record.get("usage_amount")).toPlainString()).append("</td>");
            html.append("<td class='money'>￥").append(getBigDecimal(record.get("unit_price")).toPlainString()).append("</td>");
            html.append("<td class='money'>￥").append(getBigDecimal(record.get("total_cost")).toPlainString()).append("</td>");
            html.append("<td class='money'>￥").append(getBigDecimal(record.get("current_balance")).toPlainString()).append("</td>");
            html.append("</tr>");
        }

        html.append("</tbody></table>");

        // 费用汇总
        Map<String, Object> summary = (Map<String, Object>) billData.get("summary");
        html.append("<div class='summary'>");
        html.append("<div class='summary-title'>费用汇总</div>");
        html.append("<table style='width: 400px;'>");
        html.append("<tr><td><strong>电费总计：</strong></td><td class='money'>￥")
                .append(getBigDecimal(summary.get("totalElectricCost")).toPlainString()).append("</td></tr>");
        html.append("<tr><td><strong>水费总计：</strong></td><td class='money'>￥")
                .append(getBigDecimal(summary.get("totalWaterCost")).toPlainString()).append("</td></tr>");
        html.append("<tr><td><strong>热水费总计：</strong></td><td class='money'>￥")
                .append(getBigDecimal(summary.get("totalHotWaterCost")).toPlainString()).append("</td></tr>");
        html.append("<tr><td><strong>燃气费总计：</strong></td><td class='money'>￥")
                .append(getBigDecimal(summary.get("totalGasCost")).toPlainString()).append("</td></tr>");
        html.append("<tr style='font-weight: bold; background-color: #f2f2f2;'><td><strong>总计：</strong></td><td class='money'>￥")
                .append(getBigDecimal(summary.get("grandTotal")).toPlainString()).append("</td></tr>");
        html.append("</table>");
        html.append("</div>");

        html.append("</body></html>");

        return html.toString();
    }

    /**
     * 获取设备类型名称
     */
    private String getEquipmentTypeName(String equipmentType) {
        switch (equipmentType) {
            case "1":
                return "电表";
            case "2":
                return "水表";
            case "4":
                return "热水表";
            case "221":
                return "气表";
            default:
                return "未知";
        }
    }

    /**
     * 格式化日期
     */
    private String formatDate(Object dateObj) {
        if (dateObj == null) return "";
        try {
            if (dateObj instanceof Date) {
                return new SimpleDateFormat("yyyy-MM-dd").format((Date) dateObj);
            }
            return dateObj.toString();
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimal(Object value) {
        if (value == null) return BigDecimal.ZERO;
        if (value instanceof BigDecimal) return (BigDecimal) value;
        try {
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }
}