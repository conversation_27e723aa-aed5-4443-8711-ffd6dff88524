package com.ymiots.campusos.service.card;

import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dto.card.CardTeachStudInfoDTO;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Null;
import java.util.List;

public interface CardBalanceService {

    /**
     * 查询用户余额
     */
    R<List<CardTeachStudInfoDTO>> getUserBalance(HttpServletRequest request, String orgCode, boolean viewChild,String key, int page, int size);

    R<Null> exportUserBalance(String orgCode, boolean viewChild,String key);
}
