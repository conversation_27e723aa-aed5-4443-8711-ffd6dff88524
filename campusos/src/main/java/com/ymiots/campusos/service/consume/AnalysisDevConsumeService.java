package com.ymiots.campusos.service.consume;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.ExcelUtil;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.JsonData;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class AnalysisDevConsumeService extends BaseService {

    @Autowired
    private AnalysisEverydayService everydayService;

    @Autowired
    private UserRedis userRedis;

    public JsonData getDevConsumeData(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime, int start, int limit) {
        String schemeSQL = "SELECT DISTINCT code FROM tb_consume_scheme";
        JSONArray schemeja = dbService.QueryList(schemeSQL);
        String field = "";
        if (!schemeja.isEmpty()) {
            for (int i = 0; i < schemeja.size(); i++) {
                String code = schemeja.getJSONObject(i).getString("code");
                field += "SUM(cadr.scheme" + code + ") as scheme" + code + ",SUM(cadr.schemetime" + code + ") as schemetime" + code + ",";
            }
        }
        String sql = "SELECT cadr.machineid,cadr.devname," + field + "SUM(cadr.schememoney) as schememoney,SUM(cadr.schemetimes) as schemetimes " +
                "FROM tb_consume_analysis_device_rate cadr ";
        SysUser sysUser = userRedis.get(request);
        if (sysUser.getUsertype() == 1) {
            String userid = sysUser.getUid();
            sql += " inner join tb_dev_areaframework da on cadr.areacode=da.code " +
                    "inner join tb_dev_areaframework_user cou on cou.areacode=da.code " +
                    " and cou.userid = '" + userid + "'";
        }
        String where = " cadr.daily BETWEEN '" + starttime + "' AND '" + endtime + "' ";
        if (StringUtils.isNotBlank(key)) {
            where += " AND (cadr.machineid='"+key+"' OR cadr.devname like '%"+key+"%')";
        }
        sql += "WHERE"+where;
        String countsql = sql;
        if (start == 0 && limit == 0) {
            sql += "GROUP BY cadr.machineid,cadr.devname ORDER BY " + ExtSort.Orderby(request, "cadr.machineid ASC");
        } else {
            sql += "GROUP BY cadr.machineid,cadr.devname ORDER BY " + ExtSort.Orderby(request, "cadr.machineid ASC") + " limit " + start + "," + limit;
        }
        String table = "tb_consume_analysis_device_rate cadr ";

        if (sysUser.getUsertype() == 1) {
            String userid = sysUser.getUid();
            table += " inner join tb_dev_areaframework da on cadr.areacode=da.code " +
                    "inner join tb_dev_areaframework_user cou on cou.areacode=da.code " +
                    " and cou.userid = '" + userid + "'";
        }
        JsonData data = dbService.QueryJsonData(countsql + "GROUP BY cadr.machineid,cadr.devname ORDER BY " + ExtSort.Orderby(request, "cadr.machineid ASC"));
        JSONArray list=everydayService.getSumConsume("machineid", table,"WHERE"+where);
        JsonData result = dbService.QueryJsonData(sql);
        result.getData().addAll(list);
        result.setTotal(data.getData().size());
        return result;
    }

    public JsonData getSchemeName(HttpServletRequest request, HttpServletResponse response) {
        String sql = "SELECT DISTINCT sc.code,d.name FROM tb_consume_scheme sc LEFT JOIN tb_sys_dictionary d ON d.code=sc.code and d.groupcode='SYS0000051'  ORDER BY code ASC";
        return dbService.QueryJsonData(sql);
    }

    public String ExportDevConsume(HttpServletRequest request, HttpServletResponse response, String key, String starttime, String endtime,String currentPage,String pageSize,String size) throws IOException {
        String schemeSQL = "SELECT code,name FROM tb_consume_scheme ORDER BY code ASC";
        JSONArray schemeja = dbService.QueryList(schemeSQL);
        List<String> schemeCode = new ArrayList<String>();
        List<String> schemeName = new ArrayList<String>();
        List<String> schemetimeCode = new ArrayList<String>();
        if (!schemeja.isEmpty()) {
            for (int i = 0; i < schemeja.size(); i++) {
                String code = schemeja.getJSONObject(i).getString("code");
                schemeCode.add("scheme" + code);
                schemetimeCode.add("schemetime"+code);
                schemeName.add(schemeja.getJSONObject(i).getString("name"));
            }
        }
        int star = 1;
        JsonData result = new JsonData();
        if (currentPage.equals("1")) {
            result = getDevConsumeData(request, response, key, starttime, endtime, 0, Integer.valueOf(size));
        } else if (Integer.valueOf(currentPage) > star) {
            result = getDevConsumeData(request, response, key, starttime, endtime, (Integer.valueOf(currentPage)-1)*Integer.valueOf(size), Integer.valueOf(size));
        } else {
            result = getDevConsumeData(request, response, key, starttime, endtime, 0, 0);
        }
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "machineid");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "设备名称");
        cell.put("datakey", "devname");
        cell.put("size", 10);
        cells.add(cell);

        for (int i = 0; i < schemeCode.size(); i++) {
            cell = new JSONObject();
            cell.put("name", schemeName.get(i));
            cell.put("datakey", schemeCode.get(i));
            cell.put("size", 10);
            cells.add(cell);

            cell = new JSONObject();
            cell.put("name", schemeName.get(i)+"次数");
            cell.put("datakey", schemetimeCode.get(i));
            cell.put("size", 10);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "合计金额");
        cell.put("datakey", "schememoney");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "合计次数");
        cell.put("datakey", "schemetimes");
        cell.put("size", 15);
        cells.add(cell);

        List<CellRangeAddress> slist = new ArrayList<CellRangeAddress>();
        CellRangeAddress cellRangeAddress = new CellRangeAddress(list.size(),list.size(),0,1);
        slist.add(cellRangeAddress);
        List<JSONArray> heads = new ArrayList<JSONArray>();
        String title= "设备消费汇总";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";
        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp", filename, title, slist, heads,starttime,endtime,"1","1");
        return filepath;

    }
}
