package com.ymiots.campusos.service.visitor;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.util.UUIDUtils;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dto.WorkFlowNodeDto;
import com.ymiots.campusos.redis.RedisMessageEntity;
import com.ymiots.campusos.redis.RedisPublish;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysConfigService;
import com.ymiots.campusos.util.weixin.entity.TemplateMessageData;
import com.ymiots.campusos.util.workflow.VisitorHandler;
import com.ymiots.campusos.util.workflow.WorkFlowUtil;
import com.ymiots.campusos.util.workflow.dto.VisitorSubscribeDTO;
import com.ymiots.campusos.util.workflow.entity.VisitorInfo;
import com.ymiots.campusos.util.workflow.entity.VisitorSubscribe;
import com.ymiots.framework.common.*;
import com.ymiots.framework.datafactory.CallbackTransaction;
import com.ymiots.framework.entity.AliyunSmsEntity;
import com.ymiots.framework.service.AliyunSendSmsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Repository
public class VisitorSubscribeService extends BaseService {

    @Autowired
    @Lazy
    UserRedis userredis;

    @Autowired
    SysConfigService sysconfig;

    @Autowired
    RedisPublish redispublish;

    @Autowired
    AliyunSendSmsService smg;

    @Autowired
    SysConfigService syscfg;

    @Autowired
    VisitorRegisterService visitorRegisterService;

    @Autowired
    WorkFlowUtil workFlowUtil;

    @Autowired
    VisitorHandler handler;

    @Autowired
    JdbcTemplate jdbcTemplate;

    public JsonData getSubscribeList(HttpServletRequest request, HttpServletResponse response, int auditstatus,
                                     String key, String starttime, String endtime, int start, int limit) {
        String where = " 1=1 ";
        if (userredis.get(request).getUsertype() != 1) {
            String fields = "sub.uid,info.uid as visitorid,info.name,info.idtype,info.idcard,info.sex,info.mobile,info.company,info.department,info.position," +
                    " sub.reason,sub.byvisitor,sub.byvisitorid,sub.visittime,sub.leavetime,sub.areaposition,sub.status," +
                    "sub.areapositionName as areapositionname,sub.plateno,sub.personnum,sub.things,getorgname(tt.orgcode) as byvistororgname,sub.createdate,sub.status ";
            String table = "tb_visitor_subscribe sub " +
                    "left join tb_visitor_visitorinfo info on info.uid=sub.visitorid " +
                    "left join tb_card_teachstudinfo tt on tt.uid=sub.byvisitorid " ;
                    //"left join tb_visitor_machine vm ON vm.position = sub.areaposition";
            if (!StringUtils.isBlank(starttime) && !StringUtils.isBlank(endtime)) {
                where += " and (sub.createdate between '" + starttime + "' and '" + endtime + "')";
            }
            if (!StringUtils.isBlank(key)) {
                where += " and (info.name like '%" + key + "%' or info.idcard like '%" + key + "%' or info.mobile like '%"
                        + key + "%') ";
            }
            String orderBy = ExtSort.Orderby(request, "sub.createdate desc");
            return dbService.QueryJsonData(fields, table, where, orderBy, start, limit);
        }else if(userredis.get(request).getUsertype()==1&&syscfg.get("visitormachineneedauth").equals("0")){
            String fields = "sub.uid,info.uid as visitorid,info.name,info.idtype,info.idcard,info.sex,info.mobile,info.company,info.department,info.position," +
                    " sub.reason,sub.byvisitor,sub.byvisitorid,sub.visittime,sub.leavetime,sub.areaposition,sub.status," +
                    "sub.areapositionName as areapositionname,sub.plateno,sub.personnum,sub.things,getorgname(tt.orgcode) as byvistororgname,sub.createdate,sub.status ";
            String table = "tb_visitor_subscribe sub " +
                    "left join tb_visitor_visitorinfo info on info.uid=sub.visitorid " +
                    "left join tb_card_teachstudinfo tt on tt.uid=sub.byvisitorid " +
                    "join tb_card_orgframework_user ou on ou.orgcode=tt.orgcode and ou.userid ='"+userredis.get(request).getUid()+"' " ;
            //"left join tb_visitor_machine vm ON vm.position = sub.areaposition";
            if (!StringUtils.isBlank(starttime) && !StringUtils.isBlank(endtime)) {
                where += " and (sub.createdate between '" + starttime + "' and '" + endtime + "')";
            }
            if (!StringUtils.isBlank(key)) {
                where += " and (info.name like '%" + key + "%' or info.idcard like '%" + key + "%' or info.mobile like '%"
                        + key + "%') ";
            }
            String orderBy = ExtSort.Orderby(request, "sub.createdate desc");
            return dbService.QueryJsonData(fields, table, where, orderBy, start, limit);
        }
        String docId = userredis.get(request).getEmpid();
        WorkFlowNodeDto nodeLevel = workFlowUtil.getNodeLevel(request, docId, 3);
        if (nodeLevel == null) {
            return Json.getJsonData(false, "该用户没有审批权限");
        }
        String fields = "sub.uid,info.uid as visitorid,info.name,info.idtype,info.idcard,info.sex,info.mobile,info.company,info.department,info.position," +
                " sub.reason,sub.byvisitor,sub.byvisitorid,sub.visittime,sub.leavetime," +
//                "vsp.card_no,vsp.qrcode," +
                "sub.areaposition,sub.status," +
                "vm.name as areapositionname,sub.plateno,sub.personnum,sub.things,getorgname(tt.orgcode) as byvistororgname,sub.createdate,sub.status ";
        String table = "tb_visitor_subscribe sub " +
//                "join tb_visitor_subscribe_person vsp on sub.uid = vsp.visitor_subscribe_id " +
                "left join tb_workflow_approval_record war on war.formid = sub.uid and war.mouldid = '" + nodeLevel.getMouldId() + "'" +
                "left join tb_visitor_visitorinfo info on info.uid=sub.visitorid " +
                "left join tb_card_teachstudinfo tt on tt.uid=sub.byvisitorid " +
                "left join tb_visitor_machine vm ON vm.position = sub.areaposition";

        if (!StringUtils.isBlank(starttime) && !StringUtils.isBlank(endtime)) {
            where += " and (sub.createdate between '" + starttime + "' and '" + endtime + "')";
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (info.name like '%" + key + "%' or info.idcard like '%" + key + "%' or info.mobile like '%"
                    + key + "%') ";
        }
        //控制只能查看自己的预约记录
        if (userredis.get(request).getUsertype() == 1) {
            String recordsWhereSQL = workFlowUtil.getAuditRecordsWhereSQL(request, 3, nodeLevel,"vs.byvisitorid","vs.byvisitorid");
            if (StringUtils.isNotBlank(recordsWhereSQL)) {
                where += recordsWhereSQL;
            }
            //获取自己部门的预约记录
            String infoId = userredis.get(request).getEmpid();
            if (StringUtils.isBlank(infoId)) {
                return Json.getJsonData(false, "系统账号未绑定人员信息");
            }
//            List<String> formIdList = new ArrayList<>();
            //获取自己创建的预约
//            String createSQL = "SELECT uid FROM tb_visitor_subscribe WHERE creatorid='"+infoId+"'";
//            List<String> meCreateVisitorSubscribes = dbService.queryFields(createSQL, String.class);
//            if (CollectionUtil.isNotEmpty(meCreateVisitorSubscribes)) {
//                formIdList.addAll(meCreateVisitorSubscribes);
//            }
            //获取访问我的预约
//            String visitSQL = "SELECT uid FROM tb_visitor_subscribe WHERE byvisitorid='"+infoId+"'";
//            List<String> visitorSubscribesForMe = dbService.queryFields(visitSQL, String.class);
//            if (CollectionUtil.isNotEmpty(meCreateVisitorSubscribes)) {
//                formIdList.addAll(visitorSubscribesForMe);
//            }
//            if (CollectionUtil.isNotEmpty(formIdList)) {
//                where += " and sub.uid in ("
//                        + StringUtils.join(formIdList, ",") + ")";
//            }
        }
        String orderBy = ExtSort.Orderby(request, "sub.createdate desc");
        return dbService.QueryJsonData(fields,table,where,orderBy,start,limit);
    }

    public JsonData getVisitorList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        String fields = "uid,name,idtype,idcard,sex,nation,address,birthday,mobile,photo,company,position,department,email";
        String table = "tb_visitor_visitorinfo ";
        String where = "1=1 ";
        if (!StringUtils.isBlank(key)) {
            where += "AND (cardsn='" + ComHelper.LeftPad(key, 12, '0') + "' or name like '%" + key + "%' or mobile like '%" + key + "%' or idcard='" + key + "' )";
        }
        String orderby = ExtSort.Orderby(request, "createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonResult DelSubscribe(HttpServletRequest request, HttpServletResponse response, String uids) {
        if (userredis.get(request).getUsertype() != 2 && userredis.get(request).getUsertype() != 9) {
            int isDelSQL = dbService.getCount("tb_visitor_subscribe", "auditstatus=1 AND uid IN ('" + String.join("','", uids.split(",")) + "')");
            if (isDelSQL > 0) {
                return Json.getJsonResult(false, "所选的预约记录已审核，无法删除");
            }
            String sql = "delete from tb_visitor_subscribe where find_in_set(uid,'" + uids + "')>0";
            dbService.excuteSql(sql);
        } else {
            String sql = "delete from tb_visitor_subscribe where find_in_set(uid,'" + uids + "')>0";
            dbService.excuteSql(sql);
        }
        return Json.getJsonResult(true);
    }

    public JsonResult isCanAudit(HttpServletRequest request, HttpServletResponse response, String uid, String byvisitorid, String areaposition) {

        if (userredis.get(request).getUsertype() == 2 || userredis.get(request).getUsertype() == 9) {
            return Json.getJsonResult(true);
        }
        int isAreaAdmin = dbService.getCount("tb_visitor_administrators", "infoid='" + userredis.get(request).getEmpid() + "' AND areaposition='" + areaposition + "'");

        String infoid = userredis.get(request).getEmpid();
        String orgcodeSQL = "SELECT orgcode FROM tb_card_teachstudinfo WHERE uid='" + infoid + "'";
        JSONArray ja = dbService.QueryList(orgcodeSQL);
        String orgcode = ja.getJSONObject(0).getString("orgcode");
        int isOrgAdmin = dbService.getCount("tb_visitor_subscribe vs INNER JOIN tb_card_teachstudinfo ct ON ct.uid=vs.byvisitorid", "ct.orgcode like '" + orgcode + "%' AND vs.uid='" + uid + "'");

        //被访人是自己可以审核
        if (StringUtils.isNotBlank(byvisitorid) && byvisitorid.equals(userredis.get(request).getEmpid())) {
            return Json.getJsonResult(true);
        }
        if ("1".equals(syscfg.get("pushPersonnelType"))) {
            //是自己负责的区域可以审核
            if (isOrgAdmin > 0 && isAreaAdmin > 0) {
                return Json.getJsonResult(true);
            }
        } else {
            //是自己负责的部门可以审核
            if (isOrgAdmin > 0) {
                return Json.getJsonResult(true);
            }
        }
        return Json.getJsonResult(false, "该条访问记录没有审核权限");
    }

//    public JsonResult AuditSubscribe(HttpServletRequest request, HttpServletResponse response, final String uid,
//                                     final String visitorid, String cardsn, String qrcode, int auditstatusint, final String remark,
//                                     String visittimestr, final String leavetimestr, String areaposition, String things, String reason) throws Exception {
//        String requestUserid = "";
//        if (null != request) {
//            requestUserid = userredis.getUserId(request);
//        } else {
//            requestUserid = "system";
//        }
//        final String userid = requestUserid;
//        int byagree = 0;
//        if (auditstatusint == 1) {
//            byagree = 1;
//        }
//        if (auditstatusint == 2) {
//            byagree = 0;
//        }
//        if (auditstatusint == 1 || auditstatusint == 2) {
//            auditstatusint = 1;
//        } else {
//            auditstatusint = 0;
//        }
//        if (!StringUtils.isBlank(cardsn)) {
//            cardsn = ComHelper.LeftPad(cardsn, 12, '0');
//        }
//        if (byagree == 1) {
//            if (!StringUtils.isBlank(qrcode)) {
//                qrcode = ComHelper.LeftPad(qrcode, 8, '0');
//                int visiotrCount = dbService.getCount("tb_visitor_visitorinfo", "qrcode='" + qrcode + "' and uid<>'" + visitorid + "'");
//                if (visiotrCount > 0) {
//                    return Json.getJsonResult(false, "访客二维码已被占用，请重新生成！");
//                }
//                visiotrCount = dbService.getCount("tb_card_qrcode", "qrcode='" + qrcode + "'");
//                if (visiotrCount > 0) {
//                    return Json.getJsonResult(false, "访客二维码已被占用，请重新生成！");
//                }
//            } else {
//                if ("1".equals(syscfg.get("visitorqrcodeformat"))) {
//                    qrcode = RandomHelper.CreateQRcode();
//                } else {
//                    qrcode = visitorRegisterService.createRandomCard();
//                }
//            }
//        }
//
//        String subSQL = "SELECT byvisitor,byvisitorid,byagree,isinvited,date_format(visittime,'%Y-%m-%d %H:%i:%s') as visittime FROM tb_visitor_subscribe where uid='" + uid + "';";
//        JSONArray sublist = dbService.QueryList(subSQL);
//        if (sublist.size() == 0) {
//            return Json.getJsonResult(false, "预约信息不存在");
//        }
//        final int oldbyagree = sublist.getJSONObject(0).getIntValue("byagree");
//        String qrcodetimeout = sysconfig.get("qrcodetimeout");
//        if (StringUtils.isBlank(qrcodetimeout)) {
//            qrcodetimeout = "60";
//        }
//
//
//        String isagree = "0";
//        if ((oldbyagree == 0 || oldbyagree == 1) && byagree == 1) {
//            isagree = "1";
//        }
//
//        int count = dbService.getCount("tb_visitor_visitorinfo", "uid='" + visitorid + "' AND ifnull(qrcode,'') <> ''");
//        String updateVisitorInfo = "";
//        if (count > 0) {
//            updateVisitorInfo = "update tb_visitor_visitorinfo set cardsn='" + cardsn + "',qrcodetimeout=date_add(now(), interval " + qrcodetimeout + " second) where uid='" + visitorid + "'";
//        } else {
//            updateVisitorInfo = "update tb_visitor_visitorinfo set cardsn='" + cardsn + "',qrcode='" + qrcode + "',qrcodetimeout=date_add(now(), interval " + qrcodetimeout + " second) where uid='" + visitorid + "'";
//        }
//
//        String updateVisiotorSub = "update tb_visitor_subscribe set reason=?,things=?, auditstatus=?,audittime=now(),areaposition=?,auditor=?,byagree=?,byagreetime=now(),remark=?,visittime=?,leavetime=? where uid=?";
//
//        String insertVisitorRecordNameList = "";
//        if (StringUtils.isBlank(areaposition) && !"-1".equals(areaposition)) {
//            insertVisitorRecordNameList = "INSERT INTO tb_visitor_record_namelist(uid,devid,visitorid,downnum,createdate,status) SELECT uuid(),t.devid,'"
//                    + visitorid + "',0,now(),0 FROM (select distinct devid from tb_visitor_machine_device) t ";
//        } else {
//            insertVisitorRecordNameList = "INSERT INTO tb_visitor_record_namelist(uid,devid,visitorid,downnum,createdate,status) SELECT uuid(),devid,'" + visitorid + "',0,now(),0 from tb_visitor_machine_device where position='" + areaposition + "' ";
//        }
//
//        final String insertVisitorRecord = "insert into tb_visitor_record(uid,visitorid,reason,byvisitor,byvisitorid,endtime,isleave,subscribeid,createdate,creatorid,status,plateno,things) select uuid(),visitorid,reason,byvisitor,byvisitorid,'"
//                + leavetimestr + "',0,'" + uid + "',now(),'" + userid + "',1,plateno,things from tb_visitor_subscribe where uid='" + uid + "' ";
//
//        final String insertVisitorRecordNameSQL = insertVisitorRecordNameList;
//
//        String finalIsagree = isagree;
//        String finalUpdateVisitorInfo = updateVisitorInfo;
//        int finalAuditstatusint = auditstatusint;
//        int finalByagree = byagree;
//        dbService.excuteTransaction(new CallbackTransaction() {
//            @Override
//            public void execute(Connection connection) throws SQLException {
//                // 访客信息表更新卡号、二维码
//                PreparedStatement ps = connection.prepareStatement(finalUpdateVisitorInfo);
//                ps.executeUpdate();
//                ps.close();
//
//                // 更新预约状态
//                PreparedStatement ps2 = connection.prepareStatement(updateVisiotorSub);
//                ps2.setString(1, reason);
//                ps2.setString(2, things);
//                ps2.setInt(3, finalAuditstatusint);
//                ps2.setString(4, areaposition);
//                ps2.setString(5, userid);
//                ps2.setInt(6, finalByagree);
//                ps2.setString(7, remark);
//                ps2.setString(8, visittimestr);
//                ps2.setString(9, leavetimestr);
//                ps2.setString(10, uid);
//                ps2.executeUpdate();
//                ps2.close();
//
//                // 获取随访人员
//                String selOther = "SELECT othervisitor FROM tb_visitor_subscribe WHERE uid='" + uid + "'";
//                JSONArray otherja = dbService.QueryList(selOther);
//
//                if (finalIsagree.equals("1")) {
//                    // 原来不同意，现在通过时生成访客来访记录
//                    ps = connection.prepareStatement(insertVisitorRecord);
//                    ps.executeUpdate();
//                    ps.close();
//
//                    if ("1".equals(sysconfig.get("visitorusefaceorc"))) {
//                        ps = connection.prepareStatement(insertVisitorRecordNameSQL);
//                        ps.executeUpdate();
//                        ps.close();
//                    }
//
//                    // 随访人员添加到来访记录和授权名单
//                    if (!otherja.isEmpty()) {
//                        String ov = otherja.getJSONObject(0).getString("othervisitor");
//                        if (StringUtils.isNotBlank(ov)) {
//                            String[] othervisitors = ov.split(",");
//                            for (String othervisitor : othervisitors) {
//                                String insertOtherRecord = "INSERT INTO tb_visitor_record(uid,visitorid,byvisitor,byvisitorid,plateno,personnum,reason,things,endtime,isleave,subscribeid,createdate,creatorid,status)"
//                                        + " SELECT uuid(),'" + othervisitor
//                                        + "',byvisitor,byvisitorid,plateno,personnum,reason,things,leavetime, 0, uid, now(),'"
//                                        + userid + "', 1 FROM tb_visitor_subscribe WHERE uid='" + uid + "'";
//                                ps = connection.prepareStatement(insertOtherRecord);
//                                ps.executeUpdate();
//                                ps.close();
//
//                                if ("1".equals(sysconfig.get("visitorusefaceorc"))) {
//                                    String insertNameList = "";
//
//                                    if (StringUtils.isBlank(areaposition)) {
//                                        insertNameList = "INSERT INTO tb_visitor_record_namelist(uid,devid,visitorid,downnum,createdate,status) SELECT uuid(),t.devid,'"
//                                                + othervisitor + "',0,now(),0 FROM (select distinct devid from tb_visitor_machine_device) t ";
//                                    } else {
//                                        insertNameList = "INSERT INTO tb_visitor_record_namelist(uid,devid,visitorid,downnum,createdate,status) " +
//                                                "select uuid(),devid,'" + visitorid + "',0,now(),0 from tb_visitor_machine_device where position='" + areaposition + "' ";
//                                    }
//
//                                    ps = connection.prepareStatement(insertNameList);
//                                    ps.executeUpdate();
//                                    ps.close();
//                                }
//                            }
//                        }
//                    }
//
//                    String sql = "SELECT uid,name,idcard,sex,mobile,plateno FROM tb_visitor_visitorinfo where uid='" + visitorid + "' ";
//                    JSONArray list = dbService.QueryList(sql);
//                    if (list.size() > 0) {
//                        JSONObject visitoritem = list.getJSONObject(0);
//                        if (!StringUtils.isBlank(visitoritem.getString("plateno"))) {
//                            // 如果车牌号码不为空，则推送访客信息至停车场系统
//                            JSONObject message = new JSONObject();
//                            message.put("code", visitoritem.getString("plateno"));
//                            message.put("name", visitoritem.getString("name"));
//                            message.put("idcode", visitoritem.getString("idcard"));
//                            message.put("phone", visitoritem.getString("mobile"));
//                            message.put("datestart", DateHelper.format(new Date()));
//                            message.put("dateend", leavetimestr);
//                            redispublish.Publish("visitor/visitorplate", message.toJSONString());
//                        }
//                    }
//                }
//            }
//        });
//
//        //访客自行预约的只推微信公众号审核结果，如果是邀请的，只要存在对应的联系方式就推
//        int isinvited = sublist.getJSONObject(0).getIntValue("isinvited");
//        if (isinvited == 1) {
//            if (auditstatusint == 1) {
//                //推送微信消息
//                invitedPushWeiXinMsg(byagree, oldbyagree, visitorid, uid);
//                //推送电子邮件
//                invitedPushEmail(byagree, oldbyagree, visitorid, uid);
//                //推送短信消息
//                invitedPushSMS(byagree, oldbyagree, visitorid);
//            }
//        } else {
//            if (auditstatusint == 1) {
//                // 审核同意或不同意后发送消息
//                String wxsql3 = "SELECT wu.uid,wu.openid,info.name FROM tb_weixin_user wu inner join tb_visitor_visitorinfo info on info.uid=wu.docid where info.uid='" + visitorid + "' AND wu.status=1;";
//                JSONArray list = dbService.QueryList(wxsql3);
//                if (list.size() > 0) {
//                    String openid = list.getJSONObject(0).getString("openid");
//                    String name = list.getJSONObject(0).getString("name");
//                    String wxuserid = list.getJSONObject(0).getString("uid");
//                    // 审核成功以后推送微信消息给访客
//                    String topic = "/campus/weixintemplatemsg";
//                    RedisMessageEntity message = new RedisMessageEntity();
//                    JSONObject data = new JSONObject();
//                    data.put("msgid", UUID.randomUUID().toString());
//                    data.put("wxuserid", wxuserid);
//                    data.put("openid", openid);
//                    if (byagree == 1) {
//                        data.put("cfgcode", "OPENTM407908379");
//                        data.put("url", String.format("%s/visitor/visitorqrcode", WebConfig.getWeixinDomain()));
//                        JSONObject wxdata = new JSONObject();
//                        wxdata.put("first", new TemplateMessageData(String.format("%s，您提交的预约来访信息已审核通过！", name), "#4caf50")
//                                .toJSONString());
//                        wxdata.put("keyword1", new TemplateMessageData(name).toJSONString());
//                        wxdata.put("keyword2",
//                                new TemplateMessageData(sublist.getJSONObject(0).getString("byvisitor")).toJSONString());
//                        wxdata.put("keyword3", new TemplateMessageData(visittimestr).toJSONString());
//                        wxdata.put("keyword4", new TemplateMessageData(leavetimestr).toJSONString());
//                        wxdata.put("remark", new TemplateMessageData("请在预约时间内来访，点击查看访客二维码。", "#ce0404").toJSONString());
//                        data.put("wxtempdata", wxdata);
//                    } else {
//                        String keyword1 = "未通过";
//                        if (oldbyagree == 1) {
//                            keyword1 = "审核通过已撤销";
//                        }
//                        data.put("cfgcode", "OPENTM403107564");
//                        data.put("url", String.format("%s/visitor/subDetailVisit?uid=%s&time=%s", WebConfig.getWeixinDomain(), uid, System.currentTimeMillis()));
//                        JSONObject wxdata = new JSONObject();
//                        wxdata.put("first", new TemplateMessageData(String.format("%s，您提交的预约来访信息已审核完毕！", name)).toJSONString());
//                        wxdata.put("keyword1", new TemplateMessageData(keyword1, "#ce0404").toJSONString());
//                        wxdata.put("keyword2", new TemplateMessageData(remark).toJSONString());
//                        wxdata.put("keyword3", new TemplateMessageData(DateHelper.format(new Date())).toJSONString());
//                        wxdata.put("remark", new TemplateMessageData("谢绝来访，感谢您的使用!").toJSONString());
//                        data.put("wxtempdata", wxdata);
//                    }
//                    message.setCmd("weixintemplatemsg");
//                    message.setData(data);
//                    message.setDevtype(0);
//                    redispublish.Publish(topic, message);
//                }
//            }
//        }
//
//        return Json.getJsonResult(true);
//    }

    public JsonResult CreateQRcode(HttpServletRequest request, HttpServletResponse response) {
        String qrcode = "";
        if ("1".equals(syscfg.get("visitorqrcodeformat"))) {
            qrcode = RandomHelper.CreateQRcode();
        } else if ("2".equals(syscfg.get("visitorqrcodeformat"))) {
            qrcode = visitorRegisterService.createRandomCard();
        }
        return Json.getJsonResult(true, qrcode);
    }

    //推送预约消息给被访人
    public void pushMsgToInterviewee(String subuid, String reason, String visittime, String leavetime, String visitorname, String byvisitorid) {
        String sql3 = "SELECT wu.uid,wu.openid,info.name,info.email,info.mobile FROM tb_weixin_user wu inner join tb_card_teachstudinfo info on info.uid=wu.docid where info.uid='" + byvisitorid + "' AND wu.status=1;";
        JSONArray list = dbService.QueryList(sql3);
        if (list.size() > 0) {
            //推送微信消息
            String openid = list.getJSONObject(0).getString("openid");
            String wxuserid = list.getJSONObject(0).getString("uid");
            String topic = "/campus/weixintemplatemsg";
            RedisMessageEntity message = new RedisMessageEntity();
            JSONObject data = new JSONObject();
            data.put("msgid", UUID.randomUUID().toString());
            data.put("openid", openid);
            data.put("wxuserid", wxuserid);
            data.put("cfgcode", "OPENTM409889155");
            data.put("url", String.format("%s/visitor/authorizeVisitor?uid=%s&time=%s", WebConfig.getWeixinDomain(), subuid, System.currentTimeMillis()));
            JSONObject wxdata = new JSONObject();
            wxdata.put("first", new TemplateMessageData("您好！您有待审核访客预约信息，请审核").toJSONString());
            wxdata.put("keyword1", new TemplateMessageData(reason).toJSONString());
            wxdata.put("keyword2", new TemplateMessageData(visittime.replace("T", " ") + "到" + leavetime.replace("T", " ")).toJSONString());
            wxdata.put("keyword3", new TemplateMessageData(visitorname).toJSONString());
            wxdata.put("keyword4", new TemplateMessageData(DateHelper.format(new Date())).toJSONString());
            wxdata.put("remark", new TemplateMessageData("请尽快审核。").toJSONString());
            data.put("wxtempdata", wxdata);
            message.setCmd("weixintemplatemsg");
            message.setData(data);
            message.setDevtype(0);
            redispublish.Publish(topic, message);

            pushEmailAndSMSForManager(list);
        }
    }

    public void pushEmailAndSMSForManager(JSONArray list) {
        for (int i = 0; i < list.size(); i++) {
            //推送邮件
            final String email = list.getJSONObject(i).getString("email");
            if (StringUtils.isNotBlank(email) && StringUtils.isNotBlank(sysconfig.get("emailusername"))) {
                new Thread(new Runnable() {
                    public void run() {
                        Log.info(this.getClass(), "推送邮件");
                        SimpleMailMessage message = new SimpleMailMessage();
                        message.setTo(email);
                        message.setSubject("您好，您有一份邀请信息待审核");
                        message.setText("您好，您有一份邀请信息待审核，请及时处理审核。");
                        message.setFrom(sysconfig.get("emailusername"));
                        JavaMailSenderImpl jms = new JavaMailSenderImpl();
                        jms.setHost(sysconfig.get("emailhost"));
                        jms.setUsername(sysconfig.get("emailusername"));
                        jms.setPassword(sysconfig.get("emailpassword"));
                        jms.send(message);
                    }
                }).start();
            }

            //推送短信
            String mobile = list.getJSONObject(i).getString("mobile");
            if (StringUtils.isNotBlank(mobile) && StringUtils.isNotBlank(sysconfig.get("AliyunSmsSignName"))) {
                String smsTempSQL = "SELECT code FROM tb_sms_template WHERE name='访客预约待审核'";
                JSONArray tempja = dbService.QueryList(smsTempSQL);
                if (!tempja.isEmpty()) {
                    JSONObject cfg = sysconfig.get("AliyunAccessKeyId", "AliyunAccessSecret", "AliyunSmsSignName", "AliyunSmsTemplateCode");
                    AliyunSmsEntity smsentity = new AliyunSmsEntity();
                    smsentity.setAccessKeyId(cfg.getString("AliyunAccessKeyId"));
                    smsentity.setAccessSecret(cfg.getString("AliyunAccessSecret"));
                    smsentity.setPhoneNumbers(mobile);
                    smsentity.setSignName(cfg.getString("AliyunSmsSignName"));
                    String templateCode = tempja.getJSONObject(0).getString("code");
                    smsentity.setTemplateCode(templateCode);
                    smg.SenSms(smsentity);
                }
            }
        }
    }


    public JsonData ShowByVisitor(HttpServletRequest request, HttpServletResponse response, boolean viewchild, String orgcode, String key, int start, int limit) {
        String fields = "info.uid,info.code,info.name,info.sex,info.card,info.cardsn,info.orgcode,info.infotype,info.intoyear,info.focus,info.status, getorgname(info.orgcode) as orgname";
        String table = "tb_card_teachstudinfo info";
        if ("1".equals(sysconfig.get("designatedPerson"))) {
            table += " INNER JOIN (SELECT infoid from tb_visitor_administrators GROUP BY infoid) va ON va.infoid=info.uid ";
        }
        if (userredis.get(request).getUsertype() == 1) {
            String userid = userredis.getUserId(request);
            table += " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='" + userid + "' ";
        }
        String where = " info.status=1 ";
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and info.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and info.orgcode='" + orgcode + "' ";
            }
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (info.code='" + key + "' or info.name like '%" + key + "%' )";
        }
        String orderby = ExtSort.Orderby(request, "info.createdate desc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }


    public void invitedPushWeiXinMsg(int byagree, int oldbyagree, String visitorid, String subid) {

        if (byagree == 0 && oldbyagree == 0) {
            return;
        }

        String visitorSQL = "SELECT name FROM tb_visitor_visitorinfo WHERE uid=?";
        JSONArray visitorja = dbService.QueryList(visitorSQL, visitorid);
        if (!visitorja.isEmpty()) {
            String visitorname = visitorja.getJSONObject(0).getString("name");
            String subSQL = "SELECT vs.byvisitor,vs.byvisitorid,date_format(vs.visittime,'%Y-%m-%d %H:%i:%s') as visittime,ct.mobile FROM tb_visitor_subscribe vs LEFT JOIN tb_card_teachstudinfo ct ON ct.uid=vs.byvisitorid where vs.uid='" + subid + "';";
            JSONArray subja = dbService.QueryList(subSQL);
            if (!subja.isEmpty()) {
                String visittime = subja.getJSONObject(0).getString("visittime");
                String byvisitor = subja.getJSONObject(0).getString("byvisitor");
                String mobile = subja.getJSONObject(0).getString("mobile");
                String wxsql = "SELECT wu.uid,wu.openid FROM tb_weixin_user wu WHERE wu.status=1 AND docid IN ('" + String.join("','", visitorid) + "') ";
                JSONArray ja = dbService.QueryList(wxsql);
                if (!ja.isEmpty()) {
                    String openid = ja.getJSONObject(0).getString("openid");
                    String wxuserid = ja.getJSONObject(0).getString("uid");
                    String topic = "/campus/weixintemplatemsg";
                    RedisMessageEntity message = new RedisMessageEntity();
                    JSONObject data = new JSONObject();
                    JSONObject wxdata = new JSONObject();
                    String subject = "您好，您有一份邀请信息";
                    String text = "";
                    if (byagree == 0 && oldbyagree == 1) {
                        text = "非常抱歉，您的来访邀请已取消，详情可联系" + byvisitor + " " + mobile;
                    } else if (byagree == 1) {
                        text = "您好，我们邀请您于" + visittime + "来访我司，期待您的到来。";
                        if (StringUtils.isNotBlank(mobile)) {
                            text += "联系电话:" + mobile;
                        }
                    }
                    data.put("msgid", UUID.randomUUID().toString());
                    data.put("openid", openid);
                    data.put("wxuserid", wxuserid);
                    data.put("cfgcode", "OPENTM415037561");
                    wxdata.put("first", new TemplateMessageData(subject, "#4caf50").toJSONString());
                    wxdata.put("keyword1", new TemplateMessageData(visitorname).toJSONString());
                    wxdata.put("keyword2", new TemplateMessageData(visittime).toJSONString());
                    wxdata.put("keyword3", new TemplateMessageData(byvisitor).toJSONString());
                    wxdata.put("remark", new TemplateMessageData(text).toJSONString());
                    data.put("wxtempdata", wxdata);
                    message.setCmd("weixintemplatemsg");
                    message.setData(data);
                    message.setDevtype(0);
                    redispublish.Publish(topic, message);
                }
            }
        }
    }

    public void invitedPushEmail(int byagree, int oldbyagree, String visitorid, String subid) {

        if (byagree == 0 && oldbyagree == 0) {
            return;
        }

        String visitorSQL = "SELECT name,email FROM tb_visitor_visitorinfo WHERE uid=?";
        JSONArray visitorja = dbService.QueryList(visitorSQL, visitorid);
        final SysConfigService syscfg = sysconfig;
        if (!visitorja.isEmpty() && StringUtils.isNotBlank(visitorja.getJSONObject(0).getString("email")) && StringUtils.isNotBlank(syscfg.get("emailusername"))) {
            final String email = visitorja.getJSONObject(0).getString("email");
            String subSQL = "SELECT vs.byvisitor,vs.byvisitorid,date_format(vs.visittime,'%Y-%m-%d %H:%i:%s') as visittime,ct.mobile FROM tb_visitor_subscribe vs LEFT JOIN tb_card_teachstudinfo ct ON ct.uid=vs.byvisitorid where vs.uid='" + subid + "';";
            JSONArray subja = dbService.QueryList(subSQL);
            if (!subja.isEmpty()) {
                String visittime = subja.getJSONObject(0).getString("visittime").replace("T", " ");
                String byvisitor = subja.getJSONObject(0).getString("byvisitor");
                String mobile = subja.getJSONObject(0).getString("mobile");
                String text = "";
                if (byagree == 0 && oldbyagree == 1) {
                    text = "非常抱歉，您的来访邀请已取消，详情可联系" + byvisitor;
                    if (StringUtils.isNotBlank(mobile)) {
                        text += " 联系电话:" + mobile;
                    }
                } else if (byagree == 1) {
                    text = "您好，我们邀请您于" + visittime + "来访我司，期待您的到来。";
                    if (StringUtils.isNotBlank(mobile)) {
                        text += "联系电话:" + mobile;
                    }
                    JSONArray companyja = dbService.QueryList("SELECT name,address FROM tb_sys_license");
                    if (!companyja.isEmpty()) {
                        String name = companyja.getJSONObject(0).getString("name");
                        String address = companyja.getJSONObject(0).getString("address");
                        text += "\n公司：" + name;
                        text += "\n地址：" + address;
                    }
                }
                final String ftext = text;
                new Thread(new Runnable() {
                    public void run() {
                        if (StringUtils.isNotBlank(email)) {
                            SimpleMailMessage message = new SimpleMailMessage();
                            message.setTo(email);
                            message.setSubject("您好，您有一份邀请信息");
                            message.setText(ftext);
                            message.setFrom(syscfg.get("emailusername"));
                            JavaMailSenderImpl jms = new JavaMailSenderImpl();
                            jms.setHost(syscfg.get("emailhost"));
                            jms.setUsername(syscfg.get("emailusername"));
                            jms.setPassword(syscfg.get("emailpassword"));
                            jms.send(message);
                        }
                    }
                }).start();
            }
        }
    }

    public void invitedPushSMS(int byagree, int oldbyagree, String visitorid) {
        if (byagree == 0 && oldbyagree == 0) {
            return;
        }

        String visitorSQL = "SELECT name,mobile FROM tb_visitor_visitorinfo WHERE uid=?";
        JSONArray visitorja = dbService.QueryList(visitorSQL, visitorid);
        String mobile = visitorja.getJSONObject(0).getString("mobile");
        if (!visitorja.isEmpty() && StringUtils.isNotBlank(mobile)) {
            JSONObject cfg = sysconfig.get("AliyunAccessKeyId", "AliyunAccessSecret", "AliyunSmsSignName", "AliyunSmsTemplateCode");
            AliyunSmsEntity smsentity = new AliyunSmsEntity();
            smsentity.setAccessKeyId(cfg.getString("AliyunAccessKeyId"));
            smsentity.setAccessSecret(cfg.getString("AliyunAccessSecret"));
            smsentity.setPhoneNumbers(mobile);
            smsentity.setSignName(cfg.getString("AliyunSmsSignName"));
            if (byagree == 0 && oldbyagree == 1) {
                String smsTempSQL = "SELECT code FROM tb_sms_template WHERE name='访客预约失败通知'";
                JSONArray tempja = dbService.QueryList(smsTempSQL);
                if (!tempja.isEmpty()) {
                    String templateCode = tempja.getJSONObject(0).getString("code");
                    smsentity.setTemplateCode(templateCode);
                    smg.SenSms(smsentity);
                }
            } else if (byagree == 1) {
                String smsTempSQL = "SELECT code FROM tb_sms_template WHERE name='访客预约成功通知'";
                JSONArray tempja = dbService.QueryList(smsTempSQL);
                if (!tempja.isEmpty()) {
                    String templateCode = tempja.getJSONObject(0).getString("code");
                    smsentity.setTemplateCode(templateCode);
                    smg.SenSms(smsentity);
                }
            }
        }
    }

    /**
     * 申请访客预约
     */
    @Transactional
    public R<?> SaveVisitorSubscribe(HttpServletRequest request, HttpServletResponse response, String visitorId,
                                     String name, String sex, String idType, String idCard, String mobile, String cardSn, String qrcode,
                                     String company, String department, String position, String byVisitorId, String byVisitor,
                                     String otherVisitorVal, String plateNo, String things, String reason, String visitTime,
                                     String leaveTime, String areaPosition, String byVisitorCode) throws Exception {
        //判断是否是黑名单
        if (StringUtils.isNotBlank(visitorId)) {
            int count = dbService.getCount("tb_visitor_black", "idcard = '" + idCard + "'");
            if (count > 0) {
                return R.fail("该访客已被加入黑名单，无法预约");
            }
        }
        //主访客uid
        String visitorInfoId = UUID.randomUUID().toString();
        String birthday = "";
        if (idCard.length() > 15) {
            birthday = idCard.substring(7, 15);
        }
        String empId = userredis.get(request).getEmpid();
        int personNum = 1;
        List<String> otherVisitorList = new ArrayList<>();
        personNum = getPersonNumAndSaveInfo(name, sex, idType, idCard, mobile, qrcode, company, department, position, otherVisitorVal, plateNo, visitorInfoId, birthday, empId, personNum, otherVisitorList);

        //3.创建预约信息tb_visitor_subscribe
        String visitorFromId = UUID.randomUUID().toString();
        //判断是否默认审核通过
        String isDefaultAudit = sysconfig.get("visitormachineneedauth");
        String status = "0";
        if ("0".equals(isDefaultAudit)) {
            status="1";
        }
        //生成访客二维码（无卡号时按照十六进制）
        qrcode = RandomHelper.CreateQRcode();
        //设置过期时间
        String qrCodeTimeOut = sysconfig.get("qrcodetimeout");
        if (StringUtils.isBlank(qrCodeTimeOut)) {
            qrCodeTimeOut = "60";
        }
        if (StringUtils.isNotBlank(visitorId)) {
            visitorInfoId = visitorId;
        }
        String createVisitorSubscribe = "insert into tb_visitor_subscribe (uid,visitorid,byvisitor,byvisitorid,plateno,personnum,reason,things,visittime,leavetime,isinvited,"
                + "creatorid, createdate,status,isdefault,isleave)values(?,?,?,?,?,?,?,?,?,?,0,?,now(),?,?,0)";
        dbService.excuteSql(createVisitorSubscribe, visitorFromId, visitorInfoId, byVisitor, byVisitorId, plateNo, personNum, reason, things, visitTime, leaveTime, empId, status, isDefaultAudit);
        //4.创建其他访客信息
        String createOtherVisitor = "insert into tb_visitor_subscribe_person (uid,visitor_subscribe_id,visitor_id,create_time,qrcode,qrcodetimeout) values(uuid(),'" + visitorFromId + "',?,now(),?,date_add(now(), interval " + qrCodeTimeOut + " second))";
        List<Object[]> args = new ArrayList<>();
        args.add(new Object[]{visitorInfoId, qrcode});
        if (!otherVisitorList.isEmpty()) {
            for (String s : otherVisitorList) {
                //生成访客二维码（无卡号时按照十六进制）
                qrcode = RandomHelper.CreateQRcode();
                args.add(new Object[]{s, qrcode});
            }
        }
        jdbcTemplate.batchUpdate(createOtherVisitor, args);
        //判断是否需要审核
        if ("1".equals(syscfg.get("visitormachineneedauth"))) {
            //获取工作流模板为请假的模板id
            String sqlMouldId = "SELECT uid FROM tb_workflow_mould WHERE mouldtype = 3 ";
            JSONArray ja = dbService.QueryList(sqlMouldId);
            String mouldId = ja.getJSONObject(0).getString("uid");

            if (ja.isEmpty()) {
                throw new RuntimeException("未配置审批模板");
            }

            List<WorkFlowNodeDto> workFlowNodeDTOs = workFlowUtil.selSubPerson(mouldId, visitorInfoId, byVisitorId, byVisitorCode, "", 1);
            if (workFlowNodeDTOs.size() == 0) {
                throw new RuntimeException("未配置审核人，请联系管理员！");
            }
            //判断是否是审核人提交默认审核通过
            WorkFlowNodeDto nodeLevel = workFlowUtil.getNodeLevel(request, empId, 3);
            if (nodeLevel != null) {
                String updateSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate,auditdate,auditid,remark) values (uuid(),?,?,?,?,now(),now(),?,?)";
                dbService.excuteSql(updateSql, visitorFromId, mouldId, 4, nodeLevel.getNodeLevel(), empId, "审核人提交默认审核通过");
                if (nodeLevel.getIsFinal() == 1) {
                    String updateVisitorSubscribe = "update tb_visitor_subscribe set status = 1 where uid = ?";
                    dbService.excuteSql(updateVisitorSubscribe, visitorFromId);
                    return R.ok(true);
                }
            }
            String insertRecordSql = "insert into tb_workflow_approval_record (uid,formid,mouldid,auditcondition,node_level,createdate) values (uuid(),?,?,?,1,now())";
            dbService.excuteSql(insertRecordSql, visitorFromId, mouldId, 0);
            //异步处理发送邮件和短信
            CompletableFuture.runAsync(() -> {
                handler.pushEmailAndSMSForManager(visitorFromId, workFlowNodeDTOs);
            });
        }
        return R.ok(true);
    }

    private Integer getPersonNumAndSaveInfo(String name, String sex, String idType, String idCard, String mobile, String qrcode, String company, String department, String position, String otherVisitorVal, String plateNo, String visitorInfoId, String birthday, String empId, int personNum, List<String> otherVisitorList) {
        try {
            //判断是否有其他访客
            if (StringUtils.isNotBlank(otherVisitorVal)) {
                JSONArray otherVisitorArr = JSONArray.parseArray(otherVisitorVal);
                personNum += otherVisitorArr.size();
                if (!otherVisitorArr.isEmpty()) {
                    for (int i = 0; i < otherVisitorArr.size(); i++) {
                        //处理其他访客的身份证信息，并将该信息处理结果存入访客表中
                        JSONObject otherVisitors = otherVisitorArr.getJSONObject(i);
                        String otherName = otherVisitors.getString("name");
                        String otherIdCard = otherVisitors.getString("idcard");
                        String otherUid = UUID.randomUUID().toString();
                        String otherBirthday = "";
                        String otherSex = "男";
                        if (idCard.length() > 15) {
                            otherBirthday = otherIdCard.substring(6, 14);
                            int sexNum = Integer.parseInt(otherIdCard.substring(16, 17));
                            if (sexNum % 2 == 0) {
                                otherSex = "女";
                            } else {
                                otherSex = "男";
                            }
                        }
                        String selVisitorSQL = "SELECT uid FROM tb_visitor_visitorinfo WHERE idcard='" + otherIdCard + "'";
                        String otherVisitor = dbService.queryOneField(selVisitorSQL, String.class);
                        if (StringUtils.isNotBlank(otherVisitor)) {
                            otherVisitorList.add(otherVisitor);
                        } else {
                            String createOtherVisitor = "insert into tb_visitor_visitorinfo (uid,name,idtype,idcard,sex,birthday,"
                                    + "company,position,department,createdate,creatorid,status,plateno) values(?,?,?,?,?,?,?,?,?,now(),?,1,?)";
                            dbService.excuteSql(createOtherVisitor, otherUid, otherName, idType, otherIdCard, otherSex, otherBirthday, company, position, department, empId, plateNo);
                            otherVisitorList.add(otherUid);
                        }
                    }
                }
            }
            //1. 如果visitorid（访客uid）为空，先创建访客信息
            String sql = "SELECT uid FROM tb_visitor_visitorinfo WHERE idcard='" + idCard + "'";
            String uid = dbService.queryOneField(sql, String.class);

            if (StringUtils.isNotBlank(uid)) {
                String alterSQL = "UPDATE tb_visitor_visitorinfo SET name=?, idtype=?, idcard=?, sex=?, mobile=?, company=?, position=?, department=? WHERE uid = ?";
                dbService.excuteSql(alterSQL, name, idType, idCard, sex, mobile, company, position, department, uid);
            } else {
                String createVisitor = "insert into tb_visitor_visitorinfo (uid,name,idtype,idcard,sex,birthday,mobile,"
                        + "company,position,department,createdate,creatorid,status,plateno)values(?,?,?,?,?,?,?,?,?,?,now(),?,1,?)";
                dbService.excuteSql(createVisitor, visitorInfoId, name, idType, idCard, sex, birthday, mobile, company, position, department, empId, plateNo);
            }
            return personNum;
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return personNum;
    }

    /**
     * 审核访客
     */
    public JsonResult auditSubscribe(HttpServletRequest request, HttpServletResponse response, String uid,
                               String visitorId, String cardSn, String qrcode, int auditStatus, String remark,
                               String visitTime, String leaveTime, String areaPosition, String things, String reason) throws Exception {
        String subSQL = "SELECT byvisitor,byvisitorid,isinvited,date_format(visittime,'%Y-%m-%d %H:%i:%s') as visittime FROM tb_visitor_subscribe where uid='" + uid + "';";
        JSONArray sublist = dbService.QueryList(subSQL);
        if (sublist.size() == 0) {
            return Json.getJsonResult(false, "未找到该预约信息");
        }
        //更新通行区域
        String updateArea = "UPDATE tb_visitor_subscribe SET areaposition=? WHERE uid=?";
        dbService.excuteSql(updateArea, areaPosition, uid);
        //审核
        return workFlowUtil.submitForApproval(request, uid, auditStatus, remark, "tb_visitor_subscribe", "", areaPosition);
    }

    /**
     * 获取预约成功的访客信息
     */
    public JsonData getSubscribeVisitorSuccess(String formId) {
        String sql = "select vsp.card_no as cardno ,vsp.qrcode,vv.idcard,vv.name from tb_visitor_subscribe_person vsp " +
                "join tb_visitor_visitorinfo vv on vv.uid = vsp.visitor_id" +
                " where vsp.visitor_subscribe_id= '" + formId + "'";
        return dbService.QueryJsonData(sql);

    }


}
