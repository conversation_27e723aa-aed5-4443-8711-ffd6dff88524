package com.ymiots.campusos.service.waterctrl;

import com.ymiots.campusos.common.BaseService;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 设备费用缴费服务
 * 基于现有的 tb_equipment_cost_summary 表实现缴费功能
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class EquipmentPaymentService extends BaseService {

    /**
     * 处理缴费
     * @param summaryId 费用汇总ID
     * @param paymentAmount 缴费金额
     * @param paymentMethod 缴费方式
     * @param paymentPerson 缴费人
     * @param cashierName 收费员
     * @param remark 备注
     * @return 缴费结果
     */
    public JsonResult processPayment(String summaryId, BigDecimal paymentAmount, String paymentMethod, 
                                   String paymentPerson, String cashierName, String remark) {
        try {
            // 1. 验证费用汇总记录
            Map<String, Object> summary = getCostSummary(summaryId);
            if (summary == null) {
                return Json.getJsonResult(false, "费用汇总记录不存在");
            }

            BigDecimal unpaidAmount = (BigDecimal) summary.get("unpaid_amount");
            if (unpaidAmount == null) unpaidAmount = BigDecimal.ZERO;

            // 2. 验证缴费金额
            if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return Json.getJsonResult(false, "缴费金额必须大于0");
            }

            if (paymentAmount.compareTo(unpaidAmount) > 0) {
                return Json.getJsonResult(false, 
                    String.format("缴费金额不能超过未缴费金额 ￥%.2f", unpaidAmount));
            }

            // 3. 生成缴费记录
            String paymentNo = generatePaymentNo();
            String receiptNo = generateReceiptNo();

            // 4. 保存缴费记录
            JsonResult saveResult = savePaymentRecord(summary, paymentAmount, paymentMethod, 
                paymentPerson, cashierName, paymentNo, receiptNo, remark);
            
            if (!saveResult.isSuccess()) {
                return saveResult;
            }

            // 5. 保存缴费明细
            savePaymentDetails(saveResult.getData().toString(), summary, paymentAmount);

            Log.info(EquipmentPaymentService.class, 
                String.format("缴费处理成功 - 区域: %s, 月份: %s, 金额: ￥%.2f, 方式: %s", 
                    summary.get("area_code"), summary.get("summary_month"), paymentAmount, paymentMethod));

            Map<String, Object> result = new HashMap<>();
            result.put("paymentNo", paymentNo);
            result.put("receiptNo", receiptNo);
            result.put("paymentAmount", paymentAmount);
            result.put("paymentMethod", paymentMethod);

            return Json.getJsonResult(true, "缴费成功", result);

        } catch (Exception e) {
            Log.error(EquipmentPaymentService.class, 
                String.format("处理缴费失败: %s", e.getMessage()), e);
            return Json.getJsonResult(false, "缴费处理失败: " + e.getMessage());
        }
    }

    /**
     * 查询缴费记录
     * @param areaCode 区域编码
     * @param summaryMonth 汇总月份
     * @param start 开始位置
     * @param limit 限制数量
     * @return 缴费记录列表
     */
    public Map<String, Object> getPaymentRecords(String areaCode, String summaryMonth, int start, int limit) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT pr.uid, pr.payment_no, pr.area_code, pr.summary_month, ");
            sql.append("pr.payment_amount, pr.payment_method, pr.payment_date, pr.payment_person, ");
            sql.append("pr.cashier_name, pr.receipt_no, pr.remark, ");
            sql.append("s.total_cost, s.paid_amount, s.unpaid_amount ");
            sql.append("FROM tb_equipment_payment_record pr ");
            sql.append("LEFT JOIN tb_equipment_cost_summary s ON pr.summary_id = s.uid ");
            sql.append("WHERE pr.status = 1 AND pr.payment_status = 1 ");

            List<Object> params = new ArrayList<>();
            if (areaCode != null && !areaCode.trim().isEmpty()) {
                sql.append("AND pr.area_code = ? ");
                params.add(areaCode);
            }
            if (summaryMonth != null && !summaryMonth.trim().isEmpty()) {
                sql.append("AND pr.summary_month = ? ");
                params.add(summaryMonth);
            }

            sql.append("ORDER BY pr.payment_date DESC ");

            // 查询总数
            String countSql = sql.toString().replaceFirst("SELECT.*FROM", "SELECT COUNT(*) FROM");
            int total = dbService.getCount(countSql, params.toArray());

            // 查询数据
            sql.append("LIMIT ?, ? ");
            params.add(start);
            params.add(limit);

            List<Map<String, Object>> records = dbService.queryList(sql.toString(), params.toArray());

            Map<String, Object> result = new HashMap<>();
            result.put("total", total);
            result.put("records", records);

            return result;

        } catch (Exception e) {
            Log.error(EquipmentPaymentService.class, 
                String.format("查询缴费记录失败: %s", e.getMessage()), e);
            return new HashMap<>();
        }
    }

    /**
     * 查询欠费汇总
     * @param areaCode 区域编码
     * @return 欠费汇总列表
     */
    public List<Map<String, Object>> getArrearsSummary(String areaCode) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT area_code, summary_month, total_cost, paid_amount, unpaid_amount, ");
            sql.append("payment_status, due_date, overdue_days, payment_progress ");
            sql.append("FROM v_equipment_cost_payment_summary ");
            sql.append("WHERE payment_status < 2 AND unpaid_amount > 0 ");

            List<Object> params = new ArrayList<>();
            if (areaCode != null && !areaCode.trim().isEmpty()) {
                sql.append("AND area_code = ? ");
                params.add(areaCode);
            }

            sql.append("ORDER BY overdue_days DESC, unpaid_amount DESC");

            return dbService.queryList(sql.toString(), params.toArray());

        } catch (Exception e) {
            Log.error(EquipmentPaymentService.class, 
                String.format("查询欠费汇总失败: %s", e.getMessage()), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询收费统计
     * @param summaryMonth 汇总月份
     * @return 收费统计
     */
    public Map<String, Object> getPaymentStatistics(String summaryMonth) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT summary_month, COUNT(*) AS total_areas, ");
            sql.append("SUM(total_cost) AS total_amount, SUM(paid_amount) AS paid_amount, ");
            sql.append("SUM(unpaid_amount) AS unpaid_amount, ");
            sql.append("SUM(CASE WHEN payment_status = 2 THEN 1 ELSE 0 END) AS paid_count, ");
            sql.append("SUM(CASE WHEN payment_status < 2 THEN 1 ELSE 0 END) AS unpaid_count, ");
            sql.append("ROUND(SUM(paid_amount) / SUM(total_cost) * 100, 2) AS payment_rate ");
            sql.append("FROM tb_equipment_cost_summary WHERE status = 1 AND total_cost > 0 ");

            List<Object> params = new ArrayList<>();
            if (summaryMonth != null && !summaryMonth.trim().isEmpty()) {
                sql.append("AND summary_month = ? ");
                params.add(summaryMonth);
            }

            sql.append("GROUP BY summary_month ORDER BY summary_month DESC");

            List<Map<String, Object>> results = dbService.queryList(sql.toString(), params.toArray());
            return results.isEmpty() ? new HashMap<>() : results.get(0);

        } catch (Exception e) {
            Log.error(EquipmentPaymentService.class, 
                String.format("查询收费统计失败: %s", e.getMessage()), e);
            return new HashMap<>();
        }
    }

    /**
     * 查询费用汇总列表（含缴费状态）
     * @param areaCode 区域编码
     * @param summaryMonth 汇总月份
     * @param start 开始位置
     * @param limit 限制数量
     * @return 费用汇总列表
     */
    public Map<String, Object> getCostSummaryList(String areaCode, String summaryMonth, int start, int limit) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT * FROM v_equipment_cost_payment_summary WHERE 1=1 ");

            List<Object> params = new ArrayList<>();
            if (areaCode != null && !areaCode.trim().isEmpty()) {
                sql.append("AND area_code = ? ");
                params.add(areaCode);
            }
            if (summaryMonth != null && !summaryMonth.trim().isEmpty()) {
                sql.append("AND summary_month = ? ");
                params.add(summaryMonth);
            }

            sql.append("ORDER BY summary_month DESC, area_code ");

            // 查询总数
            String countSql = sql.toString().replaceFirst("SELECT.*FROM", "SELECT COUNT(*) FROM");
            int total = dbService.getCount(countSql, params.toArray());

            // 查询数据
            sql.append("LIMIT ?, ? ");
            params.add(start);
            params.add(limit);

            List<Map<String, Object>> records = dbService.queryList(sql.toString(), params.toArray());

            Map<String, Object> result = new HashMap<>();
            result.put("total", total);
            result.put("records", records);

            return result;

        } catch (Exception e) {
            Log.error(EquipmentPaymentService.class, 
                String.format("查询费用汇总列表失败: %s", e.getMessage()), e);
            return new HashMap<>();
        }
    }

    /**
     * 获取费用汇总记录
     */
    private Map<String, Object> getCostSummary(String summaryId) {
        try {
            String sql = "SELECT uid, area_code, summary_month, water_total_usage, water_total_cost, " +
                        "electric_total_usage, electric_total_cost, hot_water_total_usage, hot_water_total_cost, " +
                        "gas_total_usage, gas_total_cost, total_cost, paid_amount, unpaid_amount, " +
                        "payment_status, due_date, last_payment_date, payment_count " +
                        "FROM tb_equipment_cost_summary WHERE uid = ? AND status = 1";

            List<Map<String, Object>> results = dbService.queryList(sql, summaryId);
            return results.isEmpty() ? null : results.get(0);

        } catch (Exception e) {
            Log.error(EquipmentPaymentService.class, 
                String.format("获取费用汇总记录失败: %s", e.getMessage()), e);
            return null;
        }
    }

    /**
     * 保存缴费记录
     */
    private JsonResult savePaymentRecord(Map<String, Object> summary, BigDecimal paymentAmount, 
                                       String paymentMethod, String paymentPerson, String cashierName,
                                       String paymentNo, String receiptNo, String remark) {
        try {
            String insertSql = "INSERT INTO tb_equipment_payment_record " +
                              "(uid, payment_no, summary_id, area_code, summary_month, payment_amount, " +
                              "payment_method, payment_date, payment_person, cashier_name, receipt_no, " +
                              "remark, status, create_date, creator_id) " +
                              "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)";

            String uid = UUID.randomUUID().toString();
            Date currentDate = new Date();

            int result = dbService.excuteSql(insertSql,
                uid, paymentNo, summary.get("uid"), summary.get("area_code"), summary.get("summary_month"),
                paymentAmount, paymentMethod, currentDate, paymentPerson, cashierName,
                receiptNo, remark, currentDate, "system");

            if (result > 0) {
                return Json.getJsonResult(true, "缴费记录保存成功", uid);
            } else {
                return Json.getJsonResult(false, "缴费记录保存失败");
            }

        } catch (Exception e) {
            Log.error(EquipmentPaymentService.class, 
                String.format("保存缴费记录失败: %s", e.getMessage()), e);
            return Json.getJsonResult(false, "保存缴费记录失败: " + e.getMessage());
        }
    }

    /**
     * 保存缴费明细
     */
    private void savePaymentDetails(String paymentId, Map<String, Object> summary, BigDecimal paymentAmount) {
        try {
            // 按比例分配缴费金额到各类费用
            BigDecimal totalCost = (BigDecimal) summary.get("total_cost");
            if (totalCost.compareTo(BigDecimal.ZERO) <= 0) return;

            // 电费明细
            BigDecimal electricCost = (BigDecimal) summary.get("electric_total_cost");
            if (electricCost != null && electricCost.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal electricPayment = paymentAmount.multiply(electricCost).divide(totalCost, 2, BigDecimal.ROUND_HALF_UP);
                savePaymentDetail(paymentId, summary, "electric", "电费", 
                    (BigDecimal) summary.get("electric_total_usage"), electricCost, electricPayment);
            }

            // 水费明细
            BigDecimal waterCost = (BigDecimal) summary.get("water_total_cost");
            if (waterCost != null && waterCost.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal waterPayment = paymentAmount.multiply(waterCost).divide(totalCost, 2, BigDecimal.ROUND_HALF_UP);
                savePaymentDetail(paymentId, summary, "water", "水费", 
                    (BigDecimal) summary.get("water_total_usage"), waterCost, waterPayment);
            }

            // 热水费明细
            BigDecimal hotWaterCost = (BigDecimal) summary.get("hot_water_total_cost");
            if (hotWaterCost != null && hotWaterCost.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal hotWaterPayment = paymentAmount.multiply(hotWaterCost).divide(totalCost, 2, BigDecimal.ROUND_HALF_UP);
                savePaymentDetail(paymentId, summary, "hot_water", "热水费", 
                    (BigDecimal) summary.get("hot_water_total_usage"), hotWaterCost, hotWaterPayment);
            }

            // 燃气费明细
            BigDecimal gasCost = (BigDecimal) summary.get("gas_total_cost");
            if (gasCost != null && gasCost.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal gasPayment = paymentAmount.multiply(gasCost).divide(totalCost, 2, BigDecimal.ROUND_HALF_UP);
                savePaymentDetail(paymentId, summary, "gas", "燃气费", 
                    (BigDecimal) summary.get("gas_total_usage"), gasCost, gasPayment);
            }

        } catch (Exception e) {
            Log.error(EquipmentPaymentService.class, 
                String.format("保存缴费明细失败: %s", e.getMessage()), e);
        }
    }

    /**
     * 保存单项缴费明细
     */
    private void savePaymentDetail(String paymentId, Map<String, Object> summary, String feeType, String feeName,
                                 BigDecimal totalUsage, BigDecimal totalAmount, BigDecimal paymentAmount) {
        try {
            String insertSql = "INSERT INTO tb_equipment_payment_detail " +
                              "(uid, payment_id, summary_id, area_code, summary_month, fee_type, fee_name, " +
                              "total_usage, total_amount, payment_amount, status, create_date, creator_id) " +
                              "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)";

            dbService.excuteSql(insertSql,
                UUID.randomUUID().toString(), paymentId, summary.get("uid"), 
                summary.get("area_code"), summary.get("summary_month"),
                feeType, feeName, totalUsage, totalAmount, paymentAmount,
                new Date(), "system");

        } catch (Exception e) {
            Log.error(EquipmentPaymentService.class, 
                String.format("保存缴费明细失败 - 费用类型: %s", feeType), e);
        }
    }

    /**
     * 生成缴费单号
     */
    private String generatePaymentNo() {
        return "PAY" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + 
               String.format("%03d", (int)(Math.random() * 1000));
    }

    /**
     * 生成收据号
     */
    private String generateReceiptNo() {
        return "REC" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + 
               String.format("%03d", (int)(Math.random() * 1000));
    }
}
