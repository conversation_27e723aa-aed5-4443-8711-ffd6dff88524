package com.ymiots.campusos.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymiots.campusos.redis.RedisPublish;
import com.ymiots.campusos.util.corn.CronUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.websocket.WebSocketSets;

@Repository
public class SysTaskService extends BaseService{

	@Autowired
	@Lazy
	UserRedis useredis;
	
	@Autowired(required=true)
	private SysDynamicTaskService taskservice;
	
	@Autowired
	RedisPublish redispublish;

	
	
	public JsonData getSysTask(HttpServletRequest request, HttpServletResponse response,String tasktype, boolean autorun, boolean runing, String key, int start,int limit) {
		 String where="1=1 ";
		 SysUser u=useredis.get(request);
		 if(u.getUsertype()!=9) {
			 where=" s.status>=0";
		 }else {
			 if(autorun) {
				 where+=" and s.status=1 ";
			 }
			 if(runing) {
				 where+=" and s.runstatus=1 ";
			 } 
		 }
		 
		 
		 if(!StringUtils.isBlank(tasktype)) {
			 where+=" and s.tasktype='"+tasktype+"' ";
		 }
		 if(!StringUtils.isBlank(key)) {
			 where+=" and (s.name like '%"+key+"%' or s.code like '%"+key+"%') ";
		 }
		 String fields="s.uid,s.name,s.code,s.taskclass,s.cron,s.tasktype,s.remark,s.status,s.sqlcode,s.lastexetime,s.exeremark,d.name as tasktypename,s.executetype,s.runstatus,s.maxlog,s.serverid";
		 String table="tb_sys_schedule s inner join tb_sys_dictionary d on d.code=s.tasktype and d.groupcode='SYS0000039'";
		 JsonData result=dbService.QueryJsonData(fields, table, where, "createdate desc", start, limit);
		 return result;
	}
	
	public JsonData getSysTaskLog(HttpServletRequest request, HttpServletResponse response,String scheduleid) {
		String sql="SELECT * FROM tb_sys_schedule_detail where scheduleid='"+scheduleid+"' order by runtime desc;";
		sql+="";
		JsonData result= dbService.QueryJsonData(sql);
		return result;
	}

	public JsonResult SaveSysTask(HttpServletRequest request, HttpServletResponse response, String times, String starttime, String minutetime, String hourtime, String uid, String code, String taskclass, String name, String tasktype, String cron, String sqlcode, String remark, String status, String executetype, String runstatus, String maxlog, String serverid) throws Exception {
		if (StringUtils.isNotBlank(times)) {
			if (Integer.parseInt(times) == -1) {
				starttime = "0000-00-00 00:" + minutetime + ":00";
			} else if (Integer.parseInt(times) == 0) {
				starttime = "0000-00-00 " + hourtime + ":00:00";
			}
			if (StringUtils.isNotBlank(times)) {
				String[] split = CronUtil.INSTANCE.generateCronByPeriodAndTime(Integer.parseInt(times), starttime).split("-");
				cron = split[0];
				remark = split[1];
			}
		}
		String userid = useredis.getUserId(request);
		if (StringUtils.isBlank(uid)) {
			String sql = "INSERT INTO tb_sys_schedule(uid,name,code,taskclass,cron,tasktype,sqlcode,remark,executetype,runstatus,maxlog,serverid,createdate,creatorid,status) values(uuid(),?,?,?,?,?,?,?,?,0,?,?,now(),?,?)";
			dbService.excuteSql(sql, name, code, taskclass, cron, tasktype, sqlcode, remark, executetype, maxlog, serverid, userid, status);
		} else {
			String sql = "UPDATE tb_sys_schedule SET name=?,code=?,taskclass=?,cron=?,tasktype=?,sqlcode=?,remark=?,executetype=?,maxlog=?,serverid=?,modifydate=now(),modifierid=?,status=? where uid=?";
			dbService.excuteSql(sql, name, code, taskclass, cron, tasktype, sqlcode, remark, executetype, maxlog, serverid, userid, status, uid);
		}
		return Json.getJsonResult(true);
	}
	
	public JsonResult DelSysTask(HttpServletRequest request, HttpServletResponse response,String uid) throws Exception {
		SysUser u=useredis.get(request);
		if(u.getUsertype()!=9) {
			return Json.getJsonResult(false,"无权操作");
		}
		String sql="SELECT name,code,cron,taskclass,sqlcode FROM tb_sys_schedule where uid='"+uid+"' ";
		JSONArray list=dbService.QueryList(sql);
		if(list.size()==0) {
			return Json.getJsonResult(false,"计划任务配置不存在");
		}
		String code=list.getJSONObject(0).getString("code");
		String taskclass=list.getJSONObject(0).getString("taskclass");
		taskservice.CancelTask(code,taskclass);
		sql="delete from tb_sys_schedule where uid='"+uid+"' ";
		dbService.excuteSql(sql);
		return Json.getJsonResult(true);
	}
	
	public JsonResult StartSysTask(HttpServletRequest request, HttpServletResponse response,String uids) throws Exception {
		String sql="SELECT name,code,cron,taskclass,sqlcode,serverid FROM tb_sys_schedule where find_in_set(uid,'"+uids+"')>0 ";
		JSONArray list=dbService.QueryList(sql);
		for(int i=0;i<list.size();i++) {
			JSONObject item=list.getJSONObject(i);
			String code=item.getString("code");
			String cron=item.getString("cron");
			String taskclass=item.getString("taskclass");
			String sqlcode=item.getString("sqlcode");
			if(item.getString("serverid").equals("os"+WebConfig.getServerid())) {
				JsonResult result= taskservice.StartTask(code, taskclass, cron, sqlcode);
				if(!result.isSuccess()) {
					UpdateSysTaskStatusByCode(code,"0",result.getMsg());
				}else {
					UpdateSysTaskStatusByCode(code,"1",result.getMsg());
				}
			}else {
				JSONObject msg=new JSONObject();
				msg.put("code", "1");
				msg.put("node", item.getString("serverid"));
				JSONObject data=new JSONObject();
				data.put("code", code);
				data.put("taskclass", taskclass);
				data.put("cron", cron);
				data.put("sqlcode", sqlcode);
				msg.put("data", data);
				redispublish.Publish("/service/servernodestatus", msg);
			}
		}
		return Json.getJsonResult(true);
	}
	
	public JsonResult StopSysTask(HttpServletRequest request, HttpServletResponse response,String uids) throws Exception {
		String sql="SELECT name,code,taskclass,sqlcode,serverid FROM tb_sys_schedule where find_in_set(uid,'"+uids+"')>0 ";
		JSONArray list=dbService.QueryList(sql);
		for(int i=0;i<list.size();i++) {
			JSONObject item=list.getJSONObject(i);
			String code=item.getString("code");
			String taskclass=item.getString("taskclass");
			if(item.getString("serverid").equals("os"+WebConfig.getServerid())) {
				JsonResult result= taskservice.CancelTask(code,taskclass);
				if(!result.isSuccess()) {
					UpdateSysTaskStatusByCode(code,"0",result.getMsg());
				}else {
					UpdateSysTaskStatusByCode(code,"0", result.getMsg());
				}
			}else {
				JSONObject msg=new JSONObject();
				msg.put("code", "2");
				msg.put("node", item.getString("serverid"));
				JSONObject data=new JSONObject();
				data.put("code", code);
				data.put("taskclass", taskclass);
				msg.put("data", data);
				redispublish.Publish("/service/servernodestatus", msg);
			}
		}
		return Json.getJsonResult(true);
	}
	
	
	public JsonResult RefreshCron(HttpServletRequest request, HttpServletResponse response,String uids) throws Exception {
		String sql="SELECT name,code,cron,taskclass,sqlcode,serverid FROM tb_sys_schedule where find_in_set(uid,'"+uids+"')>0 ";
		JSONArray list=dbService.QueryList(sql);
		for(int i=0;i<list.size();i++) {
			JSONObject item=list.getJSONObject(i);
			String code=item.getString("code");
			String cron=item.getString("cron");
			String taskclass=item.getString("taskclass");
			String sqlcode=item.getString("sqlcode");
			if(item.getString("serverid").equals("os"+WebConfig.getServerid())) {
				JsonResult result= taskservice.ChangeCron(code, taskclass, cron, sqlcode);
				if(!result.isSuccess()) {
					UpdateSysTaskStatusByCode(code,"",result.getMsg());
				}else {
					UpdateSysTaskStatusByCode(code,"", "刷新cron并重启成功");
				}
			}else {
				JSONObject msg=new JSONObject();
				msg.put("code", "4");
				msg.put("node", item.getString("serverid"));
				JSONObject data=new JSONObject();
				data.put("code", code);
				data.put("taskclass", taskclass);
				data.put("cron", cron);
				data.put("sqlcode", sqlcode);
				msg.put("data", data);
				redispublish.Publish("/service/servernodestatus", msg);
			}
		}
		return Json.getJsonResult(true);
	}
	
	public JsonResult DisAllowRun(HttpServletRequest request, HttpServletResponse response,String uids) {
		String sql="update tb_sys_schedule set status=-1 where find_in_set(uid,'"+uids+"')>0";
		dbService.excuteSql(sql);
		sql="SELECT uid,name,code,cron,taskclass,sqlcode,serverid FROM tb_sys_schedule where find_in_set(uid,'"+uids+"')>0 ";
		JSONArray list=dbService.QueryList(sql);
		for(int i=0;i<list.size();i++) {
			JSONObject item=list.getJSONObject(i);
			String uid=item.getString("uid");
			String name=item.getString("name");
			String code=item.getString("code");
			String taskclass=item.getString("taskclass");
			if(item.getString("serverid").equals("os"+WebConfig.getServerid())) {
				JsonResult rs=taskservice.CancelTask(code, taskclass);
				if(rs.isSuccess()) {
					sql="update tb_sys_schedule set runstatus=0 where uid='"+uid+"' ";
					dbService.excuteSql(sql);
					Log.info(SysTaskService.class, String.format("停止任务[%s]成功", name));
				}
				else {
					Log.error(SysTaskService.class, String.format("停止任务[%s]失败，错误：%s", name,rs.getMsg()));
				}
			}else {
				JSONObject msg=new JSONObject();
				msg.put("code", "5");
				msg.put("node", item.getString("serverid"));
				JSONObject data=new JSONObject();
				data.put("uid", uid);
				data.put("code", code);
				data.put("name", name);
				data.put("taskclass", taskclass);
				msg.put("data", data);
				redispublish.Publish("/service/servernodestatus", msg);
			}
		}
		return Json.getJsonResult(true);
	}
	
	public JsonResult AllowRun(HttpServletRequest request, HttpServletResponse response,String uids) {
		String sql="update tb_sys_schedule set status=1 where find_in_set(uid,'"+uids+"')>0";
		dbService.excuteSql(sql);
		return Json.getJsonResult(true);
	}

	public int getSysCanRunTaskCount() {
		int count=dbService.getCount("tb_sys_schedule", "serverid='os"+ WebConfig.getServerid()+"'");
		return count;
	}
	
	public JSONArray getSysCanRunTask() {
		String sql="SELECT uid,name,code,cron,taskclass,sqlcode FROM tb_sys_schedule where status=1 AND serverid='os"+ WebConfig.getServerid()+"'";
		JSONArray list=dbService.QueryList(sql);
		return list;
	}
	
	public void UpdateSysTaskStatus(String[] uids) {
		String sql="update tb_sys_schedule set runstatus=1 where find_in_set(uid,'"+String.join(",", uids)+"')>0";
		dbService.excuteSql(sql);
	}
	
	public void UpdateSysTaskStatus(String uid,String runstatus, String exeremark) {
		String sql="update tb_sys_schedule set runstatus="+runstatus+", exeremark='"+exeremark+"' where uid='"+uid+"' ";
		dbService.excuteSql(sql);
	}
	
	public void UpdateSysTaskStatusByCode(String code,String runstatus, String exeremark) {
		StringBuffer sb=new StringBuffer();
		sb.append("update tb_sys_schedule set");
		if(!StringUtils.isBlank(runstatus)) {
			sb.append(" runstatus="+runstatus+",");
		}
		sb.append(" exeremark='"+exeremark+"' where code='"+code+"' ");
		dbService.excuteSql(sb.toString());
	}


	public void UpdateSysTaskLastExeTime(String code, String exeremark, long startTimeMillis) {
		try {
			long endTimeMillis=System.currentTimeMillis();
			long takeuptime=endTimeMillis-startTimeMillis;
			JSONArray list=dbService.QueryList("select uid,maxlog from tb_sys_schedule where code='"+code+"' ");
			if(list.size()==0) {
				return;
			}
			JSONObject item=list.getJSONObject(0);
			String uid=item.getString("uid");
			int maxlog=item.getIntValue("maxlog");
			String sql="update tb_sys_schedule set lastexetime=now(), exeremark=? where uid=? ";
			dbService.excuteSql(sql,exeremark.replace("'",""),uid);
			if(maxlog>0) {
				sql="insert into tb_sys_schedule_detail(uid,scheduleid,runtime,takeuptime,remark) values(uuid(),'"+uid+"',now(),"+String.valueOf(takeuptime)+",'"+exeremark.replace("'","")+"');";
				dbService.excuteSql(sql);
				int detailcount=dbService.getCount("tb_sys_schedule_detail", "scheduleid='"+uid+"'");
				if(detailcount>50) {
					sql="delete d from tb_sys_schedule_detail d inner join (select min(n.runtime) as runtime from (SELECT runtime FROM tb_sys_schedule_detail where scheduleid='"+uid+"' order by runtime desc limit "+String.valueOf(maxlog)+") n) t on d.runtime<t.runtime and d.scheduleid='"+uid+"' ";
					dbService.excuteSql(sql);
				}
			}
		}catch(Exception ex) {
			Log.error(SysTaskService.class, ex.getMessage());
		}
	}
	
	public JsonResult ExecuteTask(HttpServletRequest request, HttpServletResponse response, final String uid) {
		new Thread(new Runnable() {
			
			@Override
			public void run() {
				JSONObject info=new JSONObject();
				info.put("msgtype", 1000);
				String sql="SELECT name,code,taskclass,sqlcode,serverid FROM tb_sys_schedule where uid='"+uid+"' ";
				JSONArray list=dbService.QueryList(sql);
				if(list.size()==0) {
					return;
				}
				JSONObject item=list.getJSONObject(0);
				String name=item.getString("name");
				String code=item.getString("code");
				String taskclass=item.getString("taskclass");
				String sqlcode=item.getString("sqlcode");
				
				try {
					if(item.getString("serverid").equals("os"+WebConfig.getServerid())) {
						JsonResult result= taskservice.ExecuteTask(code, taskclass, sqlcode);
						if(result.isSuccess()) {
							info.put("msg", String.format("计划任务：%s(%s)执行成功！", name, code));
						}else {
							info.put("msg", String.format("计划任务：%s(%s)执行失败[%s]！", name, code,result.getMsg()));
						}
						WebSocketSets.getInstance().send("sysmsg", info.toJSONString());
					}else {
						JSONObject msg=new JSONObject();
						msg.put("code", "3");
						msg.put("node", item.getString("serverid"));
						JSONObject data=new JSONObject();
						data.put("code", code);
						data.put("taskclass", taskclass);
						data.put("sqlcode", sqlcode);
						data.put("name", name);
						msg.put("data", data);
						redispublish.Publish("/service/servernodestatus", msg);
					}
				}catch(Exception ex) {
					Log.error(this.getClass(), ex.getMessage());
					try {
						info.put("msg", String.format("计划任务：%s(%s)执行失败[%s]！", name, code, ex.getMessage()));
						WebSocketSets.getInstance().send("sysmsg", info.toJSONString());
					}catch(Exception ex2) {
						Log.error(this.getClass(), ex2.getMessage());
					}
				}
			}
		}).start();
		return Json.getJsonResult(true);
	}
}
