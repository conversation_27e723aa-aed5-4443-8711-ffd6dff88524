package com.ymiots.campusos.service.alleyway;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.config.redis.RedisClient;
import com.ymiots.campusos.entity.alleyway.AlleywayRecordRow;
import com.ymiots.campusos.entity.card.TeachStudInfo;
import com.ymiots.campusos.entity.dev.AccessController;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.utils.excel.Sheet;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Null;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Repository
public class CardRecordsService extends BaseService{

	@Autowired
	SysLogsService syslogs;

	@Autowired
	UserRedis userredis;

    //@Autowired
    //private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisClient redisClient;

    public JsonData getDoorApplyRecordList(HttpServletRequest request, HttpServletResponse response,String starttime,String endtime, String status, String key, int start,int limit){
        String sql = generateSQL(starttime, endtime, status, key);
        String page = " limit " + start + "," + limit + " ";
        JSONArray jsonArray = dbService.QueryList(sql);
        JsonData result = dbService.QueryJsonData(sql + page);
        result.setTotal(jsonArray.size());
        return result;
    }
    @NotNull
    private String generateSQL(String starttime, String endtime, String status, String key) {
        String sql = "select date_format(lar.open_time_start,'%Y-%m-%d %H:%i:%s') starttime,date_format(lar.open_time_end,'%Y-%m-%d %H:%i:%s') endtime,lar.reason, lar.status," +
                " date_format(lar.application_time,'%Y-%m-%d %H:%i:%s') as applicationtime, lar.id uid,lar.*,getminiareaname(lar.department) departmentname from tb_access_application lar ";
        sql += " where 1=1 ";
        if (StringUtils.isNotBlank(status) && !("-2").equals(status)) {
            sql += " AND lar.status=" + status;
        }
        if (StringUtils.isNotBlank(starttime)&& StringUtils.isNotBlank(endtime)) {
            sql+=" AND lar.application_time BETWEEN '"+ starttime +" 00:00:00' AND '"+ endtime +" 23:59:59' ";
        }
        if (StringUtils.isNotBlank(key)) {
            sql += " AND lar.name like '%"+key+"%";
        }
        sql += "order by lar.application_time DESC ";
        return sql;
    }

    public JsonData getDoorDownload(HttpServletRequest request, HttpServletResponse response, String formid) {
        String auditRecordSQL = "select a.id uid,b.machineid,a.down_status status,b.devname name,getareaname(areacode) areaname,date_format(a.down_at, '%Y-%m-%d %H:%i:%s') as down_at,a.downnum,a.downmsg  from tb_access_application_dev  a join tb_dev_accesscontroller b on a.dev_id = b.uid where a.apply_id='"+formid+"' ";
        JsonData data = dbService.QueryJsonData(auditRecordSQL);
        return data;
    }

    public JsonData DoorReturnDownload(HttpServletRequest request, HttpServletResponse response, String recordUids) {
        String auditRecordSQL1 = "update tb_access_application_dev set down_status = 0,down_at=now(),downmsg='',downnum=0 where down_status in (0,2) and id in ('" + String.join("','", recordUids.split(",")) + "')";
        String auditRecordSQL2 = "update tb_access_application_dev set down_status = 5,down_at=now(),downmsg='',downnum=0 where down_status in (1,3,4,5,6) and id in ('" + String.join("','", recordUids.split(",")) + "')";
        dbService.excuteSql(auditRecordSQL1);
        dbService.excuteSql(auditRecordSQL2);
        return Json.getJsonData(true);
    }


    public JsonData getRecordList(HttpServletRequest request, HttpServletResponse response, String areacode,String machineid, boolean viewchild,
    		String startdate, String enddate, String starttime, String endtime, String datetimetype, String orgcode, String readheader, String infoid, String key, String status, int start, int limit,String infoType) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT r.uid,r.serialnum,r.machineid,r.datatype,date_format(r.recordtime, '%Y-%m-%d %H:%i:%s') as recordtime,r.cardsn,date_format(r.createdate, '%Y-%m-%d %H:%i:%s') as createdate,r.direction,r.areacode,r.infoid,r.serviceuid,r.action,r.recordimg,r.infoname,r.infocode,r.status,c.usedes,door.name as doorname,door.inouttype,of.name as orgname,r.tiwen,r.healthcodetype,r.qrcodeinfo,r.tripupdatetime,r.trippath,ct.infolabel,sd.name as property FROM");
        sql.append("(SELECT uid,serialnum,machineid,readhead,datatype,cardsn,recordtime,createdate,serviceuid,direction,areacode,infoid,controllerid,action,recordimg,infoname,infocode,orgcode,status,tiwen,healthcodetype,qrcodeinfo,tripupdatetime,trippath " +
                "FROM tb_alleyway_records " );
        StringBuilder where = new StringBuilder(" WHERE 1=1");

        if(!StringUtils.isBlank(machineid)) {
        	 where.append(" and machineid IN ('").append(String.join("','", machineid.split(","))).append("')");
        }
        if(!StringUtils.isBlank(areacode)){
            if(viewchild){
                where.append(" and areacode like '").append(areacode).append("%' ");
            }else{
                where.append(" and areacode='").append(areacode).append("' ");
            }
        }
        if(!StringUtils.isBlank(orgcode)){
            if(viewchild){
                where.append(" and orgcode like '").append(orgcode).append("%' ");
            }else{
                where.append(" and orgcode='").append(orgcode).append("' ");
            }
        }

        if(StringUtils.isBlank(starttime)) {
        	starttime="00:00";
        }
        if(StringUtils.isBlank(endtime)) {
        	endtime="23:59";
        }
        if(!StringUtils.isBlank(datetimetype) && !StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate) ) {
        	if(datetimetype.equals("2")) {
        		startdate=String.format("%s 00:00:00", startdate);
        		enddate=String.format("%s 23:59:59", enddate);
            	where.append(" and (recordtime between '").append(startdate).append("' and '").append(enddate).append("') ");
            	if(!starttime.equals("00:00") && !endtime.equals("23:59")) {
            		starttime=starttime.replace(":", "");
            		endtime=endtime.replace(":", "");
            		where.append(" and (cast(date_format(recordtime,'%H%i') as signed) between "+starttime+" and "+endtime+") ");
            	}
        	}else {
            	startdate=String.format("%s %s:00", startdate,starttime);
            	enddate=String.format("%s %s:59", enddate,endtime);
            	where.append(" and (recordtime between '").append(startdate).append("' and '").append(enddate).append("')");
        	}
        }

        if(!StringUtils.isBlank(readheader) && !readheader.equals("-1")){
            where.append(" and readhead=").append(readheader);
        }
        if(!StringUtils.isBlank(infoid)){
            where.append(" and infoid='").append(infoid).append("' ");
        }
        if(!StringUtils.isBlank(key)){
            where.append(" and (infoname like '%").append(key).append("%' or infocode like '%").append(key).append("%' or cardsn='").append(ComHelper.LeftPad(key, 12, '0')).append("') ");
        }
        if(StringUtils.isNotBlank(status)){
            where.append(" and status=").append(status.equals("true")?"1":"0");
        }
        sql.append(where.toString());
        if(limit != 0){
            sql.append(" ORDER BY recordtime desc limit ").append(start).append(",").append(limit).append(" ) r ");
        }else{
            sql.append(" ORDER BY recordtime) r ");
        }

        sql.append("left join tb_dev_door door on door.controllerid=r.controllerid and door.readhead=r.readhead " +
                "left join tb_card_cardinfo c on c.cardsn=r.cardsn and c.status=1 " +
                "left join tb_card_orgframework of on of.code=r.orgcode " +
                "left join tb_card_teachstudinfo ct on ct.uid=r.infoid " +
                "left join tb_sys_dictionary sd on sd.code=ct.property and sd.groupcode = 'SYS0000055' ");
        sql.append("ORDER BY recordtime desc,uid desc");
        JSONArray data = dbService.QueryList(sql.toString());
        String totalcount = "SELECT count(1) as count " +
                "FROM tb_alleyway_records " + where;
        JSONArray countja = dbService.QueryList(totalcount);
        int total = countja.getJSONObject(0).getInteger("count");
        JsonData jd = Json.getJsonData(true, "", data, total);
        return jd;
    }

    public R<List<AlleywayRecordRow>> getAlleywayList(HttpServletRequest request, HttpServletResponse response, String areacode,String machineid, boolean viewchild,
                                  String startdate, String enddate, String starttime, String endtime, String datetimetype, String orgcode, String readheader, String infoid, String key, String status, int start, int limit,String infoType) {

        String sql = "select controllerid as devId,doorid doorId,recordimg facePath,cardsn,machineid machineId,infoid infoId,status,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordTime,direction direction from tb_alleyway_records ";
        sql += "where  1=1 ";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(machineid) && !"all".equals(machineid)) {
            sql += " and machineid in ('" + String.join("','", machineid.split(",")) + "') ";
            where += " and machineid in ('" + String.join("','", machineid.split(",")) + "') ";
        }
        if (StringUtils.isNotBlank(areacode)) {
            if(viewchild){
                sql += " and areacode like '" + areacode + "%' ";
                where += " and areacode like '" + areacode + "%' ";
            }else{
                sql += " and areacode='" + areacode + "' ";
                where += " and areacode='" + areacode + "' ";
            }
        }

        if (!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate)) {
            sql += " and recordtime between '" + startdate + "' and '" + enddate + "'";
            where += " and recordtime between '" + startdate + "' and '" + enddate + "'";
        }
        if (StringUtils.isNotBlank(readheader) && !"-1".equals(readheader)) {
            sql += " and readhead ='" + readheader + "' ";
            where += " and readhead ='" + readheader + "' ";
        }
//        sql += "order by recordtime desc limit " + start + "," + limit;
        sql += " order by recordtime desc ";
        List<AlleywayRecordRow> cardRecordList = dbService.queryList(sql,AlleywayRecordRow.class);

        if (CollectionUtil.isEmpty(cardRecordList)) {
            return R.fail(new ArrayList<>());
        }
        String infoSQL = "select ct.uid,ct.name,ct.code,getorgname(ct.orgcode) orgCode,b.imgpath,ct.infolabel infoLabel,sd.name property from tb_card_teachstudinfo ct left join tb_face_infoface b on ct.uid = b.infoid left join tb_sys_dictionary sd on sd.code=ct.property and sd.groupcode = 'SYS0000055' where 1=1 ";
        if (StringUtils.isNotBlank(infoType)) {
            infoSQL += " and ct.infotype = '" + infoType + "'";
        }
        if(!StringUtils.isBlank(infoid)){
            infoSQL += " and ct.uid='" + infoid + "' ";
        }
        if(!StringUtils.isBlank(key)){
            infoSQL += " and (ct.name like '%" + key + "%' or ct.code like '%" + key + "%' or ct.card='" + ComHelper.LeftPad(key, 12, '0') + "') ";
        }
//        if(StringUtils.isNotBlank(status)){
//            infoSQL += " and status="+(status.equals("true")?"1":"0");
//        }
        if(!StringUtils.isBlank(orgcode)){
            if(viewchild){
                infoSQL += " and ct.orgcode like '" + orgcode + "%' ";
            }else{
                infoSQL += " and ct.orgcode='" + orgcode + "' ";
            }
        }
        List<TeachStudInfo> teachStudInfos = dbService.queryList(infoSQL, TeachStudInfo.class);
        //获取到人员信息
        Map<String, List<TeachStudInfo>> infoListMap = teachStudInfos.stream()
                .collect(Collectors.groupingBy(TeachStudInfo::getUid));
        //获取到设别信息
        String machineSQL = "select da.devname devName,dd.name doorName,dd.uid,getareaname(da.areacode) as area,dd.inouttype inOutType from tb_dev_accesscontroller da " +
                " join tb_dev_door dd on dd.controllerid = da.uid";
        if (StringUtils.isNotBlank(machineid) && !"all".equals(machineid)) {
            machineSQL += " where da.machineid in ('" + String.join("','", machineid.split(",")) + "') ";
        }
        List<AccessController> accessControllers = dbService.queryList(machineSQL, AccessController.class);
        Map<String, List<AccessController>> accessListMap = accessControllers.stream()
                .collect(Collectors.groupingBy(AccessController::getUid));
        Set<String> validInfoIds = teachStudInfos.stream()
                .map(TeachStudInfo::getUid)
                .collect(Collectors.toSet());
        Set<String> validDevIds = accessControllers.stream()
                .map(AccessController::getUid)
                .collect(Collectors.toSet());
        //处理通行记录表合并其他数据
        cardRecordList = cardRecordList.stream()
                .filter(record -> validInfoIds.contains(record.getInfoId()) && validDevIds.contains(record.getDoorId()))
                .peek(i -> {
                    List<AccessController> controllers = accessListMap.get(i.getDoorId());
                    if (CollectionUtil.isNotEmpty(controllers)) {
                        AccessController accessController = controllers.get(0);
                        i.setDoorName(accessController.getDoorName());
                        i.setArea(accessController.getArea());
                    }
                    List<TeachStudInfo> studInfos = infoListMap.get(i.getInfoId());
                    if (CollectionUtil.isNotEmpty(studInfos)) {
                        TeachStudInfo teachStudInfo = studInfos.get(0);
                        i.setCode(teachStudInfo.getCode());
                        i.setName(teachStudInfo.getName());
                        i.setImgpath(teachStudInfo.getImgpath());
                        i.setOrg(teachStudInfo.getOrgCode());
                        i.setInfoLabel(teachStudInfo.getInfoLabel());
                        i.setProperty(teachStudInfo.getProperty());
                    }
                }).collect(Collectors.toList());
        //int count = dbService.getCount("tb_alleyway_records", where);
        List<AlleywayRecordRow> recordRows = cardRecordList.subList(start, Math.min(cardRecordList.size(), start + limit));
        return R.okPage(recordRows, cardRecordList.size());
    }
    public R<Null> exportAlleywayRecord(HttpServletRequest request, HttpServletResponse response, String key, String machineid, String startdate, String enddate) {
        List<AlleywayRecordRow> cardRecordList = new ArrayList<>();
        String sql = "select controllerid as devId,doorid doorId,cardsn,machineid machineId,infoid infoId,status,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordTime,direction direction from tb_alleyway_records ";
        sql += "where  1=1 ";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(machineid) && !"all".equals(machineid)) {
            sql += " and machineid='" + machineid + "' ";
            where += " and machineid='" + machineid + "' ";
        }
        if (!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate)) {
            sql += " and recordtime between '" + startdate + "' and '" + enddate + "'";
            where += " and recordtime between '" + startdate + "' and '" + enddate + "'";
        }
        sql += "order by recordtime ";
        System.out.println("当前时间" + DateHelper.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        JSONArray jsonArray = dbService.QueryList(sql);
        for (int i = 0; i < jsonArray.size(); i++) {
            AlleywayRecordRow row = new AlleywayRecordRow();
            JSONObject object = jsonArray.getJSONObject(i);
            row.setInfoId(object.getString("infoId"));
            row.setArea(object.getString("area"));
            row.setCode(object.getString("code"));
            row.setInfoLabel(object.getString("infoLabel"));
            row.setName(object.getString("name"));
            row.setCardsn(object.getString("cardsn"));
            row.setDevId(object.getString("devId"));
            row.setDoorName(object.getString("doorName"));
            row.setOrg(object.getString("org"));
            row.setRecordTime(object.getString("recordTime"));
            row.setRecordType(object.getString("recordType"));
            row.setMachineId(object.getString("machineId"));
            row.setProperty(object.getString("property"));
            row.setInfoLabel(object.getString("infoLabel"));
            row.setMachineId(object.getString("machineId"));
            row.setStatus(object.getString("status"));
            row.setDirection(object.getString("direction"));
            if ("1".equals(object.getString("direction"))){
                row.setDirectionname("进");
            }else if("2".equals(object.getString("direction"))){
                row.setDirectionname("出");
            }else {
                row.setDirectionname("");
            }
            cardRecordList.add(row);
        }

        System.out.println(DateHelper.format(new Date(), "yyyy-MM-dd HH:mm:ss"));

        if (CollectionUtil.isEmpty(cardRecordList)) {
            return R.fail("无导出数据");
        }
        String infoSQL = "select ct.uid,ct.name,ct.code,getorgname(ct.orgcode) orgCode,ct.infolabel infoLabel,sd.name property from tb_card_teachstudinfo ct left join tb_sys_dictionary sd on sd.code=ct.property and sd.groupcode = 'SYS0000055' ";

        if (StringUtils.isNotBlank(key)) {
            infoSQL += " where name like '" + key + "%' or code='" + key + "'";
        }
        List<TeachStudInfo> teachStudInfos = dbService.queryList(infoSQL, TeachStudInfo.class);
        //获取到人员信息
        Map<String, List<TeachStudInfo>> infoListMap = teachStudInfos.stream()
                .collect(Collectors.groupingBy(TeachStudInfo::getUid));
        //获取到设别信息
        String machineSQL = "select da.devname devName,dd.name doorName,dd.uid,getareaname(da.areacode) as area,dd.inouttype inOutType from tb_dev_accesscontroller da " +
                " join tb_dev_door dd on dd.controllerid = da.uid";
        if (StringUtils.isNotBlank(machineid) && !"all".equals(machineid)) {
            machineSQL += " where da.machineid = '" + machineid + "'";
        }
        List<AccessController> accessControllers = dbService.queryList(machineSQL, AccessController.class);
        Map<String, List<AccessController>> accessListMap = accessControllers.stream()
                .collect(Collectors.groupingBy(AccessController::getUid));
        //处理通行记录表合并其他数据
        cardRecordList = cardRecordList.stream()
                .peek(i -> {
                    List<AccessController> controllers = accessListMap.get(i.getDoorId());
                    if (CollectionUtil.isNotEmpty(controllers)) {
                        AccessController accessController = controllers.get(0);
                        i.setDoorName(accessController.getDoorName());
                        i.setArea(accessController.getArea());
                    }
                    List<TeachStudInfo> studInfos = infoListMap.get(i.getInfoId());
                    if (CollectionUtil.isNotEmpty(studInfos)) {
                        TeachStudInfo teachStudInfo = studInfos.get(0);
                        i.setCode(teachStudInfo.getCode());
                        i.setName(teachStudInfo.getName());
                        i.setOrg(teachStudInfo.getOrgCode());
                        i.setInfoLabel(teachStudInfo.getInfoLabel());
                        i.setProperty(teachStudInfo.getProperty());
                    }
                }).collect(Collectors.toList());

        LinkedHashMap<Integer, AlleywayRecordRow> map = new LinkedHashMap<>();
        Sheet<AlleywayRecordRow> sheet = new Sheet<>(map, AlleywayRecordRow.class);
        int row = 1;
        for (AlleywayRecordRow vo : cardRecordList) {
            map.put(row, vo);
            row++;
        }
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String extFilePath = "temp/通行记录记录表" + format + ".xlsx";
        String filePath = WebConfig.getUploaddir() + extFilePath;
        com.ymiots.framework.utils.excel.ExcelUtil.write(sheet, filePath, "通行记录记录表", 6,startdate,enddate);
        return R.ok(extFilePath);

    }

    @Async
    public void cacheFaceRecordSum(String cacheKey, int totalRecords) {
        redisClient.setObject(cacheKey,totalRecords);
        redisClient.setExpire(cacheKey,  1, TimeUnit.HOURS);
    }

    @Async
    public void cacheFaceRecordPage(String cacheKey, List<AlleywayRecordRow> alleywayRecordList) {
        // 将 List 转换为 JSON 字符串
        String jsonArrayStr = JSON.toJSONString(alleywayRecordList);

        // 将 JSON 字符串转换为 JSONArray
        JSONArray jsonArray = JSON.parseArray(jsonArrayStr);
        redisClient.setJSONArray(cacheKey,jsonArray);
        redisClient.setExpire(cacheKey,  1, TimeUnit.HOURS);
    }
    public JsonData getHistoryRecordList(HttpServletRequest request, HttpServletResponse response, String areacode,String machineid, boolean viewchild,
    		String startdate, String enddate, String starttime, String endtime, String datetimetype, String orgcode, String readheader, String infoid, String key, String status, int start, int limit) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT uid,serialnum,machineid,readhead,datatype,cardsn,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordtime,createdate,serviceuid,direction,areacode,infoid,controllerid,action,recordimg,infoname,infocode,orgcode,status,tiwen,healthcodetype,qrcodeinfo,tripupdatetime,trippath,doorname,orgname,property,infolabel " +
                "FROM tb_alleyway_records_sleep " );
        StringBuilder where = new StringBuilder(" WHERE 1=1");

        if(!StringUtils.isBlank(machineid)) {
        	 where.append(" and machineid IN ('").append(String.join("','", machineid.split(","))).append("')");
        }
        //else {
        //    return new JsonData();
        //}
        if(!StringUtils.isBlank(areacode)){
            if(viewchild){
                where.append(" and areacode like '").append(areacode).append("%' ");
            }else{
                where.append(" and areacode='").append(areacode).append("' ");
            }
        }
        if(!StringUtils.isBlank(orgcode)){
            if(viewchild){
                where.append(" and orgcode like '").append(orgcode).append("%' ");
            }else{
                where.append(" and orgcode='").append(orgcode).append("' ");
            }
        }

        if(StringUtils.isBlank(starttime)) {
        	starttime="00:00";
        }
        if(StringUtils.isBlank(endtime)) {
        	endtime="23:59";
        }
        if(!StringUtils.isBlank(datetimetype) && !StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate) ) {
        	if(datetimetype.equals("2")) {
        		startdate=String.format("%s 00:00:00", startdate);
        		enddate=String.format("%s 23:59:59", enddate);
            	where.append(" and (recordtime between '").append(startdate).append("' and '").append(enddate).append("') ");
            	if(!starttime.equals("00:00") && !endtime.equals("23:59")) {
            		starttime=starttime.replace(":", "");
            		endtime=endtime.replace(":", "");
            		where.append(" and (cast(date_format(recordtime,'%H%i') as signed) between "+starttime+" and "+endtime+") ");
            	}
        	}else {
            	startdate=String.format("%s %s:00", startdate,starttime);
            	enddate=String.format("%s %s:59", enddate,endtime);
            	where.append(" and (recordtime between '").append(startdate).append("' and '").append(enddate).append("')");
        	}
        }else if(StringUtils.isBlank(datetimetype) && !StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate) ) {
            where.append(" and (recordtime between '").append(startdate).append("' and '").append(enddate).append("')");
        }

        if(!StringUtils.isBlank(readheader) && !readheader.equals("-1")){
            where.append(" and readhead=").append(readheader);
        }
        if(!StringUtils.isBlank(infoid)){
            where.append(" and infoid='").append(infoid).append("' ");
        }
        if(!StringUtils.isBlank(key)){
            where.append(" and (infoname like '%").append(key).append("%' or infocode = '").append(key).append("' or cardsn='").append(ComHelper.LeftPad(key, 12, '0')).append("') ");
        }
        if(StringUtils.isNotBlank(status)){
            where.append(" and status=").append(status.equals("true")?"1":"0");
        }
        sql.append(where.toString());
        if(limit != 0){
            sql.append(" ORDER BY recordtime desc limit ").append(start).append(",").append(limit);
        }else{
            sql.append(" ORDER BY recordtime ");
        }
        JSONArray data = dbService.QueryList(sql.toString());
        String totalcount = "SELECT count(1) as count " +
                "FROM tb_alleyway_records_sleep " + where;
        JSONArray countja = dbService.QueryList(totalcount);
        int total = countja.getJSONObject(0).getInteger("count");
        JsonData jd = Json.getJsonData(true, "", data, total);
        return jd;
    }

	public JsonResult AddBlackName(HttpServletRequest request, HttpServletResponse response,String cardnos){
		 String[] cardnox=cardnos.split(",");
		 List<String> sqlist=new ArrayList<String>();
		 StringBuffer sb=new StringBuffer();
		 String userid=userredis.getUserId(request);
		 sb.append("insert into tb_card_cardinfo_blacklist(uid,cardno,cardsn,des,createdate,creatorid) values");
		 for(int i=0;i<cardnox.length;i++){
			 String cardno=cardnox[i];
			 sqlist.add("delete from tb_card_cardinfo_blacklist where cardno='"+cardno+"';");
			 sb.append("(uuid(),'"+cardno+"','"+cardno+"','从刷卡记录中人工添加',now(),'"+userid+"')");
			 if(i<cardnox.length-1){
				 sb.append(",");
			 }else{
				 sb.append(";");
			 }
		 }
		if(sqlist.size()>0){
			dbService.excuteBatchSql(ComHelper.ListToArray(sqlist));
			syslogs.Write(request, "刷卡记录", String.format("将刷卡记录中卡号加入黑名单%s", cardnos));
		}
		if(cardnox.length>0){
			dbService.excuteSql(sb.toString());
		}
		return Json.getJsonResult(true);
	}

	public JsonData getRecordimg(HttpServletRequest request, HttpServletResponse response,String uid){
        String sql = "SELECT recordimg FROM tb_alleyway_records_img WHERE recordid = '"+uid+"' AND status=1";
        return dbService.QueryJsonData(sql);
    }

    public void DeleteAllewayRecordSleep(Date date){
        String year = dbService.QueryJSONObject("SELECT NAME FROM tb_sys_dictionary WHERE groupcode = 'SYS0000064' AND CODE = 0").getString("NAME");
        // 获取当前日期
        Date currentDate = date;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.YEAR, -Integer.parseInt(year));
        Date downDate = calendar.getTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = dateFormat.format(downDate);
        dbService.excuteSql("delete from tb_alleyway_records_sleep where date_format(recordtime, '%Y-%m-%d') < '"+format+"'");
    }

    public JsonResult updateStatus(HttpServletRequest request, HttpServletResponse response,String uid,String status){
        String[] statuses = status.split(",");
        String[] uids = uid.split(",");
        if(uids.length > 1){
            List<String> uid_0 = new ArrayList<String>();
            List<String> uid_1 = new ArrayList<String>();
            for (int i = 0; i < statuses.length; i++) {
                if ("0".equals(statuses[i])){
                    uid_0.add(uids[i]);
                }else if ("1".equals(statuses[i])){
                    uid_1.add(uids[i]);
                }
            }
            String sql = "UPDATE tb_alleyway_records SET status=0 WHERE uid IN ('"+String.join("','",uid_1)+"') AND status=1";
            dbService.excuteSql(sql);
            String sql2 = "UPDATE tb_alleyway_records SET status=1 WHERE uid IN ('"+String.join("','",uid_0)+"') AND status=0";
            dbService.excuteSql(sql2);
        }else {
            if(StringUtils.isNotBlank(status) && "1".equals(status)){
                String sql = "UPDATE tb_alleyway_records SET status=0 WHERE uid=? AND status=1";
                dbService.excuteSql(sql,uid);
            }else if(StringUtils.isNotBlank(status) && "0".equals(status)){
                String sql2 = "UPDATE tb_alleyway_records SET status=1 WHERE uid=? AND status=0";
                dbService.excuteSql(sql2,uid);
            }
        }
        return Json.getJsonResult(true);
    }

    public JsonData getDeviceList(HttpServletRequest request, HttpServletResponse response,String areacode,boolean viewchild,String key,int start,int limit){
    	String fields="ac.uid,ac.machineid,ac.devname";
    	String table="tb_dev_accesscontroller ac";
        //如果为普通管理员，需要受二级授权控制
        if(userredis.get(request).getUsertype()==1){
		    String userid=userredis.getUserId(request);
            table+= " inner join tb_dev_areaframework_user ua on ua.areacode=ac.areacode and ua.userid='"+userid+"' ";
        }
    	String where=" ac.useclass='1' ";
    	//if (!StringUtils.isBlank(areacode)) {
    	//	if(viewchild) {
    	//		where += " and ac.areacode like '" + areacode + "%' ";
    	//	}else {
    	//		where += " and ac.areacode='" + areacode + "' ";
    	//	}
		//}
		if (!StringUtils.isBlank(key)) {
			where += " and (ac.devname like '%" + key + "%' or ac.machineid='" + key + "') ";
		}
		String orderby = ExtSort.Orderby(request, " ac.machineid asc ");
		JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;

    }

    public String ExportRecords(HttpServletRequest request, HttpServletResponse response, String areacode,String machineid, boolean viewchild,
                                 String startdate, String enddate, String starttime, String endtime, String datetimetype, String orgcode, String readheader, String infoid, String key, String status) throws IOException {
        JsonData result = getRecordList(request, response, areacode, machineid, viewchild, startdate, enddate, starttime, endtime, datetimetype, orgcode, readheader, infoid, key, status, 0, 0,null);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        if ("1".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "infocode");
            cell.put("size", 15);
            cells.add(cell);
        } else if ("2".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "infocode");
            cell.put("size", 15);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "infoname");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "体温℃");
        cell.put("datakey", "tiwen");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "人员标签");
        cell.put("datakey", "infolabel");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "健康码");
        cell.put("datakey", "healthcodetype");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "二维码信息");
        cell.put("datakey", "qrcodeinfo");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "行程卡时间");
        cell.put("datakey", "tripupdatetime");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "通行时间");
        cell.put("datakey", "recordtime");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "方向(1为进/2为出)");
        cell.put("datakey", "inouttype");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "{doorname}({machineid},{inouttype})");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "门名称");
        cell.put("datakey", "doorname");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属单位");
        cell.put("datakey", "orgname");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "人员性质");
        cell.put("datakey", "property");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "人员标签");
        cell.put("datakey", "infolabel");
        cell.put("size", 25);
        cells.add(cell);

        String title= "通行信息记录";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";


        return ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
    }


    public String ExportHistoryRecords(HttpServletRequest request, HttpServletResponse response, String areacode,String machineid, boolean viewchild,
                                 String startdate, String enddate, String starttime, String endtime, String datetimetype, String orgcode, String readheader, String infoid, String key, String status) throws IOException {
        JsonData result = getHistoryRecordList(request, response, areacode, machineid, viewchild, startdate, enddate, starttime, endtime, datetimetype, orgcode, readheader, infoid, key, status, 0, 0);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        if ("1".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "infocode");
            cell.put("size", 15);
            cells.add(cell);
        } else if ("2".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "infocode");
            cell.put("size", 15);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "infoname");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "体温℃");
        cell.put("datakey", "tiwen");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "人员标签");
        cell.put("datakey", "infolabel");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "健康码");
        cell.put("datakey", "healthcodetype");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "二维码信息");
        cell.put("datakey", "qrcodeinfo");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "行程卡时间");
        cell.put("datakey", "tripupdatetime");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "通行时间");
        cell.put("datakey", "recordtime");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "方向(1为进/2为出)");
        cell.put("datakey", "inouttype");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "{doorname}({machineid},{inouttype})");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "门名称");
        cell.put("datakey", "doorname");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属单位");
        cell.put("datakey", "orgname");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "人员性质");
        cell.put("datakey", "property");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "人员标签");
        cell.put("datakey", "infolabel");
        cell.put("size", 25);
        cells.add(cell);

        String title= "通行信息记录";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";


        return ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);
    }

}
