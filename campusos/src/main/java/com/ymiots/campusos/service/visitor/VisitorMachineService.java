package com.ymiots.campusos.service.visitor;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Repository
public class VisitorMachineService extends BaseService {

    @Autowired
    UserRedis userredis;

    @Autowired
    SysLogsService syslogs;

    public JsonData getVistiorMachineList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit){
        String fields = "uid, position, name, remark, createdate ";
        String table = "tb_visitor_machine ";
        String where="status=1 ";
        if(!StringUtils.isBlank(key)) {
            where +="AND (position='"+key+"' or name like '%"+key+"%')";
        }
        String orderby= ExtSort.Orderby(request, "createdate desc");
        JsonData result= dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonResult addMachine(HttpServletRequest request, HttpServletResponse response, String uid, String position, String name, String remark, MultipartRequest filepart) throws IOException {
        MultipartFile file=  filepart.getFile("imagefile");

        final StringBuffer imagepath = new StringBuffer();

        if(file!=null){
            String filename=file.getOriginalFilename();
            if(filename.length()>0){

                String img = filename.substring(filename.lastIndexOf(".")).toLowerCase();
                if(!img.equals(".jpg")){
                    return Json.getJsonResult(false,"请选择上传图片文件，扩展名必须为.jpg");
                }
                if(file.getSize() > 5242880){
                    return Json.getJsonResult(false,"上传的照片大于5MB");
                }
                MultipartFile[] files = new MultipartFile[] { file };
                List<String> filelist = FileHelper.SaveMultipartFile(files, WebConfig.getUploaddir(),"face");
                if (filelist.size() == 0) {
                    return Json.getJsonResult(false, "文件保存失败");
                }
                String filepath = filelist.get(0);
                String path = WebConfig.getUploaddir()+filepath.substring(1,filepath.length());
                if(file.getSize() > 204800){
                    ImageUtil.changeImageWH(path,1024,1024);
                }
                imagepath.append(filepath);
            }
        }

        if (StringUtils.isBlank(uid)) {
            int c = dbService.getCount("tb_visitor_machine", "position='" + position + "' ");
            if (c > 0) {
                return Json.getJsonResult(false, "区域编号已存在");
            }
            String creatorid = userredis.getUserId(request);
            String insertSQL = "INSERT INTO tb_visitor_machine (uid, position, name, remark, createdate, creatorid, status,imagefile ) VALUES (uuid(), ?, ?, ?, now(), ?, 1 ,?)";
            dbService.excuteSql(insertSQL, position, name, remark, creatorid,imagepath);
            syslogs.Write(request, "访客通道设置", "添加区域");
        } else {
            if (StringUtils.isNotBlank(imagepath)) {
                String updateSQL = "UPDATE tb_visitor_machine SET position=?,name=?,remark=?,imagefile = ? WHERE uid=?";
                dbService.excuteSql(updateSQL, position, name, remark, imagepath,uid);
            } else {
                String updateSQL = "UPDATE tb_visitor_machine SET position=?,name=?,remark=? WHERE uid=?";
                dbService.excuteSql(updateSQL, position, name, remark, uid);
            }
            syslogs.Write(request, "访客通道设置", String.format("修改区域:%s", uid));
        }
        return Json.getJsonResult(true);
    }

    public  JsonResult delMachine(HttpServletRequest request, HttpServletResponse response,String uid,String position){
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false,"uid为空！");
        }
        int count = dbService.getCount("tb_visitor_machine_device", "position IN ('" + String.join("','", position.split(",")) + "')");

        if (count > 0) {
            return Json.getJsonResult(false,"删除所选区域位置已设置设备,不可删除");
        }

        String delSQL = "DELETE FROM tb_visitor_machine WHERE uid IN('"+String.join("','",uid.split(","))+"') ";
        dbService.excuteSql(delSQL);

        syslogs.Write(request,"访客通道设置", String.format("删除区域:%s", uid));
        return Json.getJsonResult(true,"删除成功！");
    }

    public JsonData getAllDeviceList(HttpServletRequest request, HttpServletResponse response,String position, String selecteduid, String key, int start, int limit) {
        String fields = "da.uid,da.machineid,da.devname,da.devstatus,getareaname(da.areacode) areacode,ddn.uid doorid,ddn.name doorName,ddn.relays ";
        String table = "tb_dev_accesscontroller da left join tb_dev_door_name ddn on ddn.controllerid = da.uid ";
        String where = " (SELECT count(1) FROM tb_visitor_machine_device vmd WHERE vmd.devid=da.uid and vmd.position='"+position+"')=0";

        JSONArray SelectedChild = JSONArray.parseArray(selecteduid);
        if (SelectedChild.size() > 0) {
            String[] uidx = ComHelper.ListToArray(SelectedChild);
            where += " and da.uid not in('" + String.join("','", uidx) + "')";
        }
        if (StringUtils.isNotBlank(key)) {
            where += " and (da.devsn like '%" + key + "%' or da.devname like '%" + key + "%')";
        }
//        //2 5 控制器   9人脸机 12 梯控版
//        where += " and devclass = 9 ";
        JsonData db = dbService.QueryJsonData(fields, table, where, "", start, limit);
        return db;
    }

    public JsonResult setDevice(HttpServletRequest request, HttpServletResponse response, String position, String devid, String doorids) {
        String delSQL = "DELETE FROM tb_visitor_machine_device WHERE position=? AND devid IN('" + String.join("','", devid.split(",")) + "')";
        dbService.excuteSql(delSQL, position);
        String insertSQL = "INSERT INTO tb_visitor_machine_device(uid, position, devid, machineid, name, areacode,relays,doorid,doorname)" +
                " SELECT uuid(),'" + position + "',da.uid,machineid,devname, areacode,relays,ddn.uid,ddn.name FROM tb_dev_accesscontroller da " +
                " left join tb_dev_door_name ddn on ddn.controllerid = da.uid WHERE da.uid IN ('" + String.join("','", devid.split(",")) + "') ";
        if (StringUtils.isNotBlank(doorids)) {
            insertSQL += " and ddn.uid in ('" + String.join("','", doorids.split(",")) + "')";
        }
        dbService.excuteSql(insertSQL);
        syslogs.Write(request, "访客通道设置", String.format("设置设备:区域编号为%s,设备id为:%s", position, devid));
        return Json.getJsonResult(true);
    }

    public JsonResult addFloor(HttpServletRequest request, HttpServletResponse response, String position, String devid, String floor) {
        String updateSQL = "UPDATE tb_visitor_machine_device set floor =? where position = ? and devid = ? ";
        dbService.excuteSql(updateSQL, floor, position, devid);
        syslogs.Write(request, "访客通道梯控设置", String.format("设置设备:区域编号为%s,设备id为:%s,梯控修改：%s", position, devid, floor));
        return Json.getJsonResult(true);
    }

    public  JsonResult delDevice(HttpServletRequest request, HttpServletResponse response,String uid,String position){
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false,"uid为空！");
        }
        String delSQL = "DELETE FROM tb_visitor_machine_device WHERE uid IN('"+String.join("','",uid.split(","))+"')";
        dbService.excuteSql(delSQL);

        //删除下载名单
        String delNameListSQL = "DELETE FROM tb_visitor_record_namelist WHERE status IN (0,2) AND devid IN ('"+String.join("','",uid.split(","))+"')";
        dbService.excuteSql(delNameListSQL);
        String updateNameListSQL = "UPDATE tb_visitor_record_namelist SET status=3 WHERE status IN (1,3,4,5,6) AND devid IN ('"+String.join("','",uid.split(","))+"')";
        dbService.excuteSql(updateNameListSQL);

        syslogs.Write(request,"访客通道设置", String.format("删除设备:设备id为:%s,机号为:%s",position,uid));
        return Json.getJsonResult(true,"删除成功！");
    }

    public JsonData getVMDeviceList(HttpServletRequest request, HttpServletResponse response, String position, String key, int start, int limit){
        String fields = "uid, position, devid, machineid, name, getareaname(areacode) areacode,relays,doorname,floor ";
        String table = "tb_visitor_machine_device ";
        String where="position='"+position+"'";
        if(!StringUtils.isBlank(key)) {
            where +="AND (name like '%"+key+"%' or machineid='"+key+"')";
        }
        String orderby= ExtSort.Orderby(request, "machineid desc");
        JsonData result= dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }
}
