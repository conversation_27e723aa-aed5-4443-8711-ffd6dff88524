package com.ymiots.campusos.service.face;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.RedisMessageEntity;
import com.ymiots.campusos.redis.RedisMessagePublish;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysConfigService;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.datafactory.CallbackTransaction;
import com.ymiots.framework.websocket.WebSocketSets;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.opencv.core.*;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import org.opencv.objdetect.CascadeClassifier;
import org.opencv.objdetect.Objdetect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Null;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Repository
public class IntoFaceService extends BaseService {

    @Autowired
    private UserRedis userRedis;

    @Autowired
    RedisMessagePublish redismsg;

    @Autowired
    private SysLogsService syslogs;

    @Autowired
    SysConfigService syscfg;

    public JsonData getPropleList(HttpServletRequest request, HttpServletResponse response,String status,
                                  String orgcode, String key, String infotype, boolean viewchild, String ishasmodel, String property,boolean flag, int start, int limit) {
        String fields = "a.uid,a.code,a.name,a.sex,a.card,a.cardsn,a.orgcode,a.infotype,a.intoyear,a.focus,a.status,b.imgpath, getorgname(a.orgcode) as orgname,photo_quality," +
                "(SELECT COUNT(1) FROM tb_face_infoface b WHERE b.infoid = a.uid ) as total,(SELECT COUNT(1) FROM tb_face_finger_infofinger b WHERE b.infoid = a.uid ) as fingertotal";
        String table = "tb_card_teachstudinfo a LEFT JOIN tb_face_infoface b ON a.uid = b.infoid";
        String where = " a.status=1 ";
        SysUser user=userRedis.get(request);
        if(user.getUsertype()==1){
            String userid=user.getUid();
            table+= " inner join tb_card_orgframework_user uo on uo.orgcode=a.orgcode and uo.userid='"+userid+"' ";
        }
        if (!infotype.equals("0") && StringUtils.isNotBlank(infotype)) {
            where += " and a.infotype=" + infotype;
        }
        if (flag){
            where += " and b.imgpath is not null";
        }
        if (StringUtils.isNotBlank(property) && !property.equals("-1")) {
            where += " and a.property=" + property;
        }
        if (StringUtils.isNotBlank(status) && !status.equals("-1")) {
            where += " and b.status=" + status;
        }
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and a.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and a.orgcode='" + orgcode + "' ";
            }
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (a.code='" + key + "' or a.name like '%" + key + "%' or a.mobile like '%" + key + "%' or  a.card='" + ComHelper.LeftPad(key, 12, '0') + "' or a.cardsn='" + ComHelper.LeftPad(key, 12, '0') + "' )";
        }
        if (ishasmodel.equals("0")) {
            where += " and b.imgpath is null";
        }
        if (ishasmodel.equals("1")) {
            where += " and b.imgpath is not null";
        }
        String orderby = ExtSort.Orderby(request, "a.createdate desc");
        return dbService.QueryJsonData(fields, table, where, orderby, start, limit);
    }

    public JsonData getFirstImg(HttpServletRequest request, HttpServletResponse response, String infoid) {
        String sql = "SELECT uid,imgpath FROM tb_face_infoface WHERE infoid = '" + infoid + "' ORDER BY createdate ASC";
        return dbService.QueryJsonData(sql);
    }

    public JsonResult UploadFace(final HttpServletRequest request, HttpServletResponse response, final String uid, final String infoid, MultipartFile file) throws Exception {
        if (file.getSize() > 5242880) {
            return Json.getJsonResult(false, "上传的照片大于5MB");
        }
        final StringBuilder filepath = new StringBuilder();

        String faceuid = uid;
        if (StringUtils.isBlank(faceuid)) {
            faceuid = UUID.randomUUID().toString();
        }
        if (ComHelper.IsRemotResource(WebConfig.getDomain(), WebConfig.getResourceDomain())) {
            String imagebase64 = FileHelper.ImageByte2Base64(file.getBytes());
            String url = ComHelper.getResourceUrl(WebConfig.getResourceDomain(), "/API/Face/SubmitFaceBase64");
            Map<String, String> params = new HashMap<String, String>();
            params.put("facedata", imagebase64);
            params.put("faceuid", faceuid);
            JsonResult rs = HttpRequest.HttpPost(url, params);
            if (rs.isSuccess()) {
                if (rs.getData().getBoolean("success")) {
                    filepath.append(rs.getData().getString("msg"));
                }
            }
        } else {
            String localfilepath = FileHelper.SaveMultipartFile(file, WebConfig.getUploaddir(), "face", faceuid + ".jpg");
            filepath.append(localfilepath);
        }

        if (filepath.length() == 0) {
            return Json.getJsonResult(false, "文件保存失败");
        }
        //看是否开启人脸建模校验
        JSONObject jsonObject = dbService.QueryJSONObject("select cvalue from tb_sys_config where cname = ?", "FaceModelingVerify");
        String flag = null;
        if (jsonObject==null){
            flag="0";
        }else {
            flag = jsonObject.getString("cvalue");
        }
        R<Null> nullR = R.ok("");
        if ("1".equals(flag)){
            nullR = detectFace(WebConfig.getUploaddir() + filepath.toString());
        }

        //如果文件大于2KB，将照片压缩至宽度1024高度1024范围以内的尺寸
        if (file.getSize() > 204800) {
            ImageUtil.changeImageWH(WebConfig.getUploaddir() + filepath.toString(), 1024, 1024);
        }

        final JSONObject jo = new JSONObject();
        R<Null> finalNullR = nullR;
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                try {
                    String imguid = "";
                    String creatorid = userRedis.getUserId(request);
                    if (StringUtils.isBlank(uid)) {
                        imguid = FilenameUtils.getBaseName(filepath.toString());
                        int count = dbService.getCount("tb_face_infoface", "infoid = '" + infoid + "'");
                        if (count>=1) {
                            throw new Exception("上传失败，最多只能上传1张照片");
                        }
                        if (finalNullR!=null&&!finalNullR.isSuccess()) {
                            logger.error(finalNullR.getMsg());
                            //throw new Exception(finalNullR.getMsg());

                        }else {
                            String sql = "INSERT INTO tb_face_infoface (uid,infoid,imgpath,status,creatorid,createdate) VALUES ('" + imguid + "','" + infoid + "','" + filepath.toString() + "',1,'" + creatorid + "',now())";
                            PreparedStatement ps = connection.prepareStatement(sql);
                            ps.executeUpdate();
                            ps.close();
                        }

                        syslogs.Write(request, "人脸建模", String.format("上传人脸:%s", imguid));
                    } else {
                        imguid = uid;
                        String updateSql = "";
                        if (finalNullR!=null){
                            updateSql = "UPDATE tb_face_infoface SET imgpath='" + filepath.toString() + "' ,iscreate=0,iscreatesk=0,createdate=now() WHERE uid= '" + uid + "'";
                        }else {
                            String photoQuality = "合格";
                            String status = "1";
                            if (!finalNullR.isSuccess()) {
                                photoQuality = finalNullR.getMsg();
                                status = "0";
                                logger.error(finalNullR.getMsg());
                            }
                            updateSql = "UPDATE tb_face_infoface SET imgpath='" + filepath.toString() + "' ,iscreate=0,iscreatesk=0,createdate=now(),photo_quality ='"+photoQuality+"',status = '"+status+"' WHERE uid= '" + uid + "'";
                        }
                        PreparedStatement ps = connection.prepareStatement(updateSql);
                        ps.executeUpdate();
		                ps.close();

                        int count = dbService.getCount("tb_face_authorizeface", "faceid = '" + uid + "'");
                        if (count > 0){

                            String updateFad = "UPDATE tb_face_authorizeface SET infodownstatus=5,timedownstatus=0,downnum=0,downmsg='' WHERE faceid = '" + uid + "' AND infodownstatus in(1,3,5) and timedownstatus in(0,2)";
                            ps = connection.prepareStatement(updateFad);
                            ps.executeUpdate();
                            ps.close();

                            String updateFad2 = "UPDATE tb_face_authorizeface SET infodownstatus=5,timedownstatus=5,downnum=0,downmsg='' WHERE faceid = '" + uid + "' AND infodownstatus in(1,3,5) and timedownstatus in(1,3,5)";
                            ps = connection.prepareStatement(updateFad2);
                            ps.executeUpdate();
                            ps.close();

                            String updateFad3 = "UPDATE tb_face_authorizeface SET infodownstatus=0,timedownstatus=5,downnum=0,downmsg='' WHERE faceid = '" + uid + "' AND infodownstatus in(0,2) and timedownstatus in(1,3,5)";
                            ps = connection.prepareStatement(updateFad3);
                            ps.executeUpdate();
                            ps.close();
                        }
                        count = dbService.getCount("tb_face_authorize_group_access ga join tb_face_infoface fi on fi.infoid = ga.infoid ", "fi.uid = '" + uid + "'");
                        if (count > 0){

                            String updateFad = "UPDATE tb_face_authorize_group_access ga join tb_face_infoface fi on fi.infoid = ga.infoid SET ga.downstatus=5,ga.downnum=0,ga.downmsg='' WHERE fi.uid = '" + uid + "' AND downstatus in(1,3,5) ";
                            ps = connection.prepareStatement(updateFad);
                            ps.executeUpdate();
                            ps.close();

                            String updateFad2 = "UPDATE tb_face_authorize_group_access ga join tb_face_infoface fi on fi.infoid = ga.infoid SET ga.downstatus=5,ga.downnum=0,ga.downmsg='' WHERE fi.uid = '" + uid + "' AND downstatus in(1,3,5)";
                            ps = connection.prepareStatement(updateFad2);
                            ps.executeUpdate();
                            ps.close();

                            String updateFad3 = "UPDATE tb_face_authorize_group_access ga join tb_face_infoface fi on fi.infoid = ga.infoid SET ga.downstatus=0,ga.downnum=0,ga.downmsg='' WHERE fi.uid = '" + uid + "' AND downstatus in(0,2) ";
                            ps = connection.prepareStatement(updateFad3);
                            ps.executeUpdate();
                            ps.close();
                        }
                        syslogs.Write(request, "人脸建模", String.format("修改人脸:%s", uid));
                    }

                    jo.put("uid", imguid);
                    jo.put("imgurl", filepath.toString());

                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            //向设备通知授权
                            JSONObject data = new JSONObject();
                            data.put("uid", uid);
                            RedisMessageEntity rme = new RedisMessageEntity();
                            rme.setCmd("visitorfacedel");
                            rme.setData(data);
                            rme.setDevtype(4);
                            rme.setServer_uid("");
                            redismsg.VisitorFaceDel(rme);
                            try {
                                Thread.sleep(2000);
                            } catch (InterruptedException e) {
                                Log.error(IntoFaceService.class, e.getMessage());
                            }

                            redismsg.NotifyFaceDevice();
                        }
                    }).start();

                } catch (Exception e) {
                    e.printStackTrace();
                    Log.error(IntoFaceService.class, "web端人脸上传：" + e.getMessage());
                }
            }
        });
        if (nullR!=null&&!nullR.isSuccess()) {
           return Json.getJsonResult(false, nullR.getMsg());
        }
        return Json.getJsonResult(true, jo);
    }
    /**
     * 批量上传
     */
    public JsonResult batchUploadFace(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws Exception {

        final String creatorid = userRedis.getUserId(request);
        //保存到临时文件夹
        if (file.getSize() > 104857600) {
            return Json.getJsonResult(false, "zip压缩包不能大与100MB，请分批上传");
        }
        final MultipartFile[] files = new MultipartFile[]{file};
        final List<String> filelist = FileHelper.SaveMultipartFile(files, WebConfig.getUploaddir(), "face");
        if (filelist.size() == 0) {
            return Json.getJsonResult(false, "文件保存失败");
        }

        new Thread(new Runnable() {
            @Override
            public void run() {
                dealPackage(filelist, creatorid);

				try {
					JSONObject info=new JSONObject();
					info.put("msgtype", 1000);
					info.put("msg", "人脸照片压缩包解压并处理已完成");
					WebSocketSets.getInstance().send("sysmsg", info.toJSONString());
				}catch(Exception ex) {
					Log.error(this.getClass(), ex.getMessage());
				}
            }
        }).start();
            syslogs.Write(request, "人脸建模", "打包上传");
        return Json.getJsonResult(true);
    }
    private final Logger logger = LoggerFactory.getLogger(IntoFaceService.class);

    /**
     *  检测照片是否符合
     */
    public R<Null> detectFace(String imagePath) throws Exception {
        // 加载动态库
        String currentDir = System.getProperty("user.dir");
        // 获取当前目录的父目录
        String parentDir = new File(currentDir).getParent();

        // 构建DLL文件的完整路径
        String dllPath = Paths.get(parentDir, "opencv/opencv_java490.dll").toString();
        logger.info("尝试加载的DLL路径是：" + dllPath);

        // 加载 DLL 文件
        System.load(dllPath);
        logger.info("DLL 文件加载成功: " + dllPath);

        // 加载人脸分类器
        String xmlFilePath = Paths.get(parentDir, "opencv/haarcascade_frontalface_alt.xml").toString();
        // 获取分类器文件路径
        CascadeClassifier faceDetector = new CascadeClassifier(xmlFilePath);

        if (faceDetector.empty()) {
            logger.error("无法加载人脸分类器：" + xmlFilePath);
            return R.fail("无法加载人脸分类器：" + xmlFilePath);
        }
        logger.info("人脸分类器加载成功：" + xmlFilePath);
        // 获取分类器文件路径
        String eyeXmlFilePath = Paths.get(parentDir, "opencv/haarcascade_eye.xml").toString();// 眼睛检测分类器路径
        CascadeClassifier eyeDetector = new CascadeClassifier(eyeXmlFilePath); // 加载眼睛检测器

        if (eyeDetector.empty()) {
            logger.error("无法加载眼睛分类器：" + eyeXmlFilePath);
            return R.fail("无法加载眼睛分类器：" + eyeXmlFilePath);
        }
        logger.info("分类器加载成功：" + xmlFilePath + " 和 " + eyeXmlFilePath);

        Mat image = Imgcodecs.imread(imagePath);

        MatOfRect faceDetections = new MatOfRect();
        // 调整 detectMultiScale 的参数以提高检测精度
        faceDetector.detectMultiScale(image, faceDetections, 1.1, 3, 0 | Objdetect.CASCADE_SCALE_IMAGE, new Size(100, 100), new Size());
        logger.info("检测到人脸数量：" + faceDetections.toArray().length);

        // 判断图像是否模糊
        Mat gray = new Mat();
        Imgproc.cvtColor(image, gray, Imgproc.COLOR_BGR2GRAY);
        Mat laplacian = new Mat();
        Imgproc.Laplacian(gray, laplacian, CvType.CV_64F);
        MatOfDouble mean = new MatOfDouble();
        MatOfDouble stddev = new MatOfDouble();
        Core.meanStdDev(laplacian, mean, stddev);
        double variance = stddev.get(0, 0)[0] * stddev.get(0, 0)[0];
        boolean isImageSharp = variance > 20; // 设定一个模糊的阈值
        logger.info("图像清晰度检测，方差值：" + variance + "，是否清晰：" + isImageSharp);

        if (!isImageSharp) {
            String errorMessage = "图像模糊，无法检测到合格的人脸。";
            logger.error(errorMessage);
            return R.fail(errorMessage);
        }

        int imageCenterX = image.width() / 2;
        int imageTopY = 0; // 图像的顶部位置
        int imageBottomY = image.height(); // 图像的底部位置

        boolean faceFound = false;

        for (Rect rect : faceDetections.toArray()) {
            int faceCenterX = rect.x + rect.width / 2;
            int faceTopY = rect.y;
            int faceBottomY = rect.y + rect.height;

            // 检查人脸是否完全包含在图像内
            boolean isFullyContained = faceTopY >= imageTopY && faceBottomY <= imageBottomY && rect.x >= 0 && (rect.x + rect.width) <= image.width();
            if (!isFullyContained) {
                String errorMessage = "人脸未完全包含在图像内，顶部位置：" + faceTopY + "，底部位置：" + faceBottomY;
                logger.error(errorMessage);
                return R.fail(errorMessage);
            }
            logger.info("人脸包含检测：顶部位置：" + faceTopY + "，底部位置：" + faceBottomY + "，是否完全包含：" + isFullyContained);

            // 检查人脸的宽高比是否正常（避免侧脸）
            double aspectRatio = (double) rect.width / rect.height;
            boolean isAspectRatioAcceptable = aspectRatio > 0.8 && aspectRatio < 1.2;
            if (!isAspectRatioAcceptable) {
                String errorMessage = "人脸宽高比异常，宽高比：" + aspectRatio;
                logger.error(errorMessage);
                return R.fail(errorMessage);
            }
            logger.info("人脸宽高比检测：宽高比：" + aspectRatio + "，是否正常：" + isAspectRatioAcceptable);

            // 检查眼睛位置
            Mat faceROI = gray.submat(rect);
            MatOfRect eyesDetections = new MatOfRect();
            eyeDetector.detectMultiScale(faceROI, eyesDetections, 1.1, 2, 0 | Objdetect.CASCADE_SCALE_IMAGE, new Size(30, 30), new Size());
            boolean isEyesDetected = eyesDetections.toArray().length >= 2;
            if (!isEyesDetected) {
                String errorMessage = "未检测到两个眼睛，检测到眼睛数量：" + eyesDetections.toArray().length;
                logger.error(errorMessage);
                return R.fail(errorMessage);
            }
            logger.info("眼睛检测：检测到眼睛数量：" + eyesDetections.toArray().length + "，是否检测到两个眼睛：" + isEyesDetected);

            // 在每一个识别出来并在中心区域的人脸周围画出一个方框
            Imgproc.rectangle(image, new Point(rect.x, rect.y), new Point(rect.x + rect.width, rect.y + rect.height), new Scalar(0, 255, 0), 3);
            logger.info("检测到合格的人脸，位置：({}, {})", faceCenterX, (faceTopY + faceBottomY) / 2);
            faceFound = true;
        }

        if (faceFound) {
            logger.info("检测到人脸，且符合所有条件。");
            // 返回成功信息
            return R.ok("检测到人脸，且符合所有条件。");
        } else {
            logger.info("未检测到符合条件的人脸。");
            // 返回失败信息
            return R.fail("未检测到符合条件的人脸。");
        }

    }
    /**
     * 返回批量上传结果
     *
     * @param isSuccess
     */
    private void uploadResult(String isSuccess) {
        try {
            WebSocketSets.getInstance().send("batchUpload", isSuccess);
        } catch (Exception e) {
            Log.error(IntoFaceService.class, e.getMessage());
        }
    }

    private String getFileNameMark(String filename, String batchimportfacenamerule) {
    	if(batchimportfacenamerule.equals("2")) {
    		filename=filename.replace("-", "_").replace("—", "_");
    		return filename.split("_")[0];
    	}
    	return filename;
    }

    /**
     * 处理压缩包
     *
     * @param filelist
     * @param creatorid
     */
    private void dealPackage(List<String> filelist, String creatorid) {
        try {
            //压缩包解压
            String filepath = filelist.get(0);
            List<String> imgList = ZipUtils.deCompress(WebConfig.getUploaddir() + filepath, WebConfig.getUploaddir() + "face");
            String batchimportfacenamerule=syscfg.get("batchimportfacenamerule");

            //获取图片名
            List<String> needDelUidList = new ArrayList<String>();
            Set<String> code = new HashSet<String>();
            Map<String, String> imagePath = new HashMap<String, String>();
            if (!ComHelper.IsRemotResource(WebConfig.getDomain(), WebConfig.getResourceDomain())) {
                for (String oneImg : imgList) {
                	oneImg = oneImg.replace("\\", "/");
                    String uid = UUID.randomUUID().toString();
                    String newfilename=ComHelper.getResourcePath(WebConfig.getUploaddir(), "/face/" + uid + ".jpg");
                    String imageName =FilenameUtils.getBaseName(oneImg);
                    String codestr=getFileNameMark(imageName,batchimportfacenamerule);
                    code.add(codestr);
                    File oldfile = new File(oneImg);
                    File newfile = new File(newfilename);
                    oldfile.renameTo(newfile);
                    imagePath.put(codestr, newfilename);
                }
            } else {
                for (String oneImg : imgList) {
                	oneImg = oneImg.replace("\\", "/");
                    String uid = UUID.randomUUID().toString();
                    String newfilename=ComHelper.getResourcePath(WebConfig.getUploaddir(), "/face/" + uid + ".jpg");
                    String imageName =FilenameUtils.getBaseName(oneImg);
                    String codestr=getFileNameMark(imageName,batchimportfacenamerule);
                    code.add(codestr);

                    File oldfile = new File(oneImg);
                    File newfile = new File(newfilename);
                    oldfile.renameTo(newfile);

                    String imagebase64 = FileHelper.Image2Base64(newfilename);
                    String url = ComHelper.getResourceUrl(WebConfig.getResourceDomain(), "API/Face/SubmitFaceBase64");
                    Map<String, String> params = new HashMap<String, String>();
                    params.put("facedata", imagebase64);
                    params.put("faceuid", uid);
                    JsonResult rs = HttpRequest.HttpPost(url, params);
                    if (rs.isSuccess()) {
                        if (rs.getData().getBoolean("success")) {
                            imagePath.put(codestr, WebConfig.getUploaddir() + rs.getData().getString("msg"));
                        }
                    }
                    needDelUidList.add(uid);
                }
            }
            //根据图片名(code)查询tb_card_teachstudinfo，获取对应的uid
            String where = " find_in_set(code,'" + String.join(",", code) + "')>0 ";

            Log.info(IntoFaceService.class, "导入人脸人员编号："+where);
            //看是否开启人脸建模校验
            JSONObject jsonObject = dbService.QueryJSONObject("select cvalue from tb_sys_config where cname = ?", "FaceModelingVerify");
            String flag = null;
            if (jsonObject==null){
                flag="0";
            }else {
                flag = jsonObject.getString("cvalue");
            }
            JSONArray uidList = dbService.QueryJSONArray("uid,code", "tb_card_teachstudinfo", where, "uid asc", 0, 0);
            for (int i = 0; i < uidList.size(); i++) {
                JSONObject teachstudinfo = uidList.getJSONObject(i);
                String infoid = teachstudinfo.getString("uid");
                String infoCode = teachstudinfo.getString("code");
                String sql = "SELECT uid as faceid,imgpath FROM tb_face_infoface where infoid='" + infoid + "';";
                JSONArray facelist = dbService.QueryList(sql);
                if (facelist.size() == 0) {
                    String imagpath = null;
                    try {
                        imagpath = WebConfig.getUploaddir()+imagePath.get(infoCode).substring(imagePath.get(infoCode).indexOf("face"));
                    } catch (Exception e) {
                        logger.error("异常人员信息{}",infoCode);
                        e.printStackTrace();
                        continue;
                    }
                    R<Null> nullR = null;
                    if ("1".equals(flag)){
                        nullR = detectFace(imagpath);
                    }
                    String photoQuality = "合格";
                    int status = 1;
                    if (nullR!=null&&!nullR.isSuccess()) {
                        photoQuality = nullR.getMsg();
                        status = 0;
                    }
                    try {
                        ImageUtil.changeImageWH(imagePath.get(infoCode), 800, 800);
                    } catch (IOException e) {
                        Log.error(this.getClass(), infoCode);
                    }
                    String insertOne = "INSERT INTO tb_face_infoface (uid,infoid,imgpath,status,creatorid,createdate,photo_quality) VALUES (?,?,?,?,?,now(),?)";
                    String uid = FilenameUtils.getBaseName(imagePath.get(infoCode));
                    dbService.excuteSql(insertOne, uid, infoid, "/" + imagePath.get(infoCode).substring(imagePath.get(infoCode).indexOf("face")),status, creatorid,photoQuality);

                } else {
                	JSONObject item=facelist.getJSONObject(0);
                    final String faceid = item.getString("faceid");
                    final String imgpath = item.getString("imgpath");

                    //判断是否存在下载失败
                    sql = "select uid from tb_face_infoface where uid='" + faceid + "' and status=0 ";
                    JSONArray faceauthorizelist = dbService.QueryList(sql);
                    if (faceauthorizelist.size() > 0) {
                        //1. 覆盖照片文件
                    	//存在：删除旧照片，然后获取新照片改名至旧照片名
                        //判断是否远程服务器，true:删除本地，false:删除远程服务器
                        if (!ComHelper.IsRemotResource(WebConfig.getDomain(), WebConfig.getResourceDomain())) {
                        	File oldImg = new File(ComHelper.getResourcePath(WebConfig.getUploaddir(), imgpath));
                            //判断是否存在旧照片
                            if (oldImg.exists()) {
                            	oldImg.delete();
                                new File(imagePath.get(infoCode)).renameTo(oldImg);
                            }
                        } else {
                            String url = ComHelper.getResourceUrl(WebConfig.getResourceDomain(), "API/Face/oldToNewFace");
                            Map<String, String> params = new HashMap<String, String>();
                            params.put("oldface", ComHelper.getResourcePath(WebConfig.getUploaddir(), imgpath));
                            params.put("newface", imagePath.get(infoCode));
                            JsonResult rs = HttpRequest.HttpPost(url, params);
                            if (!rs.isSuccess()) {
                                Log.error(IntoFaceService.class, rs.getMsg());
                            }
                        }
                        //照片质量不合格回写
                        String imagpath = ComHelper.getResourcePath(WebConfig.getUploaddir(), imgpath);
                        R<Null> nullR = null;
                        if ("1".equals(flag)){
                            nullR = detectFace(imagpath);
                        }
                        String photoQuality = "合格";
                        int status = 1;
                        if (nullR!=null&&!nullR.isSuccess()) {
                            photoQuality = nullR.getMsg();
                            status = 0;
                        }
                        //2. 更新iscreate,发送删除消息
                        String updateIscreate = "UPDATE tb_face_infoface SET iscreate=0,iscreatesk=0,createdate=now(),status = ?,photo_quality = ?  WHERE infoid='" + infoid + "'";
                        dbService.excuteSql(updateIscreate,status, photoQuality);
                        new Thread() {
                            public void run() {
                                try {
                                    JSONObject data = new JSONObject();
                                    data.put("uid", faceid);
                                    RedisMessageEntity rme = new RedisMessageEntity();
                                    rme.setCmd("visitorfacedel");
                                    rme.setData(data);
                                    rme.setDevtype(4);
                                    rme.setServer_uid("");
                                    redismsg.VisitorFaceDel(rme);
                                } catch (Exception e) {
                                    Log.error(IntoFaceService.class, e.getMessage());
                                }
                            }
                        }.start();
                        if (status == 1) {
                            //3. 更新人脸下载状态为待下载
                            String upFacestatus = "UPDATE tb_face_authorizeface SET infodownstatus=0,timedownstatus=0,downnum=0,downmsg='' WHERE infoid=?";
                            dbService.excuteSql(upFacestatus, infoid);
                        }
                    } else {
                    	//如果不存在下载失败的照片，则不覆盖原照片
                        if (!ComHelper.IsRemotResource(WebConfig.getDomain(), WebConfig.getResourceDomain())) {
                            File oldImg = new File(imagePath.get(infoCode));
                            oldImg.delete();
                        } else {
                            String url = ComHelper.getResourceUrl(WebConfig.getResourceDomain(), "/API/Face/deleteface");
                            Map<String, String> params = new HashMap<String, String>();
                            params.put("facepath", imagePath.get(infoCode));
                            HttpRequest.HttpPost(url, params);
                        }
                    }
                }
            }
            FileUtils.forceDelete(new File(WebConfig.getUploaddir() + filepath)); //删除压缩包
            JSONObject jo = new JSONObject();
            jo.put("end", "success");
            uploadResult(jo.toString());
            new Thread(new Runnable() {
                @Override
                public void run() {
                    //向设备通知授权
                    redismsg.NotifyFaceDevice();
                }
            }).start();

            //当路径不一致时上传完照片后删除本地照片
            if (ComHelper.IsRemotResource(WebConfig.getDomain(), WebConfig.getResourceDomain())) {
                for (String uid : needDelUidList) {
                    String imgpath = ComHelper.getResourcePath(WebConfig.getUploaddir(), "/face/" + uid + ".jpg");
                    File file = new File(imgpath);
                    if (file.exists()) {
                        file.delete();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject jo = new JSONObject();
            jo.put("end", "false");
            jo.put("errorMsg", e.getMessage());
            uploadResult(jo.toString());
            Log.error(IntoFaceService.class, e.getMessage());
        }
    }


    public JsonResult delImg(HttpServletRequest request, HttpServletResponse response, final String uid) throws IOException {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "uid为空");
        }
        //删除本地图片
        String delFiled = "SELECT imgpath FROM tb_face_infoface WHERE uid= '" + uid + "'";
        JSONArray delFiledja = dbService.QueryList(delFiled);
        if(delFiledja.size()==0) {
        	return Json.getJsonResult(false, "照片信息不存在！");
        }
        String facepath = delFiledja.getJSONObject(0).getString("imgpath");
        if (!ComHelper.IsRemotResource(WebConfig.getDomain(), WebConfig.getResourceDomain())) {
            String imgpath = ComHelper.getResourcePath(WebConfig.getUploaddir(), facepath);
            File file = new File(imgpath);
            if (file.exists()) {
                file.delete();
            }
        } else {
            String url = ComHelper.getResourceUrl(WebConfig.getResourceDomain(), "/API/Face/deleteface");
            Map<String, String> params = new HashMap<String, String>();
            params.put("facepath", facepath);
            HttpRequest.HttpPost(url, params);
        }
        //删除tb_face_infoface记录
        String sqlFace = "DELETE FROM tb_face_infoface WHERE uid = '" + uid + "'";
        dbService.excuteSql(sqlFace);



        JSONArray fad = dbService.QueryJSONArray("infodownstatus", "tb_face_authorizeface", "faceid = '" + uid + "'", "createdate DESC", 0, 0);
        String msg = "del";
        if (fad.size() > 0) {
            JSONObject getDownStatus = fad.getJSONObject(0);
            String downstatus = getDownStatus.getString("infodownstatus");
            if ("0".equals(downstatus) || "2".equals(downstatus)) {
                String delfad = "DELETE FROM tb_face_authorizeface WHERE faceid = '" + uid + "'";
                dbService.excuteSql(delfad);
            } else {
                String sql = "UPDATE tb_face_authorizeface SET infodownstatus=3,downnum=0,downmsg='' WHERE faceid= '" + uid + "'";
                dbService.excuteSql(sql);
                msg = "update";
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    //向设备通知授权
                    JSONObject data = new JSONObject();
                    data.put("uid", uid);
                    RedisMessageEntity rme = new RedisMessageEntity();
                    rme.setCmd("visitorfacedel");
                    rme.setData(data);
                    rme.setDevtype(4);
                    rme.setServer_uid("");
                    redismsg.VisitorFaceDel(rme);
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        Log.error(IntoFaceService.class, e.getMessage());
                    }

                    redismsg.NotifyFaceDevice();
                }
            }).start();
        }


        syslogs.Write(request, "人脸建模", String.format("删除人脸:%s", uid));

        return Json.getJsonResult(true,msg);
    }

    public JsonResult clearFace(HttpServletRequest request, HttpServletResponse response, final String uid) {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "uid为空");
        }



        String msg = "清除成功！！";

        JSONArray faceStatusList = dbService.QueryJSONArray("infodownstatus,faceid", "tb_face_authorizeface", "infoid IN('" + String.join("','", uid.split(",")) + "')", "createdate DESC", 0, 0);
        if (faceStatusList.size() > 0) {
            List<String> delList = new ArrayList<String>();
            List<String> updateList = new ArrayList<String>();
            for (int i = 0; i < faceStatusList.size(); i++) {
                JSONObject jo = faceStatusList.getJSONObject(i);
                String downstatus = jo.getString("infodownstatus");
                String faceid = jo.getString("faceid");
                if ("0".equals(downstatus) || "2".equals(downstatus)) {
                    delList.add(faceid);
                } else {
                    updateList.add(faceid);
                }
            }
            String delfad = "DELETE FROM tb_face_authorizeface WHERE faceid IN('" + String.join("','", delList) + "')";
            dbService.excuteSql(delfad);
            String updatefad = "UPDATE tb_face_authorizeface SET infodownstatus=3,downnum=0,downmsg='' WHERE faceid IN('" + String.join("','", updateList) + "')";
            dbService.excuteSql(updatefad);
            if (updateList.size() > 0) {
                msg = "清除成功，";
                return Json.getJsonResult(false,"人脸授权未删除！无法清理照片！ 该人员设备上有 " + updateList.size() + " 张照片待删除,正在清理请稍后再试");
            }
        }
        dbService.excuteSql("update tb_face_authorize_group_access set downstatus=3,downnum=0 where infoid IN('" + String.join("','", uid.split(",")) + "') and downstatus in (1,5,6)");
        dbService.excuteSql("DELETE FROM tb_face_authorize_group_access WHERE infoid IN('" + String.join("','", uid.split(",")) + "') and downstatus in (0,2)");

        //删除本地照片
        String delFiled = "SELECT imgpath FROM tb_face_infoface WHERE infoid IN('" + String.join("','", uid.split(",")) + "')";
        JSONArray delFiledja = dbService.QueryList(delFiled);
        if (delFiledja.size() != 0) {
            StringBuilder sb = new StringBuilder();
            for (int j = 0; j < delFiledja.size(); j++) {
                String facepath = delFiledja.getJSONObject(j).getString("imgpath");
                sb.append(facepath).append(",");
            }
            sb.deleteCharAt(sb.length() - 1);

            if (!ComHelper.IsRemotResource(WebConfig.getDomain(), WebConfig.getResourceDomain())) {
                String[] split = sb.toString().split(",");
                for (String path : split) {
                    String imgpath = ComHelper.getResourcePath(WebConfig.getUploaddir(), path);
                    File file = new File(imgpath);
                    if (file.exists()) {
                        file.delete();
                    }
                }
            } else {
                String url = ComHelper.getResourceUrl(WebConfig.getResourceDomain(), "/API/Face/deleteface");
                Map<String, String> params = new HashMap<String, String>();
                params.put("facepath", sb.toString());
                HttpRequest.HttpPost(url, params);
            }
        }
        String delfi = "DELETE FROM tb_face_infoface WHERE infoid IN('" + String.join("','", uid.split(",")) + "')";
        dbService.excuteSql(delfi);

        new Thread(new Runnable() {

            private final Object lock = new Object();

            @Override
            public void run() {
                //向设备通知授权
                synchronized (lock) {
                    JSONObject data = new JSONObject();
                    data.put("uid", uid);
                    RedisMessageEntity rme = new RedisMessageEntity();
                    rme.setCmd("visitorfacedel");
                    rme.setData(data);
                    rme.setDevtype(4);
                    rme.setServer_uid("");
                    redismsg.VisitorFaceDel(rme);
                    try {
//                    Thread.sleep(2000);
                        lock.wait(2000);
                    } catch (InterruptedException e) {
                        Log.error(IntoFaceService.class, e.getMessage());
                    }
                    redismsg.NotifyFaceDevice();
                }
            }
        }).start();
        syslogs.Write(request, "人脸建模", String.format("清空人脸:%s", uid));
        return Json.getJsonResult(true, msg);
    }


    public JsonResult SubmitBase64Face(final HttpServletRequest request, HttpServletResponse response, final String infoid, String faceuid, String imgbase64) throws Exception {
        final StringBuffer facepath = new StringBuffer();

        final StringBuffer facefilename = new StringBuffer();
        facefilename.append(faceuid);
        if (StringUtils.isBlank(facefilename)) {
            facefilename.delete(0, facefilename.length());
            facefilename.append(UUID.randomUUID().toString());
        }
        String domain = WebConfig.getDomain();
        String ResourceDomain = WebConfig.getResourceDomain();
        if (!ComHelper.IsRemotResource(domain, ResourceDomain)) {
            facepath.append(FileHelper.Base64ToImageFile(imgbase64, WebConfig.getUploaddir(), "face", String.format("%s.jpg", facefilename)));
        } else {
            String url = ComHelper.getResourceUrl(ResourceDomain, "/API/Face/SubmitFaceBase64");
            Map<String, String> params = new HashMap<String, String>();
            params.put("facedata", imgbase64);
            params.put("faceuid", facefilename.toString());
            JsonResult rs = HttpRequest.HttpPost(url, params);
            if (rs.isSuccess()) {
                if (rs.getData().getBoolean("success")) {
                    facepath.append(rs.getData().getString("msg"));
                }
            }
        }
        if (StringUtils.isBlank(facepath)) {
            return Json.getJsonResult(true, "照片上传失败");
        }

        final String creatorid = userRedis.getUserId(request);
        if (StringUtils.isBlank(faceuid)) {
            int count = dbService.getCount("tb_face_infoface", "infoid = '" + infoid + "'");
            if (count >= 1) {
                return Json.getJsonResult(true, "上传失败，最多只能上传1张照片");
            }

            dbService.excuteTransaction(new CallbackTransaction() {
                @Override
                public void execute(Connection connection) throws SQLException {
                    String sql = "INSERT INTO tb_face_infoface (uid,infoid,imgpath,status,creatorid,createdate) VALUES ('" + facefilename.toString() + "','" + infoid + "','" + facepath.toString() + "',1,'" + creatorid + "',now())";
                    PreparedStatement ps = connection.prepareStatement(sql);
                    ps.executeUpdate();
					ps.close();

                    syslogs.Write(request, "人脸建模", String.format("上传人脸:%s", facefilename.toString()));
                }
            });

        } else {
            dbService.excuteTransaction(new CallbackTransaction() {
                @Override
                public void execute(Connection connection) throws SQLException {
                    String updateSql = "UPDATE tb_face_infoface SET imgpath='" + facepath.toString() + "' WHERE uid= '" + facefilename.toString() + "'";
                    PreparedStatement ps = connection.prepareStatement(updateSql);
                    ps.executeUpdate();
					ps.close();


                    String updateFad = "UPDATE tb_face_authorizeface SET infodownstatus = 5,downnum=0,downmsg='' WHERE faceid = '" + facefilename.toString() + "' AND infodownstatus IN(1,3,5)";
                    ps = connection.prepareStatement(updateFad);
                    ps.executeUpdate();
					ps.close();


                    syslogs.Write(request, "人脸建模", String.format("修改人脸:%s", facefilename.toString()));
                }
            });
        }

        JSONObject jo = new JSONObject();
        jo.put("uid", facefilename.toString());
        jo.put("imgurl", facepath.toString());
        return Json.getJsonResult(true, jo);
    }

    public String ExportInfoFace(HttpServletRequest request, HttpServletResponse response, String orgcode, String key, String infotype, boolean viewchild, String ishasmodel, String property) throws IOException {
        JsonData result = getPropleList(request, response,"-1", orgcode, key, infotype, viewchild, ishasmodel, property,false, 0, 0);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        if ("1".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        } else if ("2".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 17);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "性别");
        cell.put("datakey", "sex");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "卡号");
        cell.put("datakey", "card");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属单位");
        cell.put("datakey", "orgname");
        cell.put("size", 25);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "人脸采集数");
        cell.put("datakey", "total");
        cell.put("size", 15);
        cells.add(cell);

        String title= "人脸采集情况";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename =  title+format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp",filename,title);

        return filepath;
    }

    public String ExportInfoFaceImage(HttpServletRequest request, HttpServletResponse response, String orgcode, String key, String infotype, boolean viewchild, String ishasmodel, String property) throws IOException {
        JsonData result = getPropleList(request, response, "-1",orgcode, key, infotype, viewchild, ishasmodel, property,true, 0, 0);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray data = result.getData();
        String newFilePath = WebConfig.getUploaddir()+"tempimage";
        // 创建File对象
        File folder = new File(newFilePath);
        // 检查文件夹是否存在
        if (!folder.exists()) {
            // 文件夹不存在，创建文件夹
            folder.mkdirs(); // 使用mkdirs()可以递归创建文件夹
        }
        deleteFolderContents(folder);
        if (data.size()>0){
            for (int i = 0; i < data.size(); i++) {
                String imgpath = data.getJSONObject(i).getString("imgpath");
                String name = data.getJSONObject(i).getString("name");
                String code = data.getJSONObject(i).getString("code");
                zhuanghua(WebConfig.getUploaddir()+imgpath,WebConfig.getUploaddir()+"tempimage/"+code+" "+name+".jpg");
            }
        }

        // 声明要输出的压缩包文件路径
        String zipFilePath = "faceimagezip/images.zip";
        String FilePath = WebConfig.getUploaddir()+"faceimagezip";
        // 创建File对象
        File folder1 = new File(FilePath);
        File[] files = folder.listFiles();
        // 检查文件夹是否存在
        if (!folder1.exists()) {
            // 文件夹不存在，创建文件夹
            folder1.mkdirs(); // 使用mkdirs()可以递归创建文件夹
        }
        // 创建 ZipOutputStream 对象
        try (FileOutputStream fos = new FileOutputStream(WebConfig.getUploaddir()+zipFilePath);
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            // 遍历图片文件数组
            for (File imageFile : files) {
                // 创建 ZipEntry 对象，设置 entry 名称为文件名
                ZipEntry entry = new ZipEntry(imageFile.getName());
                // 将 entry 添加到压缩包
                zos.putNextEntry(entry);

                // 读取图片文件内容并写入到压缩包中
                try (FileInputStream fis = new FileInputStream(imageFile)) {
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = fis.read(buffer)) > 0) {
                        zos.write(buffer, 0, length);
                    }
                }
                // 关闭当前 entry
                zos.closeEntry();
            }

            // 完成压缩并关闭流
            zos.finish();
            System.out.println("压缩完成：" + zipFilePath);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return "/"+zipFilePath;
    }

    public void zhuanghua(String originalFile,String renamedFile){
        // 原始图片文件路径
        File originalFileImage = new File(originalFile);
        // 新的图片文件路径
        File renamedFileImage = new File(renamedFile);

        // 读取原始图片文件
        try (FileInputStream fis = new FileInputStream(originalFile);
             FileOutputStream fos = new FileOutputStream(renamedFile)) {
            // 创建缓冲区
            byte[] buffer = new byte[1024];
            int length;
            // 读取原始图片文件内容并写入到新的图片文件中
            while ((length = fis.read(buffer)) > 0) {
                fos.write(buffer, 0, length);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void deleteFolderContents(File folder) {
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    // 递归删除子文件夹
                    deleteFolderContents(file);
                }
                // 删除文件或空文件夹
                file.delete();
            }
        }
    }

    /**
     * 获取权限组授权人数
     */
    public Integer getFaceAuthorizeCount(String infoid) {
        //return dbService.getCount("tb_face_authorize_group_access", "infoid = '" + infoid + "' and downstatus = 1");
        return dbService.getCount("tb_face_authorize_group_access", "infoid = '" + infoid + "'");
    }

    /**
     * 获取人脸授权人数
     */
    public Integer getFaceCount(String infoid) {
        //return dbService.getCount("tb_face_authorizeface", "infoid = '" + infoid + "' and infodownstatus = 1 ");
        return dbService.getCount("tb_face_authorizeface", "infoid = '" + infoid + "'");
    }

    /**
     * 清除指纹
     * @param request
     * @param response
     * @param uid
     * @return
     */
    public JsonResult clearFinger(HttpServletRequest request, HttpServletResponse response, String uid) {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "uid为空");
        }

        String msg = "清除成功！！";

        JSONArray faceStatusList = dbService.QueryJSONArray("infodownstatus,infoid", "tb_face_authorizeface", "infoid IN('" + String.join("','", uid.split(",")) + "')", "createdate DESC", 0, 0);
        if (faceStatusList.size() > 0) {
            List<String> updateList = new ArrayList<String>();
            for (int i = 0; i < faceStatusList.size(); i++) {
                JSONObject jo = faceStatusList.getJSONObject(i);
                String downstatus = jo.getString("infodownstatus");
                String infoid = jo.getString("infoid");
                if ("1".equals(downstatus)) {
                    updateList.add(infoid);
                }
            }
            String delfad = "DELETE FROM tb_finger_infofinger WHERE infoid IN('" +  String.join(    "','", uid.split(",")) + "')";
            dbService.excuteSql(delfad);
            String updatefad = "UPDATE tb_face_authorizeface SET infodownstatus = 5 ,timedownstatus = 5,fingerdownstatus=5,downnum=0,downmsg='' WHERE infoid IN('" + String.join("','", updateList) + "')";
            dbService.excuteSql(updatefad);
        }
        syslogs.Write(request, "人脸建模", String.format("清空指纹:%s", uid));
        return Json.getJsonResult(true, msg);

    }
}
