package com.ymiots.campusos.service.face;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.config.redis.RedisClient;
import com.ymiots.campusos.entity.card.TeachStudInfo;
import com.ymiots.campusos.entity.dev.AccessController;
import com.ymiots.campusos.entity.face.FaceRecordRow;
import com.ymiots.campusos.util.HttpRequestUtils;
import com.ymiots.framework.common.*;
import com.ymiots.framework.utils.excel.Sheet;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Null;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Repository
public class FaceNotetService extends BaseService {

    //@Autowired
    //private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisClient redisClient;

    @Autowired
    private HttpRequestUtils httpRequestUtils;


    public R<List<FaceRecordRow>> getFaceNewNote(HttpServletRequest request, HttpServletResponse response, String key, String machineid, int start, int limit, String startdate, String enddate, String infoType) {

        List<FaceRecordRow> faceRecordList = new ArrayList<>();
        String sql = "select devid devId,machineid machineId,facepath facePath,infoid infoId,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordTime,recordtype recordType from tb_face_record ";
        sql += "where  1=1 ";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(machineid)) {
            sql += " and machineid='" + machineid + "' ";
            where += " and machineid='" + machineid + "' ";
        }
        if (!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate)) {
            sql += " and recordtime between '" + startdate + "' and '" + enddate + "'";
            where += " and recordtime between '" + startdate + "' and '" + enddate + "'";
        }
        if (StringUtils.isNotEmpty(key)) {
            sql += "order by recordtime desc";
        } else {
            sql += "order by recordtime desc limit " + start + "," + limit;
        }
        //System.out.println("当前时间" + DateHelper.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        faceRecordList = dbService.queryList(sql, FaceRecordRow.class);
        //System.out.println(DateHelper.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (CollectionUtil.isEmpty(faceRecordList)) {
            return R.fail(new ArrayList<>());
        }
        String infoSQL = "select a.uid,a.name,a.code,getorgname(a.orgcode) orgCode,a.infolabel infoLabel,b.imgpath from tb_card_teachstudinfo a left join tb_face_infoface b on a.uid = b.infoid" +
                " where 1 = 1 ";
        if (StringUtils.isNotBlank(key)) {
            infoSQL += " and name like '" + key + "%' or code='" + key + "'";
        }
        if (StringUtils.isNotBlank(infoType)) {
            infoSQL += " and infotype = '" + infoType + "'";
        }
        List<TeachStudInfo> teachStudInfos = dbService.queryList(infoSQL, TeachStudInfo.class);
        //获取到人员信息
        Map<String, List<TeachStudInfo>> infoListMap = teachStudInfos.stream()
                .collect(Collectors.groupingBy(TeachStudInfo::getUid));
        //获取到设别信息
        String machineSQL = "select da.devname devName,da.uid,getareaname(da.areacode) as area,dd.inouttype inOutType from tb_dev_accesscontroller da " +
                "left join tb_dev_door dd on dd.controllerid = da.uid";
        if (StringUtils.isNotBlank(machineid)) {
            machineSQL += " where da.machineid = '" + machineid + "'";
        }
        List<AccessController> accessControllers = dbService.queryList(machineSQL, AccessController.class);
        Map<String, List<AccessController>> accessListMap = accessControllers.stream()
                .collect(Collectors.groupingBy(AccessController::getUid));
        HashMap<String, String> recordTypeMap = new HashMap<>();
        recordTypeMap.put("0", "人脸识别");
        recordTypeMap.put("1", "黑名单识别");
        recordTypeMap.put("2", "人证比对");
        recordTypeMap.put("3", "IC卡识别");
        recordTypeMap.put("4", "二维码识别");
        HashMap<String, String> inOutTypeMap = new HashMap<>();
        inOutTypeMap.put("1", "进入");
        inOutTypeMap.put("2", "出去");
        // 对 faceRecordList 进行筛选
        Set<String> validInfoIds = teachStudInfos.stream()
                .map(TeachStudInfo::getUid)
                .collect(Collectors.toSet());
        Set<String> validDevIds = accessControllers.stream()
                .map(AccessController::getUid)
                .collect(Collectors.toSet());
        //处理人脸识别表合并其他数据
        faceRecordList = faceRecordList.stream()
                .filter(record -> validInfoIds.contains(record.getInfoId()) && validDevIds.contains(record.getDevId()))
                .peek(i -> {
                    i.setRecordType(recordTypeMap.get(i.getRecordType()));
                    List<AccessController> controllers = accessListMap.get(i.getDevId());
                    if (CollectionUtil.isNotEmpty(controllers)) {
                        AccessController accessController = controllers.get(0);
                        i.setDevName(accessController.getDevName());
                        i.setArea(accessController.getArea());
                        i.setInOutType(inOutTypeMap.get(accessController.getInOutType()));
                    }
                    List<TeachStudInfo> studInfos = infoListMap.get(i.getInfoId());
                    if (CollectionUtil.isNotEmpty(studInfos)) {
                        TeachStudInfo teachStudInfo = studInfos.get(0);
                        i.setCode(teachStudInfo.getCode());
                        i.setName(teachStudInfo.getName());
                        i.setOrg(teachStudInfo.getOrgCode());
                        i.setInfoLabel(teachStudInfo.getInfoLabel());
                        i.setImgpath(teachStudInfo.getImgpath());
                    }
                }).collect(Collectors.toList());
        int count = 0;
        if (StringUtils.isNotBlank(key)) {
            count = faceRecordList.size();
        } else {
            count = dbService.getCount("tb_face_record", where);
        }
        return R.okPage(faceRecordList, count);
    }

    public void DeleteFaceRecordSleep(Date date) {
        String year = dbService.QueryJSONObject("SELECT NAME FROM tb_sys_dictionary WHERE groupcode = 'SYS0000065' AND CODE = 0").getString("NAME");
        // 获取当前日期
        Date currentDate = date;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.YEAR, -Integer.parseInt(year));
        Date downDate = calendar.getTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = dateFormat.format(downDate);
        JSONArray array = dbService.QueryList("select uid,facepath from tb_face_record_sleep where date_format(recordtime, '%Y-%m-%d') < '" + format + "'");
        if (array.size() > 0) {
            for (int i = 0; i < array.size(); i++) {
                String imgpath = ComHelper.getResourcePath(WebConfig.getUploaddir(), array.getJSONObject(i).getString("facepath"));
                File file = new File(imgpath);
                if (file.exists()) {
                    file.delete();
                }
            }
        }
        dbService.excuteSql("delete from tb_face_record_sleep where date_format(recordtime, '%Y-%m-%d') < '" + format + "'");
    }

    @Async
    public void cacheFaceRecordSum(String cacheKey, int totalRecords) {
        redisClient.setObject(cacheKey, totalRecords);
        redisClient.setExpire(cacheKey, 1, TimeUnit.HOURS);
    }

    @Async
    public void cacheFaceRecordPage(String cacheKey, List<FaceRecordRow> faceRecordList) {
        // 将 List 转换为 JSON 字符串
        String jsonArrayStr = JSON.toJSONString(faceRecordList);

        // 将 JSON 字符串转换为 JSONArray
        JSONArray jsonArray = JSON.parseArray(jsonArrayStr);
        redisClient.setJSONArray(cacheKey, jsonArray);
        redisClient.setExpire(cacheKey, 1, TimeUnit.HOURS);
    }

    public JsonData getFaceNote(HttpServletRequest request, HttpServletResponse response, String key, String machineid, int start, int limit, String startdate, String enddate) {
        String fields = "ct.name,ct.code,ac.devname,r.machineid,r.facedown,r.facepath,ff.imgpath,date_format(r.recordtime, '%Y-%m-%d %H:%i:%s') as recordtime,getareaname(ac.areacode) as area,getorgname(ct.orgcode) as org,r.tiwen ";
        String table = "tb_face_record r " +
                "INNER JOIN  tb_dev_accesscontroller ac ON r.devid=ac.uid " +
                "INNER JOIN (SELECT infoid,max(imgpath)imgpath FROM tb_face_infoface GROUP BY infoid) ff on ff.infoid=r.infoid " +
                "INNER JOIN tb_card_teachstudinfo ct  ON r.infoid=ct.uid ";
        String where = " 1=1 ";

        ArrayList<String> infoids = new ArrayList<String>();
        if (StringUtils.isNotBlank(key)) {
            String sqlInfo = "SELECT uid FROM tb_card_teachstudinfo WHERE name like '" + key + "%' or code='" + key + "'";
            JSONArray ja = dbService.QueryList(sqlInfo);
            if (!ja.isEmpty()) {
                for (int i = 0; i < ja.size(); i++) {
                    infoids.add(ja.getJSONObject(i).getString("uid"));
                }
            }
        }
        if (StringUtils.isNotBlank(machineid)) {
            where += " and r.machineid='" + machineid + "' ";
        }
        if (!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate)) {
            where += " and r.recordtime between '" + startdate + "' and '" + enddate + "'";
        }
        if (!infoids.isEmpty()) {
            where += " and r.infoid IN ('" + String.join("','", infoids) + "') ";
        }
        String order = ExtSort.Orderby(request, "r.recordtime desc");
        JsonData jd = dbService.QueryJsonData(fields, table, where, order, start, limit);
        return jd;
    }

    public R<Null> exportFaceNote(HttpServletRequest request, HttpServletResponse response, String key, String machineid, String startdate, String enddate) {
        String sql = "select devid devId,machineid machineId,infoid infoId,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordTime from tb_face_record ";
        sql += "where  1=1 ";
        if (StringUtils.isNotBlank(machineid)) {
            sql += " and machineid='" + machineid + "' ";
        }
        if (!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate)) {
            sql += " and recordtime between '" + startdate + "' and '" + enddate + "'";
        }
        sql += "order by recordtime";
        System.out.println("当前时间" + DateHelper.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        List<FaceRecordRow> faceRecordList = dbService.queryList(sql, FaceRecordRow.class);
        System.out.println(DateHelper.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (CollectionUtil.isEmpty(faceRecordList)) {
            return R.fail("无导出数据");
        }
        String infoSQL = "select uid,name,code,getorgname(orgcode) orgCode from tb_card_teachstudinfo";
        List<TeachStudInfo> teachStudInfos = dbService.queryList(infoSQL, TeachStudInfo.class);
        //获取到人员信息
        Map<String, List<TeachStudInfo>> infoListMap = teachStudInfos.stream()
                .collect(Collectors.groupingBy(TeachStudInfo::getUid));
        //获取到设别信息
        String machineSQL = "select devname devName,uid,getareaname(areacode) as area from tb_dev_accesscontroller";
        List<AccessController> accessControllers = dbService.queryList(machineSQL, AccessController.class);
        Map<String, List<AccessController>> accessListMap = accessControllers.stream()
                .collect(Collectors.groupingBy(AccessController::getUid));

        //处理人脸识别表合并其他数据
        List<FaceRecordRow> recordRows = faceRecordList.stream()
                .peek(i -> {
                    List<AccessController> controllers = accessListMap.get(i.getDevId());
                    if (CollectionUtil.isNotEmpty(controllers)) {
                        AccessController accessController = controllers.get(0);
                        i.setDevName(accessController.getDevName());
                        i.setArea(accessController.getArea());
                    }
                    List<TeachStudInfo> studInfos = infoListMap.get(i.getInfoId());
                    if (CollectionUtil.isNotEmpty(studInfos)) {
                        TeachStudInfo teachStudInfo = studInfos.get(0);
                        i.setCode(teachStudInfo.getCode());
                        i.setName(teachStudInfo.getName());
                        i.setOrg(teachStudInfo.getOrgCode());
                    }
                }).collect(Collectors.toList());

        LinkedHashMap<Integer, FaceRecordRow> map = new LinkedHashMap<>();
        Sheet<FaceRecordRow> sheet = new Sheet<>(map, FaceRecordRow.class);
        int row = 1;
        for (FaceRecordRow vo : recordRows) {
            map.put(row, vo);
            row++;
        }
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String extFilePath = "temp/人脸识别记录表" + format + ".xlsx";
        String filePath = WebConfig.getUploaddir() + extFilePath;
        com.ymiots.framework.utils.excel.ExcelUtil.write(sheet, filePath, "人脸识别记录表", 6, startdate, enddate);
        return R.ok(extFilePath);
    }


    public JsonData getFaceHistoryNote(HttpServletRequest request, HttpServletResponse response, String key, String machineid, int start, int limit, String startdate, String enddate) {
        String fields = "name,code,devname,machineid,facedown,facepath,imgpath,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordtime,area,org,tiwen ";
        String table = "tb_face_record_sleep ";
        String where = " 1=1 ";


        if (StringUtils.isNotBlank(key)) {
            where += " and (name like '%" + key + "%' or code = '" + key + "')";
        }
        if (StringUtils.isNotBlank(machineid)) {
            where += " and machineid='" + machineid + "' ";
        }
        if (!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate)) {
            where += " and recordtime between '" + startdate + "' and '" + enddate + "'";
        }

        String order = ExtSort.Orderby(request, "recordtime desc");
        JsonData jd = dbService.QueryJsonData(fields, table, where, order, start, limit);
        return jd;
    }

    public JsonData getFaceDeviceList(HttpServletRequest request, HttpServletResponse response, String serveruid, String key, int start, int limit) {
        String fields = "uid,devsn,machineid,devname";
        String table = "tb_dev_accesscontroller";
        String where = " devclass='9' ";
        if (!StringUtils.isBlank(serveruid) && !serveruid.equals("0")) {
            where += " and server_uid='" + serveruid + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (devname like '%" + key + "%' or machineid='" + key + "') ";
        }
        String orderby = ExtSort.Orderby(request, " machineid asc ");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public String ExportFaceNote(HttpServletRequest request, HttpServletResponse response, String key, String machineid, String startdate, String enddate) throws IOException {
        String fields = "name,code,devname,machineid,facedown,facepath,imgpath,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordtime,area,org,tiwen ";
        String table = "tb_face_record_sleep ";
        String where = " 1=1 ";


        if (StringUtils.isNotBlank(key)) {
            where += " and (name like '%" + key + "%' or code = '" + key + "')";
        }
        if (StringUtils.isNotBlank(machineid)) {
            where += " and machineid='" + machineid + "' ";
        }
        if (!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate)) {
            where += " and recordtime between '" + startdate + "' and '" + enddate + "'";
        }

        String order = ExtSort.Orderby(request, " recordtime desc");

        JsonData result = dbService.QueryJsonData("select " + fields + " from " + table + " where " + where + order);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "识别时间");
        cell.put("datakey", "recordtime");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 10);
        cells.add(cell);

        if ("1".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        } else if ("2".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "体温℃");
        cell.put("datakey", "tiwen");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属单位");
        cell.put("datakey", "org");
        cell.put("size", 30);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "设备");
        cell.put("datakey", "devname");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "machineid");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属区域");
        cell.put("datakey", "area");
        cell.put("size", 15);
        cells.add(cell);

        String title = "通行人脸识别记录";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename = title + format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp", filename, title);
        return filepath;
    }


    public String ExportHistoryFaceNote(HttpServletRequest request, HttpServletResponse response, String key, String machineid, String startdate, String enddate) throws IOException {

        String fields = "name,code,devname,machineid,facedown,facepath,imgpath,date_format(recordtime, '%Y-%m-%d %H:%i:%s') as recordtime,area,org,tiwen ";
        String table = "tb_face_record_sleep ";
        String where = " 1=1 ";


        if (StringUtils.isNotBlank(key)) {
            where += " and (name like '%" + key + "%' or code = '" + key + "')";
        }
        if (StringUtils.isNotBlank(machineid)) {
            where += " and machineid='" + machineid + "' ";
        }
        if (!StringUtils.isBlank(startdate) && !StringUtils.isBlank(enddate)) {
            where += " and recordtime between '" + startdate + "' and '" + enddate + "'";
        }

        String order = ExtSort.Orderby(request, " ORDER BY recordtime desc");

        JsonData result = dbService.QueryJsonData("select " + fields + " from " + table + " where " + where + order);
        if (!result.isSuccess()) {
            return "";
        }
        JSONArray list = result.getData();
        JSONArray cells = new JSONArray();
        JSONObject cell = new JSONObject();

        cell = new JSONObject();
        cell.put("name", "识别时间");
        cell.put("datakey", "recordtime");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "姓名");
        cell.put("datakey", "name");
        cell.put("size", 10);
        cells.add(cell);

        if ("1".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "学工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        } else if ("2".equals(WebConfig.getApptype())) {
            cell = new JSONObject();
            cell.put("name", "工号");
            cell.put("datakey", "code");
            cell.put("size", 15);
            cells.add(cell);
        }

        cell = new JSONObject();
        cell.put("name", "体温℃");
        cell.put("datakey", "tiwen");
        cell.put("size", 10);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属单位");
        cell.put("datakey", "org");
        cell.put("size", 30);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "设备");
        cell.put("datakey", "devname");
        cell.put("size", 20);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "机号");
        cell.put("datakey", "machineid");
        cell.put("size", 15);
        cells.add(cell);

        cell = new JSONObject();
        cell.put("name", "所属区域");
        cell.put("datakey", "area");
        cell.put("size", 35);
        cells.add(cell);

        String title = "通行人脸识别记录";
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String filename = title + format + ".xlsx";

        String filepath = ExcelUtil.ExportExcel(list, cells, WebConfig.getUploaddir(), "temp", filename, title);
        return filepath;
    }

    public JsonData pullFaceRecords(HttpServletRequest request, HttpServletResponse response, String machineids,String startdate,String enddate) throws IOException {
        List<String> machineidsList = new ArrayList<>();
        if(StringUtils.isEmpty(machineids)){
            machineidsList = dbService.queryFields("select machineid from tb_dev_accesscontroller where devstatus = 1 and devmodel=53",String.class);
        }else {
            machineidsList = Arrays.stream(machineids.split(",")).collect(Collectors.toList());
        }
        for (String machineid : machineidsList) {
            List<NameValuePair> paramsList = new ArrayList<>();
            paramsList.add(new BasicNameValuePair("deviceNo", machineid));
            paramsList.add(new BasicNameValuePair("startTime", startdate));
            paramsList.add(new BasicNameValuePair("endTime", enddate));
            R r = httpRequestUtils.doPostRequest("http://localhost:2024/card/getAccessRecords", null, paramsList, R.class);
            if (r.isSuccess()){
                Log.info(FaceNotetService.class,"人脸识别（DH系列）机号："+machineid+" ,时间："+startdate+"-"+enddate+" 成功");
            }else {
                Log.error(FaceNotetService.class,"人脸识别（DH系列）机号："+machineid+" ,时间："+startdate+"-"+enddate+" 失败");
            }
        }
        return Json.getJsonData(true);
        
    }
}
