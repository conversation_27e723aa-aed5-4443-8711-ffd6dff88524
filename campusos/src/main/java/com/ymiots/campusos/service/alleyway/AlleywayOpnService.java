package com.ymiots.campusos.service.alleyway;

import com.ymiots.campusos.dao.AlleywayOpnDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ymiots.campusos.common.constant.alleyway.AlleywayTableConstant.*;

/**
 * 所有门禁统一操作
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Service
public class AlleywayOpnService {

    @Autowired
    private AlleywayOpnDao alleywayOpnDao;

    public void delPerson(String infoId) {
        alleywayOpnDao.delDirectByInfoId(AP_OFFLINE_AUTHORIZE, infoId);
        alleywayOpnDao.delDirectByInfoId(AP_OFFLINE_GROUP_ACCESS, infoId);
        alleywayOpnDao.delDownByInfoId(AP_OFFLINE_NAMELIST, infoId);
        alleywayOpnDao.delDirectByInfoId(AP_OFFLINE_INFOINDEX, infoId);
        alleywayOpnDao.delDirectByInfoId(AUTHORIZE, infoId);
        alleywayOpnDao.delDownByInfoId(KRE_AUTHORIZE, infoId);
        alleywayOpnDao.delDirectByInfoId(KRE_GROUP_ACCESS, infoId);
        alleywayOpnDao.delDownByInfoId(OFFLINE_AUTHORIZE, infoId);
        alleywayOpnDao.delDownByInfoId(OFFLINE_AUTHORIZE_TASKLIST, infoId);
        alleywayOpnDao.delDirectByInfoId(OFFLINE_GROUP_ACCESS, infoId);
        alleywayOpnDao.delDirectByInfoId(YUN_OFFLINE_AUTHORIZE, infoId);
        alleywayOpnDao.delDirectByInfoId(YUN_OFFLINE_GROUP_ACCESS, infoId);
        alleywayOpnDao.delDownByInfoId(YUN_OFFLINE_NAMELIST, infoId);
        alleywayOpnDao.delDirectByInfoId(YUN_OFFLNE_INFO_INDEX, infoId);
        alleywayOpnDao.delDownByInfoIdSTATUS(ELECATOR_T_ACCESS, infoId);
    }

    public boolean selPerson(String infoId) {
        Integer integer = alleywayOpnDao.selAuthorizeCount(AP_OFFLINE_GROUP_ACCESS, infoId);
        if (integer > 0) {
            return false;
        }
        Integer authorizeCount = alleywayOpnDao.selAuthorizeCount(AP_OFFLINE_AUTHORIZE_DEV, infoId);
        if (authorizeCount> 0) {
            return false;
        }
        Integer integer1 = alleywayOpnDao.selAuthorizeCount(AP_OFFLINE_NAMELIST_DEV, infoId);
        if (integer1 > 0) {
            return false;
        }
        Integer integer3 = alleywayOpnDao.selAuthorizeCount(AUTHORIZE_DEV, infoId);
        if (integer3 > 0) {
            return false;
        }
        Integer integer4 = alleywayOpnDao.selAuthorizeCount(KRE_AUTHORIZE_DEV, infoId);
        if (integer4 > 0) {
            return false;
        }
        Integer integer5 = alleywayOpnDao.selAuthorizeCount(KRE_GROUP_ACCESS, infoId);
        if (integer5 > 0) {
            return false;
        }
        Integer integer6 = alleywayOpnDao.selAuthorizeCount(OFFLINE_AUTHORIZE_DEV, infoId);
        if (integer6 > 0) {
            return false;
        }
        Integer integer7 = alleywayOpnDao.selAuthorizeCount(OFFLINE_AUTHORIZE_TASKLIST_DEV, infoId);
        if (integer7 > 0) {
            return false;
        }
        Integer integer2 = alleywayOpnDao.selAuthorizeCount(OFFLINE_GROUP_ACCESS, infoId);
        if (integer2 > 0) {
            return false;
        }
        Integer integer8 = alleywayOpnDao.selAuthorizeCount(YUN_OFFLINE_AUTHORIZE_DEV, infoId);
        if (integer8 > 0) {
            return false;
        }
        Integer integer9 = alleywayOpnDao.selAuthorizeCount(YUN_OFFLINE_GROUP_ACCESS, infoId);
        if (integer9 > 0) {
            return false;
        }
        Integer integer10 = alleywayOpnDao.selAuthorizeCount(YUN_OFFLINE_NAMELIST_DEV, infoId);
        if (integer10 > 0) {
            return false;
        }
        return true;
    }
}
