package com.ymiots.campusos.service.alleyway;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-01-27 10:30
 * @since 2022-01-27 10:30
 */
@Service
public class KreGroupService extends BaseService {

    @Autowired
    private UserRedis userRedis;

    @Autowired
    SysLogsService syslogs;

    @Autowired
    UserRedis userredis;

    public JsonData getNEWGroup(HttpServletRequest request, HttpServletResponse response, String key,String areacode, int start, int limit){
        String fields = "uid,name ";
        String table = "tb_alleyway_kre_group ";
        String where = " 1=1 ";
        if(StringUtils.isEmpty(areacode)){
            return Json.getJsonData(true);
        }
        if (StringUtils.isNotBlank(key)) {
            where += " AND name like '%" + key + "%'";
        }
        where +=" AND areacode like'"+areacode+"%'";
        String order = ExtSort.Orderby(request, "createdate DESC");
        return dbService.QueryJsonData(fields, table, where, order, start, limit);
    }

    public JsonResult saveNEWGroup(HttpServletRequest request, HttpServletResponse response,String uid, String name,String areacode){
        if (StringUtils.isBlank(uid)) {
            String insertSQL = "INSERT INTO tb_alleyway_kre_group (uid,name,creatorid,createdate,areacode) VALUES (uuid(),?,?,now(),?)";
            dbService.excuteSql(insertSQL, name, userRedis.getUserId(request),areacode);
            syslogs.Write(request, "权限组设置", "新增权限组："+name);
        }else {
            String logSQL = "SELECT name FROM tb_alleyway_kre_group WHERE uid=?";
            JSONArray ja = dbService.QueryList(logSQL, uid);
            syslogs.Write(request, "权限组设置", "修改权限组'"+ja.getJSONObject(0).getString("name")+"'为'"+name+"'");
            String updateSQL = "UPDATE tb_alleyway_kre_group SET name=? WHERE uid=?";
            dbService.excuteSql(updateSQL, name, uid);
        }
        return Json.getJsonResult(true);
    }

    public JsonData getGroup(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit){
        String fields = "uid,name ";
        String table = "tb_alleyway_kre_group ";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(key)) {
            where += " AND name like '%" + key + "%'";
        }
        String order = ExtSort.Orderby(request, "createdate DESC");
        return dbService.QueryJsonData(fields, table, where, order, start, limit);
    }

    public JsonResult saveGroup(HttpServletRequest request, HttpServletResponse response,String uid, String name){
        if (StringUtils.isBlank(uid)) {
            String insertSQL = "INSERT INTO tb_alleyway_kre_group (uid,name,creatorid,createdate) VALUES (uuid(),?,?,now())";
            dbService.excuteSql(insertSQL, name, userRedis.getUserId(request));
            syslogs.Write(request, "权限组设置", "新增权限组："+name);
        }else {
            String logSQL = "SELECT name FROM tb_alleyway_kre_group WHERE uid=?";
            JSONArray ja = dbService.QueryList(logSQL, uid);
            syslogs.Write(request, "权限组设置", "修改权限组'"+ja.getJSONObject(0).getString("name")+"'为'"+name+"'");
            String updateSQL = "UPDATE tb_alleyway_kre_group SET name=? WHERE uid=?";
            dbService.excuteSql(updateSQL, name, uid);
        }
        return Json.getJsonResult(true);
    }

    public JsonResult delGroup(HttpServletRequest request, HttpServletResponse response,String uid){
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false);
        }

        int count = dbService.getCount("tb_alleyway_offline_group_cfg", "groupid='" + uid + "'");
        int count2 = dbService.getCount("tb_alleyway_offline_group_access", "groupid='" + uid + "'");
        if (count > 0 || count2 > 0) {
            return Json.getJsonResult(false,"权限组已使用，无法删除!");
        }
        String logSQL = "SELECT name FROM tb_alleyway_kre_group WHERE uid=?";
        JSONArray ja = dbService.QueryList(logSQL, uid);
        syslogs.Write(request, "权限组设置", "删除权限组："+ja.getJSONObject(0).getString("name"));

        String sql = "DELETE FROM tb_alleyway_kre_group WHERE uid=?";
        dbService.excuteSql(sql, uid);

        return Json.getJsonResult(true);
    }

    public JsonData getGroupCfg(HttpServletRequest request, HttpServletResponse response,String groupid, String key, int start, int limit){
        String fields = "kgc.uid,kgc.groupid,kg.name as groupname,kgc.devid,kgc.relays,door.name as doorname, kgc.weekindex,kgc.limits,date_format(kgc.endtime,'%Y-%m-%d') as endtime,kgc.effectivetimes,kgc.creatorid,date_format(kgc.createdate,'%Y-%m-%d %H:%i:%s') as createdate ";
        String table = "tb_alleyway_kre_group_cfg kgc " +
                "LEFT JOIN (SELECT name,controllerid,relays FROM tb_dev_door GROUP BY name,controllerid,relays) as door ON door.controllerid=kgc.devid AND door.relays=kgc.relays " +
                "INNER JOIN tb_alleyway_kre_group kg ON kg.uid=kgc.groupid "+
                "LEFT JOIN tb_dev_accesscontroller da ON da.uid=kgc.devid ";
        //如果为普通管理员，需要受二级授权控制
        if(userredis.get(request).getUsertype()==1){
            String userid=userredis.getUserId(request);
            table+= " inner join tb_dev_areaframework_user ua on ua.areacode=da.areacode and ua.userid='"+userid+"' ";
        }
        String where = "1=1 ";
        if (StringUtils.isNotBlank(groupid)) {
            where += " AND groupid='" + groupid + "'";
        }else {
            return Json.getJsonData(true);
        }
        if (StringUtils.isNotBlank(key)) {
            where += " AND door.name = '" + key + "'";
        }
        String order = ExtSort.Orderby(request, "kgc.createdate DESC");
        return dbService.QueryJsonData(fields, table, where, order, start, limit);
    }

    public JsonData getDoorList(HttpServletRequest request, String key, String areacode, String groupid, boolean viewchild, int start, int limit) {
        String fields = "da.uid,da.machineid,da.devname,door.name as doorname,door.relays";
        String table = "tb_dev_accesscontroller da " +
                "INNER JOIN ( " +
                "select door.name,door.relays,door.controllerid " +
                "from tb_dev_door door " +
                "LEFT JOIN tb_dev_accesscontroller ctrl ON ctrl.uid=door.controllerid " +
                "WHERE ctrl.status=1 and ctrl.devclass in(2,5) AND ctrl.devmodel=46 " +
                "AND NOT EXISTS (SELECT devid, relays FROM tb_alleyway_kre_group_cfg WHERE groupid='"+groupid+"' AND door.controllerid=devid AND door.relays=relays) " +
                "GROUP BY door.name,door.relays,door.controllerid " +
                ") door ON da.uid=door.controllerid ";
        String userid = userRedis.getUserId(request);
        //如果为普通管理员，需要受二级授权控制
        if (userRedis.get(request).getUsertype() == 1) {
            table += " inner join tb_dev_areaframework_user ua on ua.areacode=da.areacode and ua.userid='" + userid + "' ";
        }

        String where = " 1=1 ";
        if (StringUtils.isNotBlank(areacode)) {
            if (viewchild) {
                where += " and da.areacode like '" + areacode + "%' ";
            } else {
                where += " and da.areacode='" + areacode + "' ";
            }
        }
        if (!StringUtils.isBlank(key)) {
            where += " and  (door.name like '%" + key + "%'or da.devname like '%" + key + "%' or da.machineid='" + key + "')  ";
        }
        String orderby = ExtSort.Orderby(request, " da.machineid asc ");
        return dbService.QueryJsonData(fields, table, where, orderby, start, limit);
    }


    public JsonResult saveGroupCfg(HttpServletRequest request, HttpServletResponse response,String uid,String groupid,
                                   String devdata,String weekplanid,String limits,String endtime,String effectivetimes){
        List<String> names = new ArrayList<>();
        if (StringUtils.isBlank(uid)) {
            StringBuilder insertSQL = new StringBuilder("INSERT INTO tb_alleyway_kre_group_cfg (uid,groupid,devid,relays,weekindex,limits,endtime,effectivetimes,creatorid,createdate) VALUES ");
            StringBuilder data = new StringBuilder();
            JSONArray doorja = JSONArray.parseArray(devdata);
            String weekindex = "";
            for (int i = 0; i < doorja.size(); i++) {
                String devid = doorja.getJSONObject(i).getString("devid");
                JSONArray list = dbService.QueryList("select weekindex from tb_alleyway_kre_timescheme where devid = ? and weekplanid = ?", devid, weekplanid);
                 weekindex = "";
                if (list.size()>0){
                    weekindex =list.getJSONObject(0).getString("weekindex");
                }else {
                    String devname = dbService.QueryJSONObject("select devname from tb_dev_accesscontroller where uid = ? ", devid).getString("devname");
                    names.add(devname);
                    continue;
                }
                data.append("(uuid(),'").append(groupid).append("','").append(doorja.getJSONObject(i).getString("devid")).append("',").append(doorja.getJSONObject(i).getString("relays")).append(",");
                if (StringUtils.isBlank(weekindex)) {
                    data.append("null").append(",");
                }else {
                    data.append(weekindex).append(",");
                }
                data.append(limits).append(",'").append(endtime).append("',").append(effectivetimes).append(",'").append(userRedis.getUserId(request)).append("',now()),");
            }
            if (data.length() != 0) {
                data.deleteCharAt(data.length() - 1);
            }
            int result = dbService.excuteSql(insertSQL.append(data).toString());
            if(result > 0){
                String authSQL = "SELECT infoid FROM tb_alleyway_kre_group_access WHERE groupid=?";
                JSONArray authInfoJa = dbService.QueryList(authSQL, groupid);
                ArrayList<String> infoList = new ArrayList<>();
                for (int i = 0; i < authInfoJa.size(); i++) {
                    infoList.add(authInfoJa.getJSONObject(i).getString("infoid"));
                }
                if(!infoList.isEmpty()){
                    for (int i = 0; i < doorja.size(); i++) {
                        String devid = doorja.getJSONObject(i).getString("devid");
                        String relays = doorja.getJSONObject(i).getString("relays");
                        JSONArray list = dbService.QueryList("select weekindex from tb_alleyway_kre_timescheme where devid = ? and weekplanid = ?", devid, weekplanid);
                        weekindex = "";
                        if (list.size()>0){
                            weekindex =list.getJSONObject(0).getString("weekindex");
                        }else {
                            continue;
                        }
                        String updateAuth = "UPDATE tb_alleyway_kre_authrize SET weekindex=?,limits=?,endtime=?,effectivetimes=?,authtype=2,downstatus=5,downnum=0 WHERE devid=? AND relays=? AND infoid IN ('"+String.join("','",infoList)+"') AND downstatus IN (1,3,4,5,6) ";
                        dbService.excuteSql(updateAuth, weekindex, limits, endtime, effectivetimes, devid, relays);
                        String updateAuth2 = "UPDATE tb_alleyway_kre_authrize SET weekindex=?,limits=?,endtime=?,effectivetimes=?,authtype=2,downstatus=0,downnum=0 WHERE devid=? AND relays=? AND infoid IN ('"+String.join("','",infoList)+"') AND downstatus IN (0,2) ";
                        dbService.excuteSql(updateAuth2, weekindex, limits, endtime, effectivetimes, devid, relays);

                        String insertAuthSQL = "INSERT INTO tb_alleyway_kre_authrize (uid,infoid,cardno,devid,relays,weekindex,limits,endtime,effectivetimes,authtype,downstatus,downnum,creatorid,createdate)" +
                                "SELECT uuid(),uid,card,'"+devid+"','"+relays+"',"+weekindex+","+limits+",'"+endtime+"',"+effectivetimes+",2,0,0,'"+userRedis.getUserId(request)+"',now()" +
                                "FROM tb_card_teachstudinfo WHERE uid IN ('"+String.join("','",infoList)+"') AND not exists (SELECT infoid FROM tb_alleyway_kre_authrize WHERE devid='"+devid+"' AND relays='"+relays+"' AND infoid IN ('"+String.join("','",infoList)+"'))";
                        dbService.excuteSql(insertAuthSQL);
                    }
                }
            }
        } else {
            if (StringUtils.isBlank(weekplanid)) {
                String updateSQL = "UPDATE tb_alleyway_kre_group_cfg SET limits=?,endtime=?,effectivetimes=? WHERE uid IN ('"+String.join("','", uid.split(","))+"')";
                dbService.excuteSql(updateSQL, limits, endtime, effectivetimes);
                //更新授权表
                String updateAuth = "UPDATE tb_alleyway_kre_authrize ka INNER JOIN tb_alleyway_kre_group_cfg kgc ON kgc.devid=ka.devid AND kgc.relays=ka.relays " +
                        "SET ka.weekindex=null,ka.limits=?,ka.endtime=?,ka.effectivetimes=?,ka.authtype=2,ka.downstatus=5,downnum=0 WHERE ka.downstatus IN (1,3,4,5,6) AND kgc.uid IN ('"+String.join("','", uid.split(","))+"')";
                dbService.excuteSql(updateAuth, limits, endtime, effectivetimes);
                String updateAuth2 = "UPDATE tb_alleyway_kre_authrize ka INNER JOIN tb_alleyway_kre_group_cfg kgc ON kgc.devid=ka.devid AND kgc.relays=ka.relays " +
                        "SET ka.weekindex=null,ka.limits=?,ka.endtime=?,ka.effectivetimes=?,ka.authtype=2,ka.downstatus=0,downnum=0 WHERE ka.downstatus IN (0,2) AND kgc.uid IN ('"+String.join("','", uid.split(","))+"')";
                dbService.excuteSql(updateAuth2, limits, endtime, effectivetimes);
            }else {
                String[] uids = uid.split(",");
                for (int i = 0; i < uids.length; i++) {
                    String devid = dbService.QueryJSONObject("select devid from tb_alleyway_kre_group_cfg where uid  = '" + uids[i] + "'").getString("devid");
                    JSONArray list = dbService.QueryList("select weekindex from tb_alleyway_kre_timescheme where devid = ? and weekplanid = ?", devid, weekplanid);
                    String weekindex = "";
                    if (list.size()>0){
                        weekindex =list.getJSONObject(0).getString("weekindex");
                    }else {
                        String devname = dbService.QueryJSONObject("select devname from tb_dev_accesscontroller where uid = ? ", devid).getString("devname");
                        names.add(devname);
                        continue;
                    }

                    String updateSQL = "UPDATE tb_alleyway_kre_group_cfg SET weekindex=?,limits=?,endtime=?,effectivetimes=? WHERE uid = '"+uids[i]+"'";
                    dbService.excuteSql(updateSQL, weekindex, limits, endtime, effectivetimes);
                    //更新授权表
                    String updateAuth = "UPDATE tb_alleyway_kre_authrize ka INNER JOIN tb_alleyway_kre_group_cfg kgc ON kgc.devid=ka.devid AND kgc.relays=ka.relays " +
                            "SET ka.weekindex=?,ka.limits=?,ka.endtime=?,ka.effectivetimes=?,ka.authtype=2,ka.downstatus=5,downnum=0 WHERE ka.downstatus IN (1,3,4,5,6) AND kgc.uid = '"+uids[i]+"'";
                    dbService.excuteSql(updateAuth, weekindex, limits, endtime, effectivetimes);
                    String updateAuth2 = "UPDATE tb_alleyway_kre_authrize ka INNER JOIN tb_alleyway_kre_group_cfg kgc ON kgc.devid=ka.devid AND kgc.relays=ka.relays " +
                            "SET ka.weekindex=?,ka.limits=?,ka.endtime=?,ka.effectivetimes=?,ka.authtype=2,ka.downstatus=0,downnum=0 WHERE ka.downstatus IN (0,2) AND kgc.uid = '"+uids[i]+"'";
                    dbService.excuteSql(updateAuth2, weekindex, limits, endtime, effectivetimes);

                }
            }

        }
        if (names.size()>0){
            return Json.getJsonResult(false,String.join(",", names)+"设备没有下发该时段，请单独授权");
        }
        return Json.getJsonResult(true);
    }

    public JsonData queryweek(HttpServletRequest request, String devdata,String uid){
        ArrayList<String> uidlist = new ArrayList<>();
        if (StringUtils.isNotEmpty(uid)){
            String[] uids = uid.split(",");
            for (int i = 0; i < uids.length; i++) {
                String devid = dbService.QueryJSONObject("select devid from tb_alleyway_kre_group_cfg where uid  = '" + uids[i] + "'").getString("devid");
                JSONArray list = dbService.QueryList("select weekplanid from tb_alleyway_kre_timescheme where devid = ?", devid);
                if (list.size()>0){
                    for (int i1 = 0; i1 < list.size(); i1++) {
                        if (!uidlist.contains(list.getJSONObject(i1).getString("weekplanid"))){
                            uidlist.add(list.getJSONObject(i1).getString("weekplanid"));
                        }
                    }
                }
            }
            String result = "('" + String.join("','", uidlist)  + "')";
            JSONArray objects = dbService.QueryList("select uid as weekplanid, name from tb_alleyway_kre_weekplan where uid in " + result + " order by name");
            return Json.getJsonData(true,objects);
        }
        JSONArray devja = JSONArray.parseArray(devdata);
        for (int i = 0; i < devja.size(); i++) {
            JSONArray array = dbService.QueryList("select a.weekplanid from tb_alleyway_kre_timescheme a left join tb_alleyway_kre_weekplan b on a.weekplanid = b.uid where a.devid = ? ", devja.getJSONObject(i).getString("devid"));
            if (array.size()>0){
                for (int i1 = 0; i1 < array.size(); i1++) {
                    if (!uidlist.contains(array.getJSONObject(i1).getString("weekplanid"))){
                        uidlist.add(array.getJSONObject(i1).getString("weekplanid"));
                    }
                }
            }
        }
        String result = "('" + String.join("','", uidlist)  + "')";
        JSONArray objects = dbService.QueryList("select uid as weekplanid, name from tb_alleyway_kre_weekplan where uid in " + result + " order by name");
        return Json.getJsonData(true,objects);
    }

    public JsonResult delGroupCfg(HttpServletRequest request, HttpServletResponse response,String uid){
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false);
        }
        String delsql = "DELETE FROM tb_alleyway_kre_group_cfg WHERE uid IN ('"+String.join("','", uid.split(","))+"')";
        String sql = "SELECT kgc.devid,kgc.relays,kg.name as groupname " +
                "FROM tb_alleyway_kre_group_cfg kgc " +
                "INNER JOIN tb_alleyway_kre_group kg ON kg.uid=kgc.groupid "+
                " WHERE kgc.uid IN ('" + String.join("','", uid.split(",")) + "')";
        JSONArray ja = dbService.QueryList(sql);
        if (!ja.isEmpty()) {
            for (int i = 0; i < ja.size(); i++) {
                String updateAuth = "UPDATE tb_alleyway_kre_authrize SET downstatus=3,downnum=0 WHERE devid=? AND relays=? AND downstatus IN (1,3,4,5,6) ";
                dbService.excuteSql(updateAuth, ja.getJSONObject(i).getString("devid"), ja.getJSONObject(i).getString("relays"));

                String delAuth = "DELETE FROM tb_alleyway_kre_authrize WHERE devid=? AND relays=? AND downstatus IN (0,2)";
                dbService.excuteSql(delAuth, ja.getJSONObject(i).getString("devid"), ja.getJSONObject(i).getString("relays"));

                syslogs.Write(request, "权限组", "删除权限组设备:从[" + ja.getJSONObject(i).getString("groupname") + "]");
            }
        }


        dbService.excuteSql(delsql);
        return Json.getJsonResult(true);
    }
}
