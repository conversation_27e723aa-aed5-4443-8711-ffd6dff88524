package com.ymiots.campusos.service.face;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.entity.card.TeachStudInfo;
import com.ymiots.campusos.entity.face.FaceFingerDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.redis.RedisMessagePublish;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.ComHelper;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.datafactory.CallbackTransaction;

@Repository
public class DevFaceService extends BaseService {

    @Autowired
    private UserRedis userRedis;

    @Autowired
    RedisMessagePublish redismsg;

    @Autowired
    private SysLogsService syslogs;

    public JsonData getFaceDeviceList(HttpServletRequest request, HttpServletResponse response, String serveruid, String key, int start, int limit) {
        String fields = "ac.uid,ac.devsn,ac.machineid,ac.devname";
        String table = "tb_dev_accesscontroller ac";
        if (userRedis.get(request).getUsertype() == 1) {
            String userid = userRedis.getUserId(request);
            table += " inner join tb_dev_areaframework_user ua on ua.areacode=ac.areacode and ua.userid='" + userid + "' ";
        }

        String where = " ac.devclass='9' ";
        if (!StringUtils.isBlank(serveruid) && !serveruid.equals("0")) {
            where += " and ac.server_uid='" + serveruid + "' ";
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (ac.devname like '%" + key + "%' or ac.machineid='" + key + "') ";
        }
        String orderby = ExtSort.Orderby(request, " ac.machineid asc ");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData getInfoFaceList(HttpServletRequest request, HttpServletResponse response, String devids, String orgcode, boolean viewchild, String timeid, String key, String infodownstatus, int start, int limit) {
        String sql = "SELECT fa.uid,fa.devid,dev.machineid,dev.devname,fa.infoid,fa.timeid,fa.infodownstatus,fa.allowcard,fa.timedownstatus,fa.downmsg,fa.downnum,fa.downtime,fa.status,fa.creatorid,fa.createdate,d.name as infodownstatusname,d2.name as timedownstatusname,fa.code,fa.name,fa.orgcode,getorgname(fa.orgcode) as orgname,fa.card,fa.fingerdownstatus,t.name as timename,tfi.imgpath FROM ";
        String table = " (select f.uid,f.devid,info.code,info.name,f.infoid,info.orgcode,info.card,f.faceid,f.timeid,f.infodownstatus,f.allowcard,f.timedownstatus,f.downmsg,f.downnum,f.downtime,f.status,f.creatorid,f.createdate,f.fingerdownstatus " +
                "from tb_face_authorizeface f " +
                "inner join tb_card_teachstudinfo info on info.uid=f.infoid ";
        String where = "1=1 ";
        if (StringUtils.isNotBlank(devids)) {
            where += " and f.devid in ('" + String.join("','", devids.split(",")) + "')";
        }
        if (StringUtils.isNotBlank(orgcode)) {
            if (viewchild) {
                where += " and info.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and info.orgcode='" + orgcode + "' ";
            }
        }
        if (StringUtils.isNotBlank(timeid)) {
            where += " and f.timeid='" + timeid + "'";
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (info.name like '%" + key + "%' or info.code like '%" + key + "%' or info.card like '%" + key + "%') ";
        }
        if (StringUtils.isNotBlank(infodownstatus) && Integer.parseInt(infodownstatus) >= 0) {
            where += " and f.infodownstatus = '" + infodownstatus + "'";
        }
        int total = 0;
        SysUser user = userRedis.get(request);
        if(user.getUsertype()==1){
            String userid=user.getUid();
            table+= " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='"+userid+"' ";
            total = dbService.getCount("tb_face_authorizeface f inner join tb_card_teachstudinfo info on info.uid=f.infoid inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='"+userid+"' ", where);
        }else {
            total = dbService.getCount("tb_face_authorizeface f inner join tb_card_teachstudinfo info on info.uid=f.infoid", where);
        }
        table += "WHERE " + where + "ORDER BY f.createdate DESC LIMIT " + start + "," + limit + ") fa ";
        table += "inner join tb_face_timescheme t on t.uid=fa.timeid inner join tb_dev_accesscontroller dev ON dev.uid=fa.devid " +
                "LEFT JOIN tb_sys_dictionary d on d.code=fa.infodownstatus and d.groupcode='SYS0000025' " +
                "LEFT JOIN tb_sys_dictionary d2 on d2.code=fa.timedownstatus and d2.groupcode='SYS0000025' " +
                "LEFT JOIN tb_face_infoface tfi on tfi.uid = fa.faceid ";
        JsonData result = dbService.QueryJsonData(sql + table);
        JSONArray data = result.getData();
        for (int i = 0; i < data.size(); i++) {
            if (StringUtils.isNotEmpty(data.getJSONObject(i).getString("card"))) {
                JSONObject jsonObject = dbService.QueryJSONObject("select status from tb_card_cardinfo where cardno ='" + data.getJSONObject(i).getString("card") + "' and status != 0");
                if (jsonObject!=null){
                    int status = jsonObject.getIntValue("status");
                    if (status == 2) {
                        data.getJSONObject(i).put("card", null);
                    }
                }else {
                    data.getJSONObject(i).put("card", null);
                }
            }
        }
        result.setTotal(total);
        return result;
    }

    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response,
                                String orgcode, String key, String infotype, boolean viewchild, String devids, String property, String infolabel, String selecteduid, String ishasmodel, String ismanwoman ,int start, int limit) {
        String fields = "info.uid,info.code,info.name,info.sex,info.card,info.cardsn,info.orgcode,info.infotype,info.intoyear,info.focus,info.status, getorgname(info.orgcode) as orgname";
        String table = "tb_card_teachstudinfo info";
        if (userRedis.get(request).getUsertype() == 1) {
            String userid = userRedis.getUserId(request);
            table += " inner join tb_card_orgframework_user uo on uo.orgcode=info.orgcode and uo.userid='" + userid + "' ";
        }
        String[] splitDevids = devids.split(",");
        String where = " info.status=1 AND (SELECT COUNT(1) FROM tb_face_authorizeface fa WHERE info.uid=fa.infoid AND fa.devid IN ('" + String.join("','", splitDevids) + "'))< " + splitDevids.length;

        if (StringUtils.isNotBlank(infotype) && !infotype.equals("0")) {
            where += " and info.infotype=" + infotype;
        }
        if (StringUtils.isNotBlank(property) && !property.equals("-1")) {
            where += " and info.property=" + property;
        }
        if (StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)) {
            where += " and info.infolabel='" + infolabel + "' ";
        }

        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and info.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and info.orgcode='" + orgcode + "' ";
            }
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (info.code='" + key + "' or info.name like '%" + key + "%' or info.mobile like '%" + key + "%' or info.card='" + ComHelper.LeftPad(key, 12, '0') + "' or info.cardsn='" + ComHelper.LeftPad(key, 12, '0') + "' )";
        }
        if (ishasmodel.equals("0")) {
            where += " and not exists(SELECT uid FROM tb_face_infoface b WHERE b.infoid = info.uid and b.status=1)";
        }
        if (ishasmodel.equals("1")) {
            where += " and exists(SELECT uid FROM tb_face_infoface b WHERE b.infoid = info.uid and b.status=1)";
        }
        if (ismanwoman.equals("0")) {
            where += " and info.sex = '2'";
        }
        if (ismanwoman.equals("1")) {
            where += " and info.sex = '1'";
        }
        JSONArray Selected = JSONArray.parseArray(selecteduid);
        if (Selected.size() > 0) {
            String[] uidx = ComHelper.ListToArray(Selected);
            where += " and info.uid not in('" + String.join("','", uidx) + "')";
        }
        String orderby = ExtSort.Orderby(request, "info.createdate desc");
        return dbService.QueryJsonData(fields, table, where, orderby, start, limit);
    }

    public JsonResult SaveAuthorizeFace(HttpServletRequest request, HttpServletResponse response, String devids, String timeid, String infoids, String allowcard) {
        String creatorid = userRedis.getUserId(request);
        String[] devidx = devids.split(",");
        String[] infoidx = infoids.split(",");
        List<String> udevid = new ArrayList<String>();
        List<String> uinfoid = new ArrayList<String>();
        for (String devid : devidx) {
            for (String infoid : infoidx) {
                int count = dbService.getCount("tb_face_authorizeface", "infoid='" + infoid + "' and devid='" + devid + "'");
                if (count > 0) {
                    udevid.add(devid);
                    uinfoid.add(infoid);
                } else {
                    String appid = UUID.randomUUID().toString().replace("-", "");
                    String infofaceSql = "select infoid,imgpath from tb_face_infoface where infoid='" + infoid + "'";
                    String sql = "insert into tb_face_authorizeface(uid, devid, infoid, timeid,faceid,allowcard, infodownstatus, timedownstatus, downnum,status, creatorid, createdate) ";
                    JSONArray ja = dbService.QueryList(infofaceSql);
                    if (!ja.isEmpty()) {
                        sql += "select uuid(),'" + devid + "',infoid, '" + timeid + "',uid," + allowcard + ", 0,0,0,1,'" + creatorid + "',now() from tb_face_infoface where infoid='" + infoid + "'";
                    } else {
                        sql += " values('" + appid + "','" + devid + "','" + infoid + "','" + timeid + "',null," + allowcard + ", 0,0,0,1,'" + creatorid + "',now())";
                    }
                    dbService.excuteSql(sql);
                }

            }
        }
        dbService.excuteSql("update tb_face_authorizeface set allowcard=" + allowcard + ",infodownstatus=5,timedownstatus=0,downnum=0,downmsg='' where devid in ('" + String.join("','", udevid) + "') and infoid in ('" + String.join("','", uinfoid) + "') and infodownstatus in(1,3,5) and timedownstatus in(0,2)");
        dbService.excuteSql("update tb_face_authorizeface set allowcard=" + allowcard + ",infodownstatus=5,timedownstatus=5,downnum=0,downmsg='' where devid in ('" + String.join("','", udevid) + "') and infoid in ('" + String.join("','", uinfoid) + "') and infodownstatus in(1,3,5) and timedownstatus in(1,3,5)");
        dbService.excuteSql("update tb_face_authorizeface set allowcard=" + allowcard + ",infodownstatus=0,timedownstatus=5,downnum=0,downmsg='' where devid in ('" + String.join("','", udevid) + "') and infoid in ('" + String.join("','", uinfoid) + "') and infodownstatus in(0,2) and timedownstatus in(1,3,5)");

        syslogs.Write(request, "人脸授权", String.format("新增人脸授权,人员:%s,设备:%s", infoids, devids));
        return Json.getJsonResult(true);
    }

    public JsonData getTimeSchemeList(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        String fields = "uid, code, name, start1, end1, start2, end2, start3, end3,start4, end4,start5, end5,start6, end6";
        String table = "tb_face_timescheme";
        String where = "status=1";
        if (!StringUtils.isBlank(key)) {
            where += " and (code like '%" + key + "%' or name like '%" + key + "%')";
        }
        String orderby = ExtSort.Orderby(request, "code asc");
        return dbService.QueryJsonData(fields, table, where, orderby, start, limit);
    }

    public JsonResult UpdateTimeScheme(HttpServletRequest request, HttpServletResponse response, String timeid, String uids) throws Exception {
        String sql = "update tb_face_authorizeface set timeid='" + timeid + "',infodownstatus=0,downnum=0,downmsg='' where infodownstatus in(0,2) and uid in('" + String.join("','", uids.split(",")) + "')";
        dbService.excuteSql(sql);
        sql = "update tb_face_authorizeface set timeid='" + timeid + "',infodownstatus=5,downnum=0,downmsg='' where timedownstatus IN (1,3,4,5,6) and uid in('" + String.join("','", uids.split(",")) + "')";
        dbService.excuteSql(sql);

        String dataSQL = "SELECT devid,infoid FROM tb_face_authorizeface WHERE uid IN ('" + String.join("','", uids.split(",")) + "')";
        JSONArray ja = dbService.QueryList(dataSQL);
        List<String> devlist = new ArrayList<String>();
        List<String> infoidlist = new ArrayList<String>();
        if (!ja.isEmpty()) {
            for (int i = 0; i < ja.size(); i++) {
                devlist.add(ja.getJSONObject(i).getString("devid"));
                infoidlist.add(ja.getJSONObject(i).getString("infoid"));
            }
        }
        if (!devlist.isEmpty() && !infoidlist.isEmpty()) {
            redown(request, response, String.join(",", infoidlist), String.join(",", devlist));
        }
        syslogs.Write(request, "人脸授权", String.format("修改时段,人员:%s，时段:%s", uids, timeid));
        return Json.getJsonResult(true);
    }

    public JsonResult DelDevface(HttpServletRequest request, HttpServletResponse response, final String uids) throws Exception {
        //dbService.excuteTransaction(new CallbackTransaction() {
        //    @Override
        //    public void execute(Connection connection) throws SQLException {

                //待下载和下载失败的直接删除
                String sql = "delete from tb_face_authorizeface where infodownstatus in(0,2) and find_in_set(uid,'" + uids + "')>0";
                dbService.excuteSql(sql);
                //PreparedStatement ps = connection.prepareStatement(sql);
                //ps.executeUpdate();
                //ps.close();

                //已下载和待重下的更新状态为待删除
                sql = "update tb_face_authorizeface set infodownstatus=3, timedownstatus=3,downmsg='',downnum=0 where infodownstatus in(1,3,4,5,6) and find_in_set(uid,'" + uids + "')>0 ";
                dbService.excuteSql(sql);
                //ps = connection.prepareStatement(sql);
                //ps.executeUpdate();
                //ps.close();

                //权限组授权删除
                String sqlData = "SELECT fa.infoid,cfg.groupid FROM tb_face_authorizeface fa LEFT JOIN tb_face_authorize_group_cfg cfg ON fa.devid=cfg.controllerid WHERE fa.uid IN ('" + String.join("','", uids.split(",")) + "')";
                JSONArray dataja = dbService.QueryList(sqlData);
                if (!dataja.isEmpty()) {
                    for (int i = 0; i < dataja.size(); i++) {
                        String infoid = dataja.getJSONObject(i).getString("infoid");
                        String groupid = dataja.getJSONObject(i).getString("groupid");
                        String updateGroup = "UPDATE tb_face_authorize_group_access SET downstatus=3,downmsg='',downnum=0 WHERE infoid=? AND groupid=? AND downstatus IN (0,2)";
                        dbService.excuteSql(updateGroup,infoid,groupid);
                        //ps = connection.prepareStatement(updateGroup);
                        //ps.setString(1, infoid);
                        //ps.setString(2, groupid);
                        //ps.executeUpdate();
                        //ps.close();

                        String delGroup = "DELETE FROM tb_face_authorize_group_access WHERE infoid=? AND groupid=? AND downstatus IN (0,2)";
                        dbService.excuteSql(delGroup,infoid,groupid);
                        //ps = connection.prepareStatement(delGroup);
                        //ps.setString(1, infoid);
                        //ps.setString(2, groupid);
                        //ps.executeUpdate();
                        //ps.close();
                    }
                }

        syslogs.Write(request, "人脸授权", String.format("删除授权,人员:%s", uids));
        return Json.getJsonResult(true);
    }

    public JsonResult redown(HttpServletRequest request, HttpServletResponse response, final String infoids, final String devids) throws Exception {
        if (StringUtils.isBlank(infoids) && StringUtils.isBlank(devids)) {
            return Json.getJsonResult(false, "请至少选择设备或名单记录");
        }
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                String updateFa = "UPDATE tb_face_authorizeface SET downmsg='',infodownstatus=5,timedownstatus=5,downnum=0 WHERE find_in_set(infodownstatus,'1,3,4,5,6')>0 ";

                if (!StringUtils.isBlank(infoids)) {
                    updateFa += " AND find_in_set(infoid,'" + infoids + "')>0 ";
                }
                if (!StringUtils.isBlank(devids)) {
                    updateFa += " AND find_in_set(devid,'" + devids + "')>0 ";
                }

                PreparedStatement ps = connection.prepareStatement(updateFa);
                ps.executeUpdate();
                ps.close();

                String updateFa2 = "UPDATE tb_face_authorizeface SET downmsg='',infodownstatus=0,timedownstatus=0,downnum=0,fingerdownstatus = 0 WHERE infodownstatus='2' ";
                if (!StringUtils.isBlank(infoids)) {
                    updateFa2 += " AND find_in_set(infoid,'" + infoids + "')>0 ";
                }
                if (!StringUtils.isBlank(devids)) {
                    updateFa2 += " AND find_in_set(devid,'" + devids + "')>0 ";
                }
                ps = connection.prepareStatement(updateFa2);
                ps.executeUpdate();
                ps.close();

                String updateGroup = "UPDATE tb_face_authorize_group_access ga INNER JOIN tb_face_authorize_group_cfg cfg ON ga.groupid=cfg.groupid " +
                        "SET ga.downstatus=5,ga.downnum=0,ga.downmsg='' WHERE ga.infoid IN ('" + String.join("','", infoids.split(",")) + "') AND cfg.controllerid IN ('" + String.join("','", devids.split(",")) + "')";
                ps = connection.prepareStatement(updateGroup);
                ps.executeUpdate();
                ps.close();
            }
        });
        return Json.getJsonResult(true);
    }

    public JsonResult changeAllowCard(HttpServletRequest request, HttpServletResponse response, String uid, String allowcard) {
        dbService.excuteSql("update tb_face_authorizeface set allowcard=" + allowcard + ",infodownstatus=5,downnum=0,downmsg='' where uid in ('" + String.join("','", uid.split(",")) + "') AND infodownstatus in(1,3,5)");
        dbService.excuteSql("update tb_face_authorizeface set allowcard=" + allowcard + ",infodownstatus=0,downnum=0,downmsg='' where uid in ('" + String.join("','", uid.split(",")) + "') and infodownstatus in(0,2)");
        return Json.getJsonResult(true);
    }

    public JSONArray getInfoLabel(HttpServletRequest request, HttpServletResponse response, String infotype) {
        String sql = "SELECT label as name FROM tb_card_infolabel WHERE infotype=?";
        JSONArray ja = dbService.QueryList(sql, infotype);
        JSONObject jo = new JSONObject();
        jo.put("name", "全部");
        ja.add(0, jo);
        return ja;
    }

    /**
     * 获取已经授权的人员信息（权限组加人脸授权）
     */
    public R<List<FaceFingerDto>> getAuthorizedUsers(HttpServletRequest request, String devId, String key, Integer page, Integer limit) {
        //人脸授权人数
        String sql = "select f.devid devId,info.code,info.name,info.uid,da.devname devName,da.machineid machineId" +
                " from tb_face_authorizeface f " +
                " inner join tb_dev_accesscontroller da on da.uid = f.devid " +
                " inner join tb_card_teachstudinfo info on info.uid=f.infoid ";
        sql += " where f.infodownstatus  = 1 and f.timedownstatus = 1 and f.devid='" + devId + "'";
        if (!StringUtils.isBlank(key)) {
            sql += " and (info.code like '%" + key + "%' or info.name like '%" + key + "%') ";
        }
        List<FaceFingerDto> faceFingerDtoList = dbService.queryList(sql, FaceFingerDto.class);

        //权限组授权人数
        String sql2 = "select info.code,info.name,info.uid,da.devname devName,da.machineid machineId,da.uid as devId from " +
                " tb_face_authorize_group ag " +
                " join tb_face_authorize_group_cfg agc on ag.uid = agc.groupid " +
                " join tb_face_authorize_group_access aga on aga.devid = agc.controllerid " +
                " join tb_dev_accesscontroller da on da.uid = agc.controllerid " +
                " join tb_card_teachstudinfo info on info.uid=aga.infoid ";
        sql2 += " where aga.downstatus  = 1 and aga.devid='" + devId + "'";
        if (!StringUtils.isBlank(key)) {
            sql2 += " and (info.code like '%" + key + "%' or info.name like '%" + key + "%') ";
        }
        List<FaceFingerDto> groupFaceFingerDtoList = dbService.queryList(sql2, FaceFingerDto.class);
        //合并两个list
        faceFingerDtoList.addAll(groupFaceFingerDtoList);
        List<FaceFingerDto> fingerDtoList = faceFingerDtoList.stream()
                .sorted(Comparator.comparing(FaceFingerDto::getCode))
                .skip((long) (page - 1) * limit)
                .limit(limit)
                .collect(Collectors.toList());
        return R.ok(fingerDtoList);
    }

    public JsonData getFingerDev(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        String fields = "uid,devsn,machineid,devname,devstatus";
        String table = "tb_dev_accesscontroller";
        String where = "devclass=9 and devmodel=48";
        if (StringUtils.isNotBlank(key)) {
            where += " AND (machineid='" + key + "' or devname like '%" + key + "%')";
        }
        String orderby = ExtSort.Orderby(request, "createdate desc");
        return dbService.QueryJsonData(fields, table, where, orderby, start, limit);
    }

    public JsonData getFingerCollectionList(HttpServletRequest request, HttpServletResponse response, String infoid, String status, String key, int start, int limit) {
        String fields = "fc.uid,fc.devid,da.machineid,da.devname,ct.code as infocode,ct.name as infoname,date_format(fc.downtime,'%Y-%m-%d %H:%i:%s') downtime,fc.downnum,fc.downmsg,date_format(fc.createdate,'%Y-%m-%d %H:%i:%s') createdate,fc.status as status";
        String table = "tb_face_finger_collection fc" +
                " join tb_card_teachstudinfo ct on fc.infoid = ct.uid " +
                " join tb_dev_accesscontroller da on da.uid = fc.devid ";
        String where = "1=1";
        if (StringUtils.isNotBlank(status) && !"-1".equals(status)) {
            where += " AND fc.status ='" + status + "'";
        }
        if (StringUtils.isNotBlank(infoid)) {
            where += " AND ct.uid IN ('" + String.join("','", infoid.split(",")) + "')";
        }
        if (StringUtils.isNotBlank(key)) {
            where += " AND (da.machineid='" + key + "' or da.devname like '%" + key + "%')";
        }
        String orderby = ExtSort.Orderby(request, "fc.createdate desc");
        return dbService.QueryJsonData(fields, table, where, orderby, start, limit);
    }

    public JsonResult fingerCollection(HttpServletRequest request, HttpServletResponse response, String devid, String infoid) throws Exception {
        dbService.excuteTransaction(new CallbackTransaction() {
            @Override
            public void execute(Connection connection) throws SQLException {
                String insertSQL = "INSERT INTO tb_face_finger_collection(uid, devid, infoid, downnum, createdate, status) values(uuid(), ?, ?, 0, now(), 0)";
                try (PreparedStatement insertStatement = connection.prepareStatement(insertSQL)) {
                    for (String s : infoid.split(",")) {
                        insertStatement.setString(1, devid);
                        insertStatement.setString(2, s);
                        insertStatement.executeUpdate();
                    }
                }
            }
        });
        return Json.getJsonResult(true);
}

    public JsonResult renameThePhoto(HttpServletRequest request, HttpServletResponse response) {
        // 获取到用户信息
        String sql = "select code, name from tb_card_teachstudinfo";
        List<TeachStudInfo> teachStudInfos = dbService.queryList(sql, TeachStudInfo.class);

        // 目标目录
        String photoDirectoryPath = "D:\\upload\\照片";
        File photoDirectory = new File(photoDirectoryPath);

        // 检查目录是否存在
        if (!photoDirectory.exists() || !photoDirectory.isDirectory()) {
            return Json.getJsonResult("目标目录不存在或不是目录");
        }

        // 调试输出：输出目标目录路径
        System.out.println("目标目录路径: " + photoDirectoryPath);

        // 获取所有照片文件，包括子文件夹中的照片文件
        List<File> photoFiles = new ArrayList<>();
        getAllPhotoFiles(photoDirectory, photoFiles);

        if (photoFiles.isEmpty()) {
            return Json.getJsonResult("目标目录中没有照片文件");
        }

        // 新路径
        String newPhotoDirectoryPath = "D:\\upload\\新照片";
        File newPhotoDirectory = new File(newPhotoDirectoryPath);

        // 检查新路径目录是否存在，不存在则创建
        if (!newPhotoDirectory.exists()) {
            newPhotoDirectory.mkdirs();
        }

        // 遍历照片文件并重命名和复制到新目录
        for (File photoFile : photoFiles) {
            String originalName = photoFile.getName();
            boolean renamed = false;

            for (TeachStudInfo teachStudInfo : teachStudInfos) {
                if (originalName.contains(teachStudInfo.getName())) {
                    String newName = teachStudInfo.getCode() + originalName.substring(originalName.lastIndexOf('.'));
                    Path newFilePath = Paths.get(newPhotoDirectoryPath, photoFile.getParentFile().getName(), newName);

                    // 创建必要的子目录
                    new File(newFilePath.getParent().toString()).mkdirs();

                    try {
                        // 复制并重命名文件
                        Files.copy(photoFile.toPath(), newFilePath, StandardCopyOption.REPLACE_EXISTING);
                        renamed = true;
                        break;
                    } catch (IOException e) {
                        e.printStackTrace();
                        return Json.getJsonResult("重命名和复制失败: " + e.getMessage());
                    }
                }
            }

            if (!renamed) {
                System.out.println("未找到匹配的姓名: " + originalName);
            }
        }

        return Json.getJsonResult("照片重命名和复制完成");
    }

    // 递归遍历所有子文件夹，获取所有照片文件
    private void getAllPhotoFiles(File directory, List<File> photoFiles) {
        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    getAllPhotoFiles(file, photoFiles); // 递归遍历子文件夹
                } else if (file.getName().toLowerCase().endsWith(".jpg") || file.getName().toLowerCase().endsWith(".png")) {
                    photoFiles.add(file);
                }
            }
        }
    }
    }
