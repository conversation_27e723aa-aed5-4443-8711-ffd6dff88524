package com.ymiots.campusos.service.hotel;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.ComHelper;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;

@Repository
public class BedService extends BaseService{

	@Autowired
	UserRedis userredis;
	
	@Autowired
	SysLogsService syslogs;
	
	public JsonData getBedList(HttpServletRequest request, HttpServletResponse response,String houseid,int start,int limit){
		if(StringUtils.isBlank(houseid)){
			return Json.getJsonData(true);
		}
		String fields="uid,code,name,des,houseid,infoid,infoname,orgcode,livetime,status,getorgname(orgcode) as orgname";
		String table="tb_hotel_bed ";
		String where=" houseid='"+houseid+"'  ";
		String orderby=ExtSort.Orderby(request, " code asc ");
		JsonData result =dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}
	
	public JsonData getHouseList(HttpServletRequest request, HttpServletResponse response,String parentid,String parentcode,boolean viewchild,String key,  int start,int limit){
		if(StringUtils.isBlank(parentid) || StringUtils.isBlank(parentcode)){
			return Json.getJsonData(true);
		}
		
		String fields="distinct b.uid,b.code,b.level,b.areatype,b.name,b.parentid,b.status,b.createdate,d.name as houseType,d.attr1 as houseMoney";
		String table="tb_dev_areaframework b left join tb_hotel_bed hb on b.uid = hb.houseid " +
				"left join tb_sys_dictionary d on d.code=hb.housetype and d.groupcode='SYS0000057'";
		String where=" b.areatype in('3')  ";
		
        //如果为普通管理员，需要受二级授权控制
        if(userredis.get(request).getUsertype()==1){
        	SysUser user=userredis.get(request);
        	String userid=user.getUid();
        	table+= " inner join tb_dev_areaframework_user ua on ua.areacode=b.code and ua.userid='"+userid+"' ";
        }
		
		if(viewchild){
			if(!StringUtils.isBlank(parentcode)){
				where +=" and b.code like '"+parentcode+"%' ";
			}
		}else{
			if(!StringUtils.isBlank(parentid)){
				where +=" and b.parentid='"+parentid+"' ";
			}
		}
		if(!StringUtils.isBlank(key)){
			where +=" and (b.name like '%"+key+"%') ";
		}
		String orderby=ExtSort.Orderby(request, " b.code asc,b.createdate desc ");
		JsonData result =dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}
	
	public JsonResult SaveBed(HttpServletRequest request, HttpServletResponse response,String houseids,int houseType,int start,int end){
		if(StringUtils.isBlank(houseids)){
			return Json.getJsonResult(false);
		}
		String sqlType = "select attr2 as bedCount from tb_sys_dictionary where groupcode = 'SYS0000057' and code = " + houseType;
		JSONObject object = dbService.QueryJSONObject(sqlType);
		int bedCount = 0;
		if (!object.isEmpty()) {
			bedCount = object.getIntValue("bedCount");
		}
//		if(end<start){
//			return Json.getJsonResult(false,"床位起始编号错误");
//		}
//
//		if(end-start+1<=0 && end-start+1>100){
//			return Json.getJsonResult(false,"床位数量仅限1-100");
//		}
		if(houseids.split(",").length>1){
			int count=dbService.getCount("tb_hotel_bed", "houseid in('"+String.join("','", houseids.split(","))+"')");
			if(count>0){
				return Json.getJsonResult("所选房间已存在床位");
			}
		}
		int bedcount=0;
		String sql="select uid,code,getareaname(code) as name from tb_dev_areaframework where areatype=3 and uid in('"+String.join("','", houseids.split(","))+"')";
		JSONArray houses=dbService.QueryList(sql);
		StringBuffer sqlsb=new StringBuffer();
		sqlsb.append("insert into tb_hotel_bed(uid,code,name,des,houseid,status,isanalysis,housetype) values");
		for(int i=0;i<houses.size();i++){
			JSONObject house=houses.getJSONObject(i);
			for(int x=1;x<=bedCount;x++){
				String code=String.format("%s%s", house.getString("code"),ComHelper.LeftPad(String.valueOf(x), 3, '0'));
				String name=String.format("%s号床位", x);
				String des=String.format("%s房间", house.getString("name"));

				if(houseids.split(",").length==1){
					int _count=dbService.getCount("tb_hotel_bed", "houseid='"+house.getString("uid")+"' and code='"+code+"' and infoid is not null");
					if(_count==0){
						String sqlDel = "delete from tb_hotel_bed where houseid = '" + houseids + "'";
						dbService.excuteSql(sqlDel);
						sqlsb.append("(uuid(),'"+code+"','"+name+"','"+des+"','"+house.getString("uid")+"',0,0,"+houseType+")");
						if((i+1)*(x-start+1)<houses.size()*(bedCount-start+1)){
							sqlsb.append(",");
						}
						bedcount++;
					}else {
						return Json.getJsonResult(false,"该房间存在入住，请先退宿");
					}
				}else{
					sqlsb.append("(uuid(),'"+code+"','"+name+"','"+des+"','"+house.getString("uid")+"',0,0,"+houseType+")");
					if((i+1)*(x-start+1)<houses.size()*(bedCount-start+1)){
						sqlsb.append(",");
					}
					bedcount++;
				}
			}
		}
		if(bedcount>0){
			dbService.excuteBatchSql(sqlsb.toString());
			syslogs.Write(request, "床位管理", String.format("房间批量新增床位%d-%d，房间代码【%s】", start,end,houseids));
		}
		return Json.getJsonResult(true);
	}
	
	public JsonResult DelBed(HttpServletRequest request, HttpServletResponse response,String uids){
		String sql="delete from tb_hotel_bed where status=0 and uid in('"+String.join("','", uids.split(","))+"')";
		dbService.excuteSql(sql);
		syslogs.Write(request, "床位管理", "删除床位【"+uids+"】");
		return Json.getJsonResult(true);
	}

	public JsonResult RecordHotel(HttpServletRequest request, HttpServletResponse response, 
			String infoid, String infoname, String infocode, String orgcode,String areacode, String areaname,
			String bedid,String bedname, String houseid, String eventtype){
		if(!StringUtils.isBlank(infocode)){
			String sql="select uid,code from tb_card_teachstudinfo where uid='"+infoid+"'";
			JSONArray userinfos=dbService.QueryList(sql);
			if(userinfos.size()==0){
				return Json.getJsonResult("人员信息不存在");
			}
			infocode=userinfos.getJSONObject(0).getString("code");
		}
		String creatorid=userredis.getUserId(request);
		String sql="insert into tb_hotel_record(uid,eventtype,infoid,infoname,infocode,orgcode,areacode,areaname,bedname,bedid,houseid,createdate,creatorid) "
				+ "values(uuid(),?,?,?,?,?,?,?,?,?,?,now(),?)";
		dbService.excuteSql(sql, eventtype,infoid,infoname,infocode,orgcode,areacode,areaname,bedname,bedid,houseid,creatorid);
		return Json.getJsonResult(true);
	}
}
