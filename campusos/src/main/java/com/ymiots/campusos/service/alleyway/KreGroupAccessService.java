package com.ymiots.campusos.service.alleyway;

import com.alibaba.fastjson.JSONArray;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022-02-03 19:13
 * @since 2022-02-03 19:13
 */
@Service
public class KreGroupAccessService extends BaseService {

    @Autowired
    private UserRedis userRedis;


    @Autowired
    SysLogsService syslogs;


    public JsonData getGroup(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit){
        String fields = "uid,name ";
        String table = "tb_alleyway_kre_group ";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(key)) {
            where += " AND name like '%" + key + "%'";
        }
        String order = ExtSort.Orderby(request, "createdate DESC");
        return dbService.QueryJsonData(fields, table, where, order, start, limit);
    }

    public JsonData getNewGroup(HttpServletRequest request, HttpServletResponse response, String key,String areacode, int start, int limit){
        String fields = "uid,name ";
        String table = "tb_alleyway_kre_group ";
        String where = " 1=1 ";
        if(StringUtils.isEmpty(areacode)){
            return Json.getJsonData(true);
        }
        if (StringUtils.isNotBlank(key)) {
            where += " AND name like '%" + key + "%'";
        }
        where +=" AND areacode like'"+areacode+"%'";
        String order = ExtSort.Orderby(request, "createdate DESC");
        return dbService.QueryJsonData(fields, table, where, order, start, limit);
    }

    public JsonData getGroupAccess(HttpServletRequest request, HttpServletResponse response, String groupid, String key, int start, int limit) {
        String fields = "kga.uid,kg.name as groupname,kga.infoid,ct.name,ct.code,kga.cardno,date_format(kga.createdate,'%Y-%m-%d %H:%i:%s') as createdate";
        String table = "tb_alleyway_kre_group_access kga " +
                "LEFT JOIN tb_card_teachstudinfo ct ON ct.uid=kga.infoid " +
                "INNER JOIN tb_alleyway_kre_group kg ON kg.uid=kga.groupid ";
        String where = " 1=1 ";
        if (StringUtils.isNotBlank(key)) {
            where += " AND (ct.name like '%" + key + "%' OR ct.code='" + key + "')";
        }
        if (StringUtils.isNotBlank(groupid)) {
            where += " AND kga.groupid='" + groupid + "'";
        }
        //else {
        //    return Json.getJsonData(true);
        //}
        String order = ExtSort.Orderby(request, "kga.createdate DESC");
        return dbService.QueryJsonData(fields, table, where, order, start, limit);
    }

    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response, String groupid,
                                String orgcode, String key, String infotype, boolean viewchild, String intoyear, String infolabel, String selecteduid, int start, int limit) {
        String fields = "b.uid,b.code,b.name,b.sex,b.card,b.cardsn,b.passpassword,b.orgcode,b.infotype,b.intoyear,b.focus,b.status, getminiorgname(b.orgcode) as orgname,b.infolabel";
        String table = "tb_card_teachstudinfo b";
        if (userRedis.get(request).getUsertype() == 1) {
            String userid = userRedis.getUserId(request);
            table += " inner join tb_card_orgframework_user uo on uo.orgcode=b.orgcode and uo.userid='" + userid + "' ";
        }
        String where = " b.status=1 AND (not b.cardsn is null and b.cardsn<>'') AND NOT EXISTS (SELECT uid FROM tb_alleyway_kre_group_access WHERE groupid='" + groupid + "' AND infoid=b.uid)";
        if (!infotype.equals("0") && StringUtils.isNotBlank(infotype)) {
            where += " and b.infotype=" + infotype;
        }
        if (!intoyear.equals("0")) {
            where += " and b.intoyear=" + intoyear;
        }
        if (StringUtils.isNotBlank(infolabel) && !"全部".equals(infolabel)) {
            where += " and b.infolabel = '" + infolabel + "' ";
        }
        if (!StringUtils.isBlank(orgcode)) {
            if (viewchild) {
                where += " and b.orgcode like '" + orgcode + "%' ";
            } else {
                where += " and b.orgcode='" + orgcode + "' ";
            }
        }
        if (!StringUtils.isBlank(key)) {
            where += " and (b.code='" + key + "' or b.name like '%" + key + "%' or b.mobile like '%" + key + "%' or b.card='" + ComHelper.LeftPad(key, 12, '0') + "' or b.cardsn='" + ComHelper.LeftPad(key, 12, '0') + "' )";
        }
        JSONArray Selected = JSONArray.parseArray(selecteduid);
        if (Selected.size() > 0) {
            String[] uidx = ComHelper.ListToArray(Selected);
            where += " and b.uid not in('" + String.join("','", uidx) + "')";
        }
        String orderby = ExtSort.Orderby(request, "b.createdate desc");
        return dbService.QueryJsonData(fields, table, where, orderby, start, limit);
    }

    public JsonResult saveGroupAccess(HttpServletRequest request, HttpServletResponse response, String groupid, String infoid) {
        String insertSQL = "INSERT INTO tb_alleyway_kre_group_access (uid,groupid,infoid,cardno,creatorid,createdate) " +
                "SELECT uuid(),'" + groupid + "',uid,card,'" + userRedis.getUserId(request) + "',now() FROM tb_card_teachstudinfo WHERE uid IN ('" + String.join("','", infoid.split(",")) + "')";
        dbService.excuteSql(insertSQL);

        String groupCfgSQL = "SELECT devid,relays,weekindex,limits,date_format(endtime,'%Y-%m-%d') as endtime,effectivetimes FROM tb_alleyway_kre_group_cfg WHERE groupid=?";
        JSONArray groupCfgJa = dbService.QueryList(groupCfgSQL, groupid);
        for (int i = 0; i < groupCfgJa.size(); i++) {
            String devid = groupCfgJa.getJSONObject(i).getString("devid");
            String relays = groupCfgJa.getJSONObject(i).getString("relays");
            String weekindex = groupCfgJa.getJSONObject(i).getString("weekindex");
            String limits = groupCfgJa.getJSONObject(i).getString("limits");
            String endtime = groupCfgJa.getJSONObject(i).getString("endtime");
            String effectivetimes = groupCfgJa.getJSONObject(i).getString("effectivetimes");
            String[] infoSplit = infoid.split(",");

            String updateAuth = "UPDATE tb_alleyway_kre_authrize SET weekindex=?,limits=?,endtime=?,effectivetimes=?,authtype=2,downstatus=5,downnum=0 WHERE devid=? AND relays=? AND infoid IN ('" + String.join("','", infoSplit) + "') AND downstatus IN (1,3,4,5,6) ";
            dbService.excuteSql(updateAuth, weekindex, limits, endtime, effectivetimes, devid, relays);
            String updateAuth2 = "UPDATE tb_alleyway_kre_authrize SET weekindex=?,limits=?,endtime=?,effectivetimes=?,authtype=2,downstatus=0,downnum=0 WHERE devid=? AND relays=? AND infoid IN ('" + String.join("','", infoSplit) + "') AND downstatus IN (0,2) ";
            dbService.excuteSql(updateAuth2, weekindex, limits, endtime, effectivetimes, devid, relays);

            String insertAuthSQL = "INSERT INTO tb_alleyway_kre_authrize (uid,infoid,cardno,devid,relays,weekindex,limits,endtime,effectivetimes,authtype,downstatus,downnum,creatorid,createdate)" +
                    "SELECT uuid(),uid,card,'" + devid + "','" + relays + "',";
            if (StringUtils.isNotBlank(weekindex)) {
                insertAuthSQL += weekindex + ",";
            }else {
                insertAuthSQL += "null,";
            }
            insertAuthSQL += limits + ",'" + endtime + "'," + effectivetimes + ",2,0,0,'" + userRedis.getUserId(request) + "',now()" +
                    "FROM tb_card_teachstudinfo WHERE uid IN ('" + String.join("','", infoSplit) + "') AND not exists (SELECT infoid FROM tb_alleyway_kre_authrize WHERE devid='" + devid + "' AND relays='" + relays + "' AND infoid IN ('" + String.join("','", infoSplit) + "'))";

            dbService.excuteSql(insertAuthSQL);
        }
        syslogs.Write(request, "权限组授权", "添加权限授权");
        return Json.getJsonResult(true);
    }

    public JsonResult delGroupAccess(HttpServletRequest request, HttpServletResponse response, String uid) {
        String sql = "SELECT kgc.devid,kgc.relays,kga.infoid,kg.name as groupname,kga.cardno " +
                "FROM tb_alleyway_kre_group_cfg kgc " +
                "INNER JOIN tb_alleyway_kre_group kg ON kg.uid=kgc.groupid " +
                "LEFT JOIN tb_alleyway_kre_group_access kga ON kga.groupid=kgc.groupid WHERE kga.uid IN ('" + String.join("','", uid.split(",")) + "')";
        JSONArray ja = dbService.QueryList(sql);
        if (!ja.isEmpty()) {
            for (int i = 0; i < ja.size(); i++) {
                String updateAuth = "UPDATE tb_alleyway_kre_authrize SET downstatus=3,downnum=0 WHERE devid=? AND relays=? AND infoid=? AND downstatus IN (1,3,4,5,6) ";
                dbService.excuteSql(updateAuth, ja.getJSONObject(i).getString("devid"), ja.getJSONObject(i).getString("relays"), ja.getJSONObject(i).getString("infoid"));

                String delAuth = "DELETE FROM tb_alleyway_kre_authrize WHERE devid=? AND relays=? AND infoid=? AND downstatus IN (0,2)";
                dbService.excuteSql(delAuth, ja.getJSONObject(i).getString("devid"), ja.getJSONObject(i).getString("relays"), ja.getJSONObject(i).getString("infoid"));

                syslogs.Write(request, "权限组授权", "删除权限授权:从[" + ja.getJSONObject(i).getString("groupname") + "]删除卡号[" + ja.getJSONObject(i).getString("cardno") + "]权限");
            }
        }
        String delAccess = "DELETE FROM tb_alleyway_kre_group_access WHERE uid IN ('" + String.join("','", uid.split(",")) + "')";
        dbService.excuteSql(delAccess);


        return Json.getJsonResult(true);
    }
}
