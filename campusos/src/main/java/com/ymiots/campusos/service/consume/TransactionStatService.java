package com.ymiots.campusos.service.consume;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.DBService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dto.DailyTransactionStatDto;
import com.ymiots.campusos.dto.MonthlyTransactionStatDto;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.excel.table.DailyTransactionStatRow;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.utils.excel.Sheet;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Null;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/23 13:46
 */
@Service
public class TransactionStatService {

    @Autowired
    DBService dbService;

    @Autowired
    UserRedis userRedis;

    @Autowired
    MonthlyTransactionStatService monthlyTransactionStatService;

    /**
     * 消费详情
     */
    @Data
    private static class ConsumeDtl implements StatDtl {

        private String infoid;

        private String name;

        private String code;

        private String cardid;

        private String cardno;

        /**
         * 结算状态（1.未结算 2.已结算）
         */
        private Integer status;

        private Date recordtime;

        private BigDecimal money;

        private Integer paywallet;

        private BigDecimal balance;

        private BigDecimal vicewallet;

        /**
         * 部门
         */
        private String departName;

        /**
         * 当前余额
         *
         * @return
         */
        private Double currentbalance;

        @Override
        public String getInfoId() {
            return infoid;
        }

        @Override
        public void setInfoId(String InfoId) {
            infoid = InfoId;
        }
    }

    @Data
    public static class TransactionDetail implements StatDtl {

        private String infoid;

        private String name;

        private String code;

        private BigDecimal balance;

        private BigDecimal vicewallet;
        /**
         * 创建时间
         */
        private Date createdate;

        /**
         * 金额
         */
        private BigDecimal money;

        /**
         * 交易类型
         * 1: 充值
         * 3: 退款
         * 4: 补贴
         * 6: 充值退款
         */
        private Integer tradetype;

        /**
         * 结算状态（1.未结算 2.已结算）
         */
        private Integer status;

        /**
         * 补贴类型
         */
        private Integer dynamicsType;

        /**
         * 卡号
         */
        private String cardno;

        /**
         * 交易类型
         */
        private String des;

        /**
         * 部门
         */
        private String departName;

        @Override
        public Date getRecordtime() {
            return createdate;
        }

        @Override
        public void setRecordtime(Date date) {
            createdate = date;
        }

        @Override
        public String getInfoId() {
            return infoid;
        }

        @Override
        public void setInfoId(String InfoId) {
            infoid = InfoId;
        }
    }

    private interface StatDtl {

        Date getRecordtime();

        void setRecordtime(Date date);

        String getInfoId();

        void setInfoId(String InfoId);
    }

    public List<DailyTransactionStatDto> getStatResult(HttpServletRequest request, String start, String end, String infoid, String orgcode, boolean viewchild) {
        // 消费表获取数据并合并
        LinkedHashMap<String, List<StatDtl>> dailyConsumeDtl = getDailyConsumeDtl(request, start, end, infoid, orgcode, viewchild);
        LinkedHashMap<String, List<StatDtl>> dailyConsumeDtlSleep = getDailyConsumeDtlSleep(request, start, end, infoid, orgcode, viewchild);
        LinkedHashMap<String, List<StatDtl>> mergedConsumeRes = mergeConsumeDtl(dailyConsumeDtl, dailyConsumeDtlSleep);

//         交易表获取数据并合并
        LinkedHashMap<String, List<StatDtl>> transactionDtl = getTransactionDtl(request, start, end, infoid, orgcode, viewchild);
        LinkedHashMap<String, List<StatDtl>> transactionDtlSleep = getTransactionDtlSleep(request, start, end, infoid, orgcode, viewchild);
        LinkedHashMap<String, List<StatDtl>> dateListLinkedHashMap = mergeConsumeDtl(transactionDtl, transactionDtlSleep);

        List<DailyTransactionStatDto> res = new ArrayList<>();
        LinkedHashMap<String, DailyTransactionStatDto> resMap = new LinkedHashMap<>();

        mergedConsumeRes.forEach((info, statDtls) -> {
            DailyTransactionStatDto dto = new DailyTransactionStatDto();
            double mainWalletSum = 0;
            double subWalletSum = 0;
            String name = "";
            String code = "";
            String departName = "";
            // 获取最新记录的余额
            StatDtl latestRecord = statDtls.stream()
                    .sorted(Comparator.comparing(StatDtl::getRecordtime).reversed())
                    .limit(1)
                    .findFirst()
                    .orElse(null);
            ConsumeDtl latestRecord1 = (ConsumeDtl) latestRecord;
            for (StatDtl _consumeDtl : statDtls) {
                ConsumeDtl consumeDtl = (ConsumeDtl) _consumeDtl;
                name = consumeDtl.getName();
                code = consumeDtl.getCode();
                departName = consumeDtl.getDepartName();
                // 不考虑精度丢失问题
                double money = NumberUtils.toDouble(consumeDtl.getMoney());
                if (Objects.equals(consumeDtl.getPaywallet(), 1)) {
                    // 主钱包
                    mainWalletSum += money;
                } else if ((Objects.equals(consumeDtl.getPaywallet(), 2))) {
                    subWalletSum += money;
                }
            }
            dto.setConsumeMainWalletSum(mainWalletSum)
                    .setConsumeSubWalletSum(subWalletSum)
                    .setConsumeSum(mainWalletSum + subWalletSum)
                    .setTime(latestRecord1 == null ? new Date() : latestRecord1.getRecordtime())
                    .setInfoId(info)
                    .setCode(code)
                    .setDepartName(departName)
                    .setInfoName(name)
                    .setSum(latestRecord1 == null ? 0.0 : latestRecord1.getCurrentbalance())
                    .setPerformanceRewardSum(0.0)
                    .setCompanyAllowanceSum(0.0)
                    .setRefundChargeSum(0.0)
                    .setRefundSum(0.0)
                    .setChargeSum(0.0);
            resMap.put(info, dto);
        });

        dateListLinkedHashMap.forEach((info, statDtls) -> {
            DailyTransactionStatDto dto = Optional.ofNullable(resMap.get(info))
                    .orElseGet(() ->
                            new DailyTransactionStatDto()
                                    .setInfoId(info)
                                    .setConsumeMainWalletSum(0.0)
                                    .setConsumeSubWalletSum(0.0)
                                    .setConsumeSum(0.0)
                                    .setSum(0.0));

            double refund = 0;
            double charge = 0;
            double chargeRefund = 0;
            double performanceReward = 0;
            double companyAllowance = 0;
            String name = "";
            String code = "";
            String departName = "";
            Date currtTime = new Date();

            if (dto.getSum() == 0.0) {
                //查询一个月的记录离得最近的
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(currtTime);
                Date endTime = calendar.getTime();
                calendar.add(Calendar.MONTH, -1);
                Date startDate = calendar.getTime();

                // 转换为字符串格式
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String startDateStr = sdf.format(startDate);
                String endTimeStr = sdf.format(endTime);
                JSONObject object = getCurrentMoney(request, startDateStr, endTimeStr, info);
                if (object != null) {
                    Double currentbalance = object.getDouble("currentbalance");
                    dto.setSum(currentbalance);
                    Date createdate = object.getDate("createdate");
                    dto.setTime(createdate);
                } else {
                    dto.setSum(charge + refund);
                }
            }

            for (StatDtl _consumeDtl : statDtls) {
                TransactionDetail dtl = (TransactionDetail) _consumeDtl;
                if (dto.getTime() == null) {
                    dto.setTime(dtl.getRecordtime());
                }
                // 不考虑精度丢失问题
                name = dtl.getName();
                code = dtl.getCode();
                departName = dtl.getDepartName();
                double money = NumberUtils.toDouble(dtl.getMoney());
                if (Objects.equals(dtl.getTradetype(), 1)) {
                    charge += money;
                } else if (Objects.equals(dtl.getTradetype(), 3)) {
                    refund += money;
                } else if (Objects.equals(dtl.getTradetype(), 6)) {
                    chargeRefund += money;
                } else if (Objects.equals(dtl.getTradetype(), 4) && ("补贴".equals(dtl.getDes()) || "绩效补贴".equals(dtl.getDes()))) {
                    if (Objects.equals(dtl.getDynamicsType(), 1)) {
                        performanceReward += money;
                    } else {
                        companyAllowance += money;
                    }
                }
                if (dtl.getRecordtime() != null) {
                    if (dtl.getRecordtime().after(dto.getTime())) {
                        dto.setSum(dto.getSum() + charge + refund);
                    }
                }
            }
            dto.setChargeSum(charge);
            dto.setRefundSum(refund);
            dto.setCompanyAllowanceSum(companyAllowance);
            dto.setPerformanceRewardSum(performanceReward);
            dto.setRefundChargeSum(chargeRefund);
            dto.setInfoName(name);
            dto.setCode(code);
            dto.setDepartName(departName);
            resMap.put(info, dto);
        });
        resMap.forEach((info, dailyTransactionStatDto) -> {
            res.add(dailyTransactionStatDto);
        });
        DailyTransactionStatDto reduce = res.stream()
                .reduce(new DailyTransactionStatDto(),
                        (result, dto) -> {
                            result.setChargeSum(Optional.ofNullable(result.getChargeSum()).orElse(0.0) + dto.getChargeSum());
                            result.setCompanyAllowanceSum(Optional.ofNullable(result.getCompanyAllowanceSum()).orElse(0.0) + dto.getCompanyAllowanceSum());
                            result.setPerformanceRewardSum(Optional.ofNullable(result.getPerformanceRewardSum()).orElse(0.0) + dto.getPerformanceRewardSum());
                            result.setConsumeMainWalletSum(Optional.ofNullable(result.getConsumeMainWalletSum()).orElse(0.0) + dto.getConsumeMainWalletSum());
                            result.setConsumeSubWalletSum(Optional.ofNullable(result.getConsumeSubWalletSum()).orElse(0.0) + dto.getConsumeSubWalletSum());
                            result.setConsumeSum(Optional.ofNullable(result.getConsumeSum()).orElse(0.0) + dto.getConsumeSum());
                            result.setRefundSum(Optional.ofNullable(result.getRefundSum()).orElse(0.0) + dto.getRefundSum());
                            result.setRefundChargeSum(Optional.ofNullable(result.getRefundChargeSum()).orElse(0.0) + dto.getRefundChargeSum());
                            result.setSum(Optional.ofNullable(result.getSum()).orElse(0.0) + dto.getSum());
                            return result;
                        });
        res.add(reduce.setCode("总合计"));
        return res;
    }

    public List<DailyTransactionStatDto> getStatResultMoney(HttpServletRequest request, String start, String end, String infoid, String orgcode, boolean viewchild) {
        return getStatResultMoneyWithSave(request, start, end, infoid, orgcode, viewchild, false);
    }

    /**
     * 获取统计结果并可选择保存到月度报表
     *
     * @param request       请求对象
     * @param start         开始时间
     * @param end           结束时间
     * @param infoid        人员ID
     * @param orgcode       组织代码
     * @param viewchild     是否查看子级
     * @param saveToMonthly 是否保存到月度报表
     * @return 统计结果
     */
    public List<DailyTransactionStatDto> getStatResultMoneyWithSave(HttpServletRequest request, String start, String end,
                                                                    String infoid, String orgcode, boolean viewchild,
                                                                    boolean saveToMonthly) {
        try {
            Log.info(TransactionStatService.class,
                    String.format("开始统计交易数据 - 时间范围: %s 到 %s, 保存月报: %s", start, end, saveToMonthly));

            // 消费表获取数据并合并
            LinkedHashMap<String, List<StatDtl>> dailyConsumeDtl = getDailyConsumeDtl(request, start, end, infoid, orgcode, viewchild);
            LinkedHashMap<String, List<StatDtl>> dailyConsumeDtlSleep = getDailyConsumeDtlSleep(request, start, end, infoid, orgcode, viewchild);
            LinkedHashMap<String, List<StatDtl>> mergedConsumeRes = mergeConsumeDtl(dailyConsumeDtl, dailyConsumeDtlSleep);

            // 交易表获取数据并合并
            LinkedHashMap<String, List<StatDtl>> transactionDtl = getTransactionDtl(request, start, end, infoid, orgcode, viewchild);
            LinkedHashMap<String, List<StatDtl>> transactionDtlSleep = getTransactionDtlSleep(request, start, end, infoid, orgcode, viewchild);
            LinkedHashMap<String, List<StatDtl>> dateListLinkedHashMap = mergeConsumeDtl(transactionDtl, transactionDtlSleep);

            List<DailyTransactionStatDto> res = new ArrayList<>();
            LinkedHashMap<String, DailyTransactionStatDto> resMap = new LinkedHashMap<>();
            // 直接从开始时间字符串提取年月部分
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date date = dateFormat.parse(start);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.MONTH, -1);
            Date lastMonthDate = calendar.getTime();
            String lastMonth = dateFormat.format(lastMonthDate).substring(0, 7);
            String month = lastMonth.substring(0, 7);
            // 获取上个月数据
            List<MonthlyTransactionStatDto> monthlyTransactionStatDtos = getLastMonthData(request, lastMonth);

            // 第一步：处理消费数据
            mergedConsumeRes.forEach((info, statDtls) -> {
                DailyTransactionStatDto dto = new DailyTransactionStatDto();
                double mainWalletSum = 0;
                double subWalletSum = 0;
                String name = "";
                String code = "";
                String departName = "";
                double sum = 0.0;

                MonthlyTransactionStatDto monthlyTransactionStatDto = monthlyTransactionStatDtos.stream()
                        .filter(i -> i.getInfoId().equals(info))
                        .findFirst()
                        .orElse(null);

                for (StatDtl _consumeDtl : statDtls) {
                    ConsumeDtl consumeDtl = (ConsumeDtl) _consumeDtl;
                    name = consumeDtl.getName();
                    code = consumeDtl.getCode();
                    departName = consumeDtl.getDepartName();
                    double money = NumberUtils.toDouble(consumeDtl.getMoney());

                    if (Objects.equals(consumeDtl.getPaywallet(), 1)) {
                        mainWalletSum += money;
                    } else if (Objects.equals(consumeDtl.getPaywallet(), 2)) {
                        subWalletSum += money;
                    }
                }
                Double money = dto.subtractDecimals(monthlyTransactionStatDto == null ? 0.0 : monthlyTransactionStatDto.getMonthEndBalance(), dto.addDecimals(mainWalletSum, subWalletSum));
                dto.setConsumeMainWalletSum(mainWalletSum)
                        .setConsumeSubWalletSum(subWalletSum)
                        .setConsumeSum(dto.addDecimals(mainWalletSum, subWalletSum))
                        .setInfoId(info)
                        .setCode(code)
                        .setDepartName(departName)
                        .setInfoName(name)
                        .setPerformanceRewardSum(0.0)
                        .setCompanyAllowanceSum(0.0)
                        .setRefundChargeSum(0.0)
                        .setRefundSum(0.0)
                        .setChargeSum(0.0)
                        .setSum(money)
                        .setLastMonthEndBalance(monthlyTransactionStatDto == null ? 0.0 : monthlyTransactionStatDto.getMonthEndBalance())
                        .setTotalAmount(monthlyTransactionStatDto == null ? 0.0 : monthlyTransactionStatDto.getMonthEndBalance());

                resMap.put(info, dto);
            });

            // 第二步：处理交易数据（修复对象引用问题）
            dateListLinkedHashMap.forEach((info, statDtls) -> {
                // 获取已存在的消费数据
                DailyTransactionStatDto existingDto = resMap.get(info);

                // 创建新的DTO对象，避免引用共享
                DailyTransactionStatDto dto = new DailyTransactionStatDto();

                // 如果已存在消费数据，先复制消费相关字段
                if (existingDto != null) {
                    dto.setConsumeMainWalletSum(existingDto.getConsumeMainWalletSum())
                            .setConsumeSubWalletSum(existingDto.getConsumeSubWalletSum())
                            .setConsumeSum(existingDto.getConsumeSum())
                            .setInfoId(existingDto.getInfoId())
                            .setCode(existingDto.getCode())
                            .setDepartName(existingDto.getDepartName())
                            .setInfoName(existingDto.getInfoName())
                            .setLastMonthEndBalance(existingDto.getLastMonthEndBalance())
                            .setTotalAmount(existingDto.getTotalAmount())
                            .setSum(existingDto.getSum());
                } else {
                    // 设置默认值
                    dto.setInfoId(info)
                            .setConsumeMainWalletSum(0.0)
                            .setConsumeSubWalletSum(0.0)
                            .setConsumeSum(0.0)
                            .setSum(0.0);
                }

                // 计算交易数据
                double refund = 0;
                double charge = 0;
                double chargeRefund = 0;
                double performanceReward = 0;
                double companyAllowance = 0;
                double totalAllowance = 0;
                double lastMonthEndBalance = 0;
                String name = "";
                String code = "";
                String departName = "";

                for (StatDtl _consumeDtl : statDtls) {
                    TransactionDetail dtl = (TransactionDetail) _consumeDtl;
                    if (dto.getTime() == null) {
                        dto.setTime(dtl.getRecordtime());
                    }

                    name = dtl.getName();
                    code = dtl.getCode();
                    departName = dtl.getDepartName();
                    double money = NumberUtils.toDouble(dtl.getMoney());

                    if (Objects.equals(dtl.getTradetype(), 1)) {
                        charge += money;
                    } else if (Objects.equals(dtl.getTradetype(), 3)) {
                        refund += money;
                    } else if (Objects.equals(dtl.getTradetype(), 6)) {
                        chargeRefund += money;
                    } else if (Objects.equals(dtl.getTradetype(), 4) &&
                            ("补贴".equals(dtl.getDes()) || "绩效补贴".equals(dtl.getDes()))) {
                        if (Objects.equals(dtl.getDynamicsType(), 1)) {
                            performanceReward += money;
                        } else {
                            companyAllowance += money;
                        }
                    }
                }

                // 计算当前余额（使用精确计算）
                double currentBalance = 0;
                MonthlyTransactionStatDto monthlyTransactionStatDto = monthlyTransactionStatDtos.stream()
                        .filter(i -> i.getInfoId().equals(info))
                        .findFirst()
                        .orElse(null);

//                if (monthlyTransactionStatDto != null) {
                // 使用精确计算：本月收入 - 本月支出
                Double income = dto.addDecimals(charge, refund, companyAllowance, performanceReward, chargeRefund);
                Double expense = dto.getConsumeSum();
                Double netChange = dto.subtractDecimals(income, expense);

                // 加上上月余额
                if (monthlyTransactionStatDto != null && monthlyTransactionStatDto.getMonthEndBalance() != null) {
                    currentBalance = dto.addDecimals(monthlyTransactionStatDto.getMonthEndBalance(), netChange);
                    lastMonthEndBalance = monthlyTransactionStatDto.getMonthEndBalance();
                    totalAllowance = lastMonthEndBalance + charge;
                } else {
                    currentBalance = netChange;
                    totalAllowance = charge;
                }
//                }
                // 设置交易相关数据
                dto.setSum(currentBalance)
                        .setChargeSum(charge)
                        .setRefundSum(refund)
                        .setCompanyAllowanceSum(companyAllowance)
                        .setPerformanceRewardSum(performanceReward)
                        .setRefundChargeSum(chargeRefund)
                        .setInfoName(name)
                        .setCode(code)
                        .setDepartName(departName)
                        .setLastMonthEndBalance(lastMonthEndBalance)
                        .setTotalAmount(totalAllowance);
                // 放入map（这次是真正的替换）
                resMap.put(info, dto);
            });
            // 第三步：收集结果
            resMap.forEach((info, dailyTransactionStatDto) -> {
                res.add(dailyTransactionStatDto);
            });

            // 第三步：得到所有人员
            List<DailyTransactionStatDto> allInfoDtoList = getAllInfoList(request, start, end, infoid, orgcode, viewchild);


            //得到未收集对象单独处理
            List<DailyTransactionStatDto> transactionStatDtos = allInfoDtoList.stream()
                    .filter(i -> !resMap.containsKey(i.getInfoId()))
                    .collect(Collectors.toList());

            //单独处理，然后存放到res中
            for (DailyTransactionStatDto transactionStatDto : transactionStatDtos) {

                MonthlyTransactionStatDto monthlyTransactionStatDto = monthlyTransactionStatDtos.stream()
                        .filter(i -> i.getInfoId().equals(transactionStatDto.getInfoId()))
                        .findFirst()
                        .orElse(null);

                transactionStatDto.setSum(monthlyTransactionStatDto==null?0.0:monthlyTransactionStatDto.getMonthEndBalance())
                        .setLastMonthEndBalance(monthlyTransactionStatDto==null?0.0:monthlyTransactionStatDto.getMonthEndBalance())
                        .setTotalAmount(monthlyTransactionStatDto==null?0.0:monthlyTransactionStatDto.getMonthEndBalance())
                        .setChargeSum(0.0)
                        .setConsumeMainWalletSum(0.0)
                        .setConsumeSubWalletSum(0.0)
                        .setConsumeSum(0.0)
                        .setRefundSum(0.0)
                        .setPerformanceRewardSum(0.0)
                        .setCompanyAllowanceSum(0.0)
                        .setRefundChargeSum(0.0);
                res.add(transactionStatDto);
            }

            // 第四步：计算总合计
            DailyTransactionStatDto reduce = res.stream()
                    .reduce(new DailyTransactionStatDto(),
                            (result, dto) -> {
                                // 使用精确的加法计算，避免精度丢失
                                result.setChargeSum(result.addDecimals(result.getChargeSum(), dto.getChargeSum()));
                                result.setCompanyAllowanceSum(result.addDecimals(result.getCompanyAllowanceSum(), dto.getCompanyAllowanceSum()));
                                result.setPerformanceRewardSum(result.addDecimals(result.getPerformanceRewardSum(), dto.getPerformanceRewardSum()));
                                result.setConsumeMainWalletSum(result.addDecimals(result.getConsumeMainWalletSum(), dto.getConsumeMainWalletSum()));
                                result.setConsumeSubWalletSum(result.addDecimals(result.getConsumeSubWalletSum(), dto.getConsumeSubWalletSum()));
                                result.setConsumeSum(result.addDecimals(result.getConsumeSum(), dto.getConsumeSum()));
                                result.setRefundSum(result.addDecimals(result.getRefundSum(), dto.getRefundSum()));
                                result.setRefundChargeSum(result.addDecimals(result.getRefundChargeSum(), dto.getRefundChargeSum()));
                                result.setSum(result.addDecimals(result.getSum(), dto.getSum()));
                                return result;
                            });
            res.add(reduce.setCode("总合计"));

            // 第五步：保存到月度报表（如果需要）
            if (saveToMonthly && !res.isEmpty()) {
                try {
                    String currentMonth = start.substring(0, 7);
                    saveToMonthlyReport(res, currentMonth, start, end);
                } catch (Exception e) {
                    Log.error(TransactionStatService.class,
                            String.format("保存月度报表失败: %s", e.getMessage()));
                    // 不影响主流程，只记录错误
                }
            }

            Log.info(TransactionStatService.class,
                    String.format("统计完成 - 记录数: %d, 月份: %s", res.size(), month));

            return res;

        } catch (Exception e) {
            Log.error(TransactionStatService.class,
                    String.format("统计交易数据异常: %s", e.getMessage()));
            throw new RuntimeException("统计交易数据失败", e);
        }
    }

    /**
     * 保存到月度报表
     *
     * @param dailyStatList 日统计数据列表
     * @param month         月份
     * @param start         开始时间
     * @param end           结束时间
     */
    private void saveToMonthlyReport(List<DailyTransactionStatDto> dailyStatList, String month, String start, String end) {
        try {
            Log.info(TransactionStatService.class,
                    String.format("开始保存月度报表 - 月份: %s, 记录数: %d", month, dailyStatList.size()));

            // 过滤掉总合计行
            List<DailyTransactionStatDto> validData = dailyStatList.stream()
                    .filter(dto -> !"总合计".equals(dto.getCode()))
                    .collect(Collectors.toList());

            if (validData.isEmpty()) {
                Log.warn(TransactionStatService.class, "没有有效数据需要保存到月度报表");
                return;
            }

            // 使用月度统计服务保存数据
            JsonResult result = monthlyTransactionStatService.generateMonthlyFromDaily(validData, month);

            if (result.isSuccess()) {
                Log.info(TransactionStatService.class,
                        String.format("月度报表保存成功 - 月份: %s, 人数: %d", month, validData.size()));
            } else {
                Log.error(TransactionStatService.class,
                        String.format("月度报表保存失败 - 月份: %s, 错误: %s", month, result.getMsg()));
            }

        } catch (Exception e) {
            Log.error(TransactionStatService.class,
                    String.format("保存月度报表异常 - 月份: %s, 错误: %s", month, e.getMessage()));
            throw new RuntimeException("保存月度报表失败", e);
        }
    }
    /**
     * 获取月份结束时间
     *
     * @param month 月份，格式：YYYY-MM
     * @return 结束时间字符串
     */
    private String getMonthEndTime(String month) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Date monthDate = sdf.parse(month);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(monthDate);
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);

            SimpleDateFormat endSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return endSdf.format(calendar.getTime());

        } catch (Exception e) {
            Log.error(TransactionStatService.class,
                    "计算月份结束时间失败: " + e.getMessage());
            return month + "-31 23:59:59"; // 默认值
        }
    }

    /**
     * @param request
     * @param month   统计月份，格式：YYYY-MM
     * @return
     */
    private List<MonthlyTransactionStatDto> getLastMonthData(HttpServletRequest request, String month) {
        String sql = "select * from tb_monthly_transaction_stat where stat_month = '" + month + "'";
        return dbService.queryList(sql, MonthlyTransactionStatDto.class);
    }


    private LinkedHashMap<String, List<StatDtl>> getDailyConsumeDtl(HttpServletRequest request, String start, String end, String infoIds, String orgcode, boolean viewchild) {
        String sql = "select cp.infoid, cp.cardid, cp.cardno, cp.recordtime, money, paywallet,ct.name,ct.code,getorgname(ct.orgcode) as departName,cp.status,cp.currentbalance " +
                " from tb_consume_payrecords cp left join tb_card_teachstudinfo ct on ct.uid = cp.infoid ";
        SysUser user = userRedis.get(request);
        if (user != null && user.getUsertype() == 1) {
            String userid = user.getUid();
            sql += " inner join tb_card_orgframework co on co.code = ct.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='" + userid + "'";
        }
        sql += " where recordtime between '" + start + "' and '" + end + "'";
        sql = getString(infoIds, sql, orgcode, viewchild);
        List<ConsumeDtl> consumeDtls = dbService.queryList(sql, ConsumeDtl.class);
        return sortByInfo(consumeDtls);
    }

    private List<DailyTransactionStatDto> getAllInfoList(HttpServletRequest request, String start, String end, String infoIds, String orgcode, boolean viewchild) {
        String sql = "select ct.uid infoId,ct.name infoName,ct.code,getorgname(ct.orgcode) as departName " +
                " from  tb_card_teachstudinfo ct ";
        SysUser user = userRedis.get(request);
        if (user != null && user.getUsertype() == 1) {
            String userid = user.getUid();
            sql += " inner join tb_card_orgframework co on co.code = ct.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='" + userid + "'";
        }
        sql += " where 1=1 ";
        sql = getString(infoIds, sql, orgcode, viewchild);
        sql += " order by ct.name ";
        List<DailyTransactionStatDto> consumeDtls = dbService.queryList(sql, DailyTransactionStatDto.class);
        return consumeDtls;
    }

    private LinkedHashMap<String, List<StatDtl>> getDailyConsumeDtlSleep(HttpServletRequest request, String start, String end, String infoIds, String orgcode, boolean viewchild) {
        String sql = "select cp.infoid, cp.cardid,cp.cardno, cp.recordtime, money, paywallet,ct.name,ct.code,getorgname(ct.orgcode) as departName,cp.status,cp.currentbalance " +
                " from tb_consume_payrecords_sleep cp left join tb_card_teachstudinfo ct on ct.uid = cp.infoid ";
        SysUser user = userRedis.get(request);
        if (user != null && user.getUsertype() == 1) {
            String userid = user.getUid();
            sql += " inner join tb_card_orgframework co on co.code = ct.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='" + userid + "'";
        }
        sql += "where recordtime between '" + start + "' and '" + end + "'";
        sql = getString(infoIds, sql, orgcode, viewchild);
        List<ConsumeDtl> consumeDtls = dbService.queryList(sql, ConsumeDtl.class);
        return sortByInfo(consumeDtls);
    }

    private LinkedHashMap<String, List<StatDtl>> getTransactionDtl(HttpServletRequest request, String start, String end, String infoIds, String orgcode, boolean viewchild) {
        String sql = "select money,td.infoid,td.des,td.cardno,tradetype,td.createdate,ct.name,ct.code,td.dynamics_type,getorgname(ct.orgcode) as departName,td.status " +
                "from tb_card_transaction_detail td left join tb_card_teachstudinfo ct on ct.uid = td.infoid ";
        SysUser user = userRedis.get(request);
        if (user != null && user.getUsertype() == 1) {
            String userid = user.getUid();
            sql += " inner join tb_card_orgframework co on co.code = ct.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='" + userid + "'";
        }
        sql += "where tradedate between '" + start + "' and '" + end + "'";
        sql = getString(infoIds, sql, orgcode, viewchild);
        List<TransactionDetail> consumeDtls = dbService.queryList(sql, TransactionDetail.class);
        return sortByInfo(consumeDtls);
    }

    private LinkedHashMap<String, List<StatDtl>> getTransactionDtlSleep(HttpServletRequest request, String start, String end, String infoIds, String orgcode, boolean viewchild) {
        String sql = "select money,td.cardno,td.des,td.infoid,td.tradetype,td.createdate,ct.name,ct.code,td.dynamics_type,getorgname(ct.orgcode) as departName,td.status " +
                " from tb_card_transaction_detail_sleep td left join tb_card_teachstudinfo ct on ct.uid = td.infoid ";
        SysUser user = userRedis.get(request);
        if (user != null && user.getUsertype() == 1) {
            String userid = user.getUid();
            sql += " inner join tb_card_orgframework co on co.code = ct.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='" + userid + "'";
        }
        sql += "where tradedate between '" + start + "' and '" + end + "'";
        sql = getString(infoIds, sql, orgcode, viewchild);
        List<TransactionDetail> consumeDtls = dbService.queryList(sql, TransactionDetail.class);
        return sortByInfo(consumeDtls);
    }


    private String getString(String infoIds, String sql, String orgcode, boolean viewchild) {
        if (StringUtils.isNotBlank(orgcode)) {
            if (viewchild) {
                sql += " and ct.orgcode like '" + orgcode + "%' ";
            } else {
                sql += " and ct.orgcode='" + orgcode + "' ";
            }
            return sql;
        }
        if (StringUtils.isNotBlank(infoIds)) {
            StringJoiner joiner = new StringJoiner(",", "('", "')");
            for (String s : infoIds.split(",")) {
                joiner.add(s);
            }
            sql += " and ct.uid in " + joiner;
        }
        return sql;
    }

    /**
     * 将日期时分秒敲掉
     */
    private void pureRecordDay(List<? extends StatDtl> statDtls) {
        Calendar calendar = Calendar.getInstance();
        for (StatDtl statDtl : statDtls) {
            Date recordTime = statDtl.getRecordtime();
            calendar.setTime(recordTime);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            statDtl.setRecordtime(calendar.getTime());
        }
    }

    private LinkedHashMap<Date, List<StatDtl>> fill(LinkedHashMap<Date, List<StatDtl>> map, String start) {
        ArrayList<Date> dates = new ArrayList<>(map.keySet());
        dates.sort(Comparator.comparing(Date::getTime));
        ArrayDeque<Date> que = new ArrayDeque<>();
        Calendar calendar = Calendar.getInstance();

        for (Date date : dates) {
            Date peek = que.peek();
            if (peek == null) {
                Date startDate = DateUtil.parse(start, "yyyy-MM-dd");
                calendar.setTime(startDate);
                // 相差天数
                long diff = DateUtil.betweenDay(startDate, date, false);
                que.push(startDate);
                for (long l = 0; l < diff; l++) {
                    // 插入空日期
                    calendar.add(Calendar.DATE, 1);
                    que.push(calendar.getTime());
                }
                continue;
            }
            calendar.setTime(peek);
            // 相差天数
            long diff = DateUtil.betweenDay(peek, date, false);
            for (long l = 0; l < diff; l++) {
                // 插入空日期
                calendar.add(Calendar.DATE, 1);
                que.push(calendar.getTime());
            }
        }
        LinkedHashMap<Date, List<StatDtl>> res = new LinkedHashMap<>();
        for (Date date : que) {
            List<StatDtl> statDtls = map.get(date);
            if (statDtls == null) {
                ArrayList<Object> objects = new ArrayList<>();
                res.put(date, new ArrayList<>());
                continue;
            }
            res.put(date, statDtls);
        }
        return res;
    }

    /**
     * 补没有数据的日期
     */
    @SneakyThrows
    private <T extends StatDtl> List<T> fillNullDate(List<? extends StatDtl> statDtls, String start) {
        statDtls.sort(Comparator.comparing(StatDtl::getRecordtime));
        ArrayDeque<T> que = new ArrayDeque<>();

        for (StatDtl statDtl : statDtls) {
            T peek = que.peek();
            if (peek == null) {
                // 补全选定日期到数据实际日期间的日期数据
                Date startDate = DateUtil.parse(start, "yyyy-MM-dd");
                fillData(que, (T) statDtl, startDate);
                que.push((T) statDtl);
                continue;
            }
            fillData(que, (T) statDtl, peek.getRecordtime());
            que.push((T) statDtl);
        }
        List<T> res = new ArrayList<>(que.size());
        res.addAll(que);
        return res;
    }

    private <T extends StatDtl> void fillData(ArrayDeque<T> que, T statDtl, Date startDate) throws Exception {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        // 相差天数
        long diff = DateUtil.betweenDay(startDate, statDtl.getRecordtime(), false);
        for (long l = 0; l < diff; l++) {
            // 插入空日期
            T t = getFillData(statDtl, calendar.getTime());
            calendar.add(Calendar.DATE, 1);
            que.push(t);
        }
    }

    private <T extends StatDtl> T getFillData(T statDtl, Date date) throws Exception {
        T t = (T) statDtl.getClass().newInstance();
        t.setRecordtime(date);
        return t;
    }

//    private LinkedHashMap<Date, List<StatDtl>> sortByTime(List<? extends StatDtl> consumeDtls) {
//        Calendar calendar = Calendar.getInstance();
//        return consumeDtls.stream()
//                .peek(i -> {
//                    Date recordTime = i.getRecordtime();
//                    calendar.setTime(recordTime);
//                    calendar.set(Calendar.HOUR_OF_DAY, 0);
//                    calendar.set(Calendar.MINUTE, 0);
//                    calendar.set(Calendar.SECOND, 0);
//                    i.setRecordtime(calendar.getTime());
//                })
//                .sorted(Comparator.comparing(StatDtl::getRecordtime))
//                .collect(Collectors
//                        .groupingBy(StatDtl::getRecordtime,
//                                LinkedHashMap::new,
//                                Collectors.mapping(i -> i, Collectors.toList())));
//    }


    private LinkedHashMap<String, List<StatDtl>> sortByInfo(List<? extends StatDtl> consumeDtls) {
        return consumeDtls.stream()
                .filter(statDtl -> statDtl.getInfoId() != null) // 过滤掉getInfoId为null的元素
                .sorted(Comparator.comparing(StatDtl::getRecordtime))
                .collect(Collectors.groupingBy(
                        StatDtl::getInfoId,
                        LinkedHashMap::new,
                        Collectors.mapping(i -> i, Collectors.toList())
                ));
    }


    /**
     * 冷热表数据合并
     */
    private LinkedHashMap<String, List<StatDtl>> mergeConsumeDtl(Map<String, List<StatDtl>> map,
                                                                 Map<String, List<StatDtl>> sleepMap) {
        LinkedHashMap<String, List<StatDtl>> mergedMap = new LinkedHashMap<>((int) Math.ceil(map.size() * 1.5));
        mergedMap.putAll(sleepMap);
        map.forEach((str, consumeDtls) -> mergedMap.computeIfAbsent(str, key -> new ArrayList<>()).addAll(consumeDtls));
        return mergedMap;
    }

    public R<Null> ExportData(HttpServletRequest request, String start, String end, String infoid, String orgcode, boolean viewchild) {
        List<DailyTransactionStatDto> personRate = getStatResultMoney(request, start, end, infoid, orgcode, viewchild);
        if (personRate.size() == 0) {
            return R.fail("无导出数据");
        }
        List<DailyTransactionStatRow> rows = personRate.stream()
                .map(dto ->
                        new DailyTransactionStatRow(
                                dto.getTime(),
                                dto.getInfoId(),
                                dto.getInfoName(),
                                dto.getCode(),
                                dto.getDepartName(),
                                dto.getLastMonthEndBalance(),
                                dto.getTotalAmount(),
                                dto.getChargeSum(),
                                dto.getPerformanceRewardSum(),
                                dto.getCompanyAllowanceSum(),
                                dto.getConsumeMainWalletSum(),
                                dto.getConsumeSubWalletSum(),
                                dto.getConsumeSum(),
                                dto.getRefundSum(),
                                dto.getRefundChargeSum(),
                                dto.getSum()
                        )).collect(Collectors.toList());
        LinkedHashMap<Integer, DailyTransactionStatRow> map = new LinkedHashMap<>();
        Sheet<DailyTransactionStatRow> sheet = new Sheet<>(map, DailyTransactionStatRow.class);
        int row = 1;
        for (DailyTransactionStatRow vo : rows) {
            map.put(row, vo);
            row++;
        }
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String extFilePath = "temp/收支汇总表" + format + ".xlsx";
        String filePath = WebConfig.getUploaddir() + extFilePath;
        com.ymiots.framework.utils.excel.ExcelUtil.write(sheet, filePath, "收支汇总表", 11, start, end);

        return R.ok(extFilePath);
    }

    private JSONObject getCurrentMoney(HttpServletRequest request, String start, String end, String infoid) {
        String sql = "(" +
                "SELECT currentbalance,createdate FROM tb_consume_payrecords " +
                "WHERE infoid = '" + infoid + "' AND createdate BETWEEN '" + start + "' AND '" + end + "'" +
                ") UNION ALL (" +
                "SELECT currentbalance,createdate FROM tb_consume_payrecords_sleep " +
                "WHERE infoid = '" + infoid + "' AND createdate BETWEEN '" + start + "' AND '" + end + "'" +
                ") ORDER BY createdate DESC LIMIT 1";

        JSONObject jsonObject = dbService.QueryJSONObject(sql);
        return jsonObject;
    }
}
