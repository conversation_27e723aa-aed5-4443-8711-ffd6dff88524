package com.ymiots.campusos.service.consume;

import cn.hutool.core.date.DateUtil;
import com.ymiots.campusos.common.DBService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.common.r.R;
import com.ymiots.campusos.dto.DailyTransactionStatDto;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.excel.table.DailyTransactionStatRow;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.utils.excel.Sheet;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Null;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/23 13:46
 */
@Service
public class TransactionStatService {

    @Autowired
    DBService dbService;

    @Autowired
    UserRedis userRedis;

    /**
     * 消费详情
     */
    @Data
    private static class ConsumeDtl implements StatDtl {

        private String infoid;

        private String name;

        private String code;

        private String cardid;

        private String cardno;

        /**
         * 结算状态（1.未结算 2.已结算）
         */
        private Integer status;

        private Date recordtime;

        private BigDecimal money;

        private Integer paywallet;

        private BigDecimal balance;

        private BigDecimal vicewallet;

        /**
         * 部门
         */
        private String departName;

        @Override
        public String getInfoId() {
            return infoid;
        }

        @Override
        public void setInfoId(String InfoId) {
            infoid = InfoId;
        }
    }

    @Data
    public static class TransactionDetail implements StatDtl {

        private String infoid;

        private String name;

        private String code;

        private BigDecimal balance;

        private BigDecimal vicewallet;
        /**
         * 创建时间
         */
        private Date createdate;

        /**
         * 金额
         */
        private BigDecimal money;

        /**
         * 交易类型
         * 1: 充值
         * 3: 退款
         * 4: 补贴
         * 6: 充值退款
         */
        private Integer tradetype;

        /**
         * 结算状态（1.未结算 2.已结算）
         */
        private Integer status;

        /**
         * 补贴类型
         */
        private Integer dynamicsType;

        /**
         * 卡号
         */
        private String cardno;

        /**
         * 交易类型
         */
        private String des;

        /**
         * 部门
         */
        private String departName;

        @Override
        public Date getRecordtime() {
            return createdate;
        }

        @Override
        public void setRecordtime(Date date) {
            createdate = date;
        }

        @Override
        public String getInfoId() {
            return infoid;
        }

        @Override
        public void setInfoId(String InfoId) {
            infoid = InfoId;
        }
    }

    private interface StatDtl {

        Date getRecordtime();

        void setRecordtime(Date date);

        String getInfoId();

        void setInfoId(String InfoId);
    }

    public List<DailyTransactionStatDto> getStatResult(HttpServletRequest request, String start, String end, String infoid, String orgcode, boolean viewchild) {
        // 消费表获取数据并合并
        LinkedHashMap<String, List<StatDtl>> dailyConsumeDtl = getDailyConsumeDtl(request, start, end, infoid, orgcode, viewchild);
        LinkedHashMap<String, List<StatDtl>> dailyConsumeDtlSleep = getDailyConsumeDtlSleep(request, start, end, infoid, orgcode, viewchild);
        LinkedHashMap<String, List<StatDtl>> mergedConsumeRes = mergeConsumeDtl(dailyConsumeDtl, dailyConsumeDtlSleep);

//         交易表获取数据并合并
        LinkedHashMap<String, List<StatDtl>> transactionDtl = getTransactionDtl(request, start, end, infoid, orgcode, viewchild);
        LinkedHashMap<String, List<StatDtl>> transactionDtlSleep = getTransactionDtlSleep(request, start, end, infoid, orgcode, viewchild);
        LinkedHashMap<String, List<StatDtl>> dateListLinkedHashMap = mergeConsumeDtl(transactionDtl, transactionDtlSleep);

        List<DailyTransactionStatDto> res = new ArrayList<>();
        LinkedHashMap<String, DailyTransactionStatDto> resMap = new LinkedHashMap<>();

        mergedConsumeRes.forEach((info, statDtls) -> {
            DailyTransactionStatDto dto = new DailyTransactionStatDto();
            double mainWalletSum = 0;
            double subWalletSum = 0;
            double unsettledConsumeSum = 0;
            double sum = 0;
            String name = "";
            String code = "";
            String departName = "";
            for (StatDtl _consumeDtl : statDtls) {
                ConsumeDtl consumeDtl = (ConsumeDtl) _consumeDtl;
                name = consumeDtl.getName();
                code = consumeDtl.getCode();
                departName = consumeDtl.getDepartName();

                //主副钱包总余额
                sum = NumberUtils.toDouble(consumeDtl.getVicewallet()) + NumberUtils.toDouble(consumeDtl.getBalance());
                // 不考虑精度丢失问题
                double money = NumberUtils.toDouble(consumeDtl.getMoney());
                if (Objects.equals(consumeDtl.getPaywallet(), 1)) {
                    // 主钱包
                    mainWalletSum += money;
                } else if ((Objects.equals(consumeDtl.getPaywallet(), 2))) {
                    subWalletSum += money;
                }
                if (consumeDtl.getStatus() == 1) {
                    unsettledConsumeSum += money;
                }
            }
            dto.setConsumeMainWalletSum(mainWalletSum)
                    .setConsumeSubWalletSum(subWalletSum)
                    .setConsumeSum(mainWalletSum + subWalletSum)
                    .setTime(dto.getTime())
                    .setInfoId(info)
                    .setCode(code)
                    .setDepartName(departName)
                    .setInfoName(name)
                    .setSum(sum-unsettledConsumeSum)
                    .setPerformanceRewardSum(0.0)
                    .setCompanyAllowanceSum(0.0)
                    .setRefundChargeSum(0.0)
                    .setRefundSum(0.0)
                    .setChargeSum(0.0);
            resMap.put(info, dto);
        });

        dateListLinkedHashMap.forEach((info, statDtls) -> {
            DailyTransactionStatDto dto = Optional.ofNullable(resMap.get(info))
                    .orElseGet(() ->
                            new DailyTransactionStatDto()
                                    .setInfoId(info)
                                    .setConsumeMainWalletSum(0.0)
                                    .setConsumeSubWalletSum(0.0)
                                    .setConsumeSum(0.0));

            double refund = 0;
            double charge = 0;
            double chargeRefund = 0;
            double performanceReward = 0;
            double companyAllowance = 0;
            double unsettledTransactionSum = 0;
            double sum = 0;

            String name = "";
            String code = "";
            String departName = "";
            for (StatDtl _consumeDtl : statDtls) {
                TransactionDetail dtl = (TransactionDetail) _consumeDtl;
                // 不考虑精度丢失问题
                name = dtl.getName();
                code = dtl.getCode();
                departName = dtl.getDepartName();
                //主副钱包总余额
                sum = NumberUtils.toDouble(dtl.getVicewallet()) + NumberUtils.toDouble(dtl.getBalance());
                double money = NumberUtils.toDouble(dtl.getMoney());
                if (Objects.equals(dtl.getTradetype(), 1)) {
                    charge += money;
                } else if (Objects.equals(dtl.getTradetype(), 3)) {
                    refund += money;
                } else if (Objects.equals(dtl.getTradetype(), 6)) {
                    chargeRefund += money;
                } else if (Objects.equals(dtl.getTradetype(), 4) && ("补贴".equals(dtl.getDes()) || "绩效补贴".equals(dtl.getDes()))) {
                    if (Objects.equals(dtl.getDynamicsType(), 1)) {
                        performanceReward += money;
                    } else {
                        companyAllowance += money;
                    }
                }
                if (dtl.getStatus() == 1) {
                    unsettledTransactionSum += money;
                }
            }
            dto.setChargeSum(charge);
            dto.setRefundSum(refund);
            dto.setCompanyAllowanceSum(companyAllowance);
            dto.setPerformanceRewardSum(performanceReward);
            dto.setRefundChargeSum(chargeRefund);
            dto.setInfoName(name);
            dto.setCode(code);
            dto.setDepartName(departName);
            if (dto.getSum()==null) {
                dto.setSum(sum + unsettledTransactionSum);
            } else {
                dto.setSum(dto.getSum()+unsettledTransactionSum);
            }
            resMap.put(info, dto);
        });
        resMap.forEach((info, dailyTransactionStatDto) -> {
            res.add(dailyTransactionStatDto);
        });
        DailyTransactionStatDto reduce = res.stream()
                .reduce(new DailyTransactionStatDto(),
                        (result, dto) -> {
                            result.setChargeSum(Optional.ofNullable(result.getChargeSum()).orElse(0.0) + dto.getChargeSum());
                            result.setCompanyAllowanceSum(Optional.ofNullable(result.getCompanyAllowanceSum()).orElse(0.0) + dto.getCompanyAllowanceSum());
                            result.setPerformanceRewardSum(Optional.ofNullable(result.getPerformanceRewardSum()).orElse(0.0) + dto.getPerformanceRewardSum());
                            result.setConsumeMainWalletSum(Optional.ofNullable(result.getConsumeMainWalletSum()).orElse(0.0) + dto.getConsumeMainWalletSum());
                            result.setConsumeSubWalletSum(Optional.ofNullable(result.getConsumeSubWalletSum()).orElse(0.0) + dto.getConsumeSubWalletSum());
                            result.setConsumeSum(Optional.ofNullable(result.getConsumeSum()).orElse(0.0) + dto.getConsumeSum());
                            result.setRefundSum(Optional.ofNullable(result.getRefundSum()).orElse(0.0) + dto.getRefundSum());
                            result.setRefundChargeSum(Optional.ofNullable(result.getRefundChargeSum()).orElse(0.0) + dto.getRefundChargeSum());
                            result.setSum(Optional.ofNullable(result.getSum()).orElse(0.0) + dto.getSum());
                            return result;
                        });
        res.add(reduce.setCode("总合计"));
        return res;
    }

    private LinkedHashMap<String, List<StatDtl>> getDailyConsumeDtl(HttpServletRequest request, String start, String end, String infoIds, String orgcode, boolean viewchild) {
        String sql = "select cc.infoid, cp.cardid, cc.cardno, cp.recordtime, money, paywallet,ct.name,ct.code,getorgname(ct.orgcode) as departName,cc.balance,cc.vicewallet,cp.status " +
                "from tb_consume_payrecords cp left join tb_card_teachstudinfo ct on ct.uid = cp.infoid " +
                " left join tb_card_cardinfo cc on cc.infoid = ct.uid and cc.status != 0 ";
        SysUser user = userRedis.get(request);
        if (user.getUsertype() == 1) {
            String userid = user.getUid();
            sql += " inner join tb_card_orgframework co on co.code = ct.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='" + userid + "'";
        }
        sql += "where recordtime between '" + start + "' and '" + end + "'";
        sql = getString(infoIds, sql, orgcode, viewchild);
        List<ConsumeDtl> consumeDtls = dbService.queryList(sql, ConsumeDtl.class);
        return sortByInfo(consumeDtls);
    }


    private LinkedHashMap<String, List<StatDtl>> getDailyConsumeDtlSleep(HttpServletRequest request, String start, String end, String infoIds, String orgcode, boolean viewchild) {
        String sql = "select cc.infoid, cp.cardid,cc.cardno, cp.recordtime, money, paywallet,ct.name,ct.code,getorgname(ct.orgcode) as departName,cc.balance,cc.vicewallet,cp.status " +
                " from tb_consume_payrecords_sleep cp left join tb_card_teachstudinfo ct on ct.uid = cp.infoid " +
                " left join tb_card_cardinfo cc on cc.infoid = ct.uid and cc.status != 0 ";
        SysUser user = userRedis.get(request);
        if (user.getUsertype() == 1) {
            String userid = user.getUid();
            sql += " inner join tb_card_orgframework co on co.code = ct.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='" + userid + "'";
        }
        sql += "where recordtime between '" + start + "' and '" + end + "'";
        sql = getString(infoIds, sql, orgcode, viewchild);
        List<ConsumeDtl> consumeDtls = dbService.queryList(sql, ConsumeDtl.class);
        return sortByInfo(consumeDtls);
    }

    private LinkedHashMap<String, List<StatDtl>> getTransactionDtl(HttpServletRequest request, String start, String end, String infoIds, String orgcode, boolean viewchild) {
        String sql = "select money,cc.infoid,td.des,cc.cardno,tradetype,td.createdate,ct.name,ct.code,td.dynamics_type,getorgname(ct.orgcode) as departName,cc.balance,cc.vicewallet,td.status " +
                "from tb_card_transaction_detail td left join tb_card_teachstudinfo ct on ct.uid = td.infoid " +
                "left join tb_card_cardinfo cc on cc.infoid = ct.uid and cc.status != 0 ";
        SysUser user = userRedis.get(request);
        if (user.getUsertype() == 1) {
            String userid = user.getUid();
            sql += " inner join tb_card_orgframework co on co.code = ct.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='" + userid + "'";
        }
        sql += "where tradedate between '" + start + "' and '" + end + "'";
        sql = getString(infoIds, sql, orgcode, viewchild);
        List<TransactionDetail> consumeDtls = dbService.queryList(sql, TransactionDetail.class);
        return sortByInfo(consumeDtls);
    }

    private LinkedHashMap<String, List<StatDtl>> getTransactionDtlSleep(HttpServletRequest request, String start, String end, String infoIds, String orgcode, boolean viewchild) {
        String sql = "select money,cc.cardno,td.des,cc.infoid,tradetype,td.createdate,ct.name,ct.code,td.dynamics_type,getorgname(ct.orgcode) as departName,cc.balance,cc.vicewallet,td.status " +
                " from tb_card_transaction_detail_sleep td left join tb_card_teachstudinfo ct on ct.uid = td.infoid " +
                " left join tb_card_cardinfo cc on cc.infoid = ct.uid and cc.status != 0 ";
        SysUser user = userRedis.get(request);
        if (user.getUsertype() == 1) {
            String userid = user.getUid();
            sql += " inner join tb_card_orgframework co on co.code = ct.orgcode inner join tb_card_orgframework_user cou on cou.orgcode = co.code and cou.userid='" + userid + "'";
        }
        sql += "where tradedate between '" + start + "' and '" + end + "'";
        sql = getString(infoIds, sql, orgcode, viewchild);
        List<TransactionDetail> consumeDtls = dbService.queryList(sql, TransactionDetail.class);
        return sortByInfo(consumeDtls);
    }


    private String getString(String infoIds, String sql, String orgcode, boolean viewchild) {
        if (StringUtils.isNotBlank(orgcode)) {
            if (viewchild) {
                sql += " and ct.orgcode like '" + orgcode + "%' ";
            } else {
                sql += " and ct.orgcode='" + orgcode + "' ";
            }
            return sql;
        }
        if (StringUtils.isNotBlank(infoIds)) {
            StringJoiner joiner = new StringJoiner(",", "('", "')");
            for (String s : infoIds.split(",")) {
                joiner.add(s);
            }
            sql += " and cc.infoid in " + joiner;
        }
        return sql;
    }
    /**
     * 将日期时分秒敲掉
     */
    private void pureRecordDay(List<? extends StatDtl> statDtls) {
        Calendar calendar = Calendar.getInstance();
        for (StatDtl statDtl : statDtls) {
            Date recordTime = statDtl.getRecordtime();
            calendar.setTime(recordTime);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            statDtl.setRecordtime(calendar.getTime());
        }
    }

    private LinkedHashMap<Date, List<StatDtl>> fill(LinkedHashMap<Date, List<StatDtl>> map, String start) {
        ArrayList<Date> dates = new ArrayList<>(map.keySet());
        dates.sort(Comparator.comparing(Date::getTime));
        ArrayDeque<Date> que = new ArrayDeque<>();
        Calendar calendar = Calendar.getInstance();

        for (Date date : dates) {
            Date peek = que.peek();
            if (peek == null) {
                Date startDate = DateUtil.parse(start, "yyyy-MM-dd");
                calendar.setTime(startDate);
                // 相差天数
                long diff = DateUtil.betweenDay(startDate, date, false);
                que.push(startDate);
                for (long l = 0; l < diff; l++) {
                    // 插入空日期
                    calendar.add(Calendar.DATE, 1);
                    que.push(calendar.getTime());
                }
                continue;
            }
            calendar.setTime(peek);
            // 相差天数
            long diff = DateUtil.betweenDay(peek, date, false);
            for (long l = 0; l < diff; l++) {
                // 插入空日期
                calendar.add(Calendar.DATE, 1);
                que.push(calendar.getTime());
            }
        }
        LinkedHashMap<Date, List<StatDtl>> res = new LinkedHashMap<>();
        for (Date date : que) {
            List<StatDtl> statDtls = map.get(date);
            if (statDtls == null) {
                ArrayList<Object> objects = new ArrayList<>();
                res.put(date, new ArrayList<>());
                continue;
            }
            res.put(date, statDtls);
        }
        return res;
    }

    /**
     * 补没有数据的日期
     */
    @SneakyThrows
    private <T extends StatDtl> List<T> fillNullDate(List<? extends StatDtl> statDtls, String start) {
        statDtls.sort(Comparator.comparing(StatDtl::getRecordtime));
        ArrayDeque<T> que = new ArrayDeque<>();

        for (StatDtl statDtl : statDtls) {
            T peek = que.peek();
            if (peek == null) {
                // 补全选定日期到数据实际日期间的日期数据
                Date startDate = DateUtil.parse(start, "yyyy-MM-dd");
                fillData(que, (T) statDtl, startDate);
                que.push((T) statDtl);
                continue;
            }
            fillData(que, (T) statDtl, peek.getRecordtime());
            que.push((T) statDtl);
        }
        List<T> res = new ArrayList<>(que.size());
        res.addAll(que);
        return res;
    }

    private <T extends StatDtl> void fillData(ArrayDeque<T> que, T statDtl, Date startDate) throws Exception {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        // 相差天数
        long diff = DateUtil.betweenDay(startDate, statDtl.getRecordtime(), false);
        for (long l = 0; l < diff; l++) {
            // 插入空日期
            T t = getFillData(statDtl, calendar.getTime());
            calendar.add(Calendar.DATE, 1);
            que.push(t);
        }
    }

    private <T extends StatDtl> T getFillData(T statDtl, Date date) throws Exception {
        T t = (T) statDtl.getClass().newInstance();
        t.setRecordtime(date);
        return t;
    }

//    private LinkedHashMap<Date, List<StatDtl>> sortByTime(List<? extends StatDtl> consumeDtls) {
//        Calendar calendar = Calendar.getInstance();
//        return consumeDtls.stream()
//                .peek(i -> {
//                    Date recordTime = i.getRecordtime();
//                    calendar.setTime(recordTime);
//                    calendar.set(Calendar.HOUR_OF_DAY, 0);
//                    calendar.set(Calendar.MINUTE, 0);
//                    calendar.set(Calendar.SECOND, 0);
//                    i.setRecordtime(calendar.getTime());
//                })
//                .sorted(Comparator.comparing(StatDtl::getRecordtime))
//                .collect(Collectors
//                        .groupingBy(StatDtl::getRecordtime,
//                                LinkedHashMap::new,
//                                Collectors.mapping(i -> i, Collectors.toList())));
//    }


    private LinkedHashMap<String, List<StatDtl>> sortByInfo(List<? extends StatDtl> consumeDtls) {
        return consumeDtls.stream()
                .filter(statDtl -> statDtl.getInfoId() != null) // 过滤掉getInfoId为null的元素
                .sorted(Comparator.comparing(StatDtl::getRecordtime))
                .collect(Collectors.groupingBy(
                        StatDtl::getInfoId,
                        LinkedHashMap::new,
                        Collectors.mapping(i -> i, Collectors.toList())
                ));
    }


    /**
     * 冷热表数据合并
     */
    private LinkedHashMap<String, List<StatDtl>> mergeConsumeDtl(Map<String, List<StatDtl>> map,
                                                                 Map<String, List<StatDtl>> sleepMap) {
        LinkedHashMap<String, List<StatDtl>> mergedMap = new LinkedHashMap<>((int) Math.ceil(map.size() * 1.5));
        mergedMap.putAll(sleepMap);
        map.forEach((str, consumeDtls) -> mergedMap.computeIfAbsent(str, key -> new ArrayList<>()).addAll(consumeDtls));
        return mergedMap;
    }

    public R<Null> ExportData(HttpServletRequest request, String start, String end, String infoid, String orgcode, boolean viewchild) {
        List<DailyTransactionStatDto> personRate = getStatResult(request, start, end, infoid, orgcode, viewchild);
        if (personRate.size() == 0) {
            return R.fail("无导出数据");
        }
        List<DailyTransactionStatRow> rows = personRate.stream()
                .map(dto ->
                        new DailyTransactionStatRow(
                                dto.getTime(),
                                dto.getInfoId(),
                                dto.getInfoName(),
                                dto.getCode(),
                                dto.getDepartName(),
                                dto.getChargeSum(),
                                dto.getPerformanceRewardSum(),
                                dto.getCompanyAllowanceSum(),
                                dto.getConsumeMainWalletSum(),
                                dto.getConsumeSubWalletSum(),
                                dto.getConsumeSum(),
                                dto.getRefundSum(),
                                dto.getRefundChargeSum(),
                                dto.getSum()
                        )).collect(Collectors.toList());
        LinkedHashMap<Integer, DailyTransactionStatRow> map = new LinkedHashMap<>();
        Sheet<DailyTransactionStatRow> sheet = new Sheet<>(map, DailyTransactionStatRow.class);
        int row = 1;
        for (DailyTransactionStatRow vo : rows) {
            map.put(row, vo);
            row++;
        }
        String format = DateHelper.format(new Date(), "yyyyMMddHHmmss");
        String extFilePath = "temp/收支汇总表" + format + ".xlsx";
        String filePath = WebConfig.getUploaddir() + extFilePath;
        com.ymiots.framework.utils.excel.ExcelUtil.write(sheet, filePath, "收支汇总表", 11,start,end);

        return R.ok(extFilePath);
    }


}
