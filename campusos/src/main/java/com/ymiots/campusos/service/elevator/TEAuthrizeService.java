package com.ymiots.campusos.service.elevator;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.common.WebConfig;
import com.ymiots.campusos.entity.SysUser;
import com.ymiots.campusos.entity.card.TeachStudInfo;
import com.ymiots.campusos.redis.RedisMessagePublish;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.datafactory.CallbackTransaction;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class TEAuthrizeService extends BaseService {

    @Autowired
    UserRedis userredis;

    @Autowired
    RedisMessagePublish redismsg;

    @Autowired
    SysLogsService syslogs;

    public JsonData getEAuthrizeList(HttpServletRequest request, HttpServletResponse response, String key, String status, String devid, String isvisitor, String infoid, int start, int limit) {
        String fields = "ea.permissions,ea.uid, ea.infoid, ea.cardno, ea.infoname, ea.infoindex, ea.isvisitor, ea.passpassword, ea.idcard, ea.devid, ea.machineid, ea.weekuid, ea.storeys, date_format(ea.starttime, '%Y-%m-%d %H:%i:%s') as starttime,date_format(ea.endtime, '%Y-%m-%d %H:%i:%s') as endtime, ea.downmsg, ea.downnum, ea.status,d.name as statusname ";
        String table = "tb_elevator_t_authrize ea LEFT JOIN tb_sys_dictionary d on d.code=ea.status and d.groupcode='SYS0000025' ";
        String where = "1=1 ";
        if (StringUtils.isNotBlank(devid)) {
            where += "AND ea.devid='" + devid + "' ";
        }
        if (StringUtils.isNotBlank(status) && Integer.parseInt(status) >= -1) {
            where += "AND ea.status=" + status;
        }
        if (StringUtils.isNotBlank(isvisitor) && Integer.parseInt(isvisitor) >= 0) {
            where += " AND ea.isvisitor=" + isvisitor;
        }
        if (StringUtils.isNotBlank(infoid)) {
            where += " AND ea.infoid='" + infoid + "'";
        }
        if (StringUtils.isNotBlank(key)) {
            where += "AND (ea.infoname like '%" + key + "%' or ea.cardno='" + ComHelper.LeftPad(key, 12, '0') + "') ";
        }
        String orderby = ExtSort.Orderby(request, "createdate DESC");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData getEDev(HttpServletRequest request, HttpServletResponse response, String key, int start, int limit) {
        String fields = "ctrl.uid,ctrl.devname,ctrl.devsn,ctrl.machineid,ctrl.server_uid,ctrl.devstatus,ctrl.areacode,getareaname(ctrl.areacode) as areaname";
        String table = "tb_dev_accesscontroller ctrl ";
        String userid = userredis.getUserId(request);
        //如果为普通管理员，需要受二级授权控制
        if (userredis.get(request).getUsertype() == 1) {
            table += " inner join tb_dev_areaframework_user ua on ua.areacode=ctrl.areacode and ua.userid='" + userid + "' ";
        }
        String where = " ctrl.status=1 and ctrl.devclass='12' and ctrl.devmodel = 49";
        if (!StringUtils.isBlank(key)) {
            where += " and  (ctrl.devname like '%" + key + "%' or ctrl.machineid='" + key + "')  ";
        }
        String orderby = ExtSort.Orderby(request, " ctrl.uid desc ");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData UploadFile(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws Exception {
        MultipartFile[] files = new MultipartFile[]{file};
        List<String> filelist = FileHelper.SaveMultipartFile(files, WebConfig.getUploaddir(), "excel");
        if (filelist.size() == 0) {
            return Json.getJsonData(false, "文件保存失败");
        }
        String filepath = filelist.get(0);
        InputStream filestream = file.getInputStream();
        JSONArray firstrow = ExcelUtil.getFirstRow(filestream, filepath);
        return Json.getJsonData(true, filepath, firstrow);
    }

    public JsonResult ImportInfoFile(HttpServletRequest request, HttpServletResponse response, String excelpath, String columncfg) throws Exception {
        String filepath = String.format("%s%s", WebConfig.getUploaddir(), excelpath);
        File file = new File(filepath);
        if (!file.exists()) {
            return Json.getJsonResult("文档不存在");
        }
        InputStream stream = new FileInputStream(file);
        JSONArray exceltablerows = ExcelUtil.getBankListByExcel(stream, excelpath);
        if (exceltablerows.size() == 0) {
            return Json.getJsonResult("文档为空文件");
        }
        JSONArray columncfgarr = JSONArray.parseArray(columncfg);

        JSONArray clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("name");
            }
        });

        if (clist.size() == 0) {
            return Json.getJsonResult("请选择姓名对应列");
        }
        clist = CollectionUtils.where(columncfgarr, new CallBackFilter<JSONObject>() {
            @Override
            public Boolean filter(JSONObject model) {
                // TODO Auto-generated method stub
                return model.getString("columncode").equals("code");
            }
        });
        if (clist.size() == 0) {
            return Json.getJsonResult("请选择工号对应列");
        }

        JSONArray allowlist = new JSONArray();
        for (int t = 0; t < exceltablerows.size(); t++) {
            JSONObject row = exceltablerows.getJSONObject(t);
            boolean allowImport = true;
            JSONObject datarow = new JSONObject();
            tag1:
            for (int r = 0; r < columncfgarr.size(); r++) {
                JSONObject c = columncfgarr.getJSONObject(r);
                String columnvalue = row.getString(c.getString("column"));
                String columncode = c.getString("columncode");
                if (StringUtils.isBlank(columnvalue)) {
                    columnvalue = "";
                }
                datarow.put(columncode, columnvalue);
            }
            allowlist.add(datarow);
        }

        List<String> codes = new ArrayList<>();
        for (int t = 0; t < allowlist.size(); t++) {
            JSONObject row = allowlist.getJSONObject(t);
            for (int r = 0; r < columncfgarr.size(); r++) {
                JSONObject c = columncfgarr.getJSONObject(r);
                String columncode = c.getString("columncode");
                String columnvalue = row.getString(columncode);
                if ("code".equals(columncode)){
                    codes.add(columnvalue);
                }
            }
        }
        String joinCode = "";
        if (codes.size()>0){
            joinCode+="(";
            for (String code : codes) {
                joinCode+="'"+code+"',";
            }
            joinCode = joinCode.substring(0,joinCode.length()-1) + ")";
        }


        return Json.getJsonResult(true,joinCode);
    }


    public JsonData getInfoList(HttpServletRequest request, HttpServletResponse response, String orgcode, String key, String infotype, boolean viewchild, String intoyear, String selecteduid, String isvisitor,String codes, int start, int limit) {
        JsonData result = new JsonData();
        if (StringUtils.isNotBlank(isvisitor) && "0".equals(isvisitor)) {
            String fields = "b.uid,b.code,b.name,b.sex,b.card,b.cardsn,b.passpassword,b.orgcode,b.infotype,b.intoyear,b.focus,b.status, getorgname(b.orgcode) as orgname,b.infoindex ";
            String table = "tb_card_teachstudinfo b";
            if (userredis.get(request).getUsertype() == 1) {
                String userid = userredis.getUserId(request);
                table += " inner join tb_card_orgframework_user uo on uo.orgcode=b.orgcode and uo.userid='" + userid + "' ";
            }
            String where = " b.status=1 ";
            if (!infotype.equals("0") && StringUtils.isNotBlank(infotype)) {
                where += " and b.infotype=" + infotype;
            }
            if (StringUtils.isNotEmpty(codes)){
                where += " and b.code in "+codes;
            }
            if (!intoyear.equals("0")) {
                where += " and b.intoyear=" + intoyear;
            }
            if (!StringUtils.isBlank(orgcode)) {
                if (viewchild) {
                    where += " and b.orgcode like '" + orgcode + "%' ";
                } else {
                    where += " and b.orgcode='" + orgcode + "' ";
                }
            }
            if (!StringUtils.isBlank(key)) {
                where += " and (b.code='" + key + "' or b.name like '%" + key + "%' or b.mobile like '%" + key + "%' or b.card='" + ComHelper.LeftPad(key, 12, '0') + "' or b.cardsn='" + ComHelper.LeftPad(key, 12, '0') + "' )";
            }
            JSONArray Selected = JSONArray.parseArray(selecteduid);
            if (Selected.size() > 0) {
                String[] uidx = ComHelper.ListToArray(Selected);
                where += " and b.uid not in('" + String.join("','", uidx) + "')";
            }
            String orderby = ExtSort.Orderby(request, "b.createdate desc");
            result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        } else if (StringUtils.isNotBlank(isvisitor) && "1".equals(isvisitor)) {
            String fields = "vv.uid, vv.name, vv.cardsn, vv.idcard, vv.sex, vv.visitorindex as infoindex,vv.mobile ";
            String table = "tb_visitor_visitorinfo vv";
            String where = "1=1 AND not exists (SELECT infoid from tb_elevator_authrize where infoid=vv.uid)";
            JSONArray Selected = JSONArray.parseArray(selecteduid);
            if (Selected.size() > 0) {
                String[] uidx = ComHelper.ListToArray(Selected);
                where += " and uid not in('" + String.join("','", uidx) + "')";
            }
            if (!StringUtils.isBlank(key)) {
                where += " and (vv.name like '%" + key + "%' or vv.mobile like '%" + key + "%' or vv.cardsn like '%" + key + "%' )";
            }
            String orderby = ExtSort.Orderby(request, "createdate desc");
            result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        }
        return result;
    }

    public JsonData getDoorList(HttpServletRequest request, HttpServletResponse response, String areacode,
                                boolean viewchild, String key, int start, int limit) {
        String fields = "ctrl.uid,ctrl.devname,ctrl.devsn,ctrl.machineid,ctrl.server_uid,ctrl.devstatus,ctrl.areacode,getareaname(ctrl.areacode) as areaname";
        String table = "tb_dev_accesscontroller ctrl ";
        String userid = userredis.getUserId(request);
        //如果为普通管理员，需要受二级授权控制
        if (userredis.get(request).getUsertype() == 1) {
            table += " inner join tb_dev_areaframework_user ua on ua.areacode=ctrl.areacode and ua.userid='" + userid + "' ";
        }

        String where = " ctrl.status=1 and ctrl.devclass=12 and ctrl.devmodel=49 ";

        if (!StringUtils.isBlank(areacode)) {
            if (viewchild) {
                where += " and ctrl.areacode like '" + areacode + "%' ";
            } else {
                where += " and ctrl.areacode='" + areacode + "' ";
            }
        }

        if (!StringUtils.isBlank(key)) {
            where += " and  (ctrl.devname like '%" + key + "%' or ctrl.machineid='" + key + "')  ";
        }
        String orderby = ExtSort.Orderby(request, " ctrl.uid desc ");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }


    public JsonData getWeekPlanList(HttpServletRequest request, HttpServletResponse response, String devid, int start, int limit) {
        String fields = "tw.uid,tw.name ";
        String table = "tb_elevator_t_weekplan tw " +
                " join tb_elevator_t_timescheme ett on tw.uid=ett.weekplanid ";
        String where = "ett.devid='" + devid + "' ";
        String orderby = ExtSort.Orderby(request, "weekindex ASC");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData getStoreyList(HttpServletRequest request, HttpServletResponse response, String devid, String key,String storeylevel, int start, int limit) {
        String fields = "uid,code,name";
        String table = "tb_elevator_devicestorey";
        String where = "devid='" + devid + "'";
        if (!StringUtils.isBlank(key)) {
            where += " and (code='" + key + "' or name like '%" + key + "%') ";
        }
        if(StringUtils.isNotBlank(storeylevel)){
            where += " AND code IN ('"+String.join("','",storeylevel.split(","))+"') ";
        }
        String orderby = ExtSort.Orderby(request, "code asc");
        JsonData result = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return result;
    }

    public JsonData getAuthrizeTimeList(HttpServletRequest request, HttpServletResponse response, String authrizeuid) {
    	 if(StringUtils.isBlank(authrizeuid)) {
         	return Json.getJsonData(true);
         }
    	String sql = "SELECT a.mon,a.tues,a.wed,a.thur,a.fri,a.sat,a.sun,b.endtime from tb_elevator_t_weekplan a join tb_elevator_t_authrize b on a.uid = b.weekuid WHERE b.uid=?";
        JSONArray week = dbService.QueryList(sql, authrizeuid);
        if(week.size()==0) {
        	return Json.getJsonData(true);
        }
        JSONObject weekzone=week.getJSONObject(0);
        String mon = weekzone.getString("mon");
        String tues = weekzone.getString("tues");
        String wed = weekzone.getString("wed");
        String thur = weekzone.getString("thur");
        String fri = weekzone.getString("fri");
        String sat = weekzone.getString("sat");
        String sun = weekzone.getString("sun");
        String endtime = weekzone.getString("endtime");
        endtime =endtime.replace("T"," ");
        String monweektimesql = "SELECT '周一' as weekday,CONCAT(start1,'~',end1) as 'time1' ,CONCAT(start2,'~',end2) as 'time2' ,CONCAT(start3,'~',end3) as 'time3' ,CONCAT(start4,'~',end4) as 'time4' ,CONCAT(start5,'~',end5) as 'time5' ,CONCAT(start6,'~',end6) as 'time6','"+endtime+"' as endtime  from tb_elevator_t_time where uid = '"+mon+"'";
        String tuesweektimesql = "SELECT '周二' as weekday,CONCAT(start1,'~',end1) as 'time1' ,CONCAT(start2,'~',end2) as 'time2' ,CONCAT(start3,'~',end3) as 'time3' ,CONCAT(start4,'~',end4) as 'time4' ,CONCAT(start5,'~',end5) as 'time5' ,CONCAT(start6,'~',end6) as 'time6','"+endtime+"' as endtime  from tb_elevator_t_time where uid = '"+tues+"'";
        String wedweektimesql = "SELECT '周三' as weekday,CONCAT(start1,'~',end1) as 'time1' ,CONCAT(start2,'~',end2) as 'time2' ,CONCAT(start3,'~',end3) as 'time3' ,CONCAT(start4,'~',end4) as 'time4' ,CONCAT(start5,'~',end5) as 'time5' ,CONCAT(start6,'~',end6) as 'time6','"+endtime+"' as endtime  from tb_elevator_t_time where uid = '"+wed+"'";
        String thurweektimesql = "SELECT '周四' as weekday,CONCAT(start1,'~',end1) as 'time1' ,CONCAT(start2,'~',end2) as 'time2' ,CONCAT(start3,'~',end3) as 'time3' ,CONCAT(start4,'~',end4) as 'time4' ,CONCAT(start5,'~',end5) as 'time5' ,CONCAT(start6,'~',end6) as 'time6','"+endtime+"' as endtime  from tb_elevator_t_time where uid = '"+thur+"'";
        String friweektimesql = "SELECT '周五' as weekday,CONCAT(start1,'~',end1) as 'time1' ,CONCAT(start2,'~',end2) as 'time2' ,CONCAT(start3,'~',end3) as 'time3' ,CONCAT(start4,'~',end4) as 'time4' ,CONCAT(start5,'~',end5) as 'time5' ,CONCAT(start6,'~',end6) as 'time6','"+endtime+"' as endtime  from tb_elevator_t_time where uid = '"+fri+"'";
        String satweektimesql = "SELECT '周六' as weekday,CONCAT(start1,'~',end1) as 'time1' ,CONCAT(start2,'~',end2) as 'time2' ,CONCAT(start3,'~',end3) as 'time3' ,CONCAT(start4,'~',end4) as 'time4' ,CONCAT(start5,'~',end5) as 'time5' ,CONCAT(start6,'~',end6) as 'time6','"+endtime+"' as endtime  from tb_elevator_t_time where uid = '"+sat+"'";
        String sunweektimesql = "SELECT '周日' as weekday,CONCAT(start1,'~',end1) as 'time1' ,CONCAT(start2,'~',end2) as 'time2' ,CONCAT(start3,'~',end3) as 'time3' ,CONCAT(start4,'~',end4) as 'time4' ,CONCAT(start5,'~',end5) as 'time5' ,CONCAT(start6,'~',end6) as 'time6','"+endtime+"' as endtime  from tb_elevator_t_time where uid = '"+sun+"'";
        JSONArray array = new JSONArray();
        array.add(dbService.QueryJSONObject(monweektimesql));
        array.add(dbService.QueryJSONObject(tuesweektimesql));
        array.add( dbService.QueryJSONObject(wedweektimesql));
        array.add(dbService.QueryJSONObject(thurweektimesql));
        array.add(dbService.QueryJSONObject(friweektimesql));
        array.add(dbService.QueryJSONObject(satweektimesql));
        array.add(dbService.QueryJSONObject(sunweektimesql));
        return Json.getJsonData(true,array);
    }

    public JsonResult delEAuthrize(HttpServletRequest request, HttpServletResponse response, String uid, String infoid) {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "uid为空");
        }
        String updateSQL = "UPDATE tb_elevator_t_authrize SET status=3,downnum=0,downmsg='' WHERE uid IN ('" + String.join("','", uid.split(",")) + "')  AND status in (1,3,4,5,6) ";
        dbService.excuteSql(updateSQL);
        String delSQL = "DELETE FROM tb_elevator_t_authrize WHERE uid IN ('" + String.join("','", uid.split(",")) + "')  AND status in (-1,0,2)";
        dbService.excuteSql(delSQL);

        String[] split = infoid.split(",");
        Set<String> delInfoid = new HashSet<String>(Arrays.asList(split));
        Set<String> infoidlist = new HashSet<String>();
        JSONArray ja = dbService.QueryList("SELECT infoid FROM tb_elevator_t_authrize WHERE LOCATE(infoid,'" + String.join(",", delInfoid) + "')");
        for (int i = 0; i < ja.size(); i++) {
            JSONObject jo = ja.getJSONObject(i);
            infoidlist.add(jo.getString("infoid"));
        }
        delInfoid.removeAll(infoidlist);

        syslogs.Write(request, "梯控授权", "删除授权");
        return Json.getJsonResult(true);
    }

    public JsonResult download(HttpServletRequest request, HttpServletResponse response, String uid) {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "uid为空");
        }
        String updateSQL = "UPDATE tb_elevator_t_authrize SET status=0,downnum=0,downmsg='' WHERE uid IN ('" + String.join("','", uid.split(",")) + "') AND status = -1";
        dbService.excuteSql(updateSQL);
        syslogs.Write(request, "梯控授权", String.format("下载名单:名单uid:%s", uid));
        return Json.getJsonResult(true);
    }

    public JsonResult redownload(HttpServletRequest request, HttpServletResponse response, String uid) {
        if (StringUtils.isBlank(uid)) {
            return Json.getJsonResult(false, "uid为空");
        }
        String updateSQL = "UPDATE tb_elevator_t_authrize SET status=5,downnum=0,downmsg='' WHERE uid IN ('" + String.join("','", uid.split(",")) + "') AND status in (1,3,4,5,6)";
        dbService.excuteSql(updateSQL);
        String updateSQL2 = "UPDATE tb_elevator_t_authrize SET status=0,downnum=0,downmsg='' WHERE uid IN ('" + String.join("','", uid.split(",")) + "') AND status = 2";
        dbService.excuteSql(updateSQL2);
        syslogs.Write(request, "梯控授权", String.format("重新下载名单:名单uid:%s", uid));
        return Json.getJsonResult(true);
    }

//    public JsonResult saveEAuthrize(final HttpServletRequest request, HttpServletResponse response, final String infoid, final String devdata, final String starttime, final String endtime, final String storeys,final String status) {
//        final String userId = userredis.getUserId(request);
//        final String[] authrizeInfoid = infoid.split(",");
//        try {
//
//            dbService.excuteTransaction(new CallbackTransaction() {
//                @Override
//                public void execute(Connection connection) throws SQLException {
//                    //判断获取已关联的人员id
//                    String infocount = "SELECT uid FROM tb_elevator_infoindex WHERE uid in ('" + String.join("','", authrizeInfoid) + "')";
//                    JSONArray infoja = dbService.QueryList(infocount);
//                    ArrayList<String> infoary = new ArrayList<String>();
//                    for (int i = 0; i < infoja.size(); i++) {
//                        JSONObject infojo = infoja.getJSONObject(i);
//                        infoary.add(infojo.getString("uid"));
//                    }
//                    //获取未关联的人员id
//                    ArrayList<String> relationInfoid = new ArrayList<String>(Arrays.asList(authrizeInfoid));
//                    relationInfoid.removeAll(infoary);
//                    //获取序号
//                    int length = relationInfoid.size();
//                    if (length != 0) {
//                        String getindex = "SELECT yi.uid,ei.uid as infoid FROM tb_alleyway_yunoffline_index yi  LEFT JOIN tb_elevator_infoindex ei ON yi.uid = ei.infoindex WHERE ei.uid = '' or ei.uid is null ORDER BY yi.uid ASC limit 0," + length;
//                        JSONArray indexja = dbService.QueryList(getindex);
//                        //插入tb_alleyway_yunoffline_infoindex关系表
//                        StringBuilder insertIndex = new StringBuilder();
//                        insertIndex.append("INSERT INTO tb_elevator_infoindex(uid,infoindex) VALUES");
//                        for (int j = 0; j < indexja.size(); j++) {
//                            JSONObject indexjo = indexja.getJSONObject(j);
//                            String infoindex = indexjo.getString("uid");
//                            insertIndex.append("('" + relationInfoid.get(j) + "','" + infoindex + "'),");
//                        }
//                        insertIndex.deleteCharAt(insertIndex.length() - 1);
//                        PreparedStatement indexps = connection.prepareStatement(insertIndex.toString());
//                        indexps.executeUpdate();
//						indexps.close();
//                    }
//                    //授权
//                    JSONArray devdataja = JSONArray.parseArray(devdata);
//                    for(int i=0;i<devdataja.size();i++) {
//                    	ArrayList<String> insertInfoid = new ArrayList<String>();
//                        JSONObject devdatajo = devdataja.getJSONObject(i);
//                        String devid = devdatajo.getString("devid");
//                        String timezones = devdatajo.getString("timezones");
//                        for (String oneinfoid : authrizeInfoid) {
//                            JSONArray authrizeCount = dbService.QueryList("SELECT status FROM tb_elevator_authrize WHERE infoid='" + oneinfoid + "' AND devid='" + devid + "'");
//                            if (authrizeCount.size() > 0) {
//                                if(StringUtils.isNotBlank(status) && !"-1".equals(status)){
//                                    String updateSQL = "UPDATE tb_elevator_authrize SET timezones=?,starttime=?,endtime=?,storeys=?,status=5,downnum=0,creatorid=? WHERE status IN (1,3,4,5,6) AND infoid=? AND devid=?";
//                                    PreparedStatement ps = connection.prepareStatement(updateSQL);
//                                    ps.setString(1, timezones);
//                                    ps.setString(2, starttime);
//                                    ps.setString(3, endtime);
//                                    ps.setString(4, storeys);
//                                    ps.setString(5, userId);
//                                    ps.setString(6, oneinfoid);
//                                    ps.setString(7, devid);
//                                    ps.executeUpdate();
//									ps.close();
//
//                                    String updateSQL2 = "UPDATE tb_elevator_authrize SET timezones=?,starttime=?,endtime=?,storeys=?,status=0,downnum=0,creatorid=? WHERE status IN (0,2) AND infoid=? AND devid=?";
//                                    ps = connection.prepareStatement(updateSQL2);
//                                    ps.setString(1, timezones);
//                                    ps.setString(2, starttime);
//                                    ps.setString(3, endtime);
//                                    ps.setString(4, storeys);
//                                    ps.setString(5, userId);
//                                    ps.setString(6, oneinfoid);
//                                    ps.setString(7, devid);
//                                    ps.executeUpdate();
//									ps.close();
//
//                                }else if(StringUtils.isNotBlank(status) && "-1".equals(status)){
//                                    String updateSQL3 = "UPDATE tb_elevator_authrize SET timezones=?,starttime=?,endtime=?,storeys=?,status=-1,downnum=0,creatorid=? WHERE status = -1 AND infoid=? AND devid=?";
//                                    PreparedStatement ps = connection.prepareStatement(updateSQL3);
//                                    ps.setString(1, timezones);
//                                    ps.setString(2, starttime);
//                                    ps.setString(3, endtime);
//                                    ps.setString(4, storeys);
//                                    ps.setString(5, userId);
//                                    ps.setString(6, oneinfoid);
//                                    ps.setString(7, devid);
//                                    ps.executeUpdate();
//									ps.close();
//                                }
//                            } else {
//                                insertInfoid.add(oneinfoid);
//                            }
//                        }
//                        String insertSQL = "INSERT INTO tb_elevator_authrize (uid, infoid, cardno, infoname, isvisitor, idcard, passpassword, qrcode, devid, machineid, serveruid, timezones, storeys, starttime, endtime, downnum, createdate, creatorid, status) " +
//                                " SELECT uuid(),infodata.infoid, infodata.cardno, infodata.name, infodata.isvisitor, infodata.idcard, infodata.passpassword , infodata.qrcode, ctrl.uid, ctrl.machineid, ctrl.server_uid,'" + timezones + "', '"+storeys+"','" + starttime + "', '" + endtime + "','0', now(), '" + userId + "', -1" +
//                                " FROM ((SELECT ct.uid as infoid, ct.card as cardno, ct.name, ct.idcard, ct.passpassword, '0' as isvisitor, cq.qrcode FROM tb_card_teachstudinfo ct LEFT JOIN tb_card_qrcode cq ON ct.uid = cq.infoid) " +
//                                "UNION ALL " +
//                                "(SELECT uid as infoid, cardsn as cardno,  name, idcard, null as passpassword,'1' as isvisitor,null as qrcode FROM tb_visitor_visitorinfo )) infodata " +
//                                "JOIN tb_dev_accesscontroller ctrl ON ctrl.devclass='12'" +
//                                " WHERE infodata.infoid IN ('" + String.join("','", insertInfoid) + "') AND ctrl.uid='" + devid + "'";
//                        PreparedStatement insert = connection.prepareStatement(insertSQL);
//                        insert.executeUpdate();
//						insert.close();
//
//                        String updateIndex = "UPDATE tb_elevator_authrize ea INNER JOIN tb_elevator_infoindex ei ON  ea.infoid = ei.uid SET ea.infoindex = ei.infoindex WHERE ea.infoindex IS NULL OR ea.infoindex <>''";
//                        PreparedStatement update = connection.prepareStatement(updateIndex);
//                        update.executeUpdate();
//						update.close();
//
//                        syslogs.Write(request, "梯控授权", String.format("授权名单:人员:%s,设备%s,插入关联%s,", infoid, devdata, relationInfoid));
//
//                    }
//                }
//            });
//            return Json.getJsonResult(true);
//        } catch (Exception e) {
//            return Json.getJsonResult(false, e.getMessage());
//        }
//    }

    public JsonResult saveEAuthrize(final HttpServletRequest request, HttpServletResponse response, final String infoid, final String devdata, final String starttime, final String endtime, final String storeys,final String status,final String permissions) {
        String userId = userredis.getUserId(request);
        String[] authrizeInfoId = infoid.split(",");
        try {
            dbService.excuteTransaction(new CallbackTransaction() {
                @Override
                public void execute(Connection connection) throws SQLException {
                    //授权
                    JSONArray devdataja = JSONArray.parseArray(devdata);
                    for(int i=0;i<devdataja.size();i++) {
                        //为不同的设备进行索引index
                        List<String> insertInfoId = new ArrayList<>();
                        JSONObject devDataJo = devdataja.getJSONObject(i);
                        String devId = devDataJo.getString("devid");
                        String weekUid = devDataJo.getString("weekUid");
                        for (String oneinfoId : authrizeInfoId) {
                            JSONArray authrizeCount = dbService.QueryList("SELECT status FROM tb_elevator_t_authrize WHERE infoid='" + oneinfoId + "' AND devid='" + devId + "'");
                            if (authrizeCount.size() > 0) {
                                if(StringUtils.isNotBlank(status) && !"-1".equals(status)){
                                    String updateSQL = "UPDATE tb_elevator_t_authrize SET weekuid=?,starttime=?,endtime=?,storeys=?,status=5,downnum=0,creatorid=?,permissions=? WHERE status IN (1,3,4,5,6) AND infoid=? AND devid=?";
                                    PreparedStatement ps = connection.prepareStatement(updateSQL);
                                    ps.setString(1, weekUid);
                                    ps.setString(2, starttime);
                                    ps.setString(3, endtime);
                                    ps.setString(4, storeys);
                                    ps.setString(5, userId);
                                    ps.setInt(6, Integer.parseInt(permissions));
                                    ps.setString(7, oneinfoId);
                                    ps.setString(8, devId);
                                    ps.executeUpdate();
                                    ps.close();

                                    String updateSQL2 = "UPDATE tb_elevator_t_authrize SET weekuid=?,starttime=?,endtime=?,storeys=?,status=0,downnum=0,creatorid=?,permissions = ? WHERE status IN (0,2) AND infoid=? AND devid=?";
                                    ps = connection.prepareStatement(updateSQL2);
                                    ps.setString(1, weekUid);
                                    ps.setString(2, starttime);
                                    ps.setString(3, endtime);
                                    ps.setString(4, storeys);
                                    ps.setString(5, userId);
                                    ps.setInt(6, Integer.parseInt(permissions));
                                    ps.setString(7, oneinfoId);
                                    ps.setString(8, devId);
                                    ps.executeUpdate();
                                    ps.close();

                                }else if(StringUtils.isNotBlank(status) && "-1".equals(status)){
                                    String updateSQL3 = "UPDATE tb_elevator_t_authrize SET weekuid=?,starttime=?,endtime=?,storeys=?,status=-1,downnum=0,creatorid=?,permissions = ? WHERE status = -1 AND infoid=? AND devid=?";
                                    PreparedStatement ps = connection.prepareStatement(updateSQL3);
                                    ps.setString(1, weekUid);
                                    ps.setString(2, starttime);
                                    ps.setString(3, endtime);
                                    ps.setString(4, storeys);
                                    ps.setString(5, userId);
                                    ps.setInt(6, Integer.parseInt(permissions));
                                    ps.setString(7, oneinfoId);
                                    ps.setString(8, devId);
                                    ps.executeUpdate();
                                    ps.close();
                                }
                            } else {
                                insertInfoId.add(oneinfoId);
                            }
                        }
                        String insertSQL = "INSERT INTO tb_elevator_t_authrize (uid, infoid, cardno, infoname, isvisitor, idcard, passpassword,qrcode, devid, machineid, serveruid, weekuid, storeys, starttime, endtime, downnum, createdate, creatorid, status,permissions) " +
                                " SELECT uuid(),infodata.infoid, infodata.cardno, infodata.name, infodata.isvisitor, infodata.idcard, infodata.passpassword , infodata.qrcode, ctrl.uid, ctrl.machineid, ctrl.server_uid,'" + weekUid + "', '" + storeys + "','" + starttime + "', '" + endtime + "','0', now(), '" + userId + "', 0,'" + permissions + "'" +
                                " FROM ((SELECT ct.uid as infoid, ct.card as cardno, ct.name, ct.idcard, ct.passpassword, '0' as isvisitor, cq.qrcode FROM tb_card_teachstudinfo ct LEFT JOIN tb_card_qrcode cq ON ct.uid = cq.infoid)) infodata " +
                                "JOIN tb_dev_accesscontroller ctrl ON ctrl.devclass='12'" +
                                " WHERE infodata.infoid IN ('" + String.join("','", insertInfoId) + "') AND ctrl.uid='" + devId + "'";
                        PreparedStatement insert = connection.prepareStatement(insertSQL);
                        insert.executeUpdate();
                        insert.close();
                        syslogs.Write(request, "梯控授权", String.format("授权名单:人员:%s,设备%s,", infoid, devdata));
                    }
                }
            });
            return Json.getJsonResult(true);
        } catch (Exception e) {
            return Json.getJsonResult(false, e.getMessage());
        }
    }
}
