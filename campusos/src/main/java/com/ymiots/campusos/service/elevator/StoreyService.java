package com.ymiots.campusos.service.elevator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.ymiots.campusos.common.BaseService;
import com.ymiots.framework.common.ExtSort;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;

@Repository
public class StoreyService extends BaseService{

	public JsonData getStoreyList(HttpServletRequest request, HttpServletResponse response,String devid,String key,int start,int limit){
		String fields="uid,code,name";
		String table="tb_elevator_devicestorey";
		String where="devid='"+devid+"'";
		if(!StringUtils.isBlank(key)) {
			where+=" and (code='"+key+"' or name like '%"+key+"%') ";
		}
		String orderby=ExtSort.Orderby(request, "code asc");
		JsonData result=dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}
	

	public JsonData getElevatorDeviceList(HttpServletRequest request, HttpServletResponse response,String key,int start,int limit){
		String fields="uid,machineid,devname";
		String table="tb_dev_accesscontroller";
		String where="devclass='12'";
		if(!StringUtils.isBlank(key)) {
			where+=" and (machineid='"+key+"' or devname like '%"+key+"%' or devip='"+key+"') ";
		}
		String orderby=ExtSort.Orderby(request, "machineid asc");
		JsonData result=dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return result;
	}
	
	public JsonResult SaveStorey(HttpServletRequest request, HttpServletResponse response,String devid, String machineid, int startlevel, int endlevel, int maxflevel){
		if(startlevel>endlevel) {
			return Json.getJsonResult("最低楼层和最高楼层参数设置错误");
		}	
		int count=dbService.getCount("tb_elevator_devicestorey", " devid='"+devid+"' and code between "+String.valueOf(startlevel)+" and "+String.valueOf(endlevel)+"");
		if(count>0) {
			return Json.getJsonResult("楼层已存在，请勿重复创建");
		}
		StringBuffer sb=new StringBuffer();
		sb.append("INSERT INTO tb_elevator_devicestorey(uid,machineid,devid,code,name,status) VALUES");
		for(int i=startlevel;i<=endlevel;i++) {
			if(i>startlevel) {
				sb.append(",");
			}
			int fcode=i;
			String f="";
			if(i<=maxflevel) {
				fcode=maxflevel-i+1;
				f="B";
			}else {
				fcode=i-maxflevel;
			}
			sb.append(String.format("(uuid(),'%s','%s',%s,'%s%s层',1)", machineid,devid,i,f,fcode,i));
		}
		dbService.excuteSql(sb.toString());
		return Json.getJsonResult(true);
	}
	
	public JsonResult DelStorey(HttpServletRequest request, HttpServletResponse response,String uids){
		String sql="delete from tb_elevator_devicestorey where find_in_set(uid,?)>0";
		dbService.excuteSql(sql, uids);
		return Json.getJsonResult(true);
	}
	
	public JsonResult UpdateStorey(HttpServletRequest request, HttpServletResponse response,String uid, String name){
		String sql="update tb_elevator_devicestorey set name=? where uid=? ";
		dbService.excuteSql(sql, name, uid);
		return Json.getJsonResult(true);
	}
}
