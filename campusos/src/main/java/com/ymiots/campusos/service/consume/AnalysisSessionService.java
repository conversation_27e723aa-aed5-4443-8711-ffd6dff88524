package com.ymiots.campusos.service.consume;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.campusos.service.SysLogsService;
import com.ymiots.framework.common.*;
import com.ymiots.framework.datafactory.CallbackTransaction;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

@Repository
public class AnalysisSessionService extends BaseService {

	@Autowired
	UserRedis userredis;

    @Autowired
    private SysLogsService syslogs;

	public JsonData getSisSession(HttpServletRequest request, HttpServletResponse response, String key, int start,int limit) {
	    Date now = new Date();
	    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String date = sdf.format(now);
        String fields = "uid,name,starttime,endtime, days,status ";
        String table = "tb_consume_session ";
        String where = "uid <= "+date+" ";
        String orderby = ExtSort.Orderby(request, "uid DESC");
        if (StringUtils.isNotBlank(key)) {
            where += " and uid='" + key + "' ";
        }
        JsonData db = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
        return db;
	}

	public JsonData getSisLogs(HttpServletRequest request, HttpServletResponse response, String uid, String key,
			int start, int limit) {
		String fields = "sl.uid,sl.analysis,d.name as analysisname,sl.remark,sl.creatorid,u.name as creatname,sl.createdate,sl.overtime,sl.success ";
		String table = "tb_consume_analysis_logs sl LEFT JOIN tb_consume_session se ON se.uid = sl.sessionid LEFT JOIN tb_sys_dictionary d ON d.code=sl.analysis and d.groupcode='SYS0000033' LEFT JOIN tb_sys_user u ON u.uid = sl.creatorid ";
		String where = "1=1 ";
        String orderby = ExtSort.Orderby(request, "sl.createdate desc");
		if (StringUtils.isNotBlank(uid)) {
			where += " and sl.sessionid = '" + uid + "' ";
		}
		if (StringUtils.isNotBlank(key)) {
			where += " and d.name like '%" + key + "%'";
		}
		JsonData db = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return db;
	}

	public JsonData getReport(HttpServletRequest request, HttpServletResponse response, String sessionid, String key,
			int start, int limit) {
		String fields = "code,name,(select count(*) FROM tb_consume_analysis_logs where sessionid='" + sessionid
				+ "' and analysis = code)as isAnalysis ";
		String table = "tb_sys_dictionary ";
		String where = "groupcode='SYS0000033' AND status=1 ";
        String orderby = ExtSort.Orderby(request, "code ASC");
		if (StringUtils.isNotBlank(key)) {
			where += " and name like '%" + key + "%'";
		}
		JsonData db = dbService.QueryJsonData(fields, table, where, orderby, start, limit);
		return db;
	}

    /**
     * 设置数据：uid,name,starttime,endtime,days,year,nextid
     * @param calendar
     * @param day
     * @return
     * @throws Exception
     */
	private Map<String,String> setData(Calendar calendar, int day) throws Exception {
        Map<String,String> map = new HashMap<String,String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        SimpleDateFormat sdfname = new SimpleDateFormat("yyyy年MM月");
        SimpleDateFormat sdftime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfday = new SimpleDateFormat("yyyy-MM-dd");

        String uid = sdf.format(calendar.getTime());
        String name =sdfname.format(calendar.getTime());
        calendar.add(Calendar.MONTH,1);
        String nextid = sdf.format(calendar.getTime());
        calendar.add(Calendar.MONTH,-1);

        String starttime = "";
        String endtime = "";
        if (day == 30) {
            //起始日为每个月倒数3天时
            calendar.add(Calendar.MONTH,1);
            calendar.set(Calendar.DAY_OF_MONTH,1);
            calendar.add(Calendar.DATE,-3);
            starttime = sdfday.format(calendar.getTime())+" 00:00:00";
            calendar.add(Calendar.MONTH,2);
            calendar.set(Calendar.DAY_OF_MONTH,1);
            calendar.add(Calendar.DATE,-4);
            endtime = sdfday.format(calendar.getTime())+" 23:59:59";
        } else if (day == 31){
            //起始日为每个月倒数2天时
            calendar.add(Calendar.MONTH,1);
            calendar.set(Calendar.DAY_OF_MONTH,1);
            calendar.add(Calendar.DATE,-2);
            starttime = sdfday.format(calendar.getTime())+" 00:00:00";
            calendar.add(Calendar.MONTH,2);
            calendar.set(Calendar.DAY_OF_MONTH,1);
            calendar.add(Calendar.DATE,-3);
            endtime = sdfday.format(calendar.getTime())+" 23:59:59";
        }else if(day == 32){
            //起始日为每个月最后一天时
            calendar.add(Calendar.MONTH,1);
            calendar.set(Calendar.DAY_OF_MONTH,1);
            calendar.add(Calendar.DATE,-1);
            starttime = sdfday.format(calendar.getTime())+" 00:00:00";
            calendar.add(Calendar.MONTH,2);
            calendar.set(Calendar.DAY_OF_MONTH,1);
            calendar.add(Calendar.DATE,-2);
            endtime = sdfday.format(calendar.getTime())+" 23:59:59";
        }else {
            calendar.set(calendar.get(Calendar.YEAR),calendar.get(Calendar.MONTH),day,00,00,00);
            starttime = sdftime.format(calendar.getTime());
            calendar.set(calendar.get(Calendar.YEAR),calendar.get(Calendar.MONTH)+1,day,23,59,59);
            calendar.add(Calendar.DATE,-1);
            endtime = sdftime.format(calendar.getTime());
            calendar.add(Calendar.DATE,1);
        }
        calendar.add(Calendar.MONTH,-1);
        int year = calendar.get(Calendar.YEAR);

        Calendar startDay = Calendar.getInstance();
        startDay.setTime(sdfday.parse(starttime));
        Calendar endDay = Calendar.getInstance();
        endDay.setTime(sdfday.parse(endtime));
        int days = (int) ((endDay.getTimeInMillis()-startDay.getTimeInMillis())/(24 * 3600 * 1000))+1;

        map.put("uid",uid);
        map.put("name",name);
        map.put("starttime",starttime);
        map.put("endtime",endtime);
        map.put("days",String.valueOf(days));
        map.put("year",String.valueOf(year));
        map.put("nextid",nextid);
        return map;
    }

    /**
     * 生成预览
     */
	public JsonData getPreview (HttpServletRequest request, HttpServletResponse response,int day){
        JsonData jd = new JsonData();
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdfname = new SimpleDateFormat("yyyy年MM月");
            SimpleDateFormat sdftime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String sql = "SELECT uid,date_format(starttime, '%Y-%m-%d %H:%i:%s') as starttime FROM tb_consume_session ORDER BY uid DESC LIMIT 1";
            JSONArray isExist = dbService.QueryList(sql);
            JSONArray ja = new JSONArray();
            JSONObject data = new JSONObject();
            if(isExist.size() > 0 ){
                JSONObject jb = isExist.getJSONObject(0);
                String date = jb.getString("starttime");
                calendar.setTime(sdftime.parse(date));
                day = calendar.get(Calendar.DATE);

                int thisMonthDay = calendar.get(Calendar.DATE);
                int thisMonthLastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

                if(thisMonthDay == thisMonthLastDay){   //判断是否每个月最后一天
                    day = 32;
                }else if((thisMonthLastDay-thisMonthDay) == 1){     //判断是否每个月倒数两天
                    day = 31;
                }else if((thisMonthLastDay-thisMonthDay) == 2){     //判断是否每个月倒数两天
                    day = 30;
                }
                data.put("isExist",true);
                data.put("day",day);
            }else {
                Date date = new Date();
                String strDate = sdftime.format(date);
                calendar.setTime(sdftime.parse(strDate));
                calendar.add(Calendar.MONTH,-1);
                data.put("isExist",false);
            }
            for (int i = 0; i<12; i++){
                JSONObject jo = new JSONObject();
                calendar.add(Calendar.MONTH,1);
                Map<String, String> map = setData(calendar, day);
                jo.put("uid",map.get("uid"));
                jo.put("name",map.get("name"));
                jo.put("starttime",map.get("starttime"));
                jo.put("endtime",map.get("endtime"));
                jo.put("days",map.get("days"));
                ja.add(jo);
            }
            JSONArray newja = new JSONArray();
            for (int j = 0; j < ja.size(); j++) {
                JSONObject jadata = ja.getJSONObject(j);
                jadata.put("isExist",data.get("isExist"));
                if(StringUtils.isNotBlank(data.getString("day"))){
                    jadata.put("day",data.getString("day"));
                }
                newja.add(jadata);
            }
            jd.setData(newja);
            jd.setSuccess(true);
        } catch (Exception e) {
            return Json.getJsonData(false,e.getMessage());
        }
        return jd;
    }

	public JsonResult saveSession(HttpServletRequest request, HttpServletResponse response,int day){
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
            String sql = "SELECT uid FROM tb_consume_session ORDER BY uid DESC LIMIT 1";
            JSONArray isExist = dbService.QueryList(sql);
            String previd = new String();
            if(isExist.size() > 0 ){
                JSONObject jb = isExist.getJSONObject(0);
                previd = jb.getString("uid");
                calendar.setTime(sdf.parse(previd));
            }else {
                Date date = new Date();
                String strDate = sdf.format(date);
                calendar.setTime(sdf.parse(strDate));
                calendar.add(Calendar.MONTH,-1);
            }

            StringBuffer insertSQL = new StringBuffer();
            insertSQL.append("INSERT INTO tb_consume_session(uid,name,starttime,endtime,days,year,previd,nextid,status) VALUES");

            for (int i = 0; i<12; i++){
                previd = sdf.format(calendar.getTime());

                calendar.add(Calendar.MONTH,1);
                Map<String, String> map = setData(calendar, day);
                String uid = map.get("uid");
                String name = map.get("name");
                String starttime = map.get("starttime");
                String endtime = map.get("endtime");
                String days = map.get("days");
                String year = map.get("year");
                String nextid = map.get("nextid");
                insertSQL.append("('"+uid+"','"+name+"','"+starttime+"','"+endtime+"',"+days+","+year+","+previd+","+nextid+",0),");
            }
            insertSQL.deleteCharAt(insertSQL.length() - 1);
            dbService.excuteSql(insertSQL.toString());
            syslogs.Write(request, "结算周期",String.format("生成周期"));
        } catch (Exception e) {
            return Json.getJsonResult(false,e.getMessage());
        }
        return Json.getJsonResult(true);
    }

    public JsonResult lockSession(HttpServletRequest request, HttpServletResponse response,String uid){
        if(StringUtils.isBlank(uid)){
            return Json.getJsonResult(false,"uid为空");
        }
        String sql = "SELECT status FROM tb_consume_session WHERE uid = '"+uid+"'";
        JSONArray ja = dbService.QueryList(sql);
        JSONObject jo = ja.getJSONObject(0);
        String status = jo.getString("status");
        if("0".equals(status)){
            String update = "UPDATE tb_consume_session SET status=1 WHERE uid = '"+uid+"'";
            dbService.excuteSql(update);
            syslogs.Write(request, "结算周期",String.format("锁定周期:%s",uid));
        }else {
            String update = "UPDATE tb_consume_session SET status=0 WHERE uid = '"+uid+"'";
            dbService.excuteSql(update);
            syslogs.Write(request, "结算周期",String.format("解锁周期:%s",uid));
        }
        return Json.getJsonResult(true);
    }

	public JsonResult analysisReport(HttpServletRequest request, HttpServletResponse response, final String sessionid, String analysis) {
		String[] split = analysis.split(",");
		String creatorid = userredis.getUserId(request);
		for (String code : split) {
			final String uid = UUID.randomUUID().toString();
			String sql = "INSERT INTO tb_consume_analysis_logs(uid,sessionid,analysis,success,creatorid,createdate) VALUES (?,?,?,1,?,now())";
			dbService.excuteSql(sql, uid, sessionid, code, creatorid);

			try {
				switch (Integer.parseInt(code)) {
				case 1:
					new Thread(new Runnable() {
						@Override
						public void run() {
                            AnalysisMonthReport(uid, sessionid, "personal");
						}
					}).start();
					break;
				case 2:
					new Thread(new Runnable() {
						@Override
						public void run() {
                            AnalysisMonthReport(uid, sessionid, "device");
						}
					}).start();
					break;
				case 3:
					new Thread(new Runnable() {
						@Override
						public void run() {
                            AnalysisMonthReport(uid, sessionid, "mplace");
						}
					}).start();
					break;

				default:
					return Json.getJsonResult(false, "不存在报表类型:" + code);
				}
			} catch (Exception ex) {
				Log.error(AnalysisSessionService.class, "消费数据分析失败：" + ex.getMessage());
				dbService.excuteSql("update tb_consume_analysis_logs set success=0,overtime=now(),remark=? where uid=?", "失败", uid);
			}
		}
		return Json.getJsonResult(true);
	}

    /**
     * 结算周期-月消费统计
     * @param uid
     * @param sessionid 周期id
     * @param type 分析类型：personal(个人消费月报表)，device(设备消费月报表)mplace(商户场所月报表)
     */
	public void AnalysisMonthReport(final String uid, String sessionid,final String type){
        try {
            Log.info(AnalysisSessionService.class, "消费月报表数据统计");
            JSONArray slist=dbService.QueryList("SELECT date_format(starttime,'%Y-%m-%d') as starttime,date_format(endtime,'%Y-%m-%d') as endtime FROM tb_consume_session where uid="+sessionid);
            if(slist.size()==0) {
                dbService.excuteSql("update tb_consume_analysis_logs set success=0,overtime=now(),remark=? where uid=?", "结算周期不存在", uid);
                return;
            }
            JSONObject sissession=slist.getJSONObject(0);
            final String starttime =sissession.getString("starttime");
            final String endtime =sissession.getString("endtime");
            dbService.excuteTransaction(new CallbackTransaction() {
                @Override
                public void execute(Connection connection) throws SQLException {
                    PreparedStatement ps;
                    if("personal".equals(type)){
                        ps= connection.prepareStatement("call sp_consume_person_analysis_handle(?,?)");
                        ps.setString(1, starttime);
                        ps.setString(2, endtime);
                        ps.executeUpdate();
                        ps.close();
                    }else if("device".equals(type)){
                        ps= connection.prepareStatement("call sp_consume_device_analysis_handle(?,?)");
                        ps.setString(1, starttime);
                        ps.setString(2, endtime);
                        ps.executeUpdate();
                        ps.close();
                    }else if("mplace".equals(type)){
                        ps= connection.prepareStatement("call sp_consume_place_analysis_handle(?,?)");
                        ps.setString(1, starttime);
                        ps.setString(2, endtime);
                        ps.executeUpdate();
                        ps.close();
                    }


                    ps= connection.prepareStatement("update tb_consume_analysis_logs set success=2,overtime=now(),remark='成功'  where uid=?");
                    ps.setString(1, uid);
                    ps.executeUpdate();
                    ps.close();
                }
            });
        } catch (Exception e) {
            Log.error(AnalysisSessionService.class, "消费月统计报表数据失败：" + e.getMessage());
        }
    }
}
