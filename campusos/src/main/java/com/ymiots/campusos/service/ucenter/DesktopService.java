package com.ymiots.campusos.service.ucenter;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.redis.UserRedis;
import com.ymiots.framework.common.DateHelper;
import com.ymiots.framework.common.HttpRequest;
import com.ymiots.framework.common.Json;
import com.ymiots.framework.common.JsonData;
import com.ymiots.framework.common.JsonResult;
import com.ymiots.framework.common.MD5;
import com.ymiots.framework.common.TreeHelper;

@Repository
public class DesktopService extends BaseService{

	@Autowired
	UserRedis userredis;

	public JsonData GetDesktopInfo(HttpServletRequest request, HttpServletResponse response) {
		String userid=userredis.getUserId(request);
		String fields="wu.uid,wu.widgetuid,w.name,w.des,w.icosrc,wu.cfgjson,wu.sort";
		String table="tb_sys_desktop_widget_user wu inner join tb_sys_desktop_widget w on wu.widgetuid=w.uid";
		String where="w.status=1 and wu.userid='"+userid+"'";
		String sql="select "+fields+" from " + table+" where "+ where+" order by wu.sort asc";
		JSONArray widgetlist=dbService.QueryList(sql);

		JSONArray resultlist=new JSONArray();

		for(int i=0;i<widgetlist.size();i++){
			JSONObject widget=widgetlist.getJSONObject(i);
			int widgetuid=widget.getIntValue("widgetuid");
			if(widgetuid==1){
				//人员+设备+消费
				String filter=widget.getJSONArray("cfgjson").getJSONObject(0).getString("value");
				JSONObject item=new JSONObject();
				item.put("areacode", filter);
				item.put("widgetuid", widgetuid);
				item.put("widgetname", widget.getString("name"));
				resultlist.add(item);
			}

			if(widgetuid==2){
				//人员+设备+消费+请假
				String filter=widget.getJSONArray("cfgjson").getJSONObject(0).getString("value");
				JSONObject item=new JSONObject();
				item.put("areacode", filter);
				item.put("widgetuid", widgetuid);
				item.put("widgetname", widget.getString("name"));
				resultlist.add(item);
			}
			if(widgetuid==3){
				//人员+设备+消费+请假+宿舍+床位
				String filter=widget.getJSONArray("cfgjson").getJSONObject(0).getString("value");
				JSONObject item=new JSONObject();
				item.put("areacode", filter);
				item.put("widgetuid", widgetuid);
				item.put("widgetname", widget.getString("name"));
				resultlist.add(item);
			}
		}
		return Json.getJsonData(true, resultlist);
	}
	public JsonData getWidgetList(HttpServletRequest request, HttpServletResponse response) {
		int usertype=userredis.get(request).getUsertype();
		String userid=userredis.getUserId(request);
		String where="status=1 and uid not in(select widgetuid from tb_sys_desktop_widget_user where userid='"+userid+"')";
		if(usertype!=9 && usertype!=2){
			where+=" and usertype<="+String.valueOf(usertype);
		}
		return dbService.QueryJsonData("*", "tb_sys_desktop_widget", where, "sort asc");
	}
	public JsonData getUserWidgetList(HttpServletRequest request, HttpServletResponse response) {
		String userid=userredis.getUserId(request);
		String fields="wu.uid,wu.widgetuid,w.name,w.des,w.icosrc,wu.cfgjson,wu.sort";
		String table="tb_sys_desktop_widget_user wu inner join tb_sys_desktop_widget w on wu.widgetuid=w.uid";
		String where="w.status=1 and wu.userid='"+userid+"'";
		return dbService.QueryJsonData(fields, table, where, "wu.sort asc");
	}


	public JsonResult MoveToUser(HttpServletRequest request, HttpServletResponse response, String widgetuid) {
		String userid=userredis.getUserId(request);
		int count=dbService.getCount("tb_sys_desktop_widget_user", "widgetuid='"+widgetuid+"' and userid='"+userid+"'");
		if(count>0){
			return Json.getJsonResult("桌面已经存在该部件");
		}
		String sql="insert into tb_sys_desktop_widget_user(uid,widgetuid,userid,sort,cfgjson) select uuid(),uid,'"+userid+"',sort,cfgjson from tb_sys_desktop_widget where uid=?";
		dbService.excuteSql(sql, widgetuid);
		return Json.getJsonResult(true);
	}
	public JsonResult MoveToAll(HttpServletRequest request, HttpServletResponse response, String widgetuid) {
		String sql="delete from tb_sys_desktop_widget_user where uid=? ";
		dbService.excuteSql(sql, widgetuid);
		return Json.getJsonResult(true);
	}
	public JsonResult MoveSort(HttpServletRequest request, HttpServletResponse response, String uid,String sort, String type) {
		String userid=userredis.getUserId(request);
		String sql="select uid,sort from tb_sys_desktop_widget_user where userid='"+userid+"' and uid<>'"+uid+"' ";
		if(type.equals("prev")){
			 sql+=" and sort<="+sort+" order by sort desc limit 1";
		}
		if(type.equals("next")){
			 sql+=" and sort>="+sort+" order by sort asc limit 1";
		}
		if(sql.length()>0){
			JSONArray list= dbService.QueryList(sql);
			if(list.size()>0){
				JSONObject item=list.getJSONObject(0);
				int newsort=item.getIntValue("sort");
				dbService.excuteSql("update tb_sys_desktop_widget_user set sort=? where uid=?", sort,item.getString("uid"));
				dbService.excuteSql("update tb_sys_desktop_widget_user set sort=? where uid=?", newsort,uid);
			}
		}
		return Json.getJsonResult(true);
	}

	public Object getUserWidgetFilterData(HttpServletRequest request, HttpServletResponse response, String widgetuid,String filtervalue) {
		int widget=Integer.parseInt(widgetuid);
		String userid=userredis.getUserId(request);
		String sql="";
		switch(widget){
			case 1:
				//通讯服务监控，过滤通讯服务编号
				sql="select code,name from tb_dev_service";
				break;
			case 2:
				//刷卡监控，过滤区域编号，且树状结构
			case 3:
				//归寝监控
				if(userredis.get(request).getUsertype()==1){
					sql=" select area.uid,area.code,area.areatype,area.name,area.parentid,area.status ";
					sql+=" from tb_dev_areaframework area "
							+ " inner join tb_dev_areaframework_user ua on ua.areacode=area.code and ua.userid='"+userid+"' "
							+ " where area.code in(select areacode from tb_dev_accesscontroller where status=1) ";
					sql+=" order by area.code asc ";
				}else{
					sql="select uid,code,areatype,name,parentid,status from tb_dev_areaframework where code in(select areacode from tb_dev_accesscontroller where status=1) order by code asc ";
				}
				break;
			default:
				break;
		}
		if(StringUtils.isBlank(sql)){
			return Json.getJsonData(true);
		}
		JSONArray list=dbService.QueryList(sql);

		if(widget==2 || widget==3){
			for(int i=0;i<list.size();i++){
				String code=list.getJSONObject(i).getString("code");
				if(String.format(",%s,", filtervalue).indexOf(String.format(",%s,", code))!=-1){
					list.getJSONObject(i).put("checked", true);
				}else{
					list.getJSONObject(i).put("checked", false);
				}
			}
			//树状数据处理
			JSONArray rootlist= TreeHelper.findRootList(list, "parentid", "uid");
			for(Object m:rootlist){
				JSONObject item=(JSONObject)m;
				JSONArray children=TreeHelper.getChildren(list, "parentid",item.getString("uid"),"uid");
				if (children.size() > 0) {
					item.put("leaf", false);
					item.put("children", children);
					item.put("expanded", true);
				}else{
					item.put("leaf", true);
					item.put("children", new JSONArray());
					item.put("expanded", true);
				}
			}
			return rootlist;
		}
		return Json.getJsonData(true,list);
	}

	public JsonResult SaveUserWidgetCfgJson(HttpServletRequest request, HttpServletResponse response, String uid, String cfgjson) {
		String sql="update tb_sys_desktop_widget_user set cfgjson=? where uid=?";
		dbService.excuteSql(sql, cfgjson,uid);
		return Json.getJsonResult(true);
	}

	public JsonResult GetServiceOsInfo(HttpServletRequest request, HttpServletResponse response,String appid,String serviceurl,int angelport) {
		String url=String.format("http://%s:%s/Service/GetOsInfo", serviceurl,angelport);
		String token=MD5.MD5Encode(appid);
		List<Cookie> cookies=new ArrayList<Cookie>();
		Cookie cookie=new Cookie("tokenid", token.toLowerCase());
		cookies.add(cookie);
		Map<String,String> params=new HashMap<String,String>();
		JsonResult result=	HttpRequest.HttpPost(url,params,cookies);
		if(result.isSuccess()){
			JSONObject data= result.getData();
			if(data.getBooleanValue("Success")){
				JSONObject rs=data.getJSONObject("Msg");
				JSONObject Memory=rs.getJSONObject("Memory");
				float Cpu=rs.getFloat("Cpu");
				JSONArray CPUInfo=rs.getJSONArray("CPUInfo");

				JSONObject info=new JSONObject();
				info.put("suid", appid);
				if(CPUInfo!=null && CPUInfo.size()>0){
					info.put("cpunum", CPUInfo.getJSONObject(0).getIntValue("cores"));
					info.put("cpuname", CPUInfo.getJSONObject(0).getString("modelName"));
				}
				info.put("cpurate", Cpu);
				if(Memory!=null){
					info.put("usedPercent", Memory.getFloat("usedPercent"));
					info.put("total",Memory.getLong("total"));
					info.put("used", Memory.getLong("used"));
				}
				return Json.getJsonResult(true,info);
			}else{
				return Json.getJsonResult(false,data.getString("Msg"));
			}
		}else{
			return Json.getJsonResult(false,result.getMsg());
		}
	}

	public JsonResult GetBackHomeInfo(HttpServletRequest request, HttpServletResponse response,String areacode) {
		if(StringUtils.isBlank(areacode)){
			return Json.getJsonResult(false);
		}

		String[] areacodex=areacode.split(",");

		String codelike="";
		String codelike2="";
		for(int i=0;i<areacodex.length;i++){
			if(i<=0){
				codelike=" bed.code like '"+areacodex[i]+"%' ";
				codelike2=" areacode like '"+areacodex[i]+"%' ";
			}else{
				codelike+=" or bed.code like '"+areacodex[i]+"%' ";
				codelike2+=" or areacode like '"+areacodex[i]+"%' ";
			}
		}

		//入住总人数
		int bedpersoncount=dbService.getCount("tb_hotel_bed bed", "bed.status=1 and ("+codelike+")");
		//当前归寝人数
		String startdate=DateHelper.format(new Date(), "yyyy-MM-dd 00:00:00");

		String sql="select count(distinct p.infoid) as total from (select bed.infoid from tb_hotel_bed bed left join ("
				+ "  select s.recordtime,s.direction,s.infoid,s.areacode from tb_alleyway_records s inner join ("
				+ "      select max(recordtime) as recordtime,infoid from tb_alleyway_records where recordtime>'"+startdate+"' and (infoid<>'' and not infoid is null) and  ( "+codelike2+") group by infoid"
				+ "  ) t on t.infoid=s.infoid and s.recordtime=t.recordtime"
				+ ") r "
				+ " on r.areacode=getdeviceareacode(bed.code) and r.infoid=bed.infoid"
				+ " where bed.status=1 and ("+codelike+") and not r.recordtime is null and not r.direction is null and r.direction=1 "
				+ ") p";

		int backhomecount = dbService.getCountBySQL(sql,"total");
		JSONObject data=new JSONObject();
		data.put("bedpersoncount", bedpersoncount);
		data.put("backhomecount", backhomecount);
		data.put("nobackcount", bedpersoncount-backhomecount);
		return Json.getJsonResult(true,"",data);
	}
}
