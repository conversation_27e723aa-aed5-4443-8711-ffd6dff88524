package com.ymiots.campusos.task;

import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.service.SysTaskService;
import com.ymiots.campusos.service.card.CardNumberService;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.common.ScheduledInterface;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository("AutoDeleteTask")
public class AutoDeleteTask extends BaseService implements ScheduledInterface {

    @Autowired
    SysTaskService taskservice;

    @Autowired
    CardNumberService cardNumberService;

    @Override
    public Runnable doTask(final String code, String sqlcode) {
        return new Runnable() {
            @Override
            public void run() {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        long startTimeMillis=System.currentTimeMillis();
                        try {
                            Log.info(AutoDeleteTask.class, "开始删除人脸数据");
                            cardNumberService.autoDeleteTask();
                            Log.info(this.getClass(), "完成人脸权限删除");
                            taskservice.UpdateSysTaskLastExeTime(code, "执行正常",startTimeMillis);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            Log.error(this.getClass(), "人脸权限下发失败：" + ex.getMessage());
                            taskservice.UpdateSysTaskLastExeTime(code, "执行失败："+ex.getMessage(),startTimeMillis);
                        }
                    }
                }).start();
            }
        };
    }

    @Data
    public static class DeviceGroupsDto {
        private String groupId;
        private String devId;
    }
}
