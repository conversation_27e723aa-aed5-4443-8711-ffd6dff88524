package com.ymiots.campusos.task;

import com.ymiots.campusos.common.BaseService;
import com.ymiots.campusos.dto.thirdapi.anlide.EquipmentQueryResponse;
import com.ymiots.campusos.service.thirdapi.AnLiDeWaterCtrlService;
import com.ymiots.campusos.service.waterctrl.*;
import com.ymiots.framework.common.Log;
import com.ymiots.framework.common.ScheduledInterface;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 设备费用计算定时任务
 *
 * <AUTHOR>
 * @date 2025-06-28
 */
@Repository("EquipmentCostCalculationTask")
public class EquipmentCostCalculationTask extends BaseService implements ScheduledInterface {

    @Autowired
    private  AutoSyncWaterSystemService autoSyncWaterSystemService;

    @Autowired
    private EquipmentCostService equipmentCostService;

    @Autowired
    private EquipmentCostSummaryService equipmentCostSummaryService;

    @Autowired
    private EquipmentExceptionMonitorService equipmentExceptionMonitorService;

    @Autowired
    private AsyncEquipmentDataSyncService asyncEquipmentDataSyncService;

    @Autowired
    private AnLiDeWaterCtrlService anLiDeWaterCtrlService;

    @Override
    public Runnable doTask(final String code, String sqlcode) {
        return () -> {
            try {
                Log.info(EquipmentCostCalculationTask.class, "开始执行设备费用计算定时任务...");
                //获取到水电控设备列表入库
                EquipmentQueryResponse equipmentResponse = anLiDeWaterCtrlService.getEquipment();

                // 1. 同步设备读数数据
                autoSyncWaterSystemService.syncWaterSystem(equipmentResponse);

                // 2. 获取到每个设备的读数
                asyncEquipmentDataSyncService.syncAllEquipmentDataAsync(equipmentResponse);

                // 4. 如果是每月1号，生成上月汇总
                generateMonthlySummaryIfNeeded();

                // 5. 检查设备异常
                checkEquipmentExceptions();

                Log.info(EquipmentCostCalculationTask.class, "设备费用计算定时任务执行完成");

            } catch (Exception e) {
                Log.error(EquipmentCostCalculationTask.class, "设备费用计算定时任务执行失败: " + e.getMessage());
            }
        };
    }


    /**
     * 计算昨天的费用
     */
    private void calculateYesterdayCost() {
        try {
            Log.info(EquipmentCostCalculationTask.class, "开始计算昨天的费用...");

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1); // 昨天
            Date yesterday = calendar.getTime();

            equipmentCostService.batchCalculateCost(yesterday);

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Log.info(EquipmentCostCalculationTask.class,
                    "昨天(" + dateFormat.format(yesterday) + ")的费用计算完成");

        } catch (Exception e) {
            Log.error(EquipmentCostCalculationTask.class, "计算昨天费用失败: " + e.getMessage());
        }
    }

    /**
     * 如果需要，生成月度汇总
     */
    private void generateMonthlySummaryIfNeeded() {
        try {
            Calendar calendar = Calendar.getInstance();
            int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);

            // 如果是每月1号，生成上月汇总
            if (dayOfMonth == 1) {
                Log.info(EquipmentCostCalculationTask.class, "开始生成上月费用汇总...");

                // 获取上月
                calendar.add(Calendar.MONTH, -1);
                SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
                String lastMonth = monthFormat.format(calendar.getTime());

                equipmentCostSummaryService.batchGenerateMonthlySummary(lastMonth);

                Log.info(EquipmentCostCalculationTask.class,
                        "上月(" + lastMonth + ")费用汇总生成完成");
            }
        } catch (Exception e) {
            Log.error(EquipmentCostCalculationTask.class, "生成月度汇总失败: " + e.getMessage());
        }
    }

    /**
     * 检查设备异常
     */
    private void checkEquipmentExceptions() {
        try {
            Log.info(EquipmentCostCalculationTask.class, "开始检查设备异常...");
            equipmentExceptionMonitorService.autoCheckEquipmentExceptions();
            Log.info(EquipmentCostCalculationTask.class, "设备异常检查完成");
        } catch (Exception e) {
            Log.error(EquipmentCostCalculationTask.class, "检查设备异常失败: " + e.getMessage());
        }
    }

    public void stop() {
        Log.info(EquipmentCostCalculationTask.class, "设备费用计算定时任务停止");
    }
}
