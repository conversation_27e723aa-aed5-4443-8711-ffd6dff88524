server:
  port: 2018
spring:
  profiles:
    active: framework
  datasource:
    url: *******************************************************************************************************************
    username: root
    password: '000000'
    driver-class-name: com.mysql.cj.jdbc.Driver
    backup-path: db_backup
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    mode: HTML5
    encoding: UTF-8
    cache: false
    servlet:
      content-type: text/html
  redis:
    host: 127.0.0.1
    port: 6379
    password: '000000'
    persistent-db-no: 3
    default-db-no: 0
    jet-cache-db-no: 4
#  mail:
#    default-encoding: utf-8
#    protocol: smtp
#    properties:
#      mail:
#        smtp:
#          auth: true
#          starttls:
#            enable: true
#            required: true

webconfig:
  startNew: true
  processtask: 1
  serverid: ""
  appversion: V6.0.0
  uploaddir: D:/upload/
  domain:
  resourceDomain: ""
  weixinDomain: http://robot.ymiots.com
  parkDomain: ""
  thirdParty: 127.0.0.1:2030
  nodeid:
  campusCPDomain:
  campusCPAppId:
  campusCPAppSecret:
  #通联支付为1 微信支付2 富有为 3
  payType: 2
  mchntCode: 1615080057
  # 第三方服务地址
  iotServerAddress: http://park.gdtxwlw.com
  iotServerSecret: eebdeb8bb6aad4aad3c19fc080e98db3
  parkId: 86
#  wxAppId: "wx9fa8023ab8b47b04"
yunmai:
  security:
    permit-all_urls:
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录

regular-clean-mysql-task:
  month: 6
# 大屏宿舍查询住宿生 1开启只查住宿生 2查全部
property: 2

