<!DOCTYPE html>
<html lang="en" class="no-js" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">

<head>

    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
    <meta name="renderer" content="webkit|ie-comp|ie-stand" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <title th:text="${title}"></title>

    <script src="/resources/js/jquery-3.2.1.min.js"></script>
    <!--[if lt IE 9]>
    <script type="text/javascript" src="/resources/h-ui/lib/html5shiv.js"></script>
    <script type="text/javascript" src="/resources/h-ui/lib/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" type="text/css" href="/resources/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="/resources/h-ui/lib/Hui-iconfont/1.0.8/iconfont.min.css" />
    <!--[if lt IE 9]>
    <link href="/resources/h-ui/css/H-ui.ie.css" rel="stylesheet" type="text/css" />
    <![endif]-->
    <!--[if IE 6]>
    <script type="text/javascript" src="/resources/h-ui/lib/DD_belatedPNG_0.0.8a-min.js" ></script>
    <script>DD_belatedPNG.fix('*');</script>
    <![endif]-->
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&amp;ak=BeMM1sYl6MadtgNh5xnQ5UnTvXanSu23"></script>
    <link rel="stylesheet" type="text/css" href="/resources/style/main.css" />
</head>

<body style="background-color: rgb(233, 232, 232)">
    <div class="wap-container" style="min-height:900px;padding-top: 50px;padding-bottom: 50px;margin-left: auto;margin-right: auto;width: 800px; background-color: #fff;border-left: 1px solid #ccc;border-right: 1px solid #ccc;">
        <form class="form form-horizontal responsive" id="demoform">
            <div id="topdiv" style="height: 200px; display: block; width: 100%;"></div>
            <div id="msg" style="width: 80%; margin-left: auto;margin-right: auto;" class="Huialert Huialert-error"><i class="Hui-iconfont">&#xe6a6;</i><span th:text="${msg}"></span></div>
            <div id="success" style="width: 80%; margin-left: auto;margin-right: auto;display: none;" class="Huialert Huialert-success"><i class="Hui-iconfont">&#xe6a6;</i><span></span></div>

            <div class="row cl">
                <label class="form-label col-xs-3">软件序列号：</label>
                <div class="formControls col-xs-8">
                    <input type="text" class="input-text" placeholder="请输入软件序列号" name="licenseSN" id="licenseSN" autocomplete="off" />
                </div>
            </div>
            <div id="hardinfo" style="display: none;">
                <div class="row cl">
                    <label class="form-label col-xs-3">CPU SN：</label>
                    <div class="formControls col-xs-8">
                        <input type="text" class="input-text" th:value="${cpu}" name="cpu" id="cpu" readonly="readonly" />
                    </div>
                </div>
                <div class="row cl">
                    <label class="form-label col-xs-3">DISK SN：</label>
                    <div class="formControls col-xs-8">
                        <input type="text" class="input-text" th:value="${disksn}" name="disksn" id="disksn" readonly="readonly" />
                    </div>
                </div>
                <div class="row cl">
                    <label class="form-label col-xs-3">主板SN：</label>
                    <div class="formControls col-xs-8">
                        <input type="text" class="input-text" th:value="${boardsn}" name="boardsn" id="boardsn" readonly="readonly" />
                    </div>
                </div>
            </div>

            <div class="row cl">
                <div class="col-xs-8 col-xs-offset-3">
                    <input class="btn btn-primary" type="button" value="确定" style="width: 120px;" id="submit1" />
                </div>
            </div>

            <div id="sept2" style="display: none;">
                <div class="row cl">
                    <label class="form-label col-xs-3">单位名称：</label>
                    <div class="formControls col-xs-8">
                        <input type="text" class="input-text" placeholder="请填写完整的单位名称" name="name" id="name" />
                    </div>
                </div>
                <div class="row cl">
                    <label class="form-label col-xs-3">单位地址：</label>
                    <div class="formControls col-xs-8">
                        <input type="text" class="input-text" autocomplete="off" placeholder="请填写完整的单位地址" name="address" id="address" />
                    </div>
                </div>
                <div class="row cl">
                    <label class="form-label col-xs-3">联系人：</label>
                    <div class="formControls col-xs-8">
                        <input type="text" class="input-text" autocomplete="off" placeholder="联系人姓名" name="linkman" id="linkman" />
                    </div>
                </div>
                <div class="row cl">
                    <label class="form-label col-xs-3">称谓：</label>
                    <div class="formControls col-xs-8">
                        <input type="text" class="input-text" autocomplete="off" placeholder="联系人称谓" name="linktitle" id="linktitle" value="先生"/>
                    </div>
                </div>
                <div class="row cl">
                    <label class="form-label col-xs-3">手机号码：</label>
                    <div class="formControls col-xs-8">
                        <input type="text" class="input-text" value="" autocomplete="off" placeholder="联系人手机号码" id="mobile" name="mobile" />
                    </div>
                </div>
                <div id="container" style="height: 400px;margin: 30px;display: block;"></div>
                <div class="row cl">
                    <label class="form-label col-xs-3">地图搜索：</label>
                    <div class="formControls col-xs-8">
                        <div class="row clearfix" style="margin-top:0">
                            <div class="col-xs-8">
                                <input type="text" class="input-text" value="" autocomplete="off" placeholder="输入关键词搜索地图位置" id="mapkey" />
                            </div>
                            <div class="col-xs-4">
                                <input class="btn btn-primary" type="button" value="地图搜索" style="width: 120px;" id="mapsearch" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row clearfix">
                    <label class="form-label col-xs-3">经纬坐标：</label>
                    <div class="formControls col-xs-8">
                        <div class="row clearfix" style="margin-top:0">
                            <div class="col-xs-6">
                                <input type="text" class="input-text" value="" autocomplete="off" placeholder="经度，点击地图拾取" readonly="readonly" id="lat" name="lat" />
                            </div>
                            <div class="col-xs-6">
                                <input type="text" class="input-text" value="" autocomplete="off" placeholder="纬度，点击地图拾取" readonly="readonly" id="lng" name="lng" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row cl">
                    <label class="form-label col-xs-3">坐标地址：</label>
                    <div class="formControls col-xs-8">
                        <input type="text" class="input-text" value="" autocomplete="off" placeholder="坐标地址，点击地图获取" readonly="readonly" id="mapaddress" name="mapaddress" />
                    </div>
                </div>
                <input type="hidden" value="" id="province" />
                <input type="hidden" value="" id="city" />
                <input type="hidden" value="" id="LicenseSecret" />


                <div class="row cl">
                    <div class="col-xs-8 col-xs-offset-3">
                        <input class="btn btn-primary" type="button" value="确定" style="width: 120px;" id="submit2" />
                    </div>
                </div>
            </div>
        </form>
    </div>
    <script src="/resources/js/license.js"></script>
</body>
</html>
