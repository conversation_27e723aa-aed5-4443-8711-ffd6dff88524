<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity3">
<head>
    <meta charset="utf-8" />
    <title th:text="${title}"></title>
    <link href="/resources/dataview/swiper.min.css" rel="stylesheet" />
    <link href="/resources/dataview/main.css" rel="stylesheet" />
    <link th:href="${viewtheme}" rel="stylesheet" />
    <!--<link th:href="@{'/dataview/'+${dataviewid}+'.css'}" rel="stylesheet" />-->
    <script src="/resources/js/jquery-3.2.1.min.js" rel="script"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script src="/resources/dataview/jquery.liMarquee.js"></script>
    <script src="/resources/dataview/swiper.min.js"></script>
    <script src="/resources/dataview/echarts.min.js"></script>
    <script src="/resources/dataview/chalk.js"></script>
    <script src="/resources/dataview/template.js"></script>
    <script src="/resources/dataview/main.js" async></script>
    <!-- 引入 jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 引入 Select2 的 CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
    <!-- 引入 Select2 的 JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script th:inline="javascript">
        var dataviewid=[[${dataviewid}]];
    </script>
    <script th:src="@{'/dataview/'+${dataviewid}+'.js'}" async></script>
</head>
<body>
<div id="container">
    <div id="title" th:text="${title}"></div>
    <div id="dromselect" style="display: flex;">
        <div class="custom-dropdown" id="custom-dropdown" hidden><select id="dropdown" ></select></div>
        <div class="custom-dropdown" id="custom-dropdown2" hidden><select id="dropdown2" ></select></div>
        <div class="custom-dropdown" id="custom-dropdown1" hidden><select id="fruits" multiple="multiple" style="width: 150px;background-color: black;color: #FFFFFF;overflow-y: auto;margin-top: 5px;margin-left: 15px"></select></div>
    </div>
    <div id="timeclock">
        <div class="welcom" th:text="${clocktext}"></div>
        <div class="time"></div>
    </div>
    <div id="main"></div>
</div>
</body>
</html>