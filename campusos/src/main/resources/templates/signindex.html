<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>阳光八小放学签到大屏</title>
    <script src="/resources/js/jquery.js"></script>
    <link rel="stylesheet" href="/resources/style/comon0.css">
</head>
<script>
    $(window).load(function () {
        $(".loading").fadeOut()
    })

    /****/
    $(document).ready(function () {
        var whei = $(window).width()
        $("html").css({fontSize: whei / 20})
        $(window).resize(function () {
            var whei = $(window).width()
            $("html").css({fontSize: whei / 20})
        });
    });
</script>
<body>
<div class="canvas" style="opacity: .2">
    <iframe frameborder="0" src="/signEffects" style="width: 100%; height: 100%"></iframe>
</div>
<div class="loading">
    <div class="loadbox"><img src="/resources/images/loading.gif"> 页面加载中...</div>
</div>
<div class="head">
    <input type="hidden" id="orgcode" th:value="${orgcode}" />
    <div class="header-container">
        <div class="logo">
            <img src="/resources/images/sign_logo.png" alt="学校 Logo">
        </div>
        <h1 class="title">阳光八小签到大屏</h1>
    </div>
    <div class="weather">
        <span id="showTime"></span>
    </div>
    <div style="position: absolute; left: 10px; top: 10px; z-index: 9999;">
        <select id="classSelect" class="custom-select"></select>
    </div>

    <style>
        /* 容器样式，居中对齐 */
        .header-container {
            display: flex;
            align-items: center;
            justify-content: center; /* 水平居中 */
            gap: 20px; /* Logo 和标题之间的间距 */
        }

        .logo img {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }

        .title {
            font-size: 2rem;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #333; /* 标题文字颜色 */
        }
        /* 基础样式 */
        .custom-select {
            -webkit-appearance: none; /* 移除浏览器默认样式 */
            -moz-appearance: none;
            appearance: none;
            font-size: 16px;
            padding: 12px 20px;
            width: 200px;
            border: 2px solid #00bcd4; /* 使用鲜艳的蓝色边框 */
            border-radius: 50px; /* 圆角边框 */
            background: linear-gradient(135deg, #00bcd4, #2196f3); /* 渐变背景 */
            color: white;
            text-align: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 阴影效果 */
            transition: all 0.3s ease; /* 平滑过渡效果 */
        }

        /* 鼠标悬停时 */
        .custom-select:hover {
            border-color: #1e88e5; /* 鼠标悬停时改变边框颜色 */
            background: linear-gradient(135deg, #2196f3, #00bcd4); /* 背景渐变反转 */
            cursor: pointer; /* 鼠标指针样式 */
        }

        /* 聚焦时的效果 */
        .custom-select:focus {
            outline: none; /* 去掉浏览器默认的焦点轮廓 */
            border-color: #00796b; /* 聚焦时改变边框颜色 */
            box-shadow: 0 0 10px rgba(0, 150, 136, 0.5); /* 聚焦时的阴影效果 */
        }

        /* 下拉箭头的样式 */
        .custom-select::after {
            content: '\25BC'; /* 向下箭头 */
            font-size: 16px;
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #ffffff;
            pointer-events: none; /* 阻止箭头被点击 */
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .custom-select {
                width: 150px; /* 小屏幕时，宽度缩小 */
                font-size: 14px;
                padding: 10px 15px;
            }
        }
    </style>

    <script>
        $(document).ready(function () {


            var t = null;
            t = setTimeout(time, 1000); //开始运行
            function time() {
                clearTimeout(t); //清除定时器
                dt = new Date();
                var y = dt.getFullYear();
                var mt = dt.getMonth() + 1;
                var day = dt.getDate();
                var h = dt.getHours(); //获取时
                h = h.toString().padStart(2, '0');
                var m = dt.getMinutes(); //获取分
                m = m.toString().padStart(2, '0');
                var s = dt.getSeconds(); //获取秒
                s = s.toString().padStart(2, '0');
                document.getElementById("showTime").innerHTML = y + "年" + mt + "月" + day + "日-" + h + ":" + m + ":" + s + "";
                t = setTimeout(time, 1000); //设定定时器，循环运行
            }

            $.ajax({
                url: '/Dorm/dataview/getClasslist',  // 替换为你的后端接口
                type: 'GET',  // 或 'POST'，根据后端的要求
                async: false,
                success: function (response) {
                    const classSelect = $('#classSelect');
                    classSelect.empty();
                    classSelect.append('<option value="0">全校</option>');

                    response.forEach(function (classItem) {
                        classSelect.append(`<option value="${classItem.code}">${classItem.name}</option>`);
                    });
                },
                error: function (xhr, status, error) {
                    console.error('获取班级数据失败:', error);
                }
            });
            function setValue(val) {
                var selectElement = document.getElementById('classSelect');
                for (var i = 0; i < selectElement.options.length; i++) {
                    if (selectElement.options[i].value === val) {
                        selectElement.options[i].selected = true;
                        break;
                    }
                }
            }
            var val = $('#orgcode').val();
            if (val!=null){
                setValue(val);
            }
            var selectedClass = $('#classSelect').val();
            if (selectedClass === "" ||selectedClass==="0") {
                // 如果选择了空值，隐藏未签到区域，并让已签到区域占满空间
                $(".mainbox .boxall .alltitle:contains('未签到')").closest("li").hide();
                $(".mainbox .boxall .alltitle:contains('已签到')").closest("li").css("width", "100%"); // 让已签到区域占满
                $(".mainbox .boxall .alltitle:contains('已签到')").closest("li").css("height", "100%"); // 让已签到区域占满
            } else {
                $(".mainbox .boxall .alltitle:contains('未签到')").closest("li").show();
                $(".mainbox .boxall .alltitle:contains('已签到')").closest("li").css("width", "67%"); // 恢复原来的高度
                $(".mainbox .boxall .alltitle:contains('已签到')").closest("li").css("height", "100%"); // 恢复原来的高度
                $(".mainbox .boxall .alltitle:contains('未签到')").closest("li").css("width", "33%"); // 恢复原来的高度
                $(".mainbox .boxall .alltitle:contains('未签到')").closest("li").css("height", "100%"); // 恢复原来的高度
            }

            // 单选框选择班级后获取数据
            $("#classSelect").change(function () {
                var selectedClass = $(this).val();
                if (selectedClass === ""||selectedClass==="0") {
                    stopquerydataTimer();
                    startqueryAlldataTimer();
                    // 如果选择了空值，隐藏未签到区域，并让已签到区域占满空间
                    $(".mainbox .boxall .alltitle:contains('未签到')").closest("li").hide();
                    $(".mainbox .boxall .alltitle:contains('已签到')").closest("li").css("width", "100%"); // 让已签到区域占满
                    // 使用AJAX请求后端数据
                    $.ajax({
                        url: "/Dorm/dataview/getAllClassStudentlist", // 后端接口
                        type: "GET",
                        success: function (response) {
                            updateAttendance(response.data);
                            $(".mainbox .boxall .alltitle:contains('已签到')").text(response.data.name + " 已签到");
                        }
                    });
                } else {
                    stopqueryAlldataTimer();
                    startquerydataTimer();
                    $(".mainbox .boxall .alltitle:contains('未签到')").closest("li").show();
                    $(".mainbox .boxall .alltitle:contains('已签到')").closest("li").css("width", "67%"); // 恢复原来的高度
                    $(".mainbox .boxall .alltitle:contains('已签到')").closest("li").css("height", "100%"); // 恢复原来的高度
                    $(".mainbox .boxall .alltitle:contains('未签到')").closest("li").css("width", "33%"); // 恢复原来的高度
                    $(".mainbox .boxall .alltitle:contains('未签到')").closest("li").css("height", "100%"); // 恢复原来的高度
                    // 使用AJAX请求后端数据
                    $.ajax({
                        url: "/Dorm/dataview/getClassStudentlist", // 后端接口
                        type: "GET",
                        data: {orgcode: selectedClass},
                        success: function (response) {
                            updateAttendance(response.data);
                            $(".mainbox .boxall .alltitle:contains('已签到')").text("已签到");
                        }
                    });
                }
            });

            function querydata() {
                var selectedClass = $("#classSelect").val();
                $.ajax({
                    url: "/Dorm/dataview/getClassStudentlist", // 后端接口
                    type: "GET",
                    data: {orgcode: selectedClass},
                    success: function (response) {
                        updateAttendance(response.data);
                        $(".mainbox .boxall .alltitle:contains('已签到')").text("已签到");
                    }
                });
            }

            let timerId = null;

            function startquerydataTimer() {
                if (timerId !== null) {

                    return;  // 如果定时器已经开启，直接返回，不再启动新的定时器
                }

                // 启动定时器并将ID存储到timerId
                timerId = setInterval(querydata, 2000);  // 每秒输出一次
            }

            function stopquerydataTimer() {
                if (timerId !== null) {
                    clearInterval(timerId);  // 停止定时器
                    timerId = null;  // 清空定时器ID

                } else {

                }
            }

            function queryalldata() {
                $.ajax({
                    url: "/Dorm/dataview/getAllClassStudentlist", // 后端接口
                    type: "GET",
                    success: function (response) {
                        updateAttendance(response.data);
                        $(".mainbox .boxall .alltitle:contains('已签到')").text(response.data.name + " 已签到");
                    }
                });
            }

            let timerAllId = null;

            function startqueryAlldataTimer() {
                if (timerAllId !== null) {

                    return;  // 如果定时器已经开启，直接返回，不再启动新的定时器
                }

                // 启动定时器并将ID存储到timerId
                timerAllId = setInterval(queryalldata, 10000);  // 每秒输出一次
            }

            function stopqueryAlldataTimer() {
                if (timerAllId !== null) {
                    clearInterval(timerAllId);  // 停止定时器
                    timerAllId = null;  // 清空定时器ID

                } else {

                }
            }


            var selectedClass = $('#classSelect').val();
            if (selectedClass!='0'){
                stopqueryAlldataTimer();
                startquerydataTimer();
                $(".mainbox .boxall .alltitle:contains('未签到')").closest("li").show();
                $(".mainbox .boxall .alltitle:contains('已签到')").closest("li").css("width", "67%"); // 恢复原来的高度
                // 使用AJAX请求后端数据
                $.ajax({
                    url: "/Dorm/dataview/getClassStudentlist", // 后端接口
                    type: "GET",
                    data: {orgcode: selectedClass},
                    success: function (response) {
                        updateAttendance(response.data);
                        $(".mainbox .boxall .alltitle:contains('已签到')").text("已签到");
                    }
                });
            }else {
                queryalldata();
                startqueryAlldataTimer();
            }


            // 更新已签到和未签到的数据
            function updateAttendance(data) {
                var attendedHtml = '';
                var notAttendedHtml = '';
                var statusColor = '';

                // 已签到人员
                data.attended.forEach(function (person) {
                    // 根据状态设置样式
                    statusColor = person.status === '已离校' ? 'style="color: green;"' : '';
                    attendedHtml += `
                <div class="boxstudentsign">
                    <div class="boxitem">
                        <span class="boxfont1">学号：</span>
                        <span class="boxcode">${person.code}</span>
                    </div>
                    <div class="boxitem"">
                        <span class="boxfont1">姓名：</span>
                        <span class="boxfont2">${person.name}</span>
                    </div>
                    <div class="boxitem"">
                        <span class="boxfont1">状态：</span>
                        <span class="boxfont2" ${statusColor}>${person.status}</span>
                    </div>
                </div>
            `;
                });

                // 未签到人员
                data.notAttended.forEach(function (person) {
                    statusColor = person.status === '已离校' ? 'style="color: green;"' : '';
                    notAttendedHtml += `
                <div class="boxstudentnosign" ">
                    <div class="boxitem"">
                        <span class="boxfont1">学号：</span>
                        <span class="boxcode">${person.code}</span>
                    </div>
                    <div class="boxitem"">
                        <span class="boxfont1">姓名：</span>
                        <span class="boxfont2">${person.name}</span>
                    </div>
                    <div class="boxitem"">
                        <span class="boxfont1">状态：</span>
                        <span class="boxfont2" ${statusColor}>${person.status}</span>
                    </div>
                </div>
            `;
                });

                // 更新页面内容
                $(".mainbox .boxall .alltitle:contains('已签到')").next().html(attendedHtml);
                $(".mainbox .boxall .alltitle:contains('未签到')").next().html(notAttendedHtml);
            }

            let isPaused = false;  // 用于标记是否正在等待
            let isAtTop = false;   // 用于标记是否回到顶部

            const scroll = () => {
                // 获取滚动区域的DOM
                let parentDom = document.getElementById("yiqiandao");

                // 判断是否有滚动条
                if (parentDom.scrollHeight <= parentDom.clientHeight) return;

                // 如果滚动到底部且没有暂停
                if (
                    parentDom.scrollTop + parentDom.clientHeight >=
                    parentDom.scrollHeight - 1.5
                ) {
                    if (!isPaused) {
                        // 标记为暂停状态
                        isPaused = true;

                        // 停止当前的滚动
                        clearInterval(timer);

                        // 停止3秒后将滚动条归零并等待2秒
                        setTimeout(() => {
                            parentDom.scrollTop = 0;  // 滚动回顶部
                            isAtTop = true;  // 标记为回到顶部状态

                            // 等待2秒后继续滚动
                            setTimeout(() => {
                                // 恢复滚动
                                timer = setInterval(scroll, 50);
                                isPaused = false;  // 解除暂停状态
                                isAtTop = false;    // 解除回到顶部标记
                            }, 2000); // 等待2秒
                        }, 2000); // 等待3秒
                    }
                } else if (!isAtTop) {
                    // 如果没有到达底部，并且没有处于顶部，继续滚动
                    parentDom.scrollTop += 4;
                }
            };

// 启动滚动定时器
            let timer = setInterval(scroll, 50);



            let isPaused1 = false;  // 用于标记是否正在等待
            let isAtTop1 = false;   // 用于标记是否回到顶部

            const scroll1 = () => {
                // 获取滚动区域的DOM
                let parentDom = document.getElementById("weiqiandao");

                // 判断是否有滚动条
                if (parentDom.scrollHeight <= parentDom.clientHeight) return;

                // 如果滚动到底部且没有暂停
                if (
                    parentDom.scrollTop + parentDom.clientHeight >=
                    parentDom.scrollHeight - 1.5
                ) {
                    if (!isPaused1) {
                        // 标记为暂停状态
                        isPaused1 = true;

                        // 停止当前的滚动
                        clearInterval(timer1);

                        // 停止3秒后将滚动条归零并等待2秒
                        setTimeout(() => {
                            parentDom.scrollTop = 0;  // 滚动回顶部
                            isAtTop1 = true;  // 标记为回到顶部状态

                            // 等待2秒后继续滚动
                            setTimeout(() => {
                                // 恢复滚动
                                timer1 = setInterval(scroll1, 50);
                                isPaused1 = false;  // 解除暂停状态
                                isAtTop1 = false;    // 解除回到顶部标记
                            }, 2000); // 等待2秒
                        }, 2000); // 等待3秒
                    }
                } else if (!isAtTop1) {
                    // 如果没有到达底部，并且没有处于顶部，继续滚动
                    parentDom.scrollTop += 4;
                }
            };

// 启动滚动定时器
            let timer1 = setInterval(scroll1, 50);
        });
    </script>
</div>
<div class="mainbox">
    <ul class="clearfix">
        <li>
            <div class="boxall" style="height: auto; min-height: 9.9rem;max-height: 9.9rem;">
                <div class="alltitle">已签到</div>
                <div id="yiqiandao" style="display: flex; flex-wrap :wrap; justify-content: flex-start;align-items: center;max-height: 9.2rem;overflow-y: auto">
                </div>
                <div class="boxfoot"></div>
            </div>
        </li>
        <li>
            <div class="boxall" style="height:auto; min-height: 9.9rem; max-height: 9.9rem;">
                <div class="alltitle">未签到</div>
                <div id="weiqiandao" style="display: flex; flex-wrap :wrap; justify-content: flex-start;align-items: center; max-height: 9.2rem;overflow-y: auto">
                </div>
                <div class="boxfoot"></div>
            </div>
        </li>
    </ul>
</div>
<div class="back"></div>
</body>
</html>
