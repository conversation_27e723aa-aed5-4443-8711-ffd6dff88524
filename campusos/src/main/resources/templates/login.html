<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
  <title th:text="${title}"></title>
</head>
<link rel="stylesheet" type="text/css" href="/resources/style/main.css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.0.0/crypto-js.min.js"></script>
<style lang="scss" scoped>
  .bg-img{
    width: 450px;
    height: 450px;
    margin-left: 20px;
  }
  .bg-img img{
    width: 100%;
    height: 100%;
  }
  .login {
    /* background: #2d3a4b; */
    height: 100vh;
    background-image: linear-gradient(to bottom right, #5580a2, #3a628f);
  }
  .login-box {
    display: flex;
    align-items: center;
    width: 900px;
    height: 500px;
    background: white;
    padding:0 30px;
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 1;
    border-radius:20px;
    box-shadow:4px 3px 5px #4c4c4c;
    transform: translate(-50%, -50%);
    /* background-color: #1870b530; */
    /* box-shadow: -10px 0 10px rgba(0, 0, 0, 0.1), 10px 0 10px rgba(0, 0, 0, 0.1), 0 -10px 10px rgba(0, 0, 0, 0.1),  0 10px 10px rgba(0, 0, 0, 0.1); */
  }
  .title {
    display: flex;
    justify-content:center;
    align-items: center;
    font-size: 25px;
    font-weight: 500;
    margin-top: 40px;
    margin-bottom: 50px;
    font-weight: 600;
  }
  .title .bg-left{
    margin-right: 10px;
    width: 70px;
    height: 2px;
    background-image: linear-gradient(to bottom right, #ffffff, #878787);
  }
  .title .bg-right{
    margin-left: 15px;
    width: 70px;
    height: 2px;
    background-image: linear-gradient(to bottom left, #ffffff, #878787);
  }
  .login-logo {
    width: 150px;
    height: 150px;
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .login-logo img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
  .login-from {
  // position: absolute;
    bottom: 0;
    width: 450px;
    padding: 0 15px;
    box-sizing: border-box;
    margin-left: 40px;
    height: 400px;

  }
  .btns {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
  .ant-input {
    height: 50px !important;
  }

  .body :global(.ant-input) {
    height: 50px !important;
  }

  .ant-input {
    height: 40px !important;
    border-radius: 14px;
    padding-left: 40px !important;
    font-size: 17px;

  }
  .anticon{
    font-size:20px;

  }
  .ant-form-item-control{
    width: 100%;
  }
  /deep/ .ant-btn-primary{
    width: 100%;
    border-radius: 14px;
    height: 40px;
  }
  .ant-form-item-control-wrapper{
    width: 100%;
  }
  .input-pas {
    width: 70%;
    height: 40px !important;
    border-radius: 14px;
    padding-left: 40px !important;
    font-size: 14px;
    display: flex;
    justify-content: center;
    padding-left: 30px; /* 调整图标和输入框之间的间距 */
    background-image: url(resources/login/acc1.png); /* 替换为您的图标路径 */
    background-position: 10px center; /* 调整图标在输入框中的位置 */
    background-image-color: #0f0f0f;
    background-repeat: no-repeat;
    position: relative;
    background-color: #f0f0f0; /* 替换为您想要的背景颜色 */
    border: 1px solid #838181; /* 设置边框为白色 */
  }
  .input-pas:focus {
    border-color: #3ae1d3; /* 设置选中时的边框颜色为蓝色 */
    outline: none; /* 移除默认的选中样式 */
  }
  .input-pas::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 30px; /* 图标的宽度 */
    background-color: #f0f0f0; /* 替换为您想要的背景颜色 */
  }
    .input-pass {
    width: 70%;
    height: 40px !important;
    border-radius: 14px;
    padding-left: 40px !important;
    font-size: 14px;
    display: flex;
    justify-content: center;
    padding-left: 30px; /* 调整图标和输入框之间的间距 */
    background-image: url('resources/login/pwd1.png'); /* 替换为您的图标路径 */
    background-position: 10px center; /* 调整图标在输入框中的位置 */
    background-image-color: #0f0f0f;
    background-repeat: no-repeat;
    position: relative;
    background-color: #f0f0f0; /* 替换为您想要的背景颜色 */
    border: 1px solid #838181; /* 设置边框为白色 */
  }
  .input-pass:focus {
    border-color: #3ae1d3; /* 设置选中时的边框颜色为蓝色 */
    outline: none; /* 移除默认的选中样式 */
  }
  .input-pass::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 30px; /* 图标的宽度 */
    background-color: #f0f0f0; /* 替换为您想要的背景颜色 */
  }


  .btns {
    /*display: flex;*/
    justify-content: center;
    margin-top: 20px;
    width: 100%;
  }
  .btnsloginsubmittip {
    display: flex;
    height: 20px;
    justify-content: center;
    margin-top: 20px;
    width: 100%;
  }

  .button {
    display: inline-block;
    width: 300px; /* 设置按钮宽度为200px */
    padding: 10px 15px; /* 保持原有的内边距 */
    font-size: 18px;
    text-align: center;
    text-decoration: none;
    color: #fff;
    background-color: #1890ff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
  }

  .button:hover {
    background-color: #077eec;
  }

</style>
<script>
</script>
<body>
<div class="login">
  <div class="login-box">
    <div class="bg-img">
      <img src="resources/login/collaboration.png" alt="">
    </div>
    <div class="login-from">
      <div class="title">
        <div class="bg-left"></div>欢迎登录
        <div class="bg-right"></div>
      </div>
      <form id="f">
        <div class="btns">
          <!--<span class="u_user"></span>-->
          <input class="input-pas" id="logname" autocomplete="off" placeholder="请输入账号">
        </div>
        <div class="btns">
          <!--<span class="us_uer"></span>-->
          <input class="input-pass" id="logpass" autocomplete="off" placeholder="请输入密码" type="password">
        </div>
        <div class="btnsloginsubmittip">
          <div style="font-size: 12px;color: red;" id="loginsubmittip"></div>
        </div>
        <div class="btns">
          <button class="button" id="loginsubmit">
            登录
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

</body>
<script src="/resources/js/jquery-3.2.1.min.js"></script>
<script src="/resources/login/login.js"></script>
</html>