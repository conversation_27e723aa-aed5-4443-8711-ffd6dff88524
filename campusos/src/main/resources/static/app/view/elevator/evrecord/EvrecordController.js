Ext.define('CamPus.view.elevator.evrecord.EvrecordController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.EvrecordController',
    view: ['EvrecordView'],
    init: function () {
        var me = this;
        me.setControl({

        });

        me.store = me.view.store;
        me.grid = me.view.grid;

        me.store.on('beforeload', function (store) {
            var starttime = me.view.down('datetimefield[itemId=starttime]');
            var endtime = me.view.down('datetimefield[itemId=endtime]');
            Ext.apply(store.proxy.extraParams, {
                starttime: Ext.Date.format(starttime.getValue(), 'Y-m-d H:i:s'),
                endtime: Ext.Date.format(endtime.getValue(), 'Y-m-d H:i:s')
            });
        });
    }
});