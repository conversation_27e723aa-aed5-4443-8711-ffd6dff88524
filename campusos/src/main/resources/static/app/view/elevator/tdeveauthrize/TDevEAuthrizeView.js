Ext.define('CamPus.view.elevator.tdeveauthrize.TDevEAuthrizeView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.TDevEAuthrizeView",
    controller: 'TDevEAuthrizeController',
    requires: [
        'CamPus.view.elevator.tdeveauthrize.TDevEAuthrizeStore',
        'CamPus.view.elevator.tdeveauthrize.TDevEAuthrizeController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.devstore = Ext.create('CamPus.view.elevator.tdeveauthrize.TEdevStore', {});

        var devtbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 160,
                hideLabel: true,
                flex: 1,
                paramName: 'key',
                store: me.devstore,
                emptyText: '控制器、机号关键词...'
            }]
        });

        me.devlist = Ext.create('Ext.grid.Panel', {
            //title: '梯控控制器',
            itemId: 'edevlist',
            region: 'west',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            rowLines: true,
            columnLines: true,
            width: 300,
            store: me.devstore,
            tbar: devtbar,
            viewConfig: {
                enableTextSelection: true
            },
            selModel: 'checkboxmodel',
            columns: [
                {xtype: 'rownumberer'},
                {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 60,
                    sortable: true
                },
                {
                    text: '名称',
                    dataIndex: 'devname',
                    minWidth: 150,
                    flex: 1,
                    sortable: true,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        var color = '';
                        if (record.get('devstatus')) {
                            color = '#7bc309';
                        } else {
                            color = 'red';
                        }
                        return '<span style="margin-right:10px;background-color:' + color + ';display:inline-block;border-radius: 50%;width:10px;height:10px;"></span>' + value;
                    }
                }
            ]
        });

        me.deveauthrizestore = Ext.create('CamPus.view.elevator.tdeveauthrize.TDevEAuthrizeStore', {});

        var deveauthrizetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'status',
                groupCode: 'SYS0000025',
                insertAll: '-2|全部',
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.deveauthrizestore.load();
                    }
                }
            }, {
                xtype: 'combobox',
                itemId: 'isvisitor',
                queryMode: 'local',
                displayField: 'name',
                valueField: 'value',
                value: -1,
                width: 110,
                editable: false,
                store: Ext.create('Ext.data.Store', {
                    fields: ['value', 'name'],
                    data: [
                        {"value": -1, "name": "所有人员"},
                        {"value": 0, "name": "职工"},
                        {"value": 1, "name": "访客"}
                    ]
                }),
                listeners: {
                    change: function (combox) {
                        me.deveauthrizestore.reload();
                    }
                }
            }, {
                xtype: 'searchfield',
                itemId: 'searchKey',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.deveauthrizestore,
                emptyText: '姓名、卡号关键词...'
            }, '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.deveauthrizestore.reload();
                    }
                }
            ]
        });

        Ext.each(config.opbutton, function (item, i) {
            deveauthrizetbar.addMaxItems(item, 2);
        });

        me.deveauthrizelist = Ext.create('Ext.grid.Panel', {
            region: 'center',
            itemId: 'deveauthrizelist',
            collapsible: false,
            style: 'border-top:solid 1px #ccc;',
            multiColumnSort: true,
            border: false,
            width: 400,
            columnLines: true,
            selModel: 'checkboxmodel',
            store: me.deveauthrizestore,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {xtype: 'rownumberer'},
                {
                    text: '下载状态',
                    dataIndex: 'statusname',
                    width: 80,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record.get('status') == -1) {
                            metaData.style += 'color:#7bc309;';
                        } else if (record.get('status') == 0) {
                            metaData.style += 'color:#5fa2dd;';
                        } else if (record.get('status') == 1) {
                            metaData.style += 'color:#7bc309;';
                        } else if (record.get('status') == 2) {
                            metaData.style += 'color:red;';
                        } else if (record.get('status') == 3) {
                            metaData.style += 'color:red;';
                        } else if (record.get('status') == 4) {
                            metaData.style += 'color:red;';
                        } else if (record.get('status') == 5) {
                            metaData.style += 'color:#5fa2dd;';
                        }
                        return value;
                    }
                },
                {
                    text: '姓名',
                    dataIndex: 'infoname',
                    width: 100,
                    sortable: true
                },
                {
                    text: '卡号',
                    dataIndex: 'cardno',
                    width: 120,
                    sortable: false
                },
                {
                    text: '身份',
                    dataIndex: 'isvisitor',
                    width: 80,
                    sortable: false,
                    renderer: function (value) {
                        if (value == 0) {
                            return '职工';
                        } else if (value == 1) {
                            return '访客';
                        }
                    }
                },
                {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 60,
                    sortable: true
                },
                {
                    text: '授权开始时间',
                    dataIndex: 'starttime',
                    width: 140,
                    sortable: false
                },
                {
                    text: '授权结束时间',
                    dataIndex: 'endtime',
                    width: 140,
                    sortable: false
                },
                {
                    text: '权限',
                    dataIndex: 'permissions',
                    width: 110,
                    sortable: false,
                    renderer: function (value) {
                        if (value == 0) {
                            return '普通权限';
                        } else if (value == 1) {
                            return '管理员权限';
                        }
                    }
                },
                {
                    text: '处理时间',
                    dataIndex: 'downtime',
                    width: 110,
                    sortable: false
                },
                {
                    text: '处理次数',
                    dataIndex: 'downnum',
                    width: 110,
                    sortable: false
                },
                {
                    text: '处理结果',
                    dataIndex: 'downmsg',
                    minWidth: 80,
                    flex: 1,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                        if (value) {
                            metaData.tdAttr = 'data-qtip="' + value + '"';
                        }
                        return value;
                    }
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.storeystore = Ext.create('CamPus.view.elevator.tdeveauthrize.StoreyStore', {
            autoLoad: false,
            listeners: {
                load: function (store, records, successful, operation, eOpts) {
                    Ext.each(records, function (item, i) {
                        var icosrc = 'resources/images/floor.png';
                        item.set('icosrc', icosrc);
                    });
                },
                beforeload: function (store) {
                    var selection = me.deveauthrizelist.getSelection();
                    if (selection.length == 0) {
                        return false;
                    }
                    var storeys = selection[0].get('storeys');
                    if (!storeys) {
                        return false;
                    }
                    var storeylevel = [];
                    Ext.each(storeys.split(','), function (item, i) {
                        if (item == 1) {
                            storeylevel.push(i + 1);
                        }
                    });
                    Ext.apply(store.proxy.extraParams, {
                        devid: selection[0].get('devid'),
                        storeylevel: storeylevel.join(',')
                    });
                }
            }
        });


        me.storeygrid = Ext.create('CamPus.view.ux.IcoView', {
            itemId: 'StoreyView',
            region: 'north',
            collapsible: false,
            store: me.storeystore,
            style: 'border-top:solid 1px #ccc;',
            tpl: [
                '<div class="icoviewcls">', '<tpl for=".">',
                '<div class="img-thumb-wrap" style="width:60px;height:60px;">', '<div class="imgThumb">',
                '<img src="{icosrc}" ondragstart="return false;" style="width:65%;height:65%;"/>',
                '</div>', '<span>{name}</span>', '</div>', '</tpl>', '</div>'
            ]
        });

        me.timestore = Ext.create('CamPus.view.elevator.tdeveauthrize.AuthrizeTimeStore', {
            autoLoad: false,
            listeners: {
                beforeload: function (store) {
                    var authrizeuid = '';
                    if (me.deveauthrizelist.getSelection().length > 0) {
                        var record = me.deveauthrizelist.getSelection()[0];
                        authrizeuid = record.get('uid');
                    }
                    Ext.apply(store.proxy.extraParams, {
                        authrizeuid: authrizeuid
                    });
                }
            }
        });

        me.timelist = Ext.create('Ext.grid.Panel', {
            itemId: 'timelist',
            multiColumnSort: true,
            border: false,
            collapsible: false,
            columnLines: true,
            style: 'border-top:solid 1px #ccc;',
            region: 'center',
            store: me.timestore,
            viewConfig: {
                enableTextSelection: true
            },
            flex: 1,
            columns: [
                {
                    text: '星期',
                    dataIndex: 'weekday',
                    width: 50,
                    sortable: false
                },
                {
                    text: '时段1',
                    dataIndex: 'time1',
                    width: 150,
                    sortable: false,
                },{
                    text: '时段2',
                    dataIndex: 'time2',
                    width: 150,
                    sortable: false,
                },{
                    text: '时段3',
                    dataIndex: 'time3',
                    width: 150,
                    sortable: false,
                },{
                    text: '时段4',
                    dataIndex: 'time4',
                    width: 150,
                    sortable: false,
                },{
                    text: '时段5',
                    dataIndex: 'time5',
                    width: 150,
                    sortable: false,
                },{
                    text: '时段6',
                    dataIndex: 'time6',
                    width: 150,
                    sortable: false,
                },
                {
                    text: '有效日期',
                    dataIndex: 'endtime',
                    width: 150,
                    sortable: false
                }
            ]
        });

        Ext.apply(config, {
            items: [me.devlist, {
                //title: '授权详情',
                xtype: 'panel',
                region: 'center',
                tbar: deveauthrizetbar,
                layout: {
                    type: 'border',
                    pack: 'start',
                    align: 'stretch'
                },
                collapsible: false,
                defaults: {
                    collapsible: true,
                    split: true
                },
                border: false,
                items: [
                    me.deveauthrizelist,
                    {
                        xtype: 'panel',
                        region: 'east',
                        border: false,
                        width: 400,
                        minWidth: 400,
                        layout: {
                            type: 'border',
                            pack: 'start',
                            align: 'stretch'
                        },
                        collapsible: false,
                        defaults: {
                            collapsible: true,
                            split: true
                        },
                        items: [{
                            flex: 1,
                            border: false,
                            xtype: 'panel',
                            region: 'north',
                            collapsible: false,
                            layout: {
                                type: 'fit',
                                pack: 'start',
                                align: 'stretch'
                            },
                            items: [me.storeygrid]
                        }, me.timelist]
                    }
                ]
            }]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.elevator.tdeveauthrize.ImportPersonWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '导入人员信息',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    height: 360,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                },{
                    xtype: 'button',
                    text: '下载导入模板',
                    handler: function() {
                        Ext.Ajax.request({
                            url: '/Card/TeachStudInfo/ExportTemplate',
                            params: {
                                name: '人员信息导入模板'
                            },
                            method: 'POST',
                            success: function(response) {
                                Ext.get('loadingToast').hide();
                                var result = Ext.JSON.decode(response.responseText);
                                if (result.success) {
                                    if (result.msg) {
                                        openWindow(result.msg, null, 'get');
                                    } else {
                                        toast('无导出数据');
                                    }
                                } else {
                                    toast(result.msg);
                                }
                            }
                        });
                    }
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中应至少包含工号、姓名；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Elevator/TEAuthrize/UploadFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('上传成功，请设置数据对应列！');
                        var nextwin = Ext.create('CamPus.view.card.teacher.SetColumnWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.card.teacher.SetColumnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置导入对应列',
    width: 550,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('Ext.data.Store', {
            fields: ['column', 'value', 'columnname', 'columncode'],
            data: config.data
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            border: false,
            flex: 1,
            store: me.store,
            columns: [{
                text: '列序号',
                dataIndex: 'column',
                width: 65
            },
                {
                    text: '列名',
                    dataIndex: 'value',
                    flex: 1
                },
                {
                    text: '对应列名',
                    dataIndex: 'columncode',
                    flex: 1,
                    sortable: false,
                    editor: {
                        xtype: 'combobox',
                        queryMode: 'local',
                        displayField: 'name',
                        valueField: 'value',
                        editable: false,
                        store: Ext.create('Ext.data.Store', {
                            fields: ['name', 'value'],
                            data: [
                                { name: "忽略", value: '' },
                                { name: "工号", value: 'code' },
                                { name: "姓名", value: 'name' }
                            ]
                        }),
                        listeners: {
                            change: function (combox, newValue, oldValue, eOpts) {
                                if (newValue) {
                                    var records = me.grid.getStore().getData().items;
                                    var exists = false;
                                    Ext.each(records, function (record, i) {
                                        if (newValue && record.get('columncode') && record.get('columncode') == newValue) {
                                            exists = true;
                                        }
                                    });
                                    if (!exists) {
                                        combox.ownerCt.context.record.set('columncode', newValue);
                                        combox.ownerCt.context.record.set('columnname', combox.getRawValue());
                                    } else {
                                        toast('禁止多列对应一列');
                                        return false;
                                    }
                                } else {
                                    combox.ownerCt.context.record.set('columncode', '');
                                    combox.ownerCt.context.record.set('columnname', '');
                                }
                            }
                        }
                    },
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record) {
                            return record.get('columnname');
                        }
                    }
                }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'newlistplugin'
            }
        });

        Ext.apply(me, {
            fbar: ['->',
                {
                    xtype: 'button',
                    text: '确定',
                    listeners: {
                        click: function (button, event) {
                            me.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    listeners: {
                        click: function (button, event) {
                            me.close();
                        }
                    }
                }
            ],
            items: [me.grid, {
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                hidden: true,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    itemId: 'excelpath',
                    name: 'excelpath',
                    value: config.excelpath,
                    allowBlank: false
                }, {
                    itemId: 'columncfg',
                    name: 'columncfg',
                    allowBlank: false
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.grid.getStore().getData().items;
        var columncfg = [];
        Ext.each(records, function (record, i) {
            if (record.get('columncode')) {
                columncfg.push({
                    columncode: record.get('columncode'),
                    column: record.get('column')
                });
            }
        });
        if (columncfg.length == 0) {
            toast('请设置文档列对应关系');
            return;
        }
        var form = me.down('form[itemId=form]');
        form.down('textfield[itemId=columncfg]').setValue(JSON.stringify(columncfg));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Elevator/TEAuthrize/ImportInfoFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        // 假设 me 是当前作用域，maingrid 是一个 Grid 组件
                        var store = me.maingrid.getStore();

// 定义要传递的参数
                        var params = {
                            codes: action.result.msg
                        };

// 全局设置参数
                        store.getProxy().setExtraParams(params);
                        store.loadPage(1);
                        toast('数据导入成功！');
                        }

                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


Ext.define('CamPus.view.elevator.tdeveauthrize.EAuthorizeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择人员',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.elevator.tdeveauthrize.OrgTreeStore', {});

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get('orgname3') + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function (tree, record, index, eOpts) {
                    me.store.loadPage(1);
                }
            }
        });

        me.store = Ext.create('CamPus.view.elevator.tdeveauthrize.TechInfoStore', {
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');
                    var isvisitor = me.down('checkboxfield[itemId=isvisitor]');
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });

                    Ext.apply(store.proxy.extraParams, {
                        orgcode: orgcode,
                        intoyear: !intoyear.getValue() ? '0' : intoyear.getValue(),
                        infotype: infotype.getValue(),
                        viewchild: viewchild.getValue(),
                        isvisitor: isvisitor.getValue() ? '1' : '0',
                        selecteduid: JSON.stringify(selecteduid)
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'infotype',
                groupCode: 'SYS0000019',
                insertAll: '0|全部',
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                        var url = '/Card/OrgFramework/getCollegeClassTree';
                        if (newValue == '0') {
                            url = '/Card/OrgFramework/getAllOrgTree';
                        } else if (newValue == '2') {
                            intoyear.show();
                        } else {
                            intoyear.setValue(0);
                            intoyear.hide();
                            url = '/Card/OrgFramework/getOrgTree';
                        }
                        me.treestore.proxy.url = url;
                        me.treestore.load();
                        me.store.load();
                    }
                }
            }, {
                xtype: 'comboxdictionary',
                itemId: 'intoyear',
                groupCode: 'SYS0000004',
                insertAll: '全部',
                width: 110,
                defaultSelectFirst: true,
                hidden: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            }, {
                xtype: 'searchfield',
                width: 150,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '关键词...'
            }, {
                xtype: 'checkboxfield',
                boxLabel: '显示子节点人员',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }, {
                xtype: 'checkboxfield',
                boxLabel: '显示访客',
                inputValue: 0,
                itemId: 'isvisitor',
                checked: false,
                listeners: {
                    change: function (result, newValue) {
                        if (newValue == true) {
                            me.grid.getColumns()[2].hide();
                            me.grid.getColumns()[5].hide();
                            me.grid.getColumns()[6].show();
                        } else {
                            me.grid.getColumns()[2].show();
                            me.grid.getColumns()[5].show();
                            me.grid.getColumns()[6].hide();
                            me.grid.getColumns()[4].flex = 0;
                        }
                        me.store.reload();
                    }
                }
            }, '->', {
                xtype: 'button',
                text: '导入人员',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        var win = Ext.create('CamPus.view.elevator.tdeveauthrize.ImportPersonWindow', {
                            maingrid: me.grid
                        });
                        win.show();
                    }
                }
            }]
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                {xtype: 'rownumberer'},
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    itemId: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    itemId: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                        return value;
                    }
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    itemId: 'orgname',
                    flex: 1,
                    minWidth: 300,
                    sortable: false
                },
                {
                    text: '电话',
                    dataIndex: 'mobile',
                    itemId: 'mobile',
                    hidden: true,
                    flex: 1,
                    minWidth: 300,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            },
            selModel: 'checkboxmodel'
        });

        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选人员：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.store.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['card', 'cardsn', 'name', 'infotype', 'uid', 'code', 'orgname', 'orgcode'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                {xtype: 'rownumberer'},
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 220
        });

        Ext.apply(me, {
            items: [me.tree, me.grid, me.selectgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.selectgrid.getStore().getData().items.length == 0) {
            toast('请选择人员信息，选择以后拖拽至右侧表格');
            return;
        }
        var win = Ext.create('CamPus.view.elevator.tdeveauthrize.AuthorizeDevTimeWindow', {
            autoSize: true,
            data: me.selectgrid.getStore().getData().items,
            parentWin: me,
            deveauthrizelist: me.deveauthrizelist,
            devid: me.devid,
            storeys: me.storeys,
            devids: me.devids
        });
        win.show();
    }
});

Ext.define('CamPus.view.elevator.tdeveauthrize.AuthorizeDevTimeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择时段和楼层',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.devtimestore = Ext.create('CamPus.view.elevator.tdeveauthrize.DevTimeStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        devid: me.devid
                    });
                },
                load: function (store, records) {
                    if (me.weekUid && records && records.length > 0) {
                        var selectedRecords = [];
                        records.forEach(function(record) {
                            if (record.get('uid') === me.weekUid) {
                                selectedRecords.push(record);
                            }
                        });

                        if (selectedRecords.length > 0) {
                            me.devtimelist.getSelectionModel().select(selectedRecords);
                        }
                    }
                }
            }
        });

        me.devtimelist = Ext.create('Ext.grid.Panel', {
            region: 'west',
            itemId: 'devtimelist',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: false,
            width: 300,
            selModel: 'checkboxmodel',
            store: me.devtimestore,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {xtype: 'rownumberer'},
                {
                    text: '编号',
                    dataIndex: 'uid',
                    width: 100,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '周计划名字',
                    dataIndex: 'name',
                    width: 100,
                    sortable: false,
                    flex: 1
                }
            ]
        });


        me.storeystore = Ext.create('CamPus.view.elevator.tdeveauthrize.StoreyStore', {
            listeners: {
                load: function (store, records, successful, operation, eOpts) {
                    Ext.each(records, function (item, i) {
                        var icosrc = 'resources/images/floor.png';
                        item.set('icosrc', icosrc);
                    });
                    me.storeygrid.setCheckedRecord(config.storeys);
                },
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        devid: me.devid
                    });
                }
            }
        });

        var storeytbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 220,
                hideLabel: true,
                paramName: 'key',
                store: me.storeystore,
                emptyText: '楼层名称关键词...'
            }, '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.storeystore.reload();
                    }
                },
                {
                    xtype: 'button',
                    itemId: 'selectall',
                    iconCls: 'x-fa fa-check-square-o',
                    text: '全选/取消',
                    listeners: {
                        click: function () {
                            if (me.storeygrid.getChecked().length == 0) {
                                me.storeygrid.CheckAll();
                            } else {
                                me.storeygrid.DisCheckAll();
                            }
                        }
                    }
                }]
        });

        Ext.each(config.opbutton, function (item, i) {
            storeytbar.addMaxItems(item, config.maxopbutton);
        });

        me.storeygrid = Ext.create('CamPus.view.ux.IcoView', {
            itemId: 'StoreyView',
            collapsible: false,
            store: me.storeystore,
            flex: 1,
            style: 'border-top:solid 1px #ccc;',
            tpl: [
                '<div class="icoviewcls">',
                '<tpl for=".">',
                '<div class="img-thumb-wrap" style="width:90px;height:90px;">',
                '<div class="imgThumb">',
                '<span class="ckstatus{uid} nocheck"></span>',
                '<img src="{icosrc}" ondragstart="return false;" style="width:65%;height:65%;"/>',
                '</div>',
                '<span>{name}</span>',
                '</div>',
                '</tpl>',
                '</div>'
            ],
            listeners: {
                itemclick: function (view, record, item, index, e, eOpts) {
                    me.storeygrid.setChecked(record);
                }
            },
            CheckAll: function () {
                Ext.each(me.storeygrid.getStore().getData().items, function (item, xi) {
                    me.storeygrid.setChecked(item);
                });
            },
            DisCheckAll: function () {
                Ext.each(me.storeygrid.getStore().getData().items, function (item, xi) {
                    me.storeygrid.setChecked(item, true);
                });
            },
            setChecked: function (record, isdechecked) {
                var view = this;
                var viewid = me.storeygrid.id;
                var checkedstatus = Ext.select('#' + viewid + ' .ckstatus' + record.get('uid'));
                if (checkedstatus.elements && checkedstatus.elements.length > 0) {
                    var dm = checkedstatus.elements[0];
                    if (dm.getAttribute('class').indexOf('checked') == -1 && !isdechecked) {
                        dm.setAttribute('class', dm.getAttribute('class').replace(/ nocheck/g, ''));
                        dm.setAttribute('class', dm.getAttribute('class') + ' checked');
                    } else {
                        dm.setAttribute('class', dm.getAttribute('class').replace(/ checked/g, ''));
                        dm.setAttribute('class', dm.getAttribute('class') + ' nocheck');
                    }
                }
            },
            getChecked: function () {
                var view = this;
                var records = [];
                var viewid = me.storeygrid.id;
                Ext.each(Ext.select('#' + viewid + ' .checked').elements, function (dm, i) {
                    Ext.each(me.storeygrid.getStore().getData().items, function (item, xi) {
                        if (dm.getAttribute('class').indexOf(item.get('uid')) != -1) {
                            records.push(item);
                        }
                    });
                });
                return records;
            },
            setCheckedRecord: function (storeys) {
                var view = this;
                if (storeys) {
                    var storeysx = storeys.split(',');
                    Ext.each(me.storeygrid.getStore().getData().items, function (item, xi) {
                        if (storeysx[item.get('code') - 1] == '1') {
                            var checkedstatus = Ext.select('.ckstatus' + item.get('uid'));
                            if (checkedstatus.elements.length > 0) {
                                var dm = checkedstatus.elements[0];
                                dm.setAttribute('class', dm.getAttribute('class').replace(/ nocheck/g, ''));
                                dm.setAttribute('class', dm.getAttribute('class') + ' checked');
                            }
                        }
                    });
                }
            }
        });


        me.formpanel = Ext.create('Ext.panel.Panel', {
            region: 'east',
            collapsible: false,
            width: 300,
            layout: {
                type: 'vbox',
                pack: 'middle',
                align: 'stretch'
            },
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                layout: 'anchor',
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'fieldset',
                        title: '权限',
                        defaultType: 'textfield',
                        layout: {
                            type: 'vbox',
                            pack: 'middle',
                            align: 'stretch'
                        },
                        items: [{
                            xtype: 'fieldcontainer',
                            layout: 'vbox',
                            defaults: {
                                anchor: '100%',
                                labelWidth: 70,
                                labelAlign: 'right'
                            },
                            items: [{
                                xtype: 'combobox',
                                itemId: 'permissions',
                                queryMode: 'local',
                                displayField: 'name',
                                valueField: 'value',
                                fieldLabel: '权限选择',
                                allowBlank: false,
                                name: 'permissions',
                                value: 0,
                                width: 200,
                                editable: false,
                                store: Ext.create('Ext.data.Store', {
                                    fields: ['value', 'name'],
                                    data: [
                                        {"value": 0, "name": "普通权限"},
                                        {"value": 1, "name": "管理员权限"}
                                    ]
                                })
                            },]
                        }]
                    },
                    {
                        xtype: 'fieldset',
                        title: '有效期',
                        defaultType: 'textfield',
                        layout: {
                            type: 'vbox',
                            pack: 'middle',
                            align: 'stretch'
                        },
                        items: [{
                            xtype: 'fieldcontainer',
                            layout: 'vbox',
                            defaults: {
                                anchor: '100%',
                                labelWidth: 70,
                                labelAlign: 'right'
                            },
                            items: [{
                                xtype: 'datetimefield',
                                itemId: 'starttime',
                                name: 'starttime',
                                fieldLabel: '开始时间',
                                allowBlank: false,
                                value: new Date(Ext.Date.format(new Date(), 'Y-m-d H:i:s')),
                                format: 'Y-m-d H:i:s'
                            },
                                {
                                    xtype: 'datetimefield',
                                    itemId: 'endtime',
                                    name: 'endtime',
                                    fieldLabel: '结束时间',
                                    allowBlank: false,
                                    value: new Date(Ext.Date.format(new Date(), '2050-01-01 H:i:s')),
                                    format: 'Y-m-d H:i:s'
                                }]
                        }]
                    }
                ]
            }]
        });


        Ext.apply(me, {
            items: [me.devtimelist, {
                xtype: 'panel',
                region: 'center',
                scrollable: 'y',
                collapsible: false,
                tbar: storeytbar,
                items: [me.storeygrid]
            }, me.formpanel]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.devtimelist.getSelection().length == 0) {
            toast('请选择周计划...');
            return;
        }
        if (me.devtimelist.getSelection().length > 1) {
            toast('最多允许选择一个周计划...');
            return;
        }

        var devdata = [];

        Ext.each(me.devids, function (devid, i) {
            devdata.push({
                devid: devid,
                weekUid: me.devtimelist.getSelection()[0].get('uid'),
            });
        });
        var infoid = [];
        if (!me.infoid) {
            Ext.each(me.data, function (item, i) {
                infoid.push(item.get('uid'));
            });
        } else {
            infoid.push(me.infoid);
        }
        var form = me.down('form[itemId=form]');
        var starttime = form.down('datetimefield[itemId=starttime]').getValue();
        var endtime = form.down('datetimefield[itemId=endtime]').getValue();
        if (!starttime || !endtime) {
            toast('开始时间和结束时间不能为空...');
            return;
        }
        starttime = new Date(Date.parse(starttime));
        endtime = new Date(Date.parse(endtime));
        if (starttime > endtime) {
            toast('开始时间不能大于现结束时间');
            return;
        }
        var storyrecords = me.storeygrid.getChecked();
        var floor = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        Ext.each(storyrecords, function (item, i) {
            floor[item.get('code') - 1] = 1;
        });
        Ext.Ajax.request({
            url: '/Elevator/TEAuthrize/saveEAuthrize',
            params: {
                permissions: form.down('combobox[itemId=permissions]').getValue(),
                infoid: infoid.join(','),
                devdata: JSON.stringify(devdata),
                starttime: starttime,
                endtime: endtime,
                storeys: floor.join(','),
                status: me.status
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.deveauthrizelist.getStore().reload();
                    toast('保存成功...');
                    if (me.parentWin) {
                        me.parentWin.close();
                    }
                    me.close();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});