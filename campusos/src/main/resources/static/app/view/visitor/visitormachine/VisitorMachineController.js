Ext.define('CamPus.view.visitor.visitormachine.VisitorMachineController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.VisitorMachineController',
    view: ['VisitorMachineView'],
    init: function () {
        var me = this;
        me.setControl({
            'gridpanel[itemId=machinelist]': {
                select: me.onMachineSelect
            },
            '*[itemId=addmachine]': {
                click: me.addmachine
            },
            '*[itemId=editmachine]': {
                click: me.editmachine
            },
            '*[itemId=delmachine]': {
                click: me.delmachine
            },
            '*[itemId=setdevice]': {
                click: me.setdevice
            },
            '*[itemId=deldevice]': {
                click: me.deldevice
            },
            '*[itemId=addfloor]': {
                click: me.addfloor
            }

        });
        me.vmachineStore = me.view.vmachineStore;
        me.machinegrid = me.view.machinegrid;

        me.machineDeviceStore = me.view.machineDeviceStore;
        me.devicegrid = me.view.devicegrid;

        me.machineDeviceStore.on('beforeload', function (store) {
            var select = me.machinegrid.getSelection()[0];
            var position = '';
            if (select) {
                position = select.get('position');
            }
            Ext.apply(store.proxy.extraParams, {
                position: position
            });
        });

    },

    onMachineSelect: function () {
        var me = this;
        me.machineDeviceStore.reload();
    },
    addmachine: function () {
        var me = this;
        var win = Ext.create('CamPus.view.visitor.visitormachine.AddMachineWindow', {
            machinegrid: me.machinegrid
        });
        win.show();
    },
    editmachine: function () {
        var me = this;
        if (me.machinegrid.getSelection().length > 0) {
            if (me.machinegrid.getSelection().length > 1) {
                toast('请选择一台需修改的区域位置...');
                return;
            }
            var editmachineWin = Ext.create('CamPus.view.visitor.visitormachine.AddMachineWindow', {
                title: '修改区域位置',
                machinegrid: me.machinegrid
            });
            var form = editmachineWin.down('form[itemId=form]');
            form.down('textfield[itemId=position]').setReadOnly(true);
            form.loadRecord(me.machinegrid.getSelection()[0]);
            editmachineWin.show();
        } else {
            toast('请选择需修改的访客机...');
        }
    },
    delmachine: function () {
        var me = this;
        if (me.machinegrid.getSelection().length == 0) {
            toast('请选择要删除的访客机');
            return;
        }
        var uids = [];
        var position = [];
        Ext.each(me.machinegrid.getSelection(), function (item, index) {
            uids.push(item.get('uid'));
            position.push(item.get('position'));
        });
        Ext.Msg.show({
            title: '删除访客机',
            message: '确定删除访客机？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Visitor/VisitorMachine/delMachine',
                        params: {
                            uid: uids.join(','),
                            position: position.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                toast("删除成功！");
                                me.vmachineStore.reload();
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },

    setdevice: function () {
        var me = this;
        if (me.machinegrid.getSelection().length == 0) {
            toast('请选择访客机...');
            return;
        }

        if (me.machinegrid.getSelection().length > 1) {
            toast('一次只能选择一台访客机..');
            return;
        }

        var win = Ext.create('CamPus.view.visitor.visitormachine.DeviceWindow', {
            position: me.machinegrid.getSelection()[0].get('position'),
            devicegrid: me.devicegrid
        });
        win.show();
    },
    deldevice: function () {
        var me = this;
        if (me.devicegrid.getSelection().length == 0) {
            toast('请选择要删除的访客机');
            return;
        }
        var uids = [];
        Ext.each(me.devicegrid.getSelection(), function (item, index) {
            uids.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '删除设备',
            message: '确定删除设备？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Visitor/VisitorMachine/delDevice',
                        params: {
                            uid: uids.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                toast("删除成功！");
                                me.machineDeviceStore.reload();
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },

    addfloor: function () {
        var me = this;
        if (me.devicegrid.getSelection().length == 0) {
            toast('请选择要梯控控制器');
            return;
        }

        if (me.devicegrid.getSelection().length > 1) {
            toast('一次只能选择一台梯控控制器..');
            return;
        }

        var win = Ext.create('CamPus.view.visitor.visitormachine.AuthorizeDevTimeWindow', {
            autoSize: true,
            parentWin: me,
            deveauthrizelist: me.deveauthrizelist,
            position: me.machinegrid.getSelection()[0].get('position'),
            devid: me.devicegrid.getSelection()[0].get("devid"),
            storeys: me.storeys
        });
        win.show();
    },
});