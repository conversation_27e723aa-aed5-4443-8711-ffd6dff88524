Ext.define('CamPus.view.visitor.vsubscribe.VSubscribeView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.VSubscribeView",
    controller: 'VSubscribeController',
    requires: [
        'CamPus.view.visitor.vsubscribe.VSubscribeStore',
        'CamPus.view.visitor.vsubscribe.VSubscribeController'
    ],
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    constructor: function (config) {
        var me = this;

        me.store = Ext.create('CamPus.view.visitor.vsubscribe.VSubscribeStore', {
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'combobox',
                    itemId: 'auditstatus',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'value',
                    editable: false,
                    value: -1,
                    width: 100,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['value', 'name'],
                        data: [
                            { name: "全部", value: -1 },
                            { name: "待审", value: 0 },
                            { name: "审核通过", value: 1 },
                            { name: "不通过", value: 2 },
                            { name: "已取消", value: 3 }
                        ]
                    }),
                    listeners: {
                        change: function () {
                            me.store.loadPage(1);
                        }
                    }
                },
                {
                    xtype: 'datetimefield',
                    labelWidth: 20,
                    labelSeparator: '',
                    name: 'starttime',
                    itemId: 'starttime',
                    width: 180,
                    value: Ext.Date.add(new Date(Ext.Date.format(new Date(), 'Y-m-d 00:00:00')), Ext.Date.DAY, -7),
                    format: 'Y-m-d H:i:s',
                    listeners: {
                        change: function (field) {
                            me.store.loadPage(1);
                        }
                    }
                },
                {
                    xtype: 'datetimefield',
                    fieldLabel: '至',
                    labelWidth: 20,
                    labelSeparator: '',
                    width: 200,
                    name: 'endtime',
                    itemId: 'endtime',
                    value: new Date(Ext.Date.format(new Date(), 'Y-m-d 23:59:59')),
                    format: 'Y-m-d H:i:s',
                    listeners: {
                        change: function (field) {
                            me.store.loadPage(1);
                        }
                    }
                },
                {
                    xtype: 'searchfield',
                    itemId: 'searchKey',
                    width: 200,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.store,
                    emptyText: '姓名、身份证、电话、卡号关键词查询...'
                }, '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.store.reload();
                    }
                }
            ]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            itemId: 'vsubscribegrid',
            tbar: tbar,
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            selModel: 'checkboxmodel',
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {
                    xtype: 'rownumberer'
                },
                {
                    xtype: 'hiddenfield',
                    name: 'uid',
                    itemId: 'uid'
                },
                {
                    text: '审核',
                    dataIndex: 'auditstatus',
                    width: 85,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record.get('status') == 1) {
                            metaData.style = 'color:#7bc309';
                            return '审核通过';
                        } else if(record.get('status') == 0){
                            metaData.style = 'color:#5fa2dd';
                            return '待审';
                        }else if(record.get('status') == -1){
                            metaData.style = 'color:red';
                            return '不通过';
                        }else if(record.get('status') == 2){
                            metaData.style = 'color:red';
                            return '已取消';
                        }
                    }
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 100,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 50,
                    sortable: false
                },
                {
                    text: '电话',
                    dataIndex: 'mobile',
                    width: 100,
                    sortable: false
                },
                {
                    text: '单位',
                    dataIndex: 'company',
                    minWidth: 150,
                    flex: 1,
                    sortable: false
                },
                {
                    text: '部门',
                    dataIndex: 'department',
                    width: 85,
                    sortable: false
                },
                {
                    text: '职位',
                    dataIndex: 'position',
                    width: 80,
                    sortable: false
                },
                {
                    text: '证件类型',
                    dataIndex: 'idtype',
                    width: 80,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == '1') {
                            return '身份证';
                        } else if (value == '2') {
                            return '驾驶证';
                        } else if (value == '3') {
                            return '军官证';
                        } else if (value == '4') {
                            return '其他证件';
                        }
                    }
                },
                {
                    text: '证件号码',
                    dataIndex: 'idcard',
                    width: 150,
                    sortable: false
                },
                {
                    text: '车牌号码',
                    dataIndex: 'plateno',
                    width: 100,
                    sortable: false
                },
                {
                    text: '可通行区域',
                    dataIndex: 'areapositionname',
                    width: 150,
                    sortable: false
                },
                {
                    text: '被访人',
                    dataIndex: 'byvisitor',
                    width: 85,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'byvistororgname',
                    width: 100,
                    sortable: false
                },
                {
                    text: '同行人数',
                    dataIndex: 'personnum',
                    width: 85,
                    sortable: false
                },
                {
                    text: '事由',
                    dataIndex: 'reason',
                    width: 150,
                    sortable: false
                },
                {
                    text: '携带物品',
                    dataIndex: 'things',
                    width: 100,
                    sortable: false
                },
                {
                    text: '到访时间',
                    dataIndex: 'visittime',
                    width: 145,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '离开时间',
                    dataIndex: 'leavetime',
                    width: 145,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '预约时间',
                    dataIndex: 'createdate',
                    width: 145,
                    minWidth: 145,
                    flex: 1,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                }
            ],
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});



Ext.define('CamPus.view.visitor.vsubscribe.AuditSubscribeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '来访预约审核',
    height: 650,
    maximizable: false,
    constructor: function (config) {
        var me = this;

        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'hiddenfield',
                        name: 'uid',
                        itemId: 'uid'
                    },
                    {
                        xtype: 'hiddenfield',
                        name: 'visitorid'
                    },
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            labelAlign: 'right'
                        },
                        items: [
                            {
                                xtype: 'textfield',
                                fieldLabel: '姓名',
                                readOnly: true,
                                name: 'name'
                            },
                            {
                                xtype: 'textfield',
                                fieldLabel: '性别',
                                readOnly: true,
                                name: 'sex'
                            },
                            {
                                xtype: 'textfield',
                                fieldLabel: '手机号码',
                                readOnly: true,
                                name: 'mobile'
                            }
                        ]
                    },
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            labelAlign: 'right'
                        },
                        items: [
                            {
                                xtype: 'textfield',
                                fieldLabel: '证件类型',
                                readOnly: true,
                                name: 'idtypename'
                            },
                            {
                                xtype: 'textfield',
                                fieldLabel: '证件编号',
                                readOnly: true,
                                name: 'idcard'
                            },
                            {
                                xtype: 'textfield',
                                fieldLabel: '同行人数',
                                readOnly: true,
                                name: 'personnum'
                            }
                        ]
                    },
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            labelAlign: 'right'
                        },
                        items: [
                            {
                                xtype: 'textfield',
                                fieldLabel: '单位',
                                readOnly: true,
                                name: 'company'
                            },
                            {
                                xtype: 'textfield',
                                fieldLabel: '部门',
                                readOnly: true,
                                name: 'department'
                            },
                            {
                                xtype: 'textfield',
                                fieldLabel: '职位',
                                readOnly: true,
                                name: 'position',
                                itemId: 'position'
                            }
                        ]
                    },
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            readOnly: config.auditstatusint == 0 ? false : true,
                            labelAlign: 'right'
                        },
                        items: [
                            {
                                xtype: 'textfield',
                                fieldLabel: '来访车辆',
                                readOnly: true,
                                name: 'plateno',
                                itemId: 'plateno',
                                flex: 1
                            },
                            {
                                xtype: 'textfield',
                                fieldLabel: '被访人',
                                readOnly: true,
                                name: 'byvisitor'
                            },
                            {
                                xtype: 'hiddenfield',
                                itemId: 'byvisitorid',
                                name: 'byvisitorid'
                            },
                            {
                                xtype: 'textfield',
                                fieldLabel: '所在部门',
                                allowBlank: true,
                                readOnly: true,
                                name: 'byvistororgname'
                            }
                        ]
                    },
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            readOnly: config.auditstatusint==0?false:true,
                            labelAlign: 'right'
                        },
                        items: [
                            {
                                xtype: 'datetimefield',
                                fieldLabel: '来访时间',
                                name: 'visittimestr',
                                format: 'Y-m-d H:i:s',
                                allowBlank: false
                            },
                            {
                                xtype: 'datetimefield',
                                fieldLabel: '离开时间',
                                name: 'leavetimestr',
                                format: 'Y-m-d H:i:s',
                                allowBlank: false
                            }
                        ]
                    },
                    {
                        xtype: 'textfield',
                        fieldLabel: '来访事由',
                        readOnly: config.auditstatusint == 0 ? false : true,
                        allowBlank: true,
                        name: 'reason'
                    },
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            readOnly: config.auditstatusint==0?false:true,
                            labelAlign: 'right'
                        },
                        items: [
                            // {
                            //     xtype: 'textfield',
                            //     fieldLabel: '二维码',
                            //     allowBlank: true,
                            //     readOnly: true,
                            //     name: 'qrcode',
                            //     itemId: 'qrcode'
                            // },
                            {
                                xtype: 'textfield',
                                fieldLabel: '携带物品',
                                allowBlank: true,
                                name: 'things'
                            }
                        ]
                    },
                    {
                        xtype: 'textarea',
                        fieldLabel: '随访人员',
                        allowBlank: true,
                        readOnly: true,
                        height: 30,
                        scrollable: true,
                        submitValue: false,
                        itemId: 'children',
                        name: 'children',
                        hidden: true
                    },
                    {
                        xtype: 'fieldset',
                        title: '访客信息审核',
                        collapsible: true,
                        defaults: {
                            flex: 1,
                            labelAlign: 'right'
                        },
                        items: [
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaults: {
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [
                                    {
                                        xtype: 'combobox',
                                        fieldLabel: '审核意见',
                                        itemId: 'auditstatusint',
                                        name: 'auditstatusint',
                                        queryMode: 'local',
                                        displayField: 'name',
                                        valueField: 'value',
                                        editable: false,
                                        value: 1,
                                        store: Ext.create('Ext.data.Store', {
                                            fields: ['value', 'name'],
                                            data: [
                                                { name: "审核通过", value: 1 },
                                                { name: "不通过", value: -1 }
                                            ]
                                        })
                                    },
                                    {
                                        xtype: 'textfield',
                                        fieldLabel: '备注',
                                        allowBlank: true,
                                        name: 'remark'
                                    }
                                ]
                            },

                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [
                                    // {
                                    //     xtype: 'textfield',
                                    //     fieldLabel: '卡号',
                                    //     allowBlank: true,
                                    //     name: 'cardsn',
                                    //     itemId: 'cardsn'
                                    // },
                                    {
                                        xtype: 'comboxlist',
                                        fieldLabel: '可通行区域',
                                        itemId: 'areaposition',
                                        name: 'areaposition',
                                        table: 'tb_visitor_machine',
                                        where: '',
                                        fields: 'position as code,name',
                                        orderby: 'code asc',
                                        valueField: 'code',
                                        defaultSelectFirst: true
                                    }
                                ]
                            }]
                    },
                ]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            var auditstatusint = form.down('combobox[itemId=auditstatusint]').getValue();
            if (!auditstatusint) {
                toast("请选择审核类型。。。");
                return;
            }
            me.SubmitSave(form);
        }
    },
    SubmitSave: function (form) {
        var me = this;
        form.submit({
            url: '/Visitor/VisitorSubscribe/AuditSubscribe',
            timeout: 6000000,
            waitTitle: '系统信息',
            waitMsg: '提交数据中...',
            submitEmptyText: false,
            success: function (form, action) {
                if (action.result.success) {
                    me.grid.getStore().reload();
                    me.close();
                    toast('操作成功！！');
                } else {
                    toast(action.result.msg);
                }
            },
            failure: function (form, action) {
                toast(action.result.msg);
            }
        });
    }
});


/**
 * 访客预约登记模块
 * params
 */
Ext.define('CamPus.view.visitor.vsubscribe.GiveCardEditWindow', {
    extend: 'CamPus.view.ux.Window',
    //title: '卡片信息',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.cardlistgrid = Ext.create('Ext.grid.Panel', {
            title: '访客列表',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            margin: '0 0 10 0',
            store: Ext.create('Ext.data.Store', {
                proxy: {
                    type: 'ajax',
                    url: '/Visitor/VisitorSubscribe/getSubscribeVisitorSuccess',
                    actionMethods: {
                        read: 'POST'
                    },
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        totalProperty: 'total',
                        successProperty: 'success',
                        messageProperty: 'msg'
                    },
                    extraParams: { formId: '' }
                },
                autoLoad: false,
                listeners: {
                    beforeload: function (store, operation, eOpts) {
                        Ext.apply(store.proxy.extraParams, {
                            formId: me.down('hiddenfield[itemId=uid]').getValue()
                        });
                    }
                }
            }),
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                { text: '卡号', dataIndex: 'cardno', width: 110, sortable: false },
                { text: '持卡人', dataIndex: 'name', width: 100, sortable: false },
                { text: '身份证号', dataIndex: 'idcard', width: 100, sortable: false },
                { text: '二维码', dataIndex: 'qrcode', width: 100, sortable: false }
            ],
            selModel: 'rowmodel',
            height: 260
        });

        Ext.apply(me, {
            aotuAddNew: true,
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid',
                    allowBlank: false
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infotype',
                    name: 'infotype'
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'status',
                    name: 'status'
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'ismain',
                    name: 'ismain',
                    value: 1
                }, {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'textfield',
                        fieldLabel: '姓名',
                        itemId: 'infoname',
                        name: 'infoname',
                        readOnly: true,
                        allowBlank: false
                    }, {
                        xtype: 'comboxdictionary',
                        name: 'usedes',
                        groupCode: 'SYS0000005',
                        valueField: 'name',
                        allowBlank: false,
                        editable: true,
                        fieldLabel: '持卡人',
                        listeners: {
                            change: function (combox, newValue, oldValue, eOpts) {
                                if (combox.selection.get('code') == '0') {
                                    me.down('hiddenfield[itemId=ismain]').setValue(1);
                                } else {
                                    me.down('hiddenfield[itemId=ismain]').setValue(0);
                                }
                            }
                        }
                    }]
                },
                    me.cardlistgrid,
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaultType: 'textfield',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            labelAlign: 'right'
                        },
                        items: [{
                            fieldLabel: '卡号',
                            itemId: 'cardno',
                            name: 'cardno',
                            allowBlank: false
                        },
                            {
                                xtype: 'datefield',
                                fieldLabel: '有效期',
                                itemId: 'enddate',
                                name: 'enddate',
                                value: '2050-07-01',
                                format: 'Y-m-d'
                            },
                            {
                                xtype: 'numberfield',
                                fieldLabel: '制卡费',
                                name: 'deposit',
                                allowBlank: false,
                                value: 0,
                                minValue: 0
                            }
                        ]
                    },
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaultType: 'numberfield',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            labelAlign: 'right'
                        },
                        items: [{
                            fieldLabel: '开户金额',
                            name: 'balance',
                            allowBlank: false,
                            value: 0,
                            minValue: 0
                        },
                            {
                                fieldLabel: '支付方式',
                                xtype: 'comboxdictionary',
                                name: 'paytype',
                                groupCode: 'SYS0000007',
                                valueField: 'code',
                                allowBlank: false,
                                editable: false,
                                value: 3
                            },
                            {
                                fieldLabel: '付款渠道',
                                xtype: 'comboxdictionary',
                                name: 'payway',
                                groupCode: 'SYS0000008',
                                valueField: 'code',
                                allowBlank: false,
                                editable: false,
                                value: 3
                            }
                        ]
                    },
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaultType: 'textfield',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            labelAlign: 'right'
                        },
                        items: [
                            {
                                fieldLabel: '超额密码',
                                itemId: 'excesspwd',
                                name: 'excesspwd',
                                vtype: 'code6',
                                maxLength: 6,
                                minLength: 6,
                                emptyText: '6位纯数字字符串',
                                allowBlank: true
                            },
                            {
                                fieldLabel: '备注',
                                xtype: 'textfield',
                                name: 'des',
                                maxLength: 100,
                                flex: 2,
                                allowBlank: true
                            }
                        ]
                    }
                ]
            }]
        });
        me.callParent(arguments);
        me.InitHandle(0);
    },
    InitHandle: function (index) {
        if (index > this.handleData.length - 1) {
            toast('已选择的人员处理完毕...');
            this.close();
            return;
        }
        this.handleIndex = index;
        var record = this.handleData[this.handleIndex];
        this.down('hiddenfield[itemId=infoid]').setValue(record.get('uid'));
        this.down('hiddenfield[itemId=infotype]').setValue(record.get('infotype'));
        this.down('hiddenfield[itemId=status]').setValue(record.get('status'));
        this.down('textfield[itemId=infoname]').setValue(record.get('name'));
        this.down('textfield[itemId=cardno]').setValue('');
        this.down('progress[itemId=cardprogress]').setValue(this.handleIndex / this.handleData.length);
        var txt = Ext.String.format('已处理{0}人，待处理{1}人', this.handleIndex, this.handleData.length - this.handleIndex);
        this.down('progress[itemId=cardprogress]').setText(txt);
        this.cardlistgrid.getStore().loadPage(1);
    },
    SaveData: function (win) {
        var me = win;
        var form = win.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudCard/SaveCard',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.InitHandle(me.handleIndex + 1);
                        toast('保存成功！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            var ismain = form.down('hiddenfield[itemId=ismain]');
            var infoid = form.down('hiddenfield[itemId=infoid]');
            var CardNO = form.down('textfield[itemId=cardno]');
            Ext.Ajax.request({
                url: '/Card/TeachStudCard/ExistsCard',
                params: { cardno: CardNO.getValue(), ismain: ismain.getValue(), infoid: infoid.getValue() },
                method: 'POST',
                success: function (response) {
                    var result = Ext.JSON.decode(response.responseText);
                    if (!result.success) {
                        me.SaveData(me);
                    } else {
                        toast(result.msg);
                    }
                }
            });
        }
    },
    readCard: function () {
        var me = this;
        window.onReadCard(function (identid, CardNO) {
            var form = me.down('form[itemId=form]');
            form.down('textfield[itemId=cardno]').setValue(CardNO);
        });
    },
    nextCard: function () {
        var me = this;
        me.InitHandle(me.handleIndex + 1);
    }
});

/**
 * 访客预约发卡
 * params
 */
Ext.define('CamPus.view.visitor.vsubscribe.AddVSubscribeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '登记预约',
    maximizable: false,
    height: 650,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.formpanel = Ext.create('Ext.panel.Panel', {
            layout: {
                type: 'hbox',
                pack: 'start',
                align: 'stretch'
            },
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                flex: 1,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'fieldset',
                        title: '访客信息',
                        collapsible: true,
                        defaults: {
                            flex: 1,
                            labelAlign: 'right'
                        },
                        items: [
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaults: {
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [
                                    {
                                        xtype: 'hiddenfield',
                                        name: 'visitorid',
                                        itemId: 'visitorid'
                                    },
                                    {
                                        xtype: 'textfield',
                                        fieldLabel: '访客姓名',
                                        allowBlank: false,
                                        name: 'name',
                                        itemId: 'visitor',
                                        flex: 1
                                    }, {
                                        xtype: 'button',
                                        text: '选择访客',
                                        iconCls: 'x-fa fa-address-book',
                                        width: 130,
                                        listeners: {
                                            click: function (button, event) {
                                                var parentform = me.down('form[itemId=form]');
                                                var visitorid = parentform.down('hiddenfield[itemId=visitorid]');
                                                var visitorwin = Ext.create('CamPus.view.visitor.vsubscribe.SelectVisitor', {
                                                    callback: function (record) {
                                                        parentform.loadRecord(record);
                                                        visitorid.setValue(record.get('uid'));
                                                    }
                                                });
                                                visitorwin.show();
                                            }
                                        }
                                    }
                                ]
                            },

                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [
                                    {
                                        xtype: 'radiogroup',
                                        fieldLabel: '性别',
                                        itemId: 'sex',
                                        items: [
                                            { name: 'sex', boxLabel: '男', inputValue: '男', checked: true },
                                            { name: 'sex', boxLabel: '女', inputValue: '女' }
                                        ]
                                    },
                                    {
                                        xtype: 'combobox',
                                        fieldLabel: '证件类型',
                                        name: 'idtype',
                                        itemId: 'idtype',
                                        queryMode: 'local',
                                        displayField: 'name',
                                        valueField: 'value',
                                        editable: false,
                                        allowBlank: false,
                                        value: 0,
                                        store: Ext.create('Ext.data.Store', {
                                            fields: ['value', 'name'],
                                            data: [
                                                { name: "身份证", value: 1 },
                                                { name: "驾驶证", value: 2 },
                                                { name: "军官证", value: 3 },
                                                { name: "其他证件", value: 4 }
                                            ]
                                        })
                                    },
                                    {
                                        xtype: 'textfield',
                                        fieldLabel: '证件号码',
                                        name: 'idcard',
                                        itemId: 'idcard',
                                        allowBlank: false
                                    }
                                ]
                            },
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [{
                                    xtype: 'textfield',
                                    fieldLabel: '手机号码',
                                    allowBlank: false,
                                    name: 'mobile',
                                    itemId: 'mobile'
                                }, {
                                    xtype: 'textfield',
                                    fieldLabel: '卡号',
                                    allowBlank: true,
                                    name: 'cardsn',
                                    itemId: 'cardsn'
                                }, {
                                    xtype: 'textfield',
                                    fieldLabel: '二维码',
                                    name: 'qrcode',
                                    itemId: 'qrcode',
                                    readOnly: true,
                                    allowBlank: true
                                }]
                            },
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [{
                                    xtype: 'textfield',
                                    fieldLabel: '单位名称',
                                    allowBlank: true,
                                    name: 'company',
                                    itemId: 'company'
                                }, {
                                    xtype: 'textfield',
                                    fieldLabel: '部门',
                                    allowBlank: true,
                                    name: 'department',
                                    itemId: 'department'
                                }, {
                                    xtype: 'textfield',
                                    fieldLabel: '职位',
                                    allowBlank: true,
                                    name: 'position',
                                    itemId: 'position'
                                }]
                            }]
                    },
                    {
                        xtype: 'fieldset',
                        collapsible: true,
                        defaults: {
                            flex: 1,
                            labelAlign: 'right'
                        },
                        layout: 'hbox',
                        items: [{
                            xtype: 'fieldcontainer',
                            hideLabel: true,
                            layout: 'hbox',
                            defaults: {
                                labelAlign: 'right',
                                labelWidth: 100
                            },
                            items: [
                                {
                                    xtype: 'hiddenfield',
                                    name: 'byvisitorid',
                                    itemId: 'byvisitorid'
                                },
                                {
                                    xtype: 'hiddenfield',
                                    name: 'byvisitorcode',
                                    itemId: 'byvisitorcode'
                                },{
                                    xtype: 'textfield',
                                    fieldLabel: '被访人',
                                    allowBlank: false,
                                    readOnly: true,
                                    name: 'byvisitor',
                                    itemId: 'byvisitor',
                                    flex: 1
                                }, {
                                    xtype: 'button',
                                    itemId: 'addCard',
                                    text: '选择被访人',
                                    iconCls: 'x-fa fa-address-book',
                                    width: 130,
                                    listeners: {
                                        click: function (button, event) {
                                            var byvisitor = me.down('textfield[itemId=byvisitor]');
                                            var byvisitorid = me.down('hiddenfield[itemId=byvisitorid]');
                                            var byvisitorcode = me.down('hiddenfield[itemId=byvisitorcode]');
                                            var visitorwin = Ext.create('CamPus.view.visitor.vsubscribe.SelectByVisitor', {
                                                callback: function (uid, name, orgname,code) {
                                                    byvisitor.setValue(Ext.String.format('{0}（{1}）{2}', name, orgname,code));
                                                    byvisitorid.setValue(uid);
                                                    byvisitorcode.setValue(code);
                                                }
                                            });
                                            visitorwin.show();
                                        }
                                    }
                                }]
                        }]
                    },
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaults: {
                            labelAlign: 'right',
                            labelWidth: 100
                        },
                        items: [
                            {
                                xtype: 'hiddenfield',
                                name: 'othervisitorval',
                                itemId: 'othervisitorval'
                            },
                            {
                                xtype: 'tagfield',
                                fieldLabel: '随访人员',
                                itemId: 'othervisitor',
                                displayField: 'label',
                                valueField: 'label',
                                createNewOnEnter: true,
                                createNewOnBlur: true,
                                autoSelect: false,
                                forceSelection: false,
                                filterPickList: true,
                                queryMode: 'local',
                                publishes: 'value',
                                emptyText: '随访人员信息...',
                                allowBlank: true,
                                submitValue: false,
                                hideTrigger: true,
                                JsonVal: [],
                                flex: 1,
                                store: Ext.create('Ext.data.ArrayStore', {
                                    data: []
                                }),
                                listeners: {
                                    change: function (field, newValue, oldValue, eOpts) {
                                        if (newValue.length < oldValue.length) {
                                            var newjsonval = [];
                                            Ext.each(field.JsonVal, function (v, y) {
                                                var data = Ext.String.format('{0}（{1}）', v.name, v.idcard);
                                                Ext.each(newValue, function (r, i) {
                                                    if (data == r) {
                                                        newjsonval.push(v);
                                                        return false
                                                    }
                                                });
                                            });
                                            field.JsonVal = newjsonval;
                                        }
                                    }
                                }
                            },
                            {
                                xtype: 'button',
                                text: '增加随访人员',
                                iconCls: 'x-fa fa-plus-square-o',
                                width: 130,
                                listeners: {
                                    click: function (button, event) {
                                        var visitorwin = Ext.create('CamPus.view.visitor.vsubscribe.AddOtherVistorWindow', {
                                            callback: function (idcard, name) {
                                                var data = Ext.String.format('{0}（{1}）', name, idcard);
                                                var othervisitor = me.down('tagfield[itemId=othervisitor]');
                                                othervisitor.addValue(data);
                                                if (!othervisitor.JsonVal) {
                                                    othervisitor.JsonVal = [];
                                                }
                                                othervisitor.JsonVal.push({
                                                    name: name,
                                                    idcard: idcard
                                                });
                                            }
                                        });
                                        visitorwin.show();
                                    }
                                }
                            }]
                    },
                    {
                        xtype: 'tagfield',
                        fieldLabel: '添加来访车辆',
                        itemId: 'plateno',
                        name: 'plateno',
                        displayField: 'label',
                        valueField: 'label',
                        createNewOnEnter: true,
                        createNewOnBlur: true,
                        autoSelect: false,
                        hideTrigger: true,
                        forceSelection: false,
                        filterPickList: true,
                        queryMode: 'local',
                        publishes: 'value',
                        emptyText: '车辆信息...',
                        allowBlank: true,
                        store: Ext.create('Ext.data.ArrayStore', {
                            data: []
                        })
                    },
                    {
                        xtype: 'textfield',
                        fieldLabel: '携带物品',
                        name: 'things',
                        itemId: 'things',
                        allowBlank: true
                    }, {
                        xtype: 'textfield',
                        fieldLabel: '来访事由',
                        name: 'reason',
                        itemId: 'reason',
                        allowBlank: true
                    }, {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            labelAlign: 'right'
                        },
                        items: [{
                            xtype: 'datetimefield',
                            labelSeparator: '',
                            name: 'visittime',
                            itemId: 'visittime',
                            value: Ext.Date.add(new Date(Ext.Date.format(new Date(), 'Y-m-d 00:00:00'))),
                            format: 'Y-m-d H:i:s',
                            fieldLabel: '来访时间',
                            allowBlank: false
                        }, {
                            xtype: 'datetimefield',
                            labelSeparator: '',
                            name: 'leavetime',
                            itemId: 'leavetime',
                            value: Ext.Date.add(new Date(Ext.Date.format(new Date(), 'Y-m-d 23:59:59'))),
                            format: 'Y-m-d H:i:s',
                            fieldLabel: '离场时间',
                            allowBlank: false
                        }]
                    }]
            }]
        });

        Ext.apply(me, {
            items: [me.formpanel]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            var othervisitor = form.down('tagfield[itemId=othervisitor]');
            var othervisitorval = form.down('hiddenfield[itemId=othervisitorval]');
            othervisitorval.setValue(JSON.stringify(othervisitor.JsonVal));

            form.submit({
                url: '/Visitor/VisitorSubscribe/SaveVisitorSubscribe',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.grid.getStore().reload();
                        me.close();
                        toast('操作成功！！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.visitor.vsubscribe.AddOtherVistorWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '增加随访人员',
    width: 600,
    height: 150,
    maximizable: false,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.formpanel = Ext.create('Ext.panel.Panel', {
            layout: {
                type: 'hbox',
                pack: 'start',
                align: 'stretch'
            },
            items: [{
                xtype: 'form',
                itemId: 'byform',
                border: false,
                flex: 1,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'fieldcontainer',
                        hideLabel: true,
                        layout: 'hbox',
                        defaults: {
                            flex: 1,
                            labelWidth: 100,
                            labelAlign: 'right'
                        },
                        items: [
                            {
                                xtype: 'textfield',
                                fieldLabel: '随访人员姓名',
                                allowBlank: false,
                                name: 'name',
                                itemId: 'name'
                            }, {
                                xtype: 'textfield',
                                fieldLabel: '证件号码',
                                allowBlank: false,
                                name: 'idcard',
                                itemId: 'idcard'
                            }

                        ]
                    }

                ]
            }
            ]
        });

        Ext.apply(me, {
            items: [me.formpanel]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=byform]');
        if (form.getForm().isValid()) {
            var name = form.down('textfield[itemId=name]');
            var idcard = form.down('textfield[itemId=idcard]');
            if (me.callback && typeof (me.callback) == 'function') {
                me.callback(idcard.getValue(), name.getValue());
            }
            me.close();
        }
    }
});

Ext.define('CamPus.view.visitor.vsubscribe.SelectVisitor', {
    extend: 'CamPus.view.ux.Window',
    title: '选择访客信息',
    maximizable: false,
    constructor: function (config) {
        var me = this;

        me.visitorstore = Ext.create('CamPus.view.visitor.vsubscribe.VisitorListStore', {
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'searchfield',
                    itemId: 'searchKey',
                    width: 240,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.visitorstore,
                    emptyText: '姓名、身份证、电话、卡号关键词查询...'
                }, '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.visitorstore.reload();
                    }
                }
            ]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.visitgrid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.visitorstore,
            selModel: 'rowmodel',
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {
                    xtype: 'rownumberer'
                },
                {
                    xtype: "actioncolumn",
                    dataIndex: 'photo',
                    header: "照片",
                    align: 'center',
                    width: 65,
                    colHideable: false,
                    menuDisabled: true,
                    stopSelection: true,
                    items: [{
                        icon: './../resources/images/nobody.png',
                        tooltip: '点击查看大头像',
                        iconCls: 'border-radius50 size36',
                        handler: function (grid, rowIndex, colIndex, item, e, record, row) {
                            var record = grid.getStore().getAt(rowIndex);
                            me.controller.viewFace(record);
                        }
                    }],
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            this.items[0].icon = window.getResourceUrl(CamPus.AppSession.resourcedomain, value) + '?t=' + Math.random();
                        } else {
                            this.items[0].icon = './../resources/images/nobody.png'
                        }
                    }
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 100,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 50,
                    sortable: false
                },
                {
                    text: '电话',
                    dataIndex: 'mobile',
                    width: 100,
                    sortable: false
                },
                {
                    text: '单位',
                    dataIndex: 'company',
                    minWidth: 150,
                    flex: 1,
                    sortable: false
                },
                {
                    text: '部门',
                    dataIndex: 'department',
                    width: 85,
                    sortable: false
                },
                {
                    text: '职位',
                    dataIndex: 'position',
                    width: 80,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'cardsn',
                    width: 100,
                    sortable: false
                }, {
                    text: '二维码',
                    dataIndex: 'qrcode',
                    width: 100,
                    sortable: false
                },
                {
                    text: '证件类型',
                    dataIndex: 'idtype',
                    width: 80,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === '1') {
                            return '身份证';
                        } else if (value === '2') {
                            return '驾驶证';
                        } else if (value === '3') {
                            return '军官证';
                        } else if (value === '4') {
                            return '其他证件';
                        }
                    }
                },
                {
                    text: '证件号码',
                    dataIndex: 'idcard',
                    width: 150,
                    sortable: false
                },
                {
                    text: '民族',
                    dataIndex: 'nation',
                    width: 85,
                    sortable: false
                },
                {
                    text: '出生日期',
                    dataIndex: 'birthday',
                    width: 120,
                    sortable: true
                },
                {
                    text: '地址',
                    dataIndex: 'address',
                    minWidth: 150,
                    flex: 1,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.visitgrid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var visitgridrs = me.visitgrid.getSelection();
        if (visitgridrs.length == 0) {
            toast('请选择一条访客信息');
            return;
        }
        var record = visitgridrs[0];
        if (me.callback && typeof (me.callback) == 'function') {
            me.callback(record);
        }
        me.close();
    }
});

Ext.define('CamPus.view.visitor.vsubscribe.SelectByVisitor', {
    extend: 'CamPus.view.ux.Window',
    title: '选择被访者',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.visitor.vsubscribe.OrgTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get("orgname3") + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function (tree, record, index, eOpts) {
                    me.byselectstore.loadPage(1);
                }
            }
        });

        me.byselectstore = Ext.create('CamPus.view.visitor.vsubscribe.TechInfoStore', {
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');

                    Ext.apply(store.proxy.extraParams, {
                        orgcode: orgcode,
                        viewchild: viewchild.getValue()
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                itemId: 'searchKey',
                width: 150,
                hideLabel: true,
                paramName: 'key',
                store: me.byselectstore,
                emptyText: '关键词...'
            }, {
                xtype: 'checkboxfield',
                boxLabel: '显示子节点人员',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.byselectstore.reload();
                    }
                }
            }, {
                xtype: 'button',
                itemId: 'readCard',
                text: '读卡',
                iconCls: 'x-fa fa-microchip',
                hidden: CamPus.AppSession.cardtype == 'id',
                handler: function () {
                    window.onReadCard(function (identid, CardNO) {
                        var searchKey = me.down('searchfield[itemId=searchKey]');
                        searchKey.setValue(CardNO);
                        searchKey.onSearchClick();
                    });
                }
            }]
        });

        me.byselectgrid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            border: false,
            columnLines: true,
            store: me.byselectstore,
            collapsible: false,
            multiColumnSort: true,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(me, {
            items: [me.tree, me.byselectgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.byselectgrid.getSelection();
        if (records.length == 0) {
            toast('请选择一条被访人员信息');
            return;
        }
        var record = records[0];
        var name = record.get('name');
        var orgname = record.get('orgname');
        var code = record.get('code');
        var uid = record.get('uid');
        if (me.callback && typeof (me.callback) == 'function') {
            me.callback(uid, name, orgname,code);
        }
        me.close();
    }
});
