Ext.define('CamPus.view.visitor.vsubscribe.VSubscribeController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.VSubscribeController',
    view: ['VSubscribeView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=add]': {
                click: me.onAddVisitor
            },
            '*[itemId=audit]': {
                click: me.onAuditClick
            },
            '*[itemId=del]': {
                click: me.onDelClick
            },
            'gridpanel[itemId=vsubscribegrid]': {
                rowdblclick: me.onGridDBClick
            }
        });
        me.store = me.view.store;
        me.grid = me.view.grid;
        me.store.on('beforeload', function (store) {
            var starttime = me.grid.down('datetimefield[itemId=starttime]');
            var endtime = me.grid.down('datetimefield[itemId=endtime]');
            var auditstatus = me.view.down('combobox[itemId=auditstatus]');
            Ext.apply(store.proxy.extraParams, {
                auditstatus: auditstatus.getValue(),
                starttime: Ext.Date.format(starttime.getValue(), 'Y-m-d H:i:s'),
                endtime: Ext.Date.format(endtime.getValue(), 'Y-m-d H:i:s')
            });
        });
    },
    onAddVisitor: function () {
        var me = this;
        var win = Ext.create('CamPus.view.visitor.vsubscribe.AddVSubscribeWindow', {
            grid: me.grid,
            fbar: [{
                xtype: 'button',
                itemId: 'readCard',
                text: '读卡',
                iconCls: 'x-fa fa-microchip',
                hidden: (CamPus.AppSession.cardtype == 'id'),
                handler: function () {
                    window.onReadCard(function (identid, CardNO) {
                        var cardsn = win.down('textfield[itemId=cardsn]');
                        cardsn.setValue(CardNO);
                    });
                }
            }, {
                xtype: 'button',
                text: '确定',
                iconCls: 'x-fa fa-floppy-o',
                listeners: {
                    click: function (button, event) {
                        win.Save();
                    }
                }
            }, {
                xtype: 'button',
                text: '取消',
                iconCls: 'x-fa fa-reply-all',
                listeners: {
                    click: function (button, event) {
                        win.close();
                    }
                }
            }
            ]
        });
        win.show();
    },
    onSaveCardClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择一条需要发卡的预约信息...');
            return;
        }
        if (me.grid.getSelection().length> 1) {
            toast('请选择一条需要发卡的预约信息...');
            return;
        }
        var record = me.grid.getSelection()[0];
        if(record.get('status') != 1){
            toast('请选择一条审核成功的预约信息...');
            return;
        }
        var win = Ext.create('CamPus.view.visitor.vsubscribe.GiveCardEditWindow', {
            ctrl: me,
            handleIndex: 0,
            handleData: me.grid.getSelection(),
            insertfbar: [{
                xtype: 'progress',
                itemId: 'cardprogress',
                value: 0,
                animate: true,
                flex: 1,
                text: '发卡进度',
                style: 'border:1px solid #7fb5e4;'
            }],
            lfbar: [{
                xtype: 'button',
                text: '初始化',
                iconCls: 'x-fa fa-microchip',
                hidden: CamPus.AppSession.cardtype == 'id',
                listeners: {
                    click: function (button, event) {
                        me.onInitCard();
                    }
                }
            }, {
                xtype: 'button',
                text: '读卡',
                iconCls: 'x-fa fa-play',
                hidden: CamPus.AppSession.cardtype == 'id',
                listeners: {
                    click: function (button, event) {
                        win.readCard();
                    }
                }
            }]
        });
        win.show();
    },

    InitWindow: function (record) {
        var me = this;
        var idtype = record.get('idtype');
        var idtypename = '';
        if (idtype == '1') {
            idtypename = '身份证';
        } else if (idtype == '2') {
            idtypename = '驾驶证';
        } else if (idtype == '3') {
            idtypename = '军官证';
        } else if (idtype == '4') {
            idtypename = '其他证件';
        }
        record.set('idtypename', idtypename);

        var visittimestr = '';
        if (record.get('visittime')) {
            visittimestr = Ext.Date.format(new Date(record.get('visittime')), 'Y-m-d H:i:s');
        }
        record.set('visittimestr', visittimestr);

        var leavetimestr = '';
        if (record.get('leavetime')) {
            leavetimestr = Ext.Date.format(new Date(record.get('leavetime')), 'Y-m-d H:i:s');
        }
        record.set('leavetimestr', leavetimestr);

        var byagree = record.get('byagree');
        var auditstatus = record.get('auditstatus');
        if (byagree == 1) {
            record.set('auditstatusint', 1);
        } else {
            if (auditstatus == 1) {
                record.set('auditstatusint', 2);
            } else {
                record.set('auditstatusint', 0);
            }
        }
        var win = Ext.create('CamPus.view.visitor.vsubscribe.AuditSubscribeWindow', {
            grid: me.grid,
            auditstatusint: record.get('auditstatusint'),
            fbar: [
                /* {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '生成二维码',
                    handler: function () {
                        Ext.Ajax.request({
                            url: '/Visitor/VisitorSubscribe/CreateQRcode',
                            params: {},
                            method: 'POST',
                            success: function (response) {
                                var result = Ext.JSON.decode(response.responseText);
                                if (result.success) {
                                    win.down('textfield[itemId=qrcode]').setValue(result.msg);
                                } else {
                                    Ext.Msg.alert('系统信息', result.msg);
                                }
                            }
                        })
                    }
                }, */
                {
                    xtype: 'button',
                    text: '读卡',
                    iconCls: 'x-fa fa-microchip',
                    hidden: (CamPus.AppSession.cardtype == 'id'),
                    handler: function () {
                        window.onReadCard(function (identid, CardNO) {
                            var cardsn = win.down('textfield[itemId=cardsn]');
                            cardsn.setValue(CardNO);
                        });
                    }
                },
                {
                    xtype: 'button',
                    text: '确定',
                    iconCls: 'x-fa fa-floppy-o',
                    listeners: {
                        click: function (button, event) {
                            win.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    iconCls: 'x-fa fa-reply-all',
                    listeners: {
                        click: function (button, event) {
                            win.close();
                        }
                    }
                }
            ]
        });
        var form = win.down('form[itemId=form]');
        var othervisitor = '';
        Ext.each(record.get('children'), function (item, index) {
            othervisitor += '姓名：' + item.othername + ', 身份证号：' + item.otheridcard + ' ';
            if(index < record.get('children').length){
                othervisitor += '\n';
            }
        });
        if(record.get('auditstatusint') != 0){
            form.down('combobox[itemId=auditstatusint]').setReadOnly(true)
            // win.down('button[itemId=refresh]').setHidden(true)
        }else {
            form.down('combobox[itemId=auditstatusint]').setReadOnly(false)
            // win.down('button[itemId=refresh]').setHidden(false)
        }
        form.loadRecord(record);
        form.down('textarea[itemId=children]').setValue(othervisitor);
        win.show();
    },
    onAuditClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择需要审核的来访预约记录...');
            return;
        }
        var record = me.grid.getSelection()[0];
        if(record.get('status') == 2){
            toast('该来访预约已取消，无需审核...');
            return;
        }
        if(record.get('status') != 0){
            toast('该来访预约已审核...');
            return;
        }
        me.InitWindow(record);
    },
    onDelClick: function () {
        var me = this;

        var uids = [];
        Ext.each(me.grid.getSelection(), function (item, index) {
            uids.push(item.get('uid'));
        });

        Ext.Msg.show({
            title: '系统确认',
            message: '确定要删除所选访客预约信息？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Visitor/VisitorSubscribe/DelSubscribe',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('操作成功！');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    })
                }
            }
        });

    },
    onGridDBClick: function (grid, record, element, rowIndex, e, eOpts) {
        var me = this;
        me.InitWindow(record);
    }
});