Ext.define('CamPus.view.visitor.visitormachine.VisitorMachineView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.VisitorMachineView",
    controller: 'VisitorMachineController',
    requires: [
        'CamPus.view.visitor.visitormachine.VisitorMachineStore',
        'CamPus.view.visitor.visitormachine.VisitorMachineController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.vmachineStore = Ext.create('CamPus.view.visitor.visitormachine.VisitorMachineStore', {});

        var vmachinetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'searchfield',
                    width: 180,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.vmachineStore,
                    emptyText: '位置编号、名称关键词...'
                }, '->', {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.vmachineStore.reload();
                    }
                }]
        });

        Ext.each(config.opbutton, function (item, i) {
            if(item.itemId != 'setdevice' && item.itemId != 'deldevice' && item.itemId != 'addfloor'){
                vmachinetbar.addMaxItems(item, config.maxopbutton);
            }
        });

        me.machinegrid = Ext.create('Ext.grid.Panel', {
            itemId: 'machinelist',
            multiColumnSort: true,
            border: false,
            region: 'west',
           // title: '区域位置',
            collapsible: false,
            rowLines: true,
            columnLines: true,
            tbar: vmachinetbar,
            store: me.vmachineStore,
            selModel: 'checkboxmodel',
                        // viewConfig: {
            //     enableTextSelection: true,
            //     listeners: {
            //         refresh: function (view, eOpts) {
            //             view.select(0);
            //         }
            //     }
            // },,
            columns: [
                {
                    xtype: 'rownumberer'
                },
                {
                    text: '位置编号',
                    dataIndex: 'position',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '备注',
                    dataIndex: 'remark',
                    width: 200,
                    flex: 1,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                        if (value) {
                            metaData.tdAttr = 'data-qtip="' + value + '"';
                        }
                        return value;
                    }
                }

            ],
            width: 500,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.machineDeviceStore = Ext.create('CamPus.view.visitor.visitormachine.MachineDeviceStore', {});

        var machineDevicetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.machineDeviceStore,
                emptyText: '设备机号,设备名称关键词...'
            }, '->', {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.machineDeviceStore.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            if(item.itemId == 'setdevice' || item.itemId == 'deldevice' || item.itemId == 'addfloor'){
                machineDevicetbar.addMaxItems(item, config.maxopbutton);
            }
        });

        me.devicegrid = Ext.create('Ext.grid.Panel', {
            itemId: 'devicelist',
            multiColumnSort: true,
            border: false,
            region: 'center',
           // title: '设备',
            rowLines: true,
            columnLines: true,
            collapsible: false,
            tbar: machineDevicetbar,
            store: me.machineDeviceStore,
            selModel: 'checkboxmodel',
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {
                    xtype: 'rownumberer'

                },
                {
                    text: '设备机号',
                    dataIndex: 'machineid',
                    width: 120,
                    sortable: false
                },
                {
                    text: '设备名称',
                    dataIndex: 'name',
                    width: 200,
                    sortable: false
                },
                {
                    text: '继电器名称',
                    dataIndex: 'doorname',
                    width: 200,
                    sortable: false
                },
                {
                    text: '继电器',
                    dataIndex: 'relays',
                    width: 200,
                    sortable: false
                },
                {
                    text: '位置',
                    dataIndex: 'areacode',
                    flex: 1,
                    sortable: false
                },
                {
                    text: '楼层序号',
                    dataIndex: 'floor',
                    flex: 1,
                    sortable: false
                }
            ],
            flex: 1,
            minWidth: 390,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [
                me.machinegrid, me.devicegrid
            ]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.visitor.visitormachine.AddMachineWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '新增区域',
    width: 450,
    height: 250,
    maximizable: false,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);
        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right'
                },
                defaultType: 'textfield',
                items:[{
                    xtype: 'hiddenfield',
                    name: 'uid'
                },{
                    xtype: 'textfield',
                    fieldLabel: '区域编号',
                    allowBlank: false,
                    itemId: 'position',
                    name: 'position'
                },{
                    xtype: 'textfield',
                    fieldLabel: '名称',
                    allowBlank: false,
                    itemId: 'name',
                    name: 'name'
                }, {
                    xtype: "fieldcontainer",
                    layout: "hbox",
                    defaults: {
                        anchor: '100%',
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'filefield',
                        name: 'imagefile',
                        fieldLabel: '路线图',
                        msgTarget: 'side',
                        flex: 1,
                        buttonText: '',
                        vtype: 'ext',
                        fileType: ['jpg', 'jpeg'],
                        buttonConfig: {
                            iconCls: 'x-fa fa-photo'
                        }
                    },
                        {
                            xtype: "button",
                            text: "",
                            tooltip: '点击查看原有路线图',
                            itemId: 'btn',
                            iconCls: 'x-fa fa-eye',
                            hidden: true,
                            listeners: {
                                click: function () {
                                    var uid = me.grid.getSelection()[0].get('uid');
                                    Ext.Ajax.request({
                                        url: '/Consume/ConsumeUser/getMerchantImage',
                                        params: {
                                            uid: uid
                                        },
                                        method: 'GET',
                                        success: function (response) {
                                            var result = Ext.JSON.decode(response.responseText);
                                            if (result.success) {
                                                var imgPath = result.data.imagefile;
                                                var win = Ext.create('CamPus.view.consume.merchant.MerchantImgWindows', {
                                                    title: '营业执照图片',
                                                    imagePath: imgPath,
                                                    fbar: [{
                                                        xtype: 'button',
                                                        text: '关闭',
                                                        iconCls: 'x-fa fa-reply-all',
                                                        listeners: {
                                                            click: function (button, event) {
                                                                win.close();
                                                            }
                                                        }
                                                    }]
                                                });
                                                win.show();
                                            } else {
                                                Ext.Msg.alert('系统信息', result.msg);
                                            }
                                        }
                                    });
                                }
                            }
                        }]
                },
                    {
                    xtype: 'textfield',
                    fieldLabel: '备注',
                    itemId: 'remark',
                    name: 'remark'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function(){
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Visitor/VisitorMachine/addMachine',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function(form, action) {
                    if (action.result.success) {
                        me.machinegrid.getStore().reload();
                        me.close();
                        toast('保存成功！！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function(form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.visitor.visitormachine.DeviceWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择设备',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('CamPus.view.visitor.visitormachine.selectDeviceStore', {
            listeners: {
                beforeload: function (store) {
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });

                    Ext.apply(store.proxy.extraParams, {
                        position: config.position,
                        selecteduid: JSON.stringify(selecteduid)
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [ {
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '设备编号、设备名称关键词...'
            }]
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            selModel:'checkboxmodel',
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                {
                    xtype: 'rownumberer'
                },
                {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 100,
                    sortable: false
                },
                {
                    text: '设备名称',
                    dataIndex: 'devname',
                    width: 120,
                    sortable: false
                },
                {
                    text: '继电器号',
                    dataIndex: 'doorid',
                    width: 130,
                    sortable: false,
                    hidden: true
                },
                {
                    text: '继电器名称',
                    dataIndex: 'doorName',
                    width: 130,
                    sortable: false
                },
                {
                    text: '位置',
                    dataIndex: 'areacode',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选设备：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.store.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['machineid', 'devname', 'uid', 'areacode','doorid','doorName'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                {
                    xtype: 'rownumberer'
                },
                {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 130,
                    sortable: false
                },
                {
                    text: '继电器号',
                    dataIndex: 'doorid',
                    width: 130,
                    sortable: false
                },
                {
                    text: '继电器名称',
                    dataIndex: 'doorName',
                    width: 130,
                    sortable: false
                },
                {
                    text: '设备名称',
                    dataIndex: 'devname',
                    width: 150,
                    sortable: false
                },
                {
                    text: '位置',
                    dataIndex: 'areacode',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 350
        });

        Ext.apply(me, {
            items: [me.grid, me.selectgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var deviceids = [];
        var doorids = [];

        if (me.selectgrid.getStore().getData().items.length == 0) {
            toast('请选择设备，选择以后拖拽至右侧表格');
            return;
        }
        Ext.each(me.selectgrid.getStore().getData().items, function (item, index) {
            deviceids.push(item.get('uid'));
            doorids.push(item.get('doorid'));
        });
        Ext.Ajax.request({
            url: '/Visitor/VisitorMachine/setDevice',
            params: {
                position: me.position,
                devid: deviceids.join(','),
                doorids:doorids.join(',')
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.devicegrid.getStore().reload();
                    toast('添加设备成功...');
                    me.close();
                } else {
                    toast( result.msg);
                }
            }
        });
    }
});

Ext.define('CamPus.view.visitor.visitormachine.AuthorizeDevTimeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择楼层',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.storeystore = Ext.create('CamPus.view.elevator.deveauthrize.StoreyStore', {
            listeners: {
                load: function (store, records, successful, operation, eOpts) {
                    Ext.each(records, function (item, i) {
                        var icosrc = 'resources/images/floor.png';
                        item.set('icosrc', icosrc);
                    });
                    me.storeygrid.setCheckedRecord(config.storeys);
                },
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        devid: me.devid
                    });
                }
            }
        });

        var storeytbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 220,
                hideLabel: true,
                paramName: 'key',
                store: me.storeystore,
                emptyText: '楼层名称关键词...'
            }, '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.storeystore.reload();
                    }
                },
                {
                    xtype: 'button',
                    itemId: 'selectall',
                    iconCls: 'x-fa fa-check-square-o',
                    text: '全选/取消',
                    listeners: {
                        click: function () {
                            if (me.storeygrid.getChecked().length == 0) {
                                me.storeygrid.CheckAll();
                            } else {
                                me.storeygrid.DisCheckAll();
                            }
                        }
                    }
                }]
        });

        Ext.each(config.opbutton, function (item, i) {
            storeytbar.addMaxItems(item, config.maxopbutton);
        });

        me.storeygrid = Ext.create('CamPus.view.ux.IcoView', {
            itemId: 'StoreyView',
            collapsible: false,
            store: me.storeystore,
            flex: 1,
            style: 'border-top:solid 1px #ccc;',
            tpl: [
                '<div class="icoviewcls">',
                '<tpl for=".">',
                '<div class="img-thumb-wrap" style="width:90px;height:90px;">',
                '<div class="imgThumb">',
                '<span class="ckstatus{uid} nocheck"></span>',
                '<img src="{icosrc}" ondragstart="return false;" style="width:65%;height:65%;"/>',
                '</div>',
                '<span>{name}</span>',
                '</div>',
                '</tpl>',
                '</div>'
            ],
            listeners: {
                itemclick: function (view, record, item, index, e, eOpts) {
                    me.storeygrid.setChecked(record);
                }
            },
            CheckAll: function () {
                Ext.each(me.storeygrid.getStore().getData().items, function (item, xi) {
                    me.storeygrid.setChecked(item);
                });
            },
            DisCheckAll: function () {
                Ext.each(me.storeygrid.getStore().getData().items, function (item, xi) {
                    me.storeygrid.setChecked(item, true);
                });
            },
            setChecked: function (record, isdechecked) {
                var view = this;
                var viewid = me.storeygrid.id;
                var checkedstatus = Ext.select('#' + viewid + ' .ckstatus' + record.get('uid'));
                if (checkedstatus.elements && checkedstatus.elements.length > 0) {
                    var dm = checkedstatus.elements[0];
                    if (dm.getAttribute('class').indexOf('checked') == -1 && !isdechecked) {
                        dm.setAttribute('class', dm.getAttribute('class').replace(/ nocheck/g, ''));
                        dm.setAttribute('class', dm.getAttribute('class') + ' checked');
                    } else {
                        dm.setAttribute('class', dm.getAttribute('class').replace(/ checked/g, ''));
                        dm.setAttribute('class', dm.getAttribute('class') + ' nocheck');
                    }
                }
            },
            getChecked: function () {
                var view = this;
                var records = [];
                var viewid = me.storeygrid.id;
                Ext.each(Ext.select('#' + viewid + ' .checked').elements, function (dm, i) {
                    Ext.each(me.storeygrid.getStore().getData().items, function (item, xi) {
                        if (dm.getAttribute('class').indexOf(item.get('uid')) != -1) {
                            records.push(item);
                        }
                    });
                });
                return records;
            },
            setCheckedRecord: function (storeys) {
                var view = this;
                if (storeys) {
                    var storeysx = storeys.split(',');
                    Ext.each(me.storeygrid.getStore().getData().items, function (item, xi) {
                        if (storeysx[item.get('code') - 1] == '1') {
                            var checkedstatus = Ext.select('.ckstatus' + item.get('uid'));
                            if (checkedstatus.elements.length > 0) {
                                var dm = checkedstatus.elements[0];
                                dm.setAttribute('class', dm.getAttribute('class').replace(/ nocheck/g, ''));
                                dm.setAttribute('class', dm.getAttribute('class') + ' checked');
                            }
                        }
                    });
                }
            }
        });

        Ext.apply(me, {
            items: [{
                xtype: 'panel',
                region: 'center',
                scrollable: 'y',
                collapsible: false,
                tbar: storeytbar,
                items: [me.storeygrid]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var devid = [];
        // if (me.devtimelist.getSelection().length == 0) {
        //     toast('请选择时段...');
        //     return;
        // }

        // var timezones = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        // Ext.each(me.devtimelist.getSelection(), function (item, i) {
        //     var timeindex = item.get('timeindex');
        //     timezones[timeindex - 1] = 1;
        // });
        //
        var devdata = [];

        Ext.each(me.devids, function (devid, i) {
            devdata.push({
                devid: devid
                // timezones: timezones.toString()
            });
        });

        var infoid = [];
        if (!me.infoid) {
            Ext.each(me.data, function (item, i) {
                infoid.push(item.get('uid'));
            });
        } else {
            infoid.push(me.infoid);
        }
        // var form = me.down('form[itemId=form]');
        // var starttime = form.down('datetimefield[itemId=starttime]').getValue();
        // var endtime = form.down('datetimefield[itemId=endtime]').getValue();
        // if (!starttime || !endtime) {
        //     toast('开始时间和结束时间不能为空...');
        //     return;
        // }
        // starttime = new Date(Date.parse(starttime));
        // endtime = new Date(Date.parse(endtime));
        // if (starttime > endtime) {
        //     toast('开始时间不能大于现结束时间');
        //     return;
        // }
        var storyrecords = me.storeygrid.getChecked();
        var floor = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        Ext.each(storyrecords, function (item, i) {
            floor[item.get('code') - 1] = 1;
        });
        Ext.Ajax.request({
            url: '/Visitor/VisitorMachine/addFloor',
            params: {
                devid: me.devid,
                position: me.position,
                floor: floor.join(',')
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    toast('设置成功...');
                    me.close();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});
