Ext.define('CamPus.view.card.teacher.TeacherController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.TeacherController',
    view: ['TeacherView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=add]': {
                click: me.onAddClick
            },
            '*[itemId=edit]': {
                click: me.onEditClick
            },
            '*[itemId=del]': {
                click: me.onDelClick
            },
            'treepanel[itemId=collegetree]': {
                select: me.onTreeSelect
            },
            '*[itemId=resetpassword]': {
                click: me.onResetPasswordClick
            },
            '*[itemId=disable]': {
                click: me.onDisableClick
            },
            '*[itemId=able]': {
                click: me.onAbleClick
            },
            '*[itemId=changeorg]': {
                click: me.onChangeOrgClick
            },
            '*[itemId=import]': {
                click: me.onImportClick
            },
            '*[itemId=importUpdate]': {
                click: me.onImportUpdateClick
            },
            '*[itemId=importleave]': {
                click: me.onImportLeaveClick
            },
            'gridpanel[itemId=TeacherGrid]': {
                rowdblclick: me.onTeacherGridDBClick
            },
            '*[itemId=export]': {
                click: me.onExportClick
            },
            '*[itemId=setchild]': {
                click: me.onSetChildClick
            },
            '*[itemId=childmanage]': {
                click: me.onChildManageClick
            },
            '*[itemId=exportqrcode]': {
                click: me.onExportQRCodeClick
            },
            '*[itemId=revoke]': {
                click: me.onRevokeClick
            },
            '*[itemId=importMoney]': {
                click: me.onImportMoneyClick
            }
        });
        me.store = me.view.store;
        me.grid = me.view.grid;
        me.treestore = me.view.treestore;
        me.tree = me.view.tree;
        me.infouidstore = me.view.infouidstore;


        me.store.on('beforeload', function (store) {
            var code = 'x';
            if (me.tree.getSelection().length > 0) {
                code = me.tree.getSelection()[0].get('code');
            }
            var viewchild = me.view.down('checkboxfield[itemId=viewchild]');
            var viewleave = me.view.down('combobox[itemId=viewleave]');
            var property = me.view.down('comboxdictionary[itemId=property]');
            var infolabel = me.view.down('combobox[itemId=infolabel]');
            Ext.apply(store.proxy.extraParams, {
                orgcode: code,
                viewchild: viewchild.getValue(),
                infolabel: infolabel.getValue(),
                viewleave: (viewleave.getValue() == -1 ? '' : viewleave.getValue()),
                property: property.getValue()
            });
        });
    },
    autoAddNewFn: function () {
        this.onAddClick();
    },
    onResetPasswordClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择职工资料...');
            return;
        }
        var infoids = [];
        Ext.each(me.grid.getSelection(), function(record, index) {
            infoids.push(record.get('uid'));
        });
        Ext.Ajax.request({
            url: '/Card/TeachStudInfo/ResetPassword',
            params: {
                infoid: infoids.join(',')
            },
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    toast('密码重置成功');
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    onRevokeClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择人员信息...');
            return;
        }
        var uids = [];
        Ext.each(me.grid.getSelection(), function(item, index) {
            uids.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '权限收回后,所有相关的数据将会清空，是否继续 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/OrgFramework/RevokeStudent',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                if(result.msg){
                                    toast(result.msg);
                                }else{
                                    toast('操作成功...');
                                }
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onImportMoneyClick: function() {
        var me = this;
        var win = Ext.create('CamPus.view.card.teacher.ImportMoneyWindow', {
            maingrid: me.grid
        });
        win.show();
    },

    onAddClick: function () {
        var me = this;
        var infoid = me.infouidstore.data.items[0].data.uid;

        if (me.tree.getSelection().length == 0) {
            toast('请选择' + window.applang.get('orgname') + '...');
            return;
        }
        var record = me.tree.getSelection()[0];
        me.tree.pathName = '';
        me.tree.setPathName(record);
        var win = Ext.create('CamPus.view.card.teacher.TeacherAddWindow', {
            aotuAddNew: true,
            ctrl: me,
            orgcode: record.get('code'),
            orgname: me.tree.pathName,
            grid: me.grid,
            newInfoId: infoid,
            infouidstore: me.infouidstore,
            lfbar: [{
                xtype: 'button',
                text: '读卡',
                iconCls: 'x-fa fa-microchip',
                hidden: (CamPus.AppSession.cardtype == 'id'),
                handler: function () {
                    window.onReadCard(function (identid, CardNO) {
                        var card = win.down('textfield[itemId=card]');
                        card.setValue(CardNO);
                    });
                }
            },
                {
                xtype: 'button',
                text: '人脸采集',
                iconCls: 'x-fa fa-microchip',
                handler: function() {
                    var upMe = this.up('window'); // 获取包含这个按钮的父窗口
                    // var me = this;
                    var uploadFaceWin = Ext.create('CamPus.view.card.teacher.uploadFaceWindows',{
                        uid: infoid,
                        title: '人脸采集',
                        handleIndex: 0,
                        // handleData: me.grid.getSelection(),
                        listeners:{
                            afterrender: function(win , eOpts){
                                win.InitInfoFace(infoid);
                            },
                            beforeclose: function(){
                                // me.store.loadPage(1);
                            },
                            // 当上传窗口关闭时，将图片 URL 回传
                            close: function (win) {
                                if (win.config.uid) {
                                    var me = this;
                                    var imgComponent = upMe.down('image[itemId=photo]');
                                    // var src = me.down('hiddenfield[name=uid1]').value;
                                    var src = me.down('image[itemId=faceImg1]').src;
                                    imgComponent.setSrc(window.getResourceUrl(CamPus.AppSession.resourcedomain, src) + '?t=' + Math.random());
                                }
                            }
                        },
                        showsave:false,
                        insertfbar: [{
                            xtype: 'progress',
                            itemId: 'cardprogress',
                            value: 0,
                            animate: true,
                            flex: 1,
                            text: '进度',
                            style: 'border:1px solid #7fb5e4;'
                        }],
                    });
                    uploadFaceWin.show();
                    // var me = this;
                    // var win = Ext.create('CamPus.view.face.intoface.uploadFaceWindows', {
                    //     maingrid: me.grid
                    // });
                    // win.show();
                }
            },
                {
                xtype: 'button',
                text: '读取身份证信息',
                iconCls: 'x-fa fa-microchip',
                handler: function () {
                    var form = win.down('form[itemId=form]');
                    var Name = form.down('textfield[name=name]');
                    var Idc = form.down('textfield[name=idcard]');
                    var sex1 = form.down('radio[itemId=sex1]');
                    var sex2 = form.down('radio[itemId=sex2]');
                    var Address = form.down('textfield[name=address]');
                    var NationCName = form.down('textfield[name=nation]');
                    var Birth = form.down('datefield[name=birthday]');
                    window.onReadIdCard(function (identid,idcard) {
                        idcard.Birth=idcard.Birth.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1/$2/$3");
                        Idc.setValue(idcard.Idc);
                        Name.setValue(idcard.Name);
                        Address.setValue(idcard.Address);
                        NationCName.setValue(idcard.NationCName);
                        Birth.setValue(new Date(Date.parse(idcard.Birth)));
                        if(idcard.SexCName=='女'){
                            sex2.setValue(true);
                        }else{
                            sex1.setValue(true);
                        }
                    });
                }
            }]
        });
        win.show();
    },
    onEditClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择' + window.applang.get('infoname1') + '信息...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var win = Ext.create('CamPus.view.card.teacher.TeacherEditWindow', {
            grid: me.grid,
            record: record,
            newInfoId: record.get('uid'),
            lfbar: [{
                xtype: 'button',
                text: '读卡',
                iconCls: 'x-fa fa-microchip',
                hidden: (CamPus.AppSession.cardtype == 'id'),
                handler: function () {
                    window.onReadCard(function (identid, CardNO) {
                        var card = win.down('textfield[itemId=card]');
                        card.setValue(CardNO);
                    });
                }
            },
                {
                xtype: 'button',
                text: '人脸采集',
                iconCls: 'x-fa fa-microchip',
                handler: function() {
                    var upMe = this.up('window'); // 获取包含这个按钮的父窗口
                    // var me = this;
                    var uploadFaceWin = Ext.create('CamPus.view.card.teacher.uploadFaceWindows',{
                        uid: record.get('uid'),
                        title: '人脸采集',
                        handleIndex: 0,
                        // handleData: me.grid.getSelection(),
                        listeners:{
                            afterrender: function(win , eOpts){
                                win.InitInfoFace(record.get('uid'));
                            },
                            beforeclose: function(){
                                // me.store.loadPage(1);
                            },
                            // 当上传窗口关闭时，将图片 URL 回传
                            close: function (win) {
                                if (win.config.uid) {
                                    var me = this;
                                    var imgComponent = upMe.down('image[itemId=photo]');
                                    // var src = me.down('hiddenfield[name=uid1]').value;
                                    var src = me.down('image[itemId=faceImg1]').src;
                                    if (src) {
                                        imgComponent.setSrc(window.getResourceUrl(CamPus.AppSession.resourcedomain, src) + '?t=' + Math.random());
                                    }
                                }
                            }
                        },
                        showsave:false,
                        insertfbar: [{
                            xtype: 'progress',
                            itemId: 'cardprogress',
                            value: 0,
                            animate: true,
                            flex: 1,
                            text: '进度',
                            style: 'border:1px solid #7fb5e4;'
                        }],
                    });
                    uploadFaceWin.show();
                    // var me = this;
                    // var win = Ext.create('CamPus.view.face.intoface.uploadFaceWindows', {
                    //     maingrid: me.grid
                    // });
                    // win.show();
                }
            },
                {
                xtype: 'button',
                text: '读取身份证信息',
                iconCls: 'x-fa fa-microchip',
                handler: function () {
                    var form = win.down('form[itemId=form]');
                    var Name = form.down('textfield[name=name]');
                    var Idc = form.down('textfield[name=idcard]');
                    var sex1 = form.down('radio[itemId=sex1]');
                    var sex2 = form.down('radio[itemId=sex2]');
                    var Address = form.down('textfield[name=address]');
                    var NationCName = form.down('textfield[name=nation]');
                    var Birth = form.down('datefield[name=birthday]');
                    window.onReadIdCard(function (identid,idcard) {
                        idcard.Birth=idcard.Birth.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1/$2/$3");
                        Idc.setValue(idcard.Idc);
                        Name.setValue(idcard.Name);
                        Address.setValue(idcard.Address);
                        NationCName.setValue(idcard.NationCName);
                        Birth.setValue(new Date(Date.parse(idcard.Birth)));
                        if(idcard.SexCName=='女'){
                            sex2.setValue(true);
                        }else{
                            sex1.setValue(true);
                        }
                    });
                }
            }]
        });
        var form = win.down('form[itemId=form]');
        form.loadRecord(record);
        form.down('textfield[itemId=card]').setReadOnly(true);
        win.show();
    },
    onDelClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择' + window.applang.get('infoname1') + '资料...');
            return;
        }
        // if (me.grid.getSelection().length > 1) {
        //     toast('系统禁止批量删除' + window.applang.get('infoname1') + '资料...');
        //     return;
        // }
        // var record = me.grid.getSelection()[0];
        // var uid = record.get('uid');
        var records = me.grid.getSelection();
        var recordUids = []
        records.forEach(function(item, i) {
            recordUids.push(item.get('uid'))
        })
        var infoids = recordUids.join(',');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/DelTeacher',
                        params: {
                            infoids: infoids
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onTreeSelect: function () {
        this.store.loadPage(1);
    },
    onDisableClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择' + window.applang.get('infoname1') + '资料...');
            return;
        }
        var uids = [];
        Ext.each(me.grid.getSelection(), function (item, index) {
            uids.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要将所选择的' + window.applang.get('infoname1') + '信息设置为无效 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/DisabledTeacher',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                if (result.msg) {
                                    toast(result.msg);
                                } else {
                                    toast('操作成功...');
                                }
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onAbleClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择' + window.applang.get('infoname1') + '资料...');
            return;
        }
        var uids = [];
        Ext.each(me.grid.getSelection(), function (item, index) {
            uids.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要将所选择的' + window.applang.get('infoname1') + '信息设置为有效 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/AbledTeacher',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('操作成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onChangeOrgClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择' + window.applang.get('infoname1') + '资料...');
            return;
        }
        var infoids = [];
        var fromOrgs = [];
        Ext.each(me.grid.getSelection(), function (record, index) {
            infoids.push(record.get('uid'));
            fromOrgs.push(record.get('orgcode'));
        });
        var win = Ext.create('CamPus.view.card.teacher.ChangeTeacherOrgWindow', {
            maingrid: me.grid,
            infoid: infoids.join(','),
            fromOrgs: fromOrgs.join(',')
        });
        win.show();
    },
    onImportClick: function () {
        var me = this;
        var win = Ext.create('CamPus.view.card.teacher.ImportTeacherWindow', {
            maingrid: me.grid
        });
        win.show();
    },
    onImportUpdateClick: function () {
        var me = this;
        var win = Ext.create('CamPus.view.card.teacher.ImportUpdateWindow', {
            maingrid: me.grid
        });
        win.show();
    },
    onImportLeaveClick: function () {
        var me = this;
        var win = Ext.create('CamPus.view.card.teacher.ImportLeaveTeacherWindow', {
            maingrid: me.grid
        });
        win.show();
    },
    onTeacherGridDBClick: function (grid, record, element, rowIndex, e, eOpts) {
        var me = this;
        var win = Ext.create('CamPus.view.card.givecard.GiveCardInfoViewWindow', {
            infoid: record.get('uid'),
            title: '【' + record.get('name') + '】已领所有卡信息',
            fbar: []
        });
        win.show();
    },
    onExportClick: function () {
        var me = this;
        Ext.get('loadingToast').show();
        Ext.Ajax.request({
            url: '/Card/TeachStudInfo/ExportTeacher',
            params: me.store.proxy.extraParams,
            method: 'POST',
            success: function (response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    onSetChildClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择人员信息...');
            return;
        }
        var infoids = [];
        Ext.each(me.grid.getSelection(), function (record, index) {
            infoids.push(record.get('uid'));
        });

        var win = Ext.create('CamPus.view.card.teacher.SelectChildInfoWindow', {
            autoSize: true,
            parentuids: infoids.join(',')
        });
        win.show();
    },
    onChildManageClick: function () {
        var me = this;
        var infoids = [];
        Ext.each(me.grid.getSelection(), function (record, index) {
            infoids.push(record.get('uid'));
        });

        var win = Ext.create('CamPus.view.card.teacher.ChildInfoManageWindow', {
            autoSize: true,
            parentuids: infoids.join(',')
        });
        win.show();
    },
    onExportQRCodeClick: function(){
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择职工资料...');
            return;
        }
        var infoids = [];
        Ext.each(me.grid.getSelection(), function(record, index) {
            infoids.push(record.get('uid'));
        });
        Ext.Ajax.request({
            url: '/Card/TeachStudInfo/exportQRCode',
            params: {
                infoid: infoids.join(',')
            },
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    }
});
