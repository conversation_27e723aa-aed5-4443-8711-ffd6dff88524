Ext.define('CamPus.view.card.cardbalance.CardBalanceStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/CardBalance/getUserBalance',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        }
    }
});

Ext.define('CamPus.view.card.cardbalance.CollegeClassTreeStore', {
    extend: 'Ext.data.TreeStore',
    remoteSort: false,
    autoLoad: false,
    root: {
        uid: 'root',
        name: window.applang.get("orgname3"),
        expanded: true
    },
    proxy: {
        type: 'ajax',
        url: '/Card/OrgFramework/getOrgTree',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json'
        },
        extraParams: {infoid:''}
    }
});