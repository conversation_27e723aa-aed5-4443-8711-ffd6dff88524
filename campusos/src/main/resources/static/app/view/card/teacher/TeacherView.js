Ext.define('CamPus.view.card.teacher.TeacherView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.TeacherView",
    controller: 'TeacherController',
    requires: [
        'Ext.dd.DropZone',
        'Ext.dd.DragZone',
        'CamPus.view.card.teacher.TeacherStore',
        'CamPus.view.card.teacher.TeacherController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;


        me.infouidstore = Ext.create('CamPus.view.card.teacher.GetTeacherUidStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {});
                }
            }
        });
        me.infouidstore.load();

        me.treestore = Ext.create('CamPus.view.card.teacher.OrgTreeStore', {

        });

        var collegetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get('orgname') + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            //title: window.applang.get('orgname'),
            itemId: 'collegetree',
            tbar: collegetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 280,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
                        viewConfig: {
                enableTextSelection: true,
                listeners: {
                    refresh: function (view, eOpts) {
                        view.select(0);
                    }
                }
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false,
                editor: {
                    xtype: 'textfield',
                    allowBlank: false
                }
            }],
            setPathName: function (node) {
                this.pathName = (this.pathName ? node.get('name') + '/' + this.pathName : node.get('name'));
                if (node.parentNode && node.parentNode.id != 'root') {
                    this.setPathName(node.parentNode);
                }
            },
            listeners: {
                render: function (tree) {
                    tree.dropZone = Ext.create('Ext.dd.DropZone', tree.el, {
                        getTargetFromEvent: function (e) {
                            return e.getTarget(tree.getView().rowSelector);
                        },
                        onNodeEnter: function (nodeData, dd, e, data) {
                            tree.body.stopAnimation();
                            tree.body.highlight();
                        },
                        onNodeOver: function (nodeData, dd, e, data) {
                            var sourceEl = e.getTarget(tree.itemSelector, 10);
                            if (sourceEl) {
                                var record = tree.view.getRecord(sourceEl);
                                return "x-dd-drop-ok-add";
                            } else {
                                return "x-dd-drop-nodrop";
                            }
                        },
                        onNodeDrop: function (node, dd, e, data) {
                            var sourceEl = e.getTarget(tree.view.itemSelector, 10);
                            if (sourceEl) {
                                var record = tree.view.getRecord(sourceEl);
                                Ext.Msg.show({
                                    title: '系统确认',
                                    message: '确定要变更该人员所属部门？',
                                    buttons: Ext.Msg.OKCANCEL,
                                    icon: Ext.Msg.QUESTION,
                                    fn: function (btn) {
                                        if (btn === 'ok') {
                                            tree.moveParentNode(record, data.draggedRecord);
                                        }
                                    }
                                });
                                return true;
                            } else {
                                return false;
                            }
                        }
                    });
                }
            },
            moveParentNode: function (node, record) {
                Ext.Ajax.request({
                    url: '/Card/TeachStudInfo/MoveTeacherOrg',
                    params: {
                        orgcode: node.get('code'),
                        infoid: record.get('uid')
                    },
                    method: 'POST',
                    success: function (response) {
                        var result = Ext.JSON.decode(response.responseText);
                        if (result.success) {
                            toast('变更成功...');
                            me.store.reload();
                        } else {
                            Ext.Msg.alert('系统信息', result.msg);
                        }
                    }
                });
            }
        });


        me.store = Ext.create('CamPus.view.card.teacher.TeacherStore', {});

        me.infoLabelStore = Ext.create('CamPus.view.card.teacher.InfoLabelComboxStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        infotype: 1
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'combobox',
                itemId: 'viewleave',
                queryMode: 'local',
                displayField: 'name',
                valueField: 'value',
                editable: false,
                value: 1,
                width: 80,
                store: Ext.create('Ext.data.Store', {
                    fields: ['value', 'name'],
                    data: [
                        { "value": -1, "name": "全部" },
                        { "value": 1, "name": "在职" },
                        { "value": 0, "name": "离职" }
                    ]
                }),
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }, {
                xtype: 'combobox',
                queryMode: 'local',
                displayField: 'name',
                valueField: 'name',
                itemId: 'infolabel',
                name: 'infolabel',
                editable: false,
                width: 110,
                value: '全部',
                store: me.infoLabelStore,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            }, {
                xtype: 'comboxdictionary',
                itemId: 'property',
                groupCode: 'SYS0000055',
                insertAll: '-1|全部',
                where: 'attr1=1',
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            }, {
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '姓名、卡号、工号、手机号关键词...'
            },
            {
                xtype: 'checkboxfield',
                boxLabel: '含子节点',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }, '->',
            {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.store.reload();
                }
            }
            ]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            //title: window.applang.get('infoname1') + '信息',
            itemId: 'TeacherGrid',
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                listeners: {
                    render: function (v) {
                        v.dragZone = Ext.create('Ext.dd.DragZone', v.getEl(), {
                            getDragData: function (e) {
                                var sourceEl = e.getTarget(v.itemSelector, 10);
                                var record = null;
                                if (sourceEl) {
                                    record = sourceEl.cloneNode(true);
                                    record.id = Ext.id();
                                    return (v.dragData = {
                                        ddel: record,
                                        sourceEl: sourceEl,
                                        repairXY: Ext.fly(sourceEl).getXY(),
                                        sourceStore: v.store,
                                        draggedRecord: v.getRecord(sourceEl)
                                    });
                                }
                            },
                            getRepairXY: function () {
                                return this.dragData.repairXY;
                            }
                        });
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '工号',
                    dataIndex: 'code',
                    width: 100,
                    sortable: true
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '手机号',
                    dataIndex: 'mobile',
                    width: 120,
                    sortable: false
                },
                {
                    text: '身份证',
                    dataIndex: 'idcard',
                    width: 130,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'card',
                    width: 130,
                    sortable: false
                },
                {
                    text: '卡序列号',
                    dataIndex: 'cardsn',
                    width: 130,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '职位',
                    dataIndex: 'positionname',
                    width: 85,
                    sortable: false
                },
                {
                    text: window.applang.get('orgname'),
                    dataIndex: 'orgname',
                    minWidth: 240,
                    flex: 1,
                    sortable: false
                },
                {
                    text: '紧急联系人',
                    dataIndex: 'linkman1',
                    width: 100,
                    sortable: false,
                    hidden: true
                },
                {
                    text: '紧急联系手机',
                    dataIndex: 'linkmobile1',
                    width: 120,
                    sortable: false,
                    hidden: true
                },
                {
                    text: '与本人关系',
                    dataIndex: 'linkdes1',
                    width: 100,
                    sortable: false,
                    hidden: true
                },
                {
                    text: '备用联系人',
                    dataIndex: 'linkman2',
                    width: 100,
                    sortable: false,
                    hidden: true
                },
                {
                    text: '备用联系手机',
                    dataIndex: 'linkmobile2',
                    width: 120,
                    sortable: false,
                    hidden: true
                },
                {
                    text: '与本人关系',
                    dataIndex: 'linkdes2',
                    width: 100,
                    sortable: false,
                    hidden: true
                },
                {
                    text: '邮箱',
                    ataIndex: 'email',
                    width: 100,
                    sortable: false,
                    hidden:true
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 70,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record.get('uid') != "root") {
                            if (value) {
                                return '正常';
                            } else {
                                metaData.style += 'color:red;';
                                return '无效';
                            }
                        }
                    }
                },
                {
                    text: '通行密码',
                    dataIndex: 'passpassword',
                    width: 100,
                    minWidth: 100,
                    sortable: false,
                    hidden: true,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return value.replace(/[0-9]/ig, '*');
                        } else {
                            return '无设置密码';
                        }
                    }
                },
                {
                    text: '人员序列号',
                    dataIndex: 'infoindex',
                    width: 100,
                    flex: 1,
                    minWidth: 100,
                    sortable: false,
                    hidden: true
                },
                {
                    text: '人员标签',
                    dataIndex: 'infolabel',
                    width: 120,
                    minWidth: 100,
                    sortable: false
                },
                {
                    text: '人员性质',
                    dataIndex: 'propertyname',
                    width: 100,
                    flex: 1,
                    minWidth: 100,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            flex: 1,
            minWidth: 390,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.tree, me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.apply(Ext.form.field.VTypes, {
    // 自定义验证函数，确保身份证号码为 18 位
    idCard: function(value) {
        return value.length === 18;
    },
    // me.getGroupStore = Ext.create('CamPus.view.card.teacher.getGroupStore', {
    //     listeners: {
    //         beforeload: function (store) {
    //             Ext.apply(store.proxy.extraParams, {
    //                 infoId:config.extenduid
    //             });
    //         },
    //         load: function(store, records, successful, operation, eOpts) {
    //             // if (successful) {
    //             //     // 假设你已经通过 Ajax 请求获取了需要回显的数据
    //             //     var groupnameField = me.down('tagfield[itemId=groupname]');
    //             //
    //             //     // 在这里将后台返回的已选中的人脸权限组值进行回显
    //             //     groupnameField.setValue('123'); // config.selectedGroups 是你要回显的值
    //             // }
    //         }
    //     }
    // });
    // 自定义验证函数的错误提示信息
    idCardText: '身份证号码必须为18位'
});

Ext.define('CamPus.view.card.teacher.TeacherEditWindow', {
    extend: 'CamPus.view.ux.Window',
    title: window.applang.get('infoname1') + '信息',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    maximizable: true,
    resizable: true,
    constructor: function (config) {
        var me = this;
        me.facegroupStore = Ext.create('CamPus.view.card.teach.PermissionGroup', {});
        me.getGroupStore = Ext.create('CamPus.view.card.teacher.getGroupStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        infoId:config.newInfoId
                    });
                },
                load: function(store, records, successful, operation, eOpts) {
                    if (successful) {
                        // 假设你已经通过 Ajax 请求获取了需要回显的数据
                        var groupnameField = me.down('tagfield[itemId=groupname]');
                        // 提取获取的记录中的 `uid` 值，生成一个数组
                        var selectedUids = records.map(function(record) {
                            return record.get('uid'); // 假设 `uid` 是需要上传的字段
                        });

                        // 使用动态提取的 `uid` 集合回显 `tagfield` 的值
                        groupnameField.setValue(selectedUids);
                    }
                }
            }
        });

        me.getFaceStore = Ext.create('CamPus.view.card.teacher.getFaceStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        infoid:config.newInfoId
                    });
                },
                load: function(store, records, successful, operation, eOpts) {
                    if (successful) {
                        // 假设你已经通过 Ajax 请求获取了需要回显的数据
                        var imgComponent = me.down('image[itemId=photo]');
                        // 提取获取的记录中的 `uid` 值，生成一个数组
                        var selectedUids = records[0].get('imgpath'); // 假设 `uid` 是需要上传的字段
                        imgComponent.setSrc(window.getResourceUrl(CamPus.AppSession.resourcedomain, selectedUids) + '?t=' + Math.random());
                    }
                }
            }
        });
        Ext.apply(me, config);
        Ext.apply(me, {
            items: [
                {
                    xtype: 'form',
                    itemId: 'form',
                    border: false,
                    bodyPadding: 10,
                    defaults: {
                        anchor: '100%',
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    layout: 'hbox', // 使用 hbox 布局
                    defaultType: 'textfield',
                    scrollable: 'y',
                    items: [
                        {
                            xtype: 'container', // 左侧容器，用于放置表单项
                            flex: 1, // 占据剩余的宽度
                            layout: 'anchor',
                            defaults: {
                                anchor: '100%',
                                labelWidth: 100,
                                labelAlign: 'right'
                            },
                            items: [
                                {
                                    xtype: 'hiddenfield',
                                    name: 'uid',
                                    itemId: 'uid'
                                },
                                {
                                    xtype: 'hiddenfield',
                                    itemId: 'dutyorg',
                                    name: 'dutyorg',
                                    value: '[]'
                                },
                                {
                                    xtype: 'hiddenfield',
                                    name: 'orgcode',
                                    value: config.orgcode
                                }, {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        fieldLabel: '所属部门',
                                        allowBlank: true,
                                        value: config.orgname,
                                        readOnly: true,
                                        submitValue: false
                                    },
                                        {
                                            xtype: 'datefield',
                                            fieldLabel: '入职日期',
                                            name: 'startdate',
                                            format: 'Y-m-d',
                                            allowBlank: true
                                        },
                                        {
                                            xtype: 'datefield',
                                            fieldLabel: '离职日期',
                                            name: 'enddate',
                                            format: 'Y-m-d',
                                            allowBlank: true
                                        }
                                    ]
                                }, {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        fieldLabel: '工号',
                                        itemId: 'code',
                                        name: 'code',
                                        allowBlank: false
                                    },
                                        {
                                            fieldLabel: '姓名',
                                            name: 'name',
                                            allowBlank: false
                                        },
                                        {
                                            fieldLabel: '手机号',
                                            name: 'mobile',
                                            allowBlank: true,
                                            maxLength: 11
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        xtype: 'textfield',
                                        fieldLabel: '身份证号码',
                                        name: 'idcard',
                                        vtype: 'idCard', // 应用自定义验证函数
                                        allowBlank: true
                                    }, {
                                        fieldLabel: '民族',
                                        itemId: 'nation',
                                        name: 'nation',
                                        allowBlank: true
                                    }, {
                                        xtype: 'radiogroup',
                                        fieldLabel: '性别',
                                        items: [
                                            {itemId: 'sex1', name: 'sex', boxLabel: '男', inputValue: 1, checked: true},
                                            {itemId: 'sex2', name: 'sex', boxLabel: '女', inputValue: 2}
                                        ]
                                    }]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        xtype: 'datefield',
                                        fieldLabel: '生日',
                                        name: 'birthday',
                                        format: 'Y-m-d',
                                        allowBlank: true
                                    },
                                        {
                                            fieldLabel: '地址',
                                            name: 'address',
                                            flex: 1,
                                            allowBlank: true
                                        },
                                        {
                                            fieldLabel: '通行密码',
                                            name: 'passpassword',
                                            itemId: 'passpassword',
                                            minLength: 4,
                                            maxLength: 8,
                                            flex: 1,
                                            allowBlank: true,
                                            maskRe: /[0-9]/,
                                            validator: function (v) {
                                                return /^[0-9]*([0-9]{1,2})?$/.test(v) ? true : '只能输入数字';
                                            },
                                            listeners: {
                                                change: function (e, text, prev) {
                                                    if (!/^[0-9]*([0-9]{0,2})?$/.test(text)) {
                                                        me.down('textfield[itemId=card]').setValue(prev);
                                                    }
                                                }
                                            }
                                        }]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [
                                        {
                                            fieldLabel: '卡号',
                                            name: 'card',
                                            itemId: 'card',
                                            maxLength: 12,
                                            flex: 1,
                                            allowBlank: true,
                                            maskRe: /[0-9]/,
                                            validator: function (v) {
                                                return /^[0-9]*([0-9]{1,2})?$/.test(v) ? true : '只能输入数字';
                                            },
                                            listeners: {
                                                change: function (e, text, prev) {
                                                    if (!/^[0-9]*([0-9]{0,2})?$/.test(text)) {
                                                        me.down('textfield[itemId=card]').setValue(prev);
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'comboxdictionary',
                                            fieldLabel: '职位',
                                            insertAll: '无',
                                            name: 'position',
                                            groupCode: 'SYS0000045',
                                            defaultSelectFirst: true,
                                            allowBlank: true
                                        },
                                        {
                                            xtype: 'comboxdictionary',
                                            fieldLabel: '职级分类',
                                            insertAll: '无',
                                            name: 'posrank',
                                            groupCode: 'SYS0000046',
                                            defaultSelectFirst: true,
                                            allowBlank: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [
                                        {
                                            fieldLabel: '邮箱',
                                            itemId: 'email',
                                            name: 'email',
                                            minLength: 12,
                                            maxLength: 25,
                                            flex: 2,
                                            allowBlank: true
                                        },
                                        {
                                            xtype: 'comboxdictionary',
                                            fieldLabel: '人员性质',
                                            name: 'property',
                                            groupCode: 'SYS0000055',
                                            where: 'attr1=1',
                                            defaultSelectFirst: true,
                                            flex: 1,
                                            allowBlank: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'tagfield',
                                    fieldLabel: '标签',
                                    itemId: 'infolabel',
                                    name: 'infolabel',
                                    flex: 2,
                                    displayField: 'label',
                                    valueField: 'label',
                                    createNewOnEnter: true,
                                    createNewOnBlur: true,
                                    autoSelect: true,
                                    forceSelection: false,
                                    filterPickList: true,
                                    queryMode: 'local',
                                    publishes: 'value',
                                    emptyText: '选择输入标签',
                                    allowBlank: true,
                                    flex: 1,
                                    store: Ext.create('CamPus.view.card.teacher.InfoLabelStore')
                                },
                                {
                                    xtype: 'fieldset',
                                    title: '人脸权限组',
                                    collapsible: true,
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    layout: 'hbox',
                                    items: [
                                        {
                                            xtype: 'tagfield',
                                            fieldLabel: '人脸权限组',
                                            itemId: 'groupname',
                                            name: 'groupname',
                                            flex: 2,
                                            displayField: 'name',
                                            valueField: 'uid',
                                            createNewOnEnter: false,
                                            createNewOnBlur: false,
                                            autoSelect: true,
                                            forceSelection: false,
                                            filterPickList: true,
                                            queryMode: 'remote',
                                            publishes: 'value',
                                            emptyText: '选择人脸权限组',
                                            allowBlank: true,
                                            store: me.facegroupStore
                                        }
                                    ]
                                },

                                {
                                    xtype: 'fieldset',
                                    title: '紧急联系人',
                                    collapsible: true,
                                    defaultType: 'textfield',
                                    collapsed: true,
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    layout: 'hbox',
                                    items: [{
                                        fieldLabel: '姓名',
                                        name: 'linkman1',
                                        allowBlank: true
                                    },
                                        {
                                            fieldLabel: '联系手机',
                                            name: 'linkmobile1',
                                            allowBlank: true
                                        },
                                        {
                                            fieldLabel: '与本人关系',
                                            name: 'linkdes1',
                                            allowBlank: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: '备用联系人',
                                    collapsible: true,
                                    defaultType: 'textfield',
                                    collapsed: true,
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    layout: 'hbox',
                                    items: [{
                                        fieldLabel: '姓名',
                                        name: 'linkman2',
                                        allowBlank: true
                                    },
                                        {
                                            fieldLabel: '联系手机',
                                            name: 'linkmobile2',
                                            allowBlank: true
                                        },
                                        {
                                            fieldLabel: '与本人关系',
                                            name: 'linkdes2',
                                            allowBlank: true
                                        }]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: []
                                },
                                {
                                    xtype: 'autoforms',
                                    title: '扩展信息',
                                    table: 'tb_card_teachstudinfo',
                                    itemId: 'extendform',
                                    extenduid: config.extenduid ? config.extenduid : '',
                                    listeners: {
                                        afterrender: function (reslut, eOpts) {
                                            if (!reslut.isShowThisForm) {
                                                reslut.setHidden(true);
                                                reslut.setDisabled(true);
                                            } else {
                                                reslut.setHidden(false);
                                                reslut.setDisabled(false);
                                            }
                                        }
                                    }
                                }]
                        },
                        {
                            xtype: 'container', // 右侧容器，用于放置照片
                            width: 160, // 固定宽度
                            layout: {
                                type: 'vbox',
                                align: 'right'
                            },
                            items: [{
                                xtype: 'image',
                                itemId: 'photo',
                                width: 150,
                                height: 150,
                                style: {
                                    border: '1px solid #ccc',
                                    'margin-left': 'auto',
                                    'margin-bottom': 'auto' // 使图片靠近右上角
                                },
                                src: config.photo ? window.getResourceUrl(CamPus.AppSession.resourcedomain, config.photo) : 'resources/images/face.png'
                            }]
                        }
                    ]
                }
            ]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/SaveTeacher',
                params: {
                    userId: me.newInfoId
                },
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        if (me.down('autoforms[itemId=extendform]').isShowThisForm) {
                            //me.down('autoforms[itemId=extendform]').Save(action.result.data, function (result) {
                            me.grid.getStore().reload({
                                callback: function () {
                                    var formValues = form.getValues();
                                    var combo = me.down('#groupname');
                                    combo.setValue(formValues.groupname);
                                }
                            });
                            me.close();
                            toast('保存成功！');
                            me.infouidstore.reload();
                            // if (me.aotuAddNew && me.ctrl) {
                            //     me.ctrl.onAddClick();
                            // }
                            //});
                        } else {
                            me.grid.getStore().reload();
                            me.close();
                            toast('保存成功！');
                            me.infouidstore.reload();
                            // if (me.aotuAddNew && me.ctrl) {
                            //     me.ctrl.onAddClick();
                            // }
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.card.teacher.TeacherAddWindow', {
    extend: 'CamPus.view.ux.Window',
    title: window.applang.get('infoname1') + '信息',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    maximizable: true,
    resizable: true,
    constructor: function (config) {
        var me = this;
        me.facegroupStore = Ext.create('CamPus.view.card.teach.PermissionGroup', {});
        Ext.apply(me, config);
        Ext.apply(me, {
            items: [
                {
                    xtype: 'form',
                    itemId: 'form',
                    border: false,
                    bodyPadding: 10,
                    defaults: {
                        anchor: '100%',
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    layout: 'hbox', // 使用 hbox 布局
                    defaultType: 'textfield',
                    scrollable: 'y',
                    items: [
                        {
                            xtype: 'container', // 左侧容器，用于放置表单项
                            flex: 1, // 占据剩余的宽度
                            layout: 'anchor',
                            defaults: {
                                anchor: '100%',
                                labelWidth: 100,
                                labelAlign: 'right'
                            },
                            items: [
                                {
                                    xtype: 'hiddenfield',
                                    name: 'uid',
                                    itemId: 'uid'
                                },
                                {
                                    xtype: 'hiddenfield',
                                    itemId: 'dutyorg',
                                    name: 'dutyorg',
                                    value: '[]'
                                },
                                {
                                    xtype: 'hiddenfield',
                                    name: 'orgcode',
                                    value: config.orgcode
                                }, {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        fieldLabel: '所属部门',
                                        allowBlank: true,
                                        value: config.orgname,
                                        readOnly: true,
                                        submitValue: false
                                    },
                                        {
                                            xtype: 'datefield',
                                            fieldLabel: '入职日期',
                                            name: 'startdate',
                                            format: 'Y-m-d',
                                            allowBlank: true
                                        },
                                        {
                                            xtype: 'datefield',
                                            fieldLabel: '离职日期',
                                            name: 'enddate',
                                            format: 'Y-m-d',
                                            allowBlank: true
                                        }
                                    ]
                                }, {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        fieldLabel: '工号',
                                        itemId: 'code',
                                        name: 'code',
                                        allowBlank: false
                                    },
                                        {
                                            fieldLabel: '姓名',
                                            name: 'name',
                                            allowBlank: false
                                        },
                                        {
                                            fieldLabel: '手机号',
                                            name: 'mobile',
                                            allowBlank: true,
                                            maxLength: 11
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        xtype: 'textfield',
                                        fieldLabel: '身份证号码',
                                        name: 'idcard',
                                        vtype: 'idCard', // 应用自定义验证函数
                                        allowBlank: true
                                    }, {
                                        fieldLabel: '民族',
                                        itemId: 'nation',
                                        name: 'nation',
                                        allowBlank: true
                                    }, {
                                        xtype: 'radiogroup',
                                        fieldLabel: '性别',
                                        items: [
                                            {itemId: 'sex1', name: 'sex', boxLabel: '男', inputValue: 1, checked: true},
                                            {itemId: 'sex2', name: 'sex', boxLabel: '女', inputValue: 2}
                                        ]
                                    }]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        xtype: 'datefield',
                                        fieldLabel: '生日',
                                        name: 'birthday',
                                        format: 'Y-m-d',
                                        allowBlank: true
                                    },
                                        {
                                            fieldLabel: '地址',
                                            name: 'address',
                                            flex: 1,
                                            allowBlank: true
                                        },
                                        {
                                            fieldLabel: '通行密码',
                                            name: 'passpassword',
                                            itemId: 'passpassword',
                                            minLength: 4,
                                            maxLength: 8,
                                            flex: 1,
                                            allowBlank: true,
                                            maskRe: /[0-9]/,
                                            validator: function (v) {
                                                return /^[0-9]*([0-9]{1,2})?$/.test(v) ? true : '只能输入数字';
                                            },
                                            listeners: {
                                                change: function (e, text, prev) {
                                                    if (!/^[0-9]*([0-9]{0,2})?$/.test(text)) {
                                                        me.down('textfield[itemId=card]').setValue(prev);
                                                    }
                                                }
                                            }
                                        }]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [
                                        {
                                            fieldLabel: '卡号',
                                            name: 'card',
                                            itemId: 'card',
                                            maxLength: 12,
                                            flex: 1,
                                            allowBlank: true,
                                            maskRe: /[0-9]/,
                                            validator: function (v) {
                                                return /^[0-9]*([0-9]{1,2})?$/.test(v) ? true : '只能输入数字';
                                            },
                                            listeners: {
                                                change: function (e, text, prev) {
                                                    if (!/^[0-9]*([0-9]{0,2})?$/.test(text)) {
                                                        me.down('textfield[itemId=card]').setValue(prev);
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'comboxdictionary',
                                            fieldLabel: '职位',
                                            insertAll: '无',
                                            name: 'position',
                                            groupCode: 'SYS0000045',
                                            defaultSelectFirst: true,
                                            allowBlank: true
                                        },
                                        {
                                            xtype: 'comboxdictionary',
                                            fieldLabel: '职级分类',
                                            insertAll: '无',
                                            name: 'posrank',
                                            groupCode: 'SYS0000046',
                                            defaultSelectFirst: true,
                                            allowBlank: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [
                                        {
                                            fieldLabel: '邮箱',
                                            itemId: 'email',
                                            name: 'email',
                                            minLength: 12,
                                            maxLength: 25,
                                            flex: 2,
                                            allowBlank: true
                                        },
                                        {
                                            xtype: 'comboxdictionary',
                                            fieldLabel: '人员性质',
                                            name: 'property',
                                            groupCode: 'SYS0000055',
                                            where: 'attr1=1',
                                            defaultSelectFirst: true,
                                            flex: 1,
                                            allowBlank: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'tagfield',
                                    fieldLabel: '标签',
                                    itemId: 'infolabel',
                                    name: 'infolabel',
                                    flex: 2,
                                    displayField: 'label',
                                    valueField: 'label',
                                    createNewOnEnter: true,
                                    createNewOnBlur: true,
                                    autoSelect: true,
                                    forceSelection: false,
                                    filterPickList: true,
                                    queryMode: 'local',
                                    publishes: 'value',
                                    emptyText: '选择输入标签',
                                    allowBlank: true,
                                    flex: 1,
                                    store: Ext.create('CamPus.view.card.teacher.InfoLabelStore')
                                },
                                {
                                    xtype: 'fieldset',
                                    title: '人脸权限组',
                                    collapsible: true,
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    layout: 'hbox',
                                    items: [
                                        {
                                            xtype: 'tagfield',
                                            fieldLabel: '人脸权限组',
                                            itemId: 'groupname',
                                            name: 'groupname',
                                            flex: 2,
                                            displayField: 'name',
                                            valueField: 'uid',
                                            createNewOnEnter: false,
                                            createNewOnBlur: false,
                                            autoSelect: true,
                                            forceSelection: false,
                                            filterPickList: true,
                                            queryMode: 'remote',
                                            publishes: 'value',
                                            emptyText: '选择人脸权限组',
                                            allowBlank: true,
                                            store: me.facegroupStore
                                        }
                                    ]
                                },

                                {
                                    xtype: 'fieldset',
                                    title: '紧急联系人',
                                    collapsible: true,
                                    defaultType: 'textfield',
                                    collapsed: true,
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    layout: 'hbox',
                                    items: [{
                                        fieldLabel: '姓名',
                                        name: 'linkman1',
                                        allowBlank: true
                                    },
                                        {
                                            fieldLabel: '联系手机',
                                            name: 'linkmobile1',
                                            allowBlank: true
                                        },
                                        {
                                            fieldLabel: '与本人关系',
                                            name: 'linkdes1',
                                            allowBlank: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: '备用联系人',
                                    collapsible: true,
                                    defaultType: 'textfield',
                                    collapsed: true,
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    layout: 'hbox',
                                    items: [{
                                        fieldLabel: '姓名',
                                        name: 'linkman2',
                                        allowBlank: true
                                    },
                                        {
                                            fieldLabel: '联系手机',
                                            name: 'linkmobile2',
                                            allowBlank: true
                                        },
                                        {
                                            fieldLabel: '与本人关系',
                                            name: 'linkdes2',
                                            allowBlank: true
                                        }]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: []
                                },
                                {
                                    xtype: 'autoforms',
                                    title: '扩展信息',
                                    table: 'tb_card_teachstudinfo',
                                    itemId: 'extendform',
                                    extenduid: config.extenduid ? config.extenduid : '',
                                    listeners: {
                                        afterrender: function (reslut, eOpts) {
                                            if (!reslut.isShowThisForm) {
                                                reslut.setHidden(true);
                                                reslut.setDisabled(true);
                                            } else {
                                                reslut.setHidden(false);
                                                reslut.setDisabled(false);
                                            }
                                        }
                                    }
                                }]
                        },
                        {
                            xtype: 'container', // 右侧容器，用于放置照片
                            width: 160, // 固定宽度
                            layout: {
                                type: 'vbox',
                                align: 'right'
                            },
                            items: [{
                                xtype: 'image',
                                itemId: 'photo',
                                width: 150,
                                height: 150,
                                style: {
                                    border: '1px solid #ccc',
                                    'margin-left': 'auto',
                                    'margin-bottom': 'auto' // 使图片靠近右上角
                                },
                                src: config.photo ? window.getResourceUrl(CamPus.AppSession.resourcedomain, config.photo) : 'resources/images/face.png'
                            }]
                        }
                        ]
                }
            ]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/SaveTeacher',
                params: {
                    userId: me.newInfoId
                },
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        if (me.down('autoforms[itemId=extendform]').isShowThisForm) {
                            //me.down('autoforms[itemId=extendform]').Save(action.result.data, function (result) {
                            me.grid.getStore().reload({
                                callback: function () {
                                    var formValues = form.getValues();
                                    var combo = me.down('#groupname');
                                    combo.setValue(formValues.groupname);
                                }
                            });
                            me.close();
                            toast('保存成功！');
                            me.infouidstore.reload();
                            // if (me.aotuAddNew && me.ctrl) {
                            //     me.ctrl.onAddClick();
                            // }
                            //});
                        } else {
                            me.grid.getStore().reload();
                            me.close();
                            toast('保存成功！');
                            me.infouidstore.reload();
                            // if (me.aotuAddNew && me.ctrl) {
                            //     me.ctrl.onAddClick();
                            // }
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


Ext.define('CamPus.view.card.teacher.ImportTeacherWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '导入' + window.applang.get('infoname1') + '档案',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    height: 360,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                },{
                    xtype: 'button',
                    text: '下载导入模板',
                    handler: function() {
                        Ext.Ajax.request({
                            url: '/Card/TeachStudInfo/ExportTemplate',
                            params: {
                                name: '职工信息导入模板'
                            },
                            method: 'POST',
                            success: function(response) {
                                Ext.get('loadingToast').hide();
                                var result = Ext.JSON.decode(response.responseText);
                                if (result.success) {
                                    if (result.msg) {
                                        openWindow(result.msg, null, 'get');
                                    } else {
                                        toast('无导出数据');
                                    }
                                } else {
                                    toast(result.msg);
                                }
                            }
                        });
                    }
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中应至少包含工号、姓名、' + window.applang.get('orgname') + '、性别(1男，2女)，其他可选；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/UploadTeachFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('上传成功，请设置数据对应列！');
                        var nextwin = Ext.create('CamPus.view.card.teacher.SetColumnWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


Ext.define('CamPus.view.card.teacher.ImportUpdateWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '更新' + window.applang.get('infoname1') + '档案',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    height: 360,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                }, {
                    xtype: 'button',
                    text: '下载导入模板',
                    handler: function() {
                        openWindow("/excelTemplate/职工信息导入模板.xlsx", null, 'get');
                    }
                },{
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中应至少包含工号、身份证号，其他可选；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/uploadUpdateExcel',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('上传成功，请设置数据对应列！');
                        var nextwin = Ext.create('CamPus.view.card.teacher.SetUpdateExcelColumnWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.card.teacher.SetColumnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置导入对应列',
    width: 550,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('Ext.data.Store', {
            fields: ['column', 'value', 'columnname', 'columncode'],
            data: config.data
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            border: false,
            flex: 1,
            store: me.store,
            columns: [{
                text: '列序号',
                dataIndex: 'column',
                width: 65
            },
                {
                    text: '列名',
                    dataIndex: 'value',
                    flex: 1
                },
                {
                    text: '对应列名',
                    dataIndex: 'columncode',
                    flex: 1,
                    sortable: false,
                    editor: {
                        xtype: 'combobox',
                        queryMode: 'local',
                        displayField: 'name',
                        valueField: 'value',
                        editable: false,
                        store: Ext.create('Ext.data.Store', {
                            fields: ['name', 'value'],
                            data: [
                                { name: "忽略", value: '' },
                                { name: "工号", value: 'code' },
                                { name: "姓名", value: 'name' },
                                { name: "卡序列号", value: 'cardsn' },
                                { name: "性别(1男2女)", value: 'sex' },
                                { name: "职位编号", value: 'position' },
                                { name: "职级编号", value: 'posrank' },
                                {name: "入职日期", value: 'startdate'},
                                {name: "离职日期", value: 'enddate'},
                                { name: "手机号", value: 'mobile' },
                                { name: "邮箱", value: 'email' },
                                { name: window.applang.get('orgname') + "代码", value: 'orgcode' },
                                { name: "身份证号", value: 'idcard' },
                                { name: "民族", value: 'nation' },
                                { name: "生日(长度为10的日期)", value: 'birthday' },
                                { name: "地址", value: 'address' },
                                { name: "通行密码", value: 'passpassword' },
                                { name: "紧急联系人", value: 'linkman1' },
                                { name: "紧急联系人手机号", value: 'linkmobile1' },
                                { name: "与本人关系", value: 'linkdes1' },
                                { name: "备用联系人", value: 'linkman2' },
                                { name: "备用联系电话", value: 'linkmobile2' },
                                { name: "备用联系人与本人关系", value: 'linkdes2' },
                                { name: "标签", value: 'infolabel' },
                                { name: "人员性质", value: 'property' }
                            ]
                        }),
                        listeners: {
                            change: function (combox, newValue, oldValue, eOpts) {
                                if (newValue) {
                                    var records = me.grid.getStore().getData().items;
                                    var exists = false;
                                    Ext.each(records, function (record, i) {
                                        if (newValue && record.get('columncode') && record.get('columncode') == newValue) {
                                            exists = true;
                                        }
                                    });
                                    if (!exists) {
                                        combox.ownerCt.context.record.set('columncode', newValue);
                                        combox.ownerCt.context.record.set('columnname', combox.getRawValue());
                                    } else {
                                        toast('禁止多列对应一列');
                                        return false;
                                    }
                                } else {
                                    combox.ownerCt.context.record.set('columncode', '');
                                    combox.ownerCt.context.record.set('columnname', '');
                                }
                            }
                        }
                    },
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record) {
                            return record.get('columnname');
                        }
                    }
                }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'newlistplugin'
            }
        });

        Ext.apply(me, {
            fbar: ['->',
                {
                    xtype: 'button',
                    text: '确定',
                    listeners: {
                        click: function (button, event) {
                            me.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    listeners: {
                        click: function (button, event) {
                            me.close();
                        }
                    }
                }
            ],
            items: [me.grid, {
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                hidden: true,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    itemId: 'excelpath',
                    name: 'excelpath',
                    value: config.excelpath,
                    allowBlank: false
                }, {
                    itemId: 'columncfg',
                    name: 'columncfg',
                    allowBlank: false
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.grid.getStore().getData().items;
        var columncfg = [];
        Ext.each(records, function (record, i) {
            if (record.get('columncode')) {
                columncfg.push({
                    columncode: record.get('columncode'),
                    column: record.get('column')
                });
            }
        });
        if (columncfg.length == 0) {
            toast('请设置文档列对应关系');
            return;
        }
        var form = me.down('form[itemId=form]');
        form.down('textfield[itemId=columncfg]').setValue(JSON.stringify(columncfg));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/ImportTeacherFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        me.maingrid.getStore().loadPage(1);
                        if (action.result.msg) {
                            Ext.MessageBox.show({
                                title: '系统信息',
                                msg: '部分数据未成功导入，是否下载清单?',
                                buttons: Ext.MessageBox.YESNOCANCEL,
                                icon: Ext.MessageBox.QUESTION,
                                fn: function (btn, text) {
                                    if (btn == 'yes') {
                                        openWindow(action.result.msg, null, 'get');
                                    }
                                }
                            });
                        } else {
                            toast('数据导入成功！');
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


Ext.define('CamPus.view.card.teacher.SetUpdateExcelColumnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置导入对应列',
    width: 550,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('Ext.data.Store', {
            fields: ['column', 'value', 'columnname', 'columncode'],
            data: config.data
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            border: false,
            flex: 1,
            store: me.store,
            columns: [{
                text: '列序号',
                dataIndex: 'column',
                width: 65
            },
            {
                text: '列名',
                dataIndex: 'value',
                flex: 1
            },
            {
                text: '对应列名',
                dataIndex: 'columncode',
                flex: 1,
                sortable: false,
                editor: {
                    xtype: 'combobox',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'value',
                    editable: false,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['name', 'value'],
                        data: [
                            { name: "忽略", value: '' },
                            { name: "工号", value: 'code' },
                            { name: "姓名", value: 'name' },
                            { name: "卡序列号", value: 'cardsn' },
                            { name: "性别(1男2女)", value: 'sex' },
                            { name: "职位编号", value: 'position' },
                            { name: "职级编号", value: 'posrank' },
                            { name: "手机号", value: 'mobile' },
                            { name: "邮箱", value: 'email' },
                            { name: window.applang.get('orgname') + "代码", value: 'orgcode' },
                            { name: "身份证号", value: 'idcard' },
                            { name: "民族", value: 'nation' },
                            { name: "生日(长度为10的日期)", value: 'birthday' },
                            { name: "地址", value: 'address' },
                            { name: "通行密码", value: 'passpassword' },
                            { name: "紧急联系人", value: 'linkman1' },
                            { name: "紧急联系人手机号", value: 'linkmobile1' },
                            { name: "与本人关系", value: 'linkdes1' },
                            { name: "备用联系人", value: 'linkman2' },
                            { name: "备用联系电话", value: 'linkmobile2' },
                            { name: "备用联系人与本人关系", value: 'linkdes2' },
                            { name: "标签", value: 'infolabel' },
                            { name: "人员性质", value: 'property' }
                        ]
                    }),
                    listeners: {
                        change: function (combox, newValue, oldValue, eOpts) {
                            if (newValue) {
                                var records = me.grid.getStore().getData().items;
                                var exists = false;
                                Ext.each(records, function (record, i) {
                                    if (newValue && record.get('columncode') && record.get('columncode') == newValue) {
                                        exists = true;
                                    }
                                });
                                if (!exists) {
                                    combox.ownerCt.context.record.set('columncode', newValue);
                                    combox.ownerCt.context.record.set('columnname', combox.getRawValue());
                                } else {
                                    toast('禁止多列对应一列');
                                    return false;
                                }
                            } else {
                                combox.ownerCt.context.record.set('columncode', '');
                                combox.ownerCt.context.record.set('columnname', '');
                            }
                        }
                    }
                },
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    if (record) {
                        return record.get('columnname');
                    }
                }
            }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'newlistplugin'
            }
        });

        Ext.apply(me, {
            fbar: ['->',
                {
                    xtype: 'button',
                    text: '确定',
                    listeners: {
                        click: function (button, event) {
                            me.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    listeners: {
                        click: function (button, event) {
                            me.close();
                        }
                    }
                }
            ],
            items: [me.grid, {
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                hidden: true,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    itemId: 'excelpath',
                    name: 'excelpath',
                    value: config.excelpath,
                    allowBlank: false
                }, {
                    itemId: 'columncfg',
                    name: 'columncfg',
                    allowBlank: false
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.grid.getStore().getData().items;
        var columncfg = [];
        Ext.each(records, function (record, i) {
            if (record.get('columncode')) {
                columncfg.push({
                    columncode: record.get('columncode'),
                    column: record.get('column')
                });
            }
        });
        if (columncfg.length == 0) {
            toast('请设置文档列对应关系');
            return;
        }
        var form = me.down('form[itemId=form]');
        form.down('textfield[itemId=columncfg]').setValue(JSON.stringify(columncfg));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/updatePersonInfoByExcel',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        me.maingrid.getStore().loadPage(1);
                        if (action.result.msg) {
                            Ext.MessageBox.show({
                                title: '系统信息',
                                msg: '导入数据有误，无法更新。是否查看详细信息?',
                                buttons: Ext.MessageBox.YESNOCANCEL,
                                icon: Ext.MessageBox.QUESTION,
                                fn: function (btn, text) {
                                    if (btn == 'yes') {
                                        openWindow(action.result.msg, null, 'get');
                                    }
                                }
                            });
                        } else {
                            toast('数据导入成功！');
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.card.teacher.ImportMoneyWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '导入员工充值金额',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    height: 360,
    constructor: function(config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                },{
                    xtype: 'button',
                    text: '下载导入模板',
                    handler: function() {
                        Ext.Ajax.request({
                            url: '/Card/TeachStudInfo/ExportTeachTemplate',
                            params: {
                                name: '员工充值金额导入模板'
                            },
                            method: 'POST',
                            success: function(response) {
                                Ext.get('loadingToast').hide();
                                var result = Ext.JSON.decode(response.responseText);
                                if (result.success) {
                                    if (result.msg) {
                                        openWindow(result.msg, null, 'get');
                                    } else {
                                        toast('无导出数据');
                                    }
                                } else {
                                    toast(result.msg);
                                }
                            }
                        });
                    }
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中应至少包含工号、姓名、性别(1男，2女)、充值金额，其他可选；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }]
            }]
        });
        me.callParent(arguments);
    },

    Save: function() {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/UploadImportMoneyFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function(form, action) {
                    if (action.result.success) {
                        toast('上传成功，请设置数据对应列！');
                        var nextwin = Ext.create('CamPus.view.card.teacher.SetImportMoneyColumnWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function(form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});
Ext.define('CamPus.view.card.teacher.SetImportMoneyColumnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置导入对应列',
    width: 550,
    constructor: function(config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('Ext.data.Store', {
            fields: ['column', 'value', 'columnname', 'columncode'],
            data: config.data
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            border: false,
            flex: 1,
            store: me.store,
            columns: [{
                text: '列序号',
                dataIndex: 'column',
                width: 65
            },
                {
                    text: '列名',
                    dataIndex: 'value',
                    flex: 1
                },
                {
                    text: '对应列名',
                    dataIndex: 'columncode',
                    flex: 1,
                    sortable: false,
                    editor: {
                        xtype: 'combobox',
                        queryMode: 'local',
                        displayField: 'name',
                        valueField: 'value',
                        editable: false,
                        store: Ext.create('Ext.data.Store', {
                            fields: ['name', 'value'],
                            data: [
                                { name: "忽略", value: '' },
                                { name: "工号", value: 'code' },
                                { name: "姓名", value: 'name' },
                                { name: "性别(1男2女)", value: 'sex' },
                                { name: "充值金额", value: 'money' },
                            ]
                        }),
                        listeners: {
                            change: function(combox, newValue, oldValue, eOpts) {
                                if (newValue) {
                                    var records = me.grid.getStore().getData().items;
                                    var exists = false;
                                    Ext.each(records, function(record, i) {
                                        if (newValue && record.get('columncode') && record.get('columncode') == newValue) {
                                            exists = true;
                                        }
                                    });
                                    if (!exists) {
                                        combox.ownerCt.context.record.set('columncode', newValue);
                                        combox.ownerCt.context.record.set('columnname', combox.getRawValue());
                                    } else {
                                        toast('禁止多列对应一列');
                                        return false;
                                    }
                                } else {
                                    combox.ownerCt.context.record.set('columncode', '');
                                    combox.ownerCt.context.record.set('columnname', '');
                                }
                            }
                        }
                    },
                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record) {
                            return record.get('columnname');
                        }
                    }
                }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'newlistplugin'
            }
        });

        Ext.apply(me, {
            fbar: ['->',
                {
                    xtype: 'button',
                    text: '确定',
                    listeners: {
                        click: function(button, event) {
                            me.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    listeners: {
                        click: function(button, event) {
                            me.close();
                        }
                    }
                }
            ],
            items: [me.grid, {
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                hidden: true,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    itemId: 'excelpath',
                    name: 'excelpath',
                    value: config.excelpath,
                    allowBlank: false
                }, {
                    itemId: 'columncfg',
                    name: 'columncfg',
                    allowBlank: false
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function() {
        var me = this;
        var records = me.grid.getStore().getData().items;
        var columncfg = [];
        Ext.each(records, function(record, i) {
            if (record.get('columncode')) {
                columncfg.push({
                    columncode: record.get('columncode'),
                    column: record.get('column')
                });
            }
        });
        if (columncfg.length == 0) {
            toast('请设置文档列对应关系');
            return;
        }
        var form = me.down('form[itemId=form]');
        form.down('textfield[itemId=columncfg]').setValue(JSON.stringify(columncfg));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/ImportTeachMoneyFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function(form, action) {
                    if (action.result.success) {
                        me.close();
                        me.maingrid.getStore().loadPage(1);
                        if(action.result.msg){
                            Ext.MessageBox.show({
                                title:'系统信息',
                                msg: '部分数据未成功导入，是否下载清单?',
                                buttons: Ext.MessageBox.YESNOCANCEL,
                                icon: Ext.MessageBox.QUESTION,
                                fn:function(btn, text){
                                    if(btn=='yes'){
                                        openWindow(action.result.msg, null, 'get');
                                    }
                                }
                            });
                        }else{
                            toast('数据导入成功！');
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function(form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


Ext.define('CamPus.view.card.teacher.ChangeTeacherOrgWindow', {
    extend: 'CamPus.view.ux.Window',
    title: window.applang.get('infoname1') + '调整' + window.applang.get('orgname'),
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.card.teacher.OrgTreeStore', {

        });

        var collegetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get('orgname') + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: collegetbar,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            columnLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false,
                editor: {
                    xtype: 'textfield',
                    allowBlank: false
                }
            }],
            setPathName: function (node) {
                this.pathName = (this.pathName ? node.get('name') + '/' + this.pathName : node.get('name'));
                if (node.parentNode && node.parentNode.id != 'root') {
                    this.setPathName(node.parentNode);
                }
            }
        });

        Ext.apply(me, {
            items: [me.tree]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.tree.getSelection().length == 0) {
            toast('请选择新的' + window.applang.get('orgname') + '...');
            return;
        }
        var node = me.tree.getSelection()[0];

        me.tree.pathName = '';
        me.tree.setPathName(node);
        var orgcode = node.get('code');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要将所选' + window.applang.get('infoname1') + '调整至【' + me.tree.pathName + '】 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/MoveTeacherOrg',
                        params: {
                            orgcode: orgcode,
                            infoid: me.infoid,
                            fromOrgs: me.fromOrgs
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.close();
                                toast('调整成功成功...');
                                me.maingrid.getStore().reload();
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    }
});



Ext.define('CamPus.view.card.teacher.ImportLeaveTeacherWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '导入离职' + window.applang.get('infoname1') + '档案信息',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    height: 360,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中应至少包含离职人员的工号；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;',
                    fieldStyle: 'color:red;font-weight: bold;'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/UploadLeavelTeachFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('上传成功，请设置数据对应列！');
                        var nextwin = Ext.create('CamPus.view.card.teacher.SetLeavelColumnWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});



Ext.define('CamPus.view.card.teacher.SetLeavelColumnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置导入对应列',
    width: 550,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('Ext.data.Store', {
            fields: ['column', 'value', 'columnname', 'columncode'],
            data: config.data
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            border: false,
            flex: 1,
            store: me.store,
            columns: [{
                text: '列序号',
                dataIndex: 'column',
                width: 65
            },
            {
                text: '列名',
                dataIndex: 'value',
                flex: 1
            },
            {
                text: '对应列名',
                dataIndex: 'columncode',
                flex: 1,
                sortable: false,
                editor: {
                    xtype: 'combobox',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'value',
                    editable: false,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['name', 'value'],
                        data: [
                            { name: "忽略", value: '' },
                            { name: "工号", value: 'code' }
                        ]
                    }),
                    listeners: {
                        change: function (combox, newValue, oldValue, eOpts) {
                            if (newValue) {
                                var records = me.grid.getStore().getData().items;
                                var exists = false;
                                Ext.each(records, function (record, i) {
                                    if (newValue && record.get('columncode') && record.get('columncode') == newValue) {
                                        exists = true;
                                    }
                                });
                                if (!exists) {
                                    combox.ownerCt.context.record.set('columncode', newValue);
                                    combox.ownerCt.context.record.set('columnname', combox.getRawValue());
                                } else {
                                    toast('禁止多列对应一列');
                                    return false;
                                }
                            } else {
                                combox.ownerCt.context.record.set('columncode', '');
                                combox.ownerCt.context.record.set('columnname', '');
                            }
                        }
                    }
                },
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    if (record) {
                        return record.get('columnname');
                    }
                }
            }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'newlistplugin'
            }
        });

        Ext.apply(me, {
            fbar: ['->',
                {
                    xtype: 'button',
                    text: '确定',
                    listeners: {
                        click: function (button, event) {
                            me.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    listeners: {
                        click: function (button, event) {
                            me.close();
                        }
                    }
                }
            ],
            items: [me.grid, {
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                hidden: true,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    itemId: 'excelpath',
                    name: 'excelpath',
                    value: config.excelpath,
                    allowBlank: false
                }, {
                    itemId: 'columncfg',
                    name: 'columncfg',
                    allowBlank: false
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.grid.getStore().getData().items;
        var columncfg = [];
        Ext.each(records, function (record, i) {
            if (record.get('columncode')) {
                columncfg.push({
                    columncode: record.get('columncode'),
                    column: record.get('column')
                });
            }
        });
        if (columncfg.length == 0) {
            toast('请设置文档列对应关系');
            return;
        }
        var form = me.down('form[itemId=form]');
        form.down('textfield[itemId=columncfg]').setValue(JSON.stringify(columncfg));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/ImportLeavelTeacherFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        me.maingrid.getStore().loadPage(1);
                        toast(action.result.msg);
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


Ext.define('CamPus.view.card.teacher.SelectChildInfoWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择要关注的下属人员',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.card.teacher.CollegeClassTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get("orgname3") + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            columnLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function (tree, record, index, eOpts) {
                    me.store.loadPage(1);
                }
            }
        });

        me.store = Ext.create('CamPus.view.card.teacher.ChildTechInfoStore', {
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });

                    Ext.apply(store.proxy.extraParams, {
                        orgcode: orgcode,
                        infotype: infotype.getValue(),
                        viewchild: viewchild.getValue(),
                        selecteduid: selecteduid.join(','),
                        parentuids: me.parentuids
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'comboxdictionary',
                    itemId: 'infotype',
                    groupCode: 'SYS0000019',
                    width: 110,
                    defaultSelectFirst: true,
                    listeners: {
                        change: function (combox, newValue, oldValue, eOpts) {
                            var url = '/Card/OrgFramework/getCollegeClassTree';
                            if (newValue == '1') {
                                url = '/Card/OrgFramework/getOrgTree';
                            }
                            me.treestore.proxy.url = url;
                            me.treestore.load();
                            me.store.load();
                        }
                    }
                },
                {
                    xtype: 'searchfield',
                    itemId: 'searchKey',
                    width: 150,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.store,
                    emptyText: '关键词...'
                },
                {
                    xtype: 'checkboxfield',
                    boxLabel: '显示子节点人员',
                    inputValue: 1,
                    itemId: 'viewchild',
                    checked: false,
                    listeners: {
                        change: function () {
                            me.store.reload();
                        }
                    }
                }
            ]
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '职位',
                    dataIndex: 'positionname',
                    width: 120,
                    sortable: false
                },
                {
                    text: '职位分类',
                    dataIndex: 'posrankname',
                    width: 120,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            },
            selModel: 'checkboxmodel'
        });

        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选人员：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.store.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['card', 'cardsn', 'name', 'infotype', 'uid', 'code', 'orgname'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 120,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    minWidth: 200,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 220
        });

        Ext.apply(me, {
            items: [me.tree, me.grid, me.selectgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.selectgrid.getStore().getData().items.length == 0) {
            toast('请选择人员信息，选择以后拖拽至右侧表格');
            return;
        }

        var infoids = [];
        Ext.each(me.selectgrid.getStore().getData().items, function (record, i) {
            infoids.push(record.get('uid'));
        });

        Ext.Ajax.request({
            url: '/Card/TeachStudInfo/SaveInfoChilds',
            params: {
                parentuids: me.parentuids,
                childuids: infoids.join(',')
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.close();
                    toast('操作成功');
                } else {
                    toast(result.msg);
                }
            }
        });
    }
});



Ext.define('CamPus.view.card.teacher.ChildInfoManageWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    constructor: function (config) {
        var me = this;

        Ext.apply(me, config);

        me.parentgridstore = Ext.create('CamPus.view.card.teacher.parentInfoStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        uids: me.parentuids
                    });
                }
            }
        });

        var parenttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'searchfield',
                    flex: 1,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.parentgridstore,
                    emptyText: '姓名、卡号、工号、手机号关键词...'
                }
            ]
        });

        me.parentgrid = Ext.create('Ext.grid.Panel', {
            title: '领导信息',
            tbar: parenttbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.parentgridstore,
            viewConfig: {
                enableTextSelection: true,
                getRowClass: function () {
                    return 'x-selectable';
                },
                // listeners: {
                //     refresh: function (view, eOpts) {
                //         view.select(0);
                //     }
                // }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '工号',
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '职位',
                    dataIndex: 'positionname',
                    width: 85,
                    sortable: false
                },
                {
                    text: '职位类型',
                    dataIndex: 'positionname',
                    width: 85,
                    minWidth: 85,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            minWidth: 390,
            width: 500,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            },
            listeners: {
                select: function () {
                    me.childgridstore.loadPage(1);
                }
            }
        });


        me.childgridstore = Ext.create('CamPus.view.card.teacher.parentChildInfoStore', {
            listeners: {
                beforeload: function (store) {
                    var parendid = '';
                    if (me.parentgrid.getSelection().length > 0) {
                        parendid = me.parentgrid.getSelection()[0].get('uid');
                    }
                    Ext.apply(store.proxy.extraParams, {
                        parendid: parendid
                    });
                }
            }
        });

        var childtbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'searchfield',
                    width: 300,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.childgridstore,
                    emptyText: '姓名、卡号、工号关键词...'
                },
                '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.childgridstore.reload();
                    }
                },
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-trash-o',
                    text: '删除',
                    handler: function () {
                        me.removeChildInfo();
                    }
                }
            ]
        });

        me.childgrid = Ext.create('Ext.grid.Panel', {
            title: '下属信息',
            tbar: childtbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.childgridstore,
            viewConfig: {
                enableTextSelection: true,
                getRowClass: function () {
                    return 'x-selectable';
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '工号',
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '手机号',
                    dataIndex: 'mobile',
                    width: 120,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'card',
                    width: 130,
                    sortable: false
                },
                {
                    text: '职位',
                    dataIndex: 'positionname',
                    width: 85,
                    sortable: false
                },
                {
                    text: window.applang.get('orgname'),
                    dataIndex: 'orgname',
                    minWidth: 240,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(me, {
            items: [me.parentgrid, me.childgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        me.close();
    },
    removeChildInfo: function () {
        var me = this;
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要将所选下属从领导名下移除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    if (me.childgrid.getSelection().length == 0) {
                        toast('请选择下属信息...');
                        return;
                    }
                    var uids = [];
                    Ext.each(me.childgrid.getSelection(), function (record, index) {
                        uids.push(record.get('uid'));
                    });
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/RemoveChildInfo',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.childgrid.getStore().reload();
                                toast('操作成功');
                            } else {
                                toast(result.msg);
                            }
                        }
                    });
                }
            }
        });
    }
});

Ext.define('CamPus.view.card.teacher.uploadFaceWindows', {
    extend: 'CamPus.view.ux.Window',
    title: '人脸采集',
    defaults: {
        collapsible: false,
        split: true
    },
    maximizable: false,
    step: 1,
    width: 500,
    constructor: function (config) {
        var me = this;

        Ext.apply(me, config);

        if (typeof (useractive) != 'undefined') {
            window.changefaceimg = function (identid, imgbase64) {
                me.down('image[itemId=faceImg' + identid + ']').setSrc('data:image/png;base64,' + imgbase64);
                var faceuid = me.down('hiddenfield[name=uid' + identid + ']').getValue();
                Ext.get('loadingToast').show();
                Ext.Ajax.request({
                    url: '/Face/IntoFace/SubmitBase64Face',
                    params: {
                        imgbase64: imgbase64,
                        infoid: me.uid,
                        faceuid: faceuid
                    },
                    method: 'POST',
                    success: function (response) {
                        Ext.get('loadingToast').hide();
                        var result = Ext.JSON.decode(response.responseText);
                        if (result.success) {
                            var uid = result.data.uid;
                            var imgurl = window.getResourceUrl(CamPus.AppSession.resourcedomain, result.data.imgurl);
                            me.down('hiddenfield[name=uid' + identid + ']').setValue(uid);
                            me.down('image[itemId=faceImg' + identid + ']').setSrc(imgurl);
                        } else {
                            Ext.Msg.alert('系统信息', result.msg);
                        }
                    }
                });
            }
        }

        me.faceImage = Ext.create('Ext.panel.Panel', {
            layout: {
                type: 'vbox',
                pack: 'start',
                align: 'stretch'
            },
            items: [
                {
                    xtype: 'panel',
                    layout: {
                        type: 'hbox',
                        pack: 'start',
                        align: 'center'
                    },
                    defaults: {
                        height: 370,
                        header: false,
                        flex: 1,
                        border: false,
                        margin: '10 15 0 100'
                    },
                    header: false,
                    flex: 1,
                    items: [
                        {
                            xtype: 'panel',
                            items: [
                                {
                                    xtype: 'image',
                                    itemId: 'faceImg1',
                                    src: 'resources/images/face.png',
                                    width: 300
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'panel',
                    layout: {
                        type: 'hbox',
                        pack: 'start',
                        align: 'middle'
                    },
                    height: 80,
                    defaults: {
                        flex: 1,
                        margin: '0 20 0 20'
                    },
                    items: [
                        {
                            xtype: 'panel',
                            layout: {
                                type: 'hbox',
                                pack: 'start',
                                align: 'middle'
                            },
                            defaults: {
                                flex: 1
                            },
                            items: [
                                {
                                    xtype: 'hiddenfield',
                                    name: 'uid1'
                                },
                                {
                                    xtype: 'button',
                                    text: '上传',
                                    listeners: {
                                        click: function () {
                                            var uid = me.down('hiddenfield[name=uid1]').getValue();
                                            var uploadWin = Ext.create('CamPus.view.face.intoface.uploadWindows', {
                                                uid: uid,
                                                infoid: me.uid,
                                                parentsWin: me
                                            });
                                            uploadWin.show();
                                        }
                                    }
                                },
                                {
                                    xtype: 'button',
                                    text: '拍照',
                                    listeners: {
                                        click: function () {
                                            if (typeof (useractive) != 'undefined') {
                                                useractive.camera('window.changefaceimg', '1');
                                            } else {
                                                toast('请从专用浏览器进入本系统');
                                            }
                                        }
                                    }
                                },
                                {
                                    xtype: 'button',
                                    text: '删除',
                                    listeners: {
                                        click: function () {
                                            var img1 = me.down('image[itemId=faceImg1]');
                                            if (img1.getSrc() === 'resources/images/face.png') {
                                                return toast('无照片可删除');
                                            }
                                            var faceuid = me.down('hiddenfield[name=uid1]');
                                            me.delImg(faceuid, img1);
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        })

        Ext.apply(me, {
            items: [
                me.faceImage
            ]
        });
        me.callParent(arguments);
    },
    delImg: function (faceuid, img) {
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Face/IntoFace/delImg',
                        params: {
                            uid: faceuid.getValue()
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                if (result.msg === "del") {
                                    img.setSrc('resources/images/face.png');
                                    faceuid.setValue('');
                                    toast('删除成功...');
                                } else if (result.msg === "update") {
                                    toast('操作成功！设备图片待删除');
                                }
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    InitInfoFace: function () {
        var me = this;
        Ext.Ajax.request({
            url: '/Face/IntoFace/getFirstImg',
            params: {
                infoid: me.uid
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    Ext.each(result.data, function (item, index) {
                        for (var i = 1; i <= 3; i++) {
                            var img = me.down('image[itemId=faceImg' + i + ']');
                            var setuid = me.down('hiddenfield[name=uid' + i + ']');
                            if (img.getSrc() == 'resources/images/face.png') {
                                setuid.setValue(item.uid);
                                img.setSrc(window.getResourceUrl(CamPus.AppSession.resourcedomain, item.imgpath));
                                break;
                            }
                        }
                    });
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    },
    InitHandle: function (index) {
        var me = this;
        if (index > this.handleData.length - 1) {
            toast('已选择的人员处理完毕...');
            me.close();
            return;
        }
        this.handleIndex = index;
        var record = this.handleData[this.handleIndex];
        me.uid = record.get('uid');
        me.setTitle(record.get('name') + '——人脸采集');
        this.down('progress[itemId=cardprogress]').setValue(this.handleIndex / this.handleData.length);
        var txt = Ext.String.format('已处理{0}人，待处理{1}人', this.handleIndex, this.handleData.length - this.handleIndex);
        this.down('progress[itemId=cardprogress]').setText(txt);

        me.down('image[itemId=faceImg1]').setSrc('resources/images/face.png');
        me.down('hiddenfield[name=uid1]').setValue('');

        me.InitInfoFace();
    },
    NextInfo: function () {
        var me = this;
        me.InitHandle(me.handleIndex + 1);
    }
});
