Ext.define('CamPus.view.card.position.positionView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.positionView",
    controller: 'positionController',
    requires: [
        'CamPus.view.card.position.positionStore',
        'CamPus.view.card.position.positionController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.positionStore = Ext.create('CamPus.view.card.position.positionStore', {});

        var positionbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.positionStore,
                emptyText: '职位标签关键词'
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            if (item.itemId === 'add' || item.itemId === 'edit' || item.itemId === 'del') {
                positionbar.addMaxItems(item, 1);
            }
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            itemId: 'positionlist',
            multiColumnSort: true,
            border: false,
            region: 'west',
            //title: '补贴方案',
            collapsible: false,
            rowLines: true,
            columnLines: true,
            tbar: positionbar,
            store: me.positionStore,
            selModel: 'rowmodel',
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {
                    xtype: 'rownumberer'
                },
                {
                    text: '标签名称',
                    dataIndex: 'name',
                    flex: 1,
                    sortable: false
                }
            ],
            width: 200,
            minWidth: 150,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.groupStore = Ext.create('CamPus.view.card.position.GroupStore', {});

        var groupbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.groupStore,
                emptyText: '姓名、' + window.applang.get("infocode") + '、卡号关键词...'
            }, '->', {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.groupStore.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            if (item.itemId === 'addinfo' || item.itemId === 'deleteinfo' || item.itemId === 'clearvice' || item.itemId === 'importExcel') {
                groupbar.addMaxItems(item, config.maxopbutton);
            }
        });

        me.groupgrid = Ext.create('Ext.grid.Panel', {
            itemId: 'detaillist',
            multiColumnSort: true,
            border: false,
            region: 'center',
            //title: '名单',
            rowLines: true,
            columnLines: true,
            collapsible: false,
            tbar: groupbar,
            store: me.groupStore,
            selModel: 'checkboxmodel',
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{ xtype: 'rownumberer' },
            {
                text: window.applang.get("infocode"),
                dataIndex: 'code',
                width: 80,
                sortable: false,
                tdCls: 'vertical-align-middle'
            },
            {
                text: '姓名',
                dataIndex: 'name',
                width: 100,
                sortable: false
            },
            {
                text: '性别',
                dataIndex: 'sex',
                width: 65,
                sortable: false,
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    if (value == 1) {
                        return '男';
                    } else if (value == 2) {
                        return '女';
                    }
                }
            },
            {
                text: '所属单位',
                dataIndex: 'orgname',
                width: 200,
                sortable: false
            }
            ],
            flex: 1,
            minWidth: 390,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [
                me.grid, me.groupgrid
            ]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.card.position.editViceWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '新增职位标签',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 540,
    height: 320,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'placeform',
                border: false,
                scrollable: true,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'uid'
                }, {
                    fieldLabel: '职位名称',
                    itemId: 'name',
                    name: 'name',
                    allowBlank: false
                }
                ]
            }]
        });
        me.callParent(arguments);

    },

    Save: function () {
        var me = this;
        var form = me.down('form[itemId=placeform]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/Saveposition',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.grid.getStore().reload();
                        me.close();
                        toast('保存成功！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }

});

Ext.define('CamPus.view.card.position.AddGroupnamelistWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择人员',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.consume.group.OrgTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get('orgname3') + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function () {
                    me.store.loadPage(1);
                }
            }
        });

        me.store = Ext.create('CamPus.view.card.position.InfoListStore', {
            pageSize: 50,
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });

                    Ext.apply(store.proxy.extraParams, {
                        orgcode: orgcode,
                        intoyear: !intoyear.getValue() ? '0' : intoyear.getValue(),
                        infotype: infotype.getValue(),
                        viewchild: viewchild.getValue(),
                        selecteduid: JSON.stringify(selecteduid),
                        groupid: me.groupid
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'infotype',
                groupCode: 'SYS0000019',
                insertAll: '0|全部',
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                        var url = '/Card/OrgFramework/getCollegeClassTree';
                        if (newValue == '2') {
                            intoyear.show();
                        } else {
                            intoyear.setValue(0);
                            intoyear.hide();
                            url = '/Card/OrgFramework/getOrgTree';
                        }
                        me.treestore.proxy.url = url;
                        me.treestore.load();
                        me.store.load();
                    }
                }
            }, {
                xtype: 'comboxdictionary',
                itemId: 'intoyear',
                groupCode: 'SYS0000004',
                insertAll: '全部',
                width: 110,
                defaultSelectFirst: true,
                hidden: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            }, {
                xtype: 'searchfield',
                width: 150,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '关键词...'
            }, {
                xtype: 'checkboxfield',
                boxLabel: '显示子节点人员',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }]
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选人员：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.store.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['card', 'cardsn', 'name', 'infotype', 'uid', 'code', 'orgname', 'orgcode'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 220
        });

        Ext.apply(me, {
            items: [me.tree, me.selectgrid, me.grid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.selectgrid.getStore().getData().items.length === 0) {
            toast('请选择人员信息，选择以后拖拽至右侧表格');
            return;
        }
        var infoids = [];
        Ext.each(me.selectgrid.getStore().getData().items, function (item, index) {
            infoids.push(item.get('uid'));
        });
        Ext.Ajax.request({
            url: '/Card/TeachStudInfo/addpositiondetail',
            params: {
                name: me.name,
                infoids: infoids.join(',')
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.groupgrid.getStore().reload();
                    toast('添加成功...');
                    me.close();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});

Ext.define('CamPus.view.card.position.choseSessionWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择补贴周期',
    width: 350,
    height: 150,
    defaults: {
        collapsible: false,
        split: true
    },
    maximizable: false,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right',
                    labelWidth: 100
                },
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'comboxlist',
                        name: 'sessionid',
                        itemId: 'sessionid',
                        fieldLabel: '结算周期',
                        labelWidth: 100,
                        table: 'tb_consume_session',
                        where: "starttime>date_add(now(), interval -2 month)",
                        orderby: 'uid asc',
                        fields: 'uid,name',
                        defaultSelectFirst: false
                    }]
            }]
        });
        me.callParent(arguments);

    },
    Save: function () {
        var me = this;
        var sessionid = me.down('comboxlist[itemId=sessionid]').getValue();
        if (!sessionid) {
            toast('请选择补贴周期...');
            return;
        }
        Ext.Msg.show({
            title: '系统确认',
            message: '确认添加人员并发放历史补贴吗？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/ViceWall/addpositiondetail',
                        params: {
                            groupid: me.parWin.groupid,
                            sessionid: sessionid,
                            infoids: me.infoids
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.parWin.groupgrid.getStore().reload();
                                toast('添加成功...');
                                me.parWin.close();
                                me.close();
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    }
});

Ext.define('CamPus.view.card.position.ImportExcelWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '导入' + window.applang.get('infoname1') + '档案',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    save1BtnTitle: '确认并补贴',
    width: 600,
    height: 360,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中应至少包含工号，其他可选；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/uploadUpdateExcel',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('上传成功，请设置数据对应列！');
                        var nextwin = Ext.create('CamPus.view.card.position.SetExcelColumnWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid,
                            groupid: me.groupid
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    },
    Save1: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/uploadUpdateExcel',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('上传成功，请选择补贴周期');
                        var nextwin = Ext.create('CamPus.view.card.position.choseSessionForExcelWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid,
                            groupid: me.groupid,
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.card.position.choseSessionForExcelWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择补贴周期',
    width: 350,
    height: 150,
    defaults: {
        collapsible: false,
        split: true
    },
    maximizable: false,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right',
                    labelWidth: 100
                },
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'comboxlist',
                        name: 'sessionid',
                        itemId: 'sessionid',
                        fieldLabel: '结算周期',
                        labelWidth: 100,
                        table: 'tb_consume_session',
                        where: "starttime>date_add(now(), interval -2 month)",
                        orderby: 'uid asc',
                        fields: 'uid,name',
                        defaultSelectFirst: false
                    }]
            }]
        });
        me.callParent(arguments);

    },
    Save: function () {
        var me = this;
        var sessionid = me.down('comboxlist[itemId=sessionid]').getValue();
        if (!sessionid) {
            toast('请选择补贴周期...');
            return;
        }
        Ext.Msg.show({
            title: '系统确认',
            message: '确认添加人员并发放历史补贴吗？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    toast('上传成功，请设置数据对应列！');
                    var nextwin = Ext.create('CamPus.view.card.position.SetExcelColumnWindow', {
                        excelpath: me.excelpath,
                        data: me.data,
                        maingrid: me.maingrid,
                        groupid: me.groupid,
                        sessionid: sessionid
                    });
                    nextwin.show();
                    me.close();
                    // Ext.Ajax.request({
                    //     url: '/Card/ViceWall/addpositiondetail',
                    //     params: {
                    //         groupid: me.parWin.groupid,
                    //         sessionid: sessionid,
                    //         infoids: me.infoids
                    //     },
                    //     method: 'POST',
                    //     success: function (response) {
                    //         var result = Ext.JSON.decode(response.responseText);
                    //         if (result.success) {
                    //             me.parWin.groupgrid.getStore().reload();
                    //             toast('添加成功...');
                    //             me.parWin.close();
                    //             me.close();
                    //         } else {
                    //             Ext.Msg.alert('系统信息', result.msg);
                    //         }
                    //     }
                    // });
                }
            }
        });
    }
});

Ext.define('CamPus.view.card.position.SetExcelColumnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置导入对应列',
    width: 550,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('Ext.data.Store', {
            fields: ['column', 'value', 'columnname', 'columncode'],
            data: config.data
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            border: false,
            flex: 1,
            store: me.store,
            columns: [{
                text: '列序号',
                dataIndex: 'column',
                width: 65
            },
                {
                    text: '列名',
                    dataIndex: 'value',
                    flex: 1
                },
                {
                    text: '对应列名',
                    dataIndex: 'columncode',
                    flex: 1,
                    sortable: false,
                    editor: {
                        xtype: 'combobox',
                        queryMode: 'local',
                        displayField: 'name',
                        valueField: 'value',
                        editable: false,
                        store: Ext.create('Ext.data.Store', {
                            fields: ['name', 'value'],
                            data: [
                                { name: "忽略", value: '' },
                                { name: "工号", value: 'code' },
                                { name: "姓名", value: 'name' },
                                { name: "卡序列号", value: 'cardsn' },
                                { name: "性别(1男2女)", value: 'sex' },
                                { name: "职位编号", value: 'position' },
                                { name: "职级编号", value: 'posrank' },
                                { name: "手机号", value: 'mobile' },
                                { name: "邮箱", value: 'email' },
                                { name: window.applang.get('orgname') + "代码", value: 'orgcode' },
                                { name: "身份证号", value: 'idcard' },
                                { name: "民族", value: 'nation' },
                                { name: "生日(长度为10的日期)", value: 'birthday' },
                                { name: "地址", value: 'address' },
                                { name: "通行密码", value: 'passpassword' },
                                { name: "紧急联系人", value: 'linkman1' },
                                { name: "紧急联系人手机号", value: 'linkmobile1' },
                                { name: "与本人关系", value: 'linkdes1' },
                                { name: "备用联系人", value: 'linkman2' },
                                { name: "备用联系电话", value: 'linkmobile2' },
                                { name: "备用联系人与本人关系", value: 'linkdes2' },
                                { name: "标签", value: 'infolabel' },
                                { name: "人员性质", value: 'property' }
                            ]
                        }),
                        listeners: {
                            change: function (combox, newValue, oldValue, eOpts) {
                                if (newValue) {
                                    var records = me.grid.getStore().getData().items;
                                    var exists = false;
                                    Ext.each(records, function (record, i) {
                                        if (newValue && record.get('columncode') && record.get('columncode') == newValue) {
                                            exists = true;
                                        }
                                    });
                                    if (!exists) {
                                        combox.ownerCt.context.record.set('columncode', newValue);
                                        combox.ownerCt.context.record.set('columnname', combox.getRawValue());
                                    } else {
                                        toast('禁止多列对应一列');
                                        return false;
                                    }
                                } else {
                                    combox.ownerCt.context.record.set('columncode', '');
                                    combox.ownerCt.context.record.set('columnname', '');
                                }
                            }
                        }
                    },
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record) {
                            return record.get('columnname');
                        }
                    }
                }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'newlistplugin'
            }
        });

        Ext.apply(me, {
            fbar: ['->',
                {
                    xtype: 'button',
                    text: '确定',
                    listeners: {
                        click: function (button, event) {
                            me.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    listeners: {
                        click: function (button, event) {
                            me.close();
                        }
                    }
                }
            ],
            items: [me.grid, {
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                hidden: true,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    itemId: 'excelpath',
                    name: 'excelpath',
                    value: config.excelpath,
                    allowBlank: false
                }, {
                    itemId: 'columncfg',
                    name: 'columncfg',
                    allowBlank: false
                }, {
                    itemId: 'groupid',
                    name: 'groupid',
                    value: config.groupid,
                    allowBlank: false
                }, {
                    itemId: 'sessionid',
                    name: 'sessionid',
                    value: config.sessionid,
                    allowBlank: true
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.grid.getStore().getData().items;
        var columncfg = [];
        Ext.each(records, function (record, i) {
            if (record.get('columncode')) {
                columncfg.push({
                    columncode: record.get('columncode'),
                    column: record.get('column')
                });
            }
        });
        if (columncfg.length == 0) {
            toast('请设置文档列对应关系');
            return;
        }
        var form = me.down('form[itemId=form]');
        form.down('textfield[itemId=columncfg]').setValue(JSON.stringify(columncfg));
        // form.down('textfield[itemId=groupid]').setValue(JSON.stringify(columncfg));
        // form.down('textfield[itemId=groupid]').setValue(JSON.stringify(columncfg));
        // form.down('textfield[itemId=groupid]').setValue(JSON.stringify(columncfg));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/ViceWall/dispenceFundsByExcel',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        me.maingrid.getStore().loadPage(1);
                        if (action.result.msg) {
                            Ext.MessageBox.show({
                                title: '系统信息',
                                msg: '导入数据有误，无法更新。是否查看详细信息?',
                                buttons: Ext.MessageBox.YESNOCANCEL,
                                icon: Ext.MessageBox.QUESTION,
                                fn: function (btn, text) {
                                    if (btn == 'yes') {
                                        openWindow(action.result.msg, null, 'get');
                                    }
                                }
                            });
                        } else {
                            toast('数据导入成功！');
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


