Ext.define('CamPus.view.card.student.StudentController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.StudentController',
    view: ['StudentView'],
    init: function() {
        var me = this;
        me.setControl({
            '*[itemId=add]': {
                click: me.onAddClick
            },
            '*[itemId=edit]': {
                click: me.onEditClick
            },
            '*[itemId=del]': {
                click: me.onDelClick
            },
            'treepanel[itemId=collegeclasstree]': {
                select: me.onTreeSelect
            },
            '*[itemId=disable]': {
                click: me.onDisableClick
            },
            '*[itemId=able]': {
                click: me.onAbleClick
            },
            '*[itemId=viewidcard]': {
                click: me.onViewIdCardClick
            },
            '*[itemId=changecls]': {
                click: me.onChangeClsClick
            },
            '*[itemId=import]': {
                click: me.onImportClick
            },
            '*[itemId=resetpassword]': {
                click: me.onResetPasswordClick
            },
            'gridpanel[itemId=StudentGrid]': {
                rowdblclick: me.onStudentGridDBClick
            },
            '*[itemId=export]': {
                click: me.onExportClick
            },
            '*[itemId=importUpdate]': {
                click: me.onImportUpdateClick
            },
            '*[itemId=exportarchives]': {
                click: me.onExportArchivesClick
            },
            '*[itemId=exportqrcode]': {
                click: me.onExportQRCodeClick
            },
            '*[itemId=revoke]': {
                click: me.onRevokeClick
            },
            '*[itemId=importMoney]': {
                click: me.onImportMoneyClick
            }
        });

        me.store = me.view.store;
        me.grid = me.view.grid;
        me.treestore = me.view.treestore;
        me.tree = me.view.tree;
        me.infouidstore = me.view.infouidstore;

        me.store.on('beforeload', function(store) {
            var code = 'x';
            if (me.tree.getSelection().length > 0) {
                code = me.tree.getSelection()[0].get('code');
            }
            var viewchild = me.view.down('checkboxfield[itemId=viewchild]');
            var viewleave = me.view.down('combobox[itemId=viewleave]');
            var property = me.view.down('comboxdictionary[itemId=property]');
            var infolabel = me.view.down('combobox[itemId=infolabel]');
            Ext.apply(store.proxy.extraParams, {
                orgcode: code,
                infolabel: infolabel.getValue(),
                viewchild: viewchild.getValue(),
                viewleave: viewleave.getValue(),
                property: property.getValue()
            });
        });
    },
    onImportUpdateClick: function () {
        var me = this;
        var win = Ext.create('CamPus.view.card.student.ImportUpdateWindow', {
            maingrid: me.grid
        });
        win.show();
    },
    autoAddNewFn: function() {
        this.onAddClick();
    },
    onRevokeClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择学生资料...');
            return;
        }
        var uids = [];
        Ext.each(me.grid.getSelection(), function(item, index) {
            uids.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '权限收回后,所有相关的数据将会清空，是否继续 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/OrgFramework/RevokeStudent',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                if(result.msg){
                                    toast(result.msg);
                                }else{
                                    toast('操作成功...');
                                }
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },

    onAddClick: function () {

        var me = this;
        if (me.tree.getSelection().length == 0) {
            toast('请选择' + window.applang.get('orgname') + '...');
            return;
        }
        var infoid = me.infouidstore.data.items[0].data.uid;
        var record = me.tree.getSelection()[0];
        me.tree.pathName = '';
        me.tree.setPathName(record);
        var win = Ext.create('CamPus.view.card.student.StudentAddWindow', {
            aotuAddNew: true,
            ctrl: me,
            orgcode: record.get('code'),
            orgname: me.tree.pathName,
            grid: me.grid,
            newInfoId: infoid,
            infouidstore: me.infouidstore,
            lfbar: [{
                xtype: 'button',
                text: '读卡',
                iconCls: 'x-fa fa-microchip',
                hidden: (CamPus.AppSession.cardtype == 'id'),
                handler: function () {
                    window.onReadCard(function (identid, CardNO) {
                        var card = win.down('textfield[itemId=card]');
                        card.setValue(CardNO);
                    });
                }
            },
            {
                xtype: 'button',
                text: '人脸采集',
                iconCls: 'x-fa fa-microchip',
                handler: function() {
                    var upMe = this.up('window'); // 获取包含这个按钮的父窗口
                    // var me = this;
                    var uploadFaceWin = Ext.create('CamPus.view.card.teacher.uploadFaceWindows',{
                        uid: infoid,
                        title: '人脸采集',
                        handleIndex: 0,
                        // handleData: me.grid.getSelection(),
                        listeners:{
                            afterrender: function(win , eOpts){
                                win.InitInfoFace(infoid);
                            },
                            beforeclose: function(){
                                // me.store.loadPage(1);
                            },
                            // 当上传窗口关闭时，将图片 URL 回传
                            close: function (win) {
                                if (win.config.uid) {
                                    var me = this;
                                    var imgComponent = upMe.down('image[itemId=photo]');
                                    // var src = me.down('hiddenfield[name=uid1]').value;
                                    var src = me.down('image[itemId=faceImg1]').src;
                                    imgComponent.setSrc(window.getResourceUrl(CamPus.AppSession.resourcedomain, src) + '?t=' + Math.random());
                                }
                            }
                        },
                        showsave:false,
                        insertfbar: [{
                            xtype: 'progress',
                            itemId: 'cardprogress',
                            value: 0,
                            animate: true,
                            flex: 1,
                            text: '进度',
                            style: 'border:1px solid #7fb5e4;'
                        }],
                    });
                    uploadFaceWin.show();
                    // var me = this;
                    // var win = Ext.create('CamPus.view.face.intoface.uploadFaceWindows', {
                    //     maingrid: me.grid
                    // });
                    // win.show();
                }
            },
                {
                xtype: 'button',
                text: '读取身份证信息',
                iconCls: 'x-fa fa-microchip',
                handler: function () {
                    var form = win.down('form[itemId=form]');
                    var Name = form.down('textfield[name=name]');
                    var Idc = form.down('textfield[name=idcard]');
                    var sex1 = form.down('radio[itemId=sex1]');
                    var sex2 = form.down('radio[itemId=sex2]');
                    var Address = form.down('textfield[name=address]');
                    var NationCName = form.down('textfield[name=nation]');
                    var Birth = form.down('datefield[name=birthday]');
                    window.onReadIdCard(function (identid,idcard) {
                        idcard.Birth=idcard.Birth.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1/$2/$3");
                        Idc.setValue(idcard.Idc);
                        Name.setValue(idcard.Name);
                        Address.setValue(idcard.Address);
                        NationCName.setValue(idcard.NationCName);
                        Birth.setValue(new Date(Date.parse(idcard.Birth)));
                        if(idcard.SexCName=='女'){
                            sex2.setValue(true);
                        }else{
                            sex1.setValue(true);
                        }
                    });
                }
            }]
        });
        win.show();
    },
    onEditClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择学生信息...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var win = Ext.create('CamPus.view.card.student.StudentEditWindow', {
            grid: me.grid,
            record: record,
            extenduid: record.get('uid'),
            lfbar: [{
                xtype: 'button',
                text: '读卡',
                iconCls: 'x-fa fa-microchip',
                hidden: (CamPus.AppSession.cardtype == 'id'),
                handler: function () {
                    window.onReadCard(function (identid, CardNO) {
                        var card = win.down('textfield[itemId=card]');
                        card.setValue(CardNO);
                    });
                }
            },
                {
                    xtype: 'button',
                    text: '人脸采集',
                    iconCls: 'x-fa fa-microchip',
                    handler: function() {
                        var upMe = this.up('window'); // 获取包含这个按钮的父窗口
                        // var me = this;
                        var uploadFaceWin = Ext.create('CamPus.view.card.teacher.uploadFaceWindows',{
                            uid: record.get('uid'),
                            title: '人脸采集',
                            handleIndex: 0,
                            // handleData: me.grid.getSelection(),
                            listeners:{
                                afterrender: function(win , eOpts){
                                    win.InitInfoFace(record.get('uid'));
                                },
                                beforeclose: function(){
                                    // me.store.loadPage(1);
                                },
                                // 当上传窗口关闭时，将图片 URL 回传
                                close: function (win) {
                                    if (win.config.uid) {
                                        var me = this;
                                        var imgComponent = upMe.down('image[itemId=photo]');
                                        // var src = me.down('hiddenfield[name=uid1]').value;
                                        var src = me.down('image[itemId=faceImg1]').src;
                                        if (src) {
                                            imgComponent.setSrc(window.getResourceUrl(CamPus.AppSession.resourcedomain, src) + '?t=' + Math.random());
                                        }
                                    }
                                }
                            },
                            showsave:false,
                            insertfbar: [{
                                xtype: 'progress',
                                itemId: 'cardprogress',
                                value: 0,
                                animate: true,
                                flex: 1,
                                text: '进度',
                                style: 'border:1px solid #7fb5e4;'
                            }],
                        });
                        uploadFaceWin.show();
                        // var me = this;
                        // var win = Ext.create('CamPus.view.face.intoface.uploadFaceWindows', {
                        //     maingrid: me.grid
                        // });
                        // win.show();
                    }
                },

                {
                xtype: 'button',
                text: '读取身份证信息',
                iconCls: 'x-fa fa-microchip',
                handler: function () {
                    var form = win.down('form[itemId=form]');
                    var Name = form.down('textfield[name=name]');
                    var Idc = form.down('textfield[name=idcard]');
                    var sex1 = form.down('radio[itemId=sex1]');
                    var sex2 = form.down('radio[itemId=sex2]');
                    var Address = form.down('textfield[name=address]');
                    var NationCName = form.down('textfield[name=nation]');
                    var Birth = form.down('datefield[name=birthday]');
                    window.onReadIdCard(function (identid,idcard) {
                        idcard.Birth=idcard.Birth.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1/$2/$3");
                        Idc.setValue(idcard.Idc);
                        Name.setValue(idcard.Name);
                        Address.setValue(idcard.Address);
                        NationCName.setValue(idcard.NationCName);
                        Birth.setValue(new Date(Date.parse(idcard.Birth)));
                        if(idcard.SexCName=='女'){
                            sex2.setValue(true);
                        }else{
                            sex1.setValue(true);
                        }
                    });
                }
            }]
        });
        var form = win.down('form[itemId=form]');
        form.loadRecord(record);
        form.down('textfield[itemId=card]').setReadOnly(true);
        win.show();
    },
    onDelClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择学生资料...');
            return;
        }
        // if (me.grid.getSelection().length > 1) {
        //     toast('系统禁止批量删除学生资料...');
        //     return;
        // }
        // var record = me.grid.getSelection()[0];
        // var uid = record.get('uid');

        var records = me.grid.getSelection();
        var recordUids = []
        records.forEach(function (item, i) {
            recordUids.push(item.get('uid'))
        })
        var infoids = recordUids.join(',');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/DelStudent',
                        params: {
                            infoids: infoids
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onTreeSelect: function() {
        this.store.loadPage(1);
    },
    onDisableClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择学生资料...');
            return;
        }
        var uids = [];
        Ext.each(me.grid.getSelection(), function(item, index) {
            uids.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要将所选择的学生信息设置为无效 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/DisabledStudent',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                if(result.msg){
                                    toast(result.msg);
                                }else{
                                    toast('操作成功...');
                                }
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onAbleClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择学生资料...');
            return;
        }
        var uids = [];
        Ext.each(me.grid.getSelection(), function(item, index) {
            uids.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要将所选择的学生信息设置为有效 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/AbledStudent',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('操作成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onChangeClsClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择学生资料...');
            return;
        }
        var infoids = [];
        var fromOrgs = [];
        Ext.each(me.grid.getSelection(), function(record, index) {
            infoids.push(record.get('uid'));
            fromOrgs.push(record.get('orgcode'));
        });
        var win = Ext.create('CamPus.view.card.student.ChangeStudentClassWindow', {
            maingrid: me.grid,
            infoid: infoids.join(','),
            fromOrgs: fromOrgs.join(',')
        });
        win.show();
    },
    onImportClick: function() {
        var me = this;
        var win = Ext.create('CamPus.view.card.student.ImportStudentWindow', {
            maingrid: me.grid
        });
        win.show();
    },
    onImportMoneyClick: function() {
        var me = this;
        var win = Ext.create('CamPus.view.card.student.ImportMoneyWindow', {
            maingrid: me.grid
        });
        win.show();
    },
    onResetPasswordClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择学生资料...');
            return;
        }
        var infoids = [];
        Ext.each(me.grid.getSelection(), function(record, index) {
            infoids.push(record.get('uid'));
        });
        Ext.Ajax.request({
            url: '/Card/TeachStudInfo/ResetPassword',
            params: {
                infoid: infoids.join(',')
            },
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    toast('密码重置成功');
                } else {
                    toast(result.msg);
                }
            }
        });
    },

    onStudentGridDBClick:function(grid, record, element, rowIndex, e, eOpts){
        var me = this;
        var win = Ext.create('CamPus.view.card.givecard.GiveCardInfoViewWindow', {
            infoid: record.get('uid'),
            title: '【' + record.get('name') + '】已领所有卡信息',
            fbar: []
        });
        win.show();
    },
    onExportClick: function() {
        var me = this;
        Ext.get('loadingToast').show();
        Ext.Ajax.request({
            url: '/Card/TeachStudInfo/ExportStudent',
            params: me.store.proxy.extraParams,
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    onExportQRCodeClick: function(){
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择学生资料...');
            return;
        }
        var infoids = [];
        Ext.each(me.grid.getSelection(), function(record, index) {
            infoids.push(record.get('uid'));
        });
        Ext.Ajax.request({
            url: '/Card/TeachStudInfo/exportQRCode',
            params: {
                infoid: infoids.join(',')
            },
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    onExportArchivesClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择学生资料...');
            return;
        }
        var infoids = [];
        Ext.each(me.grid.getSelection(), function(record, index) {
            infoids.push(record.get('uid'));
        });
        Ext.Ajax.request({
            url: '/Card/TeachStudInfo/exportStudentInfo',
            params: {
                uid: infoids.join(',')
            },
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },
});