Ext.define('CamPus.view.card.cardmanage.CardManageView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.CardManageView",
    controller: 'CardManageController',
    requires: [
        'CamPus.view.card.cardmanage.CardManageStore',
        'CamPus.view.card.cardmanage.CardManageController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;


        me.personStore = Ext.create('CamPus.view.card.cardmanage.PersonStore', {});

        var persontbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'combobox',
                itemId: 'viewleave',
                queryMode: 'local',
                displayField: 'name',
                valueField: 'value',
                editable: false,
                value: 1,
                width: 80,
                store: Ext.create('Ext.data.Store', {
                    fields: ['value', 'name'],
                    data: [
                        { "value": -1, "name": "全部" },
                        { "value": 1, "name": "在职" },
                        { "value": 0, "name": "离职" }
                    ]
                }),
                listeners: {
                    change: function () {
                        me.personStore.reload();
                    }
                }
            },{
                xtype: 'searchfield',
                itemId: 'searchKey',
                hideLabel: true,
                paramName: 'key',
                flex: 1,
                store: me.personStore,
                emptyText: window.applang.get("infocode") + '、姓名'
            },
            {
                xtype: 'button',
                text: '读卡',
                itemId: 'readCard',
                iconCls: 'x-fa fa-microchip',
                hidden: CamPus.AppSession.cardtype == 'id',
                handler: function () {
                    window.onReadCard(function (identid, CardNO) {
                        var searchKey = me.down('searchfield[itemId=searchKey]');
                        searchKey.setValue(CardNO);
                        searchKey.onSearchClick();
                    });
                }
            }]
        });

        me.persongrid = Ext.create('Ext.grid.Panel', {
            //title: '人员信息',
            itemId: 'Personlist',
            multiColumnSort: true,
            border: false,
            region: 'west',
            collapsible: false,
            rowLines: true,
            columnLines: true,
            tbar: persontbar,
            store: me.personStore,
            selModel: 'checkboxmodel',
            viewConfig: {
                enableTextSelection: true,
                listeners: {
                    refresh: function (view, eOpts) {
                        view.select(0);
                    }
                }
            },
            columns: [
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 80,
                    sortable: true
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    minWidth: 100,
                    flex: 1,
                    sortable: true
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 55,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return '正常';
                        } else {
                            metaData.style += 'color:red;';
                            return '无效';
                        }
                    }
                }
            ],
            width: 320,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.store = Ext.create('CamPus.view.card.cardmanage.CardManageStore', {

        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'comboxdictionary',
                    groupCode: 'SYS0000006',
                    insertAll: '-1|全部',
                    itemId: 'dotype',
                    width: 100,
                    defaultSelectFirst: true,
                    value: -1,
                    listeners: {
                        change: function () {
                            me.store.loadPage(1);
                        }
                    }
                }, {
                    xtype: 'searchfield',
                    width: 200,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.store,
                    emptyText: '卡号、备注关键词...'
                }, '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.store.reload();
                    }
                }
            ]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            //title: '卡片操作记录',
            region: 'center',
            itemId: 'CardManageGrid',
            tbar: tbar,
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                { text: '操作', dataIndex: 'dotypename', width: 80, sortable: false },
                { text: '卡号', dataIndex: 'cardno', width: 120, sortable: false },
                { text: window.applang.get('infocode'), dataIndex: 'infocode', width: 100, sortable: true },
                { text: '姓名', dataIndex: 'infoname', width: 100, sortable: true },
                { text: '所属单位', dataIndex: 'orgname', width: 150, sortable: false },
                {
                    text: '类型',
                    dataIndex: 'ismain',
                    width: 50,
                    hidden: true,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return '主卡'
                        } else {
                            return '子卡';
                        }
                    }
                },
                { text: '持卡人', dataIndex: 'usedes', width: 100, sortable: false },
                { text: '操作人员', dataIndex: 'creator', width: 100, sortable: false },
                {
                    text: '操作时间',
                    dataIndex: 'createdate',
                    width: 160,
                    sortable: true,
                    sortIndex: 'c.createdate',
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                { text: '备注', dataIndex: 'des', minWidth:200,flex: 1, sortable: false }
            ],
            selModel: 'rowmodel',
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.persongrid, me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});


Ext.define('CamPus.view.card.cardmanage.RechargeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '充值',
    iconCls: 'x-fa fa-vcard',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.cardliststore = Ext.create('Ext.data.Store', {
            proxy: {
                type: 'ajax',
                url: '/Card/CardManage/getCardTransactionDetail',
                actionMethods: {
                    read: 'POST'
                },
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    totalProperty: 'total',
                    successProperty: 'success',
                    messageProperty: 'msg'
                },
                extraParams: { cardid: '' }
            },
            autoLoad: false
        });

        me.cardlistgrid = Ext.create('Ext.grid.Panel', {
            title: '最近交易流水',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            hidden: true,
            columnLines: true,
            margin: '0 0 15 0',
            viewConfig: {
                enableTextSelection: true
            },
            store: me.cardliststore,
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '订单号',
                    dataIndex: 'uid',
                    width: 200,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'cardno',
                    width: 140,
                    sortable: false
                },
                {
                    text: '类型',
                    dataIndex: 'tradetype',
                    width: 60,
                    sortable: false
                },
                {
                    text: '金额',
                    dataIndex: 'money',
                    width: 65,
                    sortable: false
                },
                {
                    text: '状态',
                    dataIndex: 'orderstatus',
                    width: 55,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '创建';
                            case 1:
                                metaData.style += 'color:#c35f09;';
                                return '验证';
                            case 2:
                                metaData.style += 'color:#7bc309;';
                                return '完成';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '支付方式',
                    dataIndex: 'paytype',
                    width: 75,
                    sortable: false
                },
                {
                    text: '交易渠道',
                    dataIndex: 'payway',
                    width: 75,
                    sortable: false
                },
                {
                    text: '交易时间',
                    dataIndex: 'tradedate',
                    width: 160,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    flex: 1,
                    minWidth: 120,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            height: 220
        });

        me.waterliststore = Ext.create('Ext.data.Store', {
            proxy: {
                type: 'ajax',
                url: '/Card/CardManage/getWaterTransactionDetail',
                actionMethods: {
                    read: 'POST'
                },
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    totalProperty: 'total',
                    successProperty: 'success',
                    messageProperty: 'msg'
                },
                extraParams: { cardid: '' }
            },
            autoLoad: false
        });

        me.waterlistgrid = Ext.create('Ext.grid.Panel', {
            title: '水控最近交易流水',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            hidden: true,
            columnLines: true,
            margin: '0 0 15 0',
            viewConfig: {
                enableTextSelection: true
            },
            store: me.waterliststore,
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '订单号',
                    dataIndex: 'uid',
                    width: 200,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'card',
                    width: 140,
                    sortable: false
                },
                {
                    text: '类型',
                    dataIndex: 'tradetype',
                    width: 60,
                    sortable: false
                },
                {
                    text: '金额',
                    dataIndex: 'money',
                    width: 65,
                    sortable: false
                },
                {
                    text: '交易时间',
                    dataIndex: 'tradedate',
                    width: 160,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    flex: 1,
                    minWidth: 120,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            height: 220
        });

        Ext.apply(me, {
            aotuAddNew: true,
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'cardid',
                    name: 'cardid'
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid'
                },
                {
                    xtype: 'numberfield',
                    hidden: true,
                    itemId: 'status',
                    name: 'status'
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        fieldLabel: '卡号',
                        itemId: 'cardno',
                        name: 'cardno',
                        allowBlank: false,
                        value: (CamPus.AppSession.cardtype == 'id' ? config.cardsn : '')
                    },
                    {
                        xtype: 'datefield',
                        fieldLabel: '有效期',
                        itemId: 'enddate',
                        name: 'enddate',
                        value: '',
                        format: 'Y-m-d',
                        readOnly: true,
                        allowBlank: false
                    },
                    {
                        fieldLabel: '持卡人',
                        itemId: 'infoname',
                        name: 'infoname',
                        allowBlank: false,
                        readOnly: true
                    }
                    ]
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'numberfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [
                        {
                            fieldLabel: '主钱包余额',
                            itemId: 'balance',
                            name: 'balance',
                            decimalPrecision: 2,
                            value: 0,
                            allowBlank: false,
                            readOnly: true
                        },
                        {
                            fieldLabel: '副钱包金额',
                            itemId: 'vicewallet',
                            name: 'vicewallet',
                            allowBlank: true,
                            decimalPrecision: 2,
                            value: 0,
                            readOnly: true
                        },
                        {
                            fieldLabel: '水控钱包金额',
                            itemId: 'waterwallet',
                            name: 'waterwallet',
                            allowBlank: true,
                            decimalPrecision: 2,
                            value: 0,
                            readOnly: true
                        },
                        {
                            fieldLabel: '制卡费',
                            itemId: 'deposit',
                            name: 'deposit',
                            allowBlank: true,
                            decimalPrecision: 2,
                            value: 0,
                            readOnly: true
                        }
                    ]
                },
                me.cardlistgrid,
                me.waterlistgrid,
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'numberfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [
                        {
                            fieldLabel: '充值钱包',
                            xtype: 'comboxdictionary',
                            name: 'paywallet',
                            groupCode: 'SYS0000032',
                            valueField: 'code',
                            allowBlank: false,
                            editable: false,
                            where: 'code <> 2',
                            value: 1,
                            listeners: {
                                change: function (combox, newValue, oldValue, eOpts) {
                                    if(newValue == 1){
                                        me.waterlistgrid.setHidden(true)
                                        me.cardlistgrid.setHidden(false)
                                        me.cardliststore.load();

                                    }else {
                                        me.cardlistgrid.setHidden(true)
                                        me.waterlistgrid.setHidden(false)
                                        me.waterliststore.load();
                                    }
                                }
                            }
                        },
                        {
                            fieldLabel: '充值金额',
                            itemId: 'amount',
                            name: 'amount',
                            allowBlank: false,
                            decimalPrecision: 2,
                            value: 0,
                            minValue: 1
                        },
                        {
                            fieldLabel: '支付方式',
                            xtype: 'comboxdictionary',
                            name: 'paytype',
                            groupCode: 'SYS0000007',
                            valueField: 'code',
                            allowBlank: false,
                            editable: false,
                            value: 3
                        },
                        {
                            fieldLabel: '付款渠道',
                            xtype: 'comboxdictionary',
                            name: 'payway',
                            groupCode: 'SYS0000008',
                            valueField: 'code',
                            allowBlank: false,
                            editable: false,
                            value: 3
                        }
                    ]
                },
                {
                    xtype: 'fieldcontainer',
                    fieldLabel: '选择金额',
                    layout: 'hbox',
                    defaultType: 'button',
                    defaults: {
                        flex: 1
                    },
                    items: [{
                        text: '￥10元',
                        amount: 10,
                        iconCls: 'x-fa fa-cny',
                        handler: function () {
                            me.down('numberfield[itemId=amount]').setValue(this.amount);
                        }
                    }, {
                        text: '20元',
                        amount: 20,
                        iconCls: 'x-fa fa-cny',
                        handler: function () {
                            me.down('numberfield[itemId=amount]').setValue(this.amount);
                        }
                    }, {
                        text: '30元',
                        amount: 30,
                        iconCls: 'x-fa fa-cny',
                        handler: function () {
                            me.down('numberfield[itemId=amount]').setValue(this.amount);
                        }
                    }, {
                        text: '50元',
                        amount: 50,
                        iconCls: 'x-fa fa-cny',
                        handler: function () {
                            me.down('numberfield[itemId=amount]').setValue(this.amount);
                        }
                    }, {
                        text: '100元',
                        amount: 100,
                        iconCls: 'x-fa fa-cny',
                        handler: function () {
                            me.down('numberfield[itemId=amount]').setValue(this.amount);
                        }
                    }, {
                        text: '200元',
                        amount: 200,
                        iconCls: 'x-fa fa-cny',
                        handler: function () {
                            me.down('numberfield[itemId=amount]').setValue(this.amount);
                        }
                    }, {
                        text: '300元',
                        amount: 300,
                        iconCls: 'x-fa fa-cny',
                        handler: function () {
                            me.down('numberfield[itemId=amount]').setValue(this.amount);
                        }
                    }, {
                        text: '500元',
                        amount: 500,
                        iconCls: 'x-fa fa-cny',
                        handler: function () {
                            me.down('numberfield[itemId=amount]').setValue(this.amount);
                        }
                    }, {
                        text: '1000元',
                        amount: 1000,
                        iconCls: 'x-fa fa-cny',
                        handler: function () {
                            me.down('numberfield[itemId=amount]').setValue(this.amount);
                        }
                    }]
                },
                {
                    xtype: 'textfield',
                    fieldLabel: '备注',
                    name: 'des',
                    allowBlank: true
                }
                ]
            }]
        });
        me.callParent(arguments);

        if(CamPus.AppSession.cardtype == 'id' && config.cardsn){
            me.onReadCard(config.cardsn);
        }
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            var status = form.down('numberfield[itemId=status]').getValue();
            if (status != 1) {
                var txt = me.getStatusName(status);
                toast('该卡状态为：' + txt + '，无法正常充值！');
                return;
            }
            form.submit({
                url: '/Card/CardManage/SaveRecharge',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        me.grid.getStore().loadPage(1);
                        toast('充值成功！');
                        if (me.aotuAddNew && me.ctrl) {
                            me.ctrl.onRechargeClick();
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    },
    onReadCard: function (CardNO) {
        var me = this;
        Ext.Ajax.request({
            url: '/Card/CardManage/getCardInfo',
            params: {
                cardno: CardNO
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {

                    var infoid = me.down('hiddenfield[itemId=infoid]');
                    infoid.setValue(result.data.infoid);

                    me.cardlistgrid.getStore().on('beforeload', function (store) {
                        Ext.apply(store.proxy.extraParams, {
                            cardid: result.data.uid
                        });
                    });

                    me.cardlistgrid.getStore().loadPage(1);

                    me.waterlistgrid.getStore().on('beforeload', function (store) {
                        Ext.apply(store.proxy.extraParams, {
                            cardid: result.data.uid
                        });
                    });

                    me.waterlistgrid.getStore().loadPage(1);

                    me.down('hiddenfield[itemId=cardid]').setValue(result.data.uid);
                    me.down('numberfield[itemId=status]').setValue(result.data.status);
                    me.down('numberfield[itemId=balance]').setValue(result.data.balance);
                    me.down('numberfield[itemId=deposit]').setValue(result.data.deposit);
                    me.down('numberfield[itemId=waterwallet]').setValue(result.data.waterwallet);
                    me.down('numberfield[itemId=vicewallet]').setValue(result.data.vicewallet);
                    me.down('textfield[itemId=cardno]').setValue(result.data.cardno);

                    if(!result.data.orgname){
                        result.data.orgname = '';
                    }
                    var infoname = result.data.orgname + result.data.name + result.data.usedes;
                    if (result.data.infotype == 2 && result.data.intoyear) {
                        infoname = result.data.intoyear + infoname;
                    }
                    me.down('textfield[itemId=infoname]').setValue(infoname);
                    me.down('datefield[itemId=enddate]').setValue(new Date(result.data.enddate));
                    if (result.data.status != 1) {
                        var txt = me.getStatusName(result.data.status);
                        toast('该卡状态为：' + txt + '，无法正常充值！');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    getStatusName: function (status) {
        var txt = '';
        switch (status) {
            case 0:
                txt = '无效';
                break;
            case 1:
                txt = '正常';
                break;
            case 2:
                txt = '挂失';
                break;
            default:
                break;
        }
        return txt;
    }
});


Ext.define('CamPus.view.card.cardmanage.LossStatusWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '挂失',
    iconCls: 'x-fa fa-vcard',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.cardlistgrid = Ext.create('Ext.grid.Panel', {
            title: '该人员已领相关卡',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            margin: '0 0 10 0',
            store: Ext.create('Ext.data.Store', {
                proxy: {
                    type: 'ajax',
                    url: '/Card/TeachStudCard/getOtherCardList',
                    actionMethods: {
                        read: 'POST'
                    },
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        totalProperty: 'total',
                        successProperty: 'success',
                        messageProperty: 'msg'
                    },
                    extraParams: { infoid: me.infoid }
                },
                autoLoad: (me.infoid ? true : false)
            }),
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '类型',
                    dataIndex: 'ismain',
                    width: 50,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return '主卡'
                        } else {
                            return '子卡';
                        }
                    }
                },
                { text: '卡号', dataIndex: 'cardno', width: 110, sortable: false },
                { text: '持卡人', dataIndex: 'usedes', width: 100, sortable: false },
                {
                    text: '主钱包余额',
                    dataIndex: 'balance',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '副钱包余额',
                    dataIndex: 'vicewallet',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '水控钱包余额',
                    dataIndex: 'waterwallet',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '制卡费',
                    dataIndex: 'deposit',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '无效';
                            case 1:
                                return '正常';
                            case 2:
                                metaData.style += 'color:red;';
                                return '挂失';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '发卡时间',
                    dataIndex: 'createdate',
                    width: 160,
                    sortable: true,
                    sortIndex: 'c.createdate',
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            height: 350,
            listeners: {
                select: function (grid, record, index, eOpts) {
                    var cardid = me.down('hiddenfield[itemId=cardid]');
                    cardid.setValue(record.get('uid'));
                }
            }
        });

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'cardid',
                    name: 'cardid',
                    allowBlank: false
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid',
                    value: config.infoid,
                    allowBlank: false
                },
                {
                    xtype: 'comboxgrid',
                    fieldLabel: '人员信息',
                    itemId: 'empcombox',
                    valueIndex: 'uid',
                    textIndex: 'name',
                    allowBlank: false,
                    editable: true,
                    readOnly: config.isEdit,
                    gridWidth: 800,
                    columns: [
                        { xtype: 'rownumberer' },
                        { text: window.applang.get('infocode'), dataIndex: 'code', width: 150 },
                        { text: '姓名', dataIndex: 'name', width: 180 },
                        {
                            text: '性别',
                            dataIndex: 'sex',
                            width: 60,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value == 1) {
                                    return '男';
                                } else if (value == 2) {
                                    return '女';
                                }
                            }
                        },
                        {
                            text: '职别',
                            dataIndex: 'infotype',
                            width: 85,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value == 2) {
                                    return '学生';
                                } else {
                                    return window.applang.get('infoname1');
                                }
                            }
                        },
                        {
                            text: window.applang.get('orgname2'),
                            dataIndex: 'orgname',
                            flex: 1,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (record.get('infotype') == 2 && record.get('intoyear')) {
                                    return record.get('intoyear') + value;
                                } else {
                                    return value;
                                }
                            }
                        }
                    ],
                    store: Ext.create('Ext.data.Store', {
                        pageSize: 50,
                        fields: ['uid', 'name', 'code', 'orgname', 'sex', 'infotype', 'intoyear'],
                        proxy: {
                            type: 'ajax',
                            url: '/Card/TeachStudInfo/getInfoList',
                            actionMethods: {
                                read: 'POST'
                            },
                            reader: {
                                type: 'json',
                                rootProperty: 'data',
                                totalProperty: 'total',
                                successProperty: 'success',
                                messageProperty: 'msg'
                            }
                        },
                        autoLoad: false
                    }),
                    listeners: {
                        keyup: function (picker, e, eOpts) {
                            var key = e.getKey();
                            picker.setValue('');
                            if (picker.grid) {
                                picker.expand();
                                var store = picker.grid.getStore();
                                Ext.apply(store.proxy.extraParams, {
                                    key: picker.getRawValue()
                                });
                                store.loadPage(1);
                            }
                        },
                        expand: function (picker, eOpts) {
                            if (picker.grid) {
                                var store = picker.grid.getStore();
                                store.loadPage(1);
                            }
                        },
                        change: function (picker, newValue, oldValue, eOpts) {
                            var infoid = me.down('hiddenfield[itemId=infoid]');
                            if (newValue && eOpts) {
                                infoid.setValue(newValue);
                                me.cardlistgrid.getStore().on('beforeload', function (store) {
                                    Ext.apply(store.proxy.extraParams, {
                                        infoid: newValue
                                    });
                                });
                                me.cardlistgrid.getStore().loadPage(1);
                            } else {
                                infoid.setValue('');
                            }
                        }
                    }
                },
                me.cardlistgrid,
                {
                    xtype: 'textfield',
                    fieldLabel: '备注',
                    name: 'des',
                    allowBlank: true
                }
                ]
            }]
        });

        me.callParent(arguments);

        if (config.infoname) {
            me.down('comboxgrid[itemId=empcombox]').setRawValue(config.infoname);
        }
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        var infoid = me.down('hiddenfield[itemId=infoid]');
        if (!infoid.getValue()) {
            toast('请选择拟挂失卡片的人员信息');
            return;
        }
        var cardid = me.down('hiddenfield[itemId=cardid]');
        if (!cardid.getValue()) {
            toast('请选择拟挂失的卡片记录');
            return;
        }
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/CardManage/SaveLossStatus',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.grid.getStore().loadPage(1);
                        me.close();
                        toast('操作成功');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});
Ext.define('CamPus.view.card.cardmanage.UnlockStatusWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '解挂',
    iconCls: 'x-fa fa-vcard',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.cardlistgrid = Ext.create('Ext.grid.Panel', {
            title: '该人员已领相关卡',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            margin: '0 0 10 0',
            store: Ext.create('Ext.data.Store', {
                proxy: {
                    type: 'ajax',
                    url: '/Card/TeachStudCard/getOtherCardList',
                    actionMethods: {
                        read: 'POST'
                    },
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        totalProperty: 'total',
                        successProperty: 'success',
                        messageProperty: 'msg'
                    },
                    extraParams: { infoid: me.infoid }
                },
                autoLoad: (me.infoid ? true : false)
            }),
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '类型',
                    dataIndex: 'ismain',
                    width: 50,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return '主卡'
                        } else {
                            return '子卡';
                        }
                    }
                },
                { text: '卡号', dataIndex: 'cardno', width: 110, sortable: false },
                { text: '持卡人', dataIndex: 'usedes', width: 100, sortable: false },
                {
                    text: '主钱包余额',
                    dataIndex: 'balance',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '副钱包余额',
                    dataIndex: 'vicewallet',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '水控钱包余额',
                    dataIndex: 'waterwallet',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '制卡费',
                    dataIndex: 'deposit',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '无效';
                            case 1:
                                return '正常';
                            case 2:
                                metaData.style += 'color:red;';
                                return '挂失';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '发卡时间',
                    dataIndex: 'createdate',
                    width: 160,
                    sortable: true,
                    sortIndex: 'c.createdate',
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            height: 350,
            listeners: {
                select: function (grid, record, index, eOpts) {
                    var cardid = me.down('hiddenfield[itemId=cardid]');
                    cardid.setValue(record.get('uid'));
                }
            }
        });

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'cardid',
                    name: 'cardid',
                    allowBlank: false
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid',
                    value: config.infoid,
                    allowBlank: false
                },
                    {
                        xtype: 'comboxgrid',
                        fieldLabel: '人员信息',
                        itemId: 'empcombox',
                        valueIndex: 'uid',
                        textIndex: 'name',
                        allowBlank: false,
                        editable: true,
                        readOnly: config.isEdit,
                        gridWidth: 800,
                        columns: [
                            { xtype: 'rownumberer' },
                            { text: window.applang.get('infocode'), dataIndex: 'code', width: 150 },
                            { text: '姓名', dataIndex: 'name', width: 180 },
                            {
                                text: '性别',
                                dataIndex: 'sex',
                                width: 60,
                                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                    if (value == 1) {
                                        return '男';
                                    } else if (value == 2) {
                                        return '女';
                                    }
                                }
                            },
                            {
                                text: '职别',
                                dataIndex: 'infotype',
                                width: 85,
                                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                    if (value == 2) {
                                        return '学生';
                                    } else {
                                        return window.applang.get('infoname1');
                                    }
                                }
                            },
                            {
                                text: window.applang.get('orgname2'),
                                dataIndex: 'orgname',
                                flex: 1,
                                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                    if (record.get('infotype') == 2 && record.get('intoyear')) {
                                        return record.get('intoyear') + value;
                                    } else {
                                        return value;
                                    }
                                }
                            }
                        ],
                        store: Ext.create('Ext.data.Store', {
                            pageSize: 50,
                            fields: ['uid', 'name', 'code', 'orgname', 'sex', 'infotype', 'intoyear'],
                            proxy: {
                                type: 'ajax',
                                url: '/Card/TeachStudInfo/getInfoList',
                                actionMethods: {
                                    read: 'POST'
                                },
                                reader: {
                                    type: 'json',
                                    rootProperty: 'data',
                                    totalProperty: 'total',
                                    successProperty: 'success',
                                    messageProperty: 'msg'
                                }
                            },
                            autoLoad: false
                        }),
                        listeners: {
                            keyup: function (picker, e, eOpts) {
                                var key = e.getKey();
                                picker.setValue('');
                                if (picker.grid) {
                                    picker.expand();
                                    var store = picker.grid.getStore();
                                    Ext.apply(store.proxy.extraParams, {
                                        key: picker.getRawValue()
                                    });
                                    store.loadPage(1);
                                }
                            },
                            expand: function (picker, eOpts) {
                                if (picker.grid) {
                                    var store = picker.grid.getStore();
                                    store.loadPage(1);
                                }
                            },
                            change: function (picker, newValue, oldValue, eOpts) {
                                var infoid = me.down('hiddenfield[itemId=infoid]');
                                if (newValue && eOpts) {
                                    infoid.setValue(newValue);
                                    me.cardlistgrid.getStore().on('beforeload', function (store) {
                                        Ext.apply(store.proxy.extraParams, {
                                            infoid: newValue
                                        });
                                    });
                                    me.cardlistgrid.getStore().loadPage(1);
                                } else {
                                    infoid.setValue('');
                                }
                            }
                        }
                    },
                    me.cardlistgrid,
                    {
                        xtype: 'textfield',
                        fieldLabel: '备注',
                        name: 'des',
                        allowBlank: true
                    }
                ]
            }]
        });

        me.callParent(arguments);

        if (config.infoname) {
            me.down('comboxgrid[itemId=empcombox]').setRawValue(config.infoname);
        }
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        var infoid = me.down('hiddenfield[itemId=infoid]');
        if (!infoid.getValue()) {
            toast('请选择拟解挂卡片的人员信息');
            return;
        }
        var cardid = me.down('hiddenfield[itemId=cardid]');
        if (!cardid.getValue()) {
            toast('请选择拟解挂的卡片记录');
            return;
        }
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/CardManage/UnlockStatus',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.grid.getStore().loadPage(1);
                        me.close();
                        toast('操作成功');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});



Ext.define('CamPus.view.card.cardmanage.RefundWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '退款',
    iconCls: 'x-fa fa-vcard',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.cardliststore = Ext.create('Ext.data.Store', {
            proxy: {
                type: 'ajax',
                url: '/Card/CardManage/getCardTransactionDetail',
                actionMethods: {
                    read: 'POST'
                },
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    totalProperty: 'total',
                    successProperty: 'success',
                    messageProperty: 'msg'
                },
                extraParams: { cardid: '' }
            },
            autoLoad: false
        });

        me.cardlistgrid = Ext.create('Ext.grid.Panel', {
            title: '最近交易流水',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            margin: '0 0 15 0',
            viewConfig: {
                enableTextSelection: true
            },
            store: me.cardliststore,
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '订单号',
                    dataIndex: 'uid',
                    width: 200,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'cardno',
                    width: 140,
                    sortable: false
                },
                {
                    text: '类型',
                    dataIndex: 'tradetype',
                    width: 60,
                    sortable: false
                },
                {
                    text: '金额',
                    dataIndex: 'money',
                    width: 65,
                    sortable: false
                },
                {
                    text: '状态',
                    dataIndex: 'orderstatus',
                    width: 55,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '创建';
                            case 1:
                                metaData.style += 'color:#c35f09;';
                                return '验证';
                            case 2:
                                metaData.style += 'color:#7bc309;';
                                return '完成';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '支付方式',
                    dataIndex: 'paytype',
                    width: 75,
                    sortable: false
                },
                {
                    text: '交易渠道',
                    dataIndex: 'payway',
                    width: 75,
                    sortable: false
                },
                {
                    text: '交易时间',
                    dataIndex: 'tradedate',
                    width: 160,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    flex: 1,
                    minWidth: 120,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            height: 270,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.waterliststore = Ext.create('Ext.data.Store', {
            proxy: {
                type: 'ajax',
                url: '/Card/CardManage/getWaterTransactionDetail',
                actionMethods: {
                    read: 'POST'
                },
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    totalProperty: 'total',
                    successProperty: 'success',
                    messageProperty: 'msg'
                },
                extraParams: { cardid: '' }
            },
            autoLoad: false
        });

        me.waterlistgrid = Ext.create('Ext.grid.Panel', {
            title: '水控最近交易流水',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            hidden: true,
            columnLines: true,
            margin: '0 0 15 0',
            viewConfig: {
                enableTextSelection: true
            },
            store: me.waterliststore,
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '订单号',
                    dataIndex: 'uid',
                    width: 200,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'card',
                    width: 140,
                    sortable: false
                },
                {
                    text: '类型',
                    dataIndex: 'tradetype',
                    width: 60,
                    sortable: false
                },
                {
                    text: '金额',
                    dataIndex: 'money',
                    width: 65,
                    sortable: false
                },
                {
                    text: '交易时间',
                    dataIndex: 'tradedate',
                    width: 160,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    flex: 1,
                    minWidth: 120,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            height: 270,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'cardid',
                    name: 'cardid'
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid'
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'status',
                    name: 'status'
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        fieldLabel: '卡号',
                        itemId: 'cardno',
                        name: 'cardno',
                        allowBlank: false,
                        value: (CamPus.AppSession.cardtype == 'id' ? config.cardsn : '')
                    },
                    {
                        xtype: 'datefield',
                        fieldLabel: '有效期',
                        itemId: 'enddate',
                        name: 'enddate',
                        value: '',
                        format: 'Y-m-d',
                        readOnly: true,
                        allowBlank: true
                    },
                    {
                        fieldLabel: '持卡人',
                        itemId: 'infoname',
                        name: 'infoname',
                        allowBlank: true,
                        readOnly: true
                    }
                    ]
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'numberfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [
                        {
                            fieldLabel: '主钱包余额',
                            itemId: 'balance',
                            name: 'balance',
                            decimalPrecision: 2,
                            value: 0,
                            allowBlank: true,
                            readOnly: true
                        },
                        {
                            fieldLabel: '副钱包金额',
                            itemId: 'vicewallet',
                            name: 'vicewallet',
                            allowBlank: true,
                            decimalPrecision: 2,
                            value: 0,
                            readOnly: true
                        },
                        {
                            fieldLabel: '水控钱包金额',
                            itemId: 'waterwallet',
                            name: 'waterwallet',
                            allowBlank: true,
                            decimalPrecision: 2,
                            value: 0,
                            readOnly: true
                        },
                        {
                            fieldLabel: '制卡费',
                            itemId: 'deposit',
                            name: 'deposit',
                            allowBlank: true,
                            decimalPrecision: 2,
                            value: 0,
                            readOnly: true
                        }
                    ]
                },
                me.cardlistgrid,
                me.waterlistgrid,
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'numberfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [
                        {
                            fieldLabel: '退款钱包',
                            xtype: 'comboxdictionary',
                            name: 'paywallet',
                            groupCode: 'SYS0000032',
                            valueField: 'code',
                            allowBlank: false,
                            editable: false,
                            where: 'code <> 2',
                            value: 1,
                            listeners: {
                                change: function (combox, newValue, oldValue, eOpts) {
                                    if(newValue == 1){
                                        me.waterlistgrid.setHidden(true)
                                        me.cardlistgrid.setHidden(false)
                                        me.cardliststore.load();
                        
                                    }else {
                                        me.cardlistgrid.setHidden(true)
                                        me.waterlistgrid.setHidden(false)
                                        me.waterliststore.load();
                                    }
                                }
                            }
                        },
                        {
                            fieldLabel: '退款金额',
                            itemId: 'refundamount',
                            name: 'refundamount',
                            allowBlank: false,
                            fieldStyle: 'font-weight: bold;',
                            minValue: 0.01,
                            value: 0
                        },
                        {
                            xtype: 'textfield',
                            fieldLabel: '备注',
                            name: 'des',
                            flex: 2,
                            allowBlank: true
                        }
                    ]
                }
                ]
            }]
        });

        me.callParent(arguments);

        if(CamPus.AppSession.cardtype == 'id' && config.cardsn){
            me.onReadCard(config.cardsn);
        }
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            var refundamount = form.down('numberfield[itemId=refundamount]');
            var balance = form.down('numberfield[itemId=balance]');
            var waterwallet = form.down('numberfield[itemId=waterwallet]');
            var paywallet = form.down('comboxdictionary[itemId=paywallet]');
            var status = form.down('hiddenfield[itemId=status]');
            if (status.getValue() == 0) {
                toast('无效卡禁止退款！');
                return;
            }
            if(paywallet == 1){
                if (refundamount.getValue() > balance.getValue()) {
                    toast('退款金额大于账户余额！');
                    return;
                }
            }else if(paywallet == 3){
                if (refundamount.getValue() > waterwallet.getValue()) {
                    toast('退款金额大于账户余额！');
                    return;
                }
            }
            
            form.submit({
                url: '/Card/CardManage/SaveRefund',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        var refundamount = action.result.data.refundamount;
                        me.grid.getStore().loadPage(1);
                        me.close();
                        toast('操作成功,退款：￥' + refundamount + '元');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    },
    onReadCard: function (CardNO) {
        var me = this;
        Ext.Ajax.request({
            url: '/Card/CardManage/getCardInfo',
            params: {
                cardno: CardNO
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    var infoid = me.down('hiddenfield[itemId=infoid]');
                    infoid.setValue(result.data.infoid);
                    
                    me.cardlistgrid.getStore().on('beforeload', function (store) {
                        Ext.apply(store.proxy.extraParams, {
                            cardid: result.data.uid
                        });
                    });
                    me.cardlistgrid.getStore().loadPage(1);

                    me.waterlistgrid.getStore().on('beforeload', function (store) {
                        Ext.apply(store.proxy.extraParams, {
                            cardid: result.data.uid
                        });
                    });

                    me.waterlistgrid.getStore().loadPage(1);

                    me.down('hiddenfield[itemId=cardid]').setValue(result.data.uid);
                    me.down('hiddenfield[itemId=status]').setValue(result.data.status);
                    if (parseInt(result.data.status) == 0) {
                        toast('当前卡' + me.getStatusName(result.data.status) + ',禁止退款！');
                    }
                    me.down('numberfield[itemId=balance]').setValue(result.data.balance);
                    me.down('numberfield[itemId=deposit]').setValue(result.data.deposit);
                    me.down('numberfield[itemId=vicewallet]').setValue(result.data.vicewallet);
                    me.down('numberfield[itemId=waterwallet]').setValue(result.data.waterwallet);
                    me.down('textfield[itemId=cardno]').setValue(result.data.cardno);
                    var infoname = result.data.orgname + result.data.name + result.data.usedes;
                    if (result.data.infotype == 2 && result.data.intoyear) {
                        infoname = result.data.intoyear + infoname;
                    }
                    me.down('textfield[itemId=infoname]').setValue(infoname);
                    me.down('datefield[itemId=enddate]').setValue(new Date(result.data.enddate));
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    getStatusName: function (status) {
        var txt = '';
        switch (status) {
            case 0:
                txt = '无效';
                break;
            case 1:
                txt = '正常';
                break;
            case 2:
                txt = '挂失';
                break;
            default:
                break;
        }
        return txt;
    }
});


Ext.define('CamPus.view.card.cardmanage.ReturnCardWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '退卡',
    iconCls: 'x-fa fa-vcard',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.cardlistgrid = Ext.create('Ext.grid.Panel', {
            title: '最近交易流水',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            margin: '0 0 15 0',
            viewConfig: {
                enableTextSelection: true
            },
            store: Ext.create('Ext.data.Store', {
                proxy: {
                    type: 'ajax',
                    url: '/Card/CardManage/getCardTransactionDetail',
                    actionMethods: {
                        read: 'POST'
                    },
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        totalProperty: 'total',
                        successProperty: 'success',
                        messageProperty: 'msg'
                    },
                    extraParams: { cardid: '' }
                },
                autoLoad: false
            }),
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '订单号',
                    dataIndex: 'uid',
                    width: 200,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'cardno',
                    width: 140,
                    sortable: false
                },
                {
                    text: '类型',
                    dataIndex: 'tradetype',
                    width: 60,
                    sortable: false
                },
                {
                    text: '金额',
                    dataIndex: 'money',
                    width: 65,
                    sortable: false
                },
                {
                    text: '状态',
                    dataIndex: 'orderstatus',
                    width: 55,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '创建';
                            case 1:
                                metaData.style += 'color:#c35f09;';
                                return '验证';
                            case 2:
                                metaData.style += 'color:#7bc309;';
                                return '完成';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '支付方式',
                    dataIndex: 'paytype',
                    width: 75,
                    sortable: false
                },
                {
                    text: '交易渠道',
                    dataIndex: 'payway',
                    width: 75,
                    sortable: false
                },
                {
                    text: '交易时间',
                    dataIndex: 'tradedate',
                    width: 160,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    flex: 1,
                    minWidth: 120,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            height: 270,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'cardid',
                    name: 'cardid'
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid'
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'status',
                    name: 'status'
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        fieldLabel: '卡号',
                        itemId: 'cardno',
                        name: 'cardno',
                        allowBlank: false,
                        value: (CamPus.AppSession.cardtype == 'id' ? config.cardsn : '')
                    },
                    {
                        xtype: 'datefield',
                        fieldLabel: '有效期',
                        itemId: 'enddate',
                        name: 'enddate',
                        value: '',
                        format: 'Y-m-d',
                        readOnly: true,
                        allowBlank: true
                    },
                    {
                        fieldLabel: '持卡人',
                        itemId: 'infoname',
                        name: 'infoname',
                        allowBlank: true,
                        readOnly: true
                    }
                    ]
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'numberfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [
                        {
                            fieldLabel: '主钱包余额',
                            itemId: 'balance',
                            name: 'balance',
                            decimalPrecision: 2,
                            value: 0,
                            allowBlank: true,
                            readOnly: true
                        },
                        {
                            fieldLabel: '副钱包金额',
                            itemId: 'vicewallet',
                            name: 'vicewallet',
                            allowBlank: true,
                            decimalPrecision: 2,
                            value: 0,
                            readOnly: true
                        },
                        {
                            fieldLabel: '制卡费',
                            itemId: 'deposit',
                            name: 'deposit',
                            allowBlank: true,
                            decimalPrecision: 2,
                            value: 0,
                            readOnly: true
                        }
                    ]
                },
                me.cardlistgrid,
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'numberfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [
                        {
                            xtype: 'radiogroup',
                            fieldLabel: '制卡费',
                            items: [
                                { name: 'isdeposit', boxLabel: '退还', inputValue: true },
                                { name: 'isdeposit', boxLabel: '不退还', inputValue: false, checked: true }
                            ]
                        },
                        {
                            xtype: 'textfield',
                            fieldLabel: '备注',
                            itemId: 'des',
                            name: 'des',
                            flex: 2,
                            allowBlank: true
                        }
                    ]
                }
                ]
            }]
        });

        me.callParent(arguments);
        if(CamPus.AppSession.cardtype == 'id' && config.cardsn){
            me.onReadCard(config.cardsn);
        }
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            var status = form.down('hiddenfield[itemId=status]');
            if (status.getValue() == 0) {
                toast('无效卡禁止退卡！');
                return;
            }
            form.submit({
                url: '/Card/CardManage/SaveReturnCard',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        var orderno = action.result.data.orderno;
                        var realrefund = action.result.data.realrefund;
                        var deposit = action.result.data.deposit;
                        var mstxt = Ext.String.format('实际需退款￥{0}元', realrefund + deposit);
                        if (CamPus.AppSession.cardtype == 'ic') {
                            window.onInitCard(function () {
                                me.grid.getStore().loadPage(1);
                                me.close();
                                toast('操作成功！' + mstxt);
                            });
                        } else {
                            me.grid.getStore().loadPage(1);
                            me.close();
                            toast('操作成功！' + mstxt);
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    },
    onReadCard: function (CardNO) {
        var me = this;
        Ext.Ajax.request({
            url: '/Card/CardManage/getCardInfo',
            params: {
                cardno: CardNO
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    var infoid = me.down('hiddenfield[itemId=infoid]');
                    infoid.setValue(result.data.infoid);
                    me.cardlistgrid.getStore().on('beforeload', function (store) {
                        Ext.apply(store.proxy.extraParams, {
                            cardid: result.data.uid
                        });
                    });
                    me.cardlistgrid.getStore().loadPage(1);
                    me.down('hiddenfield[itemId=cardid]').setValue(result.data.uid);
                    me.down('hiddenfield[itemId=status]').setValue(result.data.status);
                    if (parseInt(result.data.status) == 0) {
                        toast('当前卡' + me.getStatusName(result.data.status) + ',禁止退卡！');
                    }
                    me.down('numberfield[itemId=deposit]').setValue(result.data.deposit);
                    me.down('numberfield[itemId=vicewallet]').setValue(result.data.vicewallet);
                    me.down('numberfield[itemId=balance]').setValue(result.data.balance);
                    me.down('textfield[itemId=cardno]').setValue(result.data.cardno);
                    var infoname = result.data.orgname + result.data.name + result.data.usedes;
                    if (result.data.infotype == 2 && result.data.intoyear) {
                        infoname = result.data.intoyear + infoname;
                    }
                    me.down('textfield[itemId=infoname]').setValue(infoname);
                    me.down('datefield[itemId=enddate]').setValue(new Date(result.data.enddate));
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    getStatusName: function (status) {
        var txt = '';
        switch (status) {
            case 0:
                txt = '无效';
                break;
            case 1:
                txt = '正常';
                break;
            case 2:
                txt = '挂失';
                break;
            default:
                break;
        }
        return txt;
    }
    
});

Ext.define('CamPus.view.card.cardmanage.GiveCardEditWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '卡片信息',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.cardlistgrid = Ext.create('Ext.grid.Panel', {
            title: '该人员已领相关卡',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            margin: '0 0 10 0',
            store: Ext.create('Ext.data.Store', {
                proxy: {
                    type: 'ajax',
                    url: '/Card/TeachStudCard/getOtherCardList',
                    actionMethods: {
                        read: 'POST'
                    },
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        totalProperty: 'total',
                        successProperty: 'success',
                        messageProperty: 'msg'
                    },
                    extraParams: { infoid: '' }
                },
                autoLoad: false,
                listeners: {
                    beforeload: function (store, operation, eOpts) {
                        Ext.apply(store.proxy.extraParams, {
                            infoid: me.down('hiddenfield[itemId=infoid]').getValue()
                        });
                    }
                }
            }),
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '类型',
                    dataIndex: 'ismain',
                    width: 50,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return '主卡'
                        } else {
                            return '子卡';
                        }
                    }
                },
                { text: '卡号', dataIndex: 'cardno', width: 110, sortable: false },
                { text: '持卡人', dataIndex: 'usedes', width: 100, sortable: false },
                {
                    text: '主钱包余额',
                    dataIndex: 'balance',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '副钱包余额',
                    dataIndex: 'vicewallet',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '水控钱包余额',
                    dataIndex: 'waterwallet',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '制卡费',
                    dataIndex: 'deposit',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '无效';
                            case 1:
                                return '正常';
                            case 2:
                                metaData.style += 'color:red;';
                                return '挂失';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '发卡时间',
                    dataIndex: 'createdate',
                    width: 160,
                    sortable: true,
                    sortIndex: 'c.createdate',
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    minWidth: 160,
                    flex: 1,
                    sortable: false,
                    renderer:function (value, metaData, record, rowIdx, colIdx, store){
                        if(value){
                            metaData.tdAttr = 'data-qtip="'+ value + '"';
                        }
                        return value;
                    }
                }
            ],
            selModel: 'rowmodel',
            height: 260
        });

        Ext.apply(me, {
            aotuAddNew: true,
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid',
                    allowBlank: false
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infotype',
                    name: 'infotype'
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'status',
                    name: 'status'
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'ismain',
                    name: 'ismain',
                    value: 1
                }, {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'textfield',
                        fieldLabel: '姓名',
                        itemId: 'infoname',
                        name: 'infoname',
                        readOnly: true,
                        allowBlank: false
                    }, {
                        xtype: 'comboxdictionary',
                        name: 'usedes',
                        groupCode: 'SYS0000005',
                        valueField: 'name',
                        allowBlank: false,
                        editable: true,
                        fieldLabel: '持卡人',
                        listeners: {
                            change: function (combox, newValue, oldValue, eOpts) {
                                if (combox.selection.get('code') == '0') {
                                    me.down('hiddenfield[itemId=ismain]').setValue(1);
                                } else {
                                    me.down('hiddenfield[itemId=ismain]').setValue(0);
                                }
                            }
                        }
                    }]
                },
                me.cardlistgrid,
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        fieldLabel: '卡号',
                        itemId: 'cardno',
                        name: 'cardno',
                        allowBlank: false
                    },
                    {
                        xtype: 'datefield',
                        fieldLabel: '有效期',
                        itemId: 'enddate',
                        name: 'enddate',
                        value: '2050-07-01',
                        format: 'Y-m-d'
                    },
                    {
                        xtype: 'numberfield',
                        fieldLabel: '制卡费',
                        name: 'deposit',
                        allowBlank: false,
                        value: 0,
                        minValue: 0
                    }
                    ]
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'numberfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        fieldLabel: '开户金额',
                        name: 'balance',
                        allowBlank: false,
                        value: 0,
                        minValue: 0
                    },
                    {
                        fieldLabel: '支付方式',
                        xtype: 'comboxdictionary',
                        name: 'paytype',
                        groupCode: 'SYS0000007',
                        valueField: 'code',
                        allowBlank: false,
                        editable: false,
                        value: 3
                    },
                    {
                        fieldLabel: '付款渠道',
                        xtype: 'comboxdictionary',
                        name: 'payway',
                        groupCode: 'SYS0000008',
                        valueField: 'code',
                        allowBlank: false,
                        editable: false,
                        value: 3
                    }
                    ]
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [
                        {
                            fieldLabel: '超额密码',
                            itemId: 'excesspwd',
                            name: 'excesspwd',
                            vtype: 'code6',
                            maxLength: 6,
                            minLength: 6,
                            emptyText: '6位纯数字字符串',
                            allowBlank: true
                        },
                        {
                            fieldLabel: '备注',
                            xtype: 'textfield',
                            name: 'des',
                            maxLength: 100,
                            flex: 2,
                            allowBlank: true
                        }
                    ]
                }
                ]
            }]
        });
        me.callParent(arguments);
        me.InitHandle(0);
    },
    InitHandle: function (index) {
        if (index > this.handleData.length - 1) {
            toast('已选择的人员处理完毕...');
            this.close();
            return;
        }
        this.handleIndex = index;
        var record = this.handleData[this.handleIndex];
        this.down('hiddenfield[itemId=infoid]').setValue(record.get('uid'));
        this.down('hiddenfield[itemId=infotype]').setValue(record.get('infotype'));
        this.down('hiddenfield[itemId=status]').setValue(record.get('status'));
        this.down('textfield[itemId=infoname]').setValue(record.get('name'));
        this.down('textfield[itemId=cardno]').setValue('');
        this.down('progress[itemId=cardprogress]').setValue(this.handleIndex / this.handleData.length);
        var txt = Ext.String.format('已处理{0}人，待处理{1}人', this.handleIndex, this.handleData.length - this.handleIndex);
        this.down('progress[itemId=cardprogress]').setText(txt);
        this.cardlistgrid.getStore().loadPage(1);
    },
    SaveData: function (win) {
        var me = win;
        var form = win.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudCard/SaveCard',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.InitHandle(me.handleIndex + 1);
                        me.persongrid.getStore().reload();
                        me.grid.getStore().reload();
                        toast('保存成功！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            var ismain = form.down('hiddenfield[itemId=ismain]');
            var infoid = form.down('hiddenfield[itemId=infoid]');
            var CardNO = form.down('textfield[itemId=cardno]');
            Ext.Ajax.request({
                url: '/Card/TeachStudCard/ExistsCard',
                params: { cardno: CardNO.getValue(), ismain: ismain.getValue(), infoid: infoid.getValue() },
                method: 'POST',
                success: function (response) {
                    var result = Ext.JSON.decode(response.responseText);
                    if (!result.success) {
                        me.SaveData(me);
                    } else {
                        toast(result.msg);
                    }
                }
            });
        }
    },
    readCard: function () {
        var me = this;
        window.onReadCard(function (identid, CardNO) {
            var form = me.down('form[itemId=form]');
            form.down('textfield[itemId=cardno]').setValue(CardNO);
        });
    },
    nextCard: function () {
        var me = this;
        me.InitHandle(me.handleIndex + 1);
    }
});


Ext.define('CamPus.view.card.cardmanage.RecoverCardWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '补卡',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.cardlistgrid = Ext.create('Ext.grid.Panel', {
            title: '该人员已领相关卡',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            margin: '0 0 10 0',
            store: Ext.create('Ext.data.Store', {
                proxy: {
                    type: 'ajax',
                    url: '/Card/TeachStudCard/getOtherCardList',
                    actionMethods: {
                        read: 'POST'
                    },
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        totalProperty: 'total',
                        successProperty: 'success',
                        messageProperty: 'msg'
                    },
                    extraParams: { infoid: '' }
                },
                autoLoad: true,
                listeners: {
                    beforeload: function (store, operation, eOpts) {
                        Ext.apply(store.proxy.extraParams, {
                            infoid: me.down('hiddenfield[itemId=infoid]').getValue()
                        });
                    }
                }
            }),
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '类型',
                    dataIndex: 'ismain',
                    width: 50,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return '主卡'
                        } else {
                            return '子卡';
                        }
                    }
                },
                { text: '卡号', dataIndex: 'cardno', width: 110, sortable: false },
                { text: '持卡人', dataIndex: 'usedes', width: 100, sortable: false },
                {
                    text: '主钱包余额',
                    dataIndex: 'balance',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '副钱包余额',
                    dataIndex: 'vicewallet',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '水控钱包余额',
                    dataIndex: 'waterwallet',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '制卡费',
                    dataIndex: 'deposit',
                    width: 80,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '无效';
                            case 1:
                                return '正常';
                            case 2:
                                metaData.style += 'color:red;';
                                return '挂失';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '有效期',
                    dataIndex: 'enddate',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d')
                        }
                    }
                },
                {
                    text: '发卡时间',
                    dataIndex: 'createdate',
                    width: 160,
                    minWidth: 140,
                    sortable: true,
                    sortIndex: 'c.createdate',
                    flex: 1,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                }
            ],
            selModel: 'rowmodel',
            height: 280,
            listeners: {
                select: function (grid, record, index, eOpts) {
                    me.down('hiddenfield[itemId=cardid]').setValue(record.get('uid'));
                }
            }
        });

        Ext.apply(me, {
            aotuAddNew: true,
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'cardid',
                    name: 'cardid'
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid',
                    value: config.infoid
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'status',
                    name: 'status',
                    value: config.status
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infotype',
                    name: 'infotype',
                    value: config.infotype
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'ismain',
                    name: 'ismain',
                    value: 1
                }, {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'textfield',
                        fieldLabel: '姓名',
                        itemId: 'infoname',
                        name: 'infoname',
                        readOnly: true,
                        allowBlank: false,
                        value: config.infoname
                    }, {
                        xtype: 'comboxdictionary',
                        name: 'usedes',
                        groupCode: 'SYS0000005',
                        valueField: 'name',
                        allowBlank: false,
                        editable: true,
                        fieldLabel: '持卡人',
                        listeners: {
                            change: function (combox, newValue, oldValue, eOpts) {
                                if (combox.selection.get('code') == '0') {
                                    me.down('hiddenfield[itemId=ismain]').setValue(1);
                                } else {
                                    me.down('hiddenfield[itemId=ismain]').setValue(0);
                                }
                            }
                        }
                    }]
                },
                me.cardlistgrid,
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        fieldLabel: '卡号',
                        itemId: 'cardno',
                        name: 'cardno',
                        allowBlank: false
                    },
                    {
                        xtype: 'datefield',
                        fieldLabel: '有效期',
                        itemId: 'enddate',
                        name: 'enddate',
                        value: '2050-07-01',
                        format: 'Y-m-d'
                    },
                    {
                        xtype: 'numberfield',
                        fieldLabel: '制卡费',
                        name: 'deposit',
                        allowBlank: false,
                        value: 0,
                        minValue: 0
                    }
                    ]
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [
                        {
                            fieldLabel: '超额密码',
                            itemId: 'excesspwd',
                            name: 'excesspwd',
                            vtype: 'code6',
                            maxLength: 6,
                            minLength: 6,
                            emptyText: '6位纯数字字符串',
                            allowBlank: true
                        },
                        {
                            fieldLabel: '备注',
                            xtype: 'textfield',
                            name: 'des',
                            flex: 2,
                            maxLength: 100,
                            allowBlank: true
                        }
                    ]
                }
                ]
            }]
        });
        me.callParent(arguments);
    },
    SaveData: function (win) {
        var me = win;
        var form = win.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudCard/SaveRecoverCard',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.persongrid.getStore().reload();
                        me.grid.getStore().reload();
                        me.close();
                        toast('补卡成功！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    },
    readCard: function () {
        var me = this;
        window.onReadCard(function (identid, CardNO) {
            var form = me.down('form[itemId=form]');
            form.down('textfield[itemId=cardno]').setValue(CardNO);
        });
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        var cardid = me.down('hiddenfield[itemId=cardid]').getValue();
        if (!cardid) {
            toast('请选择原卡');
            return;
        }
        if (CamPus.AppSession.cardtype == 'ic') {
            if (form.getForm().isValid()) {
                var ismain = form.down('hiddenfield[itemId=ismain]');
                var infoid = form.down('hiddenfield[itemId=infoid]');
                var cardno = form.down('textfield[itemId=cardno]');
                Ext.Ajax.request({
                    url: '/Card/TeachStudCard/ExistsRecoverCard',
                    params: { cardno: cardno.getValue(), ismain: ismain.getValue(), infoid: infoid.getValue(), oldcardid: cardid },
                    method: 'POST',
                    success: function (response) {
                        var result = Ext.JSON.decode(response.responseText);
                        if (!result.success) {
                            me.SaveData(me);
                        } else {
                            toast(result.msg);
                        }
                    }
                });
            }
        } else {
            me.SaveData(me);
        }
    }
});


Ext.define('CamPus.view.card.cardmanage.CardDelayWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '卡片有效期延期',
    iconCls: 'x-fa fa-vcard',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.cardlistgrid = Ext.create('Ext.grid.Panel', {
            title: '该人员已领相关卡',
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            margin: '0 0 10 0',
            store: Ext.create('Ext.data.Store', {
                proxy: {
                    type: 'ajax',
                    url: '/Card/TeachStudCard/getOtherCardList',
                    actionMethods: {
                        read: 'POST'
                    },
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        totalProperty: 'total',
                        successProperty: 'success',
                        messageProperty: 'msg'
                    },
                    extraParams: { infoid: config.record.get('uid') }
                },
                autoLoad: true
            }),
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '类型',
                    dataIndex: 'ismain',
                    width: 50,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return '主卡'
                        } else {
                            return '子卡';
                        }
                    }
                },
                { text: '卡号', dataIndex: 'cardno', width: 110, sortable: false },
                { text: '持卡人', dataIndex: 'usedes', width: 100, sortable: false },
                {
                    text: '主钱包余额',
                    dataIndex: 'balance',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '副钱包余额',
                    dataIndex: 'vicewallet',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '水控钱包余额',
                    dataIndex: 'waterwallet',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '制卡费',
                    dataIndex: 'deposit',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '无效';
                            case 1:
                                return '正常';
                            case 2:
                                metaData.style += 'color:red;';
                                return '挂失';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '有效期',
                    dataIndex: 'enddate',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d')
                        }
                    }
                },
                {
                    text: '发卡时间',
                    dataIndex: 'createdate',
                    width: 160,
                    minWidth: 140,
                    sortable: true,
                    sortIndex: 'c.createdate',
                    flex: 1,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                }
            ],
            selModel: 'checkboxmodel',
            height: 260
        });

        Ext.apply(me, {
            aotuAddNew: true,
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid',
                    allowBlank: false,
                    value: config.record.get('uid')
                },
                {
                    xtype: 'hiddenfield',
                    itemId: 'cardids',
                    name: 'cardids'
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaultType: 'textfield',
                    defaults: {
                        flex: 1,
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'textfield',
                        fieldLabel: '姓名',
                        itemId: 'infoname',
                        name: 'infoname',
                        readOnly: true,
                        allowBlank: false,
                        value: config.record.get('name')
                    },
                    {
                        xtype: 'textfield',
                        fieldLabel: window.applang.get('infocode'),
                        itemId: 'infocode',
                        name: 'infocode',
                        readOnly: true,
                        allowBlank: false,
                        value: config.record.get('code')
                    }]
                },
                me.cardlistgrid,
                {
                    xtype: 'datefield',
                    fieldLabel: '有效期至',
                    itemId: 'toenddate',
                    name: 'toenddate',
                    value: '2050-01-01',
                    format: 'Y-m-d'
                }]
            }]
        });

        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if(me.record.get('status') == '0'){
            toast('人员已离职');
            return;
        }
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            var cardids = [];
            Ext.each(me.cardlistgrid.getSelection(), function (item, index) {
                if (item.get('status') == 1) {
                    cardids.push(item.get('uid'));
                }
            });
            if (cardids.length == 0) {
                toast('请勾选需要延期的有效卡片');
                return;
            }
            form.down('hiddenfield[itemId=cardids]').setValue(cardids.join(','));
            form.submit({
                url: '/Card/TeachStudCard/SaveDelayCard',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.persongrid.getStore().reload();
                        me.grid.getStore().reload();
                        me.close();
                        toast('操作成功！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.card.cardmanage.BatchReturnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择人员',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.card.cardmanage.OrgTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get('orgname3') + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function (tree, record, index, eOpts) {
                    me.store.loadPage(1);
                }
            }
        });

        me.store = Ext.create('CamPus.view.card.cardmanage.TechInfoStore', {
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });

                    Ext.apply(store.proxy.extraParams, {
                        orgcode: orgcode,
                        intoyear: !intoyear.getValue() ? '0' : intoyear.getValue(),
                        infotype: infotype.getValue(),
                        viewchild: viewchild.getValue(),
                        selecteduid: JSON.stringify(selecteduid)
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'infotype',
                groupCode: 'SYS0000019',
                insertAll: '0|全部',
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                        var url = '/Card/OrgFramework/getCollegeClassTree';
                        if (newValue == '0') {
                            url = '/Card/OrgFramework/getAllOrgTree';
                        }
                        else if (newValue == '2') {
                            intoyear.show();
                        } else {
                            intoyear.setValue(0);
                            intoyear.hide();
                            url = '/Card/OrgFramework/getOrgTree';
                        }
                        me.treestore.proxy.url = url;
                        me.treestore.load();
                        me.store.load();
                    }
                }
            }, {
                xtype: 'comboxdictionary',
                itemId: 'intoyear',
                groupCode: 'SYS0000004',
                insertAll: '全部',
                width: 110,
                defaultSelectFirst: true,
                hidden: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            }, {
                xtype: 'searchfield',
                width: 150,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '关键词...'
            }, {
                xtype: 'checkboxfield',
                boxLabel: '显示子节点人员',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }]
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            },
            selModel: 'checkboxmodel'
        });

        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选人员：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.store.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['card', 'cardsn', 'name', 'infotype', 'uid', 'code', 'orgname', 'orgcode'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 220
        });

        Ext.apply(me, {
            items: [me.tree, me.grid, me.selectgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.selectgrid.getStore().getData().items.length == 0) {
            toast('请选择人员信息，选择以后拖拽至右侧表格');
            return;
        }
        var infoData = [];
        Ext.each(me.selectgrid.getStore().getData().items, function (item, i) {
            infoData.push({
                infoId: item.get('uid'),
                cardNo: item.get('card')
            });
        });
        Ext.Ajax.request({
            url: '/Card/CardManage/batchReturnCard',
            params: {
                infoData: JSON.stringify(infoData)
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.maingrid.getStore().reload();
                    toast('保存成功...');
                    me.close();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});
