Ext.define('CamPus.view.card.position.positionController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.positionController',
    view: ['positionView'],
    init: function () {
        var me = this;
        me.setControl({
            'gridpanel[itemId=positionlist]': {
                select: me.onpositionSelect
            },
            '*[itemId=add]': {
                click: me.add
            },
            '*[itemId=edit]': {
                click: me.edit
            },
            '*[itemId=del]': {
                click: me.del
            },
            '*[itemId=addinfo]': {
                click: me.addinfo
            },
            '*[itemId=deleteinfo]': {
                click: me.delinfo
            },
            '*[itemId=clearvice]': {
                click: me.onClearViceClick
            },
            '*[itemId=importExcel]': {
                click: me.onImportUpdateClick
            }
        });
        me.positionStore = me.view.positionStore;
        me.grid = me.view.grid;
        me.groupStore = me.view.groupStore;
        me.groupgrid = me.view.groupgrid;

        me.groupStore.on('beforeload', function (store) {
            var name = '';
            if (me.grid.getSelection().length > 0) {
                name = me.grid.getSelection()[0].get('name');
            }
            Ext.apply(store.proxy.extraParams, {
                name: name
            });
        });

    },
    onpositionSelect: function () {
        var me = this;
        if (window.tip) {
            window.tip.close();
            window.tip = null;
        }
        me.groupStore.reload();
    },

    add: function () {
        var me = this;
        var win = Ext.create('CamPus.view.card.position.editViceWindow', {
            grid: me.grid
        });
        win.show();
    },

    edit: function () {
        var me = this;
        if (me.grid.getSelection().length === 0) {
            toast('请选择补贴方案...');
            return;
        }

        if (me.grid.getSelection().length > 1) {
            toast('一次只能修改一个方案...');
            return;
        }

        var record = me.grid.getSelection()[0];

        var win = Ext.create('CamPus.view.card.position.editViceWindow', {
            grid: me.grid,
            record: me.record
        });

        var form = win.down('form[itemId=placeform]');
        form.loadRecord(me.grid.getSelection()[0]);
        win.show();
    },

    del: function () {
        var me = this;
        if (me.grid.getSelection().length === 0) {
            toast('请选择要删除的记录');
            return;
        }

        var uids = [];

        Ext.each(me.grid.getSelection(), function (item, index) {
            uids.push(item.get('uid'));
        });

        Ext.Msg.show({
            title: '删除职位',
            message: '删除后无法撤销',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/Delposi',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                toast("删除方案成功");
                                me.positionStore.reload();
                            } else {
                                toast(result.msg);
                            }
                        }
                    });
                }
            }
        });


    },

    addinfo: function () {
        var me = this;
        if (me.grid.getSelection().length === 0) {
            toast('请选择职位...');
            return;
        }

        if (me.grid.getSelection().length > 1) {
            toast('一次只能选择一个职位...');
            return;
        }
        var name = me.grid.getSelection()[0].get('name')
        var win = Ext.create('CamPus.view.card.position.AddGroupnamelistWindow', {
            name:name,
            groupgrid:me.grid
        });
        win.show();
    },

    delinfo: function () {
        var me = this;
        if (me.groupgrid.getSelection().length === 0) {
            toast('请选择要清除职位的人员');
            return;
        }

        var uids = [];
        Ext.each(me.groupgrid.getSelection(), function (item, index) {
            uids.push(item.get('uid'));
        });

        Ext.Msg.show({
            title: '删除',
            message: '确认删除？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/Delposition',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                toast("清除成功");
                                me.groupStore.reload();
                            } else {
                                toast(result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onClearViceClick: function () {
        var me = this;
        var groupid = '';
        var infoids = [];
        //补贴归零
        if (me.grid.getSelection().length > 0) {
            groupid = me.grid.getSelection()[0].get("uid")
        }
        Ext.each(me.groupgrid.getSelection(), function (item, index) {
            infoids.push(item.get('infoid'));
        });
        if (infoids.length == 0 && !groupid) {
            toast("请选择补贴方案或具体人员");
            return;
        }

        Ext.Msg.show({
            title: '补贴归零',
            message: '确认要归零所选人员副钱包余额？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/ViceWall/Clearposition',
                        params: {
                            groupid: groupid,
                            infoids: infoids.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                toast("操作成功");
                            } else {
                                toast(result.msg);
                            }
                        }
                    });
                }
            }
        });
    },

    onImportUpdateClick: function () {
        var me = this;
        if (me.grid.getSelection().length === 0) {
            toast('请选择补贴方案...');
            return;
        }
        var win = Ext.create('CamPus.view.card.position.ImportExcelWindow', {
            maingrid: me.grid,
            groupid: me.grid.getSelection()[0].get("uid")
        });
        win.show();
    },
});