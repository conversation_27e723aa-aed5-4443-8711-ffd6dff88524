Ext.define('CamPus.view.card.teacher.TeacherStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: false,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getTeacherList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { orgcode: 'x' }
    }
});


Ext.define('CamPus.view.card.teacher.OrgTreeStore', {
    extend: 'Ext.data.TreeStore',
    remoteSort: false,
    autoLoad: false,
    root: {
        uid: 'root',
        name: window.applang.get('orgname'),
        expanded: true
    },
    proxy: {
        type: 'ajax',
        url: '/Card/OrgFramework/getOrgTree',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json'
        },
        extraParams: {

        }
    }
});


Ext.define('CamPus.view.card.teacher.CollegeClassTreeStore', {
    extend: 'Ext.data.TreeStore',
    remoteSort: false,
    autoLoad: false,
    root: {
        uid: 'root',
        name:window.applang.get("orgname3"),
        expanded: true
    },
    proxy: {
        type: 'ajax',
        url: '/Card/OrgFramework/getCollegeClassTree',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json'
        },
        extraParams: {}
    }
});

Ext.define('CamPus.view.card.teacher.ChildTechInfoStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: false,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getChildInfoList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { orgcode: '', infotype:1, viewchild: 'true' }
    }
});

Ext.define('CamPus.view.card.teacher.parentInfoStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getParentInfoList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { uids: '' }
    }
});


Ext.define('CamPus.view.card.teacher.getGroupStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getGroupStore',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { infoId: '' }
    }
});

Ext.define('CamPus.view.card.teacher.parentChildInfoStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: false,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getParentChildInfoList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { parendid: '' }
    }
});


Ext.define('CamPus.view.card.teacher.InfoLabelStore', {
    extend: 'Ext.data.Store',
    fields: ['label'],
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getTInfoLabel',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: {}
    }
});

Ext.define('CamPus.view.card.teacher.InfoLabelComboxStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getInfoLabel',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { orgcode: '', infotype: 1, viewchild: 'true' }
    }
});

Ext.define('CamPus.view.card.teacher.GetTeacherUidStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: false,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getTeacherUid',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { orgcode: 'x' }
    }
});

Ext.define('CamPus.view.card.teach.PermissionGroup', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Face/PermissionGroup/getPermissionGroup',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: {}
    }
});

Ext.define('CamPus.view.card.teacher.getFaceStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Face/IntoFace/getFirstImg',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { infoid: '' }
    }
});

