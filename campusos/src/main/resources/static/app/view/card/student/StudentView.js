Ext.define('CamPus.view.card.student.StudentView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.StudentView",
    controller: 'StudentController',
    requires: [
        'Ext.dd.DropZone',
        'CamPus.view.card.student.StudentStore',
        'CamPus.view.card.student.StudentController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.infouidstore = Ext.create('CamPus.view.card.teacher.GetTeacherUidStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {});
                }
            }
        });
        me.infouidstore.load();

        me.treestore = Ext.create('CamPus.view.card.student.CollegeClassTreeStore', {});

        var collegeclasstbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: '院系、专业、班级关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            // title: '院系、专业、班级',
            itemId: 'collegeclasstree',
            tbar: collegeclasstbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 280,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true,
                listeners: {
                    refresh: function (view, eOpts) {
                        view.select(0);
                    }
                }
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            setPathName: function (node) {
                this.pathName = (this.pathName ? node.get('name') + '/' + this.pathName : node.get('name'));
                if (node.parentNode && node.parentNode.id != 'root') {
                    this.setPathName(node.parentNode);
                }
            },
            listeners: {
                render: function (tree) {
                    tree.dropZone = Ext.create('Ext.dd.DropZone', tree.el, {
                        getTargetFromEvent: function (e) {
                            return e.getTarget(tree.getView().rowSelector);
                        },
                        onNodeEnter: function (nodeData, dd, e, data) {
                            tree.body.stopAnimation();
                            tree.body.highlight();
                        },
                        onNodeOver: function (nodeData, dd, e, data) {
                            var sourceEl = e.getTarget(tree.itemSelector, 10);
                            if (sourceEl) {
                                var record = tree.view.getRecord(sourceEl);
                                if (record.get('orgtype') == '4') {
                                    return "x-dd-drop-ok-add";
                                } else {
                                    return "x-dd-drop-nodrop";
                                }
                            } else {
                                return "x-dd-drop-nodrop";
                            }
                        },
                        onNodeDrop: function (node, dd, e, data) {
                            var sourceEl = e.getTarget(tree.view.itemSelector, 10);
                            if (sourceEl) {
                                var record = tree.view.getRecord(sourceEl);
                                Ext.Msg.show({
                                    title: '系统确认',
                                    message: '确定要变更该学生所属班级？',
                                    buttons: Ext.Msg.OKCANCEL,
                                    icon: Ext.Msg.QUESTION,
                                    fn: function (btn) {
                                        if (btn === 'ok') {
                                            tree.moveParentNode(record, data.draggedRecord);
                                        }
                                    }
                                });
                                return true;
                            } else {
                                return false;
                            }
                        }
                    });
                }
            },
            moveParentNode: function (node, record) {
                Ext.Ajax.request({
                    url: '/Card/TeachStudInfo/MoveStudentCls',
                    params: {
                        orgcode: node.get('code'),
                        infoid: record.get('uid'),
                        fromOrgs: record.get('orgcode')
                    },
                    method: 'POST',
                    success: function (response) {
                        var result = Ext.JSON.decode(response.responseText);
                        if (result.success) {
                            toast('变更成功...');
                            me.store.reload();
                        } else {
                            Ext.Msg.alert('系统信息', result.msg);
                        }
                    }
                });
            }
        });


        me.store = Ext.create('CamPus.view.card.student.StudentStore', {});

        me.infoLabelStore = Ext.create('CamPus.view.card.teacher.InfoLabelComboxStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        infotype: 2
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'combobox',
                    itemId: 'viewleave',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'value',
                    editable: false,
                    value: 1,
                    width: 80,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['value', 'name'],
                        data: [
                            {"value": -1, "name": "全部"},
                            {"value": 1, "name": "有效"},
                            {"value": 0, "name": "无效"}
                        ]
                    }),
                    listeners: {
                        change: function () {
                            me.store.reload();
                        }
                    }
                },
                {
                    xtype: 'combobox',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'name',
                    itemId: 'infolabel',
                    name: 'infolabel',
                    editable: false,
                    width: 110,
                    value: '全部',
                    store: me.infoLabelStore,
                    listeners: {
                        change: function (combox, newValue, oldValue, eOpts) {
                            me.store.load();
                        }
                    }
                },
                {
                    xtype: 'comboxdictionary',
                    itemId: 'property',
                    groupCode: 'SYS0000055',
                    insertAll: '-1|全部',
                    where: 'code IN (2,3)',
                    width: 110,
                    defaultSelectFirst: true,
                    listeners: {
                        change: function (combox, newValue, oldValue, eOpts) {
                            me.store.load();
                        }
                    }
                },
                {
                    xtype: 'searchfield',
                    width: 200,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.store,
                    emptyText: '姓名、卡号、学号、手机号关键词...'
                }, {
                    xtype: 'checkboxfield',
                    boxLabel: '含子节点',
                    inputValue: 1,
                    itemId: 'viewchild',
                    checked: true,
                    listeners: {
                        change: function () {
                            me.store.reload();
                        }
                    }
                }, '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.store.reload();
                    }
                }
            ]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            // title: '学生信息',
            itemId: 'StudentGrid',
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                listeners: {
                    render: function (v) {
                        v.dragZone = Ext.create('Ext.dd.DragZone', v.getEl(), {
                            getDragData: function (e) {
                                var sourceEl = e.getTarget(v.itemSelector, 10);
                                var record = null;
                                if (sourceEl) {
                                    record = sourceEl.cloneNode(true);
                                    record.id = Ext.id();
                                    return (v.dragData = {
                                        ddel: record,
                                        sourceEl: sourceEl,
                                        repairXY: Ext.fly(sourceEl).getXY(),
                                        sourceStore: v.store,
                                        draggedRecord: v.getRecord(sourceEl)
                                    });
                                }
                            },
                            getRepairXY: function () {
                                return this.dragData.repairXY;
                            }
                        });
                    }
                }
            },
            columns: [
                {xtype: 'rownumberer'},
                {
                    text: '学号',
                    dataIndex: 'code',
                    width: 100,
                    sortable: true
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: true,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '手机号',
                    dataIndex: 'mobile',
                    width: 120,
                    sortable: false
                },
                {
                    text: '身份证',
                    dataIndex: 'idcard',
                    width: 130,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'card',
                    width: 130,
                    sortable: false
                },
                {
                    text: '卡序列号',
                    dataIndex: 'cardsn',
                    width: 130,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '入学年份',
                    dataIndex: 'intoyear',
                    width: 85,
                    sortable: false,
                    hidden: true
                },
                {
                    text: '班级或专业',
                    dataIndex: 'orgname',
                    minWidth: 240,
                    flex: 1,
                    sortable: true
                },
                {
                    text: '人员标签',
                    dataIndex: 'infolabel',
                    width: 120,
                    sortable: false
                },
                {
                    text: '人员性质',
                    dataIndex: 'propertyname',
                    width: 100,
                    flex: 1,
                    minWidth: 100,
                    sortable: false
                },
                {
                    text: '紧急联系人',
                    dataIndex: 'linkman1',
                    width: 100,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '紧急联系手机',
                    dataIndex: 'linkmobile1',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '与本人关系',
                    dataIndex: 'linkdes1',
                    width: 100,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '备用联系人',
                    dataIndex: 'linkman2',
                    width: 100,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '备用联系手机',
                    dataIndex: 'linkmobile2',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '与本人关系',
                    dataIndex: 'linkdes2',
                    width: 100,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 70,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record.get('uid') != "root") {
                            if (value) {
                                return '正常';
                            } else {
                                metaData.style += 'color:red;';
                                return '无效';
                            }
                        }
                    }
                }
            ],
            selModel: 'checkboxmodel',
            flex: 1,
            minWidth: 390,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.tree, me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.card.student.StudentEditWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '学生信息',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    maximizable: true,
    resizable: true,
    constructor: function (config) {
        var me = this;
        me.facegroupStore = Ext.create('CamPus.view.card.teach.PermissionGroup', {});
        me.getGroupStore = Ext.create('CamPus.view.card.teacher.getGroupStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        infoId: config.extenduid
                    });
                },
                load: function (store, records, successful, operation, eOpts) {
                    if (successful) {
                        // 假设你已经通过 Ajax 请求获取了需要回显的数据
                        var groupnameField = me.down('tagfield[itemId=groupname]');
                        // 提取获取的记录中的 `uid` 值，生成一个数组
                        var selectedUids = records.map(function (record) {
                            return record.get('uid'); // 假设 `uid` 是需要上传的字段
                        });

                        // 使用动态提取的 `uid` 集合回显 `tagfield` 的值
                        groupnameField.setValue(selectedUids);
                    }
                }
            }
        });

        me.getFaceStore = Ext.create('CamPus.view.card.teacher.getFaceStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        infoid: config.extenduid
                    });
                },
                load: function (store, records, successful, operation, eOpts) {
                    if (successful) {
                        // 假设你已经通过 Ajax 请求获取了需要回显的数据
                        var imgComponent = me.down('image[itemId=photo]');
                        // 提取获取的记录中的 `uid` 值，生成一个数组
                        var selectedUids = records[0].get('imgpath'); // 假设 `uid` 是需要上传的字段
                        imgComponent.setSrc(window.getResourceUrl(CamPus.AppSession.resourcedomain, selectedUids) + '?t=' + Math.random());
                    }
                }
            }
        });

        Ext.apply(me, config);
        var intoyear = config.record ? config.record.get('intoyear') : '';
        Ext.apply(me, {
            items: [
                {
                    xtype: 'form',
                    itemId: 'form',
                    border: false,
                    bodyPadding: 10,
                    defaults: {
                        anchor: '100%',
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    layout: 'hbox', // 使用 hbox 布局
                    defaultType: 'textfield',
                    scrollable: 'y',
                    items: [
                        {
                            xtype: 'container', // 左侧容器，用于放置表单项
                            flex: 1, // 占据剩余的宽度
                            layout: 'anchor',
                            defaults: {
                                anchor: '100%',
                                labelWidth: 100,
                                labelAlign: 'right'
                            },
                            items: [{
                                xtype: 'hiddenfield',
                                name: 'uid'
                            },
                                {
                                    xtype: 'hiddenfield',
                                    name: 'orgcode',
                                    value: config.orgcode
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        xtype: 'comboxdictionary',
                                        fieldLabel: '入学年份',
                                        name: 'intoyear',
                                        groupCode: 'SYS0000004',
                                        valueField: 'name',
                                        defaultSelectFirst: true,
                                        value: intoyear ? intoyear : new Date().getFullYear()
                                    }, {
                                        fieldLabel: '所属班级',
                                        allowBlank: true,
                                        value: config.record ? config.record.get('orgname') : '',
                                        readOnly: true,
                                        submitValue: false,
                                        hidden: config.record ? false : true
                                    }]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            fieldLabel: '入学日期',
                                            name: 'startdate',
                                            format: 'Y-m-d',
                                            allowBlank: true
                                        },
                                        {
                                            xtype: 'datefield',
                                            fieldLabel: '毕业日期',
                                            name: 'enddate',
                                            format: 'Y-m-d',
                                            allowBlank: true
                                        }]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        fieldLabel: '学号',
                                        itemId: 'code',
                                        name: 'code',
                                        allowBlank: false
                                    },
                                        {
                                            fieldLabel: '姓名',
                                            name: 'name',
                                            allowBlank: false
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        fieldLabel: '手机号',
                                        name: 'mobile',
                                        maxLength: 11
                                    }, {
                                        xtype: 'radiogroup',
                                        fieldLabel: '性别',
                                        items: [
                                            {itemId: 'sex1', name: 'sex', boxLabel: '男', inputValue: 1, checked: true},
                                            {itemId: 'sex2', name: 'sex', boxLabel: '女', inputValue: 2}
                                        ]
                                    }]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        xtype: 'textfield',
                                        fieldLabel: '民族',
                                        itemId: 'nation',
                                        name: 'nation',
                                        allowBlank: true
                                    },
                                        {
                                            xtype: 'datefield',
                                            fieldLabel: '生日',
                                            name: 'birthday',
                                            format: 'Y-m-d',
                                            allowBlank: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [{
                                        xtype: 'textfield',
                                        fieldLabel: '身份证号码',
                                        name: 'idcard',
                                        allowBlank: true
                                    },
                                        {
                                            xtype: 'textfield',
                                            fieldLabel: '地址',
                                            name: 'address',
                                            allowBlank: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [
                                        {
                                            fieldLabel: '卡号',
                                            name: 'card',
                                            itemId: 'card',
                                            maxLength: 12,
                                            flex: 1,
                                            allowBlank: true,
                                            maskRe: /[0-9]/,
                                            validator: function (v) {
                                                return /^[0-9]*([0-9]{1,2})?$/.test(v) ? true : '只能输入数字';
                                            },
                                            listeners: {
                                                change: function (e, text, prev) {
                                                    if (!/^[0-9]*([0-9]{0,2})?$/.test(text)) {
                                                        me.down('textfield[itemId=card]').setValue(prev);
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'comboxdictionary',
                                            fieldLabel: '人员性质',
                                            itemId: 'property',
                                            name: 'property',
                                            groupCode: 'SYS0000055',
                                            where: 'attr1=2',
                                            defaultSelectFirst: true,
                                            allowBlank: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldcontainer',
                                    hideLabel: true,
                                    layout: 'hbox',
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    items: [
                                        {
                                            xtype: 'tagfield',
                                            fieldLabel: '标签',
                                            itemId: 'infolabel',
                                            name: 'infolabel',
                                            flex: 2,
                                            displayField: 'label',
                                            valueField: 'label',
                                            createNewOnEnter: true,
                                            createNewOnBlur: true,
                                            autoSelect: true,
                                            forceSelection: false,
                                            filterPickList: true,
                                            queryMode: 'local',
                                            publishes: 'value',
                                            emptyText: '选择输入标签',
                                            allowBlank: true,
                                            store: Ext.create('CamPus.view.card.student.InfoLabelStore')
                                        }
                                        // {
                                        //     xtype: 'combobox',
                                        //     fieldLabel: '人脸权限组',
                                        //     queryMode: 'local',
                                        //     displayField: 'name',
                                        //     valueField: 'name',
                                        //     itemId: 'groupname',
                                        //     name: 'groupname',
                                        //     editable: false,
                                        //     width: 110,
                                        //     value: value,
                                        //     store: me.facegroupStore,
                                        //     listeners: {
                                        //         select: function(combo, record,index) {
                                        //             // 获取选中的值
                                        //             value = record.get('name');
                                        //         }
                                        //     }
                                        // },
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: '人脸权限组',
                                    collapsible: true,
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    layout: 'hbox',
                                    items: [
                                        {
                                            xtype: 'tagfield',
                                            fieldLabel: '人脸权限组',
                                            itemId: 'groupname',
                                            name: 'groupname',
                                            flex: 2,
                                            displayField: 'name',
                                            valueField: 'uid',
                                            createNewOnEnter: false,
                                            createNewOnBlur: false,
                                            autoSelect: true,
                                            forceSelection: false,
                                            filterPickList: true,
                                            queryMode: 'remote',
                                            publishes: 'value',
                                            emptyText: '选择人脸权限组',
                                            allowBlank: true,
                                            store: me.facegroupStore
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: '紧急联系人',
                                    collapsible: true,
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    layout: 'hbox',
                                    items: [{
                                        fieldLabel: '姓名',
                                        name: 'linkman1',
                                        allowBlank: true
                                    },
                                        {
                                            fieldLabel: '联系手机',
                                            name: 'linkmobile1',
                                            allowBlank: true
                                        }, {
                                            xtype: 'comboxdictionary',
                                            fieldLabel: '与本人关系',
                                            name: 'linkdes1',
                                            groupCode: 'SYS0000021',
                                            valueField: 'name',
                                            defaultSelectFirst: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: '备用联系人',
                                    collapsible: true,
                                    defaultType: 'textfield',
                                    defaults: {
                                        flex: 1,
                                        labelWidth: 100,
                                        labelAlign: 'right'
                                    },
                                    layout: 'hbox',
                                    items: [{
                                        fieldLabel: '姓名',
                                        name: 'linkman2',
                                        allowBlank: true
                                    },
                                        {
                                            fieldLabel: '联系手机',
                                            name: 'linkmobile2',
                                            allowBlank: true
                                        }, {
                                            xtype: 'comboxdictionary',
                                            fieldLabel: '与本人关系',
                                            name: 'linkdes2',
                                            groupCode: 'SYS0000021',
                                            valueField: 'name',
                                            defaultSelectFirst: true
                                        }
                                    ]
                                },
                                {
                                    xtype: 'autoforms',
                                    title: '扩展信息',
                                    table: 'tb_card_teachstudinfo',
                                    itemId: 'extendform',
                                    extenduid: config.extenduid ? config.extenduid : '',
                                    listeners: {
                                        afterrender: function (reslut, eOpts) {
                                            if (!reslut.isShowThisForm) {
                                                reslut.setHidden(true);
                                                reslut.setDisabled(true);
                                            } else {
                                                reslut.setHidden(false);
                                                reslut.setDisabled(false);
                                            }
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'container', // 右侧容器，用于放置照片
                            width: 160, // 固定宽度
                            layout: {
                                type: 'vbox',
                                align: 'right'
                            },
                            items: [{
                                xtype: 'image',
                                itemId: 'photo',
                                width: 150,
                                height: 150,
                                style: {
                                    border: '1px solid #ccc',
                                    'margin-left': 'auto',
                                    'margin-bottom': 'auto' // 使图片靠近右上角
                                },
                                src: config.photo ? window.getResourceUrl(CamPus.AppSession.resourcedomain, config.photo) : 'resources/images/face.png'
                            }]
                        }
                    ]
                }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/SaveStudent',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        if (me.down('autoforms[itemId=extendform]').isShowThisForm) {
                            me.down('autoforms[itemId=extendform]').Save(action.result.data, function (result) {
                                me.grid.getStore().reload({
                                    callback: function () {
                                        var formValues = form.getValues();
                                        var combo = me.down('#groupname');
                                        combo.setValue(formValues.groupname);
                                    }
                                });
                                me.close();
                                toast('保存成功！');
                                me.infouidstore.reload();
                                // if (me.aotuAddNew && me.ctrl) {
                                //     me.ctrl.onAddClick();
                                // }
                            });
                        } else {
                            me.grid.getStore().reload();
                            me.close();
                            toast('保存成功！');
                            me.infouidstore.reload();

                            // if (me.aotuAddNew && me.ctrl) {
                            //     me.ctrl.onAddClick();
                            // }
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.card.student.StudentAddWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '学生信息',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    maximizable: true,
    resizable: true,
    constructor: function (config) {
        var me = this;
        me.facegroupStore = Ext.create('CamPus.view.card.teach.PermissionGroup', {});
        Ext.apply(me, config);
        var intoyear = config.record ? config.record.get('intoyear') : '';
        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'hbox', // 使用 hbox 布局
                defaultType: 'textfield',
                scrollable: 'y',
                items: [
                    {
                        xtype: 'container', // 左侧容器，用于放置表单项
                        flex: 1, // 占据剩余的宽度
                        layout: 'anchor',
                        defaults: {
                            anchor: '100%',
                            labelWidth: 100,
                            labelAlign: 'right'
                        },
                        items: [
                            // 放置所有表单项（与你原来的表单内容保持一致）
                            {
                                xtype: 'hiddenfield',
                                name: 'uid'
                            },
                            {
                                xtype: 'hiddenfield',
                                name: 'orgcode',
                                value: config.orgcode
                            },
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [{
                                    xtype: 'comboxdictionary',
                                    fieldLabel: '入学年份',
                                    name: 'intoyear',
                                    groupCode: 'SYS0000004',
                                    valueField: 'name',
                                    defaultSelectFirst: true,
                                    value: intoyear ? intoyear : new Date().getFullYear()
                                }, {
                                    fieldLabel: '所属班级',
                                    allowBlank: true,
                                    value: config.record ? config.record.get('orgname') : '',
                                    readOnly: true,
                                    submitValue: false,
                                    hidden: config.record ? false : true
                                }]
                            },
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [
                                    {
                                        xtype: 'datefield',
                                        fieldLabel: '入学日期',
                                        name: 'startdate',
                                        format: 'Y-m-d',
                                        allowBlank: true
                                    },
                                    {
                                        xtype: 'datefield',
                                        fieldLabel: '毕业日期',
                                        name: 'enddate',
                                        format: 'Y-m-d',
                                        allowBlank: true
                                    }]
                            },
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [{
                                    fieldLabel: '学号',
                                    itemId: 'code',
                                    name: 'code',
                                    allowBlank: false
                                },
                                    {
                                        fieldLabel: '姓名',
                                        name: 'name',
                                        allowBlank: false
                                    }
                                ]
                            },
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [{
                                    fieldLabel: '手机号',
                                    name: 'mobile',
                                    maxLength: 11
                                }, {
                                    xtype: 'radiogroup',
                                    fieldLabel: '性别',
                                    items: [
                                        {itemId: 'sex1', name: 'sex', boxLabel: '男', inputValue: 1, checked: true},
                                        {itemId: 'sex2', name: 'sex', boxLabel: '女', inputValue: 2}
                                    ]
                                }]
                            },
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [{
                                    xtype: 'textfield',
                                    fieldLabel: '民族',
                                    itemId: 'nation',
                                    name: 'nation',
                                    allowBlank: true
                                },
                                    {
                                        xtype: 'datefield',
                                        fieldLabel: '生日',
                                        name: 'birthday',
                                        format: 'Y-m-d',
                                        allowBlank: true
                                    }
                                ]
                            },
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [{
                                    xtype: 'textfield',
                                    fieldLabel: '身份证号码',
                                    name: 'idcard',
                                    allowBlank: true
                                },
                                    {
                                        xtype: 'textfield',
                                        fieldLabel: '地址',
                                        name: 'address',
                                        allowBlank: true
                                    }
                                ]
                            },
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [
                                    {
                                        fieldLabel: '卡号',
                                        name: 'card',
                                        itemId: 'card',
                                        maxLength: 12,
                                        flex: 1,
                                        allowBlank: true,
                                        maskRe: /[0-9]/,
                                        validator: function (v) {
                                            return /^[0-9]*([0-9]{1,2})?$/.test(v) ? true : '只能输入数字';
                                        },
                                        listeners: {
                                            change: function (e, text, prev) {
                                                if (!/^[0-9]*([0-9]{0,2})?$/.test(text)) {
                                                    me.down('textfield[itemId=card]').setValue(prev);
                                                }
                                            }
                                        }
                                    },
                                    {
                                        xtype: 'comboxdictionary',
                                        fieldLabel: '人员性质',
                                        itemId: 'property',
                                        name: 'property',
                                        groupCode: 'SYS0000055',
                                        where: 'attr1=2',
                                        defaultSelectFirst: true,
                                        allowBlank: true
                                    }
                                ]
                            },
                            {
                                xtype: 'fieldcontainer',
                                hideLabel: true,
                                layout: 'hbox',
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                items: [
                                    {
                                        xtype: 'tagfield',
                                        fieldLabel: '标签',
                                        itemId: 'infolabel',
                                        name: 'infolabel',
                                        flex: 2,
                                        displayField: 'label',
                                        valueField: 'label',
                                        createNewOnEnter: true,
                                        createNewOnBlur: true,
                                        autoSelect: true,
                                        forceSelection: false,
                                        filterPickList: true,
                                        queryMode: 'local',
                                        publishes: 'value',
                                        emptyText: '选择输入标签',
                                        allowBlank: true,
                                        store: Ext.create('CamPus.view.card.student.InfoLabelStore')
                                    }
                                    // {
                                    //     xtype: 'combobox',
                                    //     fieldLabel: '人脸权限组',
                                    //     queryMode: 'local',
                                    //     displayField: 'name',
                                    //     valueField: 'name',
                                    //     itemId: 'groupname',
                                    //     name: 'groupname',
                                    //     editable: false,
                                    //     width: 110,
                                    //     value: value,
                                    //     store: me.facegroupStore,
                                    //     listeners: {
                                    //         select: function(combo, record,index) {
                                    //             // 获取选中的值
                                    //             value = record.get('name');
                                    //         }
                                    //     }
                                    // },
                                ]
                            },
                            {
                                xtype: 'fieldset',
                                title: '人脸权限组',
                                collapsible: true,
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                layout: 'hbox',
                                items: [
                                    {
                                        xtype: 'tagfield',
                                        fieldLabel: '人脸权限组',
                                        itemId: 'groupname',
                                        name: 'groupname',
                                        flex: 2,
                                        displayField: 'name',
                                        valueField: 'uid',
                                        createNewOnEnter: false,
                                        createNewOnBlur: false,
                                        autoSelect: true,
                                        forceSelection: false,
                                        filterPickList: true,
                                        queryMode: 'remote',
                                        publishes: 'value',
                                        emptyText: '选择人脸权限组',
                                        allowBlank: true,
                                        store: me.facegroupStore
                                    }
                                ]
                            },
                            {
                                xtype: 'fieldset',
                                title: '紧急联系人',
                                collapsible: true,
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                layout: 'hbox',
                                items: [{
                                    fieldLabel: '姓名',
                                    name: 'linkman1',
                                    allowBlank: true
                                },
                                    {
                                        fieldLabel: '联系手机',
                                        name: 'linkmobile1',
                                        allowBlank: true
                                    }, {
                                        xtype: 'comboxdictionary',
                                        fieldLabel: '与本人关系',
                                        name: 'linkdes1',
                                        groupCode: 'SYS0000021',
                                        valueField: 'name',
                                        defaultSelectFirst: true
                                    }
                                ]
                            },
                            {
                                xtype: 'fieldset',
                                title: '备用联系人',
                                collapsible: true,
                                defaultType: 'textfield',
                                defaults: {
                                    flex: 1,
                                    labelWidth: 100,
                                    labelAlign: 'right'
                                },
                                layout: 'hbox',
                                items: [{
                                    fieldLabel: '姓名',
                                    name: 'linkman2',
                                    allowBlank: true
                                },
                                    {
                                        fieldLabel: '联系手机',
                                        name: 'linkmobile2',
                                        allowBlank: true
                                    }, {
                                        xtype: 'comboxdictionary',
                                        fieldLabel: '与本人关系',
                                        name: 'linkdes2',
                                        groupCode: 'SYS0000021',
                                        valueField: 'name',
                                        defaultSelectFirst: true
                                    }
                                ]
                            },
                            {
                                xtype: 'autoforms',
                                title: '扩展信息',
                                table: 'tb_card_teachstudinfo',
                                itemId: 'extendform',
                                extenduid: config.extenduid ? config.extenduid : '',
                                listeners: {
                                    afterrender: function (reslut, eOpts) {
                                        if (!reslut.isShowThisForm) {
                                            reslut.setHidden(true);
                                            reslut.setDisabled(true);
                                        } else {
                                            reslut.setHidden(false);
                                            reslut.setDisabled(false);
                                        }
                                    }
                                }
                            }
                        ]
                    },
                    {
                        xtype: 'container', // 右侧容器，用于放置照片
                        width: 160, // 固定宽度
                        layout: {
                            type: 'vbox',
                            align: 'right'
                        },
                        items: [{
                            xtype: 'image',
                            itemId: 'photo',
                            width: 150,
                            height: 150,
                            style: {
                                border: '1px solid #ccc',
                                'margin-left': 'auto',
                                'margin-bottom': 'auto' // 使图片靠近右上角
                            },
                            src: config.photo ? window.getResourceUrl(CamPus.AppSession.resourcedomain, config.photo) : 'resources/images/face.png'
                        }]
                    }
                ]
            }]
        });
        me.callParent(arguments);
    },
    // constructor: function(config) {
    //     var me = this;
    //     me.facegroupStore = Ext.create('CamPus.view.card.teach.PermissionGroup', {});
    //     Ext.apply(me, config);
    //     var intoyear = config.record ? config.record.get('intoyear') : '';
    //     Ext.apply(me, {
    //         items: [{
    //             xtype: 'form',
    //             itemId: 'form',
    //             border: false,
    //             bodyPadding: 10,
    //             defaults: {
    //                 anchor: '100%',
    //                 labelWidth: 100,
    //                 labelAlign: 'right'
    //             },
    //             layout: 'hbox', // 使用 hbox 布局，将表单内容和照片放在同一行
    //             defaultType: 'textfield',
    //             scrollable: 'y',
    //             items: [
    //                 {
    //                     xtype: 'container', // 左侧容器，用于放置表单项
    //                     flex: 1, // 占据剩余的宽度
    //                     layout: 'anchor',
    //                     defaults: {
    //                         anchor: '100%',
    //                         labelWidth: 100,
    //                         labelAlign: 'right'
    //                     },
    //                     items: [
    //                         // 放置所有表单项
    //                         {
    //                             xtype: 'hiddenfield',
    //                             name: 'uid'
    //                         },
    //                         {
    //                             xtype: 'hiddenfield',
    //                             name: 'orgcode',
    //                             value: config.orgcode
    //                         },
    //                         {
    //                             xtype: 'fieldcontainer',
    //                             hideLabel: true,
    //                             layout: 'hbox',
    //                             defaultType: 'textfield',
    //                             defaults: {
    //                                 flex: 1,
    //                                 labelWidth: 100,
    //                                 labelAlign: 'right'
    //                             },
    //                     items: [{
    //                         xtype: 'comboxdictionary',
    //                         fieldLabel: '入学年份',
    //                         name: 'intoyear',
    //                         groupCode: 'SYS0000004',
    //                         valueField: 'name',
    //                         defaultSelectFirst: true,
    //                         value: intoyear?intoyear:new Date().getFullYear()
    //                     }, {
    //                         fieldLabel: '所属班级',
    //                         allowBlank: true,
    //                         value: config.record?config.record.get('orgname'):'',
    //                         readOnly: true,
    //                         submitValue: false,
    //                         hidden: config.record ? false : true
    //                     }]
    //                 },
    //                 {
    //                     xtype: 'fieldcontainer',
    //                     hideLabel: true,
    //                     layout: 'hbox',
    //                     defaultType: 'textfield',
    //                     defaults: {
    //                         flex: 1,
    //                         labelWidth: 100,
    //                         labelAlign: 'right'
    //                     },
    //                     items: [
    //                         {
    //                             xtype: 'datefield',
    //                             fieldLabel: '入学日期',
    //                             name: 'startdate',
    //                             format: 'Y-m-d',
    //                             allowBlank: true
    //                         },
    //                         {
    //                             xtype: 'datefield',
    //                             fieldLabel: '毕业日期',
    //                             name: 'enddate',
    //                             format: 'Y-m-d',
    //                             allowBlank: true
    //                         }]
    //                 },
    //                 {
    //                     xtype: 'fieldcontainer',
    //                     hideLabel: true,
    //                     layout: 'hbox',
    //                     defaultType: 'textfield',
    //                     defaults: {
    //                         flex: 1,
    //                         labelWidth: 100,
    //                         labelAlign: 'right'
    //                     },
    //                     items: [{
    //                         fieldLabel: '学号',
    //                         itemId: 'code',
    //                         name: 'code',
    //                         allowBlank: false
    //                     },
    //                         {
    //                             fieldLabel: '姓名',
    //                             name: 'name',
    //                             allowBlank: false
    //                         }
    //                     ]
    //                 },
    //                 {
    //                     xtype: 'fieldcontainer',
    //                     hideLabel: true,
    //                     layout: 'hbox',
    //                     defaultType: 'textfield',
    //                     defaults: {
    //                         flex: 1,
    //                         labelWidth: 100,
    //                         labelAlign: 'right'
    //                     },
    //                     items: [{
    //                         fieldLabel: '手机号',
    //                         name: 'mobile',
    //                         maxLength: 11
    //                     }, {
    //                         xtype: 'radiogroup',
    //                         fieldLabel: '性别',
    //                         items: [
    //                             {itemId:'sex1', name: 'sex', boxLabel: '男', inputValue: 1, checked: true },
    //                             {itemId:'sex2', name: 'sex', boxLabel: '女', inputValue: 2 }
    //                         ]
    //                     }]
    //                 },
    //                 {
    //                     xtype: 'fieldcontainer',
    //                     hideLabel: true,
    //                     layout: 'hbox',
    //                     defaultType: 'textfield',
    //                     defaults: {
    //                         flex: 1,
    //                         labelWidth: 100,
    //                         labelAlign: 'right'
    //                     },
    //                     items: [{
    //                         xtype: 'textfield',
    //                         fieldLabel: '民族',
    //                         itemId: 'nation',
    //                         name: 'nation',
    //                         allowBlank: true
    //                     },
    //                         {
    //                             xtype: 'datefield',
    //                             fieldLabel: '生日',
    //                             name: 'birthday',
    //                             format: 'Y-m-d',
    //                             allowBlank: true
    //                         }
    //                     ]
    //                 },
    //                 {
    //                     xtype: 'fieldcontainer',
    //                     hideLabel: true,
    //                     layout: 'hbox',
    //                     defaultType: 'textfield',
    //                     defaults: {
    //                         flex: 1,
    //                         labelWidth: 100,
    //                         labelAlign: 'right'
    //                     },
    //                     items: [{
    //                         xtype: 'textfield',
    //                         fieldLabel: '身份证号码',
    //                         name: 'idcard',
    //                         allowBlank: true
    //                     },
    //                         {
    //                             xtype: 'textfield',
    //                             fieldLabel: '地址',
    //                             name: 'address',
    //                             allowBlank: true
    //                         }
    //                     ]
    //                 },
    //                 {
    //                     xtype: 'fieldcontainer',
    //                     hideLabel: true,
    //                     layout: 'hbox',
    //                     defaultType: 'textfield',
    //                     defaults: {
    //                         flex: 1,
    //                         labelWidth: 100,
    //                         labelAlign: 'right'
    //                     },
    //                     items: [
    //                         {
    //                             fieldLabel: '卡号',
    //                             name: 'card',
    //                             itemId: 'card',
    //                             maxLength: 12,
    //                             flex: 1,
    //                             allowBlank: true,
    //                             maskRe: /[0-9]/,
    //                             validator: function (v) {
    //                                 return /^[0-9]*([0-9]{1,2})?$/.test(v) ? true : '只能输入数字';
    //                             },
    //                             listeners: {
    //                                 change: function (e, text, prev) {
    //                                     if (!/^[0-9]*([0-9]{0,2})?$/.test(text)) {
    //                                         me.down('textfield[itemId=card]').setValue(prev);
    //                                     }
    //                                 }
    //                             }
    //                         },
    //                         {
    //                             xtype: 'comboxdictionary',
    //                             fieldLabel: '人员性质',
    //                             itemId: 'property',
    //                             name: 'property',
    //                             groupCode: 'SYS0000055',
    //                             where: 'attr1=2',
    //                             defaultSelectFirst: true,
    //                             allowBlank: true
    //                         }
    //                     ]
    //                 },
    //                 {
    //                     xtype: 'fieldcontainer',
    //                     hideLabel: true,
    //                     layout: 'hbox',
    //                     defaultType: 'textfield',
    //                     defaults: {
    //                         flex: 1,
    //                         labelWidth: 100,
    //                         labelAlign: 'right'
    //                     },
    //                     items: [
    //                         {
    //                             xtype: 'tagfield',
    //                             fieldLabel: '标签',
    //                             itemId: 'infolabel',
    //                             name: 'infolabel',
    //                             flex:2,
    //                             displayField: 'label',
    //                             valueField: 'label',
    //                             createNewOnEnter: true,
    //                             createNewOnBlur: true,
    //                             autoSelect: true,
    //                             forceSelection: false,
    //                             filterPickList: true,
    //                             queryMode: 'local',
    //                             publishes: 'value',
    //                             emptyText: '选择输入标签',
    //                             allowBlank: true,
    //                             store: Ext.create('CamPus.view.card.teacher.InfoLabelStore')
    //                         }
    //                         // {
    //                         //     xtype: 'combobox',
    //                         //     fieldLabel: '人脸权限组',
    //                         //     queryMode: 'local',
    //                         //     displayField: 'name',
    //                         //     valueField: 'name',
    //                         //     itemId: 'groupname',
    //                         //     name: 'groupname',
    //                         //     editable: false,
    //                         //     width: 110,
    //                         //     value: value,
    //                         //     store: me.facegroupStore,
    //                         //     listeners: {
    //                         //         select: function(combo, record,index) {
    //                         //             // 获取选中的值
    //                         //             value = record.get('name');
    //                         //         }
    //                         //     }
    //                         // },
    //                     ]
    //                 },
    //                 // {
    //                 //     xtype: 'fieldset',
    //                 //     title: '人脸权限组',
    //                 //     collapsible: true,
    //                 //     defaultType: 'textfield',
    //                 //     defaults: {
    //                 //         flex: 1,
    //                 //         labelWidth: 100,
    //                 //         labelAlign: 'right'
    //                 //     },
    //                 //     layout: 'hbox',
    //                 //     items: [
    //                 //         {
    //                 //             xtype: 'tagfield',
    //                 //             fieldLabel: '人脸权限组',
    //                 //             itemId: 'groupname',
    //                 //             name: 'groupname',
    //                 //             flex: 2,
    //                 //             displayField: 'name',
    //                 //             valueField: 'uid',
    //                 //             createNewOnEnter: false,
    //                 //             createNewOnBlur: false,
    //                 //             autoSelect: true,
    //                 //             forceSelection: false,
    //                 //             filterPickList: true,
    //                 //             queryMode: 'remote',
    //                 //             publishes: 'value',
    //                 //             emptyText: '选择人脸权限组',
    //                 //             allowBlank: true,
    //                 //             store: me.facegroupStore
    //                 //         }
    //                 //     ]
    //                 // },
    //                 {
    //                     xtype: 'fieldset',
    //                     title: '紧急联系人',
    //                     collapsible: true,
    //                     defaultType: 'textfield',
    //                     defaults: {
    //                         flex: 1,
    //                         labelWidth: 100,
    //                         labelAlign: 'right'
    //                     },
    //                     layout: 'hbox',
    //                     items: [{
    //                         fieldLabel: '姓名',
    //                         name: 'linkman1',
    //                         allowBlank: true
    //                     },
    //                         {
    //                             fieldLabel: '联系手机',
    //                             name: 'linkmobile1',
    //                             allowBlank: true
    //                         }, {
    //                             xtype: 'comboxdictionary',
    //                             fieldLabel: '与本人关系',
    //                             name: 'linkdes1',
    //                             groupCode: 'SYS0000021',
    //                             valueField: 'name',
    //                             defaultSelectFirst: true
    //                         }
    //                     ]
    //                 },
    //                 {
    //                     xtype: 'fieldset',
    //                     title: '备用联系人',
    //                     collapsible: true,
    //                     defaultType: 'textfield',
    //                     defaults: {
    //                         flex: 1,
    //                         labelWidth: 100,
    //                         labelAlign: 'right'
    //                     },
    //                     layout: 'hbox',
    //                     items: [{
    //                         fieldLabel: '姓名',
    //                         name: 'linkman2',
    //                         allowBlank: true
    //                     },
    //                         {
    //                             fieldLabel: '联系手机',
    //                             name: 'linkmobile2',
    //                             allowBlank: true
    //                         }, {
    //                             xtype: 'comboxdictionary',
    //                             fieldLabel: '与本人关系',
    //                             name: 'linkdes2',
    //                             groupCode: 'SYS0000021',
    //                             valueField: 'name',
    //                             defaultSelectFirst: true
    //                         }
    //                     ]
    //                 },
    //                 {
    //                     xtype:'autoforms',
    //                     title:'扩展信息',
    //                     table:'tb_card_teachstudinfo',
    //                     itemId:'extendform',
    //                     extenduid: config.extenduid?config.extenduid:'',
    //                     listeners:{
    //                         afterrender: function(reslut, eOpts){
    //                             if(!reslut.isShowThisForm){
    //                                 reslut.setHidden(true);
    //                                 reslut.setDisabled(true);
    //                             }else {
    //                                 reslut.setHidden(false);
    //                                 reslut.setDisabled(false);
    //                             }
    //                         }
    //                     }
    //                 },
    //                 {
    //                     xtype: 'panel',
    //                     layout: 'hbox',
    //                     items: [{
    //                         xtype: 'container',
    //                         flex: 1, // 左边部分占满剩余空间
    //                         layout: 'anchor',
    //                         items: [
    //                             // 左边部分内容，可以包含其他表单字段
    //                         ]
    //                     }, {
    //                         xtype: 'container',
    //                         width: 160, // 右边容器固定宽度
    //                         layout: 'vbox',
    //                         items: [{
    //                             xtype: 'image',
    //                             itemId: 'photo',
    //                             width: 150,
    //                             height: 150,
    //                             style: {
    //                                 border: '1px solid #ccc',
    //                                 'margin-left': 'auto' // 使图片居右
    //                             },
    //                             src: config.photo ? window.getResourceUrl(CamPus.AppSession.resourcedomain, config.photo) : 'resources/images/face.png'
    //                         }]
    //                     }]
    //                 }
    //             ]
    //         }]
    //     }]});
    //     me.callParent(arguments);
    // },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/SaveStudent',
                params: {
                    userId: me.newInfoId
                },
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        if (me.down('autoforms[itemId=extendform]').isShowThisForm) {
                            me.down('autoforms[itemId=extendform]').Save(action.result.data, function (result) {
                                me.grid.getStore().reload({
                                    callback: function () {
                                        var formValues = form.getValues();
                                        var combo = me.down('#groupname');
                                        combo.setValue(formValues.groupname);
                                    }
                                });
                                me.close();
                                toast('保存成功！');
                                me.infouidstore.reload();
                                // if (me.aotuAddNew && me.ctrl) {
                                //     me.ctrl.onAddClick();
                                // }
                            });
                        } else {
                            me.grid.getStore().reload();
                            me.close();
                            toast('保存成功！');
                            me.infouidstore.reload();

                            // if (me.aotuAddNew && me.ctrl) {
                            //     me.ctrl.onAddClick();
                            // }
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


Ext.define('CamPus.view.card.student.ImportStudentWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '导入学生档案',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    height: 360,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                }, {
                    xtype: 'button',
                    text: '下载导入模板',
                    handler: function () {
                        Ext.Ajax.request({
                            url: '/Card/TeachStudInfo/ExportTemplate',
                            params: {
                                name: '学生信息导入模板'
                            },
                            method: 'POST',
                            success: function (response) {
                                Ext.get('loadingToast').hide();
                                var result = Ext.JSON.decode(response.responseText);
                                if (result.success) {
                                    if (result.msg) {
                                        openWindow(result.msg, null, 'get');
                                    } else {
                                        toast('无导出数据');
                                    }
                                } else {
                                    toast(result.msg);
                                }
                            }
                        });
                    }
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中应至少包含姓名、专业或班级代码、性别(1男，2女)，其他可选；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/UploadStudentFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('上传成功，请设置数据对应列！');
                        var nextwin = Ext.create('CamPus.view.card.student.SetColumnWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.card.student.ImportMoneyWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '导入学生充值金额',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    height: 360,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                }, {
                    xtype: 'button',
                    text: '下载导入模板',
                    handler: function () {
                        Ext.Ajax.request({
                            url: '/Card/TeachStudInfo/ExportTemplate',
                            params: {
                                name: '学生充值金额导入模板'
                            },
                            method: 'POST',
                            success: function (response) {
                                Ext.get('loadingToast').hide();
                                var result = Ext.JSON.decode(response.responseText);
                                if (result.success) {
                                    if (result.msg) {
                                        openWindow(result.msg, null, 'get');
                                    } else {
                                        toast('无导出数据');
                                    }
                                } else {
                                    toast(result.msg);
                                }
                            }
                        });
                    }
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中应至少包含学号、姓名、性别(1男，2女)、充值金额，其他可选；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }]
            }]
        });
        me.callParent(arguments);
    },

    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/UploadImportMoneyFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('上传成功，请设置数据对应列！');
                        var nextwin = Ext.create('CamPus.view.card.student.SetImportMoneyColumnWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


Ext.define('CamPus.view.card.student.SetColumnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置导入对应列',
    width: 550,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('Ext.data.Store', {
            fields: ['column', 'value', 'columnname', 'columncode'],
            data: config.data
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            border: false,
            flex: 1,
            store: me.store,
            columns: [{
                text: '列序号',
                dataIndex: 'column',
                width: 65
            },
                {
                    text: '列名',
                    dataIndex: 'value',
                    flex: 1
                },
                {
                    text: '对应列名',
                    dataIndex: 'columncode',
                    flex: 1,
                    sortable: false,
                    editor: {
                        xtype: 'combobox',
                        queryMode: 'local',
                        displayField: 'name',
                        valueField: 'value',
                        editable: false,
                        store: Ext.create('Ext.data.Store', {
                            fields: ['name', 'value'],
                            data: [
                                {name: "忽略", value: ''},
                                {name: "学号", value: 'code'},
                                {name: "姓名", value: 'name'},
                                {name: "卡序列号", value: 'cardsn'},
                                {name: "性别(1男2女)", value: 'sex'},
                                {name: "手机号", value: 'mobile'},
                                {name: "班级或专业代码", value: 'orgcode'},
                                {name: "身份证号", value: 'idcard'},
                                {name: "入学年份", value: 'intoyear'},
                                {name: "民族", value: 'nation'},
                                {name: "生日(长度为10的日期)", value: 'birthday'},
                                {name: "地址", value: 'address'},
                                // { name: "紧急联系人", value: 'linkman1' },
                                // { name: "紧急联系人手机号", value: 'linkmobile1' },
                                // { name: "与本人关系", value: 'linkdes1' },
                                // { name: "备用联系人", value: 'linkman2' },
                                // { name: "备用联系电话", value: 'linkmobile2' },
                                // { name: "备用联系与本人关系", value: 'linkdes2' },
                                {name: "标签", value: 'infolabel'},
                                {name: "人员性质（3:住宿生,4:走读生）", value: 'property'}
                            ]
                        }),
                        listeners: {
                            change: function (combox, newValue, oldValue, eOpts) {
                                if (newValue) {
                                    var records = me.grid.getStore().getData().items;
                                    var exists = false;
                                    Ext.each(records, function (record, i) {
                                        if (newValue && record.get('columncode') && record.get('columncode') == newValue) {
                                            exists = true;
                                        }
                                    });
                                    if (!exists) {
                                        combox.ownerCt.context.record.set('columncode', newValue);
                                        combox.ownerCt.context.record.set('columnname', combox.getRawValue());
                                    } else {
                                        toast('禁止多列对应一列');
                                        return false;
                                    }
                                } else {
                                    combox.ownerCt.context.record.set('columncode', '');
                                    combox.ownerCt.context.record.set('columnname', '');
                                }
                            }
                        }
                    },
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record) {
                            return record.get('columnname');
                        }
                    }
                }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'newlistplugin'
            }
        });

        Ext.apply(me, {
            fbar: ['->',
                {
                    xtype: 'button',
                    text: '确定',
                    listeners: {
                        click: function (button, event) {
                            me.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    listeners: {
                        click: function (button, event) {
                            me.close();
                        }
                    }
                }
            ],
            items: [me.grid, {
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                hidden: true,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    itemId: 'excelpath',
                    name: 'excelpath',
                    value: config.excelpath,
                    allowBlank: false
                }, {
                    itemId: 'columncfg',
                    name: 'columncfg',
                    allowBlank: false
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.grid.getStore().getData().items;
        var columncfg = [];
        Ext.each(records, function (record, i) {
            if (record.get('columncode')) {
                columncfg.push({
                    columncode: record.get('columncode'),
                    column: record.get('column')
                });
            }
        });
        if (columncfg.length == 0) {
            toast('请设置文档列对应关系');
            return;
        }
        var form = me.down('form[itemId=form]');
        form.down('textfield[itemId=columncfg]').setValue(JSON.stringify(columncfg));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/ImportStudentFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        me.maingrid.getStore().loadPage(1);
                        if (action.result.msg) {
                            Ext.MessageBox.show({
                                title: '系统信息',
                                msg: '部分数据未成功导入，是否下载清单?',
                                buttons: Ext.MessageBox.YESNOCANCEL,
                                icon: Ext.MessageBox.QUESTION,
                                fn: function (btn, text) {
                                    if (btn == 'yes') {
                                        openWindow(action.result.msg, null, 'get');
                                    }
                                }
                            });
                        } else {
                            toast('数据导入成功！');
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.card.student.SetImportMoneyColumnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置导入对应列',
    width: 550,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('Ext.data.Store', {
            fields: ['column', 'value', 'columnname', 'columncode'],
            data: config.data
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            border: false,
            flex: 1,
            store: me.store,
            columns: [{
                text: '列序号',
                dataIndex: 'column',
                width: 65
            },
                {
                    text: '列名',
                    dataIndex: 'value',
                    flex: 1
                },
                {
                    text: '对应列名',
                    dataIndex: 'columncode',
                    flex: 1,
                    sortable: false,
                    editor: {
                        xtype: 'combobox',
                        queryMode: 'local',
                        displayField: 'name',
                        valueField: 'value',
                        editable: false,
                        store: Ext.create('Ext.data.Store', {
                            fields: ['name', 'value'],
                            data: [
                                {name: "忽略", value: ''},
                                {name: "学号", value: 'code'},
                                {name: "姓名", value: 'name'},
                                {name: "性别(1男2女)", value: 'sex'},
                                {name: "充值金额", value: 'money'},
                            ]
                        }),
                        listeners: {
                            change: function (combox, newValue, oldValue, eOpts) {
                                if (newValue) {
                                    var records = me.grid.getStore().getData().items;
                                    var exists = false;
                                    Ext.each(records, function (record, i) {
                                        if (newValue && record.get('columncode') && record.get('columncode') == newValue) {
                                            exists = true;
                                        }
                                    });
                                    if (!exists) {
                                        combox.ownerCt.context.record.set('columncode', newValue);
                                        combox.ownerCt.context.record.set('columnname', combox.getRawValue());
                                    } else {
                                        toast('禁止多列对应一列');
                                        return false;
                                    }
                                } else {
                                    combox.ownerCt.context.record.set('columncode', '');
                                    combox.ownerCt.context.record.set('columnname', '');
                                }
                            }
                        }
                    },
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record) {
                            return record.get('columnname');
                        }
                    }
                }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'newlistplugin'
            }
        });

        Ext.apply(me, {
            fbar: ['->',
                {
                    xtype: 'button',
                    text: '确定',
                    listeners: {
                        click: function (button, event) {
                            me.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    listeners: {
                        click: function (button, event) {
                            me.close();
                        }
                    }
                }
            ],
            items: [me.grid, {
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                hidden: true,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    itemId: 'excelpath',
                    name: 'excelpath',
                    value: config.excelpath,
                    allowBlank: false
                }, {
                    itemId: 'columncfg',
                    name: 'columncfg',
                    allowBlank: false
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.grid.getStore().getData().items;
        var columncfg = [];
        Ext.each(records, function (record, i) {
            if (record.get('columncode')) {
                columncfg.push({
                    columncode: record.get('columncode'),
                    column: record.get('column')
                });
            }
        });
        if (columncfg.length == 0) {
            toast('请设置文档列对应关系');
            return;
        }
        var form = me.down('form[itemId=form]');
        form.down('textfield[itemId=columncfg]').setValue(JSON.stringify(columncfg));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/ImportMoneyFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        me.maingrid.getStore().loadPage(1);
                        if (action.result.msg) {
                            Ext.MessageBox.show({
                                title: '系统信息',
                                msg: '部分数据未成功导入，是否下载清单?',
                                buttons: Ext.MessageBox.YESNOCANCEL,
                                icon: Ext.MessageBox.QUESTION,
                                fn: function (btn, text) {
                                    if (btn == 'yes') {
                                        openWindow(action.result.msg, null, 'get');
                                    }
                                }
                            });
                        } else {
                            toast('数据导入成功！');
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


Ext.define('CamPus.view.card.student.ChangeStudentClassWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '学生调整班级',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.card.student.CollegeClassTreeStore', {});

        var collegeclasstbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: '院系、专业、班级关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: collegeclasstbar,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            columnLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            setPathName: function (node) {
                this.pathName = (this.pathName ? node.get('name') + '/' + this.pathName : node.get('name'));
                if (node.parentNode && node.parentNode.id != 'root') {
                    this.setPathName(node.parentNode);
                }
            }
        });

        Ext.apply(me, {
            items: [me.tree]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.tree.getSelection().length == 0) {
            toast('请选择新的班级...');
            return;
        }
        var node = me.tree.getSelection()[0];
        if (node.get('orgtype') != '4') {
            toast('请选择班级...');
            return;
        }
        me.tree.pathName = '';
        me.tree.setPathName(node);
        var orgcode = node.get('code');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要将所选学生调整至【' + me.tree.pathName + '】 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Card/TeachStudInfo/MoveStudentCls',
                        params: {
                            orgcode: orgcode,
                            infoid: me.infoid,
                            fromOrgs: me.fromOrgs
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.close();
                                toast('调整成功成功...');
                                me.maingrid.getStore().reload();
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    }
});

Ext.define('CamPus.view.card.student.ImportUpdateWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '更新学生档案',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    height: 360,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中应至少包含工号,其他根据需要添加：',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/uploadUpdateExcel',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('上传成功，请设置数据对应列！');
                        var nextwin = Ext.create('CamPus.view.card.student.SetUpdateExcelColumnWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.card.student.SetUpdateExcelColumnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置导入对应列',
    width: 550,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('Ext.data.Store', {
            fields: ['column', 'value', 'columnname', 'columncode'],
            data: config.data
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            border: false,
            flex: 1,
            store: me.store,
            columns: [{
                text: '列序号',
                dataIndex: 'column',
                width: 65
            },
                {
                    text: '列名',
                    dataIndex: 'value',
                    flex: 1
                },
                {
                    text: '对应列名',
                    dataIndex: 'columncode',
                    flex: 1,
                    sortable: false,
                    editor: {
                        xtype: 'combobox',
                        queryMode: 'local',
                        displayField: 'name',
                        valueField: 'value',
                        editable: false,
                        store: Ext.create('Ext.data.Store', {
                            fields: ['name', 'value'],
                            data: [
                                {name: "忽略", value: ''},
                                {name: "学号", value: 'code'},
                                {name: "姓名", value: 'name'},
                                {name: "组织编号(不允许直接填名字)", value: 'orgcode'},
                                {name: "人员性质(2 走读生 3 住宿生)", value: 'property'},
                                {name: "紧急联系人1手机号", value: 'linkmobile1'},
                                {name: "紧急联系人1姓名", value: 'linkman1'},
                                {name: "紧急联系人1关系", value: 'linkdes1'},
                                {name: "紧急联系人2手机号", value: 'linkmobile2'},
                                {name: "紧急联系人2姓名", value: 'linkman2'},
                                {name: "紧急联系人2关系", value: 'linkdes2'}
                            ]
                        }),
                        listeners: {
                            change: function (combox, newValue, oldValue, eOpts) {
                                if (newValue) {
                                    var records = me.grid.getStore().getData().items;
                                    var exists = false;
                                    Ext.each(records, function (record, i) {
                                        if (newValue && record.get('columncode') && record.get('columncode') == newValue) {
                                            exists = true;
                                        }
                                    });
                                    if (!exists) {
                                        combox.ownerCt.context.record.set('columncode', newValue);
                                        combox.ownerCt.context.record.set('columnname', combox.getRawValue());
                                    } else {
                                        toast('禁止多列对应一列');
                                        return false;
                                    }
                                } else {
                                    combox.ownerCt.context.record.set('columncode', '');
                                    combox.ownerCt.context.record.set('columnname', '');
                                }
                            }
                        }
                    },
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record) {
                            return record.get('columnname');
                        }
                    }
                }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'newlistplugin'
            }
        });

        Ext.apply(me, {
            fbar: ['->',
                {
                    xtype: 'button',
                    text: '确定',
                    listeners: {
                        click: function (button, event) {
                            me.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    listeners: {
                        click: function (button, event) {
                            me.close();
                        }
                    }
                }
            ],
            items: [me.grid, {
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                hidden: true,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    itemId: 'excelpath',
                    name: 'excelpath',
                    value: config.excelpath,
                    allowBlank: false
                }, {
                    itemId: 'columncfg',
                    name: 'columncfg',
                    allowBlank: false
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.grid.getStore().getData().items;
        var columncfg = [];
        Ext.each(records, function (record, i) {
            if (record.get('columncode')) {
                columncfg.push({
                    columncode: record.get('columncode'),
                    column: record.get('column')
                });
            }
        });
        if (columncfg.length == 0) {
            toast('请设置文档列对应关系');
            return;
        }
        var form = me.down('form[itemId=form]');
        form.down('textfield[itemId=columncfg]').setValue(JSON.stringify(columncfg));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Card/TeachStudInfo/updateTeamInfoByExcel',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        me.maingrid.getStore().loadPage(1);
                        if (action.result.msg) {
                            Ext.MessageBox.show({
                                title: '系统信息',
                                msg: '导入数据有误，无法更新。是否查看详细信息?',
                                buttons: Ext.MessageBox.YESNOCANCEL,
                                icon: Ext.MessageBox.QUESTION,
                                fn: function (btn, text) {
                                    if (btn == 'yes') {
                                        openWindow(action.result.msg, null, 'get');
                                    }
                                }
                            });
                        } else {
                            toast('数据导入成功！');
                        }
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


