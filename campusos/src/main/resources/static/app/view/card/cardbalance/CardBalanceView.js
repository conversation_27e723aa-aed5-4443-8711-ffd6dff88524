Ext.define('CamPus.view.card.cardbalance.CardBalanceView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.CardBalanceView",
    controller: 'CardBalanceController',
    requires: [
        'CamPus.view.card.cardbalance.CardBalanceStore',
        'CamPus.view.card.cardbalance.CardBalanceController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: 10,
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.treestore = Ext.create('CamPus.view.card.cardbalance.CollegeClassTreeStore', {});

        var collegeclasstbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 180,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get("orgname2") + '关键词...'
            }, '->', {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '取消选择',
                handler: function () {
                    me.tree.getSelectionModel().deselectAll();
                    me.store.reload();
                }
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            itemId: 'collegeclasstree',
            //title: window.applang.get("orgname2"),
            tbar: collegeclasstbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 300,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            setPathName: function (node) {
                this.pathName = (this.pathName ? node.get('name') + '/' + this.pathName : node.get('name'));
                if (node.parentNode && node.parentNode.id != 'root') {
                    this.setPathName(node.parentNode);
                }
            }
        });

        me.store = Ext.create('CamPus.view.card.cardbalance.CardBalanceStore', {});

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'searchfield',
                    itemId: 'searchKey',
                    width: 200,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.store,
                    emptyText: '姓名、' + window.applang.get("infocode") + '关键词...'
                },
                {
                    xtype: 'checkboxfield',
                    boxLabel: '含子节点人员',
                    inputValue: 1,
                    itemId: 'viewchild',
                    checked: true,
                    listeners: {
                        change: function () {
                            me.store.reload();
                        }
                    }
                }, '->', {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.store.reload();
                    }
                }]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            itemId: 'userBalanceRecord',
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            tbar: tbar,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {xtype: 'rownumberer'},
                {
                    text: 'uid',
                    dataIndex: 'uid',
                    hidden: true
                },
                {
                    text: 'cardId',
                    dataIndex: 'cardId',
                    hidden: true
                },
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 80,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 80,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'card',
                    width: 120,
                    sortable: false
                },
                {
                    text: '类型',
                    dataIndex: 'isMain',
                    width: 80,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == '1') {
                            return "主卡";
                        } else {
                            return "副卡";
                        }
                    }
                },
                {
                    text: '主钱包',
                    dataIndex: 'balance',
                    width: 100,
                    sortable: false
                },
                {
                    text: '副钱包',
                    dataIndex: 'viceWallet',
                    width: 100,
                    sortable: false
                },
                {
                    text: '水控钱包',
                    dataIndex: 'waterWallet',
                    width: 80,
                    sortable: false
                },
                {
                    text: '合计',
                    dataIndex: 'sum',
                    width: 80,
                    sortable: false
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 150,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == '1') {
                            return "正常";
                        } else {
                            return "异常";
                        }
                    }
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    width: 120,
                    sortable: false
                },
                {
                    text: '发卡时间',
                    dataIndex: 'createDt',
                    width: 150,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d  H:i:s')
                        }
                    }
                },
                {
                    text: '有效期',
                    dataIndex: 'endDt',
                    width: 150,
                    minWidth: 150,
                    flex: 1,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d  H:i:s')
                        }
                    }
                }
            ],
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });
        Ext.apply(config, {
            items: [me.tree, me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});
Ext.define('CamPus.view.card.cardbalance.TimeViewWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '退款',
    width: 400,
    height: 300,
    defaults: {
        collapsible: false,
        split: true
    },
    step: 1,
    maximizable: false,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                scrollable: 'y',
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right'
                },
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'infoid',
                    allowBlank: false,
                    value: config.uid
                }, {
                    xtype: 'hiddenfield',
                    name: 'cardid',
                    allowBlank: false,
                    value: config.cardId
                },
                    {
                        xtype: 'hiddenfield',
                        name: 'card',
                        allowBlank: false,
                        value: config.card
                    },
                    {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaults: {
                        flex: 1,
                        labelWidth: 80,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'numberfield',
                        fieldLabel: '退款金额',
                        name: 'money',
                        value: config.money,
                        maxValue: config.money,
                        // minValue: 1,
                        allowBlank: false
                    }
                    ]
                }, {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    defaults: {
                        flex: 1,
                        labelWidth: 80,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'textfield',
                        fieldLabel: '备注',
                        name: 'remark',
                        allowBlank: false
                    }
                    ]
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Consume/AnalysisDeviceRate/refundChargePay',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    Ext.get('loadingToast').hide();
                    var result = Ext.JSON.decode(response.responseText);
                    if (result.success) {
                        if (result.msg) {
                            me.grid.getStore().reload();

                        } else {
                            toast('退款失败');
                        }
                    } else {
                        toast(result.msg);
                    }
                    me.close();
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.consume.consumeuser.EditConsumePlaceWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '营业场所信息',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 640,
    height: 400,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'placeform',
                border: false,
                scrollable: true,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'uid'
                }, {
                    xtype: 'hiddenfield',
                    name: 'merid',
                    itemId: 'merid'
                }, {
                    fieldLabel: '编号',
                    itemId: 'code',
                    name: 'code',
                    allowBlank: false
                }, {
                    fieldLabel: '营业场所',
                    itemId: 'name',
                    name: 'name',
                    allowBlank: false
                }, {
                    fieldLabel: '备注',
                    itemId: 'remark',
                    name: 'remark'
                }, {
                    fieldLabel: '状态',
                    xtype: 'radiogroup',
                    itemId: 'status',
                    items: [{
                        name: 'status',
                        boxLabel: '有效',
                        inputValue: 1,
                        checked: true
                    },
                        {
                            name: 'status',
                            boxLabel: '无效',
                            inputValue: 0
                        }]
                }, {
                    fieldLabel: '场所类别',
                    xtype: 'radiogroup',
                    itemId: 'merstatus',
                    items: [{
                        name: 'merstatus',
                        boxLabel: '订餐消费',
                        inputValue: 1,
                        checked: true
                    },
                        {
                            name: 'merstatus',
                            boxLabel: '自由消费',
                            inputValue: 0
                        }]
                },
                    {
                        fieldLabel: '消费群组',
                        xtype: 'radiogroup',
                        itemId: 'restrictgroup',
                        items: [{
                            name: 'restrictgroup',
                            boxLabel: '开启',
                            inputValue: 1,
                            checked: true
                        },
                            {
                                name: 'restrictgroup',
                                boxLabel: '关闭',
                                inputValue: 0
                            }]
                    }
                ]
            }]
        });
        me.callParent(arguments);
    },

    Save: function () {
        var me = this;
        var form = me.down('form[itemId=placeform]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Consume/ConsumeUser/SaveConsumePlace',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.placegrid.getStore().reload();
                        me.close();
                        toast('保存成功！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }

});
