Ext.define('CamPus.view.card.position.positionStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getpositionList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        }
    }
});

Ext.define('CamPus.view.card.position.GroupStore',{
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: false,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getNameList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        }
    }
});

Ext.define('CamPus.view.card.position.OrgTreeStore', {
    extend: 'Ext.data.TreeStore',
    remoteSort: false,
    autoLoad: false,
    root: {
        uid: 'root',
        name: window.applang.get('orgname3'),
        expanded: true
    },
    proxy: {
        type: 'ajax',
        url: '/Card/OrgFramework/getCollegeClassTree',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json'
        },
        extraParams: {}
    }
});

Ext.define('CamPus.view.card.position.InfoListStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: false,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Card/TeachStudInfo/getInfoPositionList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { orgcode: '', infotype: window.applang.get("defaultinfotype"), intoyear: '0', viewchild: 'true' }
    }
});