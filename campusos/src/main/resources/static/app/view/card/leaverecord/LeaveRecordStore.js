Ext.define('CamPus.view.card.leaverecord.LeaveRecordStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/card/LeaveRecord/getLeaveRecordList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        }
    }
});
Ext.define('CamPus.view.card.leaverecord.infostore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/card/LeaveRecord/getInfoList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        }
    }
});

Ext.define('CamPus.view.card.leaverecord.OrgTreeStore', {
    extend: 'Ext.data.TreeStore',
    remoteSort: false,
    autoLoad: false,
    root: {
        uid: 'root',
        name: window.applang.get('orgname3'),
        expanded: true
    },
    proxy: {
        type: 'ajax',
        url: '/Card/OrgFramework/getCollegeClassTree',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json'
        },
        extraParams: {}
    }
});

Ext.define('CamPus.view.card.leaverecord.TechInfoStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: false,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/card/LeaveRecord/getInfoList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { orgcode: '', infotype:window.applang.get("defaultinfotype"), intoyear: '0', viewchild: 'true' }
    }
});
