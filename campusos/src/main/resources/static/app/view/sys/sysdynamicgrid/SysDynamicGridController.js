Ext.define('CamPus.view.sys.sysdynamicgrid.SysDynamicGridController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.SysDynamicGridController',
    view: ['SysDynamicGridView'],
    init: function() {
        var me = this;
        me.setControl({
            '*[itemId=add]': {
                click: me.onAddClick
            },
            '*[itemId=edit]': {
                click: me.onEditClick
            },
            '*[itemId=del]': {
                click: me.onDelClick
            }
        });

        me.store = me.view.store;
        me.grid = me.view.grid;
       
        me.store.on('beforeload', function(store) {
            var gridtype = me.view.down('combobox[itemId=gridtype]').getValue();
            Ext.apply(store.proxy.extraParams, {
                gridtype: (gridtype == '0' ? '' : gridtype)
            });
        });
    },
    onAddClick: function() {
        var me = this;
        var win = Ext.create('CamPus.view.sys.sysdynamicgrid.EditSysDynamicGridWindow', {
            grid: me.grid
        });
        win.show();
    },
    onEditClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择系统用户...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var win = Ext.create('CamPus.view.sys.sysdynamicgrid.EditSysDynamicGridWindow', {
            grid: me.grid
        });
        var form = win.down('form[itemId=form]');
        form.loadRecord(record);
        var columns=JSON.parse(record.get('columns'));
        win.DynamicGrid.getStore().setData(columns);
        win.show();
    },
    onDelClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择动态表格记录...');
            return;
        }
        var uid = me.grid.getSelection()[0].get('uid');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要删除所选动态表格 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/DynamicGrid/DelDynamicGrid',
                        params: { uid: uid },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.store.reload();
                                toast('操作成功');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    }
});