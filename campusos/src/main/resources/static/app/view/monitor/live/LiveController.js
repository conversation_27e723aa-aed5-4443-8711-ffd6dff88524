Ext.define('CamPus.view.monitor.live.LiveController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.LiveController',
    view: ['LiveView'],
    init: function() {
        var me = this;
        me.setControl({
            '*[itemId=add]': {
                click: me.onAddClick
            },
            '*[itemId=edit]': {
                click: me.onEditClick
            },
            '*[itemId=del]': {
                click: me.onDelClick
            },

            '*[itemId=update]': {
                click: me.updateDoorRead
            },

            'treepanel[itemId=areatree]': {
                select: me.onTreeSelect
            },
            '*[itemId=test]': {
                click: me.onDevTestClick
            },

            'gridpanel[itemId=DeviceGrid]': {
                rowdblclick: me.onDeviceRowdblClick
            },
            '*[itemId=setadmin]': {
                click: me.onSetadmin
            }



        });

        me.store = me.view.store;
        me.grid = me.view.grid;
        me.treestore = me.view.treestore;
        me.tree = me.view.tree;

        // me.store.on('beforeload', function(store) {
        //     var areacode = '';
        //     if (me.tree.getSelection().length > 0) {
        //         areacode = me.tree.getSelection()[0].get('code');
        //     }
        //     var devclass = me.view.down('comboxdictionary[itemId=devclass]');
        //     var server_uid = me.view.down('comboxlist[itemId=server_uid]');
        //     var viewchild = me.view.down('checkboxfield[itemId=viewchild]');
        //     Ext.apply(store.proxy.extraParams, {
        //         areacode: areacode,
        //         devclass: devclass.getValue(),
        //         server_uid: server_uid.getValue(),
        //         viewchild: viewchild.getValue()
        //     });
        // });
    },
    updateDoorRead: function () {
        Ext.Ajax.request({
            url: '/Dev/Device/updateDoorRead',
            method: 'POST',
            success: function(response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    toast('更新成功');
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    },
    onAddClick: function() {
        var me = this;
        var areacode = '';
        if (me.tree.getSelection().length == 0) {
            toast('请选择设备区域');
            return;
        }

        var record = me.tree.getSelection()[0];
        me.tree.pathName = '';
        me.tree.setPathName(record);

        var win = Ext.create('CamPus.view.dev.device.DeviceEditWindow', {
            title: '设备信息[' + me.tree.pathName + ']',
            aotuAddNew: true,
            ctrl: me,
            areacode: record.get('code'),
            areaname: record.get('name'),
            controllerid: '',
            grid: me.grid,
            lfbar: [{
                xtype: 'button',
                text: '检测设备',
                iconCls: 'x-fa fa-microchip',
                listeners: {
                    click: function(button, event) {
                        var port = CamPus.AppSession.wssport;
                        var userid = CamPus.AppSession.userid;
                        var devip = win.down('textfield[itemId=devip]').getValue();
                        if (!devip) {
                            toast('请输入设备IP地址');
                            return;
                        }
                        var topic = devip.replace(/\./g, '');
                        var testing = win.down('textareafield[itemId=testing]');
                        testing.setValue('');
                        if (me.ws) {
                            me.ws.close();
                        }
                        me.ws = window.wssclient(port, userid, topic, function(ws, result) {
                            testing.setValue(testing.getValue() + result.data + '\r\n');
                        }, function() {
                            Ext.Ajax.request({
                                url: '/Dev/Device/PingDevice',
                                params: {
                                    ip: devip,
                                    topic: topic
                                },
                                method: 'POST',
                                success: function(response) {
                                    var result = Ext.JSON.decode(response.responseText);
                                    if (!result.success) {
                                        toast(result.msg);
                                    }
                                }
                            });
                        });
                        //优化中.不一定成功
                        me.ws.close();
                    }
                }
            }],
            listeners: {
                close: function() {
                    if (me.ws) {
                        me.ws.close();
                    }
                }
            }
        });
        win.show();
    },
    onEditClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择设备信息记录');
            return;
        }
        var record = me.grid.getSelection()[0];
        var win = Ext.create('CamPus.view.dev.device.DeviceEditWindow', {
            isEdit: true,
            grid: me.grid,
            areacode: record.get('areacode'),
            areaname: record.get('areaname'),
            devclass: record.get('devclass'),
            controllerid: record.get('uid'),
            devmodel:record.get('devmodel'),
            used:record.get('used'),
            useclass:record.get('useclass'),
            lfbar: [{
                xtype: 'button',
                text: '检测设备',
                iconCls: 'x-fa fa-microchip',
                listeners: {
                    click: function(button, event) {
                        var devip = win.down('textfield[itemId=devip]').getValue();
                        if (!devip) {
                            toast('请输入设备IP地址');
                            return;
                        }
                        me.DevTest(devip);
                    }
                }
            }],
            listeners: {
                close: function() {
                    if (me.ws) {
                        me.ws.close();
                    }
                }
            }
        });
        var form = win.down('form[itemId=form]');
        form.loadRecord(record);
        win.show();

        var plugin = win.readgrid.getPlugin('devicedoorplugin');

        plugin.on('edit', function(editor, context, eOpts) {
            context.record.set('controllerid', record.get('uid'));
            context.record.set('devclass', record.get('devclass'));
            Ext.Ajax.request({
                url: '/Dev/Device/SaveDeviceRead',
                params: context.record.data,
                method: 'POST',
                success: function(response) {
                    var result = Ext.JSON.decode(response.responseText);
                    if (result.success) {
                        win.readgrid.getStore().reload();
                        toast('保存成功');
                    } else {
                        Ext.Msg.alert('系统信息', result.msg);
                    }
                }
            });
        });
    },
    onDelClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择设备记录...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var uid = record.get('uid');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除设备及读头信息 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Dev/Device/DelDevice',
                        params: {
                            devid: uid
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onTreeSelect: function() {
        this.store.loadPage(1);
    },
    onDevTestClick: function() {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择设备记录...');
            return;
        }
        var record = me.grid.getSelection()[0];
        me.DevTest(record.get('devip'));
    },
    DevTest: function(devip) {
        var me = this;
        var record = me.grid.getSelection()[0];
        var userid = CamPus.AppSession.userid;
        if (!devip) {
            toast('设备IP地址无效');
            return;
        }
        var topic = devip.replace(/\./g, '');
        if (me.ws) {
            me.ws.close();
        }
        var win = Ext.create('CamPus.view.dev.device.DeviceTestingWindow', {});
        var testing = win.down('textareafield[itemId=testing]');
        me.ws = window.wssclient(userid, topic, function(ws, result) {
            testing.setValue(testing.getValue() + result.data + '\r\n');
        }, function() {
            Ext.Ajax.request({
                url: '/Dev/Device/PingDevice',
                params: {
                    ip: devip,
                    topic: topic
                },
                method: 'POST',
                success: function(response) {
                    var result = Ext.JSON.decode(response.responseText);
                    if (!result.success) {
                        toast(result.msg);
                    }
                }
            });
            win.show();
        });
    },
    /* onDeviceRowdblClick: function(grid, record, element, rowIndex, e, eOpts) {
        if (record.get('devclass') == '2' && record.get('devip')) {
            if (isValidIP(record.get('devip'))) {
                window.open('http://' + record.get('devip'));
            }
        }
    } */
    onDeviceRowdblClick: function() {
        var me = this;
        me.onEditClick();
    },
    onSetadmin:function(){
        var win = Ext.create('CamPus.view.dev.device.DeviceAdminWindow', {
            autoSize: true
        });
        win.show();
    }
});