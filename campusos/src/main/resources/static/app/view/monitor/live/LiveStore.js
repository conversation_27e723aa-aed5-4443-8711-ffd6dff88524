Ext.define('CamPus.view.monitor.live.LiveStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Dev/Device/getDeviceList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { areacode: '', devclass: '', server_uid: '', devstatus: '' }
    }
});

Ext.define('CamPus.view.monitor.live.Doorstore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Dev/Device/getDoorList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        }
    }
});
Ext.define('CamPus.view.monitor.live.DoorReadstore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    fields: ['uid', 'name', 'areacode', 'inouttype', 'readhead', 'relays', 'readheadname', 'inoutname', 'relaysname', 'voice', 'readdevid'],
    proxy: {
        type: 'ajax',
        url: '/Dev/Device/getDeviceReadList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        }
    }
});

Ext.define('CamPus.view.monitor.live.AreaTreeStore', {
    extend: 'Ext.data.TreeStore',
    remoteSort: false,
    autoLoad: false,
    root: {
        uid: 'root',
        name: '区域、楼宇、房间',
        expanded: true
    },
    proxy: {
        type: 'ajax',
        url: '/Dev/Device/getAreaTree',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json'
        },
        extraParams: {}
    }
});

Ext.define('CamPus.view.monitor.live.DeviceAdminStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Dev/Device/getDevAdminList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        }
    }
});

Ext.define('CamPus.view.monitor.live.OrgTreeStore', {
    extend: 'Ext.data.TreeStore',
    remoteSort: false,
    autoLoad: false,
    root: {
        uid: 'root',
        name: window.applang.get('orgname3'),
        expanded: true
    },
    proxy: {
        type: 'ajax',
        url: '/Card/OrgFramework/getCollegeClassTree',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json'
        },
        extraParams: {}
    }
});

Ext.define('CamPus.view.monitor.live.TechInfoStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: false,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Dev/Device/getInfoList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        },
        extraParams: { orgcode: '', infotype: window.applang.get("defaultinfotype"), intoyear: '0', viewchild: 'true' }
    }
});