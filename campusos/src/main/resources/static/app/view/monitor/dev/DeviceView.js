Ext.define('CamPus.view.monitor.dev.DeviceView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.DeviceView",
    layout: 'fit',
    border: false,

    initComponent: function() {
        var me = this;

        // 创建一个容器来管理加载状态
        me.items = [{
            xtype: 'container',
            layout: 'card',
            activeItem: 0, // 默认显示加载页面
            items: [
                {
                    // 加载页面
                    xtype: 'component',
                    itemId: 'loadingCard',
                    cls: 'vue-loading-container',
                    html: me.getLoadingHtml()
                },
                {
                    // Vue iframe
                    xtype: 'component',
                    itemId: 'vueCard',
                    autoEl: {
                        tag: 'iframe',
                        src: 'http://47.112.126.84:3000/#/DeviceList',
                        frameborder: 0,
                        allowfullscreen: true,
                        style: 'width:100%;height:100%'
                    },
                    listeners: {
                        afterrender: function(component) {
                            var iframe = component.getEl().dom;
                            iframe.onload = function() {
                                // 切换到Vue页面
                                var container = me.down('container');
                                container.getLayout().setActiveItem(1);
                            };

                            // 添加错误处理
                            iframe.onerror = function() {
                                me.showErrorMessage();
                            };
                        }
                    }
                }
            ]
        }];

        me.callParent();
    },

    getLoadingHtml: function() {
        return `
            <div style="display: flex; justify-content: center; align-items: center; height: 100%; flex-direction: column; background: #f5f5f5;">
                <div class="spinner">
                    <div class="bounce1"></div>
                    <div class="bounce2"></div>
                    <div class="bounce3"></div>
                </div>
                <p style="margin-top: 20px; color: #666; font-size: 14px;">正在加载设备管理模块...</p>
            </div>
            <style>
                .spinner {
                    width: 70px;
                    text-align: center;
                }
                .spinner > div {
                    width: 12px;
                    height: 12px;
                    background-color: #3498db;
                    border-radius: 100%;  
                    display: inline-block;
                    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
                }
                .spinner .bounce1 { animation-delay: -0.32s; }
                .spinner .bounce2 { animation-delay: -0.16s; }
                @keyframes sk-bouncedelay {
                    0%, 80%, 100% { 
                        transform: scale(0);
                    } 40% { 
                        transform: scale(1.0);
                    }
                }
            </style>
        `;
    },

    showErrorMessage: function() {
        var container = this.down('container');
        var errorCard = Ext.create('Ext.Component', {
            html: `
                <div style="display: flex; justify-content: center; align-items: center; height: 100%; flex-direction: column;">
                    <div style="color: #e74c3c; font-size: 48px; margin-bottom: 20px;">⚠</div>
                    <p style="color: #e74c3c; font-size: 16px;">Vue应用加载失败</p>
                    <button style="margin-top: 20px; padding: 8px 16px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;" 
                            onclick="window.location.reload()">重新加载</button>
                </div>
            `
        });
        container.add(errorCard);
        container.getLayout().setActiveItem(errorCard);
    }
});