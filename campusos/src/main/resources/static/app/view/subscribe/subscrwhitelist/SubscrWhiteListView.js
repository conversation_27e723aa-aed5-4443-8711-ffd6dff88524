Ext.define('CamPus.view.subscribe.subscrwhitelist.SubscrWhiteListView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.SubscrWhiteListView",
    controller: 'SubscrWhiteListController',
    requires: [
        'CamPus.view.subscribe.subscrwhitelist.SubscrWhiteListStore',
        'CamPus.view.subscribe.subscrwhitelist.SubscrWhiteListController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.housestore = Ext.create('CamPus.view.subscribe.subscrwhitelist.HouseStore', {});

        var housetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                hideLabel: true,
                flex:1,
                paramName: 'key',
                store: me.housestore,
                emptyText: '房间名称关键词...'
            }]
        });

        me.houselist = Ext.create('Ext.grid.Panel', {
           // title: '房间',
            itemId: 'houselist',
            region: 'west',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            rowLines: true,
            columnLines: true,
            width: 240,
            store: me.housestore,
            selModel: 'checkboxmodel',
            tbar: housetbar,
                        viewConfig: {
                enableTextSelection: true,
                listeners: {
                    refresh: function (view, eOpts) {
                        view.select(0);
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '房间名称',
                    dataIndex: 'name',
                    width: 120,
                    minWidth: 120,
                    flex:1,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });


        me.store = Ext.create('CamPus.view.subscribe.subscrwhitelist.SubscrWhiteListStore', {});

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                hideLabel: true,
                width: 300,
                paramName: 'key',
                store: me.store,
                emptyText: window.applang.get("infocode")+'、姓名、卡号关键词...'
            }, '->',
            {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.store.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            //title: '人员列表',
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            tbar: tbar,
            viewConfig: {
                enableTextSelection: true
            },
            selModel: 'checkboxmodel',
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'infocode',
                    width: 120,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'infoname',
                    width: 120,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'cardno',
                    width: 120,
                    sortable: false
                },
                {
                    text: '有效日期',
                    dataIndex: 'enddate',
                    width: 120,
                    sortable: false
                },
                {
                    text: '通行时段',
                    dataIndex: 'timezone',
                    width: 120,
                    minWidth:120,
                    flex: 1,
                    sortable: false,
                    renderer: function (value, metaData) {
                        if (value) {
                            var times = [];
                            var valuelist = value.split(',');
                            Ext.each(valuelist, function (time) {
                                var stime = time.split('-')[0];
                                var etime = time.split('-')[1];
                                var strattime = ConvertTime.toPrefixTime(stime);
                                var endtime = ConvertTime.toPrefixTime(etime);
                                if (!strattime) {
                                    strattime = '00:00';
                                }
                                if (!endtime) {
                                    endtime = '00:00';
                                }
                                times.push(strattime + '-' + endtime);
                            });
                            metaData.tdAttr = 'data-qtip="' + times.join(',') + '"';
                            return times.join(',');
                        }
                    }
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.houselist, me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.subscribe.subscrwhitelist.InfoWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择人员',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.subscribe.subscrwhitelist.OrgTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get('orgname3') + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            columnLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function (tree, record, index, eOpts) {
                    me.store.loadPage(1);
                }
            }
        });

        me.store = Ext.create('CamPus.view.subscribe.subscrwhitelist.TechInfoStore', {
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });
                    Ext.apply(store.proxy.extraParams, {
                        orgcode: orgcode,
                        areacode: config.areacode,
                        intoyear: !intoyear.getValue() ? '0' : intoyear.getValue(),
                        infotype: infotype.getValue(),
                        viewchild: viewchild.getValue(),
                        selecteduid: JSON.stringify(selecteduid)
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'infotype',
                groupCode: 'SYS0000019',
                insertAll: '0|全部',
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                        var url = '/Card/OrgFramework/getCollegeClassTree';
                        if (newValue == '2') {
                            intoyear.show();
                        } else {
                            intoyear.setValue(0);
                            intoyear.hide();
                            url = '/Card/OrgFramework/getOrgTree';
                        }
                        me.treestore.proxy.url = url;
                        me.treestore.load();
                        me.store.load();
                    }
                }
            }, {
                xtype: 'comboxdictionary',
                itemId: 'intoyear',
                groupCode: 'SYS0000004',
                insertAll: '全部',
                width: 110,
                defaultSelectFirst: true,
                hidden: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            }, {
                xtype: 'searchfield',
                width: 150,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '关键词...'
            }, {
                xtype: 'checkboxfield',
                boxLabel: '显示子节点人员',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }]
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            },
            selModel: 'checkboxmodel'
        });

        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选人员：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.store.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['card', 'cardsn', 'name', 'infotype', 'uid', 'code', 'orgname', 'orgcode'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 220
        });

        Ext.apply(me, {
            items: [me.tree, me.grid, me.selectgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.selectgrid.getStore().getData().items.length == 0) {
            toast('请选择人员信息，选择以后拖拽至右侧表格');
            return;
        }
        var infoid = [];
        Ext.each(me.selectgrid.getStore().getData().items, function (item, index) {
            infoid.push(item.get('uid'))
        });
        var win = Ext.create('CamPus.view.subscribe.subscrwhitelist.timeWindow', {
            infoid: infoid.join(','),
            parentWin: me,
            maingrid: me.maingrid,
            areacode: me.areacode
        });
        win.show();
    }
});

Ext.define('CamPus.view.subscribe.subscrwhitelist.timeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '通行时段',
    layout: {
        type: 'vbox',
        pack: 'start',
        align: 'stretch'
    },
    width: 800,
    maximizable: true,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.timestore = Ext.create('Ext.data.Store', {
            fields: ['starttime', 'endtime'],
            data: []
        });

        me.timegrid = Ext.create('Ext.grid.Panel', {
            title: '设置通行时段(不设置默认为24小时)',
            margin: '0 15 10 15',
            border: true,
            flex: 1,
            tbar: ['->', {
                xtype: 'button',
                itemId: 'add',
                iconCls: 'x-fa fa-plus-square-o',
                text: '新增',
                listeners: {
                    click: function () {
                        var timeplugin = me.timegrid.getPlugin('timeplugin');
                        me.timegrid.getStore().add({
                            starttime: '',
                            endtime: ''
                        });
                        var data = me.timegrid.getStore().getData().items;
                        timeplugin.startEdit(data[data.length - 1]);
                    }
                }
            },
                {
                    xtype: 'button',
                    itemId: 'del',
                    iconCls: 'x-fa fa-trash',
                    text: '删除',
                    listeners: {
                        click: function () {
                            var selects = me.timegrid.getSelection();
                            if (selects.length > 0) {
                                me.timegrid.getStore().remove(selects[0]);
                            } else {
                                toast('请选择要删除的记录');
                            }
                        }
                    }
                }],
            store: me.timestore,
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '起始时间',
                    dataIndex: 'starttime',
                    flex: 1,
                    sortable: false,
                    editor: {
                        xtype: 'timefield',
                        minValue: '00:00',
                        maxValue: '23:59',
                        format: 'H:i',
                        increment: 30,
                        allowBlank: false
                    },
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(value, 'H:i');
                        }
                    }
                },
                {
                    text: '结束时间',
                    dataIndex: 'endtime',
                    flex: 1,
                    sortable: false,
                    editor: {
                        xtype: 'timefield',
                        minValue: '00:00',
                        maxValue: '23:59',
                        format: 'H:i',
                        increment: 30,
                        allowBlank: false
                    },
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(value, 'H:i');
                        }
                    }
                }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'timeplugin',
                listeners: {
                    canceledit:function(editor , context , eOpts){
                        var readrecord = me.timegrid.getSelection()[0];
                        if(!readrecord.getData().starttime && !readrecord.getData().endtime){
                            me.timegrid.getStore().remove(readrecord);
                        }
                    }
                }
            }
        });

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'uid',
                    name: 'uid',
                    value: config.uid
                },
                {
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid',
                    value: config.infoid
                },
                {
                    xtype: 'hiddenfield',
                    itemId: 'areacode',
                    name: 'areacode',
                    value: config.areacode
                },
                {
                    xtype: 'hiddenfield',
                    itemId: 'timezone',
                    name: 'timezone'
                },
                {
                    xtype: 'datetimefield',
                    fieldLabel: '有效期',
                    itemId: 'enddate',
                    name: 'enddate',
                    allowBlank: false,
                    value: new Date(Ext.Date.format(new Date(), '2050-01-01')),
                    format: 'Y-m-d'
                }]
            }, me.timegrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.timegrid.getStore().getData().items;
        var startlist = [];
        var endlist = [];
        Ext.each(records, function (record, i) {
            var starttime = Ext.Date.format(record.get('starttime'), 'H:i');
            var endtime = Ext.Date.format(record.get('endtime'), 'H:i');
            record.set('start', ConvertTime.toMinute(starttime));
            record.set('end', ConvertTime.toMinute(endtime));
            startlist.push(record.get('start'))
            endlist.push(record.get('end'))
        });

        var ok = true;
        Ext.each(records, function (record, i) {
            Ext.each(records, function (item, n) {
                if (record.get('id') != item.get('id')) {
                    if (record.get('start') >= item.get('start') && record.get('start') < item.get('end')) {
                        ok = false;
                        return false;
                    }
                    if (record.get('end') >= item.get('start') && record.get('end') < item.get('end')) {
                        ok = false;
                        return false;
                    }
                    if (record.get('start') <= item.get('start') && record.get('end') >= item.get('end')) {
                        ok = false;
                        return false;
                    }
                }
            });
            if (record.get('start') >= record.get('end')) {
                ok = false;
                return false;
            }
            if (record.get('end') > 1440) {
                ok = false;
            }
            if (!ok) {
                return false;
            }
        });
        if (!ok) {
            toast('时间段之间存在交集或设置错误');
            return false;
        }

        var spreIndex, scurrent;
        for (var i = 1; i < startlist.length; i++) {
            spreIndex = i - 1;
            scurrent = startlist[i];
            while (spreIndex >= 0 && startlist[spreIndex] > scurrent) {
                startlist[spreIndex + 1] = startlist[spreIndex];
                spreIndex--;
            }
            startlist[spreIndex + 1] = scurrent;
        }

        var epreIndex, ecurrent;
        for (var j = 1; j < endlist.length; j++) {
            epreIndex = j - 1;
            ecurrent = endlist[j];
            while (epreIndex >= 0 && endlist[epreIndex] > ecurrent) {
                endlist[epreIndex + 1] = endlist[epreIndex];
                epreIndex--;
            }
            endlist[epreIndex + 1] = ecurrent;
        }
        var timevaluelist = [];
        Ext.each(startlist,function(start,i){
            timevaluelist.push(Ext.String.format('{0}-{1}', start, endlist[i]));
        });

        var form = me.down('form[itemId=form]');
        form.down('hiddenfield[itemId=timezone]').setValue(timevaluelist.join(','));
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Subscribe/SubsrcWhiteList/saveNameList',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.maingrid.getStore().reload();
                        if(me.parentWin){
                            me.parentWin.close();
                        }
                        me.close();
                        toast('保存成功！');

                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});