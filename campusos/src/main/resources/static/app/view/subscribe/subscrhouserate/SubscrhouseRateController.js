Ext.define('CamPus.view.subscribe.subscrhouserate.SubscrhouseRateController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.SubscrhouseRateController',
    view: ['SubscrhouseRateView'],
    init: function () {
        var me = this;
        me.setControl({

        });
        me.grid = me.view.tree
        me.store = me.view.store

        me.store.on('beforeload', function (store) {
            var daily = me.view.down('datefield[itemId=daily]');
            Ext.apply(store.proxy.extraParams, {
                daily: Ext.Date.format(daily.getValue(), 'Y-m-d')
            });
        });
    }
});