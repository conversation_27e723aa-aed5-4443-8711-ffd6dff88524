Ext.define('CamPus.view.weixin.msg.MsgController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.MsgController',
    view: ['MsgView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=MsgCfgGrid]': {
                select: me.onMsgCfgGridSelect
            },
            '*[itemId=add]': {
                click: me.onAddClick
            },
            '*[itemId=edit]': {
                click: me.onEditClick
            },
            '*[itemId=del]': {
                click: me.onDelClick
            },
            '*[itemId=setcfg]': {
                click: me.onSetCfgClick
            },
            '*[itemId=addusermsg]': {
                click: me.onAddUserMsg
            },
            '*[itemId=editusermsg]': {
                click: me.onEditUserMsg
            },
            '*[itemId=sendusermsg]': {
                click: me.onSendUserMsg
            }
        });

        me.cfgstore = me.view.cfgstore;
        me.cfggrid = me.view.cfggrid;
        me.grid = me.view.grid;
        me.store = me.view.store;

        me.store.on('beforeload', function (store) {
            var cfgid = '';
            if (me.cfggrid.getSelection().length > 0) {
                cfgid = me.cfggrid.getSelection()[0].get('uid');
            }
            Ext.apply(store.proxy.extraParams, {
                cfgid: cfgid
            });
        });
    },
    onMsgCfgGridSelect: function () {
        var me = this;
        me.store.loadPage(1);
    },
    onAddClick: function () {
        var me = this;
        if (me.cfggrid.getSelection().length == 0) {
            toast('请选择消息模板...');
            return;
        }
        var cfgname = me.cfggrid.getSelection()[0].get('name');
        var demo = me.cfggrid.getSelection()[0].get('demo');
        var template = me.cfggrid.getSelection()[0].get('template');
        var cfgid = me.cfggrid.getSelection()[0].get('uid');
        var win = Ext.create('CamPus.view.weixin.msg.MsgAddOrEditWindow', {
            grid: me.grid,
            cfgname: cfgname,
            demo: demo,
            template: template,
            cfgid: cfgid
        });
        win.show();
    },
    onEditClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择模板消息配置记录...');
            return;
        }
        if(me.grid.getSelection()[0].get('msgtype') === 2){
            toast('请选择系统消息...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var win = Ext.create('CamPus.view.weixin.msg.MsgAddOrEditWindow', {
            grid: me.grid
        });
        var form = win.down('form[itemId=form]');
        form.loadRecord(record);
        win.show();
    },
    onDelClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择模板消息记录...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var uid = record.get('uid');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/WeiXin/Msg/DelMsg',
                        params: {
                            uid: uid
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onSetCfgClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择消息记录...');
            return;
        }
        var record = me.grid.getSelection()[0];
        Ext.Ajax.request({
            url: '/WeiXin/Msg/getTemplateKeys',
            params: {
                cfgid: record.get('cfgid')
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    var templatecfg = record.get('templatecfg');
                    if (!templatecfg) {
                        templatecfg = [];
                    } else {
                        templatecfg = JSON.parse(templatecfg);
                    }
                    Ext.each(result.data, function (item, i) {
                        Ext.each(templatecfg, function (cfgitem, i) {
                            if (item.key == cfgitem.key) {
                                item.value = cfgitem.value;
                                item.color = cfgitem.color;
                            }
                        });
                    });
                    var win = Ext.create('CamPus.view.weixin.msg.MsgSQLWindow', {
                        maingrid: me.grid,
                        templatecfg: result.data
                    });
                    var form = win.down('form[itemId=form]');
                    form.loadRecord(record);
                    win.show();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    },
    onAddUserMsg: function () {
        var me = this;
        if (me.cfggrid.getSelection().length == 0) {
            toast('请选择消息模板...');
            return;
        }
        var cfgid = me.cfggrid.getSelection()[0].get('uid');
        var demo = me.cfggrid.getSelection()[0].get('demo');
        var template = me.cfggrid.getSelection()[0].get('template');
        Ext.Ajax.request({
            url: '/WeiXin/Msg/getTemplateKeys',
            params: {
                cfgid: cfgid
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    Ext.each(result.data, function (item, i) {
                        if (item.key == item.key) {
                            item.value = item.value;
                            item.color = item.color;
                        }
                    });
                    var win = Ext.create('CamPus.view.weixin.msg.UserMsgWindow', {
                        demo: demo,
                        template: template,
                        cfgid: cfgid,
                        maingrid: me.grid,
                        templatecfg: result.data
                    });
                    win.show();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    },
    onEditUserMsg: function(){
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择消息...');
            return;
        }
        if(me.grid.getSelection()[0].get('msgtype') === 1){
            toast('请选择用户消息...');
            return;
        }
        var record = me.grid.getSelection()[0];
        Ext.Ajax.request({
            url: '/WeiXin/Msg/getTemplateKeys',
            params: {
                cfgid: record.get('cfgid')
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    var templatecfg = record.get('templatecfg');
                    if (!templatecfg) {
                        templatecfg = [];
                    } else {
                        templatecfg = JSON.parse(templatecfg);
                    }
                    Ext.each(result.data, function (item, i) {
                        Ext.each(templatecfg, function (cfgitem, i) {
                            if (item.key == cfgitem.key) {
                                item.value = cfgitem.value;
                                item.color = cfgitem.color;
                            }
                        });
                    });
                    var win = Ext.create('CamPus.view.weixin.msg.UserMsgWindow', {
                        maingrid: me.grid,
                        templatecfg: result.data
                    });
                    var form = win.down('form[itemId=form]');
                    form.loadRecord(record);
                    win.show();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    },
    onSendUserMsg: function(){
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择需要推送的消息...');
            return;
        }
        if(me.grid.getSelection()[0].get('msgtype') === 1){
            toast('请选择用户消息...');
            return;
        }
        var cfgcode = me.grid.getSelection()[0].get('code')
        var win = Ext.create('CamPus.view.weixin.msg.UserWindow', {
            cfgcode: cfgcode
        });
        win.show();
    }
});