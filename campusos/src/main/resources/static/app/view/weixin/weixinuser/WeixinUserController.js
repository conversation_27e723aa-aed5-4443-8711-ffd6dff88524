Ext.define('CamPus.view.weixin.weixinuser.WeixinUserController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.WeixinUserController',
    view: ['WeixinUserView'],
    init: function () {
        var me = this;
        me.setControl({
            'gridpanel[itemId=weixinuserlist]': {
                select: me.onWeixinUserSelect
            },
            '*[itemId=updateweixin]':{
                click: me.updateweixin
            },
            '*[itemId=del]':{
                click: me.onDelClick
            },
            '*[itemId=export]': {
                click: me.onExport
            },
        });
        me.weixinUserStore = me.view.weixinUserStore;
        me.grid = me.view.grid;
        me.usertype = me.view.down('combobox[itemId=usertype]');

        me.weixinUserStore.on('beforeload', function (store) {
            var usertype = me.usertype.getValue();
            Ext.apply(store.proxy.extraParams, {
                usertype: usertype
            });
        });
    },
    onWeixinUserSelect: function () {
        var me = this;
        if (window.tip) {
            window.tip.close();
            window.tip = null;
        }
    },
    onExport: function () {
        var me = this;
        Ext.get('loadingToast').show();
        Ext.Ajax.request({
            url: '/Weixin/WeixinUser/export',
            params: me.weixinUserStore.proxy.extraParams,
            method: 'POST',
            success: function (response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    updateweixin: function(){

        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择要更改的记录');
            return;
        }

        var uids = [];
        var statuss = [];

        Ext.each(me.grid.getSelection(), function(item, index) {
            uids.push(item.get('uid'));
            statuss.push(item.get('status'));
        });


        Ext.Ajax.request({
            url: '/Weixin/WeixinUser/updateWeixin',
            params: {
                uid: uids.join(','),
                status:statuss.join(',')
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    toast("修改状态成功");
                    me.weixinUserStore.reload();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    },
    onDelClick:function(){
    	 var me = this;
         if (me.grid.getSelection().length == 0) {
             toast('请选择要删除的微信用户');
             return;
         }

         var uids = [];
         Ext.each(me.grid.getSelection(), function(item, index) {
             uids.push(item.get('uid'));
         });
         Ext.Msg.show({
             title: '系统确认',
             message: '确认要彻底删除 ？',
             iconCls: 'fa fa-commenting-o',
             buttons: Ext.Msg.OKCANCEL,
             icon: Ext.Msg.QUESTION,
             fn: function(btn) {
                 if (btn === 'ok') {
                	 Ext.Ajax.request({
                         url: '/Weixin/WeixinUser/deleteWeixinUser',
                         params: {
                             uid: uids.join(',')
                         },
                         method: 'POST',
                         success: function (response) {
                             var result = Ext.JSON.decode(response.responseText);
                             if (result.success) {
                                 toast("操作成功");
                                 me.weixinUserStore.reload();
                             } else {
                                 Ext.Msg.alert('系统信息', result.msg);
                             }
                         }
                     });	
                 }
             }
         });  
         
    }
});