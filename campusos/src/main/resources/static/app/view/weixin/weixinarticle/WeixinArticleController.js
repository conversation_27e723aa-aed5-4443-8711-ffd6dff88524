Ext.define('CamPus.view.weixin.weixinarticle.WeixinArticleController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.WeixinArticleController',
    view: ['WeixinArticleView'],
    init: function() {
        var me = this;
        me.setControl({
            '*[itemId=add]': {
                click: me.onAddClick
            },
            '*[itemId=edit]': {
                click: me.onEditClick
            },
            '*[itemId=del]': {
                click: me.onDelClick
            },
            '*[itemId=weixinArticleGrid]':{
                rowdblclick: me.onReadClick
            },
            '*[itemId=send]': {
                click: me.onSend
            }
        });

        me.store = me.view.store;
        me.grid = me.view.grid;
    },
    onAddClick:function(){
        var me = this;
        var win=Ext.create('CamPus.view.weixin.weixinarticle.SetWeiXinArticleWindow',{
            maingrid:me.grid
        });
        win.show();
    },
    onEditClick:function(){
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择文章...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var win=Ext.create('CamPus.view.weixin.weixinarticle.SetWeiXinArticleWindow',{
            maingrid:me.grid
        });
        var form = win.down('form[itemId=form]');
        form.loadRecord(record);
        win.show();
    },
    onDelClick:function(){
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择文章...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var uid = record.get('uid');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Weixin/Article/delArticle',
                        params: {
                            uid: uid
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onSend: function(){
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择需要推送的消息...');
            return;
        }
        var articleid = me.grid.getSelection()[0].get('uid')
        var win = Ext.create('CamPus.view.weixin.weixinarticle.UserWindow', {
            articleid: articleid,
            autoSize: true
        });
        win.show();
    },
    onReadClick: function(){
        var me = this;
        var articleid = me.grid.getSelection()[0].get('uid')
        Ext.Ajax.request({
            url: '/Weixin/Article/showQRcode',
            params: {
                articleid: articleid
            },
            method: 'POST',
            success: function(response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if(result.data){
                        var win = Ext.create('CamPus.view.weixin.weixinarticle.ReadQRcodeWindows', {
                            qrcode: result.data.qrcode,
                            fbar: [
                                {
                                    xtype: 'button',
                                    text: '关闭',
                                    iconCls: 'x-fa fa-reply-all',
                                    listeners: {
                                        click: function (button, event) {
                                            win.close();
                                        }
                                    }
                                }
                            ]
                        });
                        win.show();
                    }else {
                        toast('无二维码生成...');
                    }
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});