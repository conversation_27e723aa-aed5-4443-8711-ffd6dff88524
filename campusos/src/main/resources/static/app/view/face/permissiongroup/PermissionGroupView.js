Ext.define('CamPus.view.face.permissiongroup.PermissionGroupView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.PermissionGroupView",
    controller: 'PermissionGroupController',
    requires: [
        'CamPus.view.face.permissiongroup.PermissionGroupStore',
        'CamPus.view.face.permissiongroup.PermissionGroupController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.pgroupstore = Ext.create('CamPus.view.face.permissiongroup.PermissionGroupStore', {

        });

        var groupbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.pgroupstore,
                emptyText: '权限组名称...'
            }]
        });

        me.groupgrid = Ext.create('Ext.grid.Panel', {
           // title: '权限组',
            itemId: 'pgroup',
            tbar: groupbar,
            region: 'west',
            store: me.pgroupstore,
            collapsible: false,
            multiColumnSort: true,
            border: false,
            rowLines: true,
            columnLines: true,
            width: 400,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {
                    text:'权限组编号',
                    dataIndex: 'groupindex',
                    width: 100,
                    sortable: false
                },
                {
                    text: '权限组名称',
                    dataIndex: 'name',
                    width: 100,
                    flex:1,
                    sortable: true
                },
                {
                    text: '周计划名称',
                    dataIndex: 'weekplanname',
                    minWidth: 100,
                    flex:1,
                    sortable: false
                },
                {
                    text: '是否限次',
                    dataIndex: 'limitCount',
                    width: 100,
                    minWidth: 100,
                    sortable: false,
                    flex: 1,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 0 || value == null) {
                            metaData.style += 'color:#5fa2dd;';
                            return '不限次';
                        } else {
                            metaData.style += 'color:#7bc309;';
                            return '限次:' +value;
                        }
                    }
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.store = Ext.create('CamPus.view.face.permissiongroup.PermissionPeopleStore', {

        });

        me.infoLabelStore = Ext.create('CamPus.view.face.permissiongroup.InfoLabelStore', {
            listeners: {
                beforeload: function (store) {
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    Ext.apply(store.proxy.extraParams, {
                        infotype: infotype.getValue()
                    });
                }
            }
        });

        me.treestore = Ext.create('CamPus.view.face.permissiongroup.OrgTreeStore', {

        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'downstatus',
                groupCode: 'SYS0000025',
                insertAll: '-2|全部',
                where: "code != '-1' and code != '-100'",
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            },
                {
                    xtype: 'comboxdictionary',
                    itemId: 'facedownstatus',
                    groupCode: 'SYS0000025',
                    insertAll: '-2|全部',
                    where: "code != '-1' and code != '-100'",
                    width: 110,
                    defaultSelectFirst: true,
                    listeners: {
                        change: function () {
                            me.store.reload();
                        }
                    }
                },
                {
                    xtype: 'comboxdictionary',
                    itemId: 'infotype',
                    groupCode: 'SYS0000019',
                    insertAll: '0|全部',
                    width: 120,
                    defaultSelectFirst: true,
                    hidden: (CamPus.apptype != '1'),
                    listeners: {
                        change: function (combox, newValue, oldValue, eOpts) {
                            var url = '/Card/OrgFramework/getCollegeClassTree';
                            me.infoLabelStore.load();
                            me.treestore.proxy.url = url;
                            me.treestore.load();
                            me.store.load();
                            var property = combox.ownerCt.down('comboxdictionary[itemId=property]');
                            if (newValue == 2 ||newValue == 3) {
                                property.where = "attr1='" + newValue + "'";
                            }else if(newValue ==1){
                                property.where = "attr1='" + newValue + "'";
                            }else {
                                property.where = '1=1';
                            }
                            property.getStore().on('beforeload', function (store) {
                                Ext.apply(store.proxy.extraParams, {
                                    where: property.where
                                });
                            });
                            property.getStore().reload();
                        }
                    }
                },
                {
                    xtype: 'comboxdictionary',
                    itemId: 'property',
                    groupCode: 'SYS0000055',
                    insertAll: '-1|全部',
                    where:'',
                    width: 120,
                    defaultSelectFirst: true,
                    listeners: {
                        change: function () {
                            me.store.reload();
                        }
                    }
                },
                {
                    xtype: 'combobox',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'name',
                    itemId: 'infolabel',
                    name: 'infolabel',
                    editable: false,
                    width: 110,
                    value: '全部',
                    store: me.infoLabelStore,
                    listeners: {
                        change: function (combox, newValue, oldValue, eOpts) {
                            me.store.load();
                        }
                    }
                },
                {
                xtype: 'searchfield',
                width: 220,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '设备号、姓名搜索...'
            },
            '->',
            {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.store.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
          //  title: '权限组人员信息',
            itemId: 'PeopleGrid',
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            width:950,
            store: me.store,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '下载状态',
                    dataIndex: 'downword',
                    width: 80,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record.get('downstatus') == 0) {
                            metaData.style += 'color:#5fa2dd;';
                        } else if (record.get('downstatus') == 1) {
                            metaData.style += 'color:#7bc309;';
                        } else if (record.get('downstatus') == 2) {
                            metaData.style += 'color:red;';
                        } else if (record.get('downstatus') == 3) {
                            metaData.style += 'color:red;';
                        } else if (record.get('downstatus') == 4) {
                            metaData.style += 'color:red;';
                        } else if (record.get('downstatus') == 5) {
                            metaData.style += 'color:#5fa2dd;';
                        }
                        return value;
                    }
                },
                {
                    text: '人脸下发状态',
                    dataIndex: 'downfaceword',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record.get('facedownstatus') == 0) {
                            metaData.style += 'color:#5fa2dd;';
                        } else if (record.get('facedownstatus') == 1) {
                            metaData.style += 'color:#7bc309;';
                        } else if (record.get('facedownstatus') == 2) {
                            metaData.style += 'color:red;';
                        } else if (record.get('facedownstatus') == 3) {
                            metaData.style += 'color:red;';
                        } else if (record.get('facedownstatus') == 4) {
                            metaData.style += 'color:red;';
                        } else if (record.get('facedownstatus') == 5) {
                            metaData.style += 'color:#5fa2dd;';
                        } else if (record.get('facedownstatus') == 6) {
                            metaData.style += 'color:red;';
                        }
                        return value;
                    }
                },
                {
                    text: '指纹下发状态',
                    dataIndex: 'downfingerword',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record.get('fingerdownstatus') == 0) {
                            metaData.style += 'color:#5fa2dd;';
                        } else if (record.get('fingerdownstatus') == 1) {
                            metaData.style += 'color:#7bc309;';
                        } else if (record.get('fingerdownstatus') == 2) {
                            metaData.style += 'color:red;';
                        } else if (record.get('fingerdownstatus') == 3) {
                            metaData.style += 'color:red;';
                        } else if (record.get('fingerdownstatus') == 4) {
                            metaData.style += 'color:red;';
                        } else if (record.get('fingerdownstatus') == 5) {
                            metaData.style += 'color:#5fa2dd;';
                        } else if (record.get('fingerdownstatus') == 6) {
                            metaData.style += 'color:red;';
                        }
                        return value;
                    }
                },
                {
                    text: '人员编号',
                    dataIndex: 'infoid',
                    width: 100,
                    sortable: false,
                    hidden:true
                },
                {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 100,
                    sortable: false
                },
                {
                    text: '权限组名称',
                    dataIndex: 'groupname',
                    width: 100,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 100,
                    sortable: true
                },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 100,
                    sortable: true
                },
                {
                    text: '部门',
                    dataIndex: 'orgname',
                    width: 100,
                    sortable: false
                },
                {
                    text: '权限组编号',
                    dataIndex: 'uid',
                    width: 100,
                    sortable: false,
                    hidden:true
                },
                {
                    text: '下载时间',
                    dataIndex: 'downtime',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '下载次数',
                    dataIndex: 'downnum',
                    width: 100,
                    sortable: false
                },
                {
                    text: '下载信息',
                    dataIndex: 'downmsg',
                    width: 100,
                    flex:1,
                    sortable: false
                }

            ],
            selModel: 'checkboxmodel',
            flex: 1,
            minWidth: 390,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.groupgrid, me.grid]
        });
        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.face.permissiongroup.ImportPersonWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '导入人员信息',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    height: 360,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                },{
                    xtype: 'button',
                    text: '下载导入模板',
                    handler: function() {
                        Ext.Ajax.request({
                            url: '/Card/TeachStudInfo/ExportTemplate',
                            params: {
                                name: '人员信息导入模板'
                            },
                            method: 'POST',
                            success: function(response) {
                                Ext.get('loadingToast').hide();
                                var result = Ext.JSON.decode(response.responseText);
                                if (result.success) {
                                    if (result.msg) {
                                        openWindow(result.msg, null, 'get');
                                    } else {
                                        toast('无导出数据');
                                    }
                                } else {
                                    toast(result.msg);
                                }
                            }
                        });
                    }
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中应至少包含工号、姓名；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Elevator/TEAuthrize/UploadFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('上传成功，请设置数据对应列！');
                        var nextwin = Ext.create('CamPus.view.face.permissiongroup.SetColumnWindow', {
                            excelpath: action.result.msg,
                            data: action.result.data,
                            maingrid: me.maingrid
                        });
                        nextwin.show();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.face.permissiongroup.SetColumnWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置导入对应列',
    width: 550,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('Ext.data.Store', {
            fields: ['column', 'value', 'columnname', 'columncode'],
            data: config.data
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            border: false,
            flex: 1,
            store: me.store,
            columns: [{
                text: '列序号',
                dataIndex: 'column',
                width: 65
            },
                {
                    text: '列名',
                    dataIndex: 'value',
                    flex: 1
                },
                {
                    text: '对应列名',
                    dataIndex: 'columncode',
                    flex: 1,
                    sortable: false,
                    editor: {
                        xtype: 'combobox',
                        queryMode: 'local',
                        displayField: 'name',
                        valueField: 'value',
                        editable: false,
                        store: Ext.create('Ext.data.Store', {
                            fields: ['name', 'value'],
                            data: [
                                { name: "忽略", value: '' },
                                { name: "工号", value: 'code' },
                                { name: "姓名", value: 'name' }
                            ]
                        }),
                        listeners: {
                            change: function (combox, newValue, oldValue, eOpts) {
                                if (newValue) {
                                    var records = me.grid.getStore().getData().items;
                                    var exists = false;
                                    Ext.each(records, function (record, i) {
                                        if (newValue && record.get('columncode') && record.get('columncode') == newValue) {
                                            exists = true;
                                        }
                                    });
                                    if (!exists) {
                                        combox.ownerCt.context.record.set('columncode', newValue);
                                        combox.ownerCt.context.record.set('columnname', combox.getRawValue());
                                    } else {
                                        toast('禁止多列对应一列');
                                        return false;
                                    }
                                } else {
                                    combox.ownerCt.context.record.set('columncode', '');
                                    combox.ownerCt.context.record.set('columnname', '');
                                }
                            }
                        }
                    },
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record) {
                            return record.get('columnname');
                        }
                    }
                }
            ],
            selModel: 'rowmodel',
            plugins: {
                ptype: 'rowediting',
                clicksToEdit: 2,
                saveBtnText: '保存',
                cancelBtnText: "取消",
                id: 'newlistplugin'
            }
        });

        Ext.apply(me, {
            fbar: ['->',
                {
                    xtype: 'button',
                    text: '确定',
                    listeners: {
                        click: function (button, event) {
                            me.Save();
                        }
                    }
                },
                {
                    xtype: 'button',
                    text: '取消',
                    listeners: {
                        click: function (button, event) {
                            me.close();
                        }
                    }
                }
            ],
            items: [me.grid, {
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                hidden: true,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    itemId: 'excelpath',
                    name: 'excelpath',
                    value: config.excelpath,
                    allowBlank: false
                }, {
                    itemId: 'columncfg',
                    name: 'columncfg',
                    allowBlank: false
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.grid.getStore().getData().items;
        var columncfg = [];
        Ext.each(records, function (record, i) {
            if (record.get('columncode')) {
                columncfg.push({
                    columncode: record.get('columncode'),
                    column: record.get('column')
                });
            }
        });
        if (columncfg.length == 0) {
            toast('请设置文档列对应关系');
            return;
        }
        var form = me.down('form[itemId=form]');
        form.down('textfield[itemId=columncfg]').setValue(JSON.stringify(columncfg));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Elevator/TEAuthrize/ImportInfoFile',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        // 假设 me 是当前作用域，maingrid 是一个 Grid 组件
                        var store = me.maingrid.getStore();

// 定义要传递的参数
                        var params = {
                            codes: action.result.msg
                        };

// 全局设置参数
                        store.getProxy().setExtraParams(params);
                        store.loadPage(1);
                        toast('数据导入成功！');
                    }

                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.face.permissiongroup.AddGroupWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择人员',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get('orgname3') + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function (tree, record, index, eOpts) {
                    me.peoplestore.loadPage(1);
                }
            }
        });

        me.peoplestore = Ext.create('CamPus.view.face.permissiongroup.PeopleStore', {
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var property = me.down('comboxdictionary[itemId=property]');
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    var infolabel = me.down('combobox[itemId=infolabel]');
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');
                    var ishasmodel = me.down('combobox[itemId=ishasmodel]');
                    var ismanwoman = me.down('combobox[itemId=ismanwoman]');
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });

                    Ext.apply(store.proxy.extraParams, {
                        orgcode: orgcode,
                        groupid: config.groupuid,
                        property:property.getValue(),
                        infotype: infotype.getValue(),
                        infolabel: infolabel.getValue(),
                        viewchild: viewchild.getValue(),
                        ishasmodel: ishasmodel.getValue(),
                        ismanwoman: ismanwoman.getValue(),
                        selecteduid: JSON.stringify(selecteduid)
                    });
                }
            }
        });

        me.infoLabelStore = Ext.create('CamPus.view.face.permissiongroup.InfoLabelStore', {
            listeners: {
                beforeload: function (store) {
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    Ext.apply(store.proxy.extraParams, {
                        infotype: infotype.getValue()
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'infotype',
                groupCode: 'SYS0000019',
                insertAll: '0|全部',
                width: 120,
                defaultSelectFirst: true,
                hidden: (CamPus.apptype != '1'),
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        var url = '/Card/OrgFramework/getCollegeClassTree';
                        var property = combox.ownerCt.down('comboxdictionary[itemId=property]');
                        if (newValue == 2 || newValue == 3) {
                            property.where = "attr1='" + newValue + "'";
                        }else if(newValue ==1){
                            property.where = "attr1='" + newValue + "'";
                            url = '/Card/OrgFramework/getOrgTree';
                        }else {
                            property.where = '1=1';
                            url = '/Card/OrgFramework/getOrgTree';
                        }
                        me.infoLabelStore.load();
                        me.treestore.proxy.url = url;
                        me.treestore.load();
                        me.peoplestore.load();
                        property.getStore().on('beforeload', function (store) {
                            Ext.apply(store.proxy.extraParams, {
                                where: property.where
                            });
                        });
                        property.getStore().reload();
                    }
                }
            },
            {
                xtype: 'comboxdictionary',
                itemId: 'property',
                groupCode: 'SYS0000055',
                insertAll: '-1|全部',
                where:'',
                width: 120,
                defaultSelectFirst: true,
                listeners: {
                    change: function () {
                        me.peoplestore.reload();
                    }
                }
            },
            {
                xtype: 'combobox',
                queryMode: 'local',
                displayField: 'name',
                valueField: 'name',
                itemId: 'infolabel',
                name: 'infolabel',
                editable: false,
                width: 110,
                value: '全部',
                store: me.infoLabelStore,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.peoplestore.load();
                    }
                }
            },
                {
                    xtype: 'combobox',
                    itemId: 'ishasmodel',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'value',
                    editable: false,
                    value: -1,
                    width: 120,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['value', 'name'],
                        data: [
                            { "value": -1, "name": "全部" },
                            { "value": 1, "name": "已建模" },
                            { "value": 0, "name": "未建模" }
                        ]
                    }),
                    listeners: {
                        change: function () {
                            me.peoplestore.reload();
                        }
                    }
                },
                {
                    xtype: 'combobox',
                    itemId: 'ismanwoman',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'value',
                    editable: false,
                    value: -1,
                    width: 120,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['value', 'name'],
                        data: [
                            { "value": -1, "name": "全部" },
                            { "value": 1, "name": "男" },
                            { "value": 0, "name": "女" }
                        ]
                    }),
                    listeners: {
                        change: function () {
                            me.peoplestore.reload();
                        }
                    }
                },
            {
                xtype: 'searchfield',
                width: 150,
                hideLabel: true,
                paramName: 'key',
                store: me.peoplestore,
                emptyText: '关键词...'
            }, {
                xtype: 'checkboxfield',
                boxLabel: '显示子节点人员',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.peoplestore.reload();
                    }
                }
            }, '->', {
                    xtype: 'button',
                    text: '导入人员',
                    iconCls: 'x-fa fa-eraser',
                    listeners: {
                        click: function (button, event) {
                            var win = Ext.create('CamPus.view.face.permissiongroup.ImportPersonWindow', {
                                maingrid: me.peoplegrid
                            });
                            win.show();
                        }
                    }
                }]
        });

        me.peoplegrid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.peoplestore,
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.peoplegrid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            },
            selModel: 'checkboxmodel'
        });


        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选人员：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.peoplestore.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['name', 'uid', 'code', 'orgname', 'orgcode'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.peoplegrid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            width: 220
        });

        Ext.apply(me, {
            items: [me.tree, me.peoplegrid, me.selectgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.selectgrid.getStore().getData().items.length == 0) {
            toast('请选择人员信息，选择以后拖拽至右侧表格');
            return;
        }
        var infoid = [];
        Ext.each(me.selectgrid.getStore().getData().items, function (item, i) {
            infoid.push(item.get('uid'));
        });
        Ext.Ajax.request({
            url: '/Face/PermissionGroup/addPermissionGroup',
            params: {
                infoid: infoid.join(','),
                groupuid: me.groupuid
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.grid.getStore().reload();
                    toast('保存成功...');
                    me.close();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});


