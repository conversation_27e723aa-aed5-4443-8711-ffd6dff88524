function showLargerImages(imageUrl,imgPath) {
    Ext.create('Ext.window.Window', {
        title: '照片对比',
        modal: true,
        html: ' <div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;">'+
            '<div style="height: 520px; width: 370px; margin-right: 20px; background-image: url('+imageUrl+'); background-size: contain; background-repeat: no-repeat; background-position: center;" onclick="openZoomWindow(\''+imageUrl+'\')"></div>'+
            '<div style="height: 520px; width: 370px; background-image: url('+imgPath+'); background-size: contain; background-repeat: no-repeat; background-position: center;" onclick="openZoomWindow(\''+imgPath+'\')"></div>'+
            '</div>',
        bodyPadding: 10,
        width: 800,
        height: 600
    }).show();


}
function openZoomWindow(imageUrl) {
    Ext.create('Ext.window.Window', {
        title: '放大查看',
        modal: true,
        html: '<div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;">' +
            '<div style="height: 100%; width: 100%; background-image: url(' + imageUrl + '); background-size: contain; background-repeat: no-repeat; background-position: center;"></div>' +
            '</div>',
        bodyPadding: 10,
        width: 1000,
        height: 750
    }).show();
}
Ext.define('CamPus.view.face.facenote.FaceNoteView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.FaceNoteView",
    controller: 'FaceNoteController',
    requires: [
        'CamPus.view.face.facenote.FaceNoteStore',
        'CamPus.view.face.facenote.FaceNoteController'
    ],

    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;
        me.devstore = Ext.create('CamPus.view.face.devface.DeviceStore', {
        });

        var devtbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxlist',
                itemId: 'server_uid',
                width: 120,
                insertAll: '全部',
                table: 'tb_dev_service',
                where: '',
                fields: 'uid,code,name',
                orderby: 'uid asc',
                valueField: 'code',
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.devstore.load();
                    }
                }
            }, {
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.devstore,
                emptyText: '设备名称、机号关键词...'
            }]
        });
        me.devgrid = Ext.create('Ext.grid.Panel', {
           // title: '设备信息',
            itemId: 'DeviceGrid',
            tbar: devtbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.devstore,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '序列号',
                    dataIndex: 'devsn',
                    width: 100,
                    hidden: true,
                    sortable: false
                }, {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 120,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'devname',
                    flex: 1,
                    minWidth: 70,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            width: 340,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });



        me.FaceNoteStore = Ext.create('CamPus.view.face.facenote.FaceNoteStore', {
        });
        // 根据 CamPus.AppSession.apptype 判断要展示的数据
        var personnelData = [];
        if (CamPus.AppSession.apptype == 1) {  //1 是校园版
            personnelData = [
                { name: '全部', value: '' },
                { name: '学生', value: 2 },
                { name: '教职工', value: 1 },
                { name: '家长', value: 3 }
            ];
        } else {  // 其他情况是企业版
            personnelData = [
                { name: '全部', value: '' },
                { name: '职工', value: 1 }
            ];
        }
        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'combobox',
                    // fieldLabel: '人员类型',
                    itemId: 'infoType',
                    name: 'infoType',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'value',
                    value: '',
                    editable: false,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['name', 'value'],
                        data: personnelData  // 根据判断的结果动态传入数据
                    }),
                    listeners: {
                        change: function () {
                            me.FaceNoteStore.loadPage(1);
                        }
                    }
                },
                {
                    xtype: 'datetimefield',
                    labelWidth: 20,
                    labelSeparator: '',
                    name: 'startdate',
                    itemId: 'startdate',
                    width: 180,
                    value: Ext.Date.add(new Date(Ext.Date.format(new Date(), 'Y-m-d 00:00:00'))),
                    format: 'Y-m-d H:i:s',
                    listeners: {
                        change: function (field) {
                            me.FaceNoteStore.loadPage(1);
                        }
                    }
                },
                {
                    xtype: 'datetimefield',
                    fieldLabel: '至',
                    labelWidth: 20,
                    labelSeparator: '',
                    width: 200,
                    name: 'enddate',
                    itemId: 'enddate',
                    value: new Date(Ext.Date.format(new Date(), 'Y-m-d 23:59:59')),
                    format: 'Y-m-d H:i:s',
                    listeners: {
                        change: function (field) {
                            me.FaceNoteStore.loadPage(1);
                        }
                    }
                },
                {
                    xtype: 'searchfield',
                    width: 200,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.FaceNoteStore,
                    emptyText: '姓名、' + window.applang.get("infocode") + '...'
                }, '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.FaceNoteStore.reload();
                    }
                }]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.FaceNotegrid = Ext.create('Ext.grid.Panel', {
           // title: '识别记录',
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            itemId: 'FaceNotegrid',
            border: false,
            tbar: tbar,
            store: me.FaceNoteStore,
            flex: 1,
            rootVisible: false,
            columnLines: true,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'rownumberer'
            },
                {
                    text: '识别时间',
                    width: 170,
                    align: 'center',
                    dataIndex: 'recordTime',
                    sortable: false
                },
                {
                    text: window.applang.get("infocode"),
                    width: 120,
                    align: 'center',
                    dataIndex: 'code',
                    sortable: false
                },
                {
                    text: '姓名',
                    width: 120,
                    align: 'center',
                    dataIndex: 'name',
                    sortable: false
                },
                {
                    text: '记录类型',
                    dataIndex: 'recordType',
                    width: 120,
                    sortable: false,
                    align: 'center',
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === "人脸识别") {
                            metaData.style += 'color:#7bc309;';
                            return '人脸识别 ';
                        } else if (value === "黑名单识别") {
                            metaData.style += 'color:red;';
                            return '黑名单识别';
                        }else if (value === "人证比对") {
                            metaData.style += 'color:red;';
                            return '人证比对';
                        }else if (value === "二维码识别") {
                            metaData.style += 'color:red;';
                            return '二维码识别';
                        }
                    }
                },
                {
                    text: '进出类型',
                    dataIndex: 'inOutType',
                    width: 120,
                    sortable: false,
                    align: 'center',
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === '进入') {
                            metaData.style += 'color:red;';
                            return '进入';
                        } else if (value === '出去') {
                            metaData.style += 'color:#7bc309;';
                            return '出去';
                        }
                    }
                },
                {
                    text: '通行抓拍',
                    dataIndex: 'facePath',
                    width: 80,
                    align: 'center',
                    sortable: false,
                    renderer: function (value, metaData, record) {
                        var imgpath = record.get('imgpath');
                        return '<div style="height:60px;width:60px; position: relative"><img src="' + value + '"  style="height: 100%; width:100%;object-fit: fill" onclick="showLargerImages(\'' + value + '\',\'' + imgpath + '\')">';

                    }
                },
                {
                    text: '机号',
                    width: 100,
                    align: 'center',
                    dataIndex: 'machineId',
                    sortable: false
                },
                {
                    text: '设备',
                    width: 130,
                    align: 'center',
                    dataIndex: 'devName',
                    sortable: false
                },
                {
                    text: '所属区域',
                    minWidth: 120,
                    flex: 1,
                    align: 'center',
                    dataIndex: 'area',
                    sortable: false
                },
                {
                    text: '所属单位',
                    width: 200,
                    align: 'center',
                    dataIndex: 'org',
                    sortable: false
                },
                {
                    text: '体温',
                    dataIndex: 'tiWen',
                    width: 80,
                    align: 'center',
                    sortable: false,
                    hidden:true,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.String.format('{0}℃', value)
                        }
                    }
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });
        Ext.apply(config, {
            items: [me.devgrid, me.FaceNotegrid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.face.facenote.FaceRecordImgWindows', {
    extend: 'CamPus.view.ux.Window',
    maximizable: false,
    height: 576,
    width: 768,
    layout: {
        type: 'fit'
    },
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);
        Ext.apply(me, {
            html: '<img src="' + window.getResourceUrl(CamPus.AppSession.resourcedomain, config.imagePath) + '" style="height: 486px;margin-left:auto;margin-right:auto;"/>'
        });
        me.callParent(arguments);
    }
});
Ext.define('CamPus.view.face.facenote.PullWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '拉取记录',
    width: 300,
    height: 150,
    defaults: {
        collapsible: false,
        split: true
    },
    maximizable: false,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            width: 500,
            height: 300,
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right',
                    labelWidth: 100
                },
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'datetimefield',
                        fieldLabel: '开始时间',
                        itemId: 'startdate',
                        name: 'startdate',
                        format: 'Y-m-d H:i:s',
                        allowBlank: false
                    },{
                        xtype: 'datetimefield',
                        fieldLabel: '结束时间',
                        itemId: 'enddate',
                        name: 'enddate',
                        format: 'Y-m-d H:i:s',
                        allowBlank: false
                    }
                ]
            }]
        });
        me.callParent(arguments);

    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        var startdate = form.down('datetimefield[itemId=startdate]').getValue();
        var enddate = form.down('datetimefield[itemId=enddate]').getValue();
        var machineid = [];
        var dev = me.devgrid.getSelection();
        Ext.each(dev, function(item, i) {
            machineid.push(item.get('machineid'));
        });
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Face/FaceNote/pullFaceRecords',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                params: {
                    machineids: machineid.join(','),
                    startdate: startdate,
                    enddate: enddate,
                },
                success: function (form, action) {
                    if (action.result.success) {
                        me.devstore.reload();
                        me.close();
                        toast('正在拉取记录，请稍后查看！！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});