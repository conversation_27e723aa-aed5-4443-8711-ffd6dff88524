Ext.define('CamPus.view.face.facehistorynote.FaceHistoryNoteController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.FaceHistoryNoteController',
    view: ['FaceHistoryNoteView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=DeviceGrid]': {
                select: me.onDeviceSelect
            },
            '*[itemId=FaceHistoryNotegrid]':{
                rowdblclick: me.showFaceRecordImg
            },
            'button[itemId=export]': {
                click: me.onExport
            }
        });
        me.FaceHistoryNotegrid = me.view.FaceHistoryNotegrid
        me.FaceHistoryNoteStore = me.view.FaceHistoryNoteStore;
        me.devgrid = me.view.devgrid
        me.devstore = me.view.devstore;
        me.FaceHistoryNoteStore.on('beforeload', function (store) {
            if (me.devgrid.getSelection().length > 0) {
                me.machineid = me.devgrid.getSelection()[0].get('machineid');
            }
            var startdate = me.view.down('datetimefield[itemId=startdate]');
            var enddate = me.view.down('datetimefield[itemId=enddate]');
            Ext.apply(store.proxy.extraParams, {
                startdate: Ext.Date.format(startdate.getValue(), 'Y-m-d H:i:s'),
                enddate: Ext.Date.format(enddate.getValue(), 'Y-m-d H:i:s'),
                machineid: me.machineid
            }
            );
        });
        me.devstore.on('beforeload', function (store) {
            var serveruid = me.view.down('comboxlist[itemId=server_uid]');
            Ext.apply(store.proxy.extraParams, {
                serveruid: serveruid.getValue()
            });
        });
    },
    onDeviceSelect: function () {
        var me = this;
        me.FaceHistoryNoteStore.reload();

    },
    showFaceRecordImg: function(grid, record, element, rowIndex, e, eOpts){
        var me = this;
        var name = me.FaceHistoryNotegrid.getSelection()[0].get('name');
        var recordimgPath = record.get('facepath');
        if(!recordimgPath){
            toast('未采集照片...');
        }else{
            var imgPath = recordimgPath
            var win = Ext.create('CamPus.view.face.facehistorynote.FaceRecordImgWindows',{
                title:'抓拍照片（'+name+'）',
                imagePath: imgPath,
                fbar:[
                    {
                        xtype: 'button',
                        text: '关闭',
                        iconCls: 'x-fa fa-reply-all',
                        listeners: {
                            click: function(button, event) {
                                win.close();
                            }
                        }
                    }
                ]
            });
            win.show();
        }
    },
    onExport: function () {
        var me = this;
        Ext.get('loadingToast').show();
        var extraParams = JSON.parse(JSON.stringify(me.FaceHistoryNotegrid.getStore().proxy.extraParams));
        var searchValue = me.FaceHistoryNotegrid.down('searchfield[paramName=key]').getValue();
        if(searchValue){
            extraParams.key = searchValue
        }
        Ext.Ajax.request({
            url: '/Face/FaceNote/ExportHistoryFaceNote',
            params: extraParams,
            method: 'POST',
            success: function (response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    }
});