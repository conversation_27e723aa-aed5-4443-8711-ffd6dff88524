Ext.define('CamPus.view.face.devface.DevFaceController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.DevFaceController',
    view: ['DevFaceView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=orgtree]': {
                select: me.onTreeSelect
            },
            '*[itemId=deviceGrid]': {
                select: me.onDeviceSelect
            },
            '*[itemId=add]': {
                click: me.onAddClick
            },
            '*[itemId=edittimezone]': {
                click: me.onEditTimeZoneClick
            },
            '*[itemId=del]': {
                click: me.onDelClick
            },
            '*[itemId=redown]': {
                click: me.onRedown
            },
            '*[itemId=allowcard]': {
                click: me.onAllowcard
            },
            '*[itemId=disallowcard]': {
                click: me.onDisallowcard
            }
        });

        me.store = me.view.store;
        me.grid = me.view.grid;
        me.devstore = me.view.devstore;
        me.devgrid = me.view.devgrid;
        me.classtreestore = me.view.classtreestore;
        me.tree = me.view.tree;

        me.store.on('beforeload', function (store) {
            var infodownstatus =  me.view.down('combobox[itemId=infodownstatus]').getValue();
            var timeid = me.view.down('comboxlist[itemId=timeid]').getValue();
            var viewchild = me.view.down('checkboxfield[itemId=viewchild]').getValue();

            var devids =[];
            Ext.each(me.devgrid.getSelection(), function(item, index) {
                devids.push(item.get('uid'));
            });
            var orgcode = '';
            if (me.tree.getSelection().length > 0) {
                orgcode = me.tree.getSelection()[0].get('code');
            }
            Ext.apply(store.proxy.extraParams, {
                infodownstatus:infodownstatus,
                timeid: timeid,
                viewchild: viewchild,
                devids: devids.join(','),
                orgcode: orgcode
            });
        });


        me.devstore.on('beforeload', function (store) {
            var serveruid = me.view.down('comboxlist[itemId=server_uid]');
            Ext.apply(store.proxy.extraParams, {
                serveruid: serveruid.getValue()
            });
        });


    },
    onTreeSelect: function() {
        var me = this;
        me.store.loadPage(1);
    },
    onDeviceSelect:function(){
        var me=this;
        me.store.loadPage(1);
    },
    viewFace:function( record ){
        var me = this;
        if (window.tip) {
            window.tip.close();
            window.tip = null;
        }
        window.tip = Ext.create('Ext.tip.ToolTip', {
            autoHide: false,
            closable: true,
            draggable: true,
            resizable: false,
            height: 370,
            width:320,
            scrollable: false,
            style: 'text-align: center;',
            html: '<img src="' + window.getResourceUrl(CamPus.AppSession.resourcedomain,record.get('imgpath')) + '?t=' + Math.random() + '" style="width:300px"/>'
        });
        window.tip.showAt([window.screen.width-window.tip.width-600,100]);
    },
    onAddClick: function () {
        var me = this;
        if (me.devgrid.getSelection().length == 0) {
            toast('请选择设备信息');
            return;
        }
        var devids = [];
        Ext.each(me.devgrid.getSelection(), function (record, index) {
            devids.push(record.get('uid'));
        });

        var win = Ext.create('CamPus.view.face.devface.AuthorizeWindow', {
            autoSize: true,
            devgrid: me.devgrid,
            maingrid: me.grid,
            devids: devids.join(','),
            insertfbar: [
                {
                    xtype: 'comboxlist',
                    fieldLabel: '时段方案',
                    itemId: 'timescheme',
                    table: 'tb_face_timescheme',
                    where: 'status=1',
                    fields: 'uid,code,name',
                    orderby: 'code asc',
                    valueField: 'uid',
                    defaultSelectFirst: true,
                    width: 200
                },
                {
                    xtype: 'checkboxfield',
                    boxLabel: '是否允许刷卡',
                    itemId: 'allowcard',
                    inputValue: 1,
                    checked: true
                }
            ]
        });
        win.show();
    },
    onEditTimeZoneClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择人员信息');
            return;
        }
        var win = Ext.create('CamPus.view.face.devface.TimeSchemeWindow', {
            maingrid: me.grid
        });
        win.show();
    },
    onDelClick: function () {
        var me = this;
        if (me.grid.getSelection().length == 0) {
            toast('请选择人员信息');
            return;
        }
        var uids = [];
        Ext.each(me.grid.getSelection(), function (record, index) {
            uids.push(record.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要删除设备人脸授权 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {

                    Ext.Ajax.request({
                        url: '/Face/DevFace/DelDevface',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onRedown: function(){
        var me = this;
        var message = '';

        var infoids = [];
        var devids = [];
        Ext.each(me.grid.getSelection(), function(item, index) {
            infoids.push(item.get('infoid'));
        });
        Ext.each(me.devgrid.getSelection(), function(item, index) {
            devids.push(item.get('uid'));
        });

        if(infoids.length === 0 && devids.length === 0){
            toast('请至少选择设备或名单记录...');
            return;
        }

        if(me.grid.getSelection().length === 0){
            message = '确认要执行重下?未选择任何记录，如果继续执行，则变更全部记录的状态';
        }else if(me.grid.getSelection().length > 0){
            message = '确认要执行重下?如果继续执行，则变更所选记录的状态';
        }

        Ext.Msg.show({
            title: '系统确认',
            message: message,
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn){
                if(btn === 'ok'){
                    Ext.Ajax.request({
                        url: '/Face/DevFace/redown',
                        params: {
                            infoids: infoids.join(','),
                            devids: devids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('重新下载成功！');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    })
                }
            }
        });
    },
    onAllowcard: function(){
        var me = this;
        var uid = [];
        Ext.each(me.grid.getSelection(), function(item, index) {
            uid.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认允许刷卡吗',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn){
                if(btn === 'ok'){
                    Ext.Ajax.request({
                        url: '/Face/DevFace/changeAllowCard',
                        params: {
                            uid: uid.join(','),
                            allowcard: 1
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('操作成功！');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    })
                }
            }
        });
    },
    onDisallowcard: function(){
        var me = this;
        var uid = [];
        Ext.each(me.grid.getSelection(), function(item, index) {
            uid.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认允许刷卡吗',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn){
                if(btn === 'ok'){
                    Ext.Ajax.request({
                        url: '/Face/DevFace/changeAllowCard',
                        params: {
                            uid: uid.join(','),
                            allowcard: 0
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('操作成功！');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    })
                }
            }
        });
    }

});
