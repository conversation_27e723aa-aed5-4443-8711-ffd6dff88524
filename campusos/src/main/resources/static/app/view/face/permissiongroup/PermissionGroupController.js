Ext.define('CamPus.view.face.permissiongroup.PermissionGroupController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.PermissionGroupController',
    view: ['PermissionGroupView'],
    init: function() {
        var me = this;
        me.setControl({
            '*[itemId=pgroup]':{
                select: me.tpgroupSelect
            },
            '*[itemId=add]': {
                click: me.onAddClick
            },
            '*[itemId=del]': {
                click: me.onDeleteClick
            },
            '*[itemId=redown]': {
                click: me.onRedown
            },
            '*[itemId=downstatus]':{
                select: me.pgroupSelect
            }
        });

        me.store = me.view.store;
        me.grid = me.view.grid;
        me.groupstore = me.view.groupstore;
        me.groupgrid = me.view.groupgrid;
        me.treestore = me.view.treestore;

        me.store.on('beforeload', function(store) {
            var groupid = '';
            if (me.groupgrid.getSelection().length > 0) {
                groupid = me.groupgrid.getSelection()[0].get('uid');
            }
            var downstatus =  me.view.down('combobox[itemId=downstatus]').getValue();
            var facedownstatus =  me.view.down('combobox[itemId=facedownstatus]').getValue();
            var infotype = me.view.down('comboxdictionary[itemId=infotype]');
            var property = me.view.down('comboxdictionary[itemId=property]');
            var infolabel = me.view.down('combobox[itemId=infolabel]');
            Ext.apply(store.proxy.extraParams, {
                groupid:groupid,
                downstatus:downstatus,
                facedownstatus:facedownstatus,
                infotype: infotype.getValue(),
                property: property.getValue(),
                infolabel: infolabel.getValue()
            });
        });
    },
    tpgroupSelect:function(){
        var me = this;
        me.store.loadPage(1);
    },
    pgroupSelect:function(){
        var me = this;
        me.store.load();
    },
    onAddClick: function () {
        var me =this;
        if (me.groupgrid.getSelection().length == 0) {
            toast('请选择一个权限组');
            return;
        }
        var groupuid=me.groupgrid.getSelection()[0].get('uid');
        var addgroupwin = Ext.create('CamPus.view.face.permissiongroup.AddGroupWindow', {
            autoSize:true,
            grid:me.grid,
            groupuid:groupuid,
            treestore:me.treestore
        });
        addgroupwin.show();
    },
    onDeleteClick: function () {
        var me = this;
        var uids = [];
        if (me.grid.getSelection().length == 0) {
            toast('请选择人员');
            return;
        }
        Ext.each(me.grid.getSelection(), function (item, index) {
            uids.push(item.get('uid'));
        });

        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Face/PermissionGroup/deletePermissionGroup',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                toast('删除成功');
                                me.store.reload();
                            } else {
                                toast(result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onRedown: function(){
        var me = this;
        var message = '';

        var uids = [];

        Ext.each(me.grid.getSelection(), function (item, index) {
            uids.push(item.get('uid'));
        });

        if(me.grid.getSelection().length === 0){
            message = '确认要执行重下?未选择任何记录，如果继续执行，则变更全部记录的状态';
            var records = me.grid.getStore().getRange();
            Ext.each(records,function(item,index){
                uids.push(item.get('uid'));
            });
        }else if(me.grid.getSelection().length > 0){
            message = '确认要执行重下?如果继续执行，则变更所选记录的状态';
        }

        Ext.Msg.show({
            title: '系统确认',
            message: message,
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn){
                if(btn === 'ok'){
                    Ext.Ajax.request({
                        url: '/Face/PermissionGroup/redown',
                        params: {
                            uids: uids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('重新下载成功！');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    })
                }
            }
        });
    }



});
