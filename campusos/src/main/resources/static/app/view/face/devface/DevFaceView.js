Ext.define('CamPus.view.face.devface.DevFaceView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.DevFaceView",
    controller: 'DevFaceController',
    requires: [
        'CamPus.view.face.devface.DevFaceStore',
        'CamPus.view.face.devface.DevFaceController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.devstore = Ext.create('CamPus.view.face.devface.DeviceStore', {

        });

        var devtbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxlist',
                itemId: 'server_uid',
                width: 120,
                insertAll: '全部',
                table: 'tb_dev_service',
                where: '',
                fields: 'uid,code,name',
                orderby: 'uid asc',
                valueField: 'code',
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.devstore.load();
                    }
                }
            }, {
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.devstore,
                emptyText: '设备名称、机号关键词...'
            }]
        });

        me.devgrid = Ext.create('Ext.grid.Panel', {
            title: '设备信息',
            itemId: 'deviceGrid',
            tbar: devtbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.devstore,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '序列号',
                    dataIndex: 'devsn',
                    width: 100,
                    hidden: true,
                    sortable: false
                }, {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 120,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'devname',
                    flex: 1,
                    minWidth: 100,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 340,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.classtreestore = Ext.create('CamPus.view.face.devface.ClassTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 220,
                hideLabel: true,
                paramName: 'key',
                store: me.classtreestore,
                emptyText: window.applang.get("orgname2") + '关键词...'
            },'->',{
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '取消选择',
                handler: function () {
                    me.tree.getSelectionModel().deselectAll();
                    me.store.reload();
                }
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            itemId: 'orgtree',
            title: '所属单位',
            tbar: orgtreetbar,
            region: 'west',
            collapsible: true,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.classtreestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            setPathName: function (node) {
                this.pathName = (this.pathName ? node.get('name') + '/' + this.pathName : node.get('name'));
                if (node.parentNode && node.parentNode.id != 'root') {
                    this.setPathName(node.parentNode);
                }
            }
        });

        me.store = Ext.create('CamPus.view.face.devface.DevFaceStore', {

        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'infodownstatus',
                groupCode: 'SYS0000025',
                insertAll: '-1|全部',
                where: "code != '-1'",
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            },
            {
                xtype: 'comboxlist',
                itemId: 'timeid',
                name:'timeid',
                insertAll: '全部',
                table: 'tb_face_timescheme',
                where: "",
                fields: 'uid,name',
                orderby: 'code asc',
                valueField: 'uid',
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            },
            {
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: window.applang.get('infocode') + '、姓名关键词...'
            },
            {
                xtype: 'checkboxfield',
                boxLabel: '显示子节点人员',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            },'->', {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.store.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            //title: '人员信息',
            itemId: 'InfoFaceGrid',
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    xtype: "actioncolumn",
                    dataIndex: 'imgpath',
                    header: "照片",
                    align: 'center',
                    width: 100,
                    colHideable: false,
                    menuDisabled: true,
                    stopSelection: true,
                    items: [{
                        icon: './../resources/images/nobody.png',
                        tooltip: '点击查看大头像',
                        iconCls: 'border-radius50 size36',
                        handler: function(grid, rowIndex, colIndex) {
                            var record = grid.getStore().getAt(rowIndex);
                            me.controller.viewFace(record);
                        }
                    }],
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if(value){
                            this.items[0].icon=window.getResourceUrl(CamPus.AppSession.resourcedomain,value) + '?t=' + Math.random();
                        }else{
                            this.items[0].icon = './../resources/images/nobody.png'
                        }
                    }
                },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 100,
                    sortable: false
                },
                {
                    text: '人员下载状态',
                    dataIndex: 'infodownstatusname',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record.get('infodownstatus') == 0) {
                            metaData.style += 'color:#5fa2dd;';
                        } else if (record.get('infodownstatus') == 1) {
                            metaData.style += 'color:#7bc309;';
                        } else if (record.get('infodownstatus') == 2) {
                            metaData.style += 'color:red;';
                        } else if (record.get('infodownstatus') == 3) {
                            metaData.style += 'color:red;';
                        } else if (record.get('infodownstatus') == 4) {
                            metaData.style += 'color:red;';
                        } else if (record.get('infodownstatus') == 5) {
                            metaData.style += 'color:#5fa2dd;';
                        } else if (record.get('infodownstatus') == 6) {
                            metaData.style += 'color:red;';
                        }
                        return value;
                    }
                },
                {
                    text: '时段下载状态',
                    dataIndex: 'timedownstatusname',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record.get('timedownstatus') == 0) {
                            metaData.style += 'color:#5fa2dd;';
                        } else if (record.get('timedownstatus') == 1) {
                            metaData.style += 'color:#7bc309;';
                        } else if (record.get('timedownstatus') == 2) {
                            metaData.style += 'color:red;';
                        } else if (record.get('timedownstatus') == 3) {
                            metaData.style += 'color:red;';
                        } else if (record.get('timedownstatus') == 4) {
                            metaData.style += 'color:red;';
                        } else if (record.get('timedownstatus') == 5) {
                            metaData.style += 'color:#5fa2dd;';
                        } else if (record.get('timedownstatus') == 6) {
                            metaData.style += 'color:red;';
                        }
                        return value;
                    }
                },
                {
                    text: '指纹下发状态',
                    dataIndex: 'fingerdownstatus',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (record.get('fingerdownstatus') == 0) {
                            metaData.style += 'color:#5fa2dd;';
                            return "待下载";
                        } else if (record.get('fingerdownstatus') == 1) {
                            metaData.style += 'color:#7bc309;';
                            return "已下载";
                        } else if (record.get('fingerdownstatus') == 2) {
                            metaData.style += 'color:red;';
                            return "下载失败";
                        } else if (record.get('fingerdownstatus') == 3) {
                            metaData.style += 'color:red;';
                            return "待删除"
                        } else if (record.get('fingerdownstatus') == 4) {
                            metaData.style += 'color:red;';
                            return "删除失败"
                        } else if (record.get('fingerdownstatus') == 5) {
                            metaData.style += 'color:#5fa2dd;';
                            return "待重下"
                        } else if (record.get('fingerdownstatus') == 6) {
                            metaData.style += 'color:red;';
                            return "重下失败";
                        }
                    }
                },
                {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 80,
                    sortable: false
                },
                {
                    text: '设备名称',
                    dataIndex: 'devname',
                    width: 100,
                    sortable: false
                },
                {
                    text: '时段方案',
                    dataIndex: 'timename',
                    width: 150,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'card',
                    width: 100,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 150,
                    sortable: false
                },
                {
                    text: '是否允许刷卡',
                    dataIndex: 'allowcard',
                    width: 150,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            metaData.style += 'color:#7bc309;';
                            return '是';
                        } else if (value == 0) {
                            metaData.style += 'color:red;';
                            return '否';
                        }
                    }
                },
                {
                    text: '状态备注',
                    dataIndex: 'downmsg',
                    width: 150,
                    sortable: false
                },
                {
                    text: '授权时间',
                    dataIndex: 'createdate',
                    width: 170,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '授权人',
                    dataIndex: 'creator',
                    width: 100,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            flex: 1,
            minWidth: 390,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [{
                xtype: 'tabpanel',
                region: 'west',
                width: 356,
                collapsible: false,
                items: [
                    me.devgrid,
                    me.tree
                ]
            }, me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});



Ext.define('CamPus.view.face.devface.AuthorizeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择人员',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.face.devface.OrgTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get("orgname3") + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function (tree, record, index, eOpts) {
                    me.store.loadPage(1);
                }
            }
        });

        me.store = Ext.create('CamPus.view.face.devface.TechInfoStore', {
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var property = me.down('comboxdictionary[itemId=property]');
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');
                    var infolabel = me.down('combobox[itemId=infolabel]');
                    var ishasmodel = me.down('combobox[itemId=ishasmodel]');
                    var ismanwoman = me.down('combobox[itemId=ismanwoman]');
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });

                    Ext.apply(store.proxy.extraParams, {
                        orgcode: orgcode,
                        property:property.getValue(),
                        infotype: infotype.getValue(),
                        infolabel: infolabel.getValue(),
                        viewchild: viewchild.getValue(),
                        ishasmodel: ishasmodel.getValue(),
                        ismanwoman: ismanwoman.getValue(),
                        devids: config.devids,
                        selecteduid: JSON.stringify(selecteduid)
                    });
                }
            }
        });

        me.infoLabelStore = Ext.create('CamPus.view.face.devface.InfoLabelStore', {
            listeners: {
                beforeload: function (store) {
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    Ext.apply(store.proxy.extraParams, {
                        infotype: infotype.getValue()
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                    xtype: 'comboxdictionary',
                    itemId: 'infotype',
                    groupCode: 'SYS0000019',
                    insertAll: '0|全部',
                    width: 120,
                    defaultSelectFirst: true,
                    hidden: (CamPus.apptype != '1'),
                    listeners: {
                        change: function (combox, newValue, oldValue, eOpts) {
                            var url = '/Card/OrgFramework/getCollegeClassTree';
                            var property = combox.ownerCt.down('comboxdictionary[itemId=property]');
                            if (newValue == 2) {
                                property.where = "attr1='" + newValue + "'";

                            }else if(newValue ==1){
                                property.where = "attr1='" + newValue + "'";
                                url = '/Card/OrgFramework/getOrgTree';
                            }else {
                                property.where = '1=1';
                                url = '/Card/OrgFramework/getOrgTree';
                            }
                            me.infoLabelStore.load();
                            me.treestore.proxy.url = url;
                            me.treestore.load();
                            me.store.load();
                            property.getStore().on('beforeload', function (store) {
                                Ext.apply(store.proxy.extraParams, {
                                    where: property.where
                                });
                            });
                            property.getStore().reload();
                        }
                    }
                },
                {
                xtype: 'comboxdictionary',
                itemId: 'property',
                groupCode: 'SYS0000055',
                insertAll: '-1|全部',
                where:'',
                width: 120,
                defaultSelectFirst: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            },
            {
                xtype: 'combobox',
                queryMode: 'local',
                displayField: 'name',
                valueField: 'name',
                itemId: 'infolabel',
                name: 'infolabel',
                editable: false,
                width: 110,
                value: '全部',
                store: me.infoLabelStore,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            },
                {
                    xtype: 'combobox',
                    itemId: 'ishasmodel',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'value',
                    editable: false,
                    value: 1,
                    width: 120,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['value', 'name'],
                        data: [
                            { "value": -1, "name": "全部" },
                            { "value": 1, "name": "已建模" },
                            { "value": 0, "name": "未建模" }
                        ]
                    }),
                    listeners: {
                        change: function () {
                            me.store.reload();
                        }
                    }
                },
                {
                    xtype: 'combobox',
                    itemId: 'ismanwoman',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'value',
                    editable: false,
                    value: -1,
                    width: 120,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['value', 'name'],
                        data: [
                            { "value": -1, "name": "全部" },
                            { "value": 1, "name": "男" },
                            { "value": 0, "name": "女" }
                        ]
                    }),
                    listeners: {
                        change: function () {
                            me.store.reload();
                        }
                    }
                },
            {
                xtype: 'searchfield',
                itemId: 'searchKey',
                width: 150,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '关键词...'
            },
            {
                xtype: 'button',
                itemId: 'readCard',
                text: '读卡',
                iconCls: 'x-fa fa-microchip',
                hidden: CamPus.AppSession.cardtype == 'id',
                handler: function () {
                    window.onReadCard(function (identid, CardNO) {
                        var searchKey = me.down('searchfield[itemId=searchKey]');
                        searchKey.setValue(CardNO);
                        searchKey.onSearchClick();
                    });
                }
            },
            {
                xtype: 'checkboxfield',
                boxLabel: '含子节点',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }]
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: true,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '卡号',
                    dataIndex: 'card',
                    width: 120,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选人员：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.store.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['card', 'cardsn', 'name', 'infotype', 'uid', 'code', 'orgname'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    minWidth: 200,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 220
        });

        Ext.apply(me, {
            items: [me.tree, me.grid, me.selectgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var records = me.selectgrid.getStore().getData().items;
        if (records.length == 0) {
            toast('请选择人员信息，选择以后拖拽至右侧表格');
            return;
        }
        var timeid = me.down('comboxlist[itemId=timescheme]').getValue();
        var allowcard = me.down('checkboxfield[itemId=allowcard]').getValue();
        var infoids = [];
        Ext.each(records, function (record, index) {
            infoids.push(record.get('uid'));
        });

        if (!timeid) {
            toast('请选择时段方案（窗口左下角）');
            return;
        }
        Ext.get('loadingToast').show();
        Ext.Ajax.request({
            url: '/Face/DevFace/SaveAuthorizeFace',
            params: {
                devids: me.devids,
                timeid: timeid,
                infoids: infoids.join(','),
                allowcard: allowcard
            },
            method: 'POST',
            success: function (response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.maingrid.getStore().reload();
                    toast('授权成功...');
                    me.close();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});


Ext.define('CamPus.view.face.devface.TimeSchemeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择时段方案',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.store = Ext.create('CamPus.view.face.devface.TimeSchemeStore', {
            autoLoad: true
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            multiColumnSort: true,
            border: false,
            rowLines: true,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '时段编号',
                    dataIndex: 'code',
                    width: 80,
                    sortable: false
                },
                {
                    text: '时段名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '时区一',
                    flex: 1,
                    minWidth: 250,
                    columns: [
                        {
                            text: '开始时间',
                            dataIndex: 'start1',
                            flex: 1,
                            sortable: false
                        },
                        {
                            text: '结束时间',
                            dataIndex: 'end1',
                            flex: 1,
                            sortable: false
                        }
                    ]
                }, {
                    text: '时区二',
                    flex: 1,
                    minWidth: 250,
                    columns: [
                        {
                            text: '开始时间',
                            dataIndex: 'start2',
                            flex: 1,
                            sortable: false
                        },
                        {
                            text: '结束时间',
                            dataIndex: 'end2',
                            flex: 1,
                            sortable: false
                        }
                    ]
                }, {
                    text: '时区三',
                    flex: 1,
                    minWidth: 250,
                    columns: [
                        {
                            text: '开始时间',
                            dataIndex: 'start3',
                            flex: 1,
                            sortable: false
                        },
                        {
                            text: '结束时间',
                            dataIndex: 'end3',
                            flex: 1,
                            sortable: false
                        }
                    ]
                }, {
                    text: '时区四',
                    flex: 1,
                    minWidth: 250,
                    columns: [
                        {
                            text: '开始时间',
                            dataIndex: 'start4',
                            flex: 1,
                            sortable: false
                        },
                        {
                            text: '结束时间',
                            dataIndex: 'end4',
                            flex: 1,
                            sortable: false
                        }
                    ]
                }, {
                    text: '时区五',
                    flex: 1,
                    minWidth: 250,
                    columns: [
                        {
                            text: '开始时间',
                            dataIndex: 'start5',
                            flex: 1,
                            sortable: false
                        },
                        {
                            text: '结束时间',
                            dataIndex: 'end5',
                            flex: 1,
                            sortable: false
                        }
                    ]
                }, {
                    text: '时区六',
                    flex: 1,
                    minWidth: 250,
                    columns: [
                        {
                            text: '开始时间',
                            dataIndex: 'start6',
                            flex: 1,
                            sortable: false
                        },
                        {
                            text: '结束时间',
                            dataIndex: 'end6',
                            flex: 1,
                            sortable: false
                        }
                    ]
                }
            ],
            selModel: 'rowmodel'
        });

        Ext.apply(me, {
            items: [me.grid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;

        if (me.grid.getSelection().length == 0) {
            toast('请选择时段方案');
            return;
        }
        var timeid = me.grid.getSelection()[0].get('uid');

        var records = me.maingrid.getSelection();
        if (records.length == 0) {
            toast('请选择主界面人员信息');
            return;
        }
        var uids = [];
        Ext.each(records, function (record, index) {
            uids.push(record.get('uid'));
        });

        Ext.Ajax.request({
            url: '/Face/DevFace/UpdateTimeScheme',
            params: {
                timeid: timeid,
                uids: uids.join(',')
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.maingrid.getStore().reload();
                    toast('修改成功...');
                    me.close();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});
