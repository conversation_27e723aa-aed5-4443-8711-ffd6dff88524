Ext.define('CamPus.view.time.dailyreport.DailyReportView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.DailyReportView",
    controller: 'DailyReportController',
    requires: [
        'CamPus.view.time.dailyreport.DailyReportStore',
        'CamPus.view.time.dailyreport.DailyReportController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: false,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {

        var me = this;

        me.treestore = Ext.create('CamPus.view.time.dailyreport.CollegeClassTreeStore', {

        });

        var collegeclasstbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get("orgname2") + '关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            itemId: 'collegeclasstree',
           // title: window.applang.get("orgname2"),
            tbar: collegeclasstbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 280,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
                        // viewConfig: {
            //     enableTextSelection: true,
            //     listeners: {
            //         refresh: function (view, eOpts) {
            //             view.select(0);
            //         }
            //     }
            // },,
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            setPathName: function (node) {
                this.pathName = (this.pathName ? node.get('name') + '/' + this.pathName : node.get('name'));
                if (node.parentNode && node.parentNode.id != 'root') {
                    this.setPathName(node.parentNode);
                }
            }
        });

        var gridcolumns = [
            { xtype: 'rownumberer' },
            {
                text: window.applang.get("infocode"),
                dataIndex: 'infocode',
                width: 100,
                sortable: false
            },
            {
                text: '姓名',
                dataIndex: 'infoname',
                width: 100,
                sortable: false
            },
            {
                text: '部门',
                dataIndex: 'orgname',
                width: 120,
                sortable: false
            },
            {
                text: '日期',
                dataIndex: 'daily',
                width: 100,
                sortable: false
            },
            {
                text: '类型',
                dataIndex: 'nweekday',
                width: 60,
                sortable: false,
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    var dayname = ['休息', '工作', '法定假']
                    if (value == 1) {
                        return dayname[0];
                    } else if (value == 8) {
                        return dayname[2];
                    } else {
                        return dayname[1];
                    }
                }
            },
            {
                text: '班制',
                dataIndex: 'bzname',
                width: 100,
                sortable: false,
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    metaData.style += Ext.String.format('border-left:solid 2px {0};color:{0}', record.get('color'));
                    return value;
                }
            },
            {
                text: '调休',
                dataIndex: 'tiaoxiu',
                width: 50,
                sortable: false,
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    if (value == 0) {
                        return '否';
                    } else if (value == 1) {
                        return '是';
                    }
                }
            },
            {
                text: '互换日期',
                dataIndex: 'arfdate',
                width: 100,
                sortable: false
            },
            {
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.ST(values.b1sbtime,values.b1xbtime)]}',
                    {
                        ST: function (s, x) {
                            if (!s && !x) {
                                return '';
                            } else {
                                if (!s) {
                                    s = '<span style="color:red">未打卡</span>'
                                }
                                if (!x) {
                                    x = '<span style="color:red">未打卡</span>'
                                }
                                return Ext.String.format('{0}-{1}', s, x);
                            }
                        }
                    }
                ),
                text: '班1打卡时间',
                dataIndex: 'b1sbtime',
                width: 110,
                sortable: false
            },
            {
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.ST(values.b2sbtime,values.b2xbtime)]}',
                    {
                        ST: function (s, x) {
                            if (!s && !x) {
                                return '';
                            } else {
                                if (!s) {
                                    s = '<span style="color:red">未打卡</span>'
                                }
                                if (!x) {
                                    x = '<span style="color:red">未打卡</span>'
                                }
                                return Ext.String.format('{0}-{1}', s, x);
                            }
                        }
                    }
                ),
                text: '班2打卡时间',
                dataIndex: 'b2sbtime',
                width: 110,
                sortable: false
            },
            {
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.ST(values.b3sbtime,values.b3xbtime)]}',
                    {
                        ST: function (s, x) {
                            if (!s && !x) {
                                return '';
                            } else {
                                if (!s) {
                                    s = '<span style="color:red">未打卡</span>'
                                }
                                if (!x) {
                                    x = '<span style="color:red">未打卡</span>'
                                }
                                return Ext.String.format('{0}-{1}', s, x);
                            }
                        }
                    }
                ),
                text: '班3打卡时间',
                dataIndex: 'b3sbtime',
                width: 110,
                sortable: false
            },
            {
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.ST(values.b4sbtime,values.b4xbtime)]}',
                    {
                        ST: function (s, x) {
                            if (!s && !x) {
                                return '';
                            } else {
                                if (!s) {
                                    s = '<span style="color:red">未打卡</span>'
                                }
                                if (!x) {
                                    x = '<span style="color:red">未打卡</span>'
                                }
                                return Ext.String.format('{0}-{1}', s, x);
                            }
                        }
                    }
                ),
                text: '班4打卡时间',
                dataIndex: 'b4sbtime',
                width: 110,
                sortable: false
            },
            {
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.ST(values.b11sbtime,values.b11xbtime)]}',
                    {
                        ST: function (s, x) {
                            if (!s && !x) {
                                return '';
                            } else {
                                if (!s) {
                                    s = '<span style="color:red">未打卡</span>'
                                }
                                if (!x) {
                                    x = '<span style="color:red">未打卡</span>'
                                }
                                return Ext.String.format('{0}-{1}', s, x);
                            }
                        }
                    }
                ),
                text: '加班1打卡时间',
                dataIndex: 'b11sbtime',
                width: 130,
                sortable: false
            },
            {
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.ST(values.b12sbtime,values.b12xbtime)]}',
                    {
                        ST: function (s, x) {
                            if (!s && !x) {
                                return '';
                            } else {
                                if (!s) {
                                    s = '<span style="color:red">未打卡</span>'
                                }
                                if (!x) {
                                    x = '<span style="color:red">未打卡</span>'
                                }
                                return Ext.String.format('{0}-{1}', s, x);
                            }
                        }
                    }
                ),
                text: '加班2打卡时间',
                dataIndex: 'b12sbtime',
                width: 130,
                sortable: false
            },
            {
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.ST(values.b13sbtime,values.b13xbtime)]}',
                    {
                        ST: function (s, x) {
                            if (!s && !x) {
                                return '';
                            } else {
                                if (!s) {
                                    s = '<span style="color:red">未打卡</span>'
                                }
                                if (!x) {
                                    x = '<span style="color:red">未打卡</span>'
                                }
                                return Ext.String.format('{0}-{1}', s, x);
                            }
                        }
                    }
                ),
                text: '加班3打卡时间',
                dataIndex: 'b13sbtime',
                width: 130,
                sortable: false
            },
            {
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.ST(values.b14sbtime,values.b14xbtime)]}',
                    {
                        ST: function (s, x) {
                            if (!s && !x) {
                                return '';
                            } else {
                                if (!s) {
                                    s = '<span style="color:red">未打卡</span>'
                                }
                                if (!x) {
                                    x = '<span style="color:red">未打卡</span>'
                                }
                                return Ext.String.format('{0}-{1}', s, x);
                            }
                        }
                    }
                ),
                text: '加班4打卡时间',
                dataIndex: 'b14sbtime',
                width: 130,
                sortable: false
            },
            {
                text: '总加班',
                dataIndex: 'rcjball',
                width: 85,
                sortable: false,
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.convert(values.rcjball)]}',
                    {
                        convert: function (v) {
                            if (v == null) {
                                return '';
                            } else {
                                return Ext.String.format('{0}分钟', v);
                            }
                        }
                    }
                )
            },
            {
                xtype: 'templatecolumn',
                text: '应出勤分钟',
                dataIndex: 'ycqsj',
                width: 100,
                sortable: false,
                tpl: new Ext.XTemplate(
                    '{[this.convert(values.ycqsj)]}',
                    {
                        convert: function (v) {
                            if (v == null) {
                                return '';
                            } else {
                                return Ext.String.format('{0}分钟', v);
                            }
                        }
                    }
                )
            },
            {
                text: '实际出勤',
                dataIndex: 'sjcqsj',
                width: 100,
                sortable: false,
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.convert(values.sjcqsj)]}',
                    {
                        convert: function (v) {
                            if (v == null) {
                                return '';
                            } else {
                                return Ext.String.format('{0}分钟', v);
                            }
                        }
                    }
                )
            },
            {
                text: '应出勤小时',
                dataIndex: 'ycqxs',
                width: 100,
                sortable: false,
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.convert(values.ycqxs)]}',
                    {
                        convert: function (v) {
                            if (v == null) {
                                return '';
                            } else {
                                return Ext.String.format('{0}小时', v);
                            }
                        }
                    }
                )
            },
            {
                text: '实际出勤小时',
                dataIndex: 'sjcqxs',
                width: 100,
                sortable: false,
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.convert(values.sjcqxs)]}',
                    {
                        convert: function (v) {
                            if (v == null) {
                                return '';
                            } else {
                                return Ext.String.format('{0}小时', v);
                            }
                        }
                    }
                )
            },
            {
                text: '应出勤天数',
                dataIndex: 'ycqts',
                width: 100,
                sortable: false,
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.convert(values.ycqts)]}',
                    {
                        convert: function (v) {
                            if (v == null) {
                                return '';
                            } else {
                                return Ext.String.format('{0}天', v);
                            }
                        }
                    }
                )
            },
            {
                text: '实际出勤天数',
                dataIndex: 'sjcqts',
                width: 100,
                sortable: false,
                xtype: 'templatecolumn',
                tpl: new Ext.XTemplate(
                    '{[this.convert(values.sjcqts)]}',
                    {
                        convert: function (v) {
                            if (v == null) {
                                return '';
                            } else {
                                return Ext.String.format('{0}天', v);
                            }
                        }
                    }
                )
            },
            {
                text: '迟到次数',
                dataIndex: 'chidaocs',
                width: 85,
                sortable: false,
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    if (!value) {
                        value = 0;
                    }
                    if (value > 0) {
                        metaData.style += 'color:red;';
                    }
                    return value;
                }
            },
            {
                text: '迟到时长',
                dataIndex: 'chidaosj',
                width: 85,
                sortable: false
            },
            {
                text: '早退次数',
                dataIndex: 'zaotuics',
                width: 100,
                sortable: false,
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    if (!value) {
                        value = 0;
                    }
                    if (value > 0) {
                        metaData.style += 'color:red;';
                    }
                    return value;
                }
            },
            {
                text: '早退分钟',
                dataIndex: 'zaotuisj',
                width: 100,
                sortable: false
            },
            {
                text: '旷工次数',
                dataIndex: 'kgcs',
                width: 85,
                sortable: false
            },
            {
                text: '旷工分钟',
                dataIndex: 'kgsj',
                width: 85,
                sortable: false
            },
            {
                text: '旷工小时',
                dataIndex: 'kgxs',
                width: 85,
                sortable: false
            },
            {
                text: '旷工天数',
                dataIndex: 'kgts',
                width: 85,
                sortable: false
            },
            {
                text: '加班旷工次数',
                dataIndex: 'jbkgcs',
                width: 100,
                sortable: false
            },
            {
                text: '加班旷工时间',
                dataIndex: 'jbkgsj',
                width: 100,
                sortable: false
            },
            {
                text: '迟到旷工次数',
                dataIndex: 'kgcs_cd',
                width: 100,
                sortable: false
            },
            {
                text: '迟到旷工时间',
                dataIndex: 'kgsj_cd',
                width: 100,
                sortable: false
            },
            {
                text: '早退旷工次数',
                dataIndex: 'kgcs_zt',
                width: 100,
                sortable: false
            },
            {
                text: '早退旷工时间',
                dataIndex: 'kgsj_zt',
                width: 100,
                sortable: false
            },
            {
                text: '班1迟到时间',
                dataIndex: 'b1chidaosj',
                width: 100,
                sortable: false
            },
            {
                text: '班2迟到时间',
                dataIndex: 'b2chidaosj',
                width: 100,
                sortable: false
            },
            {
                text: '班3迟到时间',
                dataIndex: 'b3chidaosj',
                width: 100,
                sortable: false
            },
            {
                text: '班4迟到时间',
                dataIndex: 'b4chidaosj',
                width: 100,
                sortable: false
            },
            {
                text: '班1早退时间',
                dataIndex: 'b1zaotuisj',
                width: 100,
                sortable: false
            },
            {
                text: '班2早退时间',
                dataIndex: 'b2zaotuisj',
                width: 100,
                sortable: false
            },
            {
                text: '班3早退时间',
                dataIndex: 'b3zaotuisj',
                width: 100,
                sortable: false
            },
            {
                text: '班4早退时间',
                dataIndex: 'b4zaotuisj',
                width: 100,
                sortable: false
            },
            {
                text: '平时加班',
                dataIndex: 'psjbxs',
                width: 80,
                sortable: false,
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    if (!value) {
                        return '';
                    }
                    return Ext.String.format('{0}小时', value);
                }
            },
            {
                text: '周末加班',
                dataIndex: 'zmjbxs',
                width: 80,
                sortable: false,
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    if (!value) {
                        return '';
                    }
                    return Ext.String.format('{0}小时', value);
                }
            },
            {
                text: '节日加班',
                dataIndex: 'jrjbxs',
                width: 80,
                sortable: false,
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    if (!value) {
                        return '';
                    }
                    return Ext.String.format('{0}小时', value);
                }
            }
        ];
        Ext.Ajax.request({
            url: '/SysDictionary/getWidgetDictionaryList',
            params: {
                groupcode: "SYS0000035"
            },
            method: 'POST',
            async: false,
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    Ext.each(result.data, function (item, i) {
                        var qqcolumns = [];
                        var qqtscolumns = [];
                        var qqxscolumns = [];
                        var width = 85;
                        if (item.name.length == 3) {
                            width = 95;
                        } else if (item.name.length == 4) {
                            width = 105;
                        } else if (item.name.length > 4) {
                            width = 130;
                        }
                        qqcolumns.push({
                            text: item.name + '分钟',
                            dataIndex: 'qq' + item.code,
                            width: width,
                            sortable: false
                        });
                        qqtscolumns.push({
                            text: item.name + '天数',
                            dataIndex: 'qq' + item.code + 'ts',
                            width: width,
                            sortable: false
                        });
                        qqxscolumns.push({
                            text: item.name + '小时',
                            dataIndex: 'qq' + item.code + 'xs',
                            width: width,
                            sortable: false
                        });
                        gridcolumns.push(qqcolumns[0]);
                        gridcolumns.push(qqxscolumns[0]);
                        gridcolumns.push(qqtscolumns[0]);
                    });
                }
            }
        });

        me.store = Ext.create('CamPus.view.time.dailyreport.DailyReportStore', {

        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'datefield',
                itemId: 'startdate',
                width: 130,
                value: new Date(Ext.Date.format(new Date(), 'Y-m-01')),
                format: 'Y-m-d',
                listeners: {
                    change: function (field) {
                        me.store.loadPage(1);
                    }
                }
            },
            {
                xtype: 'datefield',
                fieldLabel: '至',
                itemId: 'enddate',
                labelWidth: 20,
                width: 160,
                labelSeparator: '',
                value: new Date(Ext.Date.format(new Date(), 'Y-m-d')),
                format: 'Y-m-d',
                listeners: {
                    change: function (field) {
                        me.store.loadPage(1);
                    }
                }
            },
            {
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: window.applang.get("infocode") + '、姓名'
            }, '->',
            {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.store.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            multiColumnSort: true,
            itemId: 'dailyReportList',
            border: false,
            collapsible: false,
            region: 'center',
            rowLines: true,
            columnLines: true,
            store: me.store,
            tbar: tbar,
            viewConfig: {
                enableTextSelection: true
            },
            getExtraParams: function (grid) {
                var startdate = grid.down('datefield[itemId=startdate]');
                var enddate = grid.down('datefield[itemId=enddate]');
                return {
                    startdate: Ext.Date.format(startdate.getValue(), 'Y-m-d'),
                    enddate: Ext.Date.format(enddate.getValue(), 'Y-m-d')
                };
            },
            columns: gridcolumns,
            minWidth:600,
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.tree,me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});


Ext.define('CamPus.view.time.dailyreport.analysisWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择人员或选择' + window.applang.get("orgname2"),
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.time.dailyreport.OrgTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get("orgname3") + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            columnLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function (tree, record, index, eOpts) {
                    me.store.loadPage(1);
                }
            }
        });

        me.store = Ext.create('CamPus.view.time.dailyreport.TechInfoStore', {
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });

                    Ext.apply(store.proxy.extraParams, {
                        orgcode: orgcode,
                        intoyear: (!intoyear.getValue() ? '0' : intoyear.getValue()),
                        infotype: infotype.getValue(),
                        viewchild: viewchild.getValue(),
                        selecteduid: JSON.stringify(selecteduid)
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'infotype',
                groupCode: 'SYS0000019',
                insertAll: '0|全部',
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                        var url = '/Card/OrgFramework/getCollegeClassTree';
                        if (newValue == '2') {
                            intoyear.show();
                        } else {
                            intoyear.setValue(0);
                            intoyear.hide();
                            url = '/Card/OrgFramework/getOrgTree';
                        }
                        me.treestore.proxy.url = url;
                        me.treestore.load();
                        me.store.load();
                    }
                }
            }, {
                xtype: 'comboxdictionary',
                itemId: 'intoyear',
                groupCode: 'SYS0000004',
                insertAll: '全部',
                width: 110,
                hidden: true,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            }, {
                xtype: 'searchfield',
                itemId: 'searchKey',
                width: 150,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '关键词...'
            },
            {
                xtype: 'button',
                itemId: 'readCard',
                text: '读卡',
                iconCls: 'x-fa fa-microchip',
                hidden: CamPus.AppSession.cardtype == 'id',
                handler: function () {
                    window.onReadCard(function (identid, CardNO) {
                        var searchKey = me.down('searchfield[itemId=searchKey]');
                        searchKey.setValue(CardNO);
                        searchKey.onSearchClick();
                    });
                }
            },
            {
                xtype: 'checkboxfield',
                boxLabel: '显示子节点人员',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }]
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选人员：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.store.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['card', 'cardsn', 'name', 'infotype', 'uid', 'code', 'orgname'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    minWidth: 200,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 220
        });

        Ext.apply(me, {
            items: [me.tree, me.grid, me.selectgrid]
        });
        me.callParent(arguments);
        me.timeAnalysis(me, config);
    },
    timeAnalysis: function (me, config) {
        var userid = CamPus.AppSession.userid;
        var topic = 'timeAnalysis';
        me.ws = window.wssclient(userid, topic, function (ws, result) {
            Ext.MessageBox.hide();
            var msg = Ext.JSON.decode(result.data);
            if (msg.end === 'success') {
                config.analysisgrid.getStore().reload();
                toast("分析完成！！");
                ws.close();
            } else if (msg.end === 'false') {
                toast(msg.errorMsg);
                ws.close();
            }
        });
        //优化中.不一定成功
        me.ws.close();
    },
    Save: function () {
        var me = this;
        var records = me.selectgrid.getStore().getData().items;
        if (records.length == 0 && me.tree.getSelection().length == 0) {
            toast('请选择人员信息拖拽至右侧表格 或 只选择' + window.applang.get("orgname2"));
            return;
        }
        var infoids = [];
        Ext.each(records, function (record, index) {
            infoids.push(record.get('uid'));
        });
        var orgcode = null;
        var ischildorg = me.down('checkboxfield[itemId=ischildorg]').getValue() ? 1 : 0;
        if (infoids.length > 0) {
            if (ischildorg) {
                toast('选择人员时，不需要勾选包含' + window.applang.get("orgname2"));
                return;
            }
        } else {
            orgcode = me.selectgrid.getSelection()[0].get('orgcode');
        }
        Ext.get('loadingToast').show();
        Ext.Ajax.request({
            url: '/time/DailyReport/analysisTimeDaily',
            params: {
                orgcode: orgcode,
                infoid: infoids.join(','),
                ischildorg: ischildorg,
                startdate: Ext.Date.format(me.down('datefield[itemId=startdate]').getValue(), 'Y-m-d'),
                enddate: Ext.Date.format(me.down('datefield[itemId=enddate]').getValue(), 'Y-m-d')
            },
            method: 'POST',
            success: function (response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    toast(result.msg);
                    me.close();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});

Ext.define('CamPus.view.time.dailyreport.AbsenceWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '缺勤登记',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: false,
        split: true
    },
    maximizable: false,
    height: 400,
    width: 800,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            region: 'west',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['name', 'uid', 'code', 'orgname'],
                data: config.data
            }),
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'infocode',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'infoname',
                    width: 100,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            width: 450
        });


        me.formpanel = Ext.create('Ext.panel.Panel', {
            region: 'center',
            collapsible: false,
            bodyPadding: '10',
            layout: {
                type: 'vbox',
                pack: 'middle',
                align: 'stretch'
            },
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 60,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    itemId: 'uid',
                    name: 'uid',
                    allowBlank: false
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid',
                    allowBlank: false
                }, {
                    xtype: 'datetimefield',
                    itemId: 'starttime',
                    name: 'starttime',
                    fieldLabel: '开始时间',
                    allowBlank: false,
                    format: 'Y-m-d H:i',
                    listeners: {
                        change: function (field, newValue, oldValue, eOpts) {
                            me.down('combobox[itemId=schemeid]').setValue('');
                            me.down('combobox[itemId=schemeid]').getStore().reload();
                        }
                    }
                }, {
                    xtype: 'datetimefield',
                    itemId: 'endtime',
                    name: 'endtime',
                    fieldLabel: '结束时间',
                    allowBlank: false,
                    format: 'Y-m-d H:i',
                    listeners: {
                        change: function (field, newValue, oldValue, eOpts) {
                            me.down('combobox[itemId=schemeid]').setValue('');
                            me.down('combobox[itemId=schemeid]').getStore().reload();
                        }
                    }
                },
                {
                    xtype: 'combobox',
                    itemId: 'schemeid',
                    name: 'schemeid',
                    fieldLabel: '指定班制',
                    allowBlank: true,
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'uid',
                    editable: false,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['uid', 'name'],
                        data: [],
                        proxy: {
                            type: 'ajax',
                            url: '/time/Absence/getAbsenceGroupScheme',
                            actionMethods: {
                                read: 'POST'
                            },
                            reader: {
                                type: 'json',
                                rootProperty: 'data',
                                totalProperty: 'total',
                                successProperty: 'success',
                                messageProperty: 'msg'
                            }
                        },
                        listeners: {
                            load: function (store, records, successful, operation, eOpts) {
                                if (!successful) {
                                    toast(operation.error);
                                    me.allowSubmit = false;
                                    me.disallowText = operation.error;
                                    store.setData([]);
                                } else {
                                    me.allowSubmit = true;
                                    me.disallowText = '';
                                    if (me.schemeid && records.length > 0) {
                                        me.down('combobox[itemId=schemeid]').setValue(me.schemeid);
                                    }
                                }
                            },
                            beforeload: function (store, operation, eOpts) {
                                var starttime = me.down('datetimefield[itemId=starttime]');
                                var endtime = me.down('datetimefield[itemId=endtime]');
                                if (!starttime.getValue() || !endtime.getValue()) {
                                    return false;
                                }
                                Ext.apply(store.proxy.extraParams, {
                                    startdate: Ext.Date.format(starttime.getValue(), 'Y-m-d'),
                                    enddate: Ext.Date.format(endtime.getValue(), 'Y-m-d'),
                                    infoid: me.data.infoid
                                });
                                me.down('combobox[itemId=schemeid]').setValue('');
                            }
                        }
                    })
                },
                {
                    xtype: 'datetimefield',
                    itemId: 'realendtime',
                    name: 'realendtime',
                    fieldLabel: '实际结算时间',
                    hidden: true,
                    format: 'Y-m-d H:i'
                }, {
                    xtype: 'comboxdictionary',
                    itemId: 'absencetype',
                    name: 'absencetype',
                    fieldLabel: '缺勤类型',
                    groupCode: 'SYS0000035',
                    allowBlank: false,
                    defaultSelectFirst: true
                }, {
                    xtype: 'radiogroup',
                    fieldLabel: '审核状态',
                    itemId: 'auditing',
                    items: [
                        { name: 'auditing', boxLabel: '已审核', inputValue: 1, checked: true },
                        { name: 'auditing', boxLabel: '未审核', inputValue: 2 }
                    ]
                }, {
                    xtype: 'radiogroup',
                    fieldLabel: '免打卡',
                    itemId: 'qqifdk',
                    items: [
                        { name: 'qqifdk', boxLabel: '是', inputValue: 1 },
                        { name: 'qqifdk', boxLabel: '否', inputValue: 0, checked: true }
                    ]
                },
                {
                    itemId: 'remark',
                    name: 'remark',
                    fieldLabel: '备注',
                    allowBlank: true
                }]
            }]
        });

        Ext.apply(me, {
            allowSubmit: true,
            disallowText: '',
            items: [me.selectgrid, me.formpanel]
        });
        me.callParent(arguments);

    },
    Save: function () {
        var me = this;
        var starttime = me.down('datetimefield[itemId=starttime]').getValue();
        var endtime = me.down('datetimefield[itemId=endtime]').getValue();
        var realendtime = me.down('datetimefield[itemId=realendtime]').getValue();
        starttime = new Date(Date.parse(starttime));
        endtime = new Date(Date.parse(endtime));
        realendtime = new Date(Date.parse(realendtime));
        if (starttime > endtime) {
            toast('开始时间不能大于结束时间');
            return;
        }
        if (realendtime < starttime) {
            toast('实际结束时间不能小于开始时间');
            return;
        }
        var form = me.down('form[itemId=form]');
        var infoid = [];
        Ext.each(me.selectgrid.getStore().getData().items, function (item, index) {
            infoid.push(item.get('infoid'));
        });
        form.down('hiddenfield[itemId=infoid]').setValue(infoid.join(','));

        if (form.getForm().isValid()) {
            form.submit({
                url: '/time/DailyReport/saveAbsence',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('保存成功！');
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.time.dailyreport.SigncardWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '签卡登记',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: false,
        split: true
    },
    maximizable: false,
    height: 400,
    width: 800,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            region: 'west',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['name', 'uid', 'code', 'orgname'],
                data: config.data
            }),
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'infocode',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'infoname',
                    width: 100,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            width: 450
        });


        me.formpanel = Ext.create('Ext.panel.Panel', {
            region: 'center',
            collapsible: false,
            bodyPadding: '10',
            layout: {
                type: 'vbox',
                pack: 'start',
                align: 'stretch'
            },
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                flex: 1,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 60,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'uid',
                    allowBlank: false
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'infoid',
                    name: 'infoid',
                    allowBlank: false
                }, {
                    xtype: 'hiddenfield',
                    itemId: 'banzhi',
                    name: 'banzhi',
                    allowBlank: false
                }, {
                    xtype: 'fieldcontainer',
                    bodyPadding: 10,
                    layout: {
                        type: 'hbox',
                        pack: 'middle',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'checkboxfield',
                            boxLabel: '上班',
                            itemId: 'gotowork',
                            name: 'gotowork',
                            checked: true,
                            margin: '0 20 0 0',
                            listeners: {
                                change: function (result, newValue, oldValue, eOpts) {
                                    var cardindex = me.down('textfield[itemId=cardindex]').getValue();
                                    if (newValue) {
                                        if (me.down('checkboxfield[itemId=gooffwork]').getValue()) {
                                            me.down('textfield[itemId=cardindextext]').setValue(cardindex + "," + (parseInt(cardindex) + 1));
                                        } else {
                                            me.down('textfield[itemId=cardindextext]').setValue(cardindex);
                                        }
                                    } else {
                                        if (me.down('checkboxfield[itemId=gooffwork]').getValue()) {
                                            me.down('textfield[itemId=cardindextext]').setValue(parseInt(cardindex) + 1);
                                        } else {
                                            me.down('textfield[itemId=cardindextext]').setValue('');
                                        }
                                    }
                                }
                            }
                        }, {
                            xtype: 'checkboxfield',
                            boxLabel: '下班',
                            itemId: 'gooffwork',
                            name: 'gooffwork',
                            checked: false,
                            listeners: {
                                change: function (result, newValue, oldValue, eOpts) {
                                    if (newValue) {
                                        var cardindex = me.down('textfield[itemId=cardindex]').getValue();
                                        if (me.down('checkboxfield[itemId=gotowork]').getValue()) {
                                            me.down('textfield[itemId=cardindextext]').setValue(cardindex + "," + (parseInt(cardindex) + 1));
                                        } else {
                                            me.down('textfield[itemId=cardindextext]').setValue((parseInt(cardindex) + 1));
                                        }
                                    } else {
                                        if (me.down('checkboxfield[itemId=gotowork]').getValue()) {
                                            var cardindex = me.down('textfield[itemId=cardindextext]').getValue().split(',');
                                            me.down('textfield[itemId=cardindextext]').setValue(cardindex[0]);
                                        } else {
                                            me.down('textfield[itemId=cardindextext]').setValue('');
                                        }
                                    }
                                }
                            }
                        }
                    ]
                }, {
                    xtype: 'datetimefield',
                    itemId: 'signtime',
                    name: 'signtime',
                    fieldLabel: '签卡时间',
                    allowBlank: false,
                    readOnly: true,
                    format: 'Y-m-d'
                }, {
                    xtype: 'textfield',
                    name: 'cardindex',
                    itemId: 'cardindex',
                    hidden: true,
                    allowBlank: false
                }, {
                    xtype: 'textfield',
                    fieldLabel: '签卡次号',
                    name: 'cardindextext',
                    itemId: 'cardindextext',
                    readOnly: true,
                    submitValue: false,
                    allowBlank: false
                }, {
                    xtype: 'radiogroup',
                    fieldLabel: '审核状态',
                    itemId: 'auditing',
                    items: [
                        { name: 'auditing', boxLabel: '已审核', inputValue: 1, checked: true },
                        { name: 'auditing', boxLabel: '未审核', inputValue: 2 }
                    ]
                }, {
                    xtype: 'comboxdictionary',
                    itemId: 'remark',
                    name: 'remark',
                    fieldLabel: '备注',
                    groupCode: 'SYS0000036',
                    valueField: 'name',
                    editable: true,
                    defaultSelectFirst: false,
                    forceSelection: false,
                    allowBlank: false
                }]
            }]
        });

        Ext.apply(me, {
            items: [me.selectgrid, me.formpanel]
        });
        me.callParent(arguments);

    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        var infoid = [];
        if (!form.down('checkboxfield[itemId=gotowork]').getValue() && !form.down('checkboxfield[itemId=gooffwork]').getValue()) {
            toast('请选择上下班');
            return;
        }
        Ext.each(me.selectgrid.getStore().getData().items, function (item, index) {
            infoid.push(item.get('infoid'));
        });
        form.down('hiddenfield[itemId=infoid]').setValue(infoid.join(','));
        if (form.getForm().isValid()) {
            form.submit({
                url: '/time/DailyReport/saveSigncard',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('保存成功！');
                        if (me.parentWin) {
                            me.parentWin.close();
                        }
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});
