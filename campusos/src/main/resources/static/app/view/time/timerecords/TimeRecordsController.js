Ext.define('CamPus.view.time.timerecords.TimeRecordsController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.TimeRecordsController',
    view: ['TimeRecordsView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=hrexport]': {
                click: me.onHrexport
            },
            '*[itemId=export]': {
                click: me.onExport
            },
            '*[itemId=exporttxt]': {
                click: me.onExportTxt
            }
        });

        me.timeRecordsStore = me.view.timeRecordsStore;
        me.grid = me.view.grid;

        me.timeRecordsStore.on('beforeload', function (store) {
            var starttime = me.view.down('datetimefield[itemId=starttime]');
            var endtime = me.view.down('datetimefield[itemId=endtime]');
            Ext.apply(store.proxy.extraParams, {
                starttime: Ext.Date.format(starttime.getValue(), 'Y-m-d H:i:s'),
                endtime: Ext.Date.format(endtime.getValue(), 'Y-m-d H:i:s')
            });
        });
    },
    onHrexport:function(){
        var me = this;
        Ext.get('loadingToast').show();
        var extraParams = JSON.parse(JSON.stringify(me.grid.getStore().proxy.extraParams));//使用该方法进行深拷贝只能用于简单的数据对象
        var searchValue = me.grid.down('searchfield[paramName=key]').getValue();
        if(searchValue){
            extraParams.key = searchValue
        }
        Ext.Ajax.request({
            url: '/time/TimeRecords/ExportERPTimeRecords',
            params: extraParams,
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    onExport:function(){
        var me = this;
        Ext.get('loadingToast').show();
        var extraParams = JSON.parse(JSON.stringify(me.grid.getStore().proxy.extraParams));//使用该方法进行深拷贝只能用于简单的数据对象
        var searchValue = me.grid.down('searchfield[paramName=key]').getValue();
        if(searchValue){
            extraParams.key = searchValue
        }
        Ext.Ajax.request({
            url: '/time/TimeRecords/ExportTimeRecords',
            params: extraParams,
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    onExportTxt: function(){
        var me = this;
        Ext.get('loadingToast').show();
        var extraParams = JSON.parse(JSON.stringify(me.grid.getStore().proxy.extraParams));//使用该方法进行深拷贝只能用于简单的数据对象
        var searchValue = me.grid.down('searchfield[paramName=key]').getValue();
        if(searchValue){
            extraParams.key = searchValue
        }
        Ext.Ajax.request({
            url: '/time/TimeRecords/ExportTimeRecordsTxt',
            params: extraParams,
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    }
});