Ext.define('CamPus.view.time.ruleconfig.RuleconfigView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.RuleconfigView",
    controller: 'RuleconfigController',
    requires: [
        'CamPus.view.time.ruleconfig.RuleconfigStore',
        'CamPus.view.time.ruleconfig.RuleconfigController'
    ],
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.rulestore = Ext.create('CamPus.view.time.ruleconfig.RuleconfigStore', {

        });

        Ext.apply(config, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                scrollable: true,
                bodyPadding: 30,
                defaults: {
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'vbox',
                defaultType: 'textfield',
                items: [{
                    xtype: 'fieldcontainer',
                    layout: 'hbox',
                    defaults: {
                        labelWidth: 120,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'numberfield',
                        name: 'intervalminute',
                        fieldLabel: '两次打卡间隔<=',
                        labelSeparator: '',
                        allowBlank: false,
                        decimalPrecision: 0,
                        minValue: 0
                    }, {
                        xtype: 'label',
                        text: '分钟一次',
                        margin: '8 0 0 10',
                        width: 120
                    }]
                }, {
                    xtype: 'fieldcontainer',
                    layout: 'hbox',
                    defaults: {
                        labelWidth: 120,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'numberfield',
                        name: 'maxlateminute',
                        fieldLabel: '迟到时间<=',
                        labelSeparator: '',
                        allowBlank: false,
                        decimalPrecision: 0,
                        minValue: 0
                    }, {
                        xtype: 'label',
                        text: '分钟忽略不计',
                        margin: '8 0 0 10',
                        width: 120
                    },
                    {
                        xtype: 'numberfield',
                        name: 'maxlateabsence',
                        fieldLabel: '迟到时间>=',
                        labelSeparator: '',
                        allowBlank: false,
                        decimalPrecision: 0,
                        minValue: 0
                    }, 
                    {
                        xtype: 'label',
                        text: '分钟算旷工',
                        margin: '8 0 0 10',
                        width: 120
                    }
                    ]
                }, {
                    xtype: 'fieldcontainer',
                    layout: 'hbox',
                    defaults: {
                        labelWidth: 120,
                        labelAlign: 'right'
                    },
                    items: [
                        {
                            xtype: 'numberfield',
                            name: 'maxearlyleave',
                            fieldLabel: '早退时间<=',
                            labelSeparator: '',
                            allowBlank: false,
                            decimalPrecision: 0,
                            minValue: 0
                        }, {
                            xtype: 'label',
                            text: '分钟忽略不计',
                            margin: '8 0 0 10',
                            width: 120
                        }, {
                            xtype: 'numberfield',
                            name: 'maxleaveabsence',
                            fieldLabel: '早退时间>=',
                            labelSeparator: '',
                            allowBlank: false,
                            decimalPrecision: 0,
                            minValue: 0
                        }, {
                            xtype: 'label',
                            text: '分钟算旷工',
                            margin: '8 0 0 10',
                            width: 120
                        }]
                }, {
                    xtype: 'fieldcontainer',
                    layout: 'hbox',
                    defaults: {
                        labelWidth: 120,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'numberfield',
                        name: 'minround',
                        fieldLabel: '工时达到',
                        labelSeparator: '',
                        allowBlank: false,
                        decimalPrecision: 0,
                        minValue: 31,
                        maxValue: 60,
                        value: 60
                    }, {
                        xtype: 'label',
                        text: '分钟以上算1小时',
                        margin: '8 0 0 10',
                        width: 120
                    }]
                }, {
                    xtype: 'fieldcontainer',
                    layout: 'hbox',
                    defaults: {
                        labelWidth: 120,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'numberfield',
                        name: 'halfhourround',
                        fieldLabel: '工时达到',
                        labelSeparator: '',
                        allowBlank: false,
                        decimalPrecision: 0,
                        minValue: 1,
                        maxValue: 30,
                        value: 30
                    }, {
                        xtype: 'label',
                        text: '分钟以上算0.5小时',
                        margin: '8 0 0 10',
                        width: 120
                    }]
                }, {
                    xtype: 'fieldcontainer',
                    layout: 'hbox',
                    defaults: {
                        labelWidth: 120,
                        labelAlign: 'right'
                    },
                    hidden: true,
                    items: [{
                        xtype: 'numberfield',
                        name: 'maxfreelatetime',
                        fieldLabel: '考勤周期内最多免除',
                        labelSeparator: '',
                        allowBlank: false,
                        decimalPrecision: 0,
                        minValue: 0
                    }, {
                        xtype: 'label',
                        text: '次迟到',
                        margin: '8 0 0 10',
                        width: 120
                    }]
                }, {
                    xtype: 'fieldcontainer',
                    layout: 'hbox',
                    defaults: {
                        labelWidth: 120,
                        labelAlign: 'right'
                    },
                    hidden: true,
                    items: [{
                        xtype: 'numberfield',
                        name: 'maxfreelateminute',
                        fieldLabel: '考勤周期内最多免除',
                        labelSeparator: '',
                        allowBlank: false,
                        decimalPrecision: 0,
                        minValue: 0
                    }, {
                        xtype: 'label',
                        text: '分钟迟到',
                        margin: '8 0 0 10',
                        width: 120
                    }]
                }, {
                    xtype: 'fieldcontainer',
                    layout: 'hbox',
                    defaults: {
                        labelWidth: 120,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'radiogroup',
                        fieldLabel: '周六休息',
                        itemId: 'issaturdayrest',
                        width: 300,
                        items: [
                            { name: 'issaturdayrest', boxLabel: '是', inputValue: '1', checked: true },
                            { name: 'issaturdayrest', boxLabel: '否', inputValue: '0', margin: '0 0 0 20' }
                        ]
                    },
                    {
                        xtype: 'radiogroup',
                        fieldLabel: '周日休息',
                        itemId: 'issundayrest',
                        width: 300,
                        margin: '0 0 0 50',
                        items: [
                            { name: 'issundayrest', boxLabel: '是', inputValue: '1', checked: true },
                            { name: 'issundayrest', boxLabel: '否', inputValue: '0', margin: '0 0 0 20' }
                        ]
                    }]
                },
                {
                    xtype: 'fieldcontainer',
                    layout: 'hbox',
                    defaults: {
                        labelWidth: 120,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'numberfield',
                        name: 'maxlianxuworkday',
                        fieldLabel: '连续工作',
                        labelSeparator: '',
                        allowBlank: false,
                        decimalPrecision: 0,
                        minValue: 0,
                        maxValue: 365
                    },
                    {
                        xtype: 'label',
                        text: '天，禁止考勤打卡',
                        margin: '8 0 0 10',
                        width: 120
                    },
                    {
                        xtype: 'numberfield',
                        name: 'delayhourdistime',
                        fieldLabel: '延迟',
                        labelSeparator: '',
                        allowBlank: false,
                        decimalPrecision: 0,
                        minValue: 0,
                        maxValue: 24
                    }, {
                        xtype: 'label',
                        text: '小时考勤权限失效',
                        margin: '8 0 0 10',
                        width: 120
                    }]
                },
                {
                    xtype: 'fieldcontainer',
                    hideLabel: true,
                    layout: 'hbox',
                    margin: '25 0 0 0',
                    items: [{
                        xtype: 'label',
                        width: 150
                    }, {
                        xtype: 'button',
                        itemId: 'save',
                        iconCls: 'x-fa fa-floppy-o',
                        text: '保存设置'
                    }]
                }]
            }]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});