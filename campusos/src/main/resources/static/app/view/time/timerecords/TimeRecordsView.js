Ext.define('CamPus.view.time.timerecords.TimeRecordsView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.TimeRecordsView",
    controller: 'TimeRecordsController',
    requires: [
        'CamPus.view.time.timerecords.TimeRecordsStore',
        'CamPus.view.time.timerecords.TimeRecordsController'
    ],
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;
        me.timeRecordsStore = Ext.create('CamPus.view.time.timerecords.TimeRecordsStore', {

        });

        var timerecordstbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'datetimefield',
                itemId: 'starttime',
                width: 175,
                value: Ext.Date.format(new Date(), 'Y-m-d 00:00:00'),
                format: 'Y-m-d H:i:s',
                listeners: {
                    change: function (field) {
                        if(field.getValue() > me.down('datetimefield[itemId=endtime]').getValue()){
                            toast('开始日期请勿大与结束日期');
                            return;
                        }
                    }
                }
            },
            {
                xtype: 'datetimefield',
                width: 200,
                fieldLabel: '至',
                labelWidth: 20,
                labelSeparator: '',
                itemId: 'endtime',
                value: Ext.Date.format(new Date(), 'Y-m-d 23:59:59'),
                format: 'Y-m-d H:i:s',
                listeners: {
                    change: function (field) {
                        me.timeRecordsStore.reload();
                    }
                }
            },{
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.timeRecordsStore,
                emptyText: '姓名、' + window.applang.get("infocode") + '、卡号关键词...'
            }, '->', {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.timeRecordsStore.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            timerecordstbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            itemId: 'timerecordslist',
            region: 'center',
            tbar: timerecordstbar,
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.timeRecordsStore,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {
                    xtype: 'rownumberer'
                },
                {
                    text: '打卡时间',
                    dataIndex: 'recordTime',
                    width: 160,
                    minWidth: 160,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'cardsn',
                    width: 120,
                    sortable: false
                },
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 80,
                    sortable: false
                },
                {
                    text: '机号',
                    dataIndex: 'machineId',
                    width: 80,
                    sortable: true
                },
                {
                    text: '机具名称',
                    dataIndex: 'devName',
                    width: 120,
                    sortable: false
                },
                {
                    text: '打卡类型',
                    dataIndex: 'ftype',
                    width: 120,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 7) {
                            return '上班打卡';
                        } else if (value == 8) {
                            return '下班打卡';
                        }else {
                            return '基础打卡';
                        }
                    }
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    width: 140,
                    flex: 1,
                    sortable: false
                }
            ],
            flex: 1,
            selModel: 'checkboxmodel',
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });
        Ext.apply(config, {
            items: [me.grid]
        });
        Ext.apply(me, config);
        me.callParent(arguments);
    }
});
