Ext.ns("CamPus.view.main");

Ext.define('CamPus.view.main.DashboardView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.dashboardview",
    title: '系统桌面',
    iconCls: 'x-fa fa-home',
    itemId: 'CamPusHome',
    modelid: 'CamPus.Home',
    border: false,
    defauts: {
        border: false
    },
    requires: [
        'Ext.chart.CartesianChart',
        'Ext.chart.axis.Numeric',
        'Ext.chart.series.Line',
        'Ext.chart.interactions.PanZoom',
        'Ext.chart.axis.Category',
        'Ext.chart.interactions.ItemHighlight'
    ],
    controller: 'main',
    bodyPadding: 10,
    scrollable: true,
    defaults: {
        xtype: 'panel',
        layout: {
            type: 'fit',
            pack: 'center',
            align: 'center'
        },
        height: 400,
        width: 545,
        margin:'5 5 5 5'
    },
    constructor: function(config) {
        var me = this;
        var maxwidth = window.screen.width - 280;
        var columns = 3;
        if (maxwidth / 3 < 545) {
            columns = 2;
        }
        Ext.apply(me, {
            layout: 'column',
            columns: columns,
            items: []
        });
        Ext.apply(me, config);
        me.callParent(arguments);

        me.InitDesktop();
    },
    InitDesktop: function() {
        var me = this;
        if (CamPus.AppSession) {
            Ext.Ajax.request({
                url: '/UCenter/Desktop/GetDesktopInfo',
                params: {},
                method: 'POST',
                success: function(response) {
                    var result = Ext.JSON.decode(response.responseText);
                    if (result.success) {
                        var wssmsg = {
                            topic: 'authority',
                            msg: [
                            ]
                        };
                        Ext.each(result.data, function(item, index) {
                            switch (item.widgetuid) {
                                case 1:
                                    //人员+设备+消费
                                    me.CreateHomeConsume();
                                    me.CreateDormConsumetable();
                                    me.CreateConsumerChart();
                                    me.CreatePlaceConsumeChart();
                                    me.CreateAcrossChart();
                                    break;
                                case 2:
                                    //人员+设备+消费+请假
                                    me.CreateHomePage2();
                                    me.CreateHometable();
                                    me.CreateConsumerChart();
                                    me.CreateChart();
                                    me.CreateAcrossChart();
                                    break;
                                case 3:
                                    //人员+设备+消费+请假+宿舍+床位
                                    me.CreateHomePage2();
                                    me.CreateDormHometable();
                                    me.CreateConsumerChart();
                                    me.CreateChart();
                                    me.CreateAcrossChart();
                                    break;
                            }
                        });
                        if (result.data.length ===0){
                            me.CreateHomePage();
                            me.CreateHometable();
                            me.CreateAcrossChart25();
                            me.CreateAccessRecordChart();
                        }
                        var timestamp = new Date().getTime(); // 获取当前时间戳
                        var url = '/resources/js/homePage.js?timestamp=' + timestamp;
                        Ext.Loader.loadScript({url: url,});


                        if (result.data.length > 0) {
                            //启动服务状态监控
                            if (me.ws) {
                                me.ws.close();
                            }
                            me.ServiceStatusMonitor(function() {
                                me.ws.send(JSON.stringify(wssmsg));
                            });
                            //优化中.不一定成功
                            me.ws.close();
                        }
                    }
                }
            });
        } else {
            setTimeout(function() {
                me.InitDesktop();
            }, 1000);
        }
    },
    CreateService: function(suid, name, servicestatus, livestime, item) {
        var me = this;
        var status = '正常';
        var statuscolor = '#7bc309';
        if (!servicestatus) {
            status = '停止';
            statuscolor = 'red';
        }
        if (livestime) {
            livestime = Ext.Date.format(new Date(livestime), 'Y-m-d H:i:s');
        } else {
            livestime = '';
        }

        var service = Ext.create('Ext.panel.Panel', {
            //title: item.widgetname,
            widget: item,
            columnWidth: 1/3,
            border: false,
            layout: {
                type: 'vbox',
                pack: 'start',
                align: 'stretch'
            },
            dockedItems: [{
                xtype: 'panel',
                dock: 'top',
                html: '<h3 style="text-align: center;">'+item.widgetname+'</h3>'
            }],
            items: [{
                xtype: 'panel',
                layout: {
                    type: 'hbox',
                    pack: 'start',
                    align: 'stretch'
                },
                items: [{
                    xtype: 'panel',
                    flex:1
                },{
                    xtype: 'gauge',
                    itemId: 'cpu' + suid,
                    width: 160,
                    height: 160,
                    value: 0,
                    minValue: 0,
                    maxValue: 100,
                    animation: true,
                    textTpl: ['<tpl>系统CPU<br/>{value:number("0.00")}%</tpl>']
                }, {
                    xtype: 'gauge',
                    itemId: 'memory' + suid,
                    width: 160,
                    height: 160,
                    value: 0,
                    minValue: 0,
                    maxValue: 100,
                    animation: true,
                    textTpl: ['<tpl>系统内存<br/>{value:number("0.00")}%</tpl>']
                },
                    {
                        xtype: 'panel',
                        flex:1
                    }]
            },
                {
                    xtype: 'panel',
                    itemId: 'os' + suid,
                    html: ''
                },
                {
                    xtype: 'panel',
                    itemId: 'osname' + suid,
                    margin: '10 0 0 0',
                    html: ''
                },
                {
                    xtype: 'panel',
                    itemId: 'service' + suid,
                    margin: '10 0 0 0',
                    html: '<div style="text-align:center"><span style="font-weight:bold;margin-right:20px;">' + name + '</span>状态：<span style="color:' + statuscolor + ';">' + status + '</span>，最后心跳：' + livestime + '</div>'
                }
            ]
        });
        me.add(service);

        Ext.Ajax.request({
            url: '/UCenter/Desktop/GetServiceOsInfo',
            params: { appid: suid, serviceurl: item.serviceurl, angelport: item.angelport },
            method: 'POST',
            hideLoading:true,
            success: function(response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    var msg = result.data;
                    var item = me.down('gauge[itemId=cpu' + msg.suid + ']');
                    if (item) {
                        item.setValue(msg.cpurate * 100);
                    }
                    if (msg.usedPercent) {
                        item = me.down('gauge[itemId=memory' + msg.suid + ']');
                        if (item) {
                            item.setValue(msg.usedPercent);
                        }
                    }
                    var total = Math.round(msg.total / 1024 / 1024 / 1024);
                    if (!msg.cpunum) {
                        msg.cpunum = '--';
                    }
                    item = me.down('panel[itemId=os' + msg.suid + ']');
                    if (item) {
                        html = '<div style="text-align:center;"><span style="width:155px;display:inline-block;text-indent:0px;">CPU：' + msg.cpunum + '核</span><span style="width:175px;display:inline-block;text-indent:0px;">内存：' + total + 'GB</span></div>';
                        item.setHtml(html);
                    }
                    if (msg.cpuname) {
                        item = me.down('panel[itemId=osname' + msg.suid + ']');
                        if (item) {
                            html = '<div style="text-align:center">' + msg.cpuname + '</div>';
                            item.setHtml(html);
                        }
                    }
                }
            }
        });
    },
    CreateCardRecord: function(areacode, limit, item) {
        var me = this;

        me.cardrecordgrid = Ext.create('Ext.grid.Panel', {
            //title: item.widgetname,
            widget: item,
            columnWidth: 1/3,
            multiColumnSort: false,
            border: false,
            columnLines: false,
            viewConfig: {
                enableTextSelection: true
            },
            hideHeaders: true,
            selModel: 'rowmodel',
            store: Ext.create('Ext.data.Store', {
                storeId: 'simpsonsStore',
                fields: ['inouttype', 'doorname', 'recordtime', 'cardsn', 'usedname', 'code', 'infotype', 'orgname'],
                data: [],
                pageSize: limit
            }),
            dockedItems: [{
                xtype: 'panel',
                dock: 'top',
                html: '<h3 style="text-align: center;">'+item.widgetname+'</h3>'
            }],
            columns: [{
                xtype: 'actioncolumn',
                menuDisabled: true,
                sortable: false,
                align: 'center',
                width: 40,
                items: [{
                    iconCls: 'x-fa fa-cog',
                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                        if (r.get('inouttype') == 1) {
                            return 'door-into';
                        } else {
                            return 'door-exit';
                        }
                    }
                }]
            },
                {
                    text: '刷卡时间',
                    dataIndex: 'recordtime',
                    width: 160,
                    menuDisabled: true,
                    sortable: false,
                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '描述',
                    dataIndex: 'areaname',
                    flex: 1,
                    menuDisabled: true,
                    sortable: false,
                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                        var usedes=record.get("usedes");
                        var str = Ext.String.format('{0}{1}({2}){3}{4}{5}{6}',
                            record.get('orgname'),
                            record.get('infoname'),
                            record.get('infocode'),
                            (usedes=='本人'?'':usedes),
                            record.get('inoutname'),
                            record.get('areaname'),
                            record.get('doorname'));
                        return str;
                    }
                }
            ]
        });
        me.add(me.cardrecordgrid);
    },
    CreateBackHome: function(areacode, item) {
        var me = this;
        me.backhomepanel = Ext.create('Ext.panel.Panel', {
            //title: item.widgetname,
            widget: item,
            columnWidth: 1/3,
            border: false,
            bodyPadding: '30 0 0 0',
            layout: {
                type: 'table',
                columns: 2,
                tableAttrs: {
                    style: {
                        width: '100%'
                    }
                },
                tdAttrs: {
                    style: 'padding:10px;vertical-align:middle; width: 50%; text-align:center;'
                }
            },
            dockedItems: [{
                xtype: 'panel',
                dock: 'top',
                html: '<h3 style="text-align: center;">'+item.widgetname+'</h3>'
            }],
            items: [{
                xtype: 'label',
                text: '0',
                itemId: 'bedpersoncount',
                style: 'color:#7bc309 !important; font-size:36px !important;margin-top:30px;display: block;'
            },
                {
                    xtype: 'label',
                    text: '0',
                    itemId: 'backhomecount',
                    style: 'color:#7bc309 !important; font-size:36px !important;margin-top:30px;display: block;'
                },
                {
                    xtype: 'label',
                    text: '总人数'
                },
                {
                    xtype: 'label',
                    text: '已归寝'
                },
                {
                    xtype: 'label',
                    text: '0',
                    itemId: 'nobackcount',
                    style: 'color:red !important; font-size:36px !important;margin-top:30px;display: block;'
                },
                {
                    xtype: 'label',
                    text: '0%',
                    itemId: 'backrate',
                    style: 'color:#7bc309 !important; font-size:36px !important;margin-top:30px;display: block;'
                },
                {
                    xtype: 'label',
                    text: '未归寝'
                },
                {
                    xtype: 'label',
                    text: '归寝率'
                }
            ],
            RefreshData: function() {
                Ext.Ajax.request({
                    url: '/UCenter/Desktop/GetBackHomeInfo',
                    params: { areacode: areacode },
                    method: 'POST',
                    success: function(response) {
                        var result = Ext.JSON.decode(response.responseText);
                        if (result.success) {
                            var bedperson = me.down('label[itemId=bedpersoncount]');
                            if (bedperson) {
                                bedperson.setText(result.data.bedpersoncount);
                            }
                            var backhome = me.down('label[itemId=backhomecount]');
                            if (backhome) {
                                backhome.setText(result.data.backhomecount);
                            }
                            var noback = me.down('label[itemId=nobackcount]');
                            if (noback) {
                                noback.setText(result.data.nobackcount);
                            }

                            var backrate = me.down('label[itemId=backrate]');
                            if (backrate) {
                                var backratedata = 0;
                                if (result.data.bedpersoncount > 0) {
                                    backratedata = result.data.backhomecount / (result.data.bedpersoncount * 1.00)
                                }
                                backratedata = backratedata * 100;
                                backrate.setText(backratedata.toFixed(2).toString() + '%');
                            }
                            me.backhomepanel.IntervalTimes++;
                            if (me.backhomepanel.IntervalTimes >= 5) {
                                clearInterval(me.backhomepanel.Interval);
                                me.backhomepanel.isReFresh = false;
                            }
                        }
                    }
                });
            }
        });
        me.add(me.backhomepanel);
        me.backhomepanel.RefreshData();
    },
    CreateLeave: function (areacode, item){
        var me = this;
        me.leavedetail = Ext.create('KitchenSink.view.charts.column.Stacked', {
            extend: 'Ext.Panel',
            //title: item.widgetname,
            title: item.widgetname,
            style: {
                color: 'black !important', // Set the font color to blue
                fontWeight: 'bold !important'
            },
            widget: item,
            width: 525,
            columnWidth: 1/3,
            layout: 'center',
            items: [
                {
                xtype: 'cartesian',

                padding: 5,
                width: '100%',
                height: 355,
                reference: 'chart',
                store: {
                    type: 'browsers'
                },
                legend: {
                    type: 'sprite',
                    docked: 'bottom'
                },
                axes: [{
                    type: 'numeric',
                    position: 'left',
                    grid: true,
                    fields: ['daydata1'],
                    renderer: 'onAxisLabelRender',
                    minimum: 0
                }, {
                    type: 'category',
                    position: 'bottom',
                    grid: true,
                    fields: ['day'],
                    // label: {
                    //     rotate: {
                    //         degrees: -45
                    //     }
                    // }
                }],
                series: [{
                    type: 'bar',
                    title: ['初一', '初二', '初三'],
                    xField: 'day',
                    yField: ['daydata1', 'daydata2', 'daydata3'],
                    stacked: false,
                    style: {
                        opacity: 0.80
                    },
                    highlight: {
                        fillStyle: 'yellow'
                    },
                    tooltip: {
                        renderer: 'onBarTipRender'
                    },
                    label: {
                        display: 'insideEnd', // 设置显示位置为柱子内部末端
                        field: ['daydata1', 'daydata2', 'daydata3'], // 显示的字段
                        renderer: Ext.util.Format.numberRenderer('0'),// 格式化显示的内容
                        orientation: 'horizontal' // 设置标签方向为水平
                    }
                }]
            }]

        });
        me.add(me.leavedetail);
    },
    CreateConsumerChart: function (){
        var me = this;
        me.leavedetail = Ext.create('KitchenSink.view.charts.column.Stacked', {
            extend: 'Ext.Panel',
            title: '三日消费充值情况详情',
            style: {
                color: 'black !important', // Set the font color to blue
                fontWeight: 'bold !important'
            },
            width: 525,
            columnWidth: 1/3,
            layout: 'center',
            items: [
                {
                    xtype: 'panel',
                    width: '100%',
                    html: '<div id="main" style="width: 100%;height: 350px;background-color:#FFFF"></div>'
            }]

        });
        me.add(me.leavedetail);
    },
    CreateChart: function (){
        var me = this;
        me.chart = Ext.create('KitchenSink.view.charts.pie.Basic', {
            extend: 'Ext.panel.Panel',
            title: '今日请假审批情况比例',
            style: {
                color: 'black !important', // Set the font color to blue
                fontWeight: 'bold !important'
            },
            columnWidth: 1/3,
            layout: 'center',
            width: 525,
            items: [{
                xtype: 'panel',
                width: '100%',
                html: '<div id="chart" style="width: 100%;height: 350px;background-color:#FFFF"></div>'
            }]
            });
        me.add(me.chart);
    },
    CreatePlaceConsumeChart: function (){
        var me = this;
        me.chart = Ext.create('KitchenSink.view.charts.pie.Basic', {
            extend: 'Ext.panel.Panel',
            title: '今日场所消费情况比例',
            style: {
                color: 'black !important', // Set the font color to blue
                fontWeight: 'bold !important'
            },
            columnWidth: 1/3,
            layout: 'center',
            width: 525,
            items: [{
                xtype: 'panel',
                width: '100%',
                html: '<div id="consumechart" style="width: 100%;height: 350px;background-color:#FFFF"></div>'
            }]
            });
        me.add(me.chart);
    },
    CreateChart1: function (){
        var me = this;
        me.chart = Ext.create('KitchenSink.view.charts.pie.Basic', {
            extend: 'Ext.panel.Panel',
            title: '三日内年级请假详情',
            style: {
                color: 'black !important', // Set the font color to blue
                fontWeight: 'bold !important'
            },
            columnWidth: 1/3,
            layout: 'center',
            width: 525,
            items: [{
                xtype: 'panel',
                width: '100%',
                html: '<div id="class" style="width: 100%;height: 350px;background-color:#FFFF"></div>'
            }]
            });
        me.add(me.chart);
    },
    CreateAcrossChart: function (){
        var me = this;
        me.AcrossChart = Ext.create('KitchenSink.view.charts.bar.Basic', {
            extend: 'Ext.Panel',
            title: '设备类型数量统计',
            style: {
                color: 'black !important', // Set the font color to blue
                fontWeight: 'bold !important'
            },
            columnWidth: 1/3,
            layout: 'center',
            width: 525,
            items: [{
                xtype: 'panel',
                width: '100%',
                html: '<div id="acrossChart" style="width: 100%;height: 350px;background-color:#FFFF"></div>'
            }]
        });
        me.add(me.AcrossChart);
    },
    CreateAcrossChart25: function (){
        var me = this;
        me.AcrossChart = Ext.create('KitchenSink.view.charts.bar.Basic', {
            extend: 'Ext.Panel',
            title: '设备类型数量统计',
            style: {
                color: 'black !important', // Set the font color to blue
                fontWeight: 'bold !important'
            },
            columnWidth: 2/5,
            layout: 'center',
            width: 525,
            items: [{
                xtype: 'panel',
                width: '100%',
                html: '<div id="acrossChart" style="width: 100%;height: 350px;background-color:#FFFF"></div>'
            }]
        });
        me.add(me.AcrossChart);
    },
    CreateAccessRecordChart: function (){
        var me = this;
        me.AcrossChart = Ext.create('KitchenSink.view.charts.bar.Basic', {
            extend: 'Ext.Panel',
            title: '每日正常出入记录',
            style: {
                color: 'black !important', // Set the font color to blue
                fontWeight: 'bold !important'
            },
            columnWidth: 3/5,
            layout: 'center',
            width: 525,
            items: [{
                xtype: 'panel',
                width: '100%',
                html: '<div id="accessRecord" style="width: 100%;height: 350px;background-color:#FFFF"></div>'
            }]
        });
        me.add(me.AcrossChart);
    },
    //请假的全部
    CreateHomePageqingjia: function (areacode, item){
        var me = this;
        var htmlContent = Ext.create('Ext.XTemplate',
            '            <div class="grid grid-cols-4 gap-6">\n' +
            '                <!-- Card 1 -->\n' +
            '                <!-- First card -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">今日请假人数(个)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{0}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-green-100 rounded-full">\n' +
            '                            <i class="fas fa-wallet text-green-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-green-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">昨日人数：{1}</span>\n' +
            '                        <span class="text-sm text-white">同比增加：{2}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 2 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">审批通过人数(个)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{3}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-blue-100 rounded-full">\n' +
            '                            <i class="fas fa-users text-blue-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-blue-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">昨日人数：{4}</span>\n' +
            '                        <span class="text-sm text-white">同比增加：{5}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 3 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">待审批人数(个)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{6}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-yellow-100 rounded-full">\n' +
            '                            <i class="fas fa-user-plus text-yellow-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-yellow-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">昨日还剩：{7}</span>\n' +
            '                        <span class="text-sm text-white">明日已有：{8}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 4 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                        <h3 class="text-xl text-gray-500">其他人数(个)</h3>\n' +
            '                        <p class="text-3xl font-semibold text-gray-800">{9}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-purple-100 rounded-full">\n' +
            '                            <i class="fas fa-chart-bar text-purple-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-purple-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">已拒绝：{10}</span>\n' +
            '                        <span class="text-sm text-white">已取消：{11}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '            </div>'
        );
        var dynamicHtmlContent =null;
        Ext.Ajax.request({
            url: '/card/LeaveRecord/getLeaveHomePage',
            method: 'POST',
            async: false, // 设置为同步请求
            success: function(response) {
                var result = Ext.JSON.decode(response.responseText);
                dynamicHtmlContent = htmlContent.apply(result);
            }
        });

        me.HomePage = Ext.create('Ext.panel.Panel', {
            widget: item,
            height: 200,
            columnWidth: 1,
            //border: true,
            bodyPadding: '5 0 0 0',
            items: [{
                html: dynamicHtmlContent,
            }]

        });
        me.add(me.HomePage);
    },


    CreateHomePage: function (){
        var me = this;
        var htmlContent = Ext.create('Ext.XTemplate',
            '            <div class="grid grid-cols-4 gap-6">\n' +
            '                <!-- Card 1 -->\n' +
            '                <!-- First card -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">总人数(人)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{0}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-green-100 rounded-full">\n' +
            '                            <i class="fas fa-users text-green-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-green-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">已发卡：{1}</span>\n' +
            '                        <span class="text-sm text-white">已建模：{2}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 2 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">今日新增人数(个)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{3}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-blue-100 rounded-full">\n' +
            '                            <i class="fas fa-user-plus text-blue-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-blue-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">男：{4}</span>\n' +
            '                        <span class="text-sm text-white">女：{5}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 3 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">今日补卡数(张)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{6}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-yellow-100 rounded-full">\n' +
            '                            <i class="fas fa-wallet text-yellow-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-yellow-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">挂失：{7}</span>\n' +
            '                        <span class="text-sm text-white">解挂：{8}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 4 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                        <h3 class="text-xl text-gray-500">总设备数(台)</h3>\n' +
            '                        <p class="text-3xl font-semibold text-gray-800">{9}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-purple-100 rounded-full">\n' +
            '                            <i class="fas fa-chart-bar text-purple-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-purple-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">在线：{10}</span>\n' +
            '                        <span class="text-sm text-white">离线：{11}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '            </div>'
        );
        var dynamicHtmlContent =null;
        Ext.Ajax.request({
            url: '/card/LeaveRecord/getHomePageNull',
            method: 'POST',
            async: false, // 设置为同步请求
            success: function(response) {
                var result = Ext.JSON.decode(response.responseText);
                dynamicHtmlContent = htmlContent.apply(result);
            }
        });

        me.HomePage = Ext.create('Ext.panel.Panel', {
            height: 200,
            columnWidth: 1,
            //border: true,
            bodyPadding: '5 0 0 0',
            items: [{
                html: dynamicHtmlContent,
            }]

        });
        me.add(me.HomePage);
    },
    CreateHomeConsume: function (){
        var me = this;
        var htmlContent = Ext.create('Ext.XTemplate',
            '            <div class="grid grid-cols-4 gap-6">\n' +
            '                <!-- Card 1 -->\n' +
            '                <!-- First card -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">总人数(人)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{0}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-green-100 rounded-full">\n' +
            '                            <i class="fas fa-users text-green-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-green-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">已发卡：{1}</span>\n' +
            '                        <span class="text-sm text-white">已建模：{2}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 2 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">今日消费金额(元)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{3}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-blue-100 rounded-full">\n' +
            '                            <i class="fas fa-wallet text-blue-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-blue-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">消费次数：{4}</span>\n' +
            '                        <span class="text-sm text-white">消费人数：{5}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 3 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">今日充值金额(元)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{6}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-yellow-100 rounded-full">\n' +
            '                            <i class="fas fa-wallet text-yellow-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-yellow-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">充值次数：{7}</span>\n' +
            '                        <span class="text-sm text-white">充值人数：{8}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 4 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                        <h3 class="text-xl text-gray-500">总设备数(台)</h3>\n' +
            '                        <p class="text-3xl font-semibold text-gray-800">{9}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-purple-100 rounded-full">\n' +
            '                            <i class="fas fa-chart-bar text-purple-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-purple-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">在线：{10}</span>\n' +
            '                        <span class="text-sm text-white">离线：{11}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '            </div>'
        );
        var dynamicHtmlContent =null;
        Ext.Ajax.request({
            url: '/card/LeaveRecord/getHomePageConsume',
            method: 'POST',
            async: false, // 设置为同步请求
            success: function(response) {
                var result = Ext.JSON.decode(response.responseText);
                dynamicHtmlContent = htmlContent.apply(result);
            }
        });

        me.HomePage = Ext.create('Ext.panel.Panel', {
            height: 200,
            columnWidth: 1,
            //border: true,
            bodyPadding: '5 0 0 0',
            items: [{
                html: dynamicHtmlContent,
            }]

        });
        me.add(me.HomePage);
    },
    CreateHomePage2: function (){
        var me = this;
        var htmlContent = Ext.create('Ext.XTemplate',
            '            <div class="grid grid-cols-4 gap-6">\n' +
            '                <!-- Card 1 -->\n' +
            '                <!-- First card -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">总人数(人)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{0}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-green-100 rounded-full">\n' +
            '                            <i class="fas fa-users text-green-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-green-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">已发卡：{1}</span>\n' +
            '                        <span class="text-sm text-white">已建模：{2}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 2 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">今日消费金额(元)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{3}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-blue-100 rounded-full">\n' +
            '                            <i class="fas fa-wallet text-blue-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-blue-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">充值：{4}</span>\n' +
            '                        <span class="text-sm text-white">消费人数：{5}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 3 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                            <h3 class="text-xl text-gray-500">今日请假人数(人)</h3>\n' +
            '                            <p class="text-3xl font-semibold text-gray-800">{6}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-yellow-100 rounded-full">\n' +
            '                            <i class="fas fa-user-minus text-yellow-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-yellow-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">待审核：{7}</span>\n' +
            '                        <span class="text-sm text-white">已审核：{8}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '                <!-- Card 4 -->\n' +
            '                <div class="bg-white rounded-lg shadow-lg p-6 flex-1 relative">\n' +
            '                    <div class="flex items-center mb-4 justify-between">\n' +
            '                        <div class="flex flex-col items-center mb-4 justify-between">\n' +
            '                        <h3 class="text-xl text-gray-500">总设备数(台)</h3>\n' +
            '                        <p class="text-3xl font-semibold text-gray-800">{9}</p>\n' +
            '                        </div>\n' +
            '                        <div class="p-2 bg-purple-100 rounded-full">\n' +
            '                            <i class="fas fa-chart-bar text-purple-500 text-5xl"></i>\n' +
            '                        </div>\n' +
            '                    </div>\n' +
            '                    <div class="flex justify-between bg-purple-500 p-4 rounded-b-lg z-10 relative">\n' +
            '                        <span class="text-sm text-white">在线：{10}</span>\n' +
            '                        <span class="text-sm text-white">离线：{11}</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '            </div>'
        );
        var dynamicHtmlContent =null;
        Ext.Ajax.request({
            url: '/card/LeaveRecord/getHomePage',
            method: 'POST',
            async: false, // 设置为同步请求
            success: function(response) {
                var result = Ext.JSON.decode(response.responseText);
                dynamicHtmlContent = htmlContent.apply(result);
            }
        });

        me.HomePage = Ext.create('Ext.panel.Panel', {
            height: 200,
            columnWidth: 1,
            //border: true,
            bodyPadding: '5 0 0 0',
            items: [{
                html: dynamicHtmlContent,
            }]

        });
        me.add(me.HomePage);
    },

    CreateHometable: function (areacode, item){
        var me = this;
        var htmlContent = Ext.create('Ext.XTemplate',
            '<style>\n' +
            '    th {\n' +
            '      border-bottom: 1px dashed #afaaaa;\n' +
            '    }\n' +
            '    td:nth-child(3n), th:nth-child(3n) {\n' +
            '      border-right: 1px dashed #afaaaa;\n' +
            '    }\n' +
            '    td:last-child, th:last-child {\n' +
            '      border-right: none; /* Remove the right border for the last column */\n' +
            '    }\n' +
            '  </style>\n' +
            '<div style="text-align: center;background: #FFFFFF">\n' +
            '  <table style="width: 100%; height: 150px; border-collapse: collapse;">\n' +
            '    <tr>\n' +
            '      <th style="text-align: center; width: 138px;">发卡详情</th>\n' +
            '      <th></th>\n' +
            '      <th style="text-align: center; width: 138px;">{0}</th>\n' +
            '      <th style="text-align: center; width: 138px;">人脸建模详情</th>\n' +
            '      <th></th>\n' +
            '      <th style="text-align: center; width: 138px;">{1}</th>\n' +
            '      <th style="text-align: center; width: 138px;">人员情况</th>\n' +
            '      <th></th>\n' +
            '      <th style="text-align: center; width: 138px;">{2}</th>\n' +
            '      <th style="text-align: center; width: 138px;">数据中心</th>\n' +
            '      <th></th>\n' +
            '      <th style="text-align: center; width: 138px;">{3}</th>\n' +
            '    </tr>\n' +
            '    <tr>\n' +
            '      <td style="text-align: center; width: 138px;">发卡情况</td>\n' +
            '      <td style="text-align: center; width: 138px;">已发卡</td>\n' +
            '      <td style="text-align: center; width: 138px;">{4}</td>\n' +
            '      <td style="text-align: center; width: 138px;">建模情况</td>\n' +
            '      <td style="text-align: center; width: 138px;">已建模</td>\n' +
            '      <td style="text-align: center; width: 138px;">{5}</td>\n' +
            '      <td style="text-align: center; width: 138px;">男女详情</td>\n' +
            '      <td style="text-align: center; width: 138px;">男</td>\n' +
            '      <td style="text-align: center; width: 138px;">{6}</td>\n' +
            '      <td style="text-align: center; width: 138px;">基础数据</td>\n' +
            '      <td style="text-align: center; width: 138px;">机构部门</td>\n' +
            '      <td style="text-align: center; width: 138px;">{7}</td>\n' +
            '    </tr>\n' +
            '    <tr>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">未发卡</td>\n' +
            '      <td style="text-align: center; width: 138px;">{8}</td>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">未建模</td>\n' +
            '      <td style="text-align: center; width: 138px;">{9}</td>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">女</td>\n' +
            '      <td style="text-align: center; width: 138px;">{10}</td>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">区域房间</td>\n' +
            '      <td style="text-align: center; width: 138px;">{11}</td>\n' +
            '    </tr>\n' +
            '  </table>\n' +
            '</div>'
        );
        var dynamicHtmlContent =null;
        Ext.Ajax.request({
            url: '/card/LeaveRecord/getLeaveHomePage2',
            method: 'POST',
            async: false, // 设置为同步请求
            success: function(response) {
                var result = Ext.JSON.decode(response.responseText);
                dynamicHtmlContent = htmlContent.apply(result);
            }
        });
        me.Hometable = Ext.create('Ext.panel.Panel', {
            widget: item,
            height: 175,
            bodyStyle: 'background-color: #FFFFF;',
            columnWidth : 1,
            bodyPadding: '10 0 0 0',
            items: [{
                html: dynamicHtmlContent
            }]
        });
        me.add(me.Hometable);
    },

    CreateDormHometable: function (){
        var me = this;
        var htmlContent = Ext.create('Ext.XTemplate',
            '<style>\n' +
            '    th {\n' +
            '      border-bottom: 1px solid #afaaaa;\n' +
            '    }\n' +
            '    td:nth-child(3n), th:nth-child(2n) {\n' +
            '      border-right: 1px solid #afaaaa;\n' +
            '    }\n' +
            '    td:last-child, th:last-child {\n' +
            '      border-right: none; /* Remove the right border for the last column */\n' +
            '    }\n' +
            '  </style>\n' +
            '<div style="text-align: center;background: #FFFFFF">\n' +
            '  <table style="width: 100%; height: 150px; border-collapse: collapse;">\n' +
            '    <tr>\n' +
            '      <th style="text-align: left; " colspan="2"><i class="fas fa-chart-bar text-gray-500 text-xl" style="margin-right: 10px;margin-left: 15px;"></i>消费月比分析</th>\n' +
            '      <th style="text-align: center; width: 138px;"><span class="highlight">{0}</span></th>\n' +
            '      <th style="text-align: left; " colspan="2"><i class="fas fa-chart-bar text-gray-500 text-xl" style="margin-right: 10px;margin-left: 15px;"></i>日出勤率</th>\n' +
            '      <th style="text-align: center; width: 138px;"><span class="highlight">{1}</span></th>\n' +
            '      <th style="text-align: left; " colspan="2"><i class="fas fa-chart-bar text-gray-500 text-xl" style="margin-right: 10px;margin-left: 15px;"></i>床位入住率</th>\n' +
            '      <th style="text-align: center; width: 138px;"><span class="highlight">{2}</span></th>\n' +
            '      <th style="text-align: left;" colspan="2"><i class="fas fa-chart-bar text-gray-500 text-xl" style="margin-right: 10px;margin-left: 15px;"></i>宿舍归寝率</th>\n' +
            '      <th style="text-align: center; width: 138px;"><span class="highlight">{3}</span></th>\n' +
            '    </tr>\n' +
            '    <tr>\n' +
            '      <td style="text-align: center; width: 138px;">消费情况</td>\n' +
            '      <td style="text-align: center; width: 138px;">上月消费</td>\n' +
            '      <td style="text-align: center; width: 138px;">{4}</td>\n' +
            '      <td style="text-align: center; width: 138px;">请假情况</td>\n' +
            '      <td style="text-align: center; width: 138px;">本日请假人数</td>\n' +
            '      <td style="text-align: center; width: 138px;">{5}</td>\n' +
            '      <td style="text-align: center; width: 138px;">入住情况</td>\n' +
            '      <td style="text-align: center; width: 138px;">已入住床位</td>\n' +
            '      <td style="text-align: center; width: 138px;">{6}</td>\n' +
            '      <td style="text-align: center; width: 138px;">归寝情况</td>\n' +
            '      <td style="text-align: center; width: 138px;">昨日晚归</td>\n' +
            '      <td style="text-align: center; width: 138px;">{7}</td>\n' +
            '    </tr>\n' +
            '    <tr>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">本月消费</td>\n' +
            '      <td style="text-align: center; width: 138px;">{8}</td>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">总人数</td>\n' +
            '      <td style="text-align: center; width: 138px;">{9}</td>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">未入住床位</td>\n' +
            '      <td style="text-align: center; width: 138px;">{10}</td>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">昨日未归</td>\n' +
            '      <td style="text-align: center; width: 138px;">{11}</td>\n' +
            '    </tr>\n' +
            '  </table>\n' +
            '</div>'
        );
        var dynamicHtmlContent =null;
        Ext.Ajax.request({
            url: '/card/LeaveRecord/getHomePage2',
            method: 'POST',
            async: false, // 设置为同步请求
            success: function(response) {
                var result = Ext.JSON.decode(response.responseText);
                dynamicHtmlContent = htmlContent.apply(result);
            }
        });
        me.Hometable = Ext.create('Ext.panel.Panel', {
            height: 175,
            bodyStyle: 'background-color: #FFFFF;',
            columnWidth : 1,
            //border: true,
            bodyPadding: '10 0 0 0',
            items: [{
                html: dynamicHtmlContent
            }]
        });
        me.add(me.Hometable);
    },
    CreateDormConsumetable: function (){
        var me = this;
        var htmlContent = Ext.create('Ext.XTemplate',
            '<style>\n' +
            '    th {\n' +
            '      border-bottom: 1px solid #afaaaa;\n' +
            '    }\n' +
            '    td:nth-child(3n), th:nth-child(2n) {\n' +
            '      border-right: 1px solid #afaaaa;\n' +
            '    }\n' +
            '    td:last-child, th:last-child {\n' +
            '      border-right: none; /* Remove the right border for the last column */\n' +
            '    }\n' +
            '  </style>\n' +
            '<div style="text-align: center;background: #FFFFFF">\n' +
            '  <table style="width: 100%; height: 150px; border-collapse: collapse;">\n' +
            '    <tr>\n' +
            '      <th style="text-align: left; " colspan="2"><i class="fas fa-chart-bar text-gray-500 text-xl" style="margin-right: 10px;margin-left: 15px;"></i>消费月比分析</th>\n' +
            '      <th style="text-align: center; width: 138px;"><span class="highlight">{0}</span></th>\n' +
            '      <th style="text-align: left; " colspan="2"><i class="fas fa-chart-bar text-gray-500 text-xl" style="margin-right: 10px;margin-left: 15px;"></i>充值月比分析</th>\n' +
            '      <th style="text-align: center; width: 138px;"><span class="highlight">{1}</span></th>\n' +
            '      <th style="text-align: left; " colspan="2"><i class="fas fa-chart-bar text-gray-500 text-xl" style="margin-right: 10px;margin-left: 15px;"></i>今日退款金额</th>\n' +
            '      <th style="text-align: center; width: 138px;"><span class="highlight">{2}</span></th>\n' +
            '      <th style="text-align: left;" colspan="2"><i class="fas fa-chart-bar text-gray-500 text-xl" style="margin-right: 10px;margin-left: 15px;"></i>今日补卡数</th>\n' +
            '      <th style="text-align: center; width: 138px;"><span class="highlight">{3}</span></th>\n' +
            '    </tr>\n' +
            '    <tr>\n' +
            '      <td style="text-align: center; width: 138px;">消费情况</td>\n' +
            '      <td style="text-align: center; width: 138px;">上月消费</td>\n' +
            '      <td style="text-align: center; width: 138px;">{4}</td>\n' +
            '      <td style="text-align: center; width: 138px;">充值情况</td>\n' +
            '      <td style="text-align: center; width: 138px;">上月充值</td>\n' +
            '      <td style="text-align: center; width: 138px;">{5}</td>\n' +
            '      <td style="text-align: center; width: 138px;">退款情况</td>\n' +
            '      <td style="text-align: center; width: 138px;">上月退款</td>\n' +
            '      <td style="text-align: center; width: 138px;">{6}</td>\n' +
            '      <td style="text-align: center; width: 138px;">卡操作情况</td>\n' +
            '      <td style="text-align: center; width: 138px;">今日挂失</td>\n' +
            '      <td style="text-align: center; width: 138px;">{7}</td>\n' +
            '    </tr>\n' +
            '    <tr>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">本月消费</td>\n' +
            '      <td style="text-align: center; width: 138px;">{8}</td>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">本月充值</td>\n' +
            '      <td style="text-align: center; width: 138px;">{9}</td>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">本月退款</td>\n' +
            '      <td style="text-align: center; width: 138px;">{10}</td>\n' +
            '      <td style="text-align: center; width: 138px;"></td>\n' +
            '      <td style="text-align: center; width: 138px;">今日解挂</td>\n' +
            '      <td style="text-align: center; width: 138px;">{11}</td>\n' +
            '    </tr>\n' +
            '  </table>\n' +
            '</div>'
        );
        var dynamicHtmlContent =null;
        Ext.Ajax.request({
            url: '/card/LeaveRecord/getHomePageConsume2',
            method: 'POST',
            async: false, // 设置为同步请求
            success: function(response) {
                var result = Ext.JSON.decode(response.responseText);
                dynamicHtmlContent = htmlContent.apply(result);
            }
        });
        me.Hometable = Ext.create('Ext.panel.Panel', {
            height: 175,
            bodyStyle: 'background-color: #FFFFF;',
            columnWidth : 1,
            //border: true,
            bodyPadding: '10 0 0 0',
            items: [{
                html: dynamicHtmlContent
            }]
        });
        me.add(me.Hometable);
    },


    ServiceStatusMonitor: function(opencallback) {
        var me = this;
        var userid = CamPus.AppSession.userid;
        var topic = 'serviceosinfo';

        me.ws = window.wssclient(userid, topic, function(ws, result) {
            var msg = Ext.JSON.decode(result.data);
            var item = null;
            var html = '';
            if (msg.msgtype == 1) {
                //操作系统
                item = me.down('gauge[itemId=cpu' + msg.suid + ']');
                if (item) {
                    item.setValue(msg.cpurate * 100);
                }
                if (msg.usedPercent) {
                    item = me.down('gauge[itemId=memory' + msg.suid + ']');
                    if (item) {
                        item.setValue(msg.usedPercent);
                    }
                }
                var total = Math.round(msg.total / 1024 / 1024 / 1024);
                if (!msg.cpunum) {
                    msg.cpunum = '--';
                }
                item = me.down('panel[itemId=os' + msg.suid + ']');
                if (item) {
                    html = '<div style="text-align:center"><span style="width:155px;display:inline-block;text-indent:0px;">CPU：' + msg.cpunum + '核</span><span style="width:175px;display:inline-block;text-indent:0px;">内存：' + total + 'GB</span></div>';
                    item.setHtml(html);
                }
                if (msg.cpuname) {
                    item = me.down('panel[itemId=osname' + msg.suid + ']');
                    if (item) {
                        html = '<div style="text-align:center">' + msg.cpuname + '</div>';
                        item.setHtml(html);
                    }
                }
            }
            if (msg.msgtype == 2) {
                //通讯服务
                var status = '正常';
                var statuscolor = '#7bc309';
                if (!msg.servicestatus) {
                    status = '停止';
                    statuscolor = 'red';
                }
                var livestime = '';
                if (msg.livestime) {
                    livestime = Ext.Date.format(new Date(msg.livestime), 'Y-m-d H:i:s');
                }
                item = me.down('panel[itemId=service' + msg.suid + ']');
                if (item) {
                    var html = '<div style="text-align:center"><span style="font-weight:bold;margin-right:20px;">' + msg.name + '</span>状态：<span style="color:' + statuscolor + ';">' + status + '</span>，最后心跳：' + livestime + '</div>';
                    item.setHtml(html);
                }
            }

            if (msg.msgtype == 3) {
                //刷卡记录
                if (me.cardrecordgrid) {
                    var store = me.cardrecordgrid.getStore();
                    store.insert(0, msg);
                    if (store.getData().items.length > store.pageSize) {
                        store.removeAt(store.pageSize, store.getData().items.length - store.pageSize);
                    }
                }
            }
            if (msg.msgtype == 4) {
                //监控区域有刷卡出入，则刷新数据
                if (me.backhomepanel) {
                    if (!me.backhomepanel.isReFresh) {
                        me.backhomepanel.isReFresh = true;
                        me.backhomepanel.IntervalTimes = 0;
                        me.backhomepanel.Interval = setInterval(function() {
                            //延迟10秒后刷新，刷卡记录会在10秒内完成数据同步
                            me.backhomepanel.RefreshData();
                        }, 3000);
                    } else {
                        me.backhomepanel.IntervalTimes = 0;
                    }
                }
            }

        }, function(ws, result) {
            if (opencallback && typeof(opencallback) == 'function') {
                opencallback();
            }
        });
    }
});
Ext.define('KitchenSink.view.charts.pie.Basic', {
    extend: 'Ext.Panel',
});
Ext.define('KitchenSink.view.charts.column.Stacked', {
    extend: 'Ext.Panel',
});
Ext.define('KitchenSink.view.charts.bar.Basic', {
    extend: 'Ext.Panel',
});
Ext.define('KitchenSink.view.charts.bar.Vertical', {
    extend: 'Ext.Panel',
});