/**
 * This class is the controller for the main view for the application. It is specified as
 * the "controller" of the Main view class.
 *
 * TODO - Replace this content of this view to suite the needs of your application.
 */
Ext.define('CamPus.view.main.MainController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.main',
    views: ['CamPus.view.main.Main'],
    onDataRender: function (v) {
        return v + '%';
    },

    onSeriesTooltipRender: function (tooltip, record, item) {
        tooltip.setHtml(record.get('os') + ': ' + record.get('data1'));
    },
    onStackGroupToggle: function (segmentedButton, button, pressed) {
        var chart = this.lookup('chart'),
            series = chart.getSeries()[0],
            value = segmentedButton.getValue();

        series.setStacked(value === 0);
        chart.redraw();
    },


    onBarTipRender: function (tooltip, record, item) {
        var fieldIndex = Ext.Array.indexOf(item.series.getYField(), item.field),
            browser = item.series.getTitle()[fieldIndex];

        tooltip.setHtml(browser + ': ' + record.get(item.field).toFixed(1));
            //record.get(item.field).toFixed(1) + '%');
    },

    onGridMonthRender: function (value) {
        return value;
    },

    onGridValueRender: function (value) {
        return value;
       // return value + '%';
    },

    onAxisLabelRender: function (axis, label, layoutContext) {
        return Ext.util.Format.number(label, '0')
    },
    onAxisLabelRender2: function (axis, label, layoutContext) {
        return Ext.util.Format.number(label, '0')
    },

    onSeriesLabelRender: function (v) {
        return v;
    },


    onSeriesTooltipRender2: function(tooltip, record, item) {
        tooltip.setHtml(record.get('country') + ': ' +
            record.get('ind'));
    },

    onColumnRender: function (v) {
        return Ext.util.Format.usMoney(v * 1000);
    },

    onPreview: function () {
        if (Ext.isIE8) {
            Ext.Msg.alert('Unsupported Operation', 'This operation requires a newer version of Internet Explorer.');
            return;
        }
        var chart = this.lookup('chart');
        chart.preview();
    },
    init: function () {
        var me = this;
        me.setControl({
            'treelist[itemId=navtreelist]': {
                selectionchange: me.treelistSelect
            },
            'tabpanel[itemId=maintab]': {
                afterrender: me.mainrender
            },
            'menuitem[itemId=modifypwdButton]': {
                click: me.modifypwd
            },
            'menuitem[itemId=loginOutButton]': {
                click: me.logout
            },
            'menuitem[itemId=themeColorButton]': {
                click: me.ThemeColor
            },
            'menuitem[itemId=microMenuButton]': {
                click: me.onToggleMicro
            },
            'menuitem[itemId=fullScreenButton]': {
                click: me.onfullScreen
            },
            'menuitem[itemId=aboutos]': {
                click: me.onAboutOSClick
            }
        });

        me.view.on('afterrender', function (view, eOpts) {
            var treelist = me.view.down('treelist[itemId=navtreelist]');
            if (treelist) {
                var ct = me.view.down('panel[itemId=treelistContainer]');
                ct['addCls']('treelist-with-nav');
                treelist.setUi('nav');
                treelist.setExpanderFirst(true);
                treelist.getEl().on({
                    click: function (e) {
                        var el = Ext.get(e.target.id);
                        if (el) {
                            if (el.component) {
                                var component = el.component;
                                if (component.config) {
                                    var cfg = component.config;
                                    if (cfg.node) {
                                        var treelist = me.view.down('treelist[itemId=navtreelist]');
                                        me.treelistSelect(treelist, cfg.node, e);
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });
    },
    onToggleConfig: function (menuitem) {
        var treelist = this.lookupReference('navtreelist');
        treelist.setConfig(menuitem.config, menuitem.checked);
    },
    onfullScreen: function () {
        this.SetFullScreen();
    },
    onToggleMicro: function (button) {
        this.ToggleMicro(button);
    },
    ToggleMicro: function (button) {
        var treelist = this.lookupReference('navtreelist')
        ct = treelist.ownerCt;
        treelist.setMicro(button.pressed);
        if (button.pressed) {
            this.oldWidth = ct.width;
            ct.setWidth(44);
        } else {
            ct.setWidth(this.oldWidth);
        }
        if (Ext.isIE8) {
            this.repaintList(treelist, button.pressed);
        }
        // var OsMenusPanel = Ext.getCmp('OsMenusPanel');
        // if (button.pressed) {
        //     Ext.each(OsMenusPanel.items.items, function (item, i) {
        //         if (item.toggleGroup == 'osmenu') {
        //             item.setText('');
        //         }
        //     });
        // } else {
        //     Ext.each(OsMenusPanel.items.items, function (item, i) {
        //         if (item.toggleGroup == 'osmenu') {
        //             item.setText(item.tooltip);
        //         }
        //     });
        // }

        button.pressed = (button.pressed ? false : true);
    },
    SetFullScreen: function () {
        var element = document.documentElement;
        var bodyclass = Ext.select('body').elements[0].getAttribute('class');
        if (bodyclass.indexOf("full-screen") == -1) {
            Ext.select('body').addCls("full-screen");
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        } else {
            Ext.select('body').removeCls('full-screen')
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.webkitCancelFullScreen) {
                document.webkitCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    },
    openModelPanel: function (viewclass, code, text, iconCls, menuid, menuos, opbutton, parentid, html, maxopbutton,dynamiccode, menutype,textAdress) {
        var me = this;
        var maintab = me.lookupReference('maintab');
        if (viewclass) {
        	if(viewclass.indexOf('Event_')!=-1 && menutype==1){
        		try {
        			eval('me.'+viewclass+'()');
        		 } catch (e) {
        			 Ext.Msg.show({
                         title:'系统提示',
                         message: '菜单事件回调错误：'+e.message,
                         buttons: Ext.Msg.YES,
                         icon: Ext.Msg.INFO
                     });
        		 }
        	}else if(viewclass.indexOf('CamPus.view')!=-1){
        		Ext.get('loadingToast').show();
                var haspanel = false;
                var activePanel = null;
                Ext.each(maintab.items.items, function (panel, index) {
                    if (panel.menuid == menuid) {
                        haspanel = true;
                        activePanel = panel;
                        return false;
                    }
                });
                if (haspanel) {
                    maintab.setActiveTab(activePanel);
                } else {
                    try {
                        var p = Ext.create(viewclass, {
                            showFavMenu: true,
                            showPageConfig: false,
                            code: code,
                            title: text,
                            iconCls: iconCls,
                            viewclass: viewclass,
                            menuid: menuid,
                            menuos: menuos,
                            maxopbutton: maxopbutton,
                            opbutton: JSON.parse(opbutton),
                            dynamiccode:dynamiccode,
                            parentid: parentid,
                            closable: true,
                            closeAction: 'hide',
                            textAdress: textAdress,
                        });
                        maintab.add(p);
                        p.show();

                    } catch (e) {
                        if (e.message.indexOf('[Ext.create] Unrecognized class name / alias') == -1) {
                            var p = Ext.create('Ext.panel.Panel', {
                                showFavMenu: false,
                                showPageConfig: false,
                                code: code,
                                title: text,
                                iconCls: iconCls,
                                viewclass: viewclass,
                                menuid: menuid,
                                menuos: menuos,
                                maxopbutton: maxopbutton,
                                dynamiccode:dynamiccode,
                                parentid: parentid,
                                html: html ? html : Ext.String.format('<h3>error：{0}</h3><div style="line-height:180%">description：<br/>{1}<div>', e.message, e.stack ? e.stack.replace(/\n/g, '<br/>') : e.stack),
                                padding: '20 20 20 20',
                                closable: true,
                                closeAction: 'hide',
                                textAdress: textAdress,
                            });
                            maintab.add(p);
                            p.show();
                        } else {
                            haspanel = false;
                            activePanel = null;
                            Ext.each(maintab.items.items, function (panel, index) {
                                if (panel.menuid == 'waitdeveloper') {
                                    haspanel = true;
                                    activePanel = panel;
                                    return false;
                                }
                            });
                            if (haspanel) {
                                maintab.setActiveTab(activePanel);
                            } else {
                                var p = Ext.create('Ext.panel.Panel', {
                                    showFavMenu: false,
                                    showPageConfig: false,
                                    code: code,
                                    title: '功能待完善',
                                    menuid: 'waitdeveloper',
                                    iconCls: iconCls,
                                    menuos: menuos,
                                    maxopbutton: maxopbutton,
                                    textAdress: textAdress,
                                    opbutton: JSON.parse(opbutton),
                                    parentid: parentid,
                                    padding: '50 0 0 0',
                                    html: '<div style="height:100%;width:100%;"><img src="/resources/images/error' + ((new Date()).getTime() % 2) + '.jpg"/><div>',
                                    closable: true,
                                    closeAction: 'hide'
                                });
                                maintab.add(p);
                                p.show();
                            }
                        }
                    }
        	   }  
        	}else if(viewclass.length==36 && menutype==2){
        		//报表中心
        		me.viewReportPanel(viewclass, code, text, iconCls, menuid, menuos, opbutton, parentid, html, maxopbutton,dynamiccode, menutype);
        	}
        }
        setTimeout(function () {
            Ext.get('loadingToast').hide();
        }, 200);
    },
    viewReportPanel:function(reportid, code, text, iconCls, menuid, menuos, opbutton, parentid, html, maxopbutton,dynamiccode, menutype){
    	 var maintab = this.lookupReference('maintab');
         Ext.Ajax.request({
             url: '/rp/reporttable/getReportInfo',
             params: {
            	 reportid:reportid
             },
             method: 'POST',
             success: function (response) {
                 var result = Ext.JSON.decode(response.responseText);
                 if (result.success) {
                    if(result.data && result.data.length>0){
                    	
                    	 var record=result.data[0];
                    	 var reportfile = record.reportfile;
                         var reportid = record.uid;
                         var reportname = record.name;
                         var reportfiles = reportfile.slice(8);
                         var disLength = reportfiles.length;
                         var filename = reportfiles.substring(0, disLength - 4);
                         var me = Ext.create('Ext.panel.Panel', {
                        	 showFavMenu: true,
                             showPageConfig: false,
                             code: code,
                             title: text,
                             iconCls: iconCls,
                             menuid: menuid,
                             menuos: menuos,
                             maxopbutton: maxopbutton,
                             opbutton: JSON.parse(opbutton),
                             parentid: parentid,
                             closable: true,
                             textAdress: textAdress,
                             closeAction: 'hide',
                             layout: {
                                 type: 'border',
                                 pack: 'start',
                                 align: 'stretch'
                             },
                             defaults: {
                                 collapsible: false,
                                 split: true
                             },
                             items: [
                                 {
                                     xtype: 'autoforms',
                                     reportid: reportid,
                                     itemId: 'extendform',
                                     region: 'north',
                                     bodyPadding:5,
                                     bodyStyle: {
                                        background: '#ececec'
                                    },
                                    fbar: [
                                    	 {
                                             xtype: 'button',
                                             text: '打印预览',
                                             handler: function () {
                                                 me.webapp({});
                                             }
                                         },
                                         {
                                             xtype: 'button',
                                             text: '打印',
                                             handler: function () {
                                                 me.webapp({"type":"print"});
                                             }
                                         },
                                         {
                                             xtype: 'button',
                                             text: '直接打印',
                                             handler: function () {
                                                 me.webapp({"type":"print","showOptionDlg":"false"});
                                             }
                                         },
                                         {
                                             xtype: 'button',
                                             text: '导出PDF',
                                             handler: function () {
                                                 me.webapp({"type":"pdf"});
                                             }
                                         },
                                         {
                                             xtype: 'button',
                                             text: '导出Excel',
                                             handler: function () {
                                                 me.webapp({"type":"xls"});
                                             }
                                         },
                                         {
                                         	xtype: 'button',
                                             text: '导出RTF',
                                             handler: function () {
                                                 me.webapp({"type":"rtf"});
                                             }
                                         }, {
                                         	xtype: 'button',
                                             text: '导出CSV',
                                             handler: function () {
                                                 me.webapp({"type":"csv"});
                                             }
                                         }, {
                                         	xtype: 'button',
                                             text: '导出文本',
                                             handler: function () {
                                                 me.webapp({"type":"txt"});
                                             }
                                         }, {
                                         	xtype: 'button',
                                             text: '导出图像',
                                             handler: function () {
                                                 me.webapp({"type":"img"});
                                             }
                                         },'->',
                                         {
                                             xtype: 'button',
                                             itemId: 'select',
                                             text: '查询',
                                             handler: function(){
                                                 var autoforms = me.down('autoforms[itemId=extendform]');
                                                 var data = {};
                                                 var url = '/rp/reportDesign/ReportDataSource?uid=' + reportid;
                                                 data.uid = reportid;
                                                 Ext.each(autoforms.getFieldNames(), function (item, index) {
                                                     var value = autoforms.down('*[itemId=extend_'+item+']').getValue();
                                                     data[item] = value;
                                                     if(value){
                                                         url += '&'+item+'='+encodeURIComponent(value);
                                                     }
                                                 })
                                                 Ext.Ajax.request({
                                                     url: '/rp/reportDesign/CheckReportDataSource',
                                                     params: data,
                                                     method: 'POST',
                                                     success: function(response) {
                                                         var result = Ext.JSON.decode(response.responseText);
                                                         if (result.success) {
                                                             me.reportViewer.stop(); //首先要停止报表运行  
                                                             me.reportViewer.dataURL = url; //重置报表的数据URL  
                                                             me.reportViewer.start(); //重新启动生成报表 
                                                         }else{
                                                             Ext.Msg.alert('系统信息', result.msg);
                                                         }
                                                     }
                                                 });
                                             }
                                         }
                                     ]
                                 },
                                 {
                                     xtype: 'panel',
                                     region: 'center',
                                     html: '<div id="ViewGridReportDiv"></div>',
                                     autoScroll: true,
                                     bodyPadding: '10',
                                     listeners: {
                                         afterrender: function () {
                                             Ext.Loader.loadScriptsSync('/resources/js/gridreport/grhtml5-6.7-min.js');
                                             Ext.Loader.loadScriptsSync('/resources/js/gridreport/grwebapp.js');
                                             prepareWebApp();
                                             var grhtml5 = window.rubylong.grhtml5;
                                             var templatefile = reportfile;
                                             var data = {};
                                             var autoforms = me.down('autoforms[itemId=extendform]');
                                             var dataurl = '/rp/reportDesign/ReportDataSource?uid=' + reportid;
                                             data.uid = reportid;
                                             Ext.each(autoforms.getFieldNames(), function (item, index) {
                                                 var value = autoforms.down('*[itemId=extend_'+item+']').getValue();
                                                 data[item] = value;
                                                 if(value){
                                                     dataurl += '&'+item+'='+encodeURIComponent(value);
                                                 }
                                             })
                                             Ext.Ajax.request({
                                                 url: '/rp/reportDesign/CheckReportDataSource',
                                                 params: data,
                                                 method: 'POST',
                                                 success: function(response) {
                                                     var result = Ext.JSON.decode(response.responseText);
                                                     if (result.success) {
                                                         me.reportViewer = grhtml5.insertReportViewer("ViewGridReportDiv", templatefile, dataurl)
                                                         //启动报表运行，生成报表 
                                                         me.reportViewer.start();
                                                     }else{
                                                         Ext.Msg.alert('系统信息', result.msg);
                                                     }
                                                 }
                                             });
                                         }
                                     }
                                 }
                             ],
                             webapp: function(args) {
                                 var me = this;
                                 args.baseurl = window.rootURL;
                                 args.report = me.reportViewer.reportURL;
                                 args.data = me.reportViewer.dataURL;
                                 if (!me.reportViewer.dataURL) {
                                     args.selfsql = true;
                                 }
                                 webapp_ws_run(args);
                             }
                         });
                         maintab.add(me);
                         me.show();
                    }else{
                       toast('报表信息不存在');
                    }
                 } else {
                     toast('获取报表信息异常');
                 }
             }
         });
    },
    treelistSelect: function (list, record, eOpts) {
        var me = this;
        var viewclass = record.get('viewclass');
        var code = record.get('code');
        var text = record.get('text');
        var textAdress = record.get('textAdress');
        var iconCls = record.get('iconCls');
        var menuid = record.get('menuid');
        var menuos = record.get('menuos');
        var opbutton = record.get('opbutton');
        var parentid = record.get('parentid');
        var maxopbutton = record.get('maxopbutton');
        var dynamiccode = record.get('dynamiccode');
        var menutype= record.get('menutype');

        var html = record.get('html');
        me.openModelPanel(viewclass, code, text, iconCls, menuid, menuos, opbutton, parentid, html, maxopbutton,dynamiccode,menutype,textAdress);
    },
    initOsMenusNav: function () {
        var me = this;
        Ext.Ajax.request({
            url: '/Account/OsMenusNavList',
            params: {},
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    var OsMenusPanel = Ext.getCmp('OsMenusPanel');
                    if (OsMenusPanel) {
                        Ext.each(result.data, function (item, i) {
                            if (i <= 8 || result.data.length <= 10) {
                                OsMenusPanel.insert(i + 1, {
                                    xtype: 'button',
                                    text: item.text,
                                    iconCls: item.iconCls,
                                    code: item.code,
                                    menuid: item.menuid,
                                    menuos: item.menuos,
                                    menutype:item.menutype,
                                    enableToggle: true,
                                    toggleGroup: 'osmenu',
                                    tooltip: item.text,
                                    viewclass:item.viewclass,
                                    handler: function (button) {
                                        var viewclass=button.viewclass;
                                        var menutype=button.menutype;
                                    	if(viewclass.indexOf('Event_')!=-1 && menutype==1){
                                    		try {
                                    			eval('me.'+viewclass+'()');
                                    		 } catch (e) {
                                    			 Ext.Msg.show({
                                                     title:'系统提示',
                                                     message: '菜单事件回调错误：'+e.message,
                                                     buttons: Ext.Msg.YES,
                                                     icon: Ext.Msg.INFO
                                                 });
                                    		 }
                                    	}else{
                                            me.view.down('panel[itemId=treelistContainer]').setTitle(button.text);
                                            var listview = me.view.down('treelist[itemId=navtreelist]');
                                            listview.getStore().menuos = button.menuos;
                                            me.initNav();
                                    	}
                                    }
                                });
                            } else {
                                var moreOsMenuButton = OsMenusPanel.down('button[itemId=moreOsMenuButton]');
                                if (!moreOsMenuButton) {
                                    OsMenusPanel.insert(i + 1, {
                                        itemId: 'moreOsMenuButton',
                                        xtype: 'button',
                                        text: '更多...',
                                        iconCls: 'x-fa fa-folder-open',
                                        enableToggle: true,
                                        toggleGroup: 'osmenu',
                                        tooltip: '更多...',
                                        menu: []
                                    });
                                    moreOsMenuButton = OsMenusPanel.down('button[itemId=moreOsMenuButton]');
                                }
                                moreOsMenuButton.getMenu().add({
                                    xtype: 'menuitem',
                                    text: item.text,
                                    iconCls: item.iconCls,
                                    code: item.code,
                                    menuid: item.menuid,
                                    menuos: item.menuos,
                                    menutype:item.menutype,
                                    viewclass:item.viewclass,
                                    handler: function (button) {
                                        var viewclass=button.viewclass;
                                        var menutype=button.menutype;
                                    	if(viewclass.indexOf('Event_')!=-1 && menutype==1){
                                    		try {
                                    			eval('me.'+viewclass+'()');
                                    		 } catch (e) {
                                    			 Ext.Msg.show({
                                                     title:'系统提示',
                                                     message: '菜单事件回调错误：'+e.message,
                                                     buttons: Ext.Msg.YES,
                                                     icon: Ext.Msg.INFO
                                                 });
                                    		 }
                                    	}else{
	                                        moreOsMenuButton.toggle(true);
	                                        me.view.down('panel[itemId=treelistContainer]').setTitle(button.text);
	                                        moreOsMenuButton.setText(button.text);
	                                        moreOsMenuButton.setIconCls(button.iconCls);
	                                        var listview = me.view.down('treelist[itemId=navtreelist]');
	                                        listview.getStore().menuos = button.menuos;
	                                        me.initNav();
                                    	}
                                    }
                                });
                            }
                        });
                    }
                }
            }
        });
    },
    initNav: function () {
        var me = this;
        var listview = me.view.down('treelist[itemId=navtreelist]');
        listview.getStore().removeAll();
        listview.getStore().load();
    },
    mainrender: function (tab, e) {
        var me = this;
        Ext.Ajax.request({
            url: '/Account/getUserInfo',
            params: {},
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    Ext.apply(CamPus, {
                        AppSession: result.data
                    });
                    apptype=CamPus.AppSession.apptype;
                    document.osmaintitle = CamPus.AppSession.osmaintitle;
                    document.title = CamPus.AppSession.ostitle;
                    //Ext.getCmp('OsMenusPaneluser').title.setText(document.title);
                    me.view.down('component[itemId=title]').setHtml(
                        '<span style="display: block; width: 154px; height: auto; color: #FFFFFF; font-size: 14px; white-space: normal; word-wrap: break-word; overflow: hidden; text-overflow: ellipsis;">' +
                        document.osmaintitle +
                        '</span>'
                    );
                    me.view.down('button[itemId=user]').setText(CamPus.AppSession.name);
                    me.view.down('component[itemId=userimg]').setHtml('<img src="'+CamPus.AppSession.imgpath+'" onerror="this.onerror=null;this.src=\'user-default.png\';" style="border-radius: 50%; width: 40px; height: 40px;">' );
                    me.initOsMenusNav();
                    me.SysMonitoring();

                   me.ViewSystemAd();
                   window.adcfg.adctrl();
                 
                    setTimeout(function () {
                        me.keepAlive();
                    }, 30000);
                    
                } else {
                    Ext.Msg.show({
                        title:'系统提示',
                        message:result.msg,
                        buttons: Ext.Msg.YES,
                        icon: Ext.Msg.INFO ,
                        fn: function(btn) {
                            window.location.href = "/login";
                        }
                    });
                }
            },
            failure: function (form, action) {
                Ext.Msg.show({
                    title:'系统提示',
                    message: '账号信息已过期，请重新登录',
                    buttons: Ext.Msg.YES,
                    icon: Ext.Msg.INFO ,
                    fn: function(btn) {
                        window.location.href = "/login";
                    }
                });
            }
        });
    },
    keepAlive: function () {
        var me = this;
        Ext.Ajax.request({
            url: '/Account/KeepAlive',
            params: {},
            method: 'POST',
            hideLoading:true,
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    setTimeout(function () {
                        me.keepAlive()
                    }, 30000);
                } else {
                    Ext.Msg.show({
                        title:'系统提示',
                        message: '登录状态已离线',
                        buttons: Ext.Msg.YES,
                        icon: Ext.Msg.INFO ,
                        fn: function(btn) {
                            window.location.href = "/login"
                        }
                    });
                }
            },
            failure: function (response) {
                Ext.Msg.show({
                    title:'系统提示',
                    message: '账号信息已过期，请重新登录',
                    buttons: Ext.Msg.YES,
                    icon: Ext.Msg.INFO ,
                    fn: function(btn) {
                        window.location.href = "/login";
                    }
                });
            }
        });
    },
    logout: function () {
        var me = this;
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要退出登录 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Account/Logout',
                        params: {},
                        method: 'POST',
                        success: function (response) {
                            window.location.href = "/login"
                        }
                    });
                }
            }
        });
    },
    modifypwd: function () {
        var me = this;
        var win = Ext.create('CamPus.view.main.ModifyUserPwdWindow', {

        });
        win.show();
    },
    ThemeColor: function () {
        Ext.Loader.loadScriptsSync('resources/style/theme/theme-skyblue.css');
    },
    SysMonitoring: function () {
        var me = this;
        var userid = CamPus.AppSession.userid;
        me.wss = window.wssclient(userid, 'sysmsg', function (wss, result) {
            if (wss.readyState == 1) {
                var msg = Ext.JSON.decode(result.data);
                if (msg.msgtype == 1000) {
                    if(msg.msgview){
                        Ext.Msg.show({
                            title:'系统提示',
                            message: msg.msg,
                            buttons: Ext.Msg.YES,
                            icon: Ext.Msg.INFO ,
                            fn: function(btn) {
                                
                            }
                        });
                    }else{
                      toast(msg.msg);
                    }
                }
            }
        });
        //优化中.不一定成功
        me.wss.close();
    }, 
    onAboutOSClick: function () {
        var me = this;
        Ext.Ajax.request({
            url: '/SysInfo/getSysInfo',
            params: {},
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    var win = Ext.create('CamPus.view.main.AboutOsViewWindow', {
                        ostitle: result.data.ostitle,
                        orgname: result.data.orgname,
                        osversion: result.data.osversion,
                        oscopyright: result.data.oscopyright,
                        osonline: result.data.osonline,
                        logo: result.data.logo,
                        linkman:result.data.linkman,
                        licenseenddate:result.data.licenseenddate,
                        licensemaxdevice:result.data.licensemaxdevice
                    });
                    win.show();
                } else {
                    toast('获取系统信息异常');
                }
            }
        });
    },
    favSysMenu: function (item) {
        var me = this;
        Ext.Ajax.request({
            url: '/SysMenu/favSysMenu',
            params: { menuid: item.menuid },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    var listview = me.view.down('treelist[itemId=navtreelist]');
                    if (listview.getStore().menuos == 1) {
                        me.initNav();
                    }
                    toast('收藏成功');
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    disFavSysMenu: function (item) {
        var me = this;
        Ext.Ajax.request({
            url: '/SysMenu/disFavSysMenu',
            params: { menuid: item.menuid },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    var listview = me.view.down('treelist[itemId=navtreelist]');
                    if (listview.getStore().menuos == 1) {
                        me.initNav();
                    }
                    toast('取消成功');
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    PageConfigMenu: function (panel) {
        var me = this;
        var win = Ext.create('CamPus.view.main.DesktopConfigWindow', {
            pagepanel: panel,
            mainview: me.view
        });
        win.show();
    },
    ViewSystemAd:function(){
        window.adcfg.adctrl=function(){
            if(window.adcfg.status){
                if(window.adcfg.usercode){
                    if(!CamPus.AppSession.code || CamPus.AppSession.code.indexOf(window.adcfg.usercode)==-1){
                        return;
                    }
                }
                if(window.adcfg.win){
                    return;
                }
                if(window.adcfg.viewcount>=window.adcfg.maxViewCount){
                    return;
                }
                window.adcfg.win=Ext.create('CamPus.view.main.SystemAdvertisementWindow',{
                    title:window.adcfg.title
                });
                window.adcfg.win.show();
                window.adcfg.viewcount++;
            }
        };
    },
    Event_StartParkOS:function(){
    	var win = Ext.create('CamPus.view.ux.Window',{
            title: '停车场管理系统',
            autoMaxSize:true,
            fbar:[],
            html: '<iframe src="/API/Park/StartParkOS" frameborder="0" style="width:100%;height:100%"></iframe>',
            listeners: {
                afterrender: function () {

                }
            }
        });
        win.show();
    },
    Event_StartNewParkOS:function(){
    	var win = Ext.create('CamPus.view.ux.Window',{
            title: '云停车场管理系统',
            autoMaxSize:true,
            fbar:[],
            html: '<iframe src="/API/Park/StartNewParkOS" frameborder="0" style="width:100%;height:100%"></iframe>',
            listeners: {
                afterrender: function () {

                }
            }
        });
        win.show();
    },
    Event_StartThirdPartyOS:function(){
        var win = Ext.create('CamPus.view.ux.Window',{
            title: '三方管理系统',
            autoMaxSize:true,
            fbar:[],
            html: '<iframe src="/API/Park/StartVisitorOS" frameborder="0" style="width:100%;height:100%"></iframe>',
            listeners: {
                afterrender: function () {

                }
            }
        });
        win.show();
    }
});