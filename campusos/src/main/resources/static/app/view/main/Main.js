/**
 * This class is the main view for the application. It is specified in app.js as the
 * "mainView" property. That setting automatically applies the "viewport"
 * plugin causing this view to become the body element (i.e., the viewport).
 *
 * TODO - Replace this content of this view to suite the needs of your application.
 */
Ext.ns("CamPus.view.main");

Ext.define('CamPus.view.main.Main', {
    extend: 'Ext.panel.Panel',
    xtype: 'layout-border',
    requires: [
        'Ext.layout.container.Border',
        'Ext.panel.Panel',
        'Ext.tab.Panel',
        'Ext.list.Tree',
        'CamPus.view.main.MainController',
        'CamPus.view.main.DashboardView'
    ],
    controller: 'main',
    layout: {
        type: 'border'
    },
    defaults: {
        collapsible: false,
        split: false
    },
    header: {
        id: 'OsMenusPaneluser',
        style: {
            background: 'linear-gradient(to right, rgb(50,64,78) 220px, rgb(255,255,255) 220px)'
        },
        // title: {
        //     xtype: 'title',
        //     flex: 1,
        //     text: ''
        // },
        items: [
            {
                xtype: 'container',
                width: "93%",
                height: "auto",
                layout: 'hbox',  // 设置布局为水平布局
                items: [
                    {
                        xtype: 'component',
                        itemId: 'title',
                        html: '<span style="width: 154px;height: auto"><p style="color: #FFFFFF; font-size: 14px; white-space: normal; word-wrap: break-word;">智慧物联网管理平台</p></span>',
                        width: 220,
                        style: {
                            marginRight: '10px',  // 设置右边距
                        }
                    },
                    {
                        xtype: 'component',
                        html: '<span><i class="fas fa-home text-gray-500 text-l"></i></span>',  // 使用HTML代码插入图标
                        style: {
                            marginRight: '10px',  // 设置右边距
                        }
                    },
                    {
                        xtype: 'component',
                        itemId: 'home',
                        html: '<div style="display: flex;">\n' +
                            '    <span><p style="color: #6b7280; margin-left: 10px;font-size: 14px;font-weight: bold">首页</p></span>\n' +
                            '</div>',  // 标题内容
                    }
                ]
            }
            ,
            {
                xtype: 'container',
                layout: 'hbox',
                style: {
                    marginRight: 10
                },
                items: [{
                    xtype: 'component',
                    itemId: 'userimg',
                    html: '<img src="" onerror="this.onerror=null;this.src=\'user-default.png\';" style="border-radius: 50%; width: 40px; height: 40px;">'
                },
                    {
                    xtype: 'button',
                    itemId: 'user',
                        style: {
                            'background-color': '#ffffff',
                            'border': 'none',  // 去掉边框
                        },
                    menu: {
                        xtype: 'menu',
                        items: [
                            {
                                text: '微菜单(Ctrl+Q)',
                                itemId: 'microMenuButton',
                                pressed: true,
                                iconCls: 'x-fa fa-bars'
                            },
                            {
                                text: '全屏(F11)',
                                itemId: 'fullScreenButton',
                                iconCls: 'x-fa fa-arrows-alt'
                            },
                            {
                                iconCls: 'x-fa fa-eyedropper',
                                itemId: 'themeColorButton',
                                hidden: true,
                                text: '主题颜色'
                            },
                            {
                                iconCls: 'x-fa fa-lock',
                                itemId: 'modifypwdButton',
                                text: '修改密码'
                            },
                            {
                                text: '关于系统',
                                itemId: 'aboutos',
                                iconCls: 'x-fa fa-info'
                            },
                            {
                                iconCls: 'x-fa fa-power-off',
                                itemId: 'loginOutButton',
                                text: '安全退出'
                            }
                        ]
                    }
                }]
            }
            ,
        {
            xtype: 'container',
            hidden: true,
            html: '<iframe name="downloadIframe" id="downloadIframe"></iframe><form method="get" action="" target="downloadIframe" id="downloadform"></form>'
        }
        ]
    },
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        var dashboard = Ext.create('CamPus.view.main.DashboardView', {
            showFavMenu: false,
            showPageConfig: true
        });

        Ext.apply(me, {
            items: [{
                xtype: 'panel',
                region: 'west',
                width: 220,
                id: 'treelistContainer',
                itemId: 'treelistContainer',
                layout: {
                    type: 'fit',
                    pack: 'start',
                    align: 'stretch'
                },
                split: false,
                border: false,
                scrollable: 'y',
                items: [{
                    xtype: 'treelist',
                    id: 'mainnavtreelist',
                    itemId: 'navtreelist',
                    reference: 'navtreelist',
                    ui: 'nav',
                    flex: 1,
                    expanderOnly: false,  // 单击文字也触发展开和闭合
                    floated: true,
                    store: Ext.create('Ext.data.TreeStore', {
                        remoteSort: false,
                        autoLoad: false,
                        menuos: 0,
                        root: {
                            id: 'root',
                            text: '智慧校园',
                            floated: true,
                            expanded: true
                        },
                        proxy: {
                            type: 'ajax',
                            url: '/Account/NavList',
                            actionMethods: {
                                read: 'POST'
                            },
                            reader: {
                                type: 'json'
                            },
                            extraParams: { menuos: 0 }
                        },
                        listeners: {
                            load: function (store, records, successful, operation, eOpts) {
                                if (successful) {
                                    store.getRoot().cascadeBy(function(node) {
                                        if (node.getDepth() > 0) { // 选择二级节点以下的所有节点
                                            node.collapse();
                                        }
                                    });
                                }
                            }
                        }
                    })
                }]
            },
            {
                xtype: 'tabpanel',
                region: 'center',
                activeTab: 0,
                itemId: 'maintab',
                reference: 'maintab',
                defaults: {
                    scrollable: true
                },
                plugins: [
                    Ext.create('CamPus.view.ux.TabCloseMenu', {
                        showFavMenu: true,
                        showPageConfig: true,
                        onfavSysMenu: function () {
                            me.controller.favSysMenu(this.item);
                        },
                        onDisFavSysMenu: function () {
                            me.controller.disFavSysMenu(this.item);
                        },
                        onPageConfigMenu: function () {
                            me.controller.PageConfigMenu(this.item);
                        }
                    })
                ],
                items: [dashboard],
                listeners:{
                    tabchange: function (tabPanel, newCard, oldCard) {
                        var home = me.down('component[itemId=home]');
                        if (newCard.title === "系统桌面"){
                            home.setHtml('<div style="display: flex;">\n' +
                                '    <span style="height: 20px"><p style="color: #6b7280;font-size: 14px;font-weight: bold">首页</p></span>\n' +
                                '</div>')
                        }else {
                            home.setHtml('<div style="display: flex;">\n' +
                                '    <span style="height: 20px"><p style="color: #6b7280;font-size: 14px;font-weight: bold">首页' + newCard.textAdress + '</p></span>\n' +
                                '</div>')
                        }
                        home.updateLayout(); // 重新布局并刷新组件
                    }
                }
            }
            ]
        });


        //调用父类方法
        me.callParent(arguments);

        var ct = me.down('panel[itemId=treelistContainer]');
        var treelist = me.down('treelist[itemId=navtreelist]');
        ct['addCls']('treelist-with-nav');
        treelist.setUi('nav');
        treelist.setExpanderFirst(true);
    }
});

Ext.define('CamPus.view.main.AboutOsViewWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '关于系统',
    bodyPadding: 10,
    constrain: true,
    titleAlign: 'center',
    draggable: true,
    plain: true,
    header: true,
    border: false,
    closable: true,
    frame: false,
    style: 'border-width:0px;',
    width: 800,
    height: 510,
    animateShadow: true,
    constructor: function (config) {
        var me = this;

        var str = '<img src="' + config.logo + '" class="logo"/>';
        str += '<div class="osinfo"><span class="t">系统名称：</span><span class="c">' + config.ostitle + '</span></div>';
        str += '<div class="osinfo"><span class="t">系统版本：</span><span class="c">' + config.osversion + '</span></div>';
        str += '<div class="osinfo"><span class="t">版权所有：</span><span class="c">' + config.oscopyright + '</span></div>';
        str += '<div class="osinfo"><span class="t">授权单位：</span><span class="c" title="点击刷新许可证" style="cursor: pointer;" id="refreshlicense">' + config.orgname + '</span></div>';
        str += '<div class="osinfo"><span class="t">售后服务：</span><span class="c">' + config.linkman + '</span></div>';
        //str += '<div class="osinfo"><span class="t">在线人数：</span><span class="c">' + config.osonline + '</span></div>';
        str += '<div class="osinfo"><span class="t">有效时间：</span><span class="c">' + config.licenseenddate + '</span></div>';
        str += '<div class="osinfo"><span class="t">设备数量：</span><span class="c">' + config.licensemaxdevice + '</span></div>';
        Ext.apply(config, {
            fbar: [],
            html: '<div id="aboutosdiv">' + str + '</div>'
        });

        Ext.apply(me, config);
        me.callParent(arguments);


        Ext.get('refreshlicense').on('click', function () {
            me.refreshLicense();
        });
    },
    refreshLicense: function () {
        var me = this;
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要更新许可证 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function (btn) {
                if (btn === 'ok') {
                    Ext.get('loadingToast').show();
                    Ext.Ajax.request({
                        url: '/SysLicense/RefreshLicense',
                        params: {},
                        method: 'POST',
                        success: function (response) {
                            var result = Ext.JSON.decode(response.responseText);
                            Ext.get('loadingToast').hide();
                            if (result.success) {
                                Ext.Msg.show({
                                    title: '系统提示',
                                    message: '许可证更新成功，系统将自动退出登录！',
                                    buttons: Ext.Msg.YES,
                                    icon: Ext.Msg.INFO,
                                    fn: function (btn) {
                                        Ext.Ajax.request({
                                            url: '/Account/Logout',
                                            params: {},
                                            method: 'POST',
                                            success: function (response) {
                                                window.location.href = "/login"
                                            }
                                        });
                                    }
                                });
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    }
});


Ext.define('CamPus.view.main.DesktopConfigWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '系统桌面设置',
    layout: {
        type: 'hbox',
        pack: 'start',
        align: 'stretch'
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    bodyStyle: 'background:#ececec;',
    constructor: function (config) {
        var me = this;

        me.allgrid = Ext.create('CamPus.view.ux.IcoView', {
            scrollable: true,
            flex: 1,
            cls: 'widget-allgrid-view',
            store: Ext.create('Ext.data.Store', {
                remoteSort: true,
                autoLoad: true,
                pageSize: 100,
                proxy: {
                    type: 'ajax',
                    url: '/UCenter/Desktop/getWidgetList',
                    actionMethods: {
                        read: 'POST'
                    },
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        totalProperty: 'total',
                        successProperty: 'success',
                        messageProperty: 'msg'
                    },
                    extraParams: {}
                }
            }),
            listeners: {
                render: function (panel) {
                    panel.dragZone = Ext.create('Ext.dd.DragZone', panel.getEl(), {
                        getDragData: function (e) {
                            var sourceEl = e.getTarget(panel.itemSelector, 10);
                            var record = null;
                            if (sourceEl) {
                                record = sourceEl.cloneNode(true);
                                record.id = Ext.id();
                                return (panel.dragData = {
                                    ddel: record,
                                    sourceEl: sourceEl,
                                    repairXY: Ext.fly(sourceEl).getXY(),
                                    sourceStore: panel.store,
                                    draggedRecord: panel.getRecord(sourceEl)
                                });
                            }
                        },
                        getRepairXY: function () {
                            return this.dragData.repairXY;
                        }
                    });

                    panel.dropZone = Ext.create('Ext.dd.DropZone', panel.el, {
                        getTargetFromEvent: function (e) {
                            return e.getTarget('.widget-allgrid-view');
                        },
                        onNodeEnter: function (nodeData, dd, e, data) {

                        },
                        onNodeOver: function (nodeData, dd, e, data) {
                            var exists = false;
                            var list = me.allgrid.getStore().getData().items
                            Ext.each(list, function (item, index) {
                                if (item.get('uid') == data.draggedRecord.get('uid')) {
                                    exists = true;
                                }
                            });
                            if (!exists) {
                                return "x-dd-drop-ok-add";
                            } else {
                                return "x-dd-drop-nodrop";
                            }
                        },
                        onNodeDrop: function (node, dd, e, data) {
                            var exists = false;
                            var list = me.allgrid.getStore().getData().items
                            Ext.each(list, function (item, index) {
                                if (item.get('uid') == data.draggedRecord.get('uid')) {
                                    exists = true;
                                }
                            });
                            if (!exists) {
                                panel.move(data.draggedRecord);
                                return true;
                            } else {
                                return false;
                            }
                        }
                    });
                }
            },
            move: function (record) {
                Ext.Ajax.request({
                    url: '/UCenter/Desktop/MoveToAll',
                    params: {
                        widgetuid: record.get('uid')
                    },
                    method: 'POST',
                    success: function (response) {
                        var result = Ext.JSON.decode(response.responseText);
                        if (result.success) {
                            me.allgrid.getStore().reload();
                            me.usergrid.getStore().reload();
                        } else {
                            toast(result.msg);
                        }
                    }
                });
            }
        });

        me.usergrid = Ext.create('CamPus.view.ux.IcoView', {
            scrollable: true,
            flex: 1,
            cls: 'widget-usergrid-view',
            store: Ext.create('Ext.data.Store', {
                remoteSort: true,
                autoLoad: true,
                pageSize: 100,
                proxy: {
                    type: 'ajax',
                    url: '/UCenter/Desktop/getUserWidgetList',
                    actionMethods: {
                        read: 'POST'
                    },
                    reader: {
                        type: 'json',
                        rootProperty: 'data',
                        totalProperty: 'total',
                        successProperty: 'success',
                        messageProperty: 'msg'
                    },
                    extraParams: {}
                }
            }),
            listeners: {
                render: function (panel) {
                    panel.dragZone = Ext.create('Ext.dd.DragZone', panel.getEl(), {
                        getDragData: function (e) {
                            var sourceEl = e.getTarget(panel.itemSelector, 10);
                            var record = null;
                            if (sourceEl) {
                                record = sourceEl.cloneNode(true);
                                record.id = Ext.id();
                                return (panel.dragData = {
                                    ddel: record,
                                    sourceEl: sourceEl,
                                    repairXY: Ext.fly(sourceEl).getXY(),
                                    sourceStore: panel.store,
                                    draggedRecord: panel.getRecord(sourceEl)
                                });
                            }
                        },
                        getRepairXY: function () {
                            return this.dragData.repairXY;
                        }
                    });

                    panel.dropZone = Ext.create('Ext.dd.DropZone', panel.el, {
                        getTargetFromEvent: function (e) {
                            return e.getTarget('.widget-usergrid-view');
                        },
                        onNodeEnter: function (nodeData, dd, e, data) {

                        },
                        onNodeOver: function (nodeData, dd, e, data) {
                            var exists = false;
                            var list = me.usergrid.getStore().getData().items
                            Ext.each(list, function (item, index) {
                                if (item.get('uid') == data.draggedRecord.get('uid')) {
                                    exists = true;
                                }
                            });
                            if (!exists) {
                                return "x-dd-drop-ok-add";
                            } else {
                                return "x-dd-drop-nodrop";
                            }
                        },
                        onNodeDrop: function (node, dd, e, data) {
                            var exists = false;
                            var list = me.usergrid.getStore().getData().items
                            Ext.each(list, function (item, index) {
                                if (item.get('uid') == data.draggedRecord.get('uid')) {
                                    exists = true;
                                }
                            });
                            if (!exists) {
                                panel.move(data.draggedRecord);
                                return true;
                            } else {
                                return false;
                            }
                        }
                    });
                },
                itemdblclick: function (panel, record, item, index, e, eOpts) {
                    if (record) {
                        if (record.get('cfgjson')) {
                            if (typeof (record.get('cfgjson')) == 'string') {
                                record.set('cfgjson', JSON.parse(record.get('cfgjson')));
                            }
                            var cfgjson = record.get('cfgjson');
                            if (cfgjson && cfgjson.length > 0) {
                                var win = Ext.create('CamPus.view.main.DesktopConfigFormWindow', {
                                    title: '小部件【' + record.get('name') + '】配置',
                                    record: record,
                                    mainwin: me
                                });
                                win.show();
                            }
                        }
                    }
                }
            },
            move: function (record) {
                Ext.Ajax.request({
                    url: '/UCenter/Desktop/MoveToUser',
                    params: {
                        widgetuid: record.get('uid')
                    },
                    method: 'POST',
                    success: function (response) {
                        var result = Ext.JSON.decode(response.responseText);
                        if (result.success) {
                            me.allgrid.getStore().reload();
                            me.usergrid.getStore().reload();
                        } else {
                            toast(result.msg);
                        }
                    }
                });
            },
            prev: function () {
                var me = this;
                if (me.getSelection().length == 0) {
                    toast('请选择要移动的部件...');
                    return;
                }
                var record = me.getSelection()[0];
                me.getSelectionModel().deselectAll();
                Ext.Ajax.request({
                    url: '/UCenter/Desktop/MoveSort',
                    params: {
                        uid: record.get('uid'),
                        sort: record.get('sort'),
                        type: 'prev'
                    },
                    method: 'POST',
                    success: function (response) {
                        var result = Ext.JSON.decode(response.responseText);
                        if (result.success) {
                            me.getStore().reload();
                        } else {
                            toast(result.msg);
                        }
                    }
                });
            },
            next: function () {
                var me = this;
                if (me.getSelection().length == 0) {
                    toast('请选择要移动的部件...');
                    return;
                }
                var record = me.getSelection()[0];
                me.getSelectionModel().deselectAll();
                Ext.Ajax.request({
                    url: '/UCenter/Desktop/MoveSort',
                    params: {
                        uid: record.get('uid'),
                        sort: record.get('sort'),
                        type: 'next'
                    },
                    method: 'POST',
                    success: function (response) {
                        var result = Ext.JSON.decode(response.responseText);
                        if (result.success) {
                            me.getStore().reload();
                        } else {
                            toast(result.msg);
                        }
                    }
                });
            }
        });

        Ext.apply(config, {
            items: [{
                xtype: 'panel',
                title: '系统小部件',
                layout: {
                    type: 'fit',
                    pack: 'start',
                    align: 'stretch'
                },
                margin: '0 5 0 0',
                flex: 1,
                items: [me.allgrid]
            },
            {
                xtype: 'panel',
                title: '用户桌面设置',
                layout: {
                    type: 'fit',
                    pack: 'start',
                    align: 'stretch'
                },
                margin: '0 0 0 5',
                flex: 1,
                tools: [{
                    type: 'prev',
                    tooltip: '排序：向前移动一个位置',
                    handler: function (event, toolEl, panelHeader) {
                        me.usergrid.prev();
                    }
                }, {
                    type: 'next',
                    tooltip: '排序：向后移动一个位置',
                    handler: function (event, toolEl, panelHeader) {
                        me.usergrid.next();
                    }
                }],
                items: [me.usergrid]
            }
            ]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        me.close();
        var CamPusHome = me.mainview.down('panel[itemId=CamPusHome]');
        CamPusHome.removeAll(true);
        CamPusHome.InitDesktop();
    }
});

Ext.define('CamPus.view.main.DesktopConfigFormWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '系统桌面配置',
    layout: {
        type: 'hbox',
        pack: 'start',
        align: 'stretch'
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    bodyStyle: 'background:rgba(53, 220, 220, 0.97);',
    constructor: function (config) {
        var me = this;

        Ext.apply(me, config);
        var items = [];
        var isfilter = false;
        var filtervalue = '';

        Ext.each(config.record.get('cfgjson'), function (item, i) {
            if (typeof (item.value) == 'number') {
                items.push({
                    xtype: 'numberfield',
                    fieldLabel: item.title,
                    name: item.key,
                    value: item.value,
                    decimalPrecision: 0,
                    maxValue: 100,
                    minValue: 1,
                    allowBlank: false
                });
            } else {
                items.push({
                    xtype: (item.key == 'filter' ? 'textareafield' : 'textfield'),
                    fieldLabel: item.title,
                    name: item.key,
                    value: item.value,
                    allowBlank: false,
                    readOnly: item.key == 'filter',
                    hidden: item.key == 'filter'
                });
            }
            if (item.key == 'filter') {
                isfilter = true;
                filtervalue = item.value;
            }
        });

        if (isfilter) {
            if (config.record.get('widgetuid') == 1) {
                me.filterpanel = Ext.create('Ext.grid.Panel', {
                    title: '选择需显示的记录',
                    multiColumnSort: true,
                    border: false,
                    columnLines: true,
                    margin: '0 0 0 5',
                    store: Ext.create('Ext.data.Store', {
                        remoteSort: true,
                        autoLoad: true,
                        pageSize: 1000,
                        proxy: {
                            type: 'ajax',
                            url: '/UCenter/Desktop/getUserWidgetFilterData',
                            actionMethods: {
                                read: 'POST'
                            },
                            reader: {
                                type: 'json',
                                rootProperty: 'data',
                                totalProperty: 'total',
                                successProperty: 'success',
                                messageProperty: 'msg'
                            },
                            extraParams: { widgetuid: config.record.get('widgetuid'), filtervalue: filtervalue }
                        },
                        listeners: {
                            load: function (store, records, successful, operation, eOpts) {
                                if (isfilter) {
                                    var list = [];
                                    Ext.each(records, function (record, index) {
                                        if ((',' + filtervalue + ',').indexOf(',' + record.get('code') + ',') != -1) {
                                            list.push(record);
                                        }
                                    });
                                    me.filterpanel.getSelectionModel().select(list);
                                }
                            }
                        }
                    }),
                    viewConfig: {
                        enableTextSelection: true
                    },
                    columns: [
                        { xtype: 'rownumberer' },
                        {
                            text: '编号',
                            dataIndex: 'code',
                            width: 150,
                            sortable: false
                        },
                        {
                            text: '名称',
                            dataIndex: 'name',
                            flex: 1,
                            sortable: false
                        }
                    ],
                    selModel: 'checkboxmodel',
                    flex: 1,
                    getSelectData: function () {
                        return this.getSelection();
                    }
                });
            } else if (config.record.get('widgetuid') == 2 || config.record.get('widgetuid') == 3) {
                me.filterpanel = Ext.create('Ext.tree.Panel', {
                    title: '选择需显示的记录',
                    collapsible: false,
                    multiColumnSort: false,
                    border: false,
                    columnLines: true,
                    rowLines: true,
                    columnLines: true,
                    margin: '0 0 0 5',
                    store: Ext.create('Ext.data.TreeStore', {
                        remoteSort: false,
                        autoLoad: false,
                        root: {
                            uid: 'root',
                            name: '树',
                            expanded: true
                        },
                        proxy: {
                            type: 'ajax',
                            url: '/UCenter/Desktop/getUserWidgetFilterData',
                            actionMethods: {
                                read: 'POST'
                            },
                            reader: {
                                type: 'json'
                            },
                            extraParams: { widgetuid: config.record.get('widgetuid'), filtervalue: filtervalue }
                        }
                    }),
                    rootVisible: false,
                    reserveScrollbar: true,
                    useArrows: true,
                    multiSelect: false,
                    singleExpand: true,
                    flex: 1,
                    viewConfig: {
                        enableTextSelection: true
                    },
                    columns: [{
                        xtype: 'treecolumn',
                        text: '名称',
                        dataIndex: 'name',
                        flex: 1,
                        sortable: false
                    }],
                    getSelectData: function () {
                        return this.getChecked();
                    }
                });
            }
        }

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                margin: '0 5 0 0',
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                width: 300,
                items: items
            }, me.filterpanel]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        var filterfields = form.down('textfield[name=filter]');
        if (filterfields && me.filterpanel) {
            var filter = [];
            Ext.each(me.filterpanel.getSelectData(), function (item, index) {
                filter.push(item.get('code'));
            });
            filterfields.setValue(filter.join(','));
        }

        var cfgjson = [];
        Ext.each(me.record.get('cfgjson'), function (item, i) {
            item.value = form.down('*[name=' + item.key + ']').getValue();
            cfgjson.push(item)
        });
        Ext.Ajax.request({
            url: '/UCenter/Desktop/SaveUserWidgetCfgJson',
            params: {
                uid: me.record.get('uid'),
                cfgjson: JSON.stringify(cfgjson)
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    toast('保存成功');
                    if (me.mainwin) {
                        me.mainwin.usergrid.getStore().reload();
                    }
                    me.close();
                } else {
                    toast(result.msg);
                }
            }
        });
    }
});


Ext.define('CamPus.view.main.ModifyUserPwdWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '修改账号密码',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    bodyPadding: '10',
    border: false,
    width: 500,
    height: 322,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'userid',
                    value: CamPus.AppSession.userid,
                    allowBlank: false
                }, {
                    fieldLabel: '系统账号',
                    itemId: 'code',
                    name: 'code',
                    value: CamPus.AppSession.code,
                    allowBlank: false
                },
                {
                    fieldLabel: '姓名',
                    itemId: 'name',
                    name: 'name',
                    value: CamPus.AppSession.name,
                    allowBlank: false
                },
                {
                    fieldLabel: '原始密码',
                    name: 'oldpassword',
                    inputType: 'password',
                    allowBlank: false
                },
                {
                    fieldLabel: '新密码',
                    name: 'newpassword',
                    inputType: 'password',
                    allowBlank: false
                },
                {
                    fieldLabel: '确认密码',
                    name: 'crmnewpassword',
                    inputType: 'password',
                    allowBlank: false
                }
                ]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/SysUser/ModifySysUserPassword',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.close();
                        toast('密码修改成功！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.main.SystemAdvertisementWindow', {
    extend: 'CamPus.view.ux.Window',
    constrain: true,
    titleAlign: 'center',
    draggable: true,
    plain: true,
    header: false,
    border: false,
    closable: true,
    frame: false,
    style: 'border-width:0px;',
    bodyPadding: 0,
    width: 1000,
    height: 500,
    animateShadow: true,
    constructor: function (config) {
        var me = this;

        Ext.apply(config, {
            fbar: [],
            html: '<a id="SystemAdvertisementClose" href="javascript:void(0)"  style="width: 48px;height: 48px;display: block;position: absolute;right: 10px;top: 10px;"><img src="/resources/images/close.png"/></a><iframe src="' + window.adcfg.adurl + '" frameborder="0" style="width:100%;height:100%;"></iframe>'
        });

        Ext.apply(me, config);
        me.callParent(arguments);

        Ext.get('SystemAdvertisementClose').on('click', function () {
            me.close();

        });
    },
    listeners: {
        destroy: function (w, eOpts) {
            window.adcfg.win=null;
            setTimeout(function(){
                window.adcfg.adctrl();
            },window.adcfg.timeout*window.adcfg.viewcount);
        }
    }
});