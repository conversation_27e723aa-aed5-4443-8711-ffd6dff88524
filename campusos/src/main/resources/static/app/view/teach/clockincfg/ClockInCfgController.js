Ext.define('CamPus.view.teach.clockincfg.ClockInCfgController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.ClockInCfgController',
    view: ['ClockInCfgView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=addrule]':{
                click: me.addrule
            },
            '*[itemId=editrule]':{
                click: me.editrule
            },
            '*[itemId=delrule]':{
                click: me.delrule
            }
        });


        me.clockStore = me.view.clockStore
        me.clockList = me.view.clockList
    },

    addrule:function(){
        var me = this;
        var addChickInCfgWin = Ext.create('CamPus.view.ateach.clockincfg.AddChickInCfgWindow',{
            clockList: me.clockList
        });
        addChickInCfgWin.show();
    },
    editrule:function(){
        var me = this;
        if (me.clockList.getSelection().length > 0) {
            if(me.clockList.getSelection().length > 1){
                return toast('请选择一条规则...');
            }
            var node = me.clockList.getSelection()[0];
            var uid = me.clockList.getSelection()[0].get('id');
            var editChickInCfgWin = Ext.create('CamPus.view.ateach.clockincfg.AddChickInCfgWindow',{
                title:'修改考勤规则',
                uid: uid,
                clockList: me.clockList
            });
            var form = editChickInCfgWin.down('form[itemId=form]');
            form.loadRecord(node);
            editChickInCfgWin.show();
        }else{
            toast('请选择需修改的考勤规则...');
        }
    },
    delrule:function(){
        var me = this;
        if (me.clockList.getSelection().length === 0) {
            toast('请选择需删除的考勤规则...');
            return;
        }
        var ids = [];
        Ext.each(me.clockList.getSelection(), function(item, index) {
            ids.push(item.get('id'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/teach/clockInCfg/deleteClockInCfg',
                        params: {
                            ids: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.clockList.getStore().reload();
                                toast(result.msg);
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    }
});
