Ext.define('CamPus.view.teach.viopoption.ViopOptionStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Teach/ViopOption/getViopOptionList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        }
    }
});

Ext.define('CamPus.view.teach.viopoption.InfoStore', {
    extend: 'Ext.data.Store',
    remoteSort: true,
    autoLoad: true,
    pageSize: 50,
    proxy: {
        type: 'ajax',
        url: '/Teach/ViopOption/getInfoList',
        actionMethods: {
            read: 'POST'
        },
        reader: {
            type: 'json',
            rootProperty: 'data',
            totalProperty: 'total',
            successProperty: 'success',
            messageProperty: 'msg'
        }
    }
});