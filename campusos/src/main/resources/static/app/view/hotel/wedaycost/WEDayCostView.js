Ext.define('CamPus.view.hotel.wedaycost.WEDayCostView', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.wedaycostview',
    layout: 'border',
    border: false,

    initComponent: function () {
        var me = this;

        // 创建Store
        me.wecoststore = Ext.create('CamPus.view.hotel.wedaycost.WEDayCostStore');

        // 创建区域树
        me.areatree = Ext.create('CamPus.view.ux.AreaTree', {
            region: 'west',
            width: 200,
            collapsible: true,
            title: '区域选择',
            listeners: {
                itemclick: function (view, record) {
                    me.wecoststore.reload();
                }
            }
        });

        // 创建工具栏
        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    text: '查询',
                    iconCls: 'x-fa fa-search',
                    handler: function () {
                        me.wecoststore.reload();
                    }
                },
                {
                    text: '导出',
                    iconCls: 'x-fa fa-download',
                    handler: function () {
                        // 导出功能
                    }
                },
                {
                    text: '刷新',
                    iconCls: 'x-fa fa-refresh',
                    handler: function () {
                        me.wecoststore.reload();
                    }
                }
            ]
        });

        // 创建表格
        me.wecostgrid = Ext.create('Ext.grid.Panel', {
            region: 'center',
            collapsible: false,
            tbar: tbar,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.wecoststore,
            enableLocking: true,  // 启用列锁定功能
            viewConfig: {
                enableTextSelection: true,
                stripeRows: true,  // 斑马纹
                trackOver: true    // 鼠标悬停高亮
            },
            columns: [
                { 
                    xtype: 'rownumberer',
                    locked: true,  // 固定行号列
                    width: 50
                },
                {
                    text: '设备基本信息',
                    locked: true,  // 固定设备基本信息列组
                    columns: [
                        {
                            text: '设备编号',
                            dataIndex: 'equipment_code',
                            width: 100,
                            sortable: false,
                            locked: true
                        },
                        {
                            text: '设备类型',
                            dataIndex: 'equipment_type',
                            width: 80,
                            sortable: false,
                            locked: true,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var typeMap = {
                                    '1': '电表',
                                    '2': '水表', 
                                    '4': '热水表',
                                    '221': '气表'
                                };
                                return typeMap[value] || '未知类型';
                            }
                        },
                        {
                            text: '是否分时表',
                            dataIndex: 'is_time_sharing',
                            width: 80,
                            sortable: false,
                            locked: true,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                return value == 1 ? '<span style="color: blue;">是</span>' : '<span style="color: gray;">否</span>';
                            }
                        },
                        {
                            text: '区域编码',
                            dataIndex: 'area_code',
                            width: 100,
                            sortable: false,
                            locked: true
                        }
                    ]
                },
                {
                    text: '读数信息',
                    columns: [
                        {
                            text: '读数日期',
                            dataIndex: 'reading_date',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return Ext.Date.format(new Date(value), 'Y-m-d');
                                }
                                return '';
                            }
                        },
                        {
                            text: '读数时间',
                            dataIndex: 'reading_time',
                            width: 140,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return Ext.Date.format(new Date(value), 'Y-m-d H:i:s');
                                }
                                return '';
                            }
                        }
                    ]
                },
                {
                    text: '基础读数数据',
                    columns: [
                        {
                            text: '当前读数',
                            dataIndex: 'current_reading',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var equipmentType = record.get('equipment_type');
                                var unit = me.getUnitByType(equipmentType);
                                return value ? value.toFixed(4) + ' ' + unit : '0.0000 ' + unit;
                            }
                        },
                        {
                            text: '上次读数',
                            dataIndex: 'previous_reading',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var equipmentType = record.get('equipment_type');
                                var unit = me.getUnitByType(equipmentType);
                                return value ? value.toFixed(4) + ' ' + unit : '0.0000 ' + unit;
                            }
                        },
                        {
                            text: '用量',
                            dataIndex: 'usage_amount',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var equipmentType = record.get('equipment_type');
                                var unit = me.getUnitByType(equipmentType);
                                if (value !== null && value !== undefined) {
                                    return '<strong>' + value.toFixed(4) + ' ' + unit + '</strong>';
                                }
                                // 计算用量 = 当前读数 - 上次读数
                                var current = record.get('current_reading') || 0;
                                var previous = record.get('previous_reading') || 0;
                                var calculated = current - previous;
                                return '<strong>' + calculated.toFixed(4) + ' ' + unit + '</strong>';
                            }
                        }
                    ]
                },
                {
                    text: '分时读数（尖峰平谷四时段）',
                    columns: [
                        {
                            text: '尖时读数',
                            dataIndex: 'tip_reading',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                            }
                        },
                        {
                            text: '峰时读数',
                            dataIndex: 'peak_reading',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                            }
                        },
                        {
                            text: '平时读数',
                            dataIndex: 'flat_reading',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                            }
                        },
                        {
                            text: '谷时读数',
                            dataIndex: 'valley_reading',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                            }
                        }
                    ]
                },
                {
                    text: '费用计算',
                    columns: [
                        {
                            text: '单价',
                            dataIndex: 'unit_price',
                            width: 80,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing == 1 && equipmentType == '1') {
                                    return '<span style="color: gray;">分时计费</span>';
                                }
                                return value ? '￥' + value.toFixed(4) : '￥0.0000';
                            }
                        },
                        {
                            text: '当前余额',
                            dataIndex: 'current_balance',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value !== null && value !== undefined) {
                                    var color = value >= 0 ? 'green' : 'red';
                                    return '<span style="color: ' + color + ';">￥' + value.toFixed(2) + '</span>';
                                }
                                return '<span style="color: gray;">-</span>';
                            }
                        },
                        {
                            text: '计算费用',
                            dataIndex: 'calculated_cost',
                            width: 120,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                return me.calculateCost(record);
                            }
                        }
                    ]
                },
                {
                    text: '分时电价（尖峰平谷四时段）',
                    columns: [
                        {
                            text: '尖时电价',
                            dataIndex: 'tip_price',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                return value ? '￥' + value.toFixed(4) : '￥0.0000';
                            }
                        },
                        {
                            text: '峰时电价',
                            dataIndex: 'peak_price',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                return value ? '￥' + value.toFixed(4) : '￥0.0000';
                            }
                        },
                        {
                            text: '平时电价',
                            dataIndex: 'flat_price',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                return value ? '￥' + value.toFixed(4) : '￥0.0000';
                            }
                        },
                        {
                            text: '谷时电价',
                            dataIndex: 'valley_price',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                return value ? '￥' + value.toFixed(4) : '￥0.0000';
                            }
                        }
                    ]
                },
                {
                    text: '操作信息',
                    columns: [
                        {
                            text: '创建时间',
                            dataIndex: 'create_date',
                            width: 140,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return Ext.Date.format(new Date(value), 'Y-m-d H:i:s');
                                }
                                return '';
                            }
                        },
                        {
                            text: '修改时间',
                            dataIndex: 'modify_date',
                            width: 140,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return Ext.Date.format(new Date(value), 'Y-m-d H:i:s');
                                }
                                return '';
                            }
                        }
                    ]
                },
                {
                    text: '备注信息',
                    dataIndex: 'remark',
                    width: 200,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return value || '';
                    }
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                store: me.wecoststore,
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.areatree, me.wecostgrid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    },

    /**
     * 根据设备类型获取单位
     */
    getUnitByType: function(equipmentType) {
        var typeMap = {
            '1': 'kWh',    // 电表
            '2': 'm³',     // 水表
            '4': 'm³',     // 热水表
            '221': 'm³'    // 气表
        };
        return typeMap[equipmentType] || '';
    },

    /**
     * 计算费用（智能分时/非分时计费）
     */
    calculateCost: function(record) {
        var isTimeSharing = record.get('is_time_sharing');
        var equipmentType = record.get('equipment_type');
        var current = record.get('current_reading') || 0;
        var previous = record.get('previous_reading') || 0;
        var usage = record.get('usage_amount');

        // 如果没有用量数据，计算用量
        if (usage === null || usage === undefined) {
            usage = current - previous;
        }

        // 电表且启用分时计费
        if (equipmentType == '1' && isTimeSharing == 1) {
            return this.calculateTimeSharingCost(record, usage);
        } else {
            // 非分时计费，使用统一单价
            var unitPrice = record.get('unit_price') || 0;
            var cost = usage * unitPrice;
            return '<strong>￥' + cost.toFixed(2) + '</strong>';
        }
    },

    /**
     * 计算分时电费（尖峰平谷四时段）
     */
    calculateTimeSharingCost: function(record, totalUsage) {
        var tipReading = record.get('tip_reading') || 0;
        var peakReading = record.get('peak_reading') || 0;
        var flatReading = record.get('flat_reading') || 0;
        var valleyReading = record.get('valley_reading') || 0;

        var tipPrice = record.get('tip_price') || 0;
        var peakPrice = record.get('peak_price') || 0;
        var flatPrice = record.get('flat_price') || 0;
        var valleyPrice = record.get('valley_price') || 0;

        // 如果有分时读数，使用分时读数计算
        if (tipReading > 0 || peakReading > 0 || flatReading > 0 || valleyReading > 0) {
            var tipCost = tipReading * tipPrice;
            var peakCost = peakReading * peakPrice;
            var flatCost = flatReading * flatPrice;
            var valleyCost = valleyReading * valleyPrice;

            var totalCost = tipCost + peakCost + flatCost + valleyCost;

            var detail = '尖:￥' + tipCost.toFixed(2) +
                        ' 峰:￥' + peakCost.toFixed(2) +
                        ' 平:￥' + flatCost.toFixed(2) +
                        ' 谷:￥' + valleyCost.toFixed(2);

            return '<div><strong>￥' + totalCost.toFixed(2) + '</strong></div>' +
                   '<div style="font-size: 10px; color: #666;">' + detail + '</div>';
        } else {
            // 如果没有分时读数，使用平时电价计算
            var cost = totalUsage * flatPrice;
            return '<strong>￥' + cost.toFixed(2) + '</strong><br/>' +
                   '<span style="font-size: 10px; color: #666;">按平时电价计算</span>';
        }
    }
});
