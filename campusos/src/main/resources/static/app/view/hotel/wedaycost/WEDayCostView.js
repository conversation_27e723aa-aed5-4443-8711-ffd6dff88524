Ext.define('CamPus.view.hotel.wedaycost.WEDayCostView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.WEDayCostView",
    controller: 'WEDayCostController',
    requires: [
        'CamPus.view.hotel.wedaycost.WEDayCostStore',
        'CamPus.view.hotel.wedaycost.WEDayCostController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function(config) {
        var me = this;

        me.areastore = Ext.create('CamPus.view.hotel.wedaycost.AreaTreeStore', {

        });

        var areatbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.areastore,
                emptyText: '区域、楼宇、房间名称关键词...'
            }]
        });

        me.areatree = Ext.create('Ext.tree.Panel', {
           // title: '区域、楼宇和房间',
            itemId: 'areatree',
            tbar: areatbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            rowLines: true,
            columnLines: true,
            store: me.areastore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 280,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }]
        });

        me.wecoststore = Ext.create('CamPus.view.hotel.wedaycost.WEDayCostStore', {
            autoLoad: false
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'datefield',
                fieldLabel: '日期:',
                labelWidth: 40,
                width: 180,
                labelSeparator: '',
                name: 'starttime',
                itemId: 'starttime',
                value: new Date(Ext.Date.format(new Date(), 'Y-m-d')),
                format: 'Y-m-d',
                listeners: {
                    change: function (field) {
                        me.wecoststore.loadPage(1);
                    }
                }
            },{
                xtype: 'datefield',
                fieldLabel: '至',
                labelWidth: 40,
                width: 180,
                labelSeparator: '',
                name: 'endtime',
                itemId: 'endtime',
                value: new Date(Ext.Date.format(new Date(), 'Y-m-d')),
                format: 'Y-m-d',
                listeners: {
                    change: function (field) {
                        me.wecoststore.loadPage(1);
                    }
                }
            },{
                xtype: 'checkboxfield',
                boxLabel: '显示子区域',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.wecoststore.reload();
                    }
                }
            },'->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function() {
                        me.wecoststore.reload();
                    }
                },
                '-',
                {
                    xtype: 'button',
                    itemId: 'exportBill',
                    iconCls: 'x-fa fa-file-excel-o',
                    text: '导出账单',
                    menu: [
                        {
                            text: '导出Excel',
                            iconCls: 'x-fa fa-file-excel-o',
                            handler: function() {
                                me.onExportExcel();
                            }
                        },
                        {
                            text: '打印账单',
                            iconCls: 'x-fa fa-print',
                            handler: function() {
                                me.onPrintBill();
                            }
                        },
                        {
                            text: '生成PDF账单',
                            iconCls: 'x-fa fa-file-pdf-o',
                            handler: function() {
                                me.onExportPDF();
                            }
                        }
                    ]
                }
            ]
        });

        Ext.each(config.opbutton, function(item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.wecostgrid = Ext.create('Ext.grid.Panel', {
            region: 'center',
            collapsible: false,
            tbar: tbar,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.wecoststore,
            enableLocking: true,  // 启用列锁定功能
            viewConfig: {
                enableTextSelection: true,
                stripeRows: true,  // 斑马纹
                trackOver: true    // 鼠标悬停高亮
            },
            columns: [
                { 
                    xtype: 'rownumberer',
                    locked: true,  // 固定行号列
                    width: 50
                },
                {
                    text: '设备基本信息',
                    locked: true,  // 固定设备基本信息列组
                    columns: [
                        {
                            text: '设备编号',
                            dataIndex: 'equipment_code',
                            width: 100,
                            sortable: false,
                            locked: true
                        },
                        {
                            text: '设备类型',
                            dataIndex: 'equipment_type',
                            width: 80,
                            sortable: false,
                            locked: true,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var typeMap = {
                                    '1': '电表',
                                    '2': '水表', 
                                    '4': '热水表',
                                    '221': '气表'
                                };
                                return typeMap[value] || '未知类型';
                            }
                        },
                        {
                            text: '是否分时表',
                            dataIndex: 'is_time_sharing',
                            width: 80,
                            sortable: false,
                            locked: true,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                return value == 1 ? '<span style="color: blue;">是</span>' : '<span style="color: gray;">否</span>';
                            }
                        },
                        {
                            text: '区域编码',
                            dataIndex: 'area_code',
                            width: 100,
                            sortable: false,
                            locked: true
                        }
                    ]
                },
                {
                    text: '读数信息',
                    columns: [
                        {
                            text: '读数日期',
                            dataIndex: 'reading_date',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return Ext.Date.format(new Date(value), 'Y-m-d');
                                }
                                return '';
                            }
                        },
                        {
                            text: '读数时间',
                            dataIndex: 'reading_time',
                            width: 140,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return Ext.Date.format(new Date(value), 'Y-m-d H:i:s');
                                }
                                return '';
                            }
                        }
                    ]
                },
                {
                    text: '基础读数数据',
                    columns: [
                        {
                            text: '当前读数',
                            dataIndex: 'current_reading',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var equipmentType = record.get('equipment_type');
                                var unit = me.getUnitByType(equipmentType);
                                return value ? value.toFixed(4) + ' ' + unit : '0.0000 ' + unit;
                            }
                        },
                        {
                            text: '上次读数',
                            dataIndex: 'previous_reading',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var equipmentType = record.get('equipment_type');
                                var unit = me.getUnitByType(equipmentType);
                                return value ? value.toFixed(4) + ' ' + unit : '0.0000 ' + unit;
                            }
                        },
                        {
                            text: '用量',
                            dataIndex: 'usage_amount',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var equipmentType = record.get('equipment_type');
                                var unit = me.getUnitByType(equipmentType);
                                if (value !== null && value !== undefined) {
                                    return '<strong>' + value.toFixed(4) + ' ' + unit + '</strong>';
                                }
                                // 计算用量 = 当前读数 - 上次读数
                                var current = record.get('current_reading') || 0;
                                var previous = record.get('previous_reading') || 0;
                                var calculated = current - previous;
                                return '<strong>' + calculated.toFixed(4) + ' ' + unit + '</strong>';
                            }
                        }
                    ]
                },
                {
                    text: '分时读数（尖峰平谷四时段）',
                    columns: [
                        {
                            text: '尖时读数',
                            columns: [
                                {
                                    text: '当前读数',
                                    dataIndex: 'tip_reading',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }
                                        return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                                    }
                                },
                                {
                                    text: '上次读数',
                                    dataIndex: 'previous_tip_reading',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }
                                        return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                                    }
                                },
                                {
                                    text: '尖时用量',
                                    dataIndex: 'tip_usage',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }

                                        // 直接显示数据库中的用量，如果没有则计算
                                        if (value !== null && value !== undefined) {
                                            return '<strong>' + value.toFixed(4) + ' kWh</strong>';
                                        }

                                        return '<span style="color: gray;">-</span>';
                                    }
                                }
                            ]
                        },
                        {
                            text: '峰时读数',
                            columns: [
                                {
                                    text: '当前读数',
                                    dataIndex: 'peak_reading',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }
                                        return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                                    }
                                },
                                {
                                    text: '上次读数',
                                    dataIndex: 'previous_peak_reading',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }
                                        return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                                    }
                                },
                                {
                                    text: '峰时用量',
                                    dataIndex: 'peak_usage',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }

                                        // 直接显示数据库中的用量
                                        if (value !== null && value !== undefined) {
                                            return '<strong>' + value.toFixed(4) + ' kWh</strong>';
                                        }

                                        return '<span style="color: gray;">-</span>';
                                    }
                                }
                            ]
                        },
                        {
                            text: '平时读数',
                            columns: [
                                {
                                    text: '当前读数',
                                    dataIndex: 'flat_reading',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }
                                        return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                                    }
                                },
                                {
                                    text: '上次读数',
                                    dataIndex: 'previous_flat_reading',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }
                                        return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                                    }
                                },
                                {
                                    text: '平时用量',
                                    dataIndex: 'flat_usage',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }

                                        // 直接显示数据库中的用量
                                        if (value !== null && value !== undefined) {
                                            return '<strong>' + value.toFixed(4) + ' kWh</strong>';
                                        }

                                        return '<span style="color: gray;">-</span>';
                                    }
                                }
                            ]
                        },
                        {
                            text: '谷时读数',
                            columns: [
                                {
                                    text: '当前读数',
                                    dataIndex: 'valley_reading',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }
                                        return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                                    }
                                },
                                {
                                    text: '上次读数',
                                    dataIndex: 'previous_valley_reading',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }
                                        return value ? value.toFixed(4) + ' kWh' : '0.0000 kWh';
                                    }
                                },
                                {
                                    text: '谷时用量',
                                    dataIndex: 'valley_usage',
                                    width: 90,
                                    sortable: false,
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var isTimeSharing = record.get('is_time_sharing');
                                        var equipmentType = record.get('equipment_type');

                                        if (isTimeSharing != 1 || equipmentType != '1') {
                                            return '<span style="color: gray;">-</span>';
                                        }

                                        // 直接显示数据库中的用量
                                        if (value !== null && value !== undefined) {
                                            return '<strong>' + value.toFixed(4) + ' kWh</strong>';
                                        }

                                        return '<span style="color: gray;">-</span>';
                                    }
                                }
                            ]
                        }
                    ]
                },
                {
                    text: '费用计算',
                    columns: [
                        {
                            text: '单价',
                            dataIndex: 'unit_price',
                            width: 80,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing == 1 && equipmentType == '1') {
                                    return '<span style="color: gray;">分时计费</span>';
                                }
                                return value ? '￥' + value.toFixed(4) : '￥0.0000';
                            }
                        },
                        {
                            text: '当前余额',
                            dataIndex: 'current_balance',
                            width: 100,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value !== null && value !== undefined) {
                                    var color = value >= 0 ? 'green' : 'red';
                                    return '<span style="color: ' + color + ';">￥' + value.toFixed(2) + '</span>';
                                }
                                return '<span style="color: gray;">-</span>';
                            }
                        },
                        {
                            text: '计算费用',
                            dataIndex: 'calculated_cost',
                            width: 120,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                return me.calculateCost(record);
                            }
                        }
                    ]
                },
                {
                    text: '电价信息',
                    columns: [
                        {
                            text: '尖时电价',
                            dataIndex: 'tip_price',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                // 直接显示数据库中保存的电价
                                return value ? '￥' + value.toFixed(4) : '<span style="color: gray;">-</span>';
                            }
                        },
                        {
                            text: '峰时电价',
                            dataIndex: 'peak_price',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                // 直接显示数据库中保存的电价
                                return value ? '￥' + value.toFixed(4) : '<span style="color: gray;">-</span>';
                            }
                        },
                        {
                            text: '平时电价',
                            dataIndex: 'flat_price',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                // 直接显示数据库中保存的电价
                                return value ? '￥' + value.toFixed(4) : '<span style="color: gray;">-</span>';
                            }
                        },
                        {
                            text: '谷时电价',
                            dataIndex: 'valley_price',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }
                                // 直接显示数据库中保存的电价
                                return value ? '￥' + value.toFixed(4) : '<span style="color: gray;">-</span>';
                            }
                        }
                    ]
                },
                {
                    text: '分时费用',
                    columns: [
                        {
                            text: '尖时费用',
                            dataIndex: 'tip_cost',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }

                                // 直接显示数据库中保存的费用
                                return value ? '￥' + value.toFixed(2) : '<span style="color: gray;">-</span>';
                            }
                        },
                        {
                            text: '峰时费用',
                            dataIndex: 'peak_cost',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }

                                // 直接显示数据库中保存的费用
                                return value ? '￥' + value.toFixed(2) : '<span style="color: gray;">-</span>';
                            }
                        },
                        {
                            text: '平时费用',
                            dataIndex: 'flat_cost',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }

                                // 直接显示数据库中保存的费用
                                return value ? '￥' + value.toFixed(2) : '<span style="color: gray;">-</span>';
                            }
                        },
                        {
                            text: '谷时费用',
                            dataIndex: 'valley_cost',
                            width: 90,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var isTimeSharing = record.get('is_time_sharing');
                                var equipmentType = record.get('equipment_type');

                                if (isTimeSharing != 1 || equipmentType != '1') {
                                    return '<span style="color: gray;">-</span>';
                                }

                                // 直接显示数据库中保存的费用
                                return value ? '￥' + value.toFixed(2) : '<span style="color: gray;">-</span>';
                            }
                        }
                    ]
                },
                {
                    text: '操作信息',
                    columns: [
                        {
                            text: '创建时间',
                            dataIndex: 'create_date',
                            width: 140,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return Ext.Date.format(new Date(value), 'Y-m-d H:i:s');
                                }
                                return '';
                            }
                        },
                        {
                            text: '修改时间',
                            dataIndex: 'modify_date',
                            width: 140,
                            sortable: false,
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return Ext.Date.format(new Date(value), 'Y-m-d H:i:s');
                                }
                                return '';
                            }
                        }
                    ]
                },
                {
                    text: '备注信息',
                    dataIndex: 'remark',
                    width: 200,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return value || '';
                    }
                }
            ],
            selModel: 'rowmodel',
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.areatree, me.wecostgrid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    },
    /**
     * 根据设备类型获取单位
     */
    getUnitByType: function(equipmentType) {
        var typeMap = {
            '1': 'kWh',    // 电表
            '2': 'm³',     // 水表
            '4': 'm³',     // 热水表
            '221': 'm³'    // 气表
        };
        return typeMap[equipmentType] || '';
    },

    /**
     * 计算费用（优先使用数据库中的费用）
     */
    calculateCost: function(record) {
        var isTimeSharing = record.get('is_time_sharing');
        var equipmentType = record.get('equipment_type');

        // 电表且启用分时计费
        if (equipmentType == '1' && isTimeSharing == 1) {
            // 优先使用数据库中保存的分时总费用
            var totalTimeSharingCost = record.get('total_time_sharing_cost');
            if (totalTimeSharingCost !== null && totalTimeSharingCost !== undefined) {
                return this.formatTimeSharingCostDisplay(record, totalTimeSharingCost);
            }

            // 如果没有总费用，尝试计算
            return this.calculateTimeSharingCost(record);
        } else {
            // 非分时计费
            var usage = record.get('usage_amount');
            var unitPrice = record.get('unit_price') || 0;

            if (usage === null || usage === undefined) {
                var current = record.get('current_reading') || 0;
                var previous = record.get('previous_reading') || 0;
                usage = current - previous;
            }

            var cost = usage * unitPrice;
            return '<strong>￥' + cost.toFixed(2) + '</strong>';
        }
    },

    /**
     * 格式化分时费用显示（使用数据库中的费用）
     */
    formatTimeSharingCostDisplay: function(record, totalCost) {
        var tipCost = record.get('tip_cost') || 0;
        var peakCost = record.get('peak_cost') || 0;
        var flatCost = record.get('flat_cost') || 0;
        var valleyCost = record.get('valley_cost') || 0;

        var tipUsage = record.get('tip_usage') || 0;
        var peakUsage = record.get('peak_usage') || 0;
        var flatUsage = record.get('flat_usage') || 0;
        var valleyUsage = record.get('valley_usage') || 0;

        var tipPrice = record.get('tip_price') || 0;
        var peakPrice = record.get('peak_price') || 0;
        var flatPrice = record.get('flat_price') || 0;
        var valleyPrice = record.get('valley_price') || 0;

        // 显示详细信息
        var detail = '尖:' + tipUsage.toFixed(2) + 'kWh×￥' + tipPrice.toFixed(4) + '=￥' + tipCost.toFixed(2) + '<br/>' +
                    '峰:' + peakUsage.toFixed(2) + 'kWh×￥' + peakPrice.toFixed(4) + '=￥' + peakCost.toFixed(2) + '<br/>' +
                    '平:' + flatUsage.toFixed(2) + 'kWh×￥' + flatPrice.toFixed(4) + '=￥' + flatCost.toFixed(2) + '<br/>' +
                    '谷:' + valleyUsage.toFixed(2) + 'kWh×￥' + valleyPrice.toFixed(4) + '=￥' + valleyCost.toFixed(2);

        return '<div><strong>￥' + totalCost.toFixed(2) + '</strong></div>' +
               '<div style="font-size: 9px; color: #666; line-height: 1.2;">' + detail + '</div>';
    },

    /**
     * 计算分时电费（尖峰平谷四时段）- 兜底计算
     */
    calculateTimeSharingCost: function(record) {
        // 优先使用数据库中保存的各时段费用
        var tipCost = record.get('tip_cost');
        var peakCost = record.get('peak_cost');
        var flatCost = record.get('flat_cost');
        var valleyCost = record.get('valley_cost');

        if (tipCost !== null && peakCost !== null && flatCost !== null && valleyCost !== null) {
            var totalCost = tipCost + peakCost + flatCost + valleyCost;
            return this.formatTimeSharingCostDisplay(record, totalCost);
        }

        // 兜底：如果数据库中没有费用，进行简单计算
        var usage = record.get('usage_amount') || 0;
        var flatPrice = record.get('flat_price') || 1.2; // 默认平时电价
        var cost = usage * flatPrice;

        return '<div><strong>￥' + cost.toFixed(2) + '</strong></div>' +
               '<div style="font-size: 9px; color: #666;">按平时电价计算</div>';
    }
});

Ext.define('CamPus.view.hotel.wedaycost.WEDayCostWindow', {
    extend: 'CamPus.view.ux.Window',
    width: 600,
    height: 420,
    defaults: {
        collapsible: false,
        split: true
    },
    maximizable: false,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right',
                    labelWidth: 100
                },
                defaultType: 'textfield',
                items: [{
                    xtype: 'numberfield',
                    itemId:'id',
                    name: 'id',
                    hidden:true,
                    allowBlank: false
                },
                    {
                        xtype: 'hiddenfield',
                        name: 'areaCode',
                        value: config.areacode
                    },
                    {
                        xtype: 'monthpickerfield',
                        fieldLabel: '日期',
                        hideTrigger: false,
                        allowBlank: false,
                        value: new Date(Ext.Date.format(new Date(), 'Y-m')),
                        format: 'Y-m',
                        name: 'daily'
                    },
                    {
                        xtype: 'numberfield',
                        fieldLabel: '水费',
                        allowBlank: false,
                        maxValue: 10000,
                        minValue: 1,
                        name: 'water'
                    },
                    {
                        xtype: 'numberfield',
                        fieldLabel: '电费',
                        allowBlank: false,
                        maxValue: 10000,
                        minValue: 1,
                        name: 'electric'
                    },
                    {
                        xtype: 'textfield',
                        fieldLabel: '备注',
                        allowBlank: true,
                        name: 'remark'
                    }]
            }]
        });
        me.callParent(arguments);
    },
    Save:function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        var id = me.down('numberfield[itemId =id]').getValue();
        if(!id){
            me.down('numberfield[itemId =id]').setValue(0);
        }
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Hotel/WECost/saveWECost',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast(action.result.msg);
                        me.wecostgrid.getStore().reload();
                        me.close();
                    } else {
                        Ext.Msg.alert('系统信息',action.result.msg);
                    }
                },
                failure: function (form, action) {
                    Ext.Msg.alert('系统信息',action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.hotel.wedaycost.WEDayCostImportWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '导入水电费',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 600,
    height: 360,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 20,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'filefield',
                    name: 'excelfile',
                    fieldLabel: 'Excel文件',
                    msgTarget: 'side',
                    allowBlank: false,
                    anchor: '100%',
                    buttonText: '选择Excel文件...'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '1. 上传文件必须为Excel文档，扩展名为.xls或xlsx；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '2. 第一行为列名，系统不导入第一行数据；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '3. 文档中必须包含月份、房间(区域全称)、水费、电费，选填备注；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '4. 月份的文本格式为字符串，格式为2000-01；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }, {
                    xtype: 'displayfield',
                    fieldLabel: ' ',
                    labelSeparator: '',
                    value: '5. 水电费的文本格式为数字；',
                    style: 'margin-bottom: 0px;min-height: 17px;margin-top: 0px;'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Hotel/WECost/ImportExcel',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('数据导入成功！');
                        me.grid.getStore().reload();
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

