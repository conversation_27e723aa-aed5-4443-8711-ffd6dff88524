Ext.define('CamPus.view.hotel.wedaycost.WEDayCostController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.WEDayCostController',
    view: ['WEDayCostView'],
    init: function() {
        var me = this;
        me.setControl({
            'treepanel[itemId=areatree]': {
                select: me.onTreeSelect
            },
            '*[itemId=add]': {
                click: me.onAddClick
            },
            '*[itemId=edit]': {
                click: me.onEditClick
            },
            '*[itemId=del]': {
                click: me.onDelClick
            },
            '*[itemId=export]': {
                click: me.onExportClick
            },
            '*[itemId=import]': {
                click: me.onImportClick
            }
        });

        me.areastore = me.view.areastore;
        me.areatree = me.view.areatree;
        me.wecoststore = me.view.wecoststore;
        me.wecostgrid = me.view.wecostgrid;

        me.areastore.on('load', function(store) {
            me.wecoststore.load();
        });

        me.wecoststore.on('beforeload', function(store) {
            var areacode = '';
            if (me.areatree.getSelection().length > 0) {
                var record = me.areatree.getSelection()[0];
                areacode = record.get('code');
            }
            var viewchild = me.view.down('checkboxfield[itemId=viewchild]');
            var endtime = me.view.down('datefield[itemId=endtime]');
            var starttime = me.view.down('datefield[itemId=starttime]');
            Ext.apply(store.proxy.extraParams, {
                areaCode: areacode,
                viewChild:viewchild.getValue(),
                starttime: Ext.Date.format(starttime.getValue(), 'Y-m-d'),
                endtime: Ext.Date.format(endtime.getValue(), 'Y-m-d')
            });
        });
    },
    onTreeSelect: function() {
        this.wecostgrid.getStore().loadPage(1);
    },
    onAddClick: function(){
        var me = this;
        if (me.areatree.getSelection().length == 0) {
            toast('请选择房间...');
            return;
        }
        var record = me.areatree.getSelection()[0];
        if (record.get('areatype') != '3') {
            toast('请选择房间...');
            return;
        }
        var areacode = record.get('code');
        var addWin = Ext.create('CamPus.view.hotel.wedaycost.EquipmentReadingWindow',{
            title: '新增水电',
            wecostgrid: me.wecostgrid,
            areacode:areacode
        });
        addWin.show();
    },
    onEditClick:function(){
        var me = this;
        if (me.wecostgrid.getSelection().length == 0) {
            toast('请选择需修改的水电记录...');
            return;
        }
        var node = me.wecostgrid.getSelection()[0];
        var editWin = Ext.create('CamPus.view.hotel.wedaycost.WEDayCostWindow',{
            title: '修改水电',
            wecostgrid: me.wecostgrid
        });
        var form = editWin.down('form[itemId=form]');
        form.loadRecord(node);
        editWin.show();
    },
    onDelClick:function(){
        var me = this;
        if (me.wecostgrid.getSelection().length == 0) {
            toast('请选择需删除的水电记录...');
            return;
        }
        var id = me.wecostgrid.getSelection()[0].get('id');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Hotel/WECost/delWECost',
                        params: {
                            id:id
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.wecostgrid.getStore().reload();
                                toast(result.msg);
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onExportClick: function() {
        var me = this;
        Ext.get('loadingToast').show();
        Ext.Ajax.request({
            url: '/Hotel/WECost/export',
            params: me.wecoststore.proxy.extraParams,
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    onImportClick: function(){
        var me = this;
        var win = Ext.create('CamPus.view.hotel.wedaycost.WEDayCostImportWindow', {
            grid: me.wecostgrid
        });
        win.show();
    }
});