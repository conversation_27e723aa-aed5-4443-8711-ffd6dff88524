Ext.define('CamPus.view.hotel.wedaycost.WEDayCostController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.WEDayCostController',
    view: ['WEDayCostView'],
    init: function() {
        var me = this;
        me.setControl({
            'treepanel[itemId=areatree]': {
                select: me.onTreeSelect
            },
            '*[itemId=add]': {
                click: me.onAddClick
            },
            '*[itemId=edit]': {
                click: me.onEditClick
            },
            '*[itemId=del]': {
                click: me.onDelClick
            },
            '*[itemId=export]': {
                click: me.onExportClick
            },
            '*[itemId=import]': {
                click: me.onImportClick
            }
        });

        me.areastore = me.view.areastore;
        me.areatree = me.view.areatree;
        me.wecoststore = me.view.wecoststore;
        me.wecostgrid = me.view.wecostgrid;

        me.areastore.on('load', function(store) {
            me.wecoststore.load();
        });

        me.wecoststore.on('beforeload', function(store) {
            var areacode = '';
            if (me.areatree.getSelection().length > 0) {
                var record = me.areatree.getSelection()[0];
                areacode = record.get('code');
            }
            var viewchild = me.view.down('checkboxfield[itemId=viewchild]');
            var endtime = me.view.down('datefield[itemId=endtime]');
            var starttime = me.view.down('datefield[itemId=starttime]');
            Ext.apply(store.proxy.extraParams, {
                areaCode: areacode,
                viewChild:viewchild.getValue(),
                starttime: Ext.Date.format(starttime.getValue(), 'Y-m-d'),
                endtime: Ext.Date.format(endtime.getValue(), 'Y-m-d')
            });
        });
    },
    onTreeSelect: function() {
        this.wecostgrid.getStore().loadPage(1);
    },
    onAddClick: function(){
        var me = this;
        if (me.areatree.getSelection().length == 0) {
            toast('请选择房间...');
            return;
        }
        var record = me.areatree.getSelection()[0];
        if (record.get('areatype') != '3') {
            toast('请选择房间...');
            return;
        }
        var areacode = record.get('code');
        var addWin = Ext.create('CamPus.view.hotel.wedaycost.EquipmentReadingWindow',{
            title: '新增水电',
            wecostgrid: me.wecostgrid,
            areacode:areacode
        });
        addWin.show();
    },
    onEditClick:function(){
        var me = this;
        if (me.wecostgrid.getSelection().length == 0) {
            toast('请选择需修改的设备读数记录...');
            return;
        }
        var node = me.wecostgrid.getSelection()[0];
        var editWin = Ext.create('CamPus.view.hotel.wedaycost.EquipmentReadingWindow',{
            title: '修改设备读数',
            wecostgrid: me.wecostgrid,
            areacode: node.get('area_code') || node.get('areaCode')
        });

        // 加载现有数据到表单
        var form = editWin.down('form[itemId=readingForm]');
        if (form) {
            // 设置UID字段（关键：用于判断是修改操作）
            form.down('hiddenfield[itemId=uid]').setValue(node.get('uid'));
            form.down('textfield[name=equipment_code]').setValue(node.get('equipment_code'));
            form.down('combobox[name=equipment_type]').setValue(node.get('equipment_type'));
            form.down('textfield[name=area_code]').setValue(node.get('area_code') || node.get('areaCode'));

            // 设置读数时间
            var readingDate = node.get('reading_date') || node.get('reading_time');
            if (readingDate) {
                var dateField = form.down('datetimefield[name=reading_date]');
                if (dateField) {
                    dateField.setValue(new Date(readingDate));
                }
            }

            // 设置读数信息
            form.down('numberfield[name=current_reading]').setValue(node.get('current_reading'));
            form.down('numberfield[name=current_balance]').setValue(node.get('current_balance'));

            // 检查是否是分时电表
            var isTimeSharing = node.get('is_time_sharing');
            var equipmentType = node.get('equipment_type');

            if (isTimeSharing == 1 && equipmentType == '1') {
                // 设置分时电表选项
                form.down('checkboxfield[name=is_time_sharing]').setValue(true);

                // 触发设备类型改变事件，显示分时电表字段
                editWin.onEquipmentTypeChange('1');
                editWin.onTimeSharingChange(true);

                // 设置分时读数
                var tipField = form.down('numberfield[name=tip_reading]');
                var peakField = form.down('numberfield[name=peak_reading]');
                var flatField = form.down('numberfield[name=flat_reading]');
                var valleyField = form.down('numberfield[name=valley_reading]');

                if (tipField) tipField.setValue(node.get('tip_reading'));
                if (peakField) peakField.setValue(node.get('peak_reading'));
                if (flatField) flatField.setValue(node.get('flat_reading'));
                if (valleyField) valleyField.setValue(node.get('valley_reading'));
            } else {
                form.down('checkboxfield[name=is_time_sharing]').setValue(false);
                editWin.onTimeSharingChange(false);
            }

            // 设置其他字段
            form.down('textareafield[name=remark]').setValue(node.get('remark'));
            form.down('combobox[name=status]').setValue(node.get('status') || 1);

            // 触发费用计算
            editWin.calculateCost();
        }

        editWin.show();
    },
    onDelClick:function(){
        var me = this;
        if (me.wecostgrid.getSelection().length == 0) {
            toast('请选择需删除的设备读数记录...');
            return;
        }
        var node = me.wecostgrid.getSelection()[0];
        var uid = node.get('uid');
        var equipmentCode = node.get('equipment_code');

        Ext.Msg.show({
            title: '系统确认',
            message: '确认要删除设备 [' + equipmentCode + '] 的读数记录吗？<br/><span style="color:red;">注意：删除后将同时删除相关的费用计算和汇总数据！</span>',
            iconCls: 'fa fa-warning',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.WARNING,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Hotel/WECost/deleteEquipmentReading',
                        params: {
                            uid: uid
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.wecostgrid.getStore().reload();
                                toast(result.msg);
                            } else {
                                Ext.Msg.alert('删除失败', result.msg);
                            }
                        },
                        failure: function(response) {
                            Ext.Msg.alert('系统错误', '删除请求失败，请稍后重试');
                        }
                    });
                }
            }
        });
    },
    onExportClick: function() {
        var me = this;
        Ext.get('loadingToast').show();
        Ext.Ajax.request({
            url: '/Hotel/WECost/export',
            params: me.wecoststore.proxy.extraParams,
            method: 'POST',
            success: function(response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },
    onImportClick: function(){
        var me = this;
        var win = Ext.create('CamPus.view.hotel.wedaycost.WEDayCostImportWindow', {
            grid: me.wecostgrid
        });
        win.show();
    }
});