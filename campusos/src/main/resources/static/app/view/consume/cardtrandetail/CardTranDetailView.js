Ext.define('CamPus.view.consume.cardtrandetail.CardTranDetailView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.CardTranDetailView",
    controller: 'CardTranDetailController',
    requires: [
        'CamPus.view.consume.cardtrandetail.CardTranDetailStore',
        'CamPus.view.consume.cardtrandetail.CardTranDetailController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.infostore = Ext.create('CamPus.view.consume.cardtrandetail.InfoStore', {

        });

        var infotbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'searchfield',
                    itemId: 'searchKey',
                    width: 200,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.infostore,
                    emptyText: '姓名、' + window.applang.get("infocode") + '、卡号关键词...'
                }
            ]
        });

        me.infogrid = Ext.create('Ext.grid.Panel', {
            //title: '人员信息',
            tbar: infotbar,
            region: 'west',
            itemId: 'infogrid',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            rowLines: true,
            columnLines: true,
            width: 250,
            selModel: 'checkboxmodel',
            store: me.infostore,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
            {
                text: window.applang.get("infocode"),
                dataIndex: 'code',
                width: 100,
                sortable: true
            },
            {
                text: '姓名',
                dataIndex: 'name',
                minWidth: 100,
                flex: 1,
                sortable: false
            }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });


        me.CardTranStore = Ext.create('CamPus.view.consume.cardtrandetail.CardTranDetailStore', {

        });

        var detailbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                xtype: 'comboxdictionary',
                itemId: 'tradetype',
                insertAll: '0|全部',
                groupCode: 'SYS0000009',
                where: 'code != 2',
                width: 120,
                defaultSelectFirst: true,
                listeners: {
                    change: function (field, newValue, oldValue) {
                        if (newValue === '4') {
                            var secondComboBox = field.up('container').down('#dynamicstype');
                            secondComboBox.setVisible(true);
                            secondComboBox.getStore().reload();
                        } else {
                            var secondComboBox = field.up('container').down('#dynamicstype');
                            secondComboBox.setVisible(false);
                        }
                        me.CardTranStore.reload();
                    },
                }
            },
                {
                    xtype: 'comboxdictionary',
                    itemId: 'dynamicstype',
                    insertAll: '0|全部',
                    groupCode: 'SYS0000058',
                    width: 120,
                    defaultSelectFirst: true,
                    visible: false,
                    listeners: {
                        change: function (field) {
                            me.CardTranStore.reload();
                        }
                    }
                },
                {
                xtype: 'datetimefield',
                hideLabel: true,
                itemId: 'startdate',
                labelAlign: 'right',
                labelWidth: 45,
                width: 175,
                value: Ext.Date.format(new Date(), 'Y-m-01 00:00:00'),
                format: 'Y-m-d H:i:s',
                listeners: {
                    change: function (field) {
                        me.CardTranStore.reload();
                    }
                }
            }, {
                xtype: 'datetimefield',
                fieldLabel: '至',
                itemId: 'enddate',
                labelWidth: 20,
                width: 200,
                labelSeparator: '',
                value: Ext.Date.format(new Date(), 'Y-m-d 23:59:59'),
                format: 'Y-m-d H:i:s',
                listeners: {
                    change: function (field) {
                        me.CardTranStore.reload();
                    }
                }
            }, '->', {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.CardTranStore.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            detailbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            itemId: 'trandetaillist',
            //title: '交易记录',
            region: 'center',
            tbar: detailbar,
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.CardTranStore,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {
                    xtype: 'rownumberer'
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            metaData.style += 'color:#5fa2dd;';
                            return '待结算';
                        } else if (value == 2) {
                            metaData.style += 'color:#7bc309;';
                            return '已结算';
                        }else if (value == 3) {
                            return '';
                        }else{
                            metaData.style += 'color:red;';
                            return '无效';
                        }
                    }
                },
                {
                    text: '交易时间',
                    dataIndex: 'tradedate',
                    width: 140,
                    sortable: true
                },
                {
                    text: '金额',
                    dataIndex: 'money',
                    width: 100,
                    align: 'right',
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '交易类型',
                    dataIndex: 'tradetype',
                    width: 100,
                    sortable: false
                },
                {
                    text: '交易渠道',
                    dataIndex: 'payway',
                    width: 100,
                    sortable: false
                },
                {
                    text: '支付方式',
                    dataIndex: 'paytype',
                    width: 150,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'cardno',
                    width: 110,
                    sortable: false
                },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 100,
                    sortable: true
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 110,
                    sortable: false
                },
                {
                    text: '操作人员',
                    dataIndex: 'creator',
                    width: 110,
                    sortable: false
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    sortable: false,
                    minWidth: 150,
                    flex: 1,
                    renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                        if (value) {
                            metaData.tdAttr = 'data-qtip="' + value + '"';
                        }
                        return value;
                    }
                }
            ],
            flex: 1,
            selModel: 'rowmodel',
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });
        Ext.apply(config, {
            items: [me.infogrid, me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});
