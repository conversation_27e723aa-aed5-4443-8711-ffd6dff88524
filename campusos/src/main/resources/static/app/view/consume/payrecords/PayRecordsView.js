Ext.define('CamPus.view.consume.payrecords.PayRecordsView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.PayRecordsView",
    controller: 'PayRecordsController',
    requires: [
        'CamPus.view.consume.payrecords.PayRecordsStore',
        'CamPus.view.consume.payrecords.PayRecordsController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.infostore = Ext.create('CamPus.view.consume.payrecords.InfoStore', {

        });

        var infotbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'searchfield',
                    itemId: 'searchKey',
                    width: 200,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.infostore,
                    emptyText: '姓名、' + window.applang.get("infocode") + '、卡号关键词...'
                }
            ]
        });

        me.infogrid = Ext.create('Ext.grid.Panel', {
            //title: '人员信息',
            tbar: infotbar,
            region: 'west',
            itemId: 'infogrid',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            rowLines: true,
            columnLines: true,
            width: 250,
            store: me.infostore,
            selModel: 'checkboxmodel',
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    minWidth: 100,
                    flex: 1,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.PayRecordsStore = Ext.create('CamPus.view.consume.payrecords.PayRecordsStore', {
            autoLoad: false
        });
        me.MerStore = Ext.create('CamPus.view.consume.payrecords.MerStore', {
        });

        var PayRecordstbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'searchbox',
                    itemId: 'searchKeyBox',
                    store: me.PayRecordsStore,
                    width: 250,
                    emptyText: '机号、卡号...',
                    formItems: [
                        {
                            xtype: 'fieldcontainer',
                            fieldLabel: '消费时间',
                            layout: 'hbox',
                            itemId: 'fieldcontainer',
                            defaultType: 'textfield',
                            defaults: {
                                flex: 1,
                                labelAlign: 'right'
                            },
                            items: [
                                {
                                    xtype: 'datetimefield',
                                    hideLabel: true,
                                    itemId: 'startdate',
                                    name: 'startdate',
                                    width: 175,
                                    value: Ext.Date.format(new Date(), 'Y-m-d 00:00:00'),
                                    format: 'Y-m-d H:i:s'
                                },
                                {
                                    xtype: 'datetimefield',
                                    fieldLabel: '至',
                                    itemId: 'enddate',
                                    name: 'enddate',
                                    labelWidth: 20,
                                    width: 200,
                                    labelSeparator: '',
                                    value: Ext.Date.format(new Date(), 'Y-m-d 23:59:59'),
                                    format: 'Y-m-d H:i:s'
                                }
                            ]
                        },
                        {
                            xtype: 'combobox',
                            queryMode: 'local',
                            displayField: 'name',
                            fieldLabel: '商户',
                            valueField: 'uid',
                            name: 'merid',
                            itemId: 'mername1',
                            // editable: false,
                            // width: 110,
                            //value: '全部',
                            store: me.MerStore,
                            defaultSelectFirst: true,
                            listeners: {
                                afterrender: function (combobox) {
                                    var store = combobox.getStore();
                                    // 监听 store 的 load 事件
                                    store.on('load', function (store, records, successful, eOpts) {
                                        if (records.length > 0) {
                                            // 如果 store 中有数据，选择第一个项
                                            combobox.setValue(records[0].get('uid'));
                                        }
                                    });
                                },
                                change: function (combobox, newValue, oldValue, eOpts) {
                                    var placename = combobox.ownerCt.down('comboxlist[itemId=placename]');
                                    if (placename) {
                                        placename.where = "merid='" + newValue + "'";
                                        placename.getStore().on('beforeload', function (store) {
                                            Ext.apply(store.proxy.extraParams, {
                                                where: placename.where
                                            });
                                        });
                                        placename.getStore().reload();
                                        placename.setValue('');
                                    }
                                }
                            }
                        },
                        // {
                        //     xtype: 'comboxlist',
                        //     name: 'merid',
                        //     insertAll: '全部',
                        //     itemId: 'mername',
                        //     fieldLabel: '商户',
                        //     table: 'tb_consume_merchant',
                        //     fields: 'uid,mername as name',
                        //     displayField: 'name',
                        //     defaultSelectFirst: true,
                        //     listeners: {
                        //         change: function (combobox, newValue, oldValue, eOpts) {
                        //             var placename = combobox.ownerCt.down('comboxlist[itemId=placename]');
                        //             if (placename) {
                        //                 placename.where = "merid='" + newValue + "'";
                        //                 placename.getStore().on('beforeload', function (store) {
                        //                     Ext.apply(store.proxy.extraParams, {
                        //                         where: placename.where
                        //                     });
                        //                 });
                        //                 placename.getStore().reload();
                        //                 placename.setValue('');
                        //             }
                        //         }
                        //     }
                        // },
                        {
                            xtype: 'comboxlist',
                            name: 'placeid',
                            insertAll: '全部',
                            itemId: 'placename',
                            fieldLabel: '营业场所',
                            table: 'tb_consume_merchant_place',
                            where: '',
                            fields: 'uid,name',
                            orderby: 'uid asc',
                            displayField: 'name',
                            defaultSelectFirst: true,
                            listeners: {
                                change: function (combobox, newValue, oldValue, eOpts) {
                                    var schemename = combobox.ownerCt.down('comboxlist[itemId=schemename]');
                                    if (schemename) {
                                        schemename.where = "cms.placeid='" + newValue + "'";
                                        schemename.getStore().on('beforeload', function (store) {
                                            Ext.apply(store.proxy.extraParams, {
                                                where: schemename.where
                                            });
                                        });
                                        schemename.getStore().reload();
                                        schemename.setValue('');
                                    }
                                }
                            }
                        },
                        {
                            xtype: 'comboxlist',
                            name: 'schemeid',
                            insertAll: '全部',
                            itemId: 'schemename',
                            fieldLabel: '消费方案',
                            table: 'tb_consume_merchant_scheme cms LEFT JOIN tb_consume_scheme cs on cms.schemeid = cs.uid ',
                            where: '',
                            orderby: 'cs.uid asc',
                            fields: 'cs.uid,cs.name',
                            displayField: 'name',
                            defaultSelectFirst: true
                        },
                        {
                            xtype: 'comboxdictionary',
                            itemId: 'paytype',
                            name: 'paytype',
                            insertAll: '0|全部',
                            fieldLabel: '消费类型',
                            groupCode: 'SYS0000030',
                            width: 120,
                            defaultSelectFirst: true,
                            listeners: {
                                change: function (combox, newValue, oldValue, eOpts) {
                                    me.PayRecordsStore.on('beforeload', function (store) {
                                        Ext.apply(store.proxy.extraParams, {
                                            paytype: newValue
                                        });
                                    })
                                    me.PayRecordsStore.reload();
                                }
                            }
                        }
                    ]
                },
                '->', {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.PayRecordsStore.reload();
                    }
                }]
        });

        Ext.each(config.opbutton, function (item, i) {
            PayRecordstbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            itemId: 'payrecordsList',
            multiColumnSort: true,
            border: false,
            //title: '消费记录',
            region: 'center',
            collapsible: false,
            rowLines: true,
            columnLines: true,
            tbar: PayRecordstbar,
            store: me.PayRecordsStore,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {
                    xtype: 'rownumberer'
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            metaData.style += 'color:#5fa2dd;';
                            return '待结算';
                        } else if (value == 2) {
                            metaData.style += 'color:#7bc309;';
                            return '已结算';
                        }
                    }
                },
                {
                    text: '消费时间',
                    dataIndex: 'recordtime',
                    width: 150,
                    sortable: true
                },
                {
                    text: '金额',
                    dataIndex: 'money',
                    width: 100,
                    align: 'right',
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '消费钱包',
                    dataIndex: 'paywallet',
                    width: 80,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '主钱包'
                        } else if (value == 2) {
                            return '副钱包'
                        } else if (value == 0) {
                            return '计次'
                        }
                    }
                },
                {
                    text: '卡号',
                    dataIndex: 'cardno',
                    width: 110,
                    sortable: false
                },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 120,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'infoname',
                    width: 110,
                    sortable: false
                },
                {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 60,
                    sortable: false
                },
                {
                    text: '消费模式',
                    dataIndex: 'paytypename',
                    width: 80,
                    sortable: false
                },
                {
                    text: '设备',
                    dataIndex: 'devname',
                    width: 120,
                    sortable: false
                },
                {
                    text: '商户',
                    dataIndex: 'mername',
                    width: 150,
                    sortable: false
                },
                {
                    text: '营业场所',
                    dataIndex: 'placename',
                    minWidth: 150,
                    sortable: false
                },
                {
                    text: '备注',
                    dataIndex: 'des',
                    minWidth: 150,
                    flex: 1,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                        if (value) {
                            metaData.tdAttr = 'data-qtip="' + value + '"';
                        }
                        return value;
                    }
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });
        Ext.apply(config, {
            items: [me.infogrid, me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.consume.payrecords.backpayWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '消费回退',
    width: 400,
    height: 200,
    maximizable: false,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);
        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right'
                },
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'uid',
                    value: config.uid
                }, {
                    xtype: 'numberfield',
                    fieldLabel: '回退金额',
                    allowBlank: false,
                    decimalPrecision: 2,
                    hideTrigger: true,
                    minValue: 0,
                    itemId: 'money',
                    name: 'money'
                }, {
                    xtype: 'textfield',
                    fieldLabel: '备注',
                    allowBlank: false,
                    name: 'des'
                }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        var newmoney = form.down('numberfield[itemId=money]').getValue();
        if (newmoney > me.oldmoney) {
            toast('回退金额不能大于原金额！！！');
            return
        }
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Consume/PayRecords/backpay',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.grid.getStore().reload();
                        me.close();
                        toast('操作成功！！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});


Ext.define('CamPus.view.consume.payrecords.RecordImgWindows', {
    extend: 'CamPus.view.ux.Window',
    maximizable: true,
    height: 576,
    width: 1024,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            html: '<img src="' + window.getResourceUrl(CamPus.AppSession.resourcedomain,config.imagePath) + '" style="height: 486px;margin-left:auto;margin-right:auto;"/>'
        });
        me.callParent(arguments);
    }
});


Ext.define('CamPus.view.consume.payrecords.SignPayWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '签卡消费',
    width: 520,
    height: 360,
    maximizable: false,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);
        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'hiddenfield',
                        name: 'infoid',
                        value: config.infoid
                    },
                    {
                        xtype: 'textfield',
                        fieldLabel: '人员信息',
                        value: config.infoname,
                        readOnly:true
                    },
                    {
                        xtype: 'datetimefield',
                        fieldLabel: '日期',
                        itemId: 'recordtime',
                        name: 'recordtime',
                        value: Ext.Date.format(new Date(), 'Y-m-d'),
                        allowBlank: false,
                        format: 'Y-m-d'
                    },
                    {
                        xtype: 'hiddenfield',
                        itemId:'starttime',
                        name: 'starttime'
                    },
                    {
                        xtype: 'hiddenfield',
                        itemId:'paytype',
                        name: 'paytype'
                    },
                    {
                        xtype: 'comboxlist',
                        fieldLabel: '消费时段',
                        name: 'schemeid',
                        table: 'tb_consume_scheme',
                        where: 'status=1',
                        fields: 'uid,name,starttime,consumetype,everymoney',
                        allowBlank: false,
                        listeners: {
                            change: function (field, newValue, oldValue, eOpts) {
                                var starttime= me.down('hiddenfield[itemId=starttime]');
                                var paytype= me.down('hiddenfield[itemId=paytype]');
                                var money= me.down('numberfield[itemId=money]');
                                if(field.lastSelectedRecords){
                                    if(field.lastSelectedRecords.length>0){
                                        var record=field.lastSelectedRecords[0];
                                        starttime.setValue(record.get('starttime'));
                                        paytype.setValue(record.get('consumetype'));
                                        if(record.get('consumetype')=='2'){
                                            money.setValue(record.get('everymoney'));
                                        }else{
                                            money.setValue('');
                                        }
                                    }else{
                                        starttime.setValue('');
                                        paytype.setValue('');
                                        money.setValue('');
                                    }
                                }else{
                                    starttime.setValue('');
                                    paytype.setValue('');
                                    money.setValue('');
                                }
                            }
                        }
                    },
                    {
                        xtype: 'hiddenfield',
                        itemId:'merid',
                        name: 'merid'
                    },
                    {
                        xtype: 'comboxlist',
                        fieldLabel: '营业场所',
                        name: 'placeid',
                        table: 'tb_consume_merchant_place',
                        where: 'status=1',
                        fields: 'uid,name,merid',
                        allowBlank: false,
                        listeners: {
                            change: function (field, newValue, oldValue, eOpts) {
                                var merid= me.down('hiddenfield[itemId=merid]');
                                if(field.lastSelectedRecords){
                                    if(field.lastSelectedRecords.length>0){
                                        var record=field.lastSelectedRecords[0];
                                        merid.setValue(record.get('merid'));
                                    }else{
                                        merid.setValue('');
                                    }
                                }else{
                                    merid.setValue('');
                                }
                            }
                        }
                    },
                    {
                        xtype: 'numberfield',
                        fieldLabel: '消费金额',
                        itemId: 'money',
                        name: 'money',
                        allowBlank: false,
                        decimalPrecision: 2,
                        hideTrigger: true,
                        minValue: 0.01,
                        allowBlank: false
                    },
                    {
                        xtype: 'textfield',
                        fieldLabel: '备注',
                        name: 'des'
                    }]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Consume/PayRecords/SignPayRecord',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.grid.getStore().loadPage(1);
                        me.close();
                        toast('操作成功！！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.consume.payrecords.delPayRecordsWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '删除记录',
    width: 420,
    height: 150,
    maximizable: false,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);
        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 140,
                    labelAlign: 'right'
                },
                defaultType: 'textfield',
                items: [
                    {
                        xtype: 'datefield',
                        fieldLabel: '删除日期之前的记录',
                        itemId: 'date',
                        name: 'date',
                        allowBlank: false,
                        format: 'Y-m-d'
                    }
                ]
            }]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        var date = form.down('datefield[itemId=date]').getValue()
        if(!date){
            toast('请选择日期！');
            return;
        }
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要删除消费记录？删除后无法恢复',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Consume/PayRecords/delPayRecords',
                        params: {
                            date: date
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('删除成功...');
                                me.close();
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    }
});