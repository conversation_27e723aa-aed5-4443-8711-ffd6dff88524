Ext.define('CamPus.view.consume.merchant.MerchantView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.MerchantView",
    controller: 'MerchantController',
    requires: [
        'CamPus.view.consume.merchant.MerchantStore',
        'CamPus.view.consume.merchant.MerchantController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.merchantStore = Ext.create('CamPus.view.consume.merchant.MerchantStore', {});

        var merchantbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.merchantStore,
                emptyText: '商户名称、法人姓名'
            }, '->', {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.merchantStore.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            if (item.itemId === 'addconsumeuser' || item.itemId === 'updateconsumeuser' || item.itemId === 'delconsumeuser') {
                merchantbar.addMaxItems(item, 1);
            }
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            itemId: 'merchantlist',
            multiColumnSort: true,
            border: false,
            region: 'west',
            //title: '商户信息',
            collapsible: false,
            rowLines: true,
            columnLines: true,
            tbar: merchantbar,
            store: me.merchantStore,
            selModel: 'rowmodel',
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                {
                    xtype: 'rownumberer'
                },
                {
                    text: '商户名称',
                    dataIndex: 'mername',
                    flex: 1,
                    sortable: false
                },
                {
                    text: '法人姓名',
                    dataIndex: 'name',
                    width: 90,
                    sortable: false
                },
                {
                    text: '手机号',
                    dataIndex: 'phone',
                    width: 110,
                    sortable: false
                },
                {
                    text: '状态',
                    dataIndex: 'merstatus',
                    width: 50,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 0) {
                            metaData.style += 'color:red;';
                            return '无效';
                        } else if (value == 1) {
                            metaData.style += 'color:#7bc309;';
                            return '有效';
                        }
                    }
                }
            ],
            width: 510,
            minWidth: 390,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.merchantplaceStore = Ext.create('CamPus.view.consume.merchant.MerchantPlaceStore', {});

        var merchantplacebar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.merchantplaceStore,
                emptyText: '编号、营业场所关键词...'
            }, '->', {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.merchantplaceStore.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            if (item.itemId === 'addplace' || item.itemId === 'updateplace' || item.itemId === 'delplace') {
                merchantplacebar.addMaxItems(item, config.maxopbutton);
            }
        });

        me.placegrid = Ext.create('Ext.grid.Panel', {
            itemId: 'placelist',
            multiColumnSort: true,
            border: false,
            region: 'center',
            //title: '营业场所',
            rowLines: true,
            columnLines: true,
            collapsible: false,
            tbar: merchantplacebar,
            store: me.merchantplaceStore,
            selModel: 'rowmodel',
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'rownumberer'

            },
            {
                text: '编号',
                dataIndex: 'code',
                width: 120,
                sortable: false
            },
            {
                text: '营业场所',
                dataIndex: 'name',
                width: 200,
                sortable: false
            },
            {
                text: '状态',
                dataIndex: 'status',
                width: 120,
                sortable: false,
                renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                    if (value == 0) {
                        metaData.style += 'color:red;';
                        return '无效';
                    } else if (value == 1) {
                        metaData.style += 'color:#7bc309;';
                        return '有效';
                    }
                }
            }, {
                    text: '场所类别',
                    dataIndex: 'merstatus',
                    width: 120,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 0) {
                            metaData.style += 'color:red;';
                            return '自由消费';
                        } else if (value == 1) {
                            metaData.style += 'color:#7bc309;';
                            return '订餐消费';
                        }
                    }
                },
                {
                    text: '消费群组',
                    dataIndex: 'restrictgroup',
                    width: 120,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            metaData.style += 'color:red;';
                            return '开启';
                        } else if (value == 0) {
                            metaData.style += 'color:#7bc309;';
                            return '关闭';
                        }
                    }
                },
            {
                text: '备注',
                dataIndex: 'remark',
                flex: 1,
                sortable: false
            }

            ],
            flex: 1,
            minWidth: 390,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [
                me.grid, me.placegrid
            ]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.consume.consumeuser.EditConsumeUserWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '商户信息',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 640,
    height: 480,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                scrollable: true,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'uid'
                }, {
                    fieldLabel: '商户名称',
                    itemId: 'mername',
                    name: 'mername',
                    allowBlank: false
                }, {
                    fieldLabel: '法人姓名',
                    itemId: 'name',
                    name: 'name',
                    allowBlank: false
                }, {
                    xtype: "fieldcontainer",
                    layout: "hbox",
                    defaults: {
                        anchor: '100%',
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'textfield',
                        fieldLabel: '手机号',
                        itemId: 'phone',
                        name: 'phone',
                        allowBlank: false
                    }, {
                        xtype: 'textfield',
                        fieldLabel: '身份证号',
                        itemId: 'idcard',
                        name: 'idcard'
                    }]
                }, {
                    xtype: "fieldcontainer",
                    layout: "hbox",
                    defaults: {
                        anchor: '100%',
                        labelWidth: 100,
                        labelAlign: 'right'
                    },
                    items: [{
                        xtype: 'filefield',
                        name: 'imagefile',
                        fieldLabel: '营业执照照片',
                        msgTarget: 'side',
                        flex: 1,
                        buttonText: '',
                        vtype: 'ext',
                        fileType: ['jpg', 'jpeg'],
                        buttonConfig: {
                            iconCls: 'x-fa fa-photo'
                        }
                    },
                    {
                        xtype: "button",
                        text: "",
                        tooltip: '点击查看原有营业执照照片',
                        itemId: 'btn',
                        iconCls: 'x-fa fa-eye',
                        hidden: true,
                        listeners: {
                            click: function () {
                                var uid = me.grid.getSelection()[0].get('uid');
                                Ext.Ajax.request({
                                    url: '/Consume/ConsumeUser/getMerchantImage',
                                    params: {
                                        uid: uid
                                    },
                                    method: 'GET',
                                    success: function (response) {
                                        var result = Ext.JSON.decode(response.responseText);
                                        if (result.success) {
                                            var imgPath = result.data.imagefile;
                                            var win = Ext.create('CamPus.view.consume.merchant.MerchantImgWindows', {
                                                title: '营业执照图片',
                                                imagePath: imgPath,
                                                fbar: [{
                                                    xtype: 'button',
                                                    text: '关闭',
                                                    iconCls: 'x-fa fa-reply-all',
                                                    listeners: {
                                                        click: function (button, event) {
                                                            win.close();
                                                        }
                                                    }
                                                }]
                                            });
                                            win.show();
                                        } else {
                                            Ext.Msg.alert('系统信息', result.msg);
                                        }
                                    }
                                });
                            }
                        }
                    }]
                },
                {
                    fieldLabel: '单位税号',
                    itemId: 'taxnumber',
                    name: 'taxnumber'
                }, {
                    fieldLabel: '开户行',
                    itemId: 'bank',
                    name: 'bank'
                }, {
                    fieldLabel: '开户账号',
                    itemId: 'account',
                    name: 'account'
                },
                {
                    fieldLabel: '账号状态',
                    xtype: 'radiogroup',

                    itemId: 'status',
                    items: [{
                        name: 'status',
                        boxLabel: '有效',
                        inputValue: 1,
                        checked: true

                    },
                    {
                        name: 'status',
                        boxLabel: '无效',
                        inputValue: 0
                    }]
                },
                    {
                        fieldLabel: '场所类别',
                        xtype: 'radiogroup',
                        itemId: 'merstatus',
                        items: [{
                            name: 'merstatus',
                            boxLabel: '订餐消费',
                            inputValue: 1,
                            checked: true
                        },
                            {
                                name: 'merstatus',
                                boxLabel: '自由消费',
                                inputValue: 0
                            }]
                    }]
            }]
        });
        me.callParent(arguments);

    },

    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Consume/ConsumeUser/SaveConsumeUser',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.grid.getStore().reload();
                        me.placegrid.getStore().reload();
                        me.close();
                        toast('保存成功！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }

});

Ext.define('CamPus.view.consume.merchant.MerchantImgWindows', {
    extend: 'CamPus.view.ux.Window',
    maximizable: false,
    height: 576,
    width: 768,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'image',
                src: 'data:image/jpeg;base64,' + config.imagePath
            }]
        });
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.consume.consumeuser.EditConsumePlaceWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '营业场所信息',
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    width: 640,
    height: 400,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'placeform',
                border: false,
                scrollable: true,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'uid'
                }, {
                    xtype: 'hiddenfield',
                    name: 'merid',
                    itemId: 'merid'
                }, {
                    fieldLabel: '编号',
                    itemId: 'code',
                    name: 'code',
                    allowBlank: false
                }, {
                    fieldLabel: '营业场所',
                    itemId: 'name',
                    name: 'name',
                    allowBlank: false
                }, {
                    fieldLabel: '备注',
                    itemId: 'remark',
                    name: 'remark'
                }, {
                    fieldLabel: '状态',
                    xtype: 'radiogroup',
                    itemId: 'status',
                    items: [{
                        name: 'status',
                        boxLabel: '有效',
                        inputValue: 1,
                        checked: true
                    },
                    {
                        name: 'status',
                        boxLabel: '无效',
                        inputValue: 0
                    }]
                },{
                    fieldLabel: '场所类别',
                    xtype: 'radiogroup',
                    itemId: 'merstatus',
                    items: [{
                        name: 'merstatus',
                        boxLabel: '订餐消费',
                        inputValue: 1,
                        checked: true
                    },
                        {
                            name: 'merstatus',
                            boxLabel: '自由消费',
                            inputValue: 0
                        }]
                },
                    {
                        fieldLabel: '消费群组',
                        xtype: 'radiogroup',
                        itemId: 'restrictgroup',
                        items: [{
                            name: 'restrictgroup',
                            boxLabel: '开启',
                            inputValue: 1,
                            checked: true
                        },
                            {
                                name: 'restrictgroup',
                                boxLabel: '关闭',
                                inputValue: 0
                            }]
                    }
                ]
            }]
        });
        me.callParent(arguments);
    },

    Save: function () {
        var me = this;
        var form = me.down('form[itemId=placeform]');
        if (form.getForm().isValid()) {
            form.submit({
                url: '/Consume/ConsumeUser/SaveConsumePlace',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.placegrid.getStore().reload();
                        me.close();
                        toast('保存成功！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }

});
