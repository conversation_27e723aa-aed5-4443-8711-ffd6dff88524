Ext.define('CamPus.view.consume.offlinenamelist.OfflineNameListController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.OfflineNameListController',
    view: ['OfflineNameListView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=deviceGrid]':{
                select: me.deviceGridSelect
            },
            '*[itemId=addnamelist]':{
                click: me.addnamelist
            },
            '*[itemId=editnamelist]':{
                click: me.editnamelist
            },
            '*[itemId=delnamelist]':{
                click: me.delnamelist
            },
            '*[itemId=reDownload]':{
                click: me.reDownload
            }
        });

        me.devgrid = me.view.devgrid
        me.devstore = me.view.devstore
        me.offlinenamelistgrid = me.view.offlinenamelistgrid
        me.offlinenameliststore = me.view.offlinenameliststore
        me.status = me.view.down('combobox[itemId=status]')
        me.property = me.view.down('combobox[itemId=property]')

        me.offlinenameliststore.on('beforeload', function (store) {
            var devid = '';
            if (me.devgrid.getSelection().length > 0) {
                devid = me.devgrid.getSelection()[0].get('uid');
            }
            Ext.apply(store.proxy.extraParams, {
                devId: devid,
                downStatus: me.status.getValue(),
                property:  me.property.getValue()
            });
        });
    },

    deviceGridSelect: function(){
        var me = this;
        me.offlinenameliststore.loadPage(1);
    },

    addnamelist: function(){
        var me = this;
        if (me.devgrid.getSelection().length === 0) {
            toast('请选择设备...');
            return;
        }
        var ids = [];
        Ext.each(me.devgrid.getSelection(), function(item, index) {
            ids.push(item.get('uid'));
        });
        var win = Ext.create('CamPus.view.consume.offlinenamelist.AddOfflineNameListWindow', {
            offlinenamelistgrid: me.offlinenamelistgrid,
            devIds: ids.join(',')
        });
        win.show();
    },

    editnamelist: function () {
        var me = this;

        if (me.offlinenamelistgrid.getSelection().length === 0) {
            toast('请选择人员...');
            return;
        }
        var data = {};
        var ids = [];
        Ext.each(me.offlinenamelistgrid.getSelection(), function(item, index) {
            ids.push(item.get('id'));
        });
        var win = Ext.create('CamPus.view.consume.offlinenamelist.EditMaxTimesWindow', {
            offlinenamelistgrid: me.offlinenamelistgrid,
            infoData: ids.join(',')
        });
        var form = win.down('form[itemId=form]');
        if (me.offlinenamelistgrid.getSelection().length === 1){
            data = me.offlinenamelistgrid.getSelection()[0];
            data.set({
                maxTimes1: data.get('maxtimes1'),
                maxTimes2: data.get('maxtimes2'),
                maxTimes3: data.get('maxtimes3'),
                maxTimes4: data.get('maxtimes4')

            });
            form.loadRecord(data);
        }
        win.show();
    },

    delnamelist: function(){
        var me = this;
        if (me.offlinenamelistgrid.getSelection().length === 0) {
            toast('请选择需删除的人员...');
            return;
        }
        var ids = [];
        Ext.each(me.offlinenamelistgrid.getSelection(), function(item, index) {
            ids.push(item.get('id'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Consume/OfflineNameList/deleteOfflineNameList',
                        params: {
                            ids: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.offlinenamelistgrid.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },

    reDownload: function () {
        var me = this;
        if (me.offlinenamelistgrid.getSelection().length === 0) {
            toast('请选择需重下的人员...');
            return;
        }
        var ids = [];
        Ext.each(me.offlinenamelistgrid.getSelection(), function(item, index) {
            ids.push(item.get('id'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要重下 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Consume/OfflineNameList/reDownload',
                        params: {
                            ids: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.offlinenamelistgrid.getStore().reload();
                                toast('重下成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    }
});
