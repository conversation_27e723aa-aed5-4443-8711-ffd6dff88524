Ext.define('CamPus.view.consume.Incomeexpensetracker.IncomeExpenseTrackerView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.IncomeExpenseTrackerView",
    controller: 'IncomeExpenseTrackerController',
    requires: [
        'CamPus.view.consume.Incomeexpensetracker.IncomeExpenseTrackerStore',
        'CamPus.view.consume.Incomeexpensetracker.IncomeExpenseTrackerController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.classtreestore = Ext.create('CamPus.view.face.devface.ClassTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 220,
                hideLabel: true,
                paramName: 'key',
                store: me.classtreestore,
                emptyText: window.applang.get("orgname2") + '关键词...'
            },'->']
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            itemId: 'orgtree',
            title: '所属单位',
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.classtreestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true,
                listeners: {
                    // 当点击树节点时触发
                    itemclick: function (view, record) {
                        // 获取树的选择模型
                        var treeSelectionModel = view.getSelectionModel();

                        // 取消其他节点的选中状态
                        treeSelectionModel.deselectAll();

                        // 选中当前点击的节点
                        treeSelectionModel.select(record);
                    }
                }
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            setPathName: function (node) {
                this.pathName = (this.pathName ? node.get('name') + '/' + this.pathName : node.get('name'));
                if (node.parentNode && node.parentNode.id != 'root') {
                    this.setPathName(node.parentNode);
                }
            }
        });

        // 给 me.tree 添加事件处理程序
        me.tree.on('itemclick', function (view, record) {
            // 获取 me.persongrid 的选择模型
            var personGridSelectionModel = me.persongrid.getSelectionModel();
            // 如果 me.persongrid 已选中，则取消其选中状态
            personGridSelectionModel.deselectAll();
        });

        me.personStore = Ext.create('CamPus.view.consume.Incomeexpensetracker.PersonStore', {});

        var persontbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'combobox',
                itemId: 'viewleave',
                queryMode: 'local',
                displayField: 'name',
                valueField: 'value',
                editable: false,
                value: 1,
                width: 80,
                store: Ext.create('Ext.data.Store', {
                    fields: ['value', 'name'],
                    data: [
                        { "value": -1, "name": "全部" },
                        { "value": 1, "name": "在职" },
                        { "value": 0, "name": "离职" }
                    ]
                }),
                listeners: {
                    change: function () {
                        me.personStore.reload();
                    }
                }
            },{
                xtype: 'searchfield',
                itemId: 'searchKey',
                hideLabel: true,
                paramName: 'key',
                flex: 1,
                store: me.personStore,
                emptyText: window.applang.get("infocode") + '、姓名'
            },
            {
                xtype: 'button',
                text: '读卡',
                itemId: 'readCard',
                iconCls: 'x-fa fa-microchip',
                hidden: CamPus.AppSession.cardtype == 'id',
                handler: function () {
                    window.onReadCard(function (identid, CardNO) {
                        var searchKey = me.down('searchfield[itemId=searchKey]');
                        searchKey.setValue(CardNO);
                        searchKey.onSearchClick();
                    });
                }
            }]
        });

        me.persongrid = Ext.create('Ext.grid.Panel', {
            title: '人员信息',
            itemId: 'Personlist',
            multiColumnSort: true,
            border: false,
            region: 'west',
            collapsible: false,
            rowLines: true,
            columnLines: true,
            tbar: persontbar,
            store: me.personStore,
            // selModel: 'checkboxmodel',
            viewConfig: {
                enableTextSelection: false
                // listeners: {
                //     refresh: function (view, eOpts) {
                //         view.select(0);
                //     }
                // }
            },
            columns: [
                {
                    text: window.applang.get("infocode"),
                    dataIndex: 'code',
                    width: 80,
                    sortable: true
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    minWidth: 100,
                    flex: 1,
                    sortable: false
                },
                {
                    text: '状态',
                    dataIndex: 'status',
                    width: 55,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return '正常';
                        } else {
                            metaData.style += 'color:red;';
                            return '无效';
                        }
                    }
                }
            ],
            width: 320,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.persongrid.on('itemclick', function (view, record) {
                // 获取 me.tree 的选择模型
                var treeSelectionModel = me.tree.getSelectionModel();
                // 取消 me.tree 的节点选中状态
                treeSelectionModel.deselectAll();
            }
        )

        me.store = Ext.create('CamPus.view.consume.Incomeexpensetracker.IncomeExpenseTrackerStore', {

        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'datefield',
                    itemId: 'startdate',
                    width: 125,
                    // value: Ext.Date.add(new Date(Ext.Date.format(new Date(), 'Y/m/d')), Ext.Date.DAY, -7),
                    value: Ext.Date.format(new Date(), 'Y-m-d'),
                    format: 'Y-m-d',
                    listeners: {
                        change: function (field) {
                            me.store.reload();
                        }
                    }
                },
                {
                    xtype: 'datefield',
                    width: 145,
                    fieldLabel: '至',
                    labelWidth: 20,
                    labelSeparator: '',
                    itemId: 'enddate',
                    value: Ext.Date.format(new Date(), 'Y-m-d'),
                    format: 'Y-m-d',
                    listeners: {
                        change: function (field) {
                            me.store.reload();
                        }
                    }
                },
                {
                    xtype: 'checkboxfield',
                    boxLabel: '显示子节点人员',
                    inputValue: 1,
                    itemId: 'viewchild',
                    checked: true,
                    listeners: {
                        change: function () {
                            me.store.reload();
                        }
                    }
                }, '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.store.reload();
                    }
                }
            ]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            //title: '收支汇总表',
            region: 'center',
            itemId: 'CardManageGrid',
            tbar: tbar,
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: 'uid',
                    dataIndex: 'infoId',
                    width: 100,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'infoName',
                    width: 100,
                    sortable: false
                }, {
                    text: '工号',
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '部门',
                    dataIndex: 'departName',
                    width: 150,
                    sortable: false
                },
                {
                    text: '充值',
                    dataIndex: 'chargeSum',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === null || value === undefined) {
                            value = 0;
                        }
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '绩效补贴',
                    dataIndex: 'performanceRewardSum',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === null || value === undefined) {
                            value = 0;
                        }
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '公司补贴',
                    dataIndex: 'companyAllowanceSum',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === null || value === undefined) {
                            value = 0;
                        }
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '主钱包消费',
                    dataIndex: 'consumeMainWalletSum',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === null || value === undefined) {
                            value = 0;
                        }
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                {
                    text: '副钱包消费',
                    dataIndex: 'consumeSubWalletSum',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === null || value === undefined) {
                            value = 0;
                        }
                        return Ext.String.format('￥{0}元', value);
                    }
                },
                { text: '退款', dataIndex: 'refundSum', width: 100, sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === null || value === undefined) {
                            value = 0;
                        }
                        return Ext.String.format('￥{0}元', value);
                    }},
                { text: '充值退款', dataIndex: 'refundCharge', width: 100, sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === null || value === undefined) {
                            value = 0;
                        }
                        return Ext.String.format('￥{0}元', value);
                    }},
                { text: '消费金额', dataIndex: 'consumeSum', minWidth:100,flex: 1, sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === null || value === undefined) {
                            value = 0;
                        }
                        return Ext.String.format('￥{0}元', value);
                    }},
                { text: '卡余额', dataIndex: 'sum', minWidth:100,flex: 1, sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === null || value === undefined) {
                            value = 0;
                        }
                        return Ext.String.format('￥{0}元', value);
                    }}
            ],
            selModel: 'rowmodel',
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [{
                xtype: 'tabpanel',
                region: 'west',
                width: 356,
                collapsible: false,
                items: [
                    me.persongrid,
                    me.tree
                ]
            }, me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});














