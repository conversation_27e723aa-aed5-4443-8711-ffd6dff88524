Ext.define('CamPus.view.workflow.workflowcfg.WorkflowCfgView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.WorkflowCfgView",
    controller: 'WorkflowCfgController',
    requires: [
        'CamPus.view.workflow.workflowcfg.WorkflowCfgStore',
        'CamPus.view.workflow.workflowcfg.WorkflowCfgController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        me.mouldStore = Ext.create('CamPus.view.workflow.workflowcfg.WorkflowMouldStore', {

        });

        var mouldtbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 160,
                hideLabel: true,
                paramName: 'key',
                store: me.mouldStore,
                emptyText: '模板名称关键词...'
            },'->']
        });

        Ext.each(config.opbutton, function (item, i) {
            if (item.itemId == 'addmould' || item.itemId == 'editmould' || item.itemId == 'delmould') {
                mouldtbar.addMaxItems(item, config.maxopbutton);
            }
        });

        me.mouldlist = Ext.create('Ext.grid.Panel', {
           // title: '审批流模板',
            itemId: 'mouldlist',
            region: 'west',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            rowLines: true,
            columnLines: true,
            width: 400,
            store: me.mouldStore,
            tbar: mouldtbar,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '名称',
                    dataIndex: 'mouldname',
                    width: 100,
                    sortable: false
                },
                {
                    text: '模板类型',
                    dataIndex: 'mouldtypename',
                    width: 100,
                    minWidth: 100,
                    flex: 1,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.nodeStore = Ext.create('CamPus.view.workflow.workflowcfg.WorkflowCfgStore', {

        });

        var nodetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: ['->',
            {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.nodeStore.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            if (item.itemId == 'addnode' ||
            item.itemId == 'editnode' ||
            item.itemId == 'delnode' ||
            item.itemId == 'selapprover') {
                nodetbar.addMaxItems(item, config.maxopbutton);
            }
        });

        me.nodelist = Ext.create('Ext.grid.Panel', {
           // title: '审核节点',
            region: 'center',
            itemId: 'nodelist',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            selModel: 'checkboxmodel',
            store: me.nodeStore,
            tbar: nodetbar,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '审核等级',
                    dataIndex: 'nodelevel',
                    width: 150,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        // if(value == 0){
                        //     if(store.getData().items.length > 1){
                        //         value = store.getData().items[1].getData().nodelevel + 1;
                        //     }else {
                        //         value = store.getData().items[0].getData().nodelevel + 1;
                        //     }
                        // }
                        return value+'级审核'
                    }
                },
                {
                    text: '审核类型',
                    dataIndex: 'audittype',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false,
                    hidden: true,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === 1) {
                            return '单人确认';
                        } else {
                            return '多人确认';
                        }
                    }
                },
                {
                    text: '审核行为',
                    dataIndex: 'behaviorStatus',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 0) {
                            return '默认';
                        } else if (value == 1) {
                            return '上下级审核';
                        } else if (value == 2){
                            return '指定区域审核';
                        }else if (value == 3){
                            return '组织审核';
                        }else if (value == 4){
                            return '指定人审核';
                        }else if (value == 5){
                            return '指定部门领导审核';
                        }
                    }
                },
                {
                    text: '是否最后一级',
                    dataIndex: 'isfinal',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value === 1) {
                            return '是';
                        } else {
                            return '否';
                        }
                    }
                },
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.mouldlist, me.nodelist]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.workflow.workflowcfg.WorkflowCfgWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置模板',
    width: 500,
    height: 200,
    defaults: {
        collapsible: false,
        split: true
    },
    maximizable: false,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right',
                    labelWidth: 100
                },
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'uid',
                    value: config.uid
                },
                {
                    xtype: 'textfield',
                    fieldLabel: '名称',
                    hideTrigger: true,
                    allowBlank: false,
                    itemId: 'mouldname',
                    name: 'mouldname'
                },
                {
                    xtype: 'comboxdictionary',
                    fieldLabel: '模板类型',
                    itemId: 'mouldtype',
                    name: 'mouldtype',
                    groupCode: 'SYS0000056',
                    defaultSelectFirst: true
                    // hidden:true
                }]
            }]
        });
        me.callParent(arguments);

    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Workflow/Mould/saveMould',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.mouldlist.getStore().reload();
                        me.close();
                        toast('保存成功！！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.workflow.workflowcfg.workflowNodeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置节点',
    width: 500,
    height: 250,
    defaults: {
        collapsible: false,
        split: true
    },
    maximizable: false,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right',
                    labelWidth: 100
                },
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'uid',
                    value: config.uid
                },
                {
                    xtype: 'hiddenfield',
                    name: 'mouldid',
                    value: config.mouldid
                },
                {
                    xtype: 'numberfield',
                    itemId: 'nodelevel',
                    fieldLabel: '审核等级',
                    name: 'nodelevel',
                    minValue: 1,
                    value: 1,
                    maxValue: 10,
                    allowBlank: false
                },
                // {
                //     xtype: 'numberfield',
                //     itemId: 'auditduration',
                //     name: 'auditduration',
                //     fieldLabel: '审核时长(分钟)',
                //     emptyText: '申请单超时挂起，为0时不限制',
                //     minValue: 0,
                //     value: 0,
                //     allowBlank: false,
                //     hidden:true
                // },
                {
                    xtype: 'combobox',
                    itemId: 'audittype',
                    name: 'audittype',
                    fieldLabel: '审核类型',
                    queryMode: 'local',
                    displayField: 'name',
                    valueField: 'value',
                    value: 1,
                    width: 120,
                    editable: false,
                    hidden: true,
                    store: Ext.create('Ext.data.Store', {
                        fields: ['value', 'name'],
                        data: [
                            { value: 1, name: "单人确认" }
                            // { value: 2, name: "多人确认" }
                        ]
                    })
                },
                    {
                        xtype: 'combobox',
                        itemId: 'behaviorStatus',
                        name: 'behaviorStatus',
                        fieldLabel: '审核行为',
                        queryMode: 'local',
                        displayField: 'name',
                        valueField: 'value',
                        value: 1,
                        width: 120,
                        editable: false,
                        store: Ext.create('Ext.data.Store', {
                            fields: ['value', 'name'],
                            data: [
                                { value: 0, name: "默认节点审核" },
                                { value: 1, name: "上下级审核" },
                                { value: 2, name: "指定组审核" },
                                { value: 3, name: "组织审核" },
                                { value: 4, name: "指定人审核" },
                                { value: 5, name: "指定部门领导审核" }
                            ]
                        })
                    }
                    // {
                    //     xtype: 'combobox',
                    //     itemId: 'isFinal',
                    //     name: 'isFinal',
                    //     fieldLabel: '是否为最后一级',
                    //     queryMode: 'local',
                    //     displayField: 'name',
                    //     valueField: 'value',
                    //     value: 1,
                    //     width: 120,
                    //     editable: false,
                    //     store: Ext.create('Ext.data.Store', {
                    //         fields: ['value', 'name'],
                    //         data: [
                    //             { value: 0, name: "否" },
                    //             { value: 1, name: "是" }
                    //         ]
                    //     })
                    // }
                ]
            }]
        });
        me.callParent(arguments);

    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');

        if (form.getForm().isValid()) {
            form.submit({
                url: '/Workflow/Node/saveNode',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.nodelist.getStore().reload();
                        me.close();
                        toast('保存成功！！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.workflow.workflowcfg.WorkflowApprovarWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '节点审核人',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    constructor: function (config) {
        var me = this;

        Ext.apply(me, config);

        me.approvarStore = Ext.create('CamPus.view.workflow.workflowcfg.WorkflowApproverStore', {
            listeners: {
                beforeload: function (store) {
                    Ext.apply(store.proxy.extraParams, {
                        nodeid: config.nodeid
                    });
                }
            }
        });

        var approvartbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [
                {
                    xtype: 'searchfield',
                    width: 300,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.approvarStore,
                    emptyText: '姓名、工号关键词...'
                },
                '->',
                {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.approvarStore.reload();
                    }
                },
                {
                    xtype: 'button',
                    itemId: 'addapprover',
                    iconCls: 'x-fa fa-plus-square-o',
                    text: '新增审核人',
                    handler: function () {
                        var win = Ext.create('CamPus.view.workflow.workflowcfg.AddApprovarWindow', {
                            autoSize: true,
                            nodeid: me.nodeid,
                            approvarGrid: me.approvarGrid,
                            personType: 1,
                            status: 0
                        });
                        win.show();
                    }
                },
                {
                    xtype: 'button',
                    itemId: 'delapprovar',
                    iconCls: 'x-fa fa-trash-o',
                    text: '删除审核人',
                    handler: function () {
                        me.delApprovar();
                    }
                },
                {
                    xtype: 'button',
                    itemId: 'setdefaultapprover',
                    iconCls: 'x-fa fa-trash-o',
                    text: '设置默认审核人',
                    handler: function () {
                        me.setdefaultapprover();
                    }
                }
            ]
        });

        me.approvarGrid = Ext.create('Ext.grid.Panel', {
            title: '审核人',
            tbar: approvartbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.approvarStore,
            viewConfig: {
                enableTextSelection: true,
                getRowClass: function () {
                    return 'x-selectable';
                },
                // listeners: {
                //     refresh: function (view, eOpts) {
                //         view.select(0);
                //     }
                // }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '工号',
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '职位',
                    dataIndex: 'positionname',
                    width: 85,
                    sortable: false
                },
                {
                    text: window.applang.get('orgname'),
                    dataIndex: 'orgname',
                    minWidth: 240,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
            // ,
            // listeners: {
            //     select: function () {
            //         me.carrierStore.loadPage(1);
            //     }
            // }
        });
        Ext.apply(me, {
            items: [me.approvarGrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        me.close();
    },
    delApprovar: function(){
        var me = this;
        if (me.approvarGrid.getSelection().length == 0) {
            toast('请选择删除的主要审核人...');
            return;
        }
        var uid = [];
        Ext.each(me.approvarGrid.getSelection(), function (record, index) {
            uid.push(record.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Workflow/Approver/delApprover',
                        params: {
                            uid: uid
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.approvarGrid.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    setdefaultapprover: function(){
        var me = this;
        if (me.approvarGrid.getSelection().length == 0) {
            toast('请选择设置的默认审核人...');
            return;
        }
        var uid = [];
        Ext.each(me.approvarGrid.getSelection(), function (record, index) {
            uid.push(record.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认设置默认审核人 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/Workflow/Approver/setDefaultApprover',
                        params: {
                            uid: uid
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.approvarGrid.getStore().reload();
                                toast('设置成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    }
});


Ext.define('CamPus.view.workflow.workflowcfg.AddApprovarWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择人员',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.workflow.workflowcfg.OrgTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get('orgname3') + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function (tree, record, index, eOpts) {
                    me.store.loadPage(1);
                }
            }
        });

        me.store = Ext.create('CamPus.view.workflow.workflowcfg.TechInfoStore', {
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });

                    Ext.apply(store.proxy.extraParams, {
                        groupid: me.groupid,
                        orgcode: orgcode,
                        infotype: infotype.getValue(),
                        viewchild: viewchild.getValue(),
                        selecteduid: JSON.stringify(selecteduid)
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'infotype',
                groupCode: 'SYS0000019',
                insertAll: '0|全部',
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        var url = '/Card/OrgFramework/getCollegeClassTree';
                        if (newValue == '0') {
                            url = '/Card/OrgFramework/getAllOrgTree';
                        }
                        else if (newValue == '2') {
                        } else {
                            url = '/Card/OrgFramework/getOrgTree';
                        }
                        me.treestore.proxy.url = url;
                        me.treestore.load();
                        me.store.load();
                    }
                }
            },
            {
                xtype: 'searchfield',
                width: 150,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '关键词...'
            }, {
                xtype: 'checkboxfield',
                boxLabel: '显示子节点人员',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }]
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '性别',
                    dataIndex: 'sex',
                    width: 65,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '男';
                        } else if (value == 2) {
                            return '女';
                        }
                    }
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            },
            selModel: 'checkboxmodel'
        });

        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选人员：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.store.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['card', 'cardsn', 'name', 'infotype', 'uid', 'code', 'orgname', 'orgcode'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 220
        });

        Ext.apply(me, {
            items: [me.tree, me.grid, me.selectgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.selectgrid.getStore().getData().items.length == 0) {
            toast('请选择人员信息，选择以后拖拽至右侧表格');
            return;
        }
        var infoid = [];
        Ext.each(me.selectgrid.getStore().getData().items, function (item, i) {
            infoid.push(item.get('uid'));
        });
        var url = '';
        //传入人员类型至添加人员窗口，类型为1时保存添加主审核人，类型为2时保存添加副审核人
        if(me.personType == 1){
            url = '/Workflow/Approver/saveApprover';
        }else {
            url = '/Workflow/ApproverCarrier/saveCarrier';
        }
        Ext.Ajax.request({
            url: url,
            params: {
                listInfoid: infoid.join(','),
                nodeid: me.nodeid,
                reviewerid: me.reviewerid,
                mouldid: me.mouldid,
                status :me.status
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if(me.personType == 1){
                        me.approvarGrid.getStore().reload();
                    }else {
                        me.carrierrGrid.getStore().reload();
                    }
                    toast('保存成功...');
                    me.close();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});
