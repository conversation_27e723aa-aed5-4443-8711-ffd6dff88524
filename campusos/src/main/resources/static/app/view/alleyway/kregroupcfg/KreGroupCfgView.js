Ext.define('CamPus.view.alleyway.kregroupcfg.KreGroupCfgView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.KreGroupCfgView",
    controller: 'KreGroupCfgController',
    requires: [
        'CamPus.view.alleyway.kregroupcfg.KreGroupCfgStore',
        'CamPus.view.alleyway.kregroupcfg.KreGroupCfgController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;

        // me.treestore = Ext.create('CamPus.view.dev.device.AreaTreeStore', {
        //
        // });
        //
        // var areatreetbar = Ext.create('CamPus.view.ux.Toolbar', {
        //     items: [{
        //         xtype: 'searchfield',
        //         flex: 1,
        //         hideLabel: true,
        //         paramName: 'key',
        //         store: me.treestore,
        //         emptyText: '区域、楼宇、房间名称关键词...'
        //     }]
        // });
        //
        // me.tree = Ext.create('Ext.tree.Panel', {
        //     //title: '设备区域',
        //     itemId: 'areatree',
        //     tbar: areatreetbar,
        //     region: 'west',
        //     collapsible: false,
        //     multiColumnSort: false,
        //     border: false,
        //     columnLines: true,
        //     rowLines: true,
        //     columnLines: true,
        //     store: me.treestore,
        //     rootVisible: false,
        //     reserveScrollbar: true,
        //     useArrows: true,
        //     multiSelect: false,
        //     singleExpand: true,
        //     width: 280,
        //     defaultSelect: '',
        //     defaultSelectCode: [],
        //     defaultAutoInsert: 0,
        //     viewConfig: {
        //         enableTextSelection: true,
        //         listeners: {
        //             refresh: function (view, eOpts) {
        //                 view.select(0);
        //             }
        //         }
        //     },
        //     columns: [{
        //         xtype: 'treecolumn',
        //         text: '名称',
        //         dataIndex: 'name',
        //         flex: 1,
        //         sortable: false
        //     }]
        // });

        me.groupStore = Ext.create('CamPus.view.alleyway.kregroupcfg.KreGroupStore', {

        });

        var grouptbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 160,
                hideLabel: true,
                paramName: 'key',
                store: me.groupStore,
                emptyText: '权限组名称关键词...'
            }, '->']
        });

        Ext.each(config.opbutton, function (item, i) {
            if (item.itemId != 'adddoor' && item.itemId != 'editdoor' && item.itemId != 'deldoor') {
                grouptbar.addMaxItems(item, config.maxopbutton);
            }
        });

        me.grouplist = Ext.create('Ext.grid.Panel', {
            //title: '权限组',
            itemId: 'grouplist',
            region: 'west',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            rowLines: true,
            columnLines: true,
            width: 400,
            store: me.groupStore,
            tbar: grouptbar,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '权限组名称',
                    dataIndex: 'name',
                    width: 100,
                    minWidth: 100,
                    flex: 1,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.groupcfgDoorStore = Ext.create('CamPus.view.alleyway.kregroupcfg.KreGroupCfgStore', {

        });

        var groupcfgDoortbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.groupcfgDoorStore,
                emptyText: '门名称关键词...'
            }, '->',
            {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.groupcfgDoorStore.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            if (item.itemId == 'adddoor' || item.itemId == 'editdoor' || item.itemId == 'deldoor') {
                groupcfgDoortbar.addMaxItems(item, config.maxopbutton);
            }
        });

        me.groupcfgDoorlist = Ext.create('Ext.grid.Panel', {
            //title: '权限组门配置',
            region: 'center',
            itemId: 'groupcfgDoorlist',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            selModel: 'checkboxmodel',
            store: me.groupcfgDoorStore,
            tbar: groupcfgDoortbar,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '权限组名称',
                    dataIndex: 'groupname',
                    width: 150,
                    sortable: false
                },
                {
                    text: '门',
                    dataIndex: 'doorname',
                    width: 150,
                    sortable: false
                },
                {
                    text: '周计划编号',
                    dataIndex: 'weekindex',
                    width: 90,
                    sortable: false
                },
                {
                    text: '权限',
                    dataIndex: 'limits',
                    width: 90,
                    sortable: false,
                    renderer: function (value) {
                        if (value === 0) {
                            return '普通卡'
                        } else if (value === 1) {
                            return '首卡'
                        } else if (value === 2) {
                            return '常开卡'
                        } else if (value === 3) {
                            return '巡更卡'
                        } else if (value === 4) {
                            return '防盗设置卡'
                        } else {
                            return ''
                        }
                    }
                },
                {
                    text: '有效时间',
                    dataIndex: 'endtime',
                    width: 120,
                    sortable: false
                },
                {
                    text: '有效次数',
                    dataIndex: 'effectivetimes',
                    width: 90,
                    sortable: false,
                    renderer: function (value) {
                        if (value == 65535) {
                            return '不限次'
                        }
                        return value;
                    }
                },
                {
                    text: '添加时间',
                    dataIndex: 'createdate',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.grouplist, me.groupcfgDoorlist]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.alleyway.kregroupcfg.AddGroupCfgWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '设置权限组',
    width: 300,
    height: 150,
    defaults: {
        collapsible: false,
        split: true
    },
    maximizable: false,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        Ext.apply(me, {
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelAlign: 'right',
                    labelWidth: 100
                },
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'uid',
                    value: config.uid
                },
                {
                    xtype: 'textfield',
                    fieldLabel: '权限组名称',
                    hideTrigger: true,
                    allowBlank: false,
                    name: 'name'
                }]
            }]
        });
        me.callParent(arguments);

    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');
        form.on('beforeaction', function (form, action) {
            if (action.type === 'submit') {
                // 在提交操作中附加额外参数
                action.params = action.params || {};
                action.params.areacode = me.areacode;
            }
        });
        if (form.getForm().isValid()) {
            form.submit({
                url: '/alleyway/KreGroupCfg/saveGroup',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        me.grouplist.getStore().reload();
                        me.close();
                        toast('保存成功！！');
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    toast(action.result.msg);
                }
            });
        }
    }
});

Ext.define('CamPus.view.alleyway.kregroupcfg.AddDoorhWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '添加门',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    autoSize: true,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        // 授权表单(左)
        me.getAreaTreeStore = Ext.create('CamPus.view.alleyway.kregroupcfg.AreaTreeStore', {

        });

        var areatreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.getAreaTreeStore,
                emptyText: '区域、楼宇、房间名称关键词...'
            }]
        });


        me.areaTree = Ext.create('Ext.tree.Panel', {
            itemId: 'areatree',
            tbar: areatreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.getAreaTreeStore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            defaultSelect: '',
            width: 280,
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function () {
                    me.devdoorstore.loadPage(1);
                }
            }
        });

        //授权表单(中)
        me.devdoorstore = Ext.create('CamPus.view.alleyway.kregroupcfg.DoorStore', {
            listeners: {
                beforeload: function (store) {
                    var areacode = '';
                    if (me.areaTree.getSelection().length > 0) {
                        areacode = me.areaTree.getSelection()[0].get('code');
                    }
                    var viewchild = true;
                    if (me.devgrid) {
                        viewchild = me.devgrid.down('checkboxfield[itemId=viewchild]').getValue();
                    }

                    Ext.apply(store.proxy.extraParams, {
                        areacode: areacode,
                        viewchild: viewchild,
                        groupid: me.groupid
                    });
                }
            }
        });

        var devdoortbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 160,
                hideLabel: true,
                paramName: 'key',
                store: me.devdoorstore,
                emptyText: '名称关键词...'
            }, {
                xtype: 'checkboxfield',
                boxLabel: '显示子区域门',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.devdoorstore.reload();
                    }
                }
            }, '->',
            {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.devdoorstore.reload();
                }
            }
            ]
        });

        me.devgrid = Ext.create('Ext.grid.Panel', {
            itemId: 'DoorGrid',
            tbar: devdoortbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.devdoorstore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'rownumberer'
            },
            {
                text: '机号',
                dataIndex: 'machineid',
                width: 85,
                sortable: false
            },
            {
                text: '设备名称',
                dataIndex: 'devname',
                width: 240,
                sortable: false
            },
            {
                text: '门名称',
                dataIndex: 'doorname',
                width: 150,
                flex: 1,
                minWidth: 150,
                sortable: false
            }],
            selModel: 'checkboxmodel',
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(me, {
            items: [me.areaTree, me.devgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var devdata = [];
        if (me.devgrid.getSelection().length == 0) {
            toast('请选择门');
            return;
        }
        Ext.each(me.devgrid.getSelection(), function(item, index) {
            devdata.push({
                devid: item.get('uid'),
                relays: item.get('relays')
            });
        });
        var win = Ext.create('CamPus.view.alleyway.kregroupcfg.KreGroupCfgTimeSchemeWindow', {
            groupcfgDoorlist: me.groupcfgDoorlist,
            isEdit: false,
            parentWin: me,
            groupid: me.groupid,
            devdata: JSON.stringify(devdata)
        });
        win.show();
    }
});

Ext.define('CamPus.view.alleyway.kregroupcfg.KreGroupCfgTimeSchemeWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '添加时段',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: false,
        split: true
    },
    width: 650,
    height: 350,
    maximizable: false,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.formpanel = Ext.create('Ext.panel.Panel', {
            region: 'center',
            collapsible: false,
            bodyPadding: '10',
            layout: {
                type: 'vbox',
                pack: 'start',
                align: 'stretch'
            },
            items: [{
                xtype: 'form',
                itemId: 'form',
                border: false,
                bodyPadding: 10,
                defaults: {
                    anchor: '100%',
                    labelWidth: 100,
                    labelAlign: 'right'
                },
                layout: 'anchor',
                defaultType: 'textfield',
                items: [{
                    xtype: 'hiddenfield',
                    name: 'uid',
                    value: config.uid
                },
                {
                    xtype: 'hiddenfield',
                    name: 'groupid',
                    value: config.groupid
                },
                {
                    xtype: 'hiddenfield',
                    name: 'devdata',
                    value: config.devdata
                },
                {
                    xtype: 'radiogroup',
                    itemId: 'limittype',
                    fieldLabel: '是否限次',
                    items: [
                        { name: 'limittypevalue', boxLabel: '限次', inputValue: 1},
                        { name: 'limittypevalue', boxLabel: '不限次', inputValue: 0,checked: true}
                    ],
                    listeners: {
                        change: function (change, newValue, oldValue, eOpts) {
                            if (newValue.limittypevalue === 1){
                               me.down('numberfield[itemId=effectivetimes]').setHidden(false);
                               me.down('numberfield[itemId=effectivetimes]').setValue(0);
                            }else {
                                me.down('numberfield[itemId=effectivetimes]').setHidden(true);
                                me.down('numberfield[itemId=effectivetimes]').setValue(65535);
                            }
                        }
                    }
                },
                {
                    xtype: 'numberfield',
                    itemId: 'effectivetimes',
                    fieldLabel: '有效次数',
                    name: 'effectivetimes',
                    minValue: 0,
                    value: 65535,
                    maxValue: 65535,
                    hidden: true,
                    allowBlank: false
                },
                {
                    xtype: 'datetimefield',
                    itemId: 'endtime',
                    fieldLabel: '有效时间',
                    name: 'endtime',
                    value: new Date(Ext.Date.format(new Date(), '2050-1-1 H:i:s')),
                    format: 'Y-m-d',
                    allowBlank: false
                },
                {
                    xtype: 'radiogroup',
                    fieldLabel: '特殊权限',
                    items: [
                        { name: 'limits', boxLabel: '普通卡', inputValue: 0, checked: true },
                        { name: 'limits', boxLabel: '首卡', inputValue: 1 },
                        { name: 'limits', boxLabel: '常开卡', inputValue: 2 },
                        { name: 'limits', boxLabel: '巡更卡', inputValue: 3 },
                        { name: 'limits', boxLabel: '防盗设置卡', inputValue: 4 }]
                },
                    {
                        xtype: 'combobox',
                        itemId: 'weekplanid',
                        name: 'weekplanid',
                        fieldLabel: '通行时段',
                        width: 120,
                        defaultSelectFirst: true,
                        allowBlank: false,
                        displayField: 'name', // 指定显示值字段为 'name'
                        valueField: 'weekplanid', // 指定实际值字段为 'weekplanid'
                        store: {
                            type: 'store',
                            fields: ['weekplanid', 'name'],
                            proxy: {
                                type: 'ajax',
                                url: '/alleyway/KreGroupCfg/queryweek',
                                method: 'POST',
                                reader: {
                                    type: 'json',
                                    rootProperty: 'data'
                                },
                                extraParams: {
                                    devdata: me.devdata,
                                    uid: me.uid,
                                }
                            },
                            autoLoad: true
                        }
                    }
                ]
            }]
        });

        Ext.apply(me, {
            items: [me.formpanel]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        var form = me.down('form[itemId=form]');

        if (form.getForm().isValid()) {
            form.submit({
                url: '/alleyway/KreGroupCfg/saveGroupCfg',
                timeout: 6000000,
                waitTitle: '系统信息',
                waitMsg: '提交数据中...',
                submitEmptyText: false,
                success: function (form, action) {
                    if (action.result.success) {
                        toast('保存成功！');
                        me.groupcfgDoorlist.getStore().reload();
                        if(!me.isEdit){
                            me.parentWin.close()
                        }
                        me.close();
                    } else {
                        toast(action.result.msg);
                    }
                },
                failure: function (form, action) {
                    me.groupcfgDoorlist.getStore().reload();
                    if(!me.isEdit){
                        me.parentWin.close()
                    }
                    me.close();
                    toast(action.result.msg);
                }
            });
        }
        
    }
});