Ext.define('CamPus.view.alleyway.kregroupaccess.KreGroupAccessView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.KreGroupAccessView",
    controller: 'KreGroupAccessController',
    requires: [
        'CamPus.view.alleyway.kregroupaccess.KreGroupAccessStore',
        'CamPus.view.alleyway.kregroupaccess.KreGroupAccessController'
    ],
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    bodyPadding: '10',
    border: false,
    scrollable: false,
    constructor: function (config) {
        var me = this;


        // me.treestore = Ext.create('CamPus.view.dev.device.AreaTreeStore', {
        //
        // });
        //
        // var areatreetbar = Ext.create('CamPus.view.ux.Toolbar', {
        //     items: [{
        //         xtype: 'searchfield',
        //         flex: 1,
        //         hideLabel: true,
        //         paramName: 'key',
        //         store: me.treestore,
        //         emptyText: '区域、楼宇、房间名称关键词...'
        //     }]
        // });
        //
        // me.tree = Ext.create('Ext.tree.Panel', {
        //     //title: '设备区域',
        //     itemId: 'areatree',
        //     tbar: areatreetbar,
        //     region: 'west',
        //     collapsible: false,
        //     multiColumnSort: false,
        //     border: false,
        //     columnLines: true,
        //     rowLines: true,
        //     columnLines: true,
        //     store: me.treestore,
        //     rootVisible: false,
        //     reserveScrollbar: true,
        //     useArrows: true,
        //     multiSelect: false,
        //     singleExpand: true,
        //     width: 280,
        //     defaultSelect: '',
        //     defaultSelectCode: [],
        //     defaultAutoInsert: 0,
        //     viewConfig: {
        //         enableTextSelection: true,
        //         listeners: {
        //             refresh: function (view, eOpts) {
        //                 view.select(0);
        //             }
        //         }
        //     },
        //     columns: [{
        //         xtype: 'treecolumn',
        //         text: '名称',
        //         dataIndex: 'name',
        //         flex: 1,
        //         sortable: false
        //     }]
        // });

        me.groupStore = Ext.create('CamPus.view.alleyway.kregroupaccess.KreGroupStore', {

        });

        var grouptbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 160,
                hideLabel: true,
                paramName: 'key',
                store: me.groupStore,
                emptyText: '权限组名称关键词...'
            }]
        });

        me.grouplist = Ext.create('Ext.grid.Panel', {
            //title: '权限组',
            itemId: 'grouplist',
            region: 'west',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            rowLines: true,
            columnLines: true,
            width: 400,
            store: me.groupStore,
            tbar: grouptbar,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '权限组名称',
                    dataIndex: 'name',
                    width: 100,
                    minWidth: 100,
                    flex: 1,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        me.groupAccessStore = Ext.create('CamPus.view.alleyway.kregroupaccess.KreGroupAccessStore', {

        });

        var groupAccesstbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 200,
                hideLabel: true,
                paramName: 'key',
                store: me.groupAccessStore,
                emptyText: '门名称关键词...'
            }, '->',
            {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.groupAccessStore.reload();
                }
            }]
        });

        Ext.each(config.opbutton, function (item, i) {
            if (item.itemId == 'add' || item.itemId == 'del') {
                groupAccesstbar.addMaxItems(item, config.maxopbutton);
            }
        });

        me.groupAccesslist = Ext.create('Ext.grid.Panel', {
            //title: '权限组授权',
            region: 'center',
            itemId: 'groupAccesslist',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            selModel: 'checkboxmodel',
            store: me.groupAccessStore,
            tbar: groupAccesstbar,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '权限组名称',
                    dataIndex: 'groupname',
                    width: 150,
                    sortable: false
                },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 120,
                    sortable: false
                },
                {
                    text: '姓名',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '卡号',
                    dataIndex: 'cardno',
                    width: 120,
                    sortable: false
                },
                {
                    text: '授权时间',
                    dataIndex: 'createdate',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.grouplist, me.groupAccesslist]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.alleyway.kregroupaccess.AddGroupAccWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '选择人员',
    layout: {
        type: 'border',
        pack: 'start',
        align: 'stretch'
    },
    defaults: {
        collapsible: true,
        split: true
    },
    maximizable: true,
    step: 1,
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.treestore = Ext.create('CamPus.view.alleyway.kregroupaccess.OrgTreeStore', {

        });

        var orgtreetbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                flex: 1,
                hideLabel: true,
                paramName: 'key',
                store: me.treestore,
                emptyText: window.applang.get('orgname3') + '名称关键词...'
            }]
        });

        me.tree = Ext.create('Ext.tree.Panel', {
            tbar: orgtreetbar,
            region: 'west',
            collapsible: false,
            multiColumnSort: false,
            border: false,
            columnLines: true,
            rowLines: true,
            store: me.treestore,
            rootVisible: false,
            reserveScrollbar: true,
            useArrows: true,
            multiSelect: false,
            singleExpand: true,
            width: 230,
            defaultSelect: '',
            defaultSelectCode: [],
            defaultAutoInsert: 0,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [{
                xtype: 'treecolumn',
                text: '名称',
                dataIndex: 'name',
                flex: 1,
                sortable: false
            }],
            listeners: {
                select: function (tree, record, index, eOpts) {
                    me.store.loadPage(1);
                }
            }
        });

        me.store = Ext.create('CamPus.view.alleyway.kregroupaccess.TechInfoStore', {
            listeners: {
                beforeload: function (store) {
                    var orgcode = '';
                    if (me.tree.getSelection().length > 0) {
                        orgcode = me.tree.getSelection()[0].get('code');
                    }
                    var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                    var infotype = me.down('comboxdictionary[itemId=infotype]');
                    var viewchild = me.down('checkboxfield[itemId=viewchild]');
                    var selecteduid = [];
                    Ext.each(me.selectgrid.getStore().getData().items, function (item) {
                        selecteduid.push(item.get('uid'));
                    });

                    Ext.apply(store.proxy.extraParams, {
                        groupid: me.groupid,
                        orgcode: orgcode,
                        intoyear: !intoyear.getValue() ? '0' : intoyear.getValue(),
                        infotype: infotype.getValue(),
                        viewchild: viewchild.getValue(),
                        selecteduid: JSON.stringify(selecteduid)
                    });
                }
            }
        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'comboxdictionary',
                itemId: 'infotype',
                groupCode: 'SYS0000019',
                insertAll: '0|全部',
                width: 110,
                defaultSelectFirst: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        var intoyear = me.down('comboxdictionary[itemId=intoyear]');
                        var url = '/Card/OrgFramework/getCollegeClassTree';
                        if (newValue == '0') {
                            url = '/Card/OrgFramework/getAllOrgTree';
                        }
                        else if (newValue == '2') {
                            intoyear.show();
                        } else {
                            intoyear.setValue(0);
                            intoyear.hide();
                            url = '/Card/OrgFramework/getOrgTree';
                        }
                        me.treestore.proxy.url = url;
                        me.treestore.load();
                        me.store.load();
                    }
                }
            }, {
                xtype: 'comboxdictionary',
                itemId: 'intoyear',
                groupCode: 'SYS0000004',
                insertAll: '全部',
                width: 110,
                defaultSelectFirst: true,
                hidden: true,
                listeners: {
                    change: function (combox, newValue, oldValue, eOpts) {
                        me.store.load();
                    }
                }
            }, {
                xtype: 'searchfield',
                width: 150,
                hideLabel: true,
                paramName: 'key',
                store: me.store,
                emptyText: '关键词...'
            }, {
                xtype: 'checkboxfield',
                boxLabel: '显示子节点人员',
                inputValue: 1,
                itemId: 'viewchild',
                checked: true,
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }]
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            region: 'center',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'firstGridDDGroup',
                    dropGroup: 'secondGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 100,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                // {
                //     text: '性别',
                //     dataIndex: 'sex',
                //     width: 65,
                //     sortable: false,
                //     renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                //         if (value == 1) {
                //             return '男';
                //         } else if (value == 2) {
                //             return '女';
                //         }
                //     }
                // },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            },
            selModel: 'checkboxmodel'
        });

        var selecttbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'label',
                text: '已选人员：'
            }, '->', {
                xtype: 'button',
                text: '清空',
                iconCls: 'x-fa fa-eraser',
                listeners: {
                    click: function (button, event) {
                        me.selectgrid.getStore().removeAll();
                        me.store.loadPage(1);
                    }
                }
            }]
        });

        me.selectgrid = Ext.create('Ext.grid.Panel', {
            tbar: selecttbar,
            region: 'east',
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: Ext.create('Ext.data.Store', {
                fields: ['card', 'cardsn', 'name', 'infotype', 'uid', 'code', 'orgname', 'orgcode'],
                data: []
            }),
            viewConfig: {
                enableTextSelection: true,
                plugins: {
                    ptype: 'gridviewdragdrop',
                    dragGroup: 'secondGridDDGroup',
                    dropGroup: 'firstGridDDGroup'
                },
                listeners: {
                    drop: function (node, data, dropRec, dropPosition) {
                        me.grid.getStore().reload();
                    }
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: window.applang.get('infocode'),
                    dataIndex: 'code',
                    width: 120,
                    hidden: true,
                    sortable: false
                },
                {
                    text: '名称',
                    dataIndex: 'name',
                    width: 150,
                    sortable: false
                },
                {
                    text: '所属单位',
                    dataIndex: 'orgname',
                    flex: 1,
                    minWidth: 200,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            width: 220
        });

        Ext.apply(me, {
            items: [me.tree, me.grid, me.selectgrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        if (me.selectgrid.getStore().getData().items.length == 0) {
            toast('请选择人员信息，选择以后拖拽至右侧表格');
            return;
        }
        var infoid = [];
        Ext.each(me.selectgrid.getStore().getData().items, function (item, i) {
            infoid.push(item.get('uid'));
        });
        Ext.Ajax.request({
            url: '/alleyway/KreGroupAccess/saveGroupAccess',
            params: {
                infoid: infoid.join(','),
                groupid: me.groupid
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.groupAccesslist.getStore().reload();
                    toast('保存成功...');
                    me.close();
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });
    }
});
