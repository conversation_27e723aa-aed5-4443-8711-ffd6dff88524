Ext.define('CamPus.view.alleyway.doorapplyrecord.DoorApplyRecordController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.DoorApplyRecordController',
    view: ['DoorApplyRecordView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=auditrecord]': {
                click: me.onAuditRecordClick
            },
            '*[itemId=doorauthorize]': {
                click: me.onDoorRecordClick
            },
            'button[itemId=export]': {
                click: me.onExport
            }
        });

        me.store = me.view.store;
        me.grid = me.view.grid;

        me.store.on('beforeload', function (store) {
            var starttime = me.view.down('datetimefield[itemId=starttime]');
            var endtime = me.view.down('datetimefield[itemId=endtime]');
            var status = me.view.down('combobox[itemId=statusbox]');
            Ext.apply(store.proxy.extraParams, {
                status: status.getValue(),
                starttime: Ext.Date.format(starttime.getValue(), 'Y-m-d'),
                endtime: Ext.Date.format(endtime.getValue(), 'Y-m-d'),
            });
        });
    },
    onExport: function () {
        var me = this;
        Ext.get('loadingToast').show();
        Ext.Ajax.request({
            url: '/door/apply/exportRecord',
            params:  me.store.proxy.extraParams,
            method: 'POST',
            success: function (response) {
                Ext.get('loadingToast').hide();
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    if (result.msg) {
                        openWindow(result.msg, null, 'get');
                    } else {
                        toast('无导出数据');
                    }
                } else {
                    toast(result.msg);
                }
            }
        });
    },

    onAuditRecordClick: function(){
        var me = this;
        if(me.grid.getSelection().length == 0){
            toast('请选择开门申请记录...');
            return;
        }
        if(me.grid.getSelection().length > 1){
            toast('请选择一条开门申请记录...');
            return;
        }
        var formid = me.grid.getSelection()[0].get('uid')
        var win = Ext.create('CamPus.view.alleyway.doorapplyrecord.approvalRecordWindow',{
            autoSize: true,
            grid: me.grid,
            formid: formid
        })
        win.show();
    },
    onDoorRecordClick: function(){
        var me = this;
        if(me.grid.getSelection().length == 0){
            toast('请选择开门申请记录...');
            return;
        }
        if(me.grid.getSelection().length > 1){
            toast('请选择一条开门申请记录...');
            return;
        }
        var formid = me.grid.getSelection()[0].get('uid')
        var win = Ext.create('CamPus.view.alleyway.doorapplyrecord.DoorRecordWindow',{
            autoSize: true,
            grid: me.grid,
            formid: formid
        })
        win.show();
    },



    onRetryDownClick:function () {
        var me = this;
        if(me.grid.getSelection().length === 0){
            toast('请选择实验室申请记录...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var uid = record.get('uid');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/door/delDoorAgreement',
                        params: {
                            uid: uid
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.grid.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });
    },
    onAuditDownClick11: function () {
        var me = this;
        if (me.grid.getSelection().length === 0) {
            toast('请选择就餐申请记录...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var uid = [];
        Ext.each(me.grid.getSelection(), function(item, index) {
            uid.push(item.get('uid'));
        });
        Ext.Ajax.request({
            url: '/door/apply/DoorAuditRecord',
            params: {
                uid: uid.join(',')
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.grid.getStore().reload();
                    toast('审核成功...');
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });

    },
    onAuditDownClick: function () {
        var me = this;
        if (me.grid.getSelection().length === 0) {
            toast('请选择就餐申请记录...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var uid = [];
        var alreadyReviewed = false;
        Ext.each(me.grid.getSelection(), function(item, index) {
            uid.push(item.get('uid'));
            if (item.get('status') != 0) {
                toast('选中的记录已审核，无需再审核');
                alreadyReviewed = true;  // 标志变量设为true
                return false;  // 退出循环
            }
        });

        if (alreadyReviewed) {
            return;  // 终止方法执行
        }
        Ext.onReady(function() {
            Ext.create('Ext.window.Window', {
                title: '审核窗口',
                height: 250,
                width: 500,
                layout: 'anchor',
                items: [
                    {
                        xtype: 'radiogroup',
                        fieldLabel: '请选择',
                        anchor: '100%',
                        columns: 2,
                        vertical: true,
                        items: [
                            { boxLabel: '同意', name: 'rb', inputValue: '1', style: 'white-space: nowrap;' },
                            { boxLabel: '不同意', name: 'rb', inputValue: '2', style: 'white-space: nowrap;' }
                        ]
                    },
                    {
                        xtype: 'textarea',
                        fieldLabel: '审核理由',
                        anchor: '100%',
                        height: 120
                    }
                ],
                buttons: [
                    {
                        text: '提交',
                        handler: function() {
                            var window = this.up('window');
                            var radioGroup = window.down('radiogroup');
                            var textArea = window.down('textarea');

                            var selectedRadio = radioGroup.getValue().rb; // 获取单选框值
                            var reviewReason = textArea.getValue(); // 获取文本区域值

                            Ext.Ajax.request({
                                url: '/door/apply/DoorAuditJCanRecord',
                                params: {
                                    uid: uid.join(','),
                                    type: selectedRadio,
                                    reason: reviewReason
                                },
                                method: 'POST',
                                success: function(response) {
                                    var result = Ext.JSON.decode(response.responseText);
                                    if (result.success) {
                                        me.grid.getStore().reload();
                                        window.close();
                                        toast('审核成功...');
                                    } else {
                                        Ext.Msg.alert('系统信息', result.msg);
                                    }
                                }
                            });
                        }
                    },
                    {
                        text: '取消',
                        handler: function() {
                            this.up('window').close();
                        }
                    }
                ],
                style: {
                    backgroundColor: '#f5f5f5',
                    padding: '10px'
                }
            }).show();
        });



    },
    onTurnDownClick: function () {
        var me = this;
        if (me.grid.getSelection().length === 0) {
            toast('请选择实验室申请记录...');
            return;
        }
        var record = me.grid.getSelection()[0];
        var uid = record.get('uid');

        Ext.Ajax.request({
            url: '/door/apply/DoorTurnDownRecord',
            params: {
                uid: uid
            },
            method: 'POST',
            success: function (response) {
                var result = Ext.JSON.decode(response.responseText);
                if (result.success) {
                    me.grid.getStore().reload();
                    toast('驳回成功...');
                } else {
                    Ext.Msg.alert('系统信息', result.msg);
                }
            }
        });

    }
});