Ext.define('CamPus.view.alleyway.kregroupaccess.KreGroupAccessController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.KreGroupAccessController',
    view: ['KreGroupAccessView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=grouplist]':{
                select: me.groupSelect
            },
            // 'treepanel[itemId=areatree]': {
            //     select: me.onTreeSelect
            // },
            '*[itemId=add]':{
                click: me.onAddAccessClick
            },
            '*[itemId=del]':{
                click: me.onDelAccessClick
            }
        });


        me.groupStore = me.view.groupStore
        me.grouplist = me.view.grouplist

        me.groupAccessStore = me.view.groupAccessStore
        me.groupAccesslist = me.view.groupAccesslist


        // me.treestore = me.view.treestore;
        // me.tree = me.view.tree;



        me.groupAccessStore.on('beforeload',function(store){
            var groupid = '';
            if (me.grouplist.getSelection().length > 0) {
                groupid = me.grouplist.getSelection()[0].get('uid');
            }
            Ext.apply(store.proxy.extraParams, {
                groupid: groupid
            });
        });

        // me.groupStore.on('beforeload',function(store){
        //     var areacode = '';
        //     if (me.tree.getSelection().length > 0) {
        //         areacode = me.tree.getSelection()[0].get('code');
        //     }
        //     Ext.apply(store.proxy.extraParams, {
        //         areacode: areacode
        //     });
        // });
    },
    groupSelect:function(){
        var me = this;
        me.groupAccessStore.loadPage(1);
    },
    // onTreeSelect:function(){
    //     var me = this;
    //     me.groupStore.loadPage(1);
    //     me.groupAccessStore.loadPage(1);
    // },
    onAddAccessClick:function(){
        var me = this;
        if (me.grouplist.getSelection().length == 0) {
            toast('请选择权限组...');
            return;
        }
        var win = Ext.create('CamPus.view.alleyway.kregroupaccess.AddGroupAccWindow',{
            groupAccesslist: me.groupAccesslist,
            groupid: me.grouplist.getSelection()[0].get('uid')
        });
        win.show();
    },
    onDelAccessClick:function(){
        var me = this;
        if (me.groupAccesslist.getSelection().length == 0) {
            toast('请选择需删除的授权...');
            return;
        }
        var uid = [];
        Ext.each(me.groupAccesslist.getSelection(), function(item, index) {
            uid.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/alleyway/KreGroupAccess/delGroupAccess',
                        params: {
                            uid: uid.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.groupAccesslist.getStore().reload();
                                me.grouplist.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });  
    }
});