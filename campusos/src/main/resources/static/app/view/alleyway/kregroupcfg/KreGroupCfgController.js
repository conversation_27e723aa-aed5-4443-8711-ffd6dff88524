Ext.define('CamPus.view.alleyway.kregroupcfg.KreGroupCfgController', {
    extend: 'Ext.app.ViewController',
    alias: 'controller.KreGroupCfgController',
    view: ['KreGroupCfgView'],
    init: function () {
        var me = this;
        me.setControl({
            '*[itemId=grouplist]':{
                select: me.groupSelect
            },
            '*[itemId=addgroup]':{
                click: me.addgroup
            },
            '*[itemId=editgroup]':{
                click: me.editgroup
            },
            // 'treepanel[itemId=areatree]': {
            //     select: me.onTreeSelect
            // },
            '*[itemId=delgroup]':{
                click: me.delgroup
            },
            '*[itemId=adddoor]':{
                click: me.onAddDoorClick
            },
            '*[itemId=editdoor]':{
                click: me.onEditDoorClick
            },
            '*[itemId=deldoor]':{
                click: me.onDelDoorClick
            }
        });


        me.groupStore = me.view.groupStore
        me.grouplist = me.view.grouplist

        me.groupcfgDoorStore = me.view.groupcfgDoorStore
        me.groupcfgDoorlist = me.view.groupcfgDoorlist

        // me.treestore = me.view.treestore;
        // me.tree = me.view.tree;



        me.groupcfgDoorStore.on('beforeload',function(store){
            var groupid = '';
            if (me.grouplist.getSelection().length > 0) {
                groupid = me.grouplist.getSelection()[0].get('uid');
            }
            Ext.apply(store.proxy.extraParams, {
                groupid: groupid
            });
        });
        // me.groupStore.on('beforeload',function(store){
        //     var areacode = '';
        //     if (me.tree.getSelection().length > 0) {
        //         areacode = me.tree.getSelection()[0].get('code');
        //     }
        //     Ext.apply(store.proxy.extraParams, {
        //         areacode: areacode
        //     });
        // });
    },
    groupSelect:function(){
        var me = this;
        me.groupcfgDoorStore.loadPage(1);
    },
    // onTreeSelect:function(){
    //     var me = this;
    //     me.groupStore.loadPage(1);
    //     me.groupcfgDoorStore.loadPage(1);
    // },
    addgroup:function(){
        var me = this;
        // if (me.tree.getSelection().length == 0) {
        //     toast('请选择区域');
        //     return;
        // }
        // var record = me.tree.getSelection()[0];
        var addgroupWin = Ext.create('CamPus.view.alleyway.kregroupcfg.AddGroupCfgWindow',{
            grouplist: me.grouplist
            // , areacode: record.get('code'),
        });
        addgroupWin.show();
    },
    editgroup:function(){
        var me = this;
        if (me.grouplist.getSelection().length > 0) {
            if(me.grouplist.getSelection().length > 1){
                return toast('请选择一个权限组...');
            }
            var node = me.grouplist.getSelection()[0];
            var uid = me.grouplist.getSelection()[0].get('uid');
            var editgroupWin = Ext.create('CamPus.view.alleyway.kregroupcfg.AddGroupCfgWindow',{
                title:'修改权限组',
                uid: uid,
                grouplist: me.grouplist
            });
            var form = editgroupWin.down('form[itemId=form]');
            form.loadRecord(node);
            editgroupWin.show();
        }else{
            toast('请选择需修改权限组...');
        }
    },
    delgroup:function(){
        var me = this;
        if (me.grouplist.getSelection().length == 0) {
            toast('请选择需删除的权限组...');
            return;
        }
        var groupid = me.grouplist.getSelection()[0].get('uid');
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/alleyway/KreGroupCfg/delGroup',
                        params: {
                            uid: groupid
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.groupcfgDoorlist.getStore().reload();
                                me.grouplist.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });  
    },
    onAddDoorClick:function(){
        var me = this;
        if (me.grouplist.getSelection().length > 0) {
            if(me.grouplist.getSelection().length > 1){
                return toast('请选择一个权限组...');
            }
            var node = me.grouplist.getSelection()[0];
            var groupid = node.get('uid');
            var AddDevAuthWindow = Ext.create('CamPus.view.alleyway.kregroupcfg.AddDoorhWindow',{
                autoSize:true,
                groupid: groupid,
                groupcfgDoorlist: me.groupcfgDoorlist
            });
            AddDevAuthWindow.show();
        }else{
            toast('请选择需权限组后添加门禁权限...');
        }
    },
    onEditDoorClick: function () {
        var me = this;
        if (me.groupcfgDoorlist.getSelection().length === 0) {
            toast('请选择门禁信息');
            return
        }
        if (me.groupcfgDoorlist.getSelection().length == 1) {
            var record = me.groupcfgDoorlist.getSelection()[0];
            var win = Ext.create('CamPus.view.alleyway.kregroupcfg.KreGroupCfgTimeSchemeWindow', {
                isEdit: true,
                uid: record.get('uid'),
                groupcfgDoorlist: me.groupcfgDoorlist
            });
            var form = win.down('form[itemId=form]');
            if(record.get('effectivetimes') != 65535){
                form.down('radiogroup[itemId=limittype]').down('radiofield[name=limittypevalue]').setValue(1);
            }else{
                form.down('radiogroup[itemId=limittype]').down('radiofield[name=limittypevalue]').setValue(0);
            }
            form.loadRecord(record);
            win.show();
        }else {
            var uid = [];
            Ext.each(me.groupcfgDoorlist.getSelection(), function(item, index) {
                uid.push(item.get('uid'));
            });
            var win = Ext.create('CamPus.view.alleyway.kregroupcfg.KreGroupCfgTimeSchemeWindow', {
                isEdit: true,
                uid: uid.join(','),
                groupcfgDoorlist: me.groupcfgDoorlist
            });
            win.show();
        }
        
    },
    onDelDoorClick:function(){
        var me = this;
        if (me.groupcfgDoorlist.getSelection().length == 0) {
            toast('请选择需删除的门禁配置...');
            return;
        }
        var uid = [];
        Ext.each(me.groupcfgDoorlist.getSelection(), function(item, index) {
            uid.push(item.get('uid'));
        });
        Ext.Msg.show({
            title: '系统确认',
            message: '确认要彻底删除 ？',
            iconCls: 'fa fa-commenting-o',
            buttons: Ext.Msg.OKCANCEL,
            icon: Ext.Msg.QUESTION,
            fn: function(btn) {
                if (btn === 'ok') {
                    Ext.Ajax.request({
                        url: '/alleyway/KreGroupCfg/delGroupCfg',
                        params: {
                            uid:uid
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.groupcfgDoorlist.getStore().reload();
                                toast('删除成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });
                }
            }
        });  
    }
});