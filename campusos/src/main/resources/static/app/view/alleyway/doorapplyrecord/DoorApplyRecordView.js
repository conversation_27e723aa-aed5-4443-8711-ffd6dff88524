Ext.define('CamPus.view.alleyway.doorapplyrecord.DoorApplyRecordView', {
    extend: 'Ext.panel.Panel',
    alias: "widget.DoorApplyRecordView",
    controller: 'DoorApplyRecordController',
    requires: [
        'CamPus.view.alleyway.doorapplyrecord.DoorApplyRecordStore',
        'CamPus.view.alleyway.doorapplyrecord.DoorApplyRecordController'
    ],
    layout: {
        type: 'fit',
        pack: 'start',
        align: 'stretch'
    },
    constructor: function (config) {
        var me = this;

        me.store = Ext.create('CamPus.view.alleyway.doorapplyrecord.DoorApplyRecordStore', {

        });

        var tbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'combobox',
                itemId: 'statusbox',
                queryMode: 'local',
                displayField: 'name',
                valueField: 'value',
                defaultSelectFirst: true,
                width: 120,
                value: -2,
                store: Ext.create('Ext.data.Store', {
                    fields: ['value', 'name'],
                    data: [
                        { name: "全部", value: -2 },
                        { name: "待审核", value: 0 },
                        { name: "审核通过", value: 1 },
                        { name: "审核不通过", value: -1 }
                        //, { name: "已取消", value: 2 }
                    ]
                }),
                listeners: {
                    change: function () {
                        me.store.reload();
                    }
                }
            }, {
                xtype: 'datetimefield',
                itemId: 'starttime',
                width: 180,
                value: new Date(Ext.Date.format(new Date(), 'Y-m-d 00:00:00')),
                format: 'Y-m-d H:i:s',
                listeners: {
                    change: function (field) {
                        me.store.reload();
                    }
                }
            },
                {
                    xtype: 'datetimefield',
                    width: 200,
                    fieldLabel: '至',
                    labelWidth: 20,
                    labelSeparator: '',
                    itemId: 'endtime',
                    value: new Date(Ext.Date.format(new Date(), 'Y-m-d 23:59:59')),
                    format: 'Y-m-d H:i:s',
                    // value: Ext.Date.format(new Date(), 'Y-m-d'),
                    // format: 'Y-m-d',
                    listeners: {
                        change: function (field) {
                            me.store.reload();
                        }
                    }
                }, {
                    xtype: 'searchfield',
                    width: 250,
                    hideLabel: true,
                    paramName: 'key',
                    store: me.store,
                    emptyText: '请输入姓名关键词...'
                }, '->', {
                    xtype: 'button',
                    itemId: 'refresh',
                    iconCls: 'x-fa fa-refresh',
                    text: '刷新',
                    handler: function () {
                        me.store.reload();
                    }
                }]
        });

        Ext.each(config.opbutton, function (item, i) {
            tbar.addMaxItems(item, config.maxopbutton);
        });

        me.grid = Ext.create('Ext.grid.Panel', {
            tbar: tbar,
            collapsible: false,
            multiColumnSort: true,
            border: false,
            columnLines: true,
            store: me.store,
            viewConfig: {
                enableTextSelection: true
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '审核结果',
                    dataIndex: 'status',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 0) {
                            metaData.style += 'color:red;';
                            return '待审核';
                        } else if (value == -1) {
                            metaData.style += 'color:red;';
                            return '审核不通过';
                        } else if (value == 1) {
                            metaData.style += 'color:#7bc309;';
                            return '审核通过';
                        }
                    }
                },
                {
                    text: '姓名',
                    dataIndex: 'applicant_name',
                    width: 100,
                    sortable: false
                },
                {
                    text: '申请部门',
                    dataIndex: 'departmentname',
                    width: 200,
                    flex: 1,
                    sortable:false
                },{
                    text: '申请人类型',
                    dataIndex: 'applicant_type',
                    width: 200,
                    flex: 1,
                    sortable:false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '内部人员';
                        } else if (value == 2) {
                            return '外部人员';
                        }
                    }
                },
                {
                    text: '申请时间',
                    dataIndex: 'applicationtime',
                    width: 150,
                    sortable: false
                },
                {
                    text: '申请门禁权限类型',
                    dataIndex: 'access_type',
                    width: 150,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value == 1) {
                            return '卡号';
                        } else if (value == 2) {
                            return '密码';
                        }
                    }
                },{
                    text: '密码',
                    dataIndex: 'password',
                    width: 150,
                    sortable: false
                },{
                    text: '使用次数',
                    dataIndex: 'usage_count',
                    width: 150,
                    sortable: false
                },{
                    text: '开门开始时间',
                    dataIndex: 'starttime',
                    width: 150,
                    sortable: false
                },{
                    text: '开门结束时间',
                    dataIndex: 'endtime',
                    width: 150,
                    sortable: false
                },
                {
                    text: '申请原因',
                    dataIndex: 'reason',
                    flex:1,
                    sortable: false
                }

            ],
            selModel: 'checkboxmodel',
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(config, {
            items: [me.grid]
        });

        Ext.apply(me, config);
        me.callParent(arguments);
    }
});

Ext.define('CamPus.view.alleyway.doorapplyrecord.approvalRecordWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '审核记录',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.approvalRecordStore = Ext.create('Ext.data.Store', {
            proxy: {
                type: 'ajax',
                url: '/Workflow/ApprovalRecord/getApprovalRecord',
                actionMethods: {
                    read: 'POST'
                },
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    totalProperty: 'total',
                    successProperty: 'success',
                    messageProperty: 'msg'
                }
            },
            autoLoad: true,
            listeners: {
                beforeload: function (store, operation, eOpts) {
                    Ext.apply(store.proxy.extraParams, {
                        formid: config.formid
                    });
                }
            }
        });

        var approvalRecordTbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 220,
                hideLabel: true,
                paramName: 'key',
                store: me.approvalRecordStore,
                emptyText: '工号、姓名关键词...'
            },'->', {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.approvalRecordStore.reload();
                }
            }]
        });

        me.approvalRecordGrid = Ext.create('Ext.grid.Panel', {
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            tbar: approvalRecordTbar,
            store: me.approvalRecordStore,
            viewConfig: {
                enableTextSelection: true,
                getRowClass: function () {
                    return this.enableTextSelection ? 'x-selectable' : '';
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '审核状态',
                    dataIndex: 'auditcondition',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '审核中';
                            case 1:
                                metaData.style += 'color:#7bc309;';
                                return '已完成 ';
                            case 2:
                                metaData.style += 'color:red;';
                                return '权限被取消 ';
                            case 3:
                                metaData.style += 'color:#7bc309;';
                                return '权限已恢复 ';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '工号',
                    dataIndex: 'code',
                    width: 120,
                    sortable: false
                },
                {
                    text: '审核人',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '审核时间',
                    dataIndex: 'auditdate',
                    width: 150,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '备注',
                    dataIndex: 'remark',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(me, {
            items: [me.approvalRecordGrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        me.close();
    }
});
Ext.define('CamPus.view.alleyway.doorapplyrecord.DoorRecordWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '门禁下载状态',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.approvalRecordStore = Ext.create('Ext.data.Store', {
            proxy: {
                type: 'ajax',
                url: '/AlleyWay/CardRecords/getDoorDownload',
                actionMethods: {
                    read: 'POST'
                },
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    totalProperty: 'total',
                    successProperty: 'success',
                    messageProperty: 'msg'
                }
            },
            autoLoad: true,
            listeners: {
                beforeload: function (store, operation, eOpts) {
                    Ext.apply(store.proxy.extraParams, {
                        formid: config.formid
                    });
                }
            }
        });

        var approvalRecordTbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: ['->',{
                xtype: 'button',
                itemId: 'redownload',
                iconCls: 'x-fa fa-refresh',
                text: '重新下载',
                handler: function () {
                    var records = me.approvalRecordGrid.getSelection();
                    var recordUids = []
                    records.forEach(function (item, i) {
                        recordUids.push(item.get('uid'))
                    });
                    Ext.Ajax.request({
                        url: '/AlleyWay/CardRecords/DoorReturnDownload',
                        params: {
                            recordUids: recordUids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.approvalRecordStore.reload();
                                toast('重新下载成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });

                }
            }, {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.approvalRecordStore.reload();
                }
            }]
        });

        me.approvalRecordGrid = Ext.create('Ext.grid.Panel', {
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            tbar: approvalRecordTbar,
            store: me.approvalRecordStore,
            viewConfig: {
                enableTextSelection: true,
                getRowClass: function () {
                    return this.enableTextSelection ? 'x-selectable' : '';
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '下载状态',
                    dataIndex: 'status',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:#1967d2;';
                                return '待下载';
                            case 1:
                                metaData.style += 'color:#7bc309;';
                                return '已下载 ';
                            case 2:
                                metaData.style += 'color:red;';
                                return '下载失败 ';
                            case 3:
                                metaData.style += 'color:red;';
                                return '待删除 ';
                            case 4:
                                metaData.style += 'color:red;';
                                return '删除失败 ';
                            case 5:
                                metaData.style += 'color:#1967d2;';
                                return '待重下 ';
                            case 6:
                                metaData.style += 'color:red;';
                                return '重下失败 ';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '门名称',
                    dataIndex: 'name',
                    width: 200,
                    sortable: false
                },
                {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 200,
                    sortable: false
                },
                {
                    text: '下载时间',
                    dataIndex: 'down_at',
                    width: 200,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '下载次数',
                    dataIndex: 'downnum',
                    width: 200,
                    sortable: false
                },
                {
                    text: '下载提示',
                    dataIndex: 'downmsg',
                    width: 200,
                    sortable: false
                },
                {
                    text: '所属区域',
                    dataIndex: 'areaname',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'checkboxmodel',
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(me, {
            items: [me.approvalRecordGrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        me.close();
    }
});

Ext.define('CamPus.view.alleyway.doorapplyrecord.lockDownloadRecordWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '锁下载状态',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.approvalRecordStore = Ext.create('Ext.data.Store', {
            proxy: {
                type: 'ajax',
                url: '/Workflow/ApprovalRecord/getLockDownload',
                actionMethods: {
                    read: 'POST'
                },
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    totalProperty: 'total',
                    successProperty: 'success',
                    messageProperty: 'msg'
                }
            },
            autoLoad: true,
            listeners: {
                beforeload: function (store, operation, eOpts) {
                    Ext.apply(store.proxy.extraParams, {
                        formid: config.formid
                    });
                }
            }
        });

        var approvalRecordTbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 220,
                hideLabel: true,
                paramName: 'key',
                store: me.approvalRecordStore,
                emptyText: '锁名称、机号关键词...'
            },'->', {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.approvalRecordStore.reload();
                }
            }]
        });

        me.approvalRecordGrid = Ext.create('Ext.grid.Panel', {
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            tbar: approvalRecordTbar,
            store: me.approvalRecordStore,
            viewConfig: {
                enableTextSelection: true,
                getRowClass: function () {
                    return this.enableTextSelection ? 'x-selectable' : '';
                }
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '下载状态',
                    dataIndex: 'status',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '未下载';
                            case 1:
                                metaData.style += 'color:#7bc309;';
                                return '已下载 ';
                            case 2:
                                metaData.style += 'color:red;';
                                return '下载失败 ';
                            case 3:
                                metaData.style += 'color:#red;';
                                return '删除成功 ';
                            case 4:
                                metaData.style += 'color:#red;';
                                return '删除失败 ';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '锁名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 120,
                    sortable: false
                },
                {
                    text: '下载时间',
                    dataIndex: 'downtime',
                    width: 150,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '所属区域',
                    dataIndex: 'areaname',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false
                },
                {
                    text: '备注',
                    dataIndex: 'reason',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false
                }
            ],
            selModel: 'rowmodel',
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(me, {
            items: [me.approvalRecordGrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        me.close();
    }
});
Ext.define('CamPus.view.alleyway.doorapplyrecord.electronicDownloadRecordWindow', {
    extend: 'CamPus.view.ux.Window',
    title: '电控下载状态',
    constructor: function (config) {
        var me = this;
        Ext.apply(me, config);

        me.approvalRecordStore = Ext.create('Ext.data.Store', {
            proxy: {
                type: 'ajax',
                url: '/Workflow/ApprovalRecord/getElectronicDownload',
                actionMethods: {
                    read: 'POST'
                },
                reader: {
                    type: 'json',
                    rootProperty: 'data',
                    totalProperty: 'total',
                    successProperty: 'success',
                    messageProperty: 'msg'
                }
            },
            autoLoad: true,
            listeners: {
                beforeload: function (store, operation, eOpts) {
                    Ext.apply(store.proxy.extraParams, {
                        formid: config.formid
                    });
                }
            }
        });

        var approvalRecordTbar = Ext.create('CamPus.view.ux.Toolbar', {
            items: [{
                xtype: 'searchfield',
                width: 220,
                hideLabel: true,
                paramName: 'key',
                store: me.approvalRecordStore,
                emptyText: '电控名称、机号关键词...'
            },'->',{
                xtype: 'button',
                itemId: 'redownload',
                iconCls: 'x-fa fa-refresh',
                text: '重新下载',
                handler: function () {
                    var records = me.approvalRecordGrid.getSelection();
                    var recordUids = []
                    records.forEach(function (item, i) {
                        recordUids.push(item.get('uid'))
                    });
                    Ext.Ajax.request({
                        url: '/Workflow/ApprovalRecord/ElectronicReturnDownload',
                        params: {
                            recordUids: recordUids.join(',')
                        },
                        method: 'POST',
                        success: function(response) {
                            var result = Ext.JSON.decode(response.responseText);
                            if (result.success) {
                                me.approvalRecordStore.reload();
                                toast('重新下载成功...');
                            } else {
                                Ext.Msg.alert('系统信息', result.msg);
                            }
                        }
                    });

                }
            }, {
                xtype: 'button',
                itemId: 'refresh',
                iconCls: 'x-fa fa-refresh',
                text: '刷新',
                handler: function () {
                    me.approvalRecordStore.reload();
                }
            }]
        });

        me.approvalRecordGrid = Ext.create('Ext.grid.Panel', {
            collapsible: false,
            multiColumnSort: true,
            border: true,
            columnLines: true,
            tbar: approvalRecordTbar,
            store: me.approvalRecordStore,
            viewConfig: {
                enableTextSelection: true,
                getRowClass: function () {
                    return this.enableTextSelection ? 'x-selectable' : '';
                }
            },
            selModel: {
                selType: 'checkboxmodel'
            },
            columns: [
                { xtype: 'rownumberer' },
                {
                    text: '下载状态',
                    dataIndex: 'status',
                    width: 100,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        switch (parseInt(value)) {
                            case 0:
                                metaData.style += 'color:red;';
                                return '未下载';
                            case 1:
                                metaData.style += 'color:#7bc309;';
                                return '已下载 ';
                            case 2:
                                metaData.style += 'color:red;';
                                return '下载失败 ';
                            case 3:
                                metaData.style += 'color:#red;';
                                return '删除成功 ';
                            case 4:
                                metaData.style += 'color:#red;';
                                return '删除失败 ';
                            default:
                                return '';
                        }
                    }
                },
                {
                    text: '电控名称',
                    dataIndex: 'name',
                    width: 120,
                    sortable: false
                },
                {
                    text: '机号',
                    dataIndex: 'machineid',
                    width: 120,
                    sortable: false
                },
                {
                    text: '下载时间',
                    dataIndex: 'downtime',
                    width: 150,
                    sortable: false,
                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                        if (value) {
                            return Ext.Date.format(new Date(value), 'Y-m-d H:i:s')
                        }
                    }
                },
                {
                    text: '所属区域',
                    dataIndex: 'areaname',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false
                },
                {
                    text: '备注',
                    dataIndex: 'reason',
                    width: 120,
                    minWidth: 120,
                    flex: 1,
                    sortable: false
                }
            ],
            flex: 1,
            bbar: {
                xtype: 'pagingtoolbar',
                displayInfo: true,
                displayMsg: '显示记录 {0} - {1} / {2}',
                emptyMsg: "没有任何数据"
            }
        });

        Ext.apply(me, {
            items: [me.approvalRecordGrid]
        });
        me.callParent(arguments);
    },
    Save: function () {
        var me = this;
        me.close();
    }
});
