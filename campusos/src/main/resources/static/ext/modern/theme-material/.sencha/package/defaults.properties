# =============================================================================
# This file defines properties used by build-impl.xml and the associated
# *-impl.xml files (sass-impl.xml, js-impl.xml, etc.), which are the core of
# the applications build process. 
#
# IMPORTANT - This file is not  modifiable by a package, and will be overwritten
# during each app upgrade. Please use build.properties for defining package 
# customizations to these properties.
# =============================================================================

# ===========================================
# properties defining various directory
# locations
# ===========================================
build.dir=${package.build.dir}

package.output=${build.dir}
package.output.base=${package.output}

package.output.js=
package.output.css=resources
package.output.sass=${package.output.js}
package.output.resources=${package.output.css}

build.out.js.dir=${package.output.base}/${package.output.js}
build.out.css.dir=${package.output.base}/${package.output.css}
build.out.sass.dir=${package.output.base}/${package.output.sass}
build.out.resources.dir=${package.output.base}/${package.output.resources}


# a temporary output directory used for staging intermediate build artifacts
build.temp.dir=${workspace.build.dir}/temp/${package.name}

build.resources.dir=${build.out.resources.dir}
package.resources.dir=${package.dir}/resources
package.sass.dir=${package.dir}/sass
package.licenses.dir=${package.dir}/licenses

# ===========================================
# definitions of various file name patterns
# used for output artifacts
# ===========================================

build.name.prefix=${package.name}
build.name.css.prefix=${build.resources.dir}/${package.name}
build.name.ruby=config.rb

build.debug.suffix=-debug
build.all.suffix=-all
build.rtl.suffix=-rtl

build.all.debug.suffix=${build.all.suffix}${build.debug.suffix}
build.all.rtl.suffix=${build.all.suffix}${build.rtl.suffix}
build.all.rtl.debug.suffix=${build.all.suffix}${build.rtl.suffix}${build.debug.suffix}

# ===========================================
# define the output js file names for dev,
# debug, and compressed (no suffix)
# ===========================================
build.all.js=${build.out.js.dir}/${build.name.prefix}.js
build.all.debug.js=${build.out.js.dir}/${build.name.prefix}${build.debug.suffix}.js

package.sass.build.dir=${build.out.sass.dir}

# ===========================================
# output file names for the scss files
# ===========================================
build.all.scss=${package.sass.build.dir}/${build.name.prefix}${build.all.debug.suffix}.scss
build.all.rtl.scss=${package.sass.build.dir}/${build.name.prefix}${build.all.rtl.debug.suffix}.scss

# ===========================================
# output file names for the css files 
# generated from the scss files by running
# a compass compilation
# ===========================================
build.all.css.debug.prefix=${package.name}${build.all.debug.suffix}
build.all.css.debug=${build.out.css.dir}/${build.all.css.debug.prefix}.css
build.all.rtl.css.debug.prefix=${package.name}${build.all.rtl.debug.suffix}
build.all.rtl.css.debug=${build.out.css.dir}/${build.all.rtl.css.debug.prefix}.css
build.all.css.prefix=${package.name}${build.all.suffix}
build.all.css=${build.out.css.dir}/${build.all.css.prefix}.css
build.all.rtl.css.prefix=${package.name}${build.all.rtl.suffix}
build.all.rtl.css=${build.out.css.dir}/${build.all.rtl.css.prefix}.css

build.all.ruby=${package.sass.build.dir}/${build.name.ruby}

# ===========================================
# options to pass to the 'sencha fs slice' command
# ===========================================
build.slice.options=

# ===========================================
# preprocessor options used when generating
# concatenated js output files
# ===========================================
build.compile.js.debug.options=debug:true
build.compile.js.options=debug:false

# enables / disables removing text references from 
# package js build files
build.remove.references=false

# This property can be modified to change general build options
# such as excluding files from the set.  The format expects newlines
# for each argument, for example:
#            
#   build.operations=\
#       exclude\n \
#       -namespace=Ext\n
#
# NOTE: modifications to build.operations are intended to be 
# placed in an override of the "-after-init" target, where it 
# can be calculated based on other
# ant properties
#
# build.operations=

# ===========================================
# compression option used to generate '-all'
# js output file
# ===========================================
build.compile.js.compress=+yui

build.compile.temp.dir=${build.temp.dir}/sencha-compiler

# controles whether to keep the temp compile dir after the build
build.compile.temp.dir.keep=true


# ===========================================
# selector count threshold to use when 
# splitting a single css file into multiple
# css files (IE selector limit workaround)
# ===========================================
build.css.selector.limit=4095

# controls the ruby command used to execute compass.  a full path
# to ruby may be specified rather than allowing the system shell
# to resolve the command
build.ruby.path=ruby

# controls the working directory of the child compass process
# and the output location for the .sass-cache folder
compass.working.dir=${package.sass.build.dir}

# enables / disables console highlighting for compass
compass.compile.boring=false

# enables / disables forced rebuilds for compass
compass.compile.force=true

# enables / disables stack traces in compass failure output
compass.compile.trace=true

# the directory containing sass files for compass to compile
compass.sass.dir=${package.sass.build.dir}

# the output directory where compass should place built css files
compass.css.dir=${build.out.css.dir}

# the directory containing the ruby config file for compass
compass.config.file=${build.all.ruby}

compass.cache.dir=${workspace.build.dir}/.sass-cache

# ===========================================
# Options for sub-packages

# Set to true/1 to enable build.version inheritance by sub-pacakges
build.subpkgs.inherit.version=0

# ===========================================
# theme slicing example page settings
# ===========================================
package.example.dir=${package.dir}/sass/example
package.example.build.dir=${build.temp.dir}/slicer-temp
package.example.base=${build.all.rtl.css.debug.prefix}
package.example.css=${package.example.build.dir}/${package.example.base}.css
package.example.scss=${package.example.build.dir}/${package.example.base}.scss
package.example.theme.html=${package.example.dir}/theme.html
package.example.fashion.html=${package.example.dir}/fashion.html

# the name of the intermediate screenshot file used for image slicing
build.capture.png=${package.example.build.dir}/theme-capture.png

# the name of the intermediate widget manifest file used for image slicing
build.capture.json=${package.example.build.dir}/theme-capture.json



# the microloader to use for bootstrapping operations
package.microloader.bootstrap=${package.microloader.dir}/${package.microloader.development}

build.boot.name=Boot.js
build.boot.file=${package.config.dir}/${build.boot.name}
build.slicer.microloader.name=Microloader.js
build.slicer.microloader.file=${package.config.dir}/${build.slicer.microloader.name}


# the ruby compass config file to generate for slicer page scss
package.example.out.ruby=${package.example.build.dir}/config.rb
package.example.compass.config=${package.example.out.ruby}


bootstrap.base.path=${package.example.dir}
bootstrap.example.js=${package.example.dir}/bootstrap.js
bootstrap.example.json=${package.example.dir}/bootstrap.json


# ===========================================
# options controlling output packaging
# operations for output '.pkg' file
# ===========================================
pkg.build.dir=${workspace.build.dir}/${package.name}
pkg.file.name=${package.name}.pkg
pkg.includes=**/*
pkg.excludes=package.json


# the port number to start the local web server on
build.web.port=1841

# the directory representing the root web folder
build.web.root=${workspace.dir}

