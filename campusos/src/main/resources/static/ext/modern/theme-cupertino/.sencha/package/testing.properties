# ===========================================
# This file defines properties used by 
# build-impl.xml, which is the base impl
# of an applications build process. The 
# properties from this file correspond to the
# 'testing' build environment, specified
# by 'sencha app build testing'.  These will
# take precedence over defaults provided by
# build.properties.
# ===========================================

# ===========================================
# compression option used to generate '-all'
# js output file.  this value disables
# compression for testing builds
# ===========================================
build.compile.js.compress=
