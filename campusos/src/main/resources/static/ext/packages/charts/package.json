{
    /**
     * The namespace of this package.
     *
     * As a general rule, all classes that belong to this package should be under this namespace
     * if multiple namespaces are part of this package, set this to "".
     */
    "namespace": "Ext",

    "name": "charts",
    "type": "code",
    "alternateName": [
        "sencha-charts"
    ],

    "framework": "ext",
    "requires": [
        "core"
    ],

    "creator": "Sencha",
    "summary": "Sencha Charts",
    "detailedDescription": "Sencha Charts is a package that allows to generate charts of different types (line, columns, scatter, radar, gauge, etc...). It also includes the Drawing library which provides an abstraction sitting on top of HTML Canvas, SVG and VML.",

    "version": "6.2.0.981",
    "compatVersion": "6.0.0",
    "format": "1",

    "sass": {
        "namespace": "Ext",

        "etc": [
            "${package.dir}/sass/etc/all.scss",
            "${package.dir}/${toolkit.name}/sass/etc/all.scss"
        ],

        "var": [
            "${package.dir}/sass/var",
            "${package.dir}/${toolkit.name}/sass/var"
        ],

        "src": [
            "${package.dir}/sass/src",
            "${package.dir}/${toolkit.name}/sass/src"
        ]
    },

    "output": {
        "base": "${framework.dir}/build/packages/${package.name}/${toolkit.name}/${build.id}",
        "js": "..",
        "sass": ""
    },
    "local": true,

    "classpath": [
        "${package.dir}/src",
        "${package.dir}/${toolkit.name}/src"
    ],

    "overrides": [
        "${package.dir}/overrides",
        "${package.dir}/${toolkit.name}/overrides"
    ],

    "slicer": {
        "js": [
            {
                "path": "${package.dir}/sass/example/custom.js",
                "isWidgetManifest": true
            }
        ]
    },

    "builds": {
        "triton": {
            "toolkit": "classic",
            "theme": "theme-triton"
        },

        "neptune": {
            "toolkit": "classic",
            "theme": "theme-neptune"
        },

        "crisp": {
            "toolkit": "classic",
            "theme": "theme-crisp"
        },

        "classic": {
            "toolkit": "classic",
            "theme": "theme-classic"
        },

        "modern-triton": {
            "toolkit": "modern",
            "theme": "theme-triton"
        },

        "modern-neptune": {
            "toolkit": "modern",
            "theme": "theme-neptune"
        } 
    }
}
