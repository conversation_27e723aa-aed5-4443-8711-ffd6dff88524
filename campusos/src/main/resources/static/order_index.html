<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点餐界面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #e0eafc, #cfdef3);
        }
        .container {
            display: flex;
            border-radius: 12px;
            background-color: #ffffff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            width: 90%;
            max-width: 1200px;
            height: 80%;
            overflow: hidden;
            position: relative;
        }
        .selection, .cart {
            flex: 1;
            margin: 10px;
            padding: 10px;
            border-radius: 12px;
            overflow-y: auto;
        }
        .date-selection {
            background-color: #f0f4f8;
        }
        .department-selection {
            background-color: #f9f9f9;
        }
        .selection h3, .cart h3 {
            margin-top: 0;
            color: #333;
            font-size: 18px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            position: sticky;
            top: 0;
            background-color: #ffffff;
            z-index: 1;
        }
        .selection ul, .cart ul {
            list-style: none;
            padding: 0;
        }
        .selection li, .cart li {
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .selection li.selected, .cart li.selected {
            background-color: #007bff;
            color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .meal-buttons {
            display: flex;
            flex-direction: row;
            margin-top: 10px;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        .meal-buttons button {
            margin-bottom: 5px;
            padding: 10px;
            cursor: pointer;
            background-color: #e0e0e0;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        .meal-buttons button.selected {
            background-color: #007bff;
            color: white;
        }
        .meal-buttons button.disabled {
            background-color: #cccccc;
            color: #666666;
            cursor: not-allowed;
        }
        .cart-icon {
            position: fixed;
            bottom: 20px;
            left: 20px;
            cursor: pointer;
            font-size: 29px;
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            padding: 15px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 2000; /* 确保图标在弹出层之上 */
        }
        .cart-count {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: red;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 14px;
        }
        .submit-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 25px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: background-color 0.3s ease;
            z-index: 2000; /* 确保按钮在弹出层之上 */
        }
        .submit-button:hover {
            background-color: #218838;
        }
        .cart-modal {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 25%;
            background-color: #fff;
            box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.2);
            border-radius: 12px 12px 0 0;
            overflow-y: auto;
            transform: translateY(100%);
            transition: transform 0.3s ease-in-out;
            z-index: 1000; /* 确保弹出层在图标和按钮下方 */
        }
        .cart-modal.show {
            display: flex;
            transform: translateY(0);
        }
        .cart-modal-content {
            padding: 20px;
            width: 100%;
        }
        .cart-modal-content h3 {
            margin-top: 0;
            color: #333;
        }
        .close-modal {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 10px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: background-color 0.3s ease;
        }
        .close-modal:hover {
            background-color: #c82333;
        }
        .delete-button {
            margin-left: auto;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 5px 10px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .delete-button:hover {
            background-color: #c82333;
        }
        .reason-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex; /* 确保使用弹性布局居中内容 */
            align-items: center; /* 垂直居中 */
            justify-content: center; /* 水平居中 */
            z-index: 3000;
        }

        .reason-modal-content {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            max-width: 500px; /* 增大最大宽度 */
            width: 90%; /* 占据屏幕宽度的90% */
            position: relative;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        }

        .reason-modal-content h3 {
            margin-top: 0;
        }

        .reason-modal-content input {
            width: calc(100% - 20px); /* 调整输入框的宽度以适应边距 */
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .reason-modal-content button.confirm-reason-button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            position: absolute; /* 将按钮定位在右下角 */
            right: 20px;
            bottom: 30px;
        }

        .reason-modal-content button.confirm-reason-button:hover {
            background-color: #0056b3;
        }

        .reason-modal-content .close-reason-modal {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            padding: 10px;
            cursor: pointer;
            font-size: 18px;
            transition: background-color 0.3s ease;
        }

        .reason-modal-content .close-reason-modal:hover {
            background-color: #c82333;
        }

    </style>
</head>
<body>
<input type="hidden" th:value="${userinfo.docid}"  id = "userdocid"/>
<input type="hidden" th:value="${userinfo.name}"  id = "username"/>
<div class="container">
    <div class="selection date-selection">
        <h3 style="text-align: center">就餐日期</h3>
        <ul id="date-list"></ul>
    </div>
    <div class="selection department-selection">
        <h3 style="text-align: center">部门餐次</h3>
        <ul id="department-list"></ul>
    </div>
</div>

<div class="cart-icon">
    🛒<span id="cart-count" class="cart-count hidden">0</span>
</div>

<button class="submit-button">确认</button>

<!-- 就餐原因填写弹出框 -->
<div class="reason-modal" style="display: none;">
    <div class="reason-modal-content">
        <button class="close-reason-modal">&times;</button>
        <h3>请输入就餐原因</h3>
        <input type="text" id="reason-input" placeholder="请输入就餐原因">
        <button class="confirm-reason-button">提交</button>
    </div>
</div>


<div class="cart-modal">
    <div class="cart-modal-content">
        <button class="close-modal">&times;</button>
        <h3>已选餐次</h3>
        <ul id="modal-cart-list"></ul>
    </div>
</div>

<link rel="stylesheet" href="/ui/css/weui.min.css" />
<link rel="stylesheet" href="/ui/css/jquery-weui.min.css" />
<link rel="stylesheet" href="/ui/css/main.css" />
<script src="/ui/js/jquery-2.1.4.js"></script>
<script src="/ui/js/jquery-weui.min.js"></script>
<script src="/ui/js/main.js"></script>
<script>
    $(document).ready(function() {
        const cart = {};
        var infoid = $('#userdocid').val()
        var username = $('#username').val()

        // 生成最近7天的日期
        function generateDates() {
            const dates = [];
            for (let i = 0; i < 7; i++) {
                const date = new Date();
                date.setDate(date.getDate() + i);
                dates.push(date.toISOString().split('T')[0]);
            }
            return dates;
        }

        // 判断是否为周末或节假日
        function isHolidayOrWeekend(date) {
            const holidays = []; // 节假日数组，格式为'YYYY-MM-DD'
            const day = new Date(date).getDay();
            return day === 0 || day === 6 || holidays.includes(date);
        }

        // 渲染日期列表
        function renderDateList() {
            const dates = generateDates();
            const dateList = $('#date-list');

            dates.forEach(date => {
                const listItem = $('<li>').text(date);
                if (isHolidayOrWeekend(date)) {
                    listItem.css('color', 'red');
                    listItem.on('click', function() {
                        alert('该日期不可选择');
                    });
                } else {
                    listItem.on('click', function() {
                        //if (Object.keys(cart).length === 0) {
                        $('#date-list li').removeClass('selected');
                        $(this).addClass('selected');
                        renderDepartmentList(date);
                        //}
                    });
                }
                dateList.append(listItem);
            });
        }

        // VIP部门数据（灵活数据）
        let departments = [];

        // 发送到后端
        $.ajax({
            url: '/ordering/qreyVIPDept', // 替换为实际的 API 端点
            type: 'POST',
            contentType: 'application/json',
            success: function(response) {
                departments =response.data;
                console.log('Success:', response);
            },
            error: function(xhr, status, error) {
                console.error('Error:', error);
            }
        });


        // 更新购物车
        function updateCart(date, department,orgcode, meal, action) {
            if (!cart[date]) {
                cart[date] = {};
            }
            if (!cart[date][department]) {
                cart[date][department] = { breakfast: false, lunch: false,orgcode: orgcode };
            }

            if (action === 'add') {
                cart[date][department][meal] = true;
            } else if (action === 'remove') {
                cart[date][department][meal] = false;
                if (!cart[date][department].breakfast && !cart[date][department].lunch) {
                    delete cart[date];
                }
            }
            renderCart();
            if (cart[date] == null) {
                enableAllSelections();
            }else {
                disableOtherSelections(date, department);
            }
        }

        // 渲染购物车
        function renderCart() {
            const modalCartList = $('#modal-cart-list');
            modalCartList.empty();
            let itemCount = 0;

            for (const date in cart) {
                for (const department in cart[date]) {
                    const meals = cart[date][department];
                    const listItem = $('<li style="display: flex;margin: 5px;justify-content: space-between;">');
                    const mealDetails = [];

                    if (meals.breakfast) mealDetails.push('早餐');
                    if (meals.lunch) mealDetails.push('中餐');

                    const deleteButton = $('<button class="delete-button">删除</button>');
                    deleteButton.on('click', function() {
                        delete cart[date][department];
                        if (Object.keys(cart[date]).length === 0) {
                            delete cart[date];
                        }
                        renderCart();
                        renderDepartmentList($('#date-list li.selected').text());
                        if (Object.keys(cart).length === 0) {
                            enableAllSelections();
                        }
                    });

                    listItem.html(`<span>${date}</span> - ${department} - ${mealDetails.join(', ')}`);
                    listItem.append(deleteButton);
                    modalCartList.append(listItem);
                    itemCount++;
                }
            }

            $('#cart-count').text(itemCount).toggleClass('hidden', itemCount === 0);
        }

        // 渲染部门列表
        function renderDepartmentList(date) {
            const departmentList = $('#department-list');
            departmentList.empty();

            departments.forEach(department => {
                const listItem = $('<li class="department-item">').text(department.name);
                const mealButtons = $('<div class="meal-buttons">');
                const breakfastButton = $('<button>').text('早餐');
                const lunchButton = $('<button>').text('中餐');

                if (cart[date] && cart[date][department.name]) {
                    if (cart[date][department.name].breakfast) breakfastButton.addClass('selected');
                    if (cart[date][department.name].lunch) lunchButton.addClass('selected');
                }

                breakfastButton.on('click', function() {
                    const selectedDate = $('#date-list li.selected').text();
                    if ($(this).hasClass('selected')) {
                        updateCart(selectedDate, department.name,department.orgcode, 'breakfast', 'remove');
                    } else {
                        updateCart(selectedDate, department.name,department.orgcode, 'breakfast', 'add');
                    }
                    $(this).toggleClass('selected');
                });

                lunchButton.on('click', function() {
                    const selectedDate = $('#date-list li.selected').text();
                    if ($(this).hasClass('selected')) {
                        updateCart(selectedDate, department.name,department.orgcode, 'lunch', 'remove');
                    } else {
                        updateCart(selectedDate, department.name,department.orgcode, 'lunch', 'add');
                    }
                    $(this).toggleClass('selected');
                });

                mealButtons.append(breakfastButton, lunchButton);
                listItem.append(mealButtons);
                departmentList.append(listItem);
            });
        }

        // 禁用其他选择项
        function disableOtherSelections(selectedDate, selectedDepartment) {
            $('#date-list li').each(function() {
                if (!$(this).hasClass('selected')) {
                    $(this).css('pointer-events', 'none');
                    $(this).css('color', '#ccc');
                }
            });

            $('#department-list .department-item').each(function() {
                if (!$(this).text().includes(selectedDepartment)) {
                    $(this).find('button').each(function() {
                        $(this).addClass('disabled');
                        $(this).css('pointer-events', 'none');
                    });
                }
            });
        }

        // 启用所有选择项
        function enableAllSelections() {
            $('#date-list li').each(function() {
                $(this).css('pointer-events', '');
                $(this).css('color', '');
            });

            $('#department-list .department-item').each(function() {
                $(this).find('button').each(function() {
                    $(this).removeClass('disabled');
                    $(this).css('pointer-events', '');
                });
            });
        }

        // 确认按钮点击事件
        $('.submit-button').on('click', function() {
            $('.reason-modal').show();
        });

        // 关闭就餐原因填写弹框
        $('.close-reason-modal').on('click', function() {
            $('.reason-modal').hide();
        });

        // 确认就餐原因提交
        $('.confirm-reason-button').on('click', function() {
            const reason = $('#reason-input').val().trim();
            if (reason === '') {
                alert('请填写就餐原因');
            } else {
                $('.reason-modal').hide();
                console.log('提交数据:', reason);
                console.log('购物车数据:', cart);
                // 示例 JSON 数据
                //var jsonData = JSON.stringify(cart);

                // 转换 JSON 数据为适合传递的格式
                function transformData(data) {
                    var transformed = [];

                    $.each(data, function(date, departments) {
                        $.each(departments, function(dep, info) {
                            transformed.push({
                                daily: date,
                                diningDep: info.orgcode,
                                diningDepName: dep,
                                breakfast: info.breakfast,
                                lunch: info.lunch
                            });
                        });
                    });
                    return transformed;
                }

                // 执行转换
                var transformedData = transformData(cart);

                console.log(transformedData);

                // 发送到后端
                $.ajax({
                    url: '/ordering/save', // 替换为实际的 API 端点
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        infoId: infoid, // 替换为实际的 infoId
                        name: username, // 替换为实际的 用户姓名
                        reason: reason, // 替换为实际的 reason
                        consumeApplyDtoList: transformedData
                    }),
                    success: function(response) {
                        $.alert("申请成功", "系统提示");
                    },
                    error: function(xhr, status, error) {
                        $.alert("申请失败，请重新申请", "系统提示");
                    }
                });

            }
        });

        // 切换购物车显示/隐藏
        $('.cart-icon').on('click', function() {
            $('.cart-modal').toggleClass('show');
            renderCart(); // Ensure cart items are rendered when opening the modal
        });

        // 关闭购物车弹出框
        $('.cart-modal').on('click', function(e) {
            if ($(e.target).is('.cart-modal')) {
                $(this).removeClass('show');
            }
        });
        $('.close-modal').on('click', function() {
            $('.cart-modal').removeClass('show');
        });

        // 初始化日期列表
        renderDateList();
    });
</script>
</body>
</html>
