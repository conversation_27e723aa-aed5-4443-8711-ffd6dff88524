<!DOCTYPE HTML>
<html lang="zh-CN" manifest="">

<head>
    <meta name="renderer" content="webkit"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=10, user-scalable=yes"/>
    <title></title>
    <link href="/resources/style/tailwind.css" rel="stylesheet">
    <link rel="shortcut icon" href="yunmai.ico">
    <link rel="stylesheet" href="/resources/style/main.css"/>
    <link rel="stylesheet" type="text/css" href="resources/style/all.css">
    <link rel="stylesheet" type="text/css" href="resources/style/css2">
    <script src="/resources/js/jquery-3.2.1.min.js" rel="script"></script>
    <script src="/resources/dataview/jquery.liMarquee.js"></script>
    <script src="/resources/dataview/swiper.min.js"></script>
    <script src="/resources/dataview/echarts.min.js"></script>
    <script src="/resources/js/common.js"></script>
    <!--
    <script type="text/javascript">
        var Ext = Ext || {}; // Ext namespace won't be defined yet...

        // This function is called by the Microloader after it has performed basic
        // device detection. The results are provided in the "tags" object. You can
        // use these tags here or even add custom tags. These can be used by platform
        // filters in your manifest or by platformConfig expressions in your app.
        //
        Ext.beforeLoad = function (tags) {
            var s = location.search,  // the query string (ex "?foo=1&bar")
                profile;

            // For testing look for "?classic" or "?modern" in the URL to override
            // device detection default.
            //
            if (s.match(/\bclassic\b/)) {
                profile = 'classic';
            }
            else if (s.match(/\bmodern\b/)) {
                profile = 'modern';
            }
            else {
                profile = tags.desktop ? 'classic' : 'modern';
                //profile = tags.phone ? 'modern' : 'classic';
            }

            Ext.manifest = profile; // this name must match a build profile name

            // This function is called once the manifest is available but before
            // any data is pulled from it.
            //
            //return function (manifest) {
                // peek at / modify the manifest object
            //};
        };
    </script>
    -->

    <!-- The line below must be kept intact for Sencha Cmd to build your application -->
    <script id="microloader" data-app="de44cbf6-8931-415f-9694-543a604c44e8" type="text/javascript" src="bootstrap.js"></script>
</head>

<body>
    <div id="loadingToast">
        <div class="weui-mask_transparent"></div>
        <div class="weui-toast">
            <i class="weui-loading weui-icon_toast"></i>
            <p class="weui-toast__content" id="loadingToastText">加载中</p>
        </div>
    </div>
</body>
<!--<script src="app.gzjs"></script>-->
</html>
