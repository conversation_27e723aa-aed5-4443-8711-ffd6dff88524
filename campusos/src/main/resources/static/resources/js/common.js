var toast = function (msg) {
    Ext.toast({
        html: '<div class="toastmsg fa-bell">' + msg + '</div>',
        minWidth: 420,
        align: 't',
        border: false,
        frame: false,
        style: 'border:none;border-width:0px;',
        bodyStyle: 'background-color:#c10fe1;'
    });
};

window.adcfg = {
    status: 0,
    usercode: '',
    timeout: 10000,
    viewcount: 0,
    maxViewCount: 3,
    title: '欢迎来电垂询',
    adurl: '/resources/ad/1.html'
};

var loadCalendarJs = function () {
    Ext.Loader.loadScriptsSync('resources/style/calendar.css');
    Ext.Loader.loadScriptsSync('resources/js/calendar/data/CalendarMappings.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/data/CalendarModel.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/data/Calendars.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/data/Events.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/data/EventMappings.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/data/EventModel.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/data/MemoryCalendarStore.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/data/MemoryEventStore.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/util/Date.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/util/WeekEventRenderer.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/util/CNDate.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/form/EventDetails.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/template/BoxLayout.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/template/DayBody.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/template/DayHeader.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/template/Month.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/view/AbstractCalendar.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/view/Day.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/view/Week.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/view/MonthDayDetail.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/view/Month.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/view/DayHeader.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/view/DayBody.js');
    Ext.Loader.loadScriptsSync('resources/js/calendar/CalendarPanel.js');
};

window.wsslist = [];
window.onbeforeunload = function () {
    for (var i = 0; i < window.wsslist.length; i++) {
        window.wsslist[i].close();
    }
};

window.wssclient = function (userid, topic, callback, opencallback) {
    if (window.WebSocket) {
        var host = window.location.host;
        var wsprotocol = 'ws:';
        if (document.location.protocol == 'https:') {
            wsprotocol = 'wss:'
        }
        var ws = {
            wsprotocol: wsprotocol,
            topic: topic,
            userid: userid,
            host: host,
            wssurl: '',
            callback: callback,
            opencallback: opencallback,
            client: null,
            keeplive: function () {
                var _wss = this;
                setInterval(function () {
                    if (_wss.client) {
                        if (_wss.client.readyState == 1) {
                            _wss.client.send('{"topic":"ping"}');
                        } else {
                            console.log("断开状态，尝试重连");
                            _wss.newWebSocket();
                        }
                    }
                }, 10000);
            },
            newWebSocket: function () {
                var _wss = this;
                _wss.wssurl = _wss.wsprotocol + '//' + _wss.host + '/websocket/' + _wss.userid + '/' + _wss.topic;
                _wss.client = new window.WebSocket( _wss.wssurl);
                _wss.client.onopen = function (evn) {
                    console.log('主题[' + _wss.topic + ']WSS服务连接成功');
                    // _wss.keeplive();
                    if (opencallback && typeof (opencallback) == 'function') {
                        opencallback(_wss.client, evn);
                    }
                };
                _wss.client.onmessage = function (evn) {
                    if (evn.data) {
                        if (callback && typeof (callback) == 'function') {
                            callback(_wss.client, evn);
                        }
                    }
                };
                _wss.client.onclose = function () {
                    console.log('主题[' + _wss.topic + ']WSS服务连接已断开');
                };
            }
        };
        ws.newWebSocket();
        window.wsslist.push(ws.client);
        return ws.client;
    } else {
        Ext.Msg.showToast('当前浏览器不支持wss通讯协议', '系统信息');
        return null;
    }
};

window.ls = {};
window.ls.setItem = function (key, value) {
    if (window.localStorage) {
        window.localStorage.setItem(key, value);
    } else {
        console.error('当前浏览器不支持本地存储');
    }
};
window.ls.getItem = function (key) {
    if (window.localStorage) {
        return window.localStorage.getItem(key);
    } else {
        console.error('当前浏览器不支持本地存储')
        return '';
    }
};
window.ls.removeItem = function (key) {
    if (window.localStorage) {
        window.localStorage.removeItem(key);
    } else {
        console.error('当前浏览器不支持本地存储');
    }
};
window.ls.clear = function () {
    if (window.localStorage) {
        window.localStorage.clear();
    } else {
        console.error('当前浏览器不支持本地存储');
    }
};

//验证是否为IP地址格式
var isValidIP = function (ip) {
    var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
    return reg.test(ip);
}

var TimeIntToTime = function (timeint) {
    var str = timeint.substr(0, 2) + ':' + timeint.substr(2, 4);
    return str;
}

//打开隐藏窗口
var openWindow = function (url, params, method) {
    if (!method) {
        method = 'get';
    }
    Ext.get('downloadform').dom.method = method;
    Ext.get('downloadform').dom.action = url;
    Ext.get('downloadform').dom.innerHTML = '';
    if (params) {
        Ext.Object.each(params, function (k, v) {
            Ext.get('downloadform').createChild({
                tag: 'input',
                type: 'hidden',
                name: k,
                value: v
            });
        });
    } else {
        Ext.get('downloadform').createChild({
            tag: 'input',
            type: 'hidden',
            name: "t",
            value: Math.random()
        });
    }
    Ext.get('downloadform').dom.submit();
};

window.applang = {
    lang: [
        {
            infocode: '学工号',
            info1code: '工号',
            info2code: '学号',
            orgname: '院系部门',
            orgname1: '部门',
            orgname2: '院系、部门、专业或班级',
            orgname3: '院系、专业或班级',
            orgname4: '专业或班级',
            infoname1: '职工',
            infoname2: '学生',
            infoname3: '家属',
            intoyear: '入学',
            direction1: '正常',
            direction2: '未归',
            direction3: '迟到',
            defaultinfotype: 2
        },
        {
            infocode: '工号',
            info1code: '工号',
            info2code: '工号',
            orgname: '部门',
            orgname1: '部门',
            orgname3: '部门',
            orgname2: '部门',
            orgname4: '部门',
            infoname1: '员工',
            infoname2: '员工',
            intoyear: '入职',
            direction1: '在岗',
            direction2: '外出',
            direction3: '迟到',
            defaultinfotype: 1
        }
    ],
    get: function (key) {
        if (typeof (CamPus.apptype) == 'undefined') {
            Ext.Ajax.request({
                url: '/SysInfo/getApptype',
                params: {},
                method: 'POST',
                async: false,
                success: function (response) {
                    var result = Ext.JSON.decode(response.responseText);
                    if (result.success) {
                        CamPus.apptype = result.msg;
                    }
                }
            });
        }
        if (CamPus.apptype == '0' || typeof (CamPus.apptype) == 'undefined' || !CamPus.apptype) {
            CamPus.apptype = 2;
        }
        var me = this;
        var v = '';
        if (key) {
            v = me.lang[CamPus.apptype - 1][key];
            if (v == undefined) {
                v = '';
            }
        }
        return v;
    }
};


window.onReadCard = function (callback) {
    if (typeof (useractive) != 'undefined') {
        window.readcardcallback = function (identid, cardsn, msg) {
            if (msg) {
                toast(msg);
            } else if (cardsn && callback && typeof (callback) == 'function') {
                callback(identid, cardsn);
            }
        }
        useractive.readICCardSN('window.readcardcallback', 'card');
    } else {
        toast('请从专用浏览器进入本系统');
    }
};

window.onInitCard = function (callback) {
    if (typeof (useractive) != 'undefined') {
        window.initcardcallback = function (identid, msg) {
            if (msg) {
                toast(msg);
            } else if (callback && typeof (callback) == 'function') {
                callback(identid);
            }
        }
        useractive.initICCard('window.initcardcallback', 'card');
    } else {
        toast('请从专用浏览器进入本系统');
    }
};

window.writeIcCardCommon = function (identid, code, name, callback) {
    if (typeof (useractive) != 'undefined') {
    	/*window.writeIcCardCommon('','2015119025','胡东林',function (identid) {
      		toast('操作成功');
      	});*/
        window.writeIcCardCommoncallback = function (identid, msg) {
            if (msg) {
                toast(msg);
            } else if (callback && typeof (callback) == 'function') {
                callback(identid);
            }
        }
        useractive.writeIcCardCommon('window.writeIcCardCommoncallback', identid, code, name);
    } else {
        toast('请从专用浏览器进入本系统');
    }
};

window.readIcCardCommon = function (identid, callback) {
    if (typeof (useractive) != 'undefined') {
    	/*window.readIcCardCommon('',function (identid, code, name) {
            toast(Ext.String.format('读卡成功——工号：{0}，姓名：{1}',code,name))
         });*/
        window.readIcCardCommoncallback = function (identid, msg, code, name) {
            if (msg) {
                toast(msg);
            } else if (callback && typeof (callback) == 'function') {
                callback(identid, code, name);
            }
        }
        useractive.readIcCardCommon('window.readIcCardCommoncallback', identid);
    } else {
        toast('请从专用浏览器进入本系统');
    }
};


window.onReadIdCard = function (callback,identid) {
    if (typeof (useractive) != 'undefined') {
        window.readidcardcallback = function (identid, data, msg) {
            if (msg) {
                toast(msg);
            } else if (callback && typeof (callback) == 'function') {
            	idcarddata=JSON.parse(data);
            	console.info(idcarddata);
                idcard = {
                    Name: idcarddata.Name,
                    CNName: idcarddata.CNName,
                    SexCName: idcarddata.Sex,
                    Idc: idcarddata.IdCardNo,
                    NationCode: idcarddata.NationCode,
                    NationCName: idcarddata.Nation,
                    Birth: idcarddata.Birthday,
                    Address: idcarddata.Address,
                    Regorg: idcarddata.Department,
                    StartDate: idcarddata.ValidStartDate,
                    EndDate: idcarddata.ValidEndDate,
                    PictureBase64: idcarddata.FaceData,
                    SamID:idcarddata.SamID
                };
                if (callback && typeof (callback) == 'function') {
                    callback(identid, idcard);
                }
            }
        }
        if(!identid || identid==undefined){
        	identid='idcard';
        }
        useractive.readIdCard('window.readidcardcallback', identid);
    } else {
        toast('请从专用浏览器进入本系统');
    }
};

window.FingerPrint = function () {
    if (typeof (useractive) != 'undefined') {
        window.FingerPrintmsg = function (base) {
            if (base) {
                toast(base);
            } else if (callback && typeof (callback) == 'function') {
                callback(base);
            }
        }
        useractive.fingercollect('window.FingerPrintmsg', "123","name");
    } else {
        toast('请从专用浏览器进入本系统');
    }
};

var ConvertTime = {
    toMinute: function (time1) {
        if (!time1) {
            return 0;
        }
        var time1s = time1.split(':')
        var time1value = parseInt(time1s[0]) * 60 + parseInt(time1s[1]);
        return time1value;
    },
    toPrefixTime: function (intvalue) {
        if (!intvalue) {
            return '00:00';
        }
        var time = 0;
        intvalue = parseInt(intvalue);
        if (intvalue > 1440) {
            time = intvalue - 1440;
        } else {
            time = intvalue;
        }
        var m = time % 60;
        var h = (time - m) / 60;
        if (h == 0 && m == 0) {
            return '';
        }
        return (h < 10 ? '0' + h : h) + ':' + (m < 10 ? '0' + m : m);
    }
}

window.getResourceUrl = function (resourceDomain, path) {
    if (resourceDomain == undefined || resourceDomain == 'undefined' || !resourceDomain) {
        resourceDomain = '';
    }
    if (path == undefined || path == 'undefined' || !path) {
        path = '';
    }
    if (!path) {
        return '';
    }
    if (resourceDomain == '/') {
        if (path.substr(0, 1) == '/') {
            return path;
        } else {
            return '/' + path;
        }
    } else {
        if (resourceDomain.substr(resourceDomain.length - 1) == "/") {
            resourceDomain = resourceDomain.substr(0, resourceDomain.length - 1);
        }
        if (path.substr(0, 1) == '/') {
            return resourceDomain + path;
        } else {
            return resourceDomain + '/' + path;
        }
    }
}
