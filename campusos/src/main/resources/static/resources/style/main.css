* {
    font-family: "微软雅黑", "<PERSON><PERSON><PERSON>", "SimHei", "Microsoft YaHei", "SimSun";
}

body {
    background: rgba(238, 237, 237, 0.7);
}

.weui-mask_transparent {
    position: fixed;
    z-index: 99998;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
}

.weui-toast {
    position: fixed;
    z-index: 99999;
    width: 130px;
    min-height: 130px;
    top: 50%;
    left: 50%;
    margin-left: -65px;
    background: rgba(17, 17, 17, 0.7);
    text-align: center;
    border-radius: 5px;
    color: #FFFFFF;
    margin-top: -65px;
}

@-webkit-keyframes weuiLoading {
    0% {
        -webkit-transform: rotate3d(0, 0, 1, 0deg);
        transform: rotate3d(0, 0, 1, 0deg);
    }
    100% {
        -webkit-transform: rotate3d(0, 0, 1, 360deg);
        transform: rotate3d(0, 0, 1, 360deg);
    }
}

@keyframes weuiLoading {
    0% {
        -webkit-transform: rotate3d(0, 0, 1, 0deg);
        transform: rotate3d(0, 0, 1, 0deg);
    }
    100% {
        -webkit-transform: rotate3d(0, 0, 1, 360deg);
        transform: rotate3d(0, 0, 1, 360deg);
    }
}

.weui-loading {
    width: 20px;
    height: 20px;
    display: inline-block;
    vertical-align: middle;
    -webkit-animation: weuiLoading 1s steps(12, end) infinite;
    animation: weuiLoading 1s steps(12, end) infinite;
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;
    background-size: 100%;
}

.weui-icon_toast.weui-loading {
    margin: 30px auto 0;
    width: 38px;
    height: 38px;
    vertical-align: baseline;
}

.weui-icon_toast {
    margin: 22px 0 0;
    display: block;
}

.weui-icon_toast.weui-icon-success-no-circle:before {
    color: #FFFFFF;
    font-size: 55px;
}

.weui-toast__content {
    margin: 15 0 15px;
    padding-left: 0px;
    font-size: 13px;
}

.weui-dialog {
    position: fixed;
    z-index: 5000;
    width: 80%;
    max-width: 300px;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: #FFFFFF;
    text-align: center;
    border-radius: 3px;
    overflow: hidden;
}

.weui-dialog__hd {
    padding: 1.3em 1.6em 0.5em;
}

.weui-dialog__title {
    font-weight: 400;
    font-size: 18px;
}

.weui-dialog__bd {
    padding: 0 1.6em 0.8em;
    min-height: 40px;
    font-size: 15px;
    line-height: 1.3;
    word-wrap: break-word;
    word-break: break-all;
    color: #999999;
}

.weui-dialog__bd:first-child {
    padding: 2.7em 20px 1.7em;
    color: #353535;
}

.weui-dialog__ft {
    position: relative;
    line-height: 48px;
    font-size: 18px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}

.weui-dialog__ft:after {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid #D5D5D6;
    color: #D5D5D6;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}

.weui-dialog__btn {
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    color: #3CC51F;
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    position: relative;
}

.weui-dialog__btn:active {
    background-color: #EEEEEE;
}

.weui-dialog__btn:after {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    bottom: 0;
    border-left: 1px solid #D5D5D6;
    color: #D5D5D6;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(0.5);
    transform: scaleX(0.5);
}

.weui-dialog__btn:first-child:after {
    display: none;
}

.weui-dialog__btn_default {
    color: #353535;
}

.weui-dialog__btn_primary {
    color: #0BB20C;
}

#sysmsgview {
    margin: 15px;
    height: auto;
}

#sysmsgview h1 {
    height: 32px;
    line-height: 32px;
    font-weight: normal;
    font-size: 24px;
}

#sysmsgview p {
    line-height: 200%;
    font-size: 15px;
    white-space: normal;
    word-break: break-all;
}

#sysmsgview p span.content {
    margin: 15px;
    text-indent: 30px;
    white-space: normal;
    word-break: break-all;
}

#syslogview {
    margin: 15px;
    height: auto;
}

#syslogview h1 {
    height: 32px;
    line-height: 32px;
    font-weight: normal;
    font-size: 24px;
}

#syslogview p {
    line-height: 200%;
    font-size: 15px;
    white-space: normal;
    word-break: break-all;
}

#syslogview p span.content {
    margin: 15px;
    text-indent: 30px;
    white-space: normal;
    word-break: break-all;
}

#aboutosdiv {
    margin: 30px;
    text-align: center;
    font-size: 18px;
}

#aboutosdiv .logo {
    margin-bottom: 40px;
}

#aboutosdiv .osinfo {
    line-height: 40px;
    height: 40px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 600px;
}

#aboutosdiv .osinfo .t {
    line-height: 40px;
    height: 40px;
    display: block;
    float: left;
    width: 100px;
}

#aboutosdiv .osinfo .c {
    line-height: 40px;
    height: 40px;
    display: block;
    float: left;
    text-align: left;
}

.thumb {
    background: transparent;
    text-align: center;
}

.thumb-wrap {
    float: left;
    margin: 4px;
    margin-right: 0;
    padding: 3px;
    border: 1px none #dddddd;
}

.thumb-wrap span {
    overflow: hidden;
    text-align: center;
    width: 64px;
    height: 64px;
    display: inline-block;
    vertical-align: middle;
    line-height: 64px;
}

.x-fa-view:before {
    font-family: FontAwesome !important;
    font-size: 36px;
    color: #333;
}

.toastmsg {
    color: #FFF;
    line-height: 40px;
    height: 40px;
    font-size: 16px;
}

.toastmsg:before {
    font-family: FontAwesome !important;
    font-size: 28px;
    color: #FFF;
    margin-right: 15px;
}

.door-into {
    background-image: url(../images/into.png) !important;
    background-repeat: no-repeat;
    background-size: cover;
}

.door-exit {
    background-image: url(../images/exit.png) !important;
    background-repeat: no-repeat;
    background-size: cover;
}

.icoviewcls .imgThumb {
    padding: 3px;
    text-align: center;
}

.icoviewcls .imgThumb img {
    width: 94px;
    height: 94px;
}

.icoviewcls .img-thumb-wrap {
    float: left;
    margin: 4px;
    margin-right: 0px;
    padding: 5px;
    width: 110px;
    height: 140px;
    display: block;
    overflow: hidden;
}

.icoviewcls .img-thumb-wrap span {
    display: block;
    overflow: hidden;
    text-align: center;
}

.icoviewcls .img-x-view-over {
    border: 1px solid #dddddd;
    background-color: #efefef;
    padding: 4px;
}

.icoviewcls .img-x-item-selected {
    background: #DFEDFF;
    border: 1px solid #6593cf;
    padding: 4px;
}

.icoviewcls .img-x-item-selected .imgthumb {
    background: transparent;
}

.icoviewcls .img-x-item-selected span {
    color: #1A4D8F;
}

.icoviewcls .checked{
	width:15px;
	height:15px;
	display: block;
	background-image: url(../images/check.png);
    background-position: center;
    border: 1px solid #003366;
    position: absolute;
    background-repeat: no-repeat;
}
.icoviewcls .nocheck{
	width:15px;
	height:15px;
	display: block;
	background: none;
	border: 1px solid #dddddd;
	position: absolute;
}

.idcard-item {
    display: block;
}

.greencolor {
    color: #7bc309 !important;
}

.redcolor {
    color: red !important;
}

.bluecolor {
    color: #5fa2dd;
}

.border-radius50{border-radius: 50%;}
.size36{width: 36px !important; height: 36px !important;}
.vertical-align-middle{vertical-align: middle !important;}


#treelistContainer div {
    scrollbar-face-color: #40658d;
    scrollbar-shadow-color: #40658d;
    scrollbar-highlight-color: #40658d;
    scrollbar-3dlight-color: #2c3845;
    scrollbar-darkshadow-color: #2c3845;
    scrollbar-track-color: #2c3845;
    scrollbar-arrow-color: #40658d;
}

#treelistContainer div ::-webkit-scrollbar-track-piece {
    background-color: #2c3845;
    -webkit-border-radius: 0;
}

#treelistContainer div ::-webkit-scrollbar {
    width: 7px;
    height: 7px;
}

#treelistContainer div ::-webkit-scrollbar-thumb {
    height: 50px;
    background-color: #40658d;
    -webkit-border-radius: 4px;
    outline: 2px solid #40658d;
    outline-offset: -2px;
    border: none;
}

#treelistContainer div ::-webkit-scrollbar-thumb:hover {
    height: 50px;
    background-color: #40658d;
    -webkit-border-radius: 4px;
}


.x-table-row
{
    border-collapse: collapse;
    margin: 0 auto;
    text-align: center;
    background: #eaeff4;
}
.x-table-row td, .x-table-row th
{
    border: 1px solid #d9d9d9;
    color: #333;
    height: 20px;
    font-weight:bold;
}


.shiftrule {
    border-right: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;
    width: 100%
}

td.shiftrule-allow-item {
    border-left: 1px solid #dddddd;
    border-top: 1px solid #dddddd;
    height: 64px;
    cursor: pointer;
    text-align: center;
    width: 14.2%
}

td.shiftrule-allow-item:before {
    content: attr(data-beforeData);
    color: #1798c0;
    background: #f5f3f3;
    border-radius: 10px;
    padding: 4px;
    display: block;
    width: 40px;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 5px;
}

.shiftrule-dis-abled {
    border-left: 1px solid #dddddd;
    border-top: 1px solid #dddddd;
    background-color: #f1f1f1;
    height: 64px;
    cursor: pointer;
    text-align: center;
    width: 14.2%
}

.shiftrule td.itemtd:hover {
    background-color: #EFFCFD;
}


#ViewGridReportDiv .-gr-fs1{font:bold 20px 宋体;}
#ViewGridReportDiv .-gr-fs0{font:12px 宋体;}
#ViewGridReportDiv .-gr-section5{height:37px;}
#ViewGridReportDiv .-gr-section4{height:8px;}
#ViewGridReportDiv .-gr-section3{height:22px;}
#ViewGridReportDiv .-gr-section2{height:7px;}
#ViewGridReportDiv .-gr-section1{height:30px;font:bold 12px 宋体;background-color:#c0c0c0;}
#ViewGridReportDiv .-gr-section0{height:30px;}
#ViewGridReportDiv .-gr-cell1{text-align:left;vertical-align:middle;overflow:hidden;white-space:nowrap;padding:1px 1px 1px 2px;border:1px solid #000000;background-color:#ffffff;}
#ViewGridReportDiv .-gr-cell0{text-align:left;vertical-align:middle;overflow:hidden;white-space:nowrap;padding:1px 1px 1px 2px;border:1px solid #000000;}
#ViewGridReportDiv .-gr-ctrl0{padding:1px 1px 1px 2px;text-align:left;vertical-align:middle;overflow:hidden;white-space:nowrap;}
#ViewGridReportDiv ._grdg{table-layout:fixed;}
#ViewGridReportDiv ._grdg>tbody>tr:hover:not(._grcsssh) {background-color:#eee;color:#000;}
#ViewGridReportDiv ._grdg>tbody>tr>td:hover:not(._grcsssh) {background-color:#eee;color:#000;}
#ViewGridReportDiv ._grcsssh{ background-color:rgba(20,20,127,1);color:rgba(255,255,255,1);}